<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>

        <el-form-item label="选择小区" prop="communityId">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="选择月份">
          <el-date-picker v-model="listQuery.yearMonth" value-format="yyyy-MM" format="yyyy-MM" type="month" placeholder="月份">
          </el-date-picker>
        </el-form-item>

        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <el-form inline>
      <el-form-item label="总收入:">
        {{totalItem.count || 0}} 元
      </el-form-item>
    </el-form>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区">
          <template slot-scope="scope">
            <div>{{ scope.row.communityName }}</div>
          </template>
        </el-table-column>

        <el-table-column label="年">
          <template slot-scope="scope" v-if="scope.row.yearMonth">
            <div>{{ scope.row.yearMonth.split("-")[0] }}</div>
          </template>
        </el-table-column>

        <el-table-column label="月">
          <template slot-scope="scope" v-if="scope.row.yearMonth">
            <span>{{ scope.row.yearMonth.split("-")[1] }}</span>
          </template>
        </el-table-column>

        <el-table-column label="收入(单位:元)">
          <template slot-scope="scope">
            <span>{{ scope.row.total }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')">详情</el-button>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="'出租详情'" :visible.sync="dlgShow" width='1200px' top="30px" append-to-body>
      <el-table v-if="dlgType === 'VIEW'" class='m-small-table' :data="dlgData.list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="楼栋">
          <template slot-scope="scope">
            {{scope.row.floorName}}
          </template>
        </el-table-column>

        <el-table-column label="单元">
          <template slot-scope="scope">
            {{scope.row.unitName}}
          </template>
        </el-table-column>

        <el-table-column label="广告位">
          <template slot-scope="scope">
            {{scope.row.adSpace}}
          </template>
        </el-table-column>

        <el-table-column label="月租费(元)">
          <template slot-scope="scope">
            {{scope.row.price}}
          </template>
        </el-table-column>

      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'

import {
  formAdvertising,
  formAdvertisingInfo
} from '@/api/reportMan'

import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  list: []
}
export default {
  name: 'adPosStatistic',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {

      pickerOptions: {
        disabledDate (time) {
          return time.getTime() > Date.now() - (1000 * 60 * 60 * 24);
        },
      },
      // 弹窗 类型
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        danyuanId: [{ required: true, message: '必填字段', trigger: 'change' }],
        fangwuId: [{ required: true, message: '必填字段', trigger: 'change' }],
        loudongId: [{ required: true, message: '必填字段', trigger: 'change' }],
        valuateUserName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        valuateType: [{ required: true, message: '必填字段', trigger: 'change' }],
        valuateContent: [{ required: true, message: '必填字段', trigger: 'blur' }],
        valuateGenzong: [{ required: true, message: '必填字段', trigger: 'blur' }],
        valuatePhone: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: '号码格式有误！',
            trigger: 'blur'
          }
        ],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        yearMonth: ''
      },
      totalItem: {},
      userInfo: {},
      communityList: [],
      buildingList: [],
      unitList: [],
      roomList: [],
      typeList: [
        {
          id: 1,
          name: '投诉'
        },
        {
          id: 2,
          name: '建议'
        },
      ],
    }
  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },


    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = type
      this.dlgShow = true
      if (type === 'VIEW') {
        let postParam = {
          communityId: data.communityId,
          yearMonth: data.yearMonth
        }
        formAdvertisingInfo(postParam).then(res => {
          if (res.data.code == 200) {
            this.dlgData.list = JSON.parse(JSON.stringify(res.data.data))
          }
        })
      }
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    formatList () {

    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      formAdvertising(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.formatList()
          this.total = res.data.page ? res.data.page.total : 0
          this.totalItem = res.data.dataMap ? res.data.dataMap : {}
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.table-container {
  height: calc(100% - 150px);
}
</style>


