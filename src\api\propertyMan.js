/**
 * desc 物业管理系统
 * by 马振国
 */
import request from '@/utils/request'

// 【【 1 支援管理
// 1 支援单列表  我的需求
export function findOrderByDynamic(data) {
  return request({
    url: `/ade/findOrderByDynamic`,
    method: 'post',
    data
  })
}

// 2 支援单列表 需求审核 列表
export function findOrderAllByDynamic(data) {
  return request({
    url: `/ade/findOrderAllByDynamic`,
    method: 'post',
    data
  })
}

// 3 发布支援单 缺员 开荒 活动
export function sendSupOrder(data) {
  return request({
    url: `/ade/sendSupOrder`,
    method: 'post',
    data
  })
}

// 4 编辑支援单
export function updateSupOrder(data) {
  return request({
    url: `/ade/updateSupOrder`,
    method: 'post',
    data
  })
}

// 5 查询支援单详情
export function findOrderDetails(orderId) {
  return request({
    url: `/ade/findOrderDetails/${orderId}`,
    method: 'get'
  })
}
// 审核通过， 关闭需求， 接单状态改变  1,9,4
export function auditOrderStatus(status, orderIds) {
  return request({
    url: `/ade/auditOrderStatus/${status}/${orderIds}`,
    method: 'get'
  })
}

// 获取我的接单
export function findOrderByUserId({page, size}) {
  return request({
    url: `/ade/findOrderByUserId/${page}/${size}`,
    method: 'get'
  })
}

// 获取当前登陆人 下的员工
export function findUserByBranchLeaderId() {
  return request({
    url: `/sys/findUserByBranchLeaderId`,
    method: 'get'
  })
}

// 1 派人，2换人，3 撤人
/**
 * params
 * updateType   // 1 派人 //2 换人 // 3 撤人,
 * orderType    	// 1缺员 //2 开荒 // 3活动
 * orderId    	支援单id
 * userIds    支援员工id “，”分割
 * datas    	支援日期 “，”分割
 */
// params: updateType
export function upDateOrderUser(data) {
  return request({
    url: `/ade/upDateOrderUser`,
    method: 'post',
    data
  })
}
