import request from '@/utils/request'

/*
 *角色权限相关
 */

// 查询角色列表 
export function findRoleLike(data) {
  return request({
    url: `/sys/findRoleLike`,
    method: 'post',
    data
  })
}
// 新增角色
export function saveSysRole(data) {
  return request({
    url: `/sys/saveSysRole`,
    method: 'post',
    data
  })
}
// 修改角色
export function upDateSysRole(data) {
  return request({
    url: `/sys/upDateSysRole`,
    method: 'post',
    data
  })
}
// 删除角色
export function delSysRole(id) {
  return request({
    url: `/sys/delSysRole/${id}`,
    method: 'get'
  })
}
// 所有的权限树
export function findPermissionTree() {
  return request({
    url: `/sys/findPermissionTree`,
    method: 'post'
  })
}

// 根据角色查询已拥有的权限
export function findPerByRole(roleId) {
  return request({
    url: `/sys/findPerByRole/${roleId}`,
    method: 'get'
  })
}

// 获取角色分类列表
export function findRoleTypeByDynamic(data) {
  return request({
    url: `/sys/findRoleTypeByDynamic`,
    method: 'post',
    data
  })
}
// 根据分类ID获取角色信息
export function findRoleByTypeId({
  roleTypeId,
  page,
  size
}) {
  return request({
    url: `/sys/findRoleByTypeId/${roleTypeId}/${page}/${size}`,
    method: 'get'
  })
}



// [[ 角色分类start
//保存角色分类
export function saveRoleType(data) {
  return request({
    url: `/sys/saveRoleType`,
    method: 'post',
    data
  })
}
//修改角色分类
export function updateRoleTypeById(data) {
  return request({
    url: `/sys/updateRoleTypeById`,
    method: 'post',
    data
  })
}
// 删除角色分类
export function delRoleTypeById(id) {
  return request({
    url: `/sys/delRoleTypeById/${id}`,
    method: 'get'
  })
}
// ]] 角色分类end

//[[ 权限分配start
// 获取角色和分类树
export function findRoleAndTypeTree() {
  return request({
    url: `/sys/findRoleAndTypeTree`,
    method: 'get'
  })
}


//提交授权信息
export function updateRolePermissions(data) {
  return request({
    url: `/sys/updateRolePermissions`,
    method: 'post',
    data
  })
}
// 根据分类ID获取角色信息根据角色id查找员工信息
export function findUserByRoleId({
  roleId,
  page,
  size
}) {
  return request({
    url: `/sys/findUserByRoleId/${roleId}/${page}/${size}`,
    method: 'get'
  })
}
//]]权限分配end
