<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="时间筛选：">
          <el-date-picker v-model="listQuery.rangeDate" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入访客名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="访客姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.visitorName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="性别">
          <template slot-scope="scope">
            <span>{{ scope.row.visitorSex }}</span>
          </template>
        </el-table-column>

        <el-table-column label="联系方式">
          <template slot-scope="scope">
            <span>{{ scope.row.visitorPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="事由">
          <template slot-scope="scope">
            <span>{{ scope.row.visitorReason }}</span>
          </template>
        </el-table-column>

        <el-table-column label="来访时间">
          <template slot-scope="scope">
            <span>{{ scope.row.visitorTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="随行人数">
          <template slot-scope="scope">
            <span>{{ scope.row.visitorCount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车牌号">
          <template slot-scope="scope">
            <span>{{ scope.row.carNum }}</span>
          </template>
        </el-table-column>

        <el-table-column label="业主姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.memberName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="小区">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="房号">
          <template slot-scope="scope">
            <span>{{ scope.row.roomName }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑访客信息" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :disabled="dlgType === 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">

        <el-form-item label="选择小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择房屋" prop="roomName">
          <el-input v-model="dlgData.roomName" @focus="showRoomDlg" placeholder="请选择房屋" readonly>
          </el-input>
        </el-form-item>

        <el-form-item label="业主">
          {{dlgData.roomInfo.memberName}}
        </el-form-item>

        <el-form-item label="访客姓名" prop="visitorName">
          <el-input v-model="dlgData.visitorName" placeholder="请填写姓名" style="width:240px"></el-input>
          <el-radio-group v-model="dlgData.visitorSex">
            <el-radio :label="'先生'">先生</el-radio>
            <el-radio :label="'女士'">女士</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="来访人数" prop="visitorCount">
              <el-input-number v-model="dlgData.visitorCount" :controls='false' :min="1" :precision="0" :step="1"></el-input-number>
            </el-form-item>

          </el-col>
          <el-col :span="12">
            <el-form-item label="来访事由" prop="visitorReasonValue">
              <el-select v-model="dlgData.visitorReasonValue" filterable clearable placeholder="请选择来访事由">
                <el-option v-for="item in reasonList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="手机" prop="visitorPhone">
          <el-input v-model="dlgData.visitorPhone" placeholder="请填写手机号码"></el-input>
        </el-form-item>

        <el-form-item label="失效日期" prop="validityTime">
          <el-date-picker v-model="dlgData.validityTime" type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" placeholder="有效日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="是否驾车">
          <el-switch v-model="dlgData.isCar" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>

        <el-form-item label="车牌号" prop="carNum" v-if="dlgData.isCar">
          <el-input v-model="dlgData.carNum" placeholder="请填写车牌号"></el-input>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <roomDlg />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'
import { visitorrecordPage, visitorrecordAddOrUpdate, visitorrecordDisable } from '@/api/visitorMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import roomDlg from '@/components/Dialog/communityMan/roomDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  carNum: '',
  communityId: '',
  communityName: '',
  inWay: '0',
  isCar: false,
  roomId: '',
  roomName: '',
  roomInfo: {},
  validityTime: '',
  visitorCount: 1,
  visitorName: '',
  visitorPhone: '',
  visitorReason: '',
  visitorReasonValue: '',
  visitorSex: '先生',
  visitorTime: ''
}


export default {
  name: 'visitorReg',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    roomDlg
  },
  data () {
    return {
      // 弹窗 状态
      dlgShowBind: false,
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        roomName: [{ required: true, message: '必填字段', trigger: 'change' }],
        visitorSex: [{ required: true, message: '必填字段', trigger: 'blur' }],
        visitorCount: [{ required: true, message: '必填字段', trigger: 'blur' }],
        visitorReasonValue: [{ required: true, message: '必填字段', trigger: 'blur' }],
        validityTime: [{ required: true, message: '必填字段', trigger: 'blur' }],
        visitorName: [{ required: true, message: '必填字段', trigger: 'change' }],
        carNum: [{ required: true, message: '必填字段', trigger: 'change' }],
        visitorPhone: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: '手机号码格式有误！',
            trigger: 'blur'
          }
        ],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        rangeDate: '',
        type: '1',
      },
      userInfo: {},
      communityList: [],
      reasonList: []
    }
  },

  computed: {
    ...mapGetters('communityMan/roomDlg', {
      roomId: 'roomId',
      roomName: 'roomName',
      roomInfo: 'roomInfo'
    }),

  },

  watch: {
    roomId (val) {
      this.dlgData.roomId = val
    },

    roomName (val) {
      this.dlgData.roomName = val
    },

    roomInfo (val) {
      this.dlgData.roomInfo = JSON.parse(JSON.stringify(val))
    },
  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    utils.getDataDict(this, 'visitorReason', 'reasonList')
  },

  methods: {

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    communityChange () {
      this.dlgData.roomId = ''
      this.dlgData.roomName = ''
      this.dlgData.roomInfo = {}
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 显示房间对话框
    showRoomDlg () {
      if (utils.isNull(this.dlgData.communityId)) {
        this.$message.warning("请先选择小区")
        return
      }
      this.$store.commit('communityMan/roomDlg/SET_COMMUNITYID', this.dlgData.communityId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMID', this.dlgData.roomId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMNAME', this.dlgData.roomName)
      this.$store.commit('communityMan/roomDlg/SET_ROOMINFO', this.dlgData.roomInfo)
      this.$store.commit('communityMan/roomDlg/SET_DLGSHOW', true)
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      this.listQuery.startDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[0]
      this.listQuery.endDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[1]
      visitorrecordPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          postParam.visitorReason = utils.getNameById(postParam.visitorReasonValue, this.reasonList)
          postParam.isCar = postParam.isCar ? 1 : 0
          postParam.memberId = postParam.roomInfo.memberId
          postParam.memberName = postParam.roomInfo.memberName
          this.dlgLoading = true
          visitorrecordAddOrUpdate(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgData.isCar = this.dlgData.isCar == 1
      this.dlgData.roomInfo = {
        id: this.dlgData.roomId,
        roomFullName: this.dlgData.roomName,
        memberId: this.dlgData.memberId,
        memberName: this.dlgData.memberName
      }
      console.log(this.dlgData)
      this.dlgType = type
      this.dlgShow = true
    },

    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        visitorrecordDisable(data.id, flag).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/ownerInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


