// 弹窗 垂直高度最高值
.m-dialog-h {
  min-height: 200px;
  max-height: 400px;
  overflow: auto;
}

// 【【 流程管理 弹框 样式
// .process-dialog {
// 【【 弹窗样式

// 右侧提示
.m-item-box-tips {
  position: absolute;
  top: 7px;
  left: 420px;
  display: block;
  line-height: 1.2;
  width: 80%;
  font-size: 12px;
  color: #666;
}

// 底部提示
.m-item-box-btips {
  font-size: 12px;
  color: #999;
  line-height: 1.2;
  margin-top: -14px;
  margin-bottom: 4px;
  // width: 100%;
  span {
    // float: left;
    display: inline-block;
    margin-right: 26px;
  }
}
.m-item-box {
  display: flex;
  justify-content: space-between;
  position: relative;

  .el-form-item {
    // flex: 1;
    width: 100%;
  }
  .sfzh-span {
    line-height: 36px;
    padding-right: 40px;
  }

  // 详细信息
  .disc {
    margin-top: -6px;
    color: #e6a23c;
    font-size: 12px;
    padding-left: 120px;
    span {
      margin-right: 30px;
    }
  }
}

// 弹窗
.dialog-nav {
  margin-top: -10px;
  margin-bottom: 20px;
}

// 弹窗 选择部门
.dialog-bm-bar {
  display: flex;
  justify-content: space-between;
  .dialog-bm-left,
  .dialog-bm-right {
    width: 400px;
  }
  // loading
  .dialog-bm-right-loading {
    padding-top: 40px;
    text-align: center;
    font-size: 60px;
  }
  .dialog-bm-right-ul {
    a {
      display: block;
      line-height: 26px;
      margin-left: 10px;
      color: #666;
      i {
        display: inline-block;
        margin-right: 8px;
        line-height: 26px;
        color: #19aa8d;
      }
    }
  }
}
.dialog-footer-span {
  font-size: 14px;
  color: #666;
  display: inline-block;
  padding-right: 10px;
}
.dialog-upload-bar-file {
  width: 160px;
  height: 160px;
  border: 1px dashed #999;
  line-height: 160px;
  font-size: 40px;
  text-align: center;
  color: #999;
  border-radius: 3px;
  cursor: pointer;
}
.dialog-upload-bar-img {
  width: 160px;
  cursor: pointer;
}

// [ 弹窗 提示 新
.dialog-popover-btn {
  height: 27px;
}
// ] 弹框 提示 新

// }
