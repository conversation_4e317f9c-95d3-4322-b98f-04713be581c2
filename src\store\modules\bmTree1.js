
const bmTree1 = {
  state: {
    bmTreeState1: false,
    bmTreeIsRole1: false,  // 是否根据权限
    bmTreeBranchId1: '',
    bmTreeBranchName1: ''
  },

  mutations: {
    SET_BMTREESTATE1: (state, bmTreeState1) => {
      state.bmTreeState1 = bmTreeState1
    },
    SET_BMTREEISROLE1: (state, val) => {
      state.bmTreeIsRole1 = val
    },
    SET_BMTREEBRANCHID1: (state, bmTreeBranchId1) => {
      state.bmTreeBranchId1 = bmTreeBranchId1
    },
    SET_BMTREEBRANCHNAME1: (state, bmTreeBranchName1) => {
      state.bmTreeBranchName1 = bmTreeBranchName1
    }
  },

  actions: {
    
  }
}

export default bmTree1
