import request from '@/utils/request'
import {
  requestExcel
} from '@/utils'

// 白名单列表
export function listPageBranchWhiteByBranchId (data) {
  return request({
    url: `/sys/listPageBranchWhiteByBranchId`,
    method: 'post',
    data
  })
}

// 添加白名单
export function saveBranchWhiteInfo (data) {
  return request({
    url: '/sys/saveBranchWhiteInfo',
    method: 'post',
    data
  })
}

// 删除白名单
export function delSysUser (data) {
  return request({
    url: `/sys/delSysUser`,
    method: 'post',
    data
  })
}
// 导入
export function uploadBranchWhite (data) {
  return requestExcel('/sys/uploadBranchWhite', data)
}

// 手机号码查询人员信息
export function findUserInfoById (id) {
  return request({
    url: '/sys/findUserInfoById?id=' + id,
    method: 'get'
  })
}

// 白名单下线
export function whiteListLogout (data) {
  return request({
    url: `/sys/whiteListLogout`,
    method: 'post',
    data
  })
}