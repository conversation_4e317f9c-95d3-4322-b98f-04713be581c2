<template>
  <el-dialog :close-on-click-modal='false' :title="'库房列表'" :visible.sync="dlgShow">
    <div class="filter-container">
      <div class='fr'>
        <el-input v-model="listQuery.str" placeholder='请填写库房名称'>
          <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
        </el-input>
        <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">
          搜索
        </el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectStorageId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>

        <el-table-column label="库房名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属科室" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'

import { findStorageDynamic } from '@/api/medicalMatchManSystem/clothingMan/storageMan'

export default {
  components: {
    Pagination
  },
  data() {
    return {
      list: [],

      listQuery: {
        page: 1,
        size: 10,
        str: "",
      },

      total: 0,

      selectStorageId: '',

      selectStorageName: ''

    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.clothingMan.storageDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('clothingMan/storageDlg/SET_DLGSHOW', val)
      }
    },

    storageId: {
      get: function () {
        return this.$store.state.clothingMan.storageDlg.storageId
      },
      set: function (val) {
        this.$store.commit('clothingMan/storageDlg/SET_STORAGEID', val)
      }
    },

    storageName: {
      get: function () {
        return this.$store.state.clothingMan.storageDlg.storageName
      },
      set: function (val) {
        this.$store.commit('clothingMan/storageDlg/SET_STORAGENAME', val)
      }
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.storageId)) {
          this.selectStorageId = ""
          this.selectStorageName = ""
        }
        this.getList()
      }
    },

    storageId(val) {
      this.selectStorageId = val
    },

    storageName(val) {
      this.selectStorageName = val
    }
  },

  methods: {
    resetStr() {
      this.listQuery.str = ""
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    rowClick(row, column, event) {
      this.selectStorageId = row['id']
      this.selectStorageName = row['name']
    },

    getList() {
      this.list = []
      findStorageDynamic(this.listQuery).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      this.storageId = this.selectStorageId
      this.storageName = this.selectStorageName
      this.$store.commit('clothingMan/storageDlg/SET_STORAGEID', this.storageId)
      this.$store.commit('clothingMan/storageDlg/SET_STORAGENAME', this.storageName)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('clothingMan/storageDlg/SET_DLGSHOW', false)
    }
  }
}
</script>