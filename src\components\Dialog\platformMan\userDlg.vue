<template>
  <el-dialog :close-on-click-modal='false' :title="'选择员工'" :visible.sync="dlgShow" append-to-body>
    <div class="filter-container">
      <el-form inline>
        <el-form-item label="关键字">
          <el-input v-model="listQuery.label" placeholder='请输入员工姓名'>
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-radio-group @change="getList" v-model="listQuery.branchType">
            <el-radio label="0">当前部门</el-radio>
            <el-radio label="1">当前及所属部门</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.departmentName" @focus="showBranchDlg" :title='listQuery.departmentName' placeholder="选择部门" readonly>
            <i @click='resetSearchItem(["departmentId", "departmentName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>

        <el-button icon='el-icon-search' type="success" size='mini' @click="getList">
          搜索
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="#" width="60">
          <template slot-scope="scope">
            <el-radio v-model="selectUserId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="员工姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属部门">
          <template slot-scope="scope">
            <span>{{ scope.row.departmentName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考勤组">
          <template slot-scope="scope">
            <span>{{ scope.row.groupName }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check">
        确 定
      </el-button>
    </div>
    <DialogBranch @superFunc="superBranch" :superDlgShow.sync="dlgShowBranch" :superSelectId="listQuery.departmentId" :superSelectName="listQuery.departmentName" :superPermission="true" />
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import DialogBranch from '@/components/Dialog/platformMan/DialogBranch'
import { findUserByLabelAndNumByGroup } from '@/api/staffMan'

export default {
  components: {
    Pagination,
    DialogBranch
  },
  data () {
    return {
      list: [],

      listQuery: {
        size: 10,
        label: "",
        page: 1,
        departmentId: '',
        departmentName: '',
        branchType: '0'
      },

      total: 0,

      selectUserId: '',

      selectUserName: '',

      dlgShowBranch: false
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.platformMan.userDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('platformMan/userDlg/SET_DLGSHOW', val)
      }
    },

    userId: {
      get: function () {
        return this.$store.state.platformMan.userDlg.userId
      },
      set: function (val) {
        this.$store.commit('platformMan/userDlg/SET_USERID', val)
      }
    },

    userName: {
      get: function () {
        return this.$store.state.platformMan.userDlg.userName
      },
      set: function (val) {
        this.$store.commit('platformMan/userDlg/SET_USERNAME', val)
      }
    },


  },

  watch: {

    dlgShow (val) {
      if (val) {
        if (utils.isNull(this.userId)) {
          this.selectUserId = ""
          this.selectUserName = ""
        }
        this.getList()
      }
    },

    userId (val) {
      this.selectUserId = val
    },

    userName (val) {
      this.selectUserName = val
    },

  },

  methods: {
    // 显示部门树
    showBranchDlg () {
      this.dlgShowBranch = true
    },

    superBranch (params) {
      this.listQuery.departmentId = params.selectId
      this.listQuery.departmentName = params.selectName
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    rowClick (row, column, event) {
      this.selectUserId = row['id']
      this.selectUserName = row['label']
    },

    getList () {
      this.list = []
      findUserByLabelAndNumByGroup(this.listQuery).then(res => {
        if (res.data.code == 200) {
          this.list = res.data ? res.data.data : []
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    subDlg () {
      this.userId = this.selectUserId
      this.userName = this.selectUserName
      this.$store.commit('platformMan/userDlg/SET_USERID', this.userId)
      this.$store.commit('platformMan/userDlg/SET_USERNAME', this.userName)
      this.closeDlg()
    },

    closeDlg () {
      this.$store.commit('platformMan/userDlg/SET_DLGSHOW', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);

  /deep/ .el-table td {
    border-bottom: 1px solid #ebeef5;
  }
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>