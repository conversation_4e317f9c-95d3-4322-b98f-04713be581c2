<template>
  <el-dialog :close-on-click-modal='false' :title="'选择车位'" :visible.sync="dlgShow" append-to-body>
    <div class="filter-container">
      <el-form inline>
        <el-form-item prop="communityId">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.label" placeholder='请输入车位名称'>
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click="getList">
          搜索
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' ref="multipleTable" :data="list" :row-key="getRowKeys" @selection-change="selectionChange" border fit highlight-current-row>
        <el-table-column label="#" align="center" type="selection" :reserve-selection='true' :selectable="selectable" width="50">
        </el-table-column>
        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车位编号">
          <template slot-scope="scope">
            <span>{{ scope.row.numStr }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车位类型">
          <template slot-scope="scope">
            <span>{{ scope.row.typeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="区域">
          <template slot-scope="scope">
            <span>{{ scope.row.area }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import { communityPage, coparkingPageParking } from '@/api/communityMan'

export default {
  components: {
    Pagination,
  },
  data () {
    return {
      list: [],

      listQuery: {
        limit: 10,
        label: "",
        page: 1,
        communityId: '',
        memberId: '',
      },

      communityList: [],

      total: 0,

      selectParkingList: [],

      selectList: [],

      selectIdList: []
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.communityMan.parkingDlgMul.dlgShow
      },
      set: function (val) {
        this.$store.commit('communityMan/parkingDlgMul/SET_DLGSHOW', val)
      }
    },

    memberId: {
      get: function () {
        return this.$store.state.communityMan.parkingDlgMul.memberId
      },
      set: function (val) {
        this.$store.commit('communityMan/parkingDlgMul/SET_MEMBERID', val)
      }
    },

    parkingList: {
      get: function () {
        return this.$store.state.communityMan.parkingDlgMul.list
      },
      set: function (val) {
        this.$store.commit('communityMan/parkingDlgMul/SET_LIST', val)
      }
    },

  },

  watch: {

    dlgShow (val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.multipleTable.clearSelection();
          this.listQuery.label = ""
          this.getCommunityList()
          this.getList()
        })
      }
    },

    parkingList (val) {
      this.selectParkingList = JSON.parse(JSON.stringify(val))
    },

    memberId: {
      handler (val) {
        this.listQuery.memberId = val
      },
      immediate: true
    }

  },

  methods: {
    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    selectable (row, index) {
      return !this.selectIdList.includes(row.id)

    },

    getRowKeys (row) {
      return row.id
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    selectionChange (val) {
      this.selectList = JSON.parse(JSON.stringify(val));
    },

    // 设置选中行
    toggleRowSelection () {
      let idList = []
      for (let i of this.selectParkingList) {
        if (!idList.includes(i['id'])) {
          idList.push(i['id'])
        }
      }
      this.selectIdList = idList
      console.log(idList)
      if (idList.length == 0) {
        this.$refs.multipleTable.clearSelection();
      } else {
        this.$nextTick(() => {
          this.list.forEach(item => {
            for (let i in idList) {
              if (idList[i] == item.id) {
                this.$refs.multipleTable.toggleRowSelection(item, true);
              }
            }
          });
        })
      }
    },

    getList () {
      this.list = []
      coparkingPageParking(this.listQuery).then(res => {
        if (res.data.code == 200) {
          this.list = res.data ? res.data.data : []
          this.total = res.data.page ? res.data.page.total : 0
          this.toggleRowSelection()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    subDlg () {
      this.$store.commit('communityMan/parkingDlgMul/SET_LIST', this.selectList)
      this.closeDlg()
    },

    closeDlg () {
      this.$store.commit('communityMan/parkingDlgMul/SET_DLGSHOW', false)
    }


  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>