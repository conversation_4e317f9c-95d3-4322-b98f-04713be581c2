<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' 
      title="选择部门" 
      
      :visible.sync="bmTreeState1" 
      width='600px'
      top='30px'
      append-to-body>
      <div class="">
        <el-input
          placeholder="输入关键字进行过滤"
          style="margin-bottom: 10px;"
          v-model="filterBmLeftText">
        </el-input>
        <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
        <div class='m-dialog-h'>
          <el-tree 
            :data="treeData" 
            ref="treeDom"
            default-expand-all
            :filter-node-method="filterNode"
            :props="defaultProps"
            @node-click="nodeClick">
          </el-tree>
        </div>
        
      </div>
      <div slot="footer" class="dialog-footer">
        <span class='dialog-footer-span' v-show='branchName'>当前选中：{{ branchName }}</span>
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { 
  findOrgBranchAll,  // 获取全部树（不根据权限）
  findTreeByFrom,  // 树根据权限

} from '@/api/dataDic'
import { setTimeout } from 'timers';
// import adminDashboard from './admin'
// import editorDashboard from './editor'

export default {
  name: 'Dashboard',
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      
      // 部门树
      treeData: [],
      selectNode: {},
      
      filterBmLeftText: '',  // 部门左侧筛选

      branchId: '',
      branchName: '',
      // 树过滤
      defaultProps: {
        children: 'children',
        label: 'label',
      },

      bmTreeIsRole1: '',  // 是否根据权限
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ]),
    bmTreeState1: {
      get: function() {
        let state = this.$store.getters.bmTreeState1
        if (state) {
          this.branchId = ''
          this.branchName = ''
          $('.tree-on').removeClass('tree-on')
          setTimeout(() => {
            this.bmTreeIsRole1 = this.$store.getters.bmTreeIsRole1

            this.findOrgBranchAll()
            
          }, 50)
        }
        return state
      },
      set: function(newVal) {
        this.$store.commit('SET_BMTREESTATE1', newVal)
      }
    }
  },
  watch: {
    filterBmLeftText(val) {
      this.$refs.treeDom.filter(val);
    }
  },
  created() {
    // 部门
    
  },
  methods: {
    // [[ 部门弹窗相关
    // 获取部门(根据权限)
    findOrgBranchAll() {
      if(this.bmTreeIsRole1) {
        // 根据权限
        findOrgBranchAll().then(res => {
          let code = res.data.code
          let data = res.data.data
          let msg = res.data.msg
          if (code === '200') {
            this.treeData = JSON.parse(JSON.stringify(res.data.list))
            if (msg == '该员工暂无权限查看！') {
              this.$message({
                type: 'warning',
                message: '该员工暂无权限查看！'
              })
            }
          } else {
            this.$message.error(msg)
          }
        })
      } 
      else {
        // 不根据权限
        findTreeByFrom().then(res => {
          let code = res.data.code
          let data = res.data.data
          let msg = res.data.msg

          if (code === '200') {
            this.treeData = JSON.parse(JSON.stringify(res.data.list))
          } else {
            this.$message.error(msg)
          }
        })
      }

    },

    // 节点点击事件
    nodeClick(data, node, mNode) {
      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      // this.filterText = data.label
      this.selectNode = data
      // alert(JSON.stringify(data))
      // 获取部门 下 岗位
      this.branchId = data.id
      this.branchName = data.label
    },
    
    // 提交
    bumenOkFunc() {
      if (this.branchName) { 
        this.$store.commit('SET_BMTREEBRANCHID1', 'empty')
        this.$store.commit('SET_BMTREEBRANCHNAME1', '')

        setTimeout(() => {
          this.bmTreeState1 = false
          this.$store.commit('SET_BMTREESTATE1', false)
          
          this.$store.commit('SET_BMTREEBRANCHID1', this.branchId)
          this.$store.commit('SET_BMTREEBRANCHNAME1', this.branchName)
        }, 50)
      } else {
        this.$message({
          type: 'warning',
          message: '请选择部门'
        })
      }
    },
    
    // 筛选部门
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 关闭弹窗 
    closeDialog() {
      this.$store.commit('SET_BMTREEISROLE1', true)
      this.$store.commit('SET_BMTREESTATE1', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.dialog-footer-span {
    font-size: 14px;
    color: #666;
    display: inline-block;
    padding-right: 10px;
  }
</style>