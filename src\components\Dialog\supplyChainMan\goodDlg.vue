<template>
  <el-dialog
    class="mazhenguo"
    :close-on-click-modal="false"
    :title="'选择商品'"
    :visible.sync="dlgShow"
    @close="closeDlg"
    width="1200px"
    top="30px"
    append-to-body
  >
    <div class="left-right-container clearfix">
      <div class="left-container">
        <el-input placeholder="输入关键字进行过滤" v-model="filterText"> </el-input>
        <el-tree
          :expand-on-click-node="false"
          :data="treeList"
          default-expand-all
          highlight-current
          ref="goodTree"
          node-key="id"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-click="treeNodeClick"
        ></el-tree>
      </div>
      <div class="right-container">
        <div class="filter-container">
          <div class="fr">
            <el-input v-model="listQuery.label" placeholder="请填写商品名称" @keyup.enter.native="searchFunc">
              <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
            </el-input>
            <el-select v-model="listQuery.commonUseId" clearable placeholder="常用类别" v-if="branchShow">
              <el-option v-for="item of commonUseList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
            <el-button icon="el-icon-search" type="success" size="mini" @click="searchFunc"> 搜索 </el-button>
          </div>
        </div>
        <div class="table-container">
          <el-table class="m-small-table" :data="list" @row-click="rowClick" border fit highlight-current-row>
            <el-table-column label="#" width="50" align="center">
              <template slot-scope="scope">
                <el-radio v-model="selectGoodId" :label="scope.row.id">
                  <i></i>
                </el-radio>
              </template>
            </el-table-column>

            <el-table-column label="商品名称" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="商品编码" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.code }}</span>
              </template>
            </el-table-column>

            <el-table-column label="规格型号" width="120" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <span>{{ scope.row.model }}</span>
              </template>
            </el-table-column>

            <el-table-column label="照片" width="70" align="center">
              <template slot-scope="scope">
                <el-image
                  v-if="scope.row.imgUrl"
                  style="width: 40px; height: 40px"
                  :preview-src-list="[scope.row.imgUrl]"
                  :src="scope.row.imgUrl"
                  alt=""
                ></el-image>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="page-container">
          <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back"> 取 消 </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'

// import { cacfsTree, caesPage } from '@/api/supplyChainApi'
import { postAction, getAction } from '@/api'

export default {
  props: {
    dlgQuery0: {},
  },
  components: {
    Pagination,
  },
  data() {
    return {
      filterText: '',

      treeList: [],
      defaultOpenList: [],
      list: [],

      listQuery: {
        label: '', // 模糊
        page: 1,
        limit: 20,

        code: '', // 编码
        name: '', //name

        warehouseId: '', // 库房
      },

      total: 0,

      selectGoodInfo: {},

      selectGoodId: '',

      selectGoodName: '',

      selectGoodType: '',

      defaultProps: {
        children: 'children',
        label: 'name',
      },

      commonUseList: [
        { id: 0, name: '全部' },
        { id: 1, name: '部门常用' },
      ],
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.supplyChainMan.goodDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('supplyChainMan/goodDlg/SET_DLGSHOW', val)
      },
    },

    dlgQuery: {
      get: function () {
        return this.$store.state.supplyChainMan.goodDlg.dlgQuery
      },
      set: function (val) {
        this.$store.commit('supplyChainMan/goodDlg/SET_DLGQUERY', val)
      },
    },

    goodId: {
      get: function () {
        return this.$store.state.supplyChainMan.goodDlg.goodId
      },
      set: function (val) {
        this.$store.commit('supplyChainMan/goodDlg/SET_GOODID', val)
      },
    },

    goodName: {
      get: function () {
        return this.$store.state.supplyChainMan.goodDlg.goodName
      },
      set: function (val) {
        this.$store.commit('supplyChainMan/goodDlg/SET_GOODNAME', val)
      },
    },

    goodType: {
      get: function () {
        return this.$store.state.supplyChainMan.goodDlg.goodType
      },
      set: function (val) {
        this.$store.commit('supplyChainMan/goodDlg/SET_GOODTYPE', val)
      },
    },

    goodInfo: {
      get: function () {
        return this.$store.state.supplyChainMan.goodDlg.goodInfo
      },
      set: function (val) {
        this.$store.commit('supplyChainMan/goodDlg/SET_GOODINFO', val)
      },
    },

    branchShow: {
      get: function () {
        return this.$store.state.supplyChainMan.goodDlg.branchShow
      },
      set: function (val) {
        this.$store.commit('supplyChainMan/goodDlg/SET_BRANCHSHOW', val)
      },
    },
  },

  watch: {
    filterText(val) {
      this.$refs.goodTree.filter(val)
    },

    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.goodId)) {
          this.selectGoodId = ''
          this.selectGoodName = ''
          this.selectGoodType = ''
          this.selectGoodInfo = {}
        }
        this.getTreeList()
        this.list = []

        this.selectGoodType = ''

        console.log('---this.dlgQuery0', this.dlgQuery0)
        setTimeout(() => {
          if (this.dlgQuery) {
            if (this.dlgQuery0) {
              this.listQuery = { ...this.listQuery, ...this.dlgQuery0 }
            }
          }
          this.getList()
        }, 200)
        // if (!utils.isNull(this.selectGoodType)) {
        //   this.getList()
        // }
      }
    },

    goodId(val) {
      this.selectGoodId = val
    },

    goodName(val) {
      this.selectGoodName = val
    },

    goodType(val) {
      this.selectGoodType = val
    },

    goodInfo(val) {
      this.selectGoodInfo = val
    },
  },

  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },

    // // 树节点展开
    // handleNodeExpand(data) {
    //   // 保存当前展开的节点
    //   let flag = false
    //   this.defaultOpenList.some((item) => {
    //     if (item === data.commodityCode) {
    //       // 判断当前节点是否存在， 存在不做处理
    //       flag = true
    //       return true
    //     }
    //   })
    //   if (!flag) {
    //     // 不存在则存到数组里
    //     this.defaultOpenList.push(data.commodityCode)
    //   }
    // },

    // 树节点关闭
    // handleNodeCollapse(data) {
    //   this.defaultOpenList.some((item, i) => {
    //     if (item === data.commodityCode) {
    //       // 删除关闭节点
    //       this.defaultOpenList.length = i
    //     }
    //   })
    // },

    // 树点击
    treeNodeClick(data) {
      if (data.id === 0 || data.id === '0') {
        this.selectGoodType = ''
      } else {
        this.selectGoodType = data.code
      }
      this.getList()
    },

    resetStr() {
      this.listQuery.label = ''
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    rowClick(row, column, event) {
      this.selectGoodId = row['id']
      this.selectGoodName = row['name']
      this.selectGoodInfo = JSON.parse(JSON.stringify(row))
      console.log('this.selectGoodInfo', this.selectGoodInfo)
    },

    // 获取树列表
    getTreeList() {
      let userInfo = JSON.parse(window.localStorage.userInfo)
      getAction(`/schain/commodity/class/tree/${userInfo.projectId}`).then((res) => {
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code == 200) {
          let list = res.data.data
          this.treeList = list
          if (this.defaultOpenList.length == 0) {
            console.log("list[0]['id']", list[0]['id'])
            this.defaultOpenList = [list[0]['id']]
          }
          this.$nextTick(() => {
            this.$refs.goodTree.setCurrentKey(this.selectGoodType)
          })
        } else {
          this.$message.error(msg)
        }
      })
    },

    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },

    getList() {
      this.list = []

      this.listQuery.code = this.selectGoodType

      postAction('/schain/commodity/page', this.listQuery).then((res) => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let data = res.data.data
          if (utils.isNull(data)) {
            this.total = 0
            return
          }
          let list = res.data.data
          this.list = list
          this.total = res.data.page.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      this.$emit('backFunc', this.selectGoodInfo)
      console.log('this.selectGoodInfo', this.selectGoodInfo)
      this.goodId = this.selectGoodId
      this.goodName = this.selectGoodName
      this.goodType = this.selectGoodType
      this.goodInfo = this.selectGoodInfo
      this.$store.commit('supplyChainMan/goodDlg/SET_GOODID', this.goodId)
      this.$store.commit('supplyChainMan/goodDlg/SET_GOODNAME', this.goodName)
      this.$store.commit('supplyChainMan/goodDlg/SET_GOODTYPE', this.goodType)
      this.$store.commit('supplyChainMan/goodDlg/SET_GOODINFO', this.goodInfo)

      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('supplyChainMan/goodDlg/SET_DLGQUERY', {})
      this.$store.commit('supplyChainMan/goodDlg/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 800px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>