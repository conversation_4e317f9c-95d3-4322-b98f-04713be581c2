<template>
  <div class="gytreeone">
    <!-- 供应链- 商品档案分类 树 -->
    <el-dialog :close-on-click-modal="false" :title="diaTitle" :visible.sync="gylTreeOneState" width="600px" top="30px" append-to-body>
      <div class="">
        <el-input placeholder="输入关键字进行过滤" style="margin-bottom: 10px" v-model="filterBmLeftText"> </el-input>
        <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
        <div class="m-dialog-h">
          <el-tree
            :data="treeData"
            ref="treeDom"
            default-expand-all
            :filter-node-method="filterNode"
            :props="defaultProps"
            @node-click="nodeClick"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>（{{ data.code }}）{{ data.name }}</span>
              <!-- <span :class="data.commodityName == rightFormData.commodityName ? 'tree-on-span on' : 'tree-on-span'" :title='data.commodityName'>（{{ data.commodityCode }}）{{ data.commodityName }}</span> -->
            </span>
          </el-tree>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <span class="dialog-footer-span" v-show="selectNode && selectNode.name">当前选中：{{ selectNode.name }}</span>
        <el-button @click="closeDialog" icon="el-icon-back">取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { postAction, getAction } from '@/api'

import {
  cacfsTree, // 商品树
  acfsTree, // 资产树
  storageRoomTree, // 库房树
} from '@/api/supplyChainApi'
import { setTimeout } from 'timers'
// import adminDashboard from './admin'
// import editorDashboard from './editor'

export default {
  name: '',
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      // 部门树
      treeData: [],
      selectNode: {},

      filterBmLeftText: '', // 部门左侧筛选

      branchId: '', // 选择的 id
      branchName: '', // 选择的 name

      dialogType: '', // 弹窗类别，资产，商品，供应商
      // 树过滤
      defaultProps: {
        children: 'children',
        label: 'label',
      },

      bmTreeIsRole: '', // 是否根据权限

      diaTitle: '', // 标题
    }
  },
  computed: {
    ...mapGetters(['roles']),
    gylTreeOneState: {
      get: function () {
        let state = this.$store.getters.gylTreeOneState
        if (state) {
          // 设置弹窗头
          let diaType = this.$store.getters.gylTreeOneType
          switch (diaType) {
            case 'zichan':
              this.diaTitle = '选择资产分类'
              break
            case 'shangpin':
              this.diaTitle = '选择商品档案分类'
              break
            case 'gongyingshang':
              this.diaTitle = '选择供应商分类'
              break
            case 'kufang':
              this.diaTitle = '选择库房'
              break
          }

          this.selectNode = ''
          $('.gytreeone .tree-on').removeClass('tree-on')
          setTimeout(() => {
            this.dialogType = this.$store.getters.gylTreeOneType
            this.getTree()
          }, 50)
        }
        return state
      },
      set: function (newVal) {
        this.$store.commit('SET_GYLTREEONESTATE', newVal)
      },
    },
  },
  watch: {
    filterBmLeftText(val) {
      this.$refs.treeDom.filter(val)
    },
  },
  created() {
    // 部门
  },
  methods: {
    // [[ 部门弹窗相关
    getTree() {
      if (this.dialogType == 'shangpin') {
        let userInfo = JSON.parse(window.localStorage.userInfo)
        getAction(`/schain/commodity/class/tree/${userInfo.projectId}`).then((res) => {
          let code = res.data.code
          let data = res.data.data
          let msg = res.data.msg

          if (code === '200') {
            let listStr = JSON.stringify(res.data.data)

            this.treeData = JSON.parse(listStr)
          } else {
            this.$message.error(msg)
          }
        })
      } else if (this.dialogType == 'zichan') {
        acfsTree().then((res) => {
          let code = res.data.code
          let data = res.data.data
          let msg = res.data.msg

          if (code === '200') {
            let listStr = JSON.stringify(res.data.list)
            listStr = listStr.replace(/classificationName/g, 'mName')
            listStr = listStr.replace(/classificationNum/g, 'mBM')
            console.log('222', listStr)

            this.treeData = JSON.parse(listStr)
          } else {
            this.$message.error(msg)
          }
        })
      } else if (this.dialogType == 'gongyingshang') {
        acfsTree().then((res) => {
          let code = res.data.code
          let data = res.data.data
          let msg = res.data.msg

          if (code === '200') {
            let listStr = JSON.stringify(res.data.list)
            listStr = listStr.replace(/classificationName/g, 'mName')
            listStr = listStr.replace(/classificationNum/g, 'mBM')
            console.log('222', listStr)

            this.treeData = JSON.parse(listStr)
          } else {
            this.$message.error(msg)
          }
        })
      } else if (this.dialogType == 'kufang') {
        storageRoomTree().then((res) => {
          let code = res.data.code
          let data = res.data.data
          let msg = res.data.msg

          if (code === '200') {
            let listStr = JSON.stringify(res.data.list)
            listStr = listStr.replace(/storageRoomName/g, 'mName')
            listStr = listStr.replace(/storageRoomCode/g, 'mBM')
            console.log('222', listStr)

            this.treeData = JSON.parse(listStr)
          } else {
            this.$message.error(msg)
          }
        })
      }
    },
    // 节点点击事件
    nodeClick(data, node, mNode) {
      $('.gytreeone .tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.gytreeone .is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      // this.filterText = data.label
      this.selectNode = data
      // alert(JSON.stringify(data))
      // 获取部门 下 岗位
      // this.branchId = data.this.branchName = data.mName
    },

    // 提交
    bumenOkFunc() {
      if (this.selectNode) {
        // this.$store.commit('SET_GYLTREEONEID', 'empty')
        // this.$store.commit('SET_GYLTREEONENAME', 'empty')

        setTimeout(() => {
          this.gylTreeOneState = false
          this.$store.commit('SET_GYLTREEONESTATE', false)

          // this.$store.commit('SET_GYLTREEONEID', this.branchId)
          // this.$store.commit('SET_GYLTREEONENAME', this.branchName)

          let selectNode = JSON.parse(JSON.stringify(this.selectNode))
          delete selectNode.children
          this.$emit('backFunc', selectNode)
        }, 50)
      } else {
        this.$message({
          type: 'warning',
          message: `请${this.diaTitle}`,
        })
      }
    },

    // 筛选部门
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },

    // 关闭弹窗
    closeDialog() {
      this.$store.commit('SET_GYLTREEONESTATE', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss">
.dialog-footer-span {
  font-size: 14px;
  color: #666;
  display: inline-block;
  padding-right: 10px;
}
</style>