<template>
  <el-dialog :close-on-click-modal="false" :title="'选择业主'" :visible.sync="dlgShow" append-to-body>
    <div class="filter-container">
      <el-form inline @submit.native.prevent>
        <el-form-item label="关键字">
          <el-input v-model="listQuery.label" placeholder="请输入业主姓名" @keyup.enter.native="getList">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList"> 搜索 </el-button>
      </el-form>
    </div>
    
      <el-table class="m-small-table" height="400" :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="#" width="60">
          <template slot-scope="scope">
            <el-radio v-model="selectMemberId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="性别">
          <template slot-scope="scope">
            <span>{{ scope.row.sex }}</span>
          </template>
        </el-table-column>

        <el-table-column label="年龄">
          <template slot-scope="scope">
            <span>{{ scope.row.age }}</span>
          </template>
        </el-table-column>

        <el-table-column label="身份证">
          <template slot-scope="scope">
            <span>{{ scope.row.idCard }}</span>
          </template>
        </el-table-column>

        <el-table-column label="联系方式">
          <template slot-scope="scope">
            <span>{{ scope.row.phone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="房屋">
          <template slot-scope="scope">
            <span>{{ scope.row.roomName }}</span>
          </template>
        </el-table-column>
      </el-table>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
    <div slot="footer" class="dialog-footer" style="margin-top:10px">
      <el-button @click="closeDlg" icon="el-icon-back"> 取 消 </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import { buildingmemberPage, buildingmemberPageView } from '@/api/ownerMan'

export default {
  components: {
    Pagination,
  },
  data() {
    return {
      list: [],

      listQuery: {
        limit: 10,
        label: '',
        page: 1,
        type: 1, //1 业主 2成员 3租户
      },

      total: 0,

      selectMemberId: '',

      selectMemberName: '',

      selectMemberInfo: {},
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.communityMan.memberDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('communityMan/memberDlg/SET_DLGSHOW', val)
      },
    },

    dlgType: {
      get: function () {
        return this.$store.state.communityMan.memberDlg.dlgType
      },
      set: function (val) {
        this.$store.commit('communityMan/memberDlg/SET_DLGTYPE', val)
      },
    },

    memberId: {
      get: function () {
        return this.$store.state.communityMan.memberDlg.memberId
      },
      set: function (val) {
        this.$store.commit('communityMan/memberDlg/SET_MEMBERID', val)
      },
    },

    memberName: {
      get: function () {
        return this.$store.state.communityMan.memberDlg.memberName
      },
      set: function (val) {
        this.$store.commit('communityMan/memberDlg/SET_MEMBERNAME', val)
      },
    },

    memberInfo: {
      get: function () {
        return this.$store.state.communityMan.memberDlg.memberInfo
      },
      set: function (val) {
        this.$store.commit('communityMan/memberDlg/SET_MEMBERINFO', val)
      },
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.memberId)) {
          this.selectMemberId = ''
          this.selectMemberName = ''
          this.selectMemberInfo = {}
        }
        this.getList()
      }
    },

    memberId(val) {
      this.selectMemberId = val
    },

    memberName(val) {
      this.selectMemberName = val
    },

    memberInfo(val) {
      this.selectMemberInfo = val
    },
  },

  methods: {
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    rowClick(row, column, event) {
      this.selectMemberId = row['id']
      this.selectMemberName = row['name']
      this.selectMemberInfo = JSON.parse(JSON.stringify(row))
    },

    getList() {
      this.list = []
      if (this.dlgType === 'VIEW') {
        buildingmemberPageView(this.listQuery).then((res) => {
          if (res.data.code == 200) {
            this.list = res.data ? res.data.data : []
            this.total = res.data.page ? res.data.page.total : 0
          } else {
            this.$message.error(res.data.msg)
          }
        })
      } else {
        buildingmemberPage(this.listQuery).then((res) => {
          if (res.data.code == 200) {
            this.list = res.data ? res.data.data : []
            this.total = res.data.page ? res.data.page.total : 0
          } else {
            this.$message.error(res.data.msg)
          }
        })
      }
    },

    subDlg() {
      this.memberId = this.selectMemberId
      this.memberName = this.selectMemberName
      this.memberInfo = this.selectMemberInfo
      this.$store.commit('communityMan/memberDlg/SET_MEMBERID', this.memberId)
      this.$store.commit('communityMan/memberDlg/SET_MEMBERNAME', this.memberName)
      this.$store.commit('communityMan/memberDlg/SET_MEMBERINFO', this.memberInfo)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('communityMan/memberDlg/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>