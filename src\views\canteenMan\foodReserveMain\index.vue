

<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-date-picker
              class="fl"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="预定开始时间"
              end-placeholder="预定结束时间"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 280px"
            >
            </el-date-picker>
            <el-input
              v-model="searchForm.dishesName"
              placeholder="请输入菜品名称"
              clearable
              class="fl ml10"
              style="width: 160px"
              @change="searchFunc"
            ></el-input>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
            <el-button
              type="success"
              @click="showDlg('add')"
              icon="el-icon-plus"
              class="fl ml10"
              >添加</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc(100vh - 290px)"
      ref="tableBar"
      class="m-small-table"
      :loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="startTime"
        label="预定开始时间"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="endTime"
        label="预定结束时间"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="dishesName"
        label="菜品"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createTime.slice(0, 10) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="creatorName"
        label="创建人"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" width="280" align="center">
        <template slot-scope="scope">
          <el-button
            @click="showDlg('info', scope.row)"
            icon="el-icon-document"
            size="mini"
            type="success"
            title="详情"
            plain
            >详情</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="showDlg('edit', scope.row)"
            plain
            icon="el-icon-edit"
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="delFunc(scope.row)"
            plain
            icon="el-icon-delete"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getList"
    />
    <div class="clear"></div>

    <AddEdit ref="addEdit" :dlgType="dlgType" />
  </div>
</template>
  
  <script>
import AddEdit from "./addEdit";
import { getAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination";

export default {
  components: {
    AddEdit,
    Pagination,
  },
  data() {
    return {
      dateRange: undefined,
      searchForm: {
        startTime: "",
        endTime: "",
        dishesName: "",
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      total: 0,
      listLoading: false,
      dlgType: "add",
    };
  },
  mounted() {
    this.searchFunc();
  },
  methods: {
    showDlg(type, row) {
      this.$refs.addEdit.init(type, row);
    },
    searchFunc() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    getList() {
      let postData = { ...this.searchForm };
      this.listLoading = true;

      if (this.dateRange) {
        postData.startTime = this.dateRange[0];
        postData.endTime = this.dateRange[1];
      }
      getAction(`/canteen/cn/food-booking/page`, postData).then((res) => {
        let { code, data } = res.data;
        this.listLoading = false;
        if (code === "200") {
          data.list.forEach((item) => {
            if (item.imgUrl) {
              item.imgUrl = JSON.parse(item.imgUrl);
            }
          });
          this.tableData = data.list || [];
          this.total = data.total || 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAction(`/canteen/cn/food-booking/delete?id=${row.id}`).then(
          (res) => {
            if (res.data.code === "200") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.searchFunc();
            } else {
              this.$message.error(res.data.msg);
            }
          }
        );
      });
    },
  },
};
</script>
  
  <style rel="stylesheet/scss" lang="scss" scoped>
// 样式部分根据需要添加
</style>