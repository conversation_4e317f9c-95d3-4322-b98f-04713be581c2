<template>
  <!--订单管理-->
  <div class="app-container">
    <div class="filter-container">
      <el-form ref="searchForm" class='n-search' label-width="90px" @submit.native.prevent>
        <div class="n-search-bar">
          <div class='n-search-item fl'>
            <el-form-item label="订单日期：">
              <el-date-picker v-model="listQuery.startTime" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </div>
          <div class='n-search-item n-search-item-r fr'>
            <el-select v-model="listQuery.status" clearable placeholder="支付状态">
              <el-option v-for="item of paySelect" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
            <el-input v-model="listQuery.str" placeholder='请输入就诊医院\患者姓名\性别\联系电话\评价\陪护人'>
              <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
            </el-input>
            <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">搜索</el-button>
            <el-button icon='el-icon-refresh' type="primary" size='mini' @click="resetItem">重置</el-button>
          </div>
          <div class="clear"></div>
        </div>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="状态">
          <template slot-scope="scope">
            <span>{{ scope.row.zhuangtai }}</span>
          </template>
        </el-table-column>

        <el-table-column label="任务内容">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="就诊医院">
          <template slot-scope="scope">
            <span>{{ scope.row.projectName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="患者姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.patientName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="性别/年龄">
          <template slot-scope="scope">
            <span>{{ scope.row.sexAge }}</span>
          </template>
        </el-table-column>

        <el-table-column label="联系电话">
          <template slot-scope="scope">
            <span>{{ scope.row.patientPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="紧急联系方式">
          <template slot-scope="scope">
            <span>{{ scope.row.emergencyPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="陪护时间">
          <template slot-scope="scope">
            <span>{{ scope.row.times }}</span>
          </template>
        </el-table-column>

        <el-table-column label="详细情况说明">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="评价">
          <template slot-scope="scope">
            <span>{{ scope.row.evaluation }}</span>
          </template>
        </el-table-column>

        <el-table-column label="陪护人">
          <template slot-scope="scope">
            <span>{{ scope.row.pzStaffName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="患者总结">
          <template slot-scope="scope">
            <span>{{ scope.row.conclusion }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination' //分页
import { getDataDict } from "@/utils";
import { findPzTaskDynamic, findPzTaskUserDynamic, pzTaskAssign } from "@/api/medicalAuxiliaryManSystem/accompany/accompany"

export default {
  components: {
    Pagination
  },
  data () {
    return {
      paySelect: [
        {
          id: '0',
          name: '未指派'
        },
        {
          id: '1',
          name: '已指派'
        },
        {
          id: '2',
          name: '已接受未认证'
        },
        {
          id: '3',
          name: '已认证未完成'
        },
        {
          id: '4',
          name: '已完成未邮寄'
        },
        {
          id: '5',
          name: '已加时未支付'
        },
        {
          id: '6',
          name: '已加时已支付'
        },
        {
          id: '7',
          name: '已确认未邮寄'
        },
        {
          id: '10',
          name: '已完成'
        },
        {
          id: '99',
          name: '已取消'
        }
      ],
      list: [],
      listQuery: {
        startTime: "",
        status: "",
        str: "",
        page: 1,
        size: 20,
      },
      total: 0,
      listLoading: false,
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.list = []
      this.listLoading = true;
      findPzTaskDynamic(this.listQuery).then(res => {
        this.listLoading = false;
        let code = res.data.code;
        let msg = res.data.msg;
        if (code === "200") {
          let data = res.data.data;
          if (data === null) {
            this.$message({
              type: "warning",
              message: msg
            });
            return false
          }
          this.total = data.total
          this.list = res.data.list;
        }
        else {
          this.$message.error(msg);
        }
      });
    },
    resetStr () {
      this.listQuery.str = ''
      this.getList()
    },
    searchItem () {
      this.getList()
    },
    resetItem () {
      this.listQuery.str = ''
      this.listQuery.status = ''
      this.listQuery.startTime = ''
      this.getList()
    },
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>