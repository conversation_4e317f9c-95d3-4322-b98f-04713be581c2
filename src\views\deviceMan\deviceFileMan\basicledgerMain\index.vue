<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <!-- <div class="search-item">
            <div class="search-item-label lh28">选择项目：</div>
            <el-select
              class="fl"
              style="width: 220px"
              v-model="searchForm.projectId"
              placeholder="选择项目"
              @change="projectChange"
              filterable
              clearable
            >
              <el-option
                v-for="item of projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div> -->
          <div class="search-item">
            <div class="search-item-label lh28">选择设备类型：</div>
            <el-select
              class="fl"
              style="width: 220px"
              v-model="searchForm.equType"
              placeholder="选择设备类型"
              @change="searchFunc"
              filterable
              clearable
            >
              <el-option
                v-for="item of equipmentTypeOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input
              v-model="searchForm.equName"
              placeholder="关键字"
              clearable
              class="fl"
              style="width: 160px"
              @change="searchFunc"
            ></el-input>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
            <el-button
              type="success"
              @click="showDlg('add')"
              icon="el-icon-plus"
              class="fl ml10"
              >添加</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc(100vh - 290px)"
      ref="tableBar"
      class="m-small-table"
      v-loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="设备二维码"
        align="center"
        prop="equName"
        width="110"
      >
        <template slot-scope="scope">
          <i
            @click="showQrCode(scope.row)"
            class="el-icon-picture"
            style="font-size: 24px; cursor: pointer; color: #666"
          ></i>
        </template>
      </el-table-column>
      <el-table-column
        prop="equName"
        label="设备名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="equModel"
        label="设备型号"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="controlArea"
        label="设备控制区域"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="equTypeStr"
        label="设备类型"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="statusStr"
        label="设备使用状态"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="responsiblePersonName"
        label="设备负责人"
        width="200"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="equPosition"
        label="设备所在位置"
        width="200"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" width="250" align="center">
        <template slot-scope="scope">
          <!-- <el-button
            type="success"
            size="mini"
            @click="infoFunc(scope.row)"
            plain
            icon="el-icon-d-arrow-right"
            >运行记录</el-button
          > -->
          <el-button
            @click="showInfo(scope.row)"
            icon="el-icon-document"
            size="mini"
            type="success"
            title="详情"
            plain
            >详情</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="showDlg('edit', scope.row)"
            plain
            icon="el-icon-edit"
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="delFunc(scope.row)"
            plain
            icon="el-icon-delete"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="searchFunc()"
    />
    <div class="clear"></div>

    <addEdit ref="addEdit" :dlgType="dlgType"></addEdit>
    <infoView ref="infoView"></infoView>
    <el-dialog
      :title="'设备二维码-' + selectRow.equName"
      :visible.sync="dialogVisible"
      width="300px"
    >
      <div class="qrbox2">
        <div v-if="dialogVisible" ref="qrCode"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import addEdit from "./addEdit";
import infoView from "./infoView";
import { postAction, getAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination"; // 分页
import QRCode from "qrcodejs2";
import { getDataDict } from "@/utils";

export default {
  components: {
    addEdit,
    infoView,
    Pagination,
  },
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),
      searchForm: {
        projectId: "",
        equType: "",
        equName: "",
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      total: 0,
      listLoading: false,
      dlgType: "add",
      projectList: [],
      dialogVisible: false,
      selectRow: "",
      equipmentTypeOptions: [],
    };
  },
  created() {
    getDataDict(this, "equipmentType", "equipmentTypeOptions");
  },
  mounted() {
    // this.searchFunc()
    this.searchForm.projectId = this.userInfo.projectId;
  },
  methods: {
    showQrCode(row) {
      this.selectRow = row;
      this.dialogVisible = true;
      setTimeout(() => {
        let qrUrl = row.equCode + "";
        this.qrcode = new QRCode(this.$refs.qrCode, {
          text: qrUrl,
          width: 200,
          height: 200,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H,
        });
      }, 200);
    },
    projectChange() {
      this.searchFunc();
    },
    // infoFunc(row) {
    //   this.$router.push({
    //     path: "/deviceMan/deviceFileMan/deviceOperRecords",
    //     query: {
    //       projectId: row.projectId,
    //       modelId: row.id,
    //       responsiblePersonId: row.responsiblePersonId,
    //     },
    //   });
    // },
    showInfo(row) {
      this.$refs.infoView.init(row);
    },
    showDlg(type, row) {
      let addEdit = this.$refs.addEdit;
      this.dlgType = type;
      if (type == "add") {
        addEdit.title = "添加设备";
      } else if (type == "info") {
        addEdit.title = "设备详情";
      } else {
        addEdit.title = "编辑设备";
        this.$refs.addEdit.getEquPositionList(row.projectId);
      }
      if (type !== "add") {
        getAction(`sa/green/equipment-manage/get?id=${row.id}`).then((res) => {
          console.log(res.data);
          let { code, data } = res.data;
          if (code === "200") {
            //pestIds返回来的是数字类型数组，转换成字符串类型
            if (data.picUrl) {
              data.picUrl = data.picUrl.split(",");
            } else {
              data.picUrl = [];
            }
            if (data.drawingUrl) {
              data.drawingUrl = data.drawingUrl.split(",");
            } else {
              data.drawingUrl = [];
            }
            data.useAge = data.useAge.split('~');
            data.useAgeStart = data.useAge[0];
            data.useAgeEnd = data.useAge[1];
            addEdit.listData = data.list;
            delete data.list;
            addEdit.formData = data ? JSON.parse(JSON.stringify(data)) : [];
            console.log(data);

            // for (let i = 0; i < this.form.pestIds.length; i++) {
            //   console.log(this.form.pestIds[i]);
            //   this.form.pestIds[i] = this.form.pestIds[i].toString();
            // }
          } else {
            this.$message.error(res.data.msg);
          }
        });
      }
      this.$refs.addEdit.dialogVisible = true;
    },
    searchFunc() {
      let postData = JSON.parse(JSON.stringify(this.searchForm));
      if (postData.projectId == "") {
        this.$message.warning("请先选择项目");
        return;
      }
      this.listLoading = true;
      getAction(`sa/green/equipment-manage/page`, postData).then((res) => {
        this.listLoading = false;
        let { code, data } = res.data;
        if (code === "200") {
          this.tableData = data.list ? data.list : [];
          this.total = data.total ? data.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAction(`sa/green/equipment-manage/delete?id=${row.id}`).then(
          (res) => {
            if (res.data.code === "200") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.searchFunc();
            } else {
              this.$message.error(res.data.msg);
            }
          }
        );
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.qrbox2 {
  padding: 36px 0 64px;
  width: 200px;
  height: 200px;
  margin: 0 auto;
  box-sizing: content-box;
}
</style>