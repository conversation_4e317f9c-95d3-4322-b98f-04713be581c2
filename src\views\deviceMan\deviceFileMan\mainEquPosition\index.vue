<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input
              v-model="searchForm.equPosition"
              placeholder="请输入设备所在位置"
              clearable
              class="fl"
              style="width: 180px"
              @change="searchFunc"
            ></el-input>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
            <el-button
              type="success"
              @click="showDlg('add')"
              icon="el-icon-plus"
              class="fl ml10"
              >添加</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc(100vh - 290px)"
      ref="tableBar"
      class="m-small-table"
      v-loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="projectName"
        label="项目"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="equPosition"
        label="设备所在位置"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="创建人姓名	"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" width="350" align="center">
        <template slot-scope="scope">
          <el-button
            @click="showDlg('info', scope.row)"
            icon="el-icon-document"
            size="mini"
            type="success"
            title="详情"
            plain
            >详情</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="showDlg('edit', scope.row)"
            plain
            icon="el-icon-edit"
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="delFunc(scope.row)"
            plain
            icon="el-icon-delete"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getList()"
    />
    <div class="clear"></div>

    <addEdit
      ref="addEdit"
      :dlgType="dlgType"
    ></addEdit>
  </div>
</template>

<script>
import { getDataDict } from "@/utils";
import addEdit from "./addEdit";
import { postAction, getAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination"; // 分页
export default {
  components: {
    Pagination,
    addEdit,
  },
  data() {
    return {
      searchForm: {
        projectId: JSON.parse(window.localStorage.userInfo).projectId,
        equPosition: "",
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      total: 0,
      listLoading: false,
      dlgType: "add",
    };
  },
  created() {},
  mounted() {
  },
  methods: {
    projectIdChange() {
      this.searchFunc();
    },
    async searchFunc() {
      this.searchForm.pageNo = 1;
      await this.getList();
    },
    async getList() {
      const { equPosition, pageNo, pageSize, projectId } = this.searchForm;
      this.listLoading = true;
      try {
        const res = await getAction(
          `sa/green/equ/equ-position/page?projectId=${projectId}&equPosition=${equPosition}&pageNo=${pageNo}&pageSize=${pageSize}`
        );
        const { code, data } = res.data;
        if (code === "200") {
          this.tableData = data.list || [];
          this.total = data.total || 0;
        } else {
          this.$message.error(res.data.msg);
        }
      } catch (error) {
        this.$message.error("获取数据失败");
      } finally {
        this.listLoading = false;
      }
    },
    showDlg(type, row) {
      const titles = { add: "添加", info: "详情", edit: "编辑" };
      const addEdit = this.$refs.addEdit;
      this.dlgType = type;

      // 设置标题
      addEdit.title = titles[type];

      // 如果不是添加操作，则需要获取数据
      if (type !== "add") {
        getAction(`sa/green/equ/equ-position/get?id=${row.id}`)
          .then((res) => {
            const { code, data } = res.data;
            if (code === "200") {
              addEdit.formData = JSON.parse(JSON.stringify(data));
            } else if (res.data.msg) {
              this.$message.error(res.data.msg);
            } else {
              this.$message.error("未知错误");
            }
          })
          .catch((error) => {
            this.$message.error("请求出错");
          });
      }

      // 显示对话框
      addEdit.dialogVisible = true;
    },
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return deleteAction(`sa/green/equ/equ-position/delete?id=${row.id}`);
        })
        .then((res) => {
          if (res.data.code === "200") {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.searchFunc();
          } else {
            this.$message.error(res.data.msg);
          }
        })
        .catch((error) => {
          // 添加错误处理逻辑
          console.error("删除操作失败", error);
          this.$message.error("删除失败，请稍后重试");
        });
    },
  },
};
</script>

<style></style>