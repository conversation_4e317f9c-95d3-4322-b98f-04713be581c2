<!-- 消毒设备统计 -->
<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label-width="0">
          <el-input
            @focus="showKeshiDlg"
            class=""
            placeholder="请选择科室"
            v-model="listQuery.departmentName"
            style="width: 140px"
            readonly
          >
          </el-input>
        </el-form-item>

        <el-form-item label="消毒设备:">
          <el-select v-model="listQuery.pointEquipId" @change="pointEquipIdChange" clearable placeholder="请选择" style="width: 130px">
            <el-option v-for="item of navList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="消毒时间:">
          <el-date-picker
            class=""
            style="width: 230px"
            v-model="listQuery.dateRange"
            @change="searchFunc"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="mini"
          >
          </el-date-picker>
        </el-form-item>
        <el-button @click="searchFunc" icon="el-icon-search" type="success" size="mini">查询</el-button>
        <!-- <el-button @click="printItem" icon="el-icon-printer" size="mini" type="primary" plain>打印</el-button> -->
        <el-button @click="downLoadFunc" icon="el-icon-download" size="mini" type="primary" plain>导出Excel</el-button>
      </el-form>
    </div>

    <div style="height: calc(100vh - 400px); overflow: auto; width: 100%; box-sizing: border-box">
      <el-row :gutter="20" style="width: 100%">
        <el-col :xl="16" :lg="24" style="margin-bottom: 20px">
          <table v-if="tableHead0.length" class="m-table" style="width: 100%; margin-top: 4px">
            <thead>
              <tr>
                <td colspan="9999" class="tac">{{ tableHead0[0][0] }}</td>
              </tr>
              <tr v-for="index in lenMax">
                <template v-for="(item2, index2) of tableHead0">
                  <td
                    class="tac"
                    v-if="(item2[index] && index2 != 0 && item2[index] != tableHead0[index2 - 1][index]) || (item2[index] && index2 == 0)"
                    :colspan="colspanFunc(index, index2)"
                    :rowspan="!item2[index + 1] && index != lenMax - 1 ? '2' : '1'"
                  >
                    {{ item2[index] }}
                  </td>
                </template>
              </tr>
            </thead>

            <tbody v-if="list.length">
              <tr v-for="(item, index) of list">
                <template>
                  <td v-if="rowsFunc(index, index2)" v-for="(item2, index2) of item" class="tac" :rowspan="rowsFunc(index, index2)">
                    <!-- rowsFunc(index, index2)-{{ rowsFunc(index, index2) }} -->
                    <span v-if="index2 < item.length - signImgNum"> {{ item2 }} </span>

                    <div v-else>
                      <span v-if="item2 + '' === 'null' || item2 == ''"></span>

                      <el-image
                        v-else
                        style="width: 60px; height: 30px"
                        :preview-src-list="[`data:image/jpg;base64,${item2}`]"
                        :z-index="9999"
                        :src="`data:image/jpg;base64,${item2}`"
                        alt=""
                      ></el-image>
                    </div>

                    <!-- <img v-else :src="`data:image/jpg;base64,${item2}`" style="width: 60px; height: 30px; display: inline-block" /> -->
                  </td>
                </template>

                <!-- <td v-else-if="item[0]" style="padding: 10px; line-height: 1.1" colspan="9999">{{ item[0] }}</td> -->
              </tr>
            </tbody>
          </table>

          <div v-if="list.length == 0" class="tac" style="border: 1px solid #ebeef5; border-top: none; padding: 20px">暂无数据</div>
        </el-col>

        <el-col :xl="8" :lg="24">
          <table v-if="tableHeadR.length" class="m-table" style="width: 100%; margin-top: 4px">
            <thead>
              <tr>
                <td colspan="9999" class="tac">{{ tableHeadR[0][0] }}</td>
              </tr>
              <tr v-for="index in lenMaxR">
                <template v-for="(item2, index2) of tableHeadR">
                  <td
                    class="tac"
                    v-if="(item2[index] && index2 != 0 && item2[index] != tableHeadR[index2 - 1][index]) || (item2[index] && index2 == 0)"
                    :colspan="colspanFunc(index, index2)"
                    :rowspan="!item2[index + 1] && index != lenMaxR - 1 ? '2' : '1'"
                  >
                    {{ item2[index] }}
                  </td>
                </template>
              </tr>
            </thead>

            <tbody v-if="listR.length">
              <tr v-for="(item, index) of listR">
                <template v-if="index < listR.length - 1">
                  <td v-if="rowsFuncR(index, index2)" v-for="(item2, index2) of item" class="tac" :rowspan="rowsFuncR(index, index2)">
                    <!-- rowsFunc(index, index2)-{{ rowsFuncR(index, index2) }} -->
                    <span v-if="index2 < item.length - 1"> {{ item2 }} </span>

                    <div v-else>
                      <span v-if="item2 + '' === 'null' || item2 == ''"></span>

                      <el-image
                        v-else
                        style="width: 60px; height: 30px"
                        :preview-src-list="[`data:image/jpg;base64,${item2}`]"
                        :z-index="9999"
                        :src="`data:image/jpg;base64,${item2}`"
                        alt=""
                      ></el-image>
                    </div>

                    <!-- <img v-else :src="`data:image/jpg;base64,${item2}`" style="width: 60px; height: 30px; display: inline-block" /> -->
                  </td>
                </template>

                <td v-else-if="item[0]" style="padding: 10px; line-height: 1.1" colspan="9999">{{ item[0] }}</td>
              </tr>
            </tbody>
          </table>

          <div v-if="listR.length == 0" class="tac" style="border: 1px solid #ebeef5; border-top: none; padding: 20px">暂无数据</div>
        </el-col>
      </el-row>
    </div>

    <!-- <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div> -->

    <selectBmDlg
      :dlgState0="dlgKeshiState"
      :dlgType="dlgKeshiType"
      :dlgQuery="dlgKeshiQuery"
      :dlgSelectData="dlgKeshiSelectData"
      :isRole="false"
      title="科室"
      @closeDlg="closeKeshiDlg"
      @backFunc="dlgKeshibackFunc"
    />
  </div>
</template>

<script>
import * as utils from '@/utils'
import { postAction, getAction } from '@/api'
import selectBmDlg from '@/components/Dialog2/selectBmDlg'
// import Pagination from '@/components/Pagination'
// import infoDlg from './infoDlg'

export default {
  components: {
    selectBmDlg,
  },
  data() {
    return {
      fileName: '',
      navList: [],

      userInfo: JSON.parse(window.localStorage.userInfo),

      // 弹窗
      dlgKeshiQuery: {},
      dlgKeshiState: false,
      dlgKeshiType: '', // 弹框状态add, edit
      dlgKeshiSelectData: { id: '', label: '' },

      /////
      tableHead: [],
      tableData: [],
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        // label: '',
        // page: 1,
        // limit: 20,

        departmentId: '',
        departmentName: '',
        dateRange: [],
        type: 1,

        pointEquipId: '',
      },

      tableHead0: [],
      tableHead: [],
      lenMax: 0,

      signImgNum: 0,

      // 表格2
      tableHeadR: [],
      listR: [],
      lenMaxR: 0,
      signImgNumR: 0,
    }
  },

  created() {
    // console.log('==', JSON.parse(window.localStorage.userInfo))
    let userInfo = JSON.parse(window.localStorage.userInfo)
    // console.log('===userInfo', userInfo)
    this.listQuery.departmentId = userInfo.departmentId
    this.listQuery.departmentName = userInfo.departmentName

    this.listQuery.dateRange = utils.returnToMonth()

    this.getTabList()
  },
  computed: {},
  watch: {},

  methods: {
    pointEquipIdChange(val) {
      if (!utils.isNull(val)) {
        let row = utils.arrId2Row(this.navList, val)
        this.signImgNum = parseInt(row.signType) + 1
      }
      this.searchFunc()
    },
    getTabList() {
      if (utils.isNull(this.listQuery.departmentName)) {
        this.$message.warning('请选择科室')
        return false
      }
      this.navList = []

      let sendObj = {
        departmentId: this.listQuery.departmentId,
        departmentName: this.listQuery.departmentName,
        page: 1,
        limit: 99999,
      }

      postAction('/cloth/dis-equipment/page', sendObj).then((res0) => {
        let res = res0.data
        if (res.code == 200) {
          if (!utils.isNull(res.data)) {
            let list = res.data
            for (let item of list) {
              item.id += ''
            }
            this.navList = list
          }
        }
        // window.open(res0.data.data)
      })
    },
    colspanFunc(rowIndex, colIndex) {
      let tStr = this.tableHead0[colIndex][rowIndex]

      let returnNum = 1
      for (let i = colIndex + 1; i < this.tableHead0.length; i++) {
        if (tStr == this.tableHead0[i][rowIndex]) {
          returnNum++
        } else {
          break
        }
      }
      return returnNum
    },
    // 表内容跨行
    rowsFunc(index, index2) {
      if (index2 == 0) {
        if (index != 0 && this.list[index][index2] == this.list[index - 1][index2]) {
          return 0
        } else {
          let num = 1
          let i = index
          for (let j = i + 1; j < this.list.length; j++) {
            if (this.list[i][0] == this.list[j][0]) {
              num++
            }
          }
          return num
        }
      } else {
        return 1
      }
    },

    rowsFuncR(index, index2) {
      if (index2 == 0) {
        if (index != 0 && this.listR[index][index2] == this.listR[index - 1][index2]) {
          return 0
        } else {
          let num = 1
          let i = index
          for (let j = i + 1; j < this.listR.length; j++) {
            if (this.listR[i][0] == this.listR[j][0]) {
              num++
            }
          }
          return num
        }
      } else {
        return 1
      }
    },

    /////
    showKeshiDlg() {
      if (this.listQuery.departmentName) {
        this.dlgKeshiSelectData = {
          id: this.listQuery.departmentId,
          label: this.listQuery.departmentName,
        }
      } else {
        this.dlgKeshiSelectData = { id: '', label: '' }
      }
      this.dlgKeshiState = true
    },
    closeKeshiDlg() {
      this.dlgKeshiState = false
    },
    dlgKeshibackFunc(data) {
      this.listQuery.departmentId = data.id
      this.listQuery.departmentName = data.label
      this.listQuery.pointEquipId = ''
      this.getTabList()

      this.tableHead0 = []
      this.list = []
      this.tableHeadR = []
      this.listR = []
    },

    printItem() {
      if (utils.isNull(this.listQuery.departmentName)) {
        this.$message.warning('请选择科室')
        return false
      }
      if (utils.isNull(this.listQuery.pointEquipId) || this.navList.length == 0) {
        this.$message.warning('请选择消毒设备')
        return false
      }
      if (utils.isNull(this.listQuery.dateRange) || this.listQuery.dateRange.length == 0) {
        this.$message.warning('请选择时间')
        return false
      }

      let xdtjcxPrintDate = {
        thead: this.tableHead0,
        theadLenMax: this.lenMax,
        list: this.list,
      }

      window.sessionStorage.xdtjcxPrintDate = JSON.stringify(xdtjcxPrintDate)

      let newWin = this.$router.resolve({ path: '/disinfectionStatisticsQueryPrint' })
      window.open(newWin.href, '_blank')
    },
    // 科室
    showBmTree() {
      this.$store.commit('SET_BMTREEISROLE', true)
      this.$store.commit('SET_BMTREESTATE', true)
    },

    downLoadFunc() {
      if (utils.isNull(this.listQuery.departmentName)) {
        this.$message.warning('请选择科室')
        return false
      }
      if (utils.isNull(this.listQuery.pointEquipId) || this.navList.length == 0) {
        this.$message.warning('请选择消毒设备')
        return false
      }
      if (utils.isNull(this.listQuery.dateRange) || this.listQuery.dateRange.length == 0) {
        this.$message.warning('请选择时间')
        return false
      }

      let sendObj = JSON.parse(JSON.stringify(this.listQuery))
      // sendObj.label = sendObj.label.trim()

      sendObj.beginDate = ''
      sendObj.endDate = ''
      if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
        sendObj.beginDate = sendObj.dateRange[0]
        sendObj.endDate = sendObj.dateRange[1]
      }
      delete sendObj.dateRange

      delete sendObj.departmentName

      let sendUrl = location.protocol + '//' + location.host + `/saapi/cloth/dis-record/reportExport` + utils.objToParam(sendObj)

      console.log('===sendUrl', sendUrl)
      window.open(sendUrl)
    },

    // // 获取小区列表
    // getAjax() {
    //   let sendObj = {
    //     page: 1,
    //     limit: 200,
    //   }
    //   postAction('/aaa/bbb', sendObj).then((res0) => {
    //     let res = res0.data
    //     if (res.code == 200) {
    //       this.communityList = res.data.data
    //     } else {
    //       this.$message.warning(res.msg)
    //     }
    //   })
    // },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // << 列表
    searchFunc() {
      // this.listQuery.page = 1
      this.getList()
    },

    // 获取数据
    getList() {
      if (utils.isNull(this.listQuery.departmentName)) {
        this.$message.warning('请选择科室')
        return false
      }
      if (utils.isNull(this.listQuery.pointEquipId) || this.navList.length == 0) {
        this.$message.warning('请选择消毒设备')
        return false
      }
      if (utils.isNull(this.listQuery.dateRange) || this.listQuery.dateRange.length == 0) {
        this.$message.warning('请选择时间')
        return false
      }

      this.list = []
      let sendObj = JSON.parse(JSON.stringify(this.listQuery))
      // sendObj.label = sendObj.label.trim()

      sendObj.beginDate = ''
      sendObj.endDate = ''
      if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
        sendObj.beginDate = sendObj.dateRange[0]
        sendObj.endDate = sendObj.dateRange[1]
      }
      delete sendObj.dateRange

      this.listLoading = true
      postAction('/cloth/dis-record/report', sendObj).then((res0) => {
        this.listLoading = false
        let res = res0.data
        if (res.code == 200) {
          this.fileName = res.data.fileName
          // == 表1
          this.list = JSON.parse(JSON.stringify(res.data.dataList))
          // 表头
          this.tableHead0 = res.data.header
          let lenMax = 0
          for (let item of this.tableHead0) {
            if (item.length > lenMax) {
              lenMax = item.length
            }
          }
          this.lenMax = lenMax

          // == 表2
          let listR0 = JSON.parse(JSON.stringify(res.data.equipLogs))
          let listR = []
          for (let i = 0; i < listR0.length - 1; i++) {
            let item = listR0[i]
            let arr = [item.date, item.equipName, item.firstSign]
            listR.push(arr)
          }
          listR.push([listR0[listR0.length - 1].equipName])
          this.listR = listR
          // 表头
          this.tableHeadR = [
            [this.fileName + '消毒记录', '日期'],
            [this.fileName + '消毒记录', this.fileName],
            [this.fileName + '消毒记录', '签名'],
          ]
          let lenMaxR = 0
          for (let item of this.tableHeadR) {
            if (item.length > lenMaxR) {
              lenMaxR = item.length
            }
          }
          this.lenMaxR = lenMaxR
        } else {
          this.list = []
          this.total = 0
          this.listR = []
          this.$message.error(res.msg)
        }
      })
    },

    // >> 列表
    // 删除
    delItem(data, flag) {
      let title = '确认删除?'

      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        getAction(`/aaa/bbb/${data.id}`).then((res0) => {
          let res = res0.data
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss">
.el-image-viewer__img {
  background: #ffffff !important;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}

.m-table {
  // background:#666;
  border-top: 1px solid #ebeef5;
  border-left: 1px solid #ebeef5;
  border-spacing: 0px;
  font-size: 12px;
  td {
    background: #fff;
    min-height: 30px;
    box-sizing: border-box;
    padding: 1px;
    border-right: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    padding: 6px 10px;
  }
  thead td {
    font-size: 14px;
    font-weight: 700;
    color: #19aa8d;
  }
  .mh {
    min-height: 36px;
  }
  .m-input .el-input__inner {
    border: none;
    border-radius: 0;
    border-bottom: 1px solid #ebeef5;
    padding: 0;
    height: 18px;
    line-height: 18px;
  }
}
.tac {
  text-align: center;
}
</style>


