<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input
              v-model="searchForm.name"
              placeholder="请输入菜品名称"
              clearable
              class="fl"
              style="width: 160px"
              @change="searchFunc"
            ></el-input>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
            <el-button
              type="success"
              @click="showDlg('add')"
              icon="el-icon-plus"
              class="fl ml10"
              >添加</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc(100vh - 290px)"
      ref="tableBar"
      class="m-small-table"
      :loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equTypeStr"
        label="菜品图片"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <el-image
            style="width: 30px; height: 30px"
            :src="scope.row.imgUrl[0]"
            :preview-src-list="scope.row.imgUrl"
          ></el-image>
        </template>
      </el-table-column>
      <!-- 其他列省略... -->
      <el-table-column
        prop="name"
        label="菜品名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="model"
        label="规格"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="unit"
        label="单位"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建日期"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="creatorName"
        label="创建人"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" width="280" align="center">
        <template slot-scope="scope">
          <el-button
            @click="showDlg('info', scope.row)"
            icon="el-icon-document"
            size="mini"
            type="success"
            title="详情"
            plain
            >详情</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="showDlg('edit', scope.row)"
            plain
            icon="el-icon-edit"
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="delFunc(scope.row)"
            plain
            icon="el-icon-delete"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getList"
    />
    <div class="clear"></div>

    <AddEdit ref="addEdit" :dlgType="dlgType" />
  </div>
</template>

<script>
import AddEdit from "./addEdit";
import { getAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination";

export default {
  components: {
    AddEdit,
    Pagination,
  },
  data() {
    return {
      searchForm: {
        name: "",
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      total: 0,
      listLoading: false,
      dlgType: "add",
    };
  },
  mounted() {
    this.searchFunc();
  },
  methods: {
    showDlg(type, row) {
      this.$refs.addEdit.init(type, row);
    },
    searchFunc() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    getList() {
      let postData = { ...this.searchForm };
      this.listLoading = true;

      getAction(`/canteen/cn/dishes/page`, postData).then((res) => {
        let { code, data } = res.data;
        this.listLoading = false;
        if (code === "200") {
          data.list.forEach((item) => {
            if (item.imgUrl) {
              item.imgUrl = JSON.parse(item.imgUrl);
            }
          });
          this.tableData = data.list || [];
          this.total = data.total || 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAction(`/canteen/cn/dishes/delete?id=${row.id}`).then((res) => {
          if (res.data.code === "200") {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.searchFunc();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
// 样式部分根据需要添加
</style>