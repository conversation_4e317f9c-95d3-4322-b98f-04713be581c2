<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' 
      title="选择岗位" 
      
      :visible.sync="gwsState" 
      width='900px' 
      top='30px'
      icon-class='el-icon-info'
      append-to-body>
      <div class="dialog-bms-bar">
        <!-- 部门 -->
        <div class='dialog-bms-left'>
          <el-input
            placeholder="输入关键字进行过滤"
            style="margin-bottom: 10px;"
            v-model="bmsText">
          </el-input>
          <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
          <div class="bms-tree">
            <el-tree 
              :data="bmsData" 
              ref="bmsDom"
              default-expand-all
              :filter-node-method="bmsFilter"
              @node-click="bmsClick">
            </el-tree>
          </div>
        </div>

        <!-- 中间 -->
        <div class='dialog-bm-right'>
          <el-input
            placeholder="输入关键字进行过滤"
            style="margin-bottom: 10px;"
            @input='filterGangwei($event)'
            v-model="filterBmrightText">
          </el-input>
          <div class='dialog-bm-right-loading' v-show='filterBmrightLoading'>
            <i class='el-icon-loading'></i>
          </div>
          <div class='dialog-bm-right-ul' v-show='!filterBmrightLoading'>
            <p v-if='!filterBmrightList'>暂无数据</p>
            <a href="javascript:void(0)" v-for='(item, index) of filterBmrightList' :key='index' @click='selectGangWei(item)'><i class='el-icon-news'></i>{{ item.label }}</a>
          </div>
        </div>

        <div class='dialog-bms-right'>
          <!-- <el-button v-for='(item, index) of gwsArr2' :key='index' class='bms-a' type='success' size='mini' plain>{{ item.relationName }}<i class="el-icon-close" @click='delGwFunc(item.relationId, item.relationName)'></i></el-button> -->
          <el-button v-for='(item, index) of gwsArr2' :key='index' class='bms-a' type='success' size='mini' plain>{{ item.relationName }}</el-button>

        </div>
        <!-- 部门多选框 -->
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="primary" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findOrgBranchAll } from '@/api/dataDic'
import { findOrgBranchPost } from '@/api/postMan'

export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      // 部门树
      bmsData: [],
      bmsSelect: {},
      bmsText: '',  // 部门左侧筛选

      // 中间岗位
      filterBmrightLoading: false, // 右侧loading
      filterBmrightText: '',  // 部门右侧筛选    
      filterBmrightList: [  // 部门右侧 列表
      ], 
      filterBmrightListOld: [],

      // 右侧选中的样式
      bmsIds: [],
      bmsNames: [],
      gwsArr2: []

    }
  },
  computed: {
    ...mapGetters([
      'gwsArr'
    ]),
    
    gwsState: {
      get: function() {
        let state = this.$store.getters.gwsState

        if (state === false) {
          this.filterBmrightText = ''
          this.filterBmrightList = []
          this.filterBmrightListOld = []
          this.gwsArr2 = []
        }

        return state
      },
      set: function(newVal) {
        this.$store.commit('SET_GWSSTATE', newVal)
      }
    }
    // bmsArr: {
    //   get: function() {
    //     let bmsArr = this.$store.getters.bmsArr
        
    //     return bmsArr
    //   },
    //   set: function(newVal) {
    //   }
    // },

  },
  watch: {
    bmsText(val) {
      this.$refs.bmsDom.filter(val);
    },
    gwsArr(val) {
      this.gwsArr2 = JSON.parse(JSON.stringify(val))
    }
  },
  created() {
    this.findOrgBranchAll()
  },
  methods: {
    // 【【 左侧相关
    // 获取部门
    findOrgBranchAll() {
      findOrgBranchAll().then(res => {
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code === '200') {
          this.bmsData = JSON.parse(JSON.stringify(res.data.list))
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 节点左键点击事件
    bmsClick(data, node, mNode) {
      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      // this.filterText = data.label
      this.selectNode = data
      // 获取部门 下 岗位
      this.getBmGangwei(data.id)
    },

    // 选择部门后，获取部门岗位
    getBmGangwei(brId) {
      this.postName = ''
      this.filterBmrightLoading = true
      let sendObj = {
        brId,
        page: 1,
        size: 5000
      }
      findOrgBranchPost(sendObj).then(res => {
        this.filterBmrightLoading = false
        this.filterBmrightList = res.data.list
        this.filterBmrightListOld = res.data.list
      })
    },

    // 节点右键点击事件
    // addBms(event, data, node, mNode ) {
    //   let relationId = data.id
    //   let relationName = data.label
    //   let isHas = this.gwsArr2.every(item => {
    //     return item.relationId !== relationId
    //   })

    //   if (isHas) {
    //     this.gwsArr2.push({relationId, relationName})
    //   } else {
    //     this.$message({
    //       type: 'warning',
    //       message: '所选部门已在列表中，请勿重复添加。'
    //     })
    //   }
      
    // },
    // 筛选部门
    bmsFilter(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 】】 左侧相关

    // 【【 中间相关
    selectGangWei(data) {
      let relationId = data.id
      let relationName = data.label
      let isHas = this.gwsArr2.every(item => {
        return item.relationId !== relationId
      })

      if (isHas) {
        this.gwsArr2.push({relationId, relationName})
      } else {
        this.$message({
          type: 'warning',
          message: '所选岗位已在列表中，请勿重复添加。'
        })
      }

    },
    // 筛选岗位
    filterGangwei(val) {
      this.filterBmrightList = JSON.parse(JSON.stringify(this.filterBmrightListOld))
      this.filterBmrightList = this.filterBmrightList.filter(item => {
        return item.label.indexOf(val) >= 0
      })
    },
    // 】】 中间相关

    // 【【 右侧相关
    // 右侧删除部门方法
    delGwFunc(relationId, relationName) {
      let nArr = this.gwsArr2.filter(item => {
        return item.relationId !== relationId
      })
      console.log(nArr)
      this.gwsArr2 = JSON.parse(JSON.stringify(nArr))
    },
    // 】】 右侧相关

    // 【【 其他
    // 选择部门提交
    bumenOkFunc() {
      this.$store.commit('SET_GWSARR', this.gwsArr2)
      this.closeDialog()
    },
    // 关闭弹窗 
    closeDialog() {
      this.$store.commit('SET_GWSSTATE', false)
    }
    // 】】 其他
    

    // 筛选岗位
    // filterBmrightListOld
    // filterGangwei(val) {
    //   this.filterBmrightList = JSON.parse(JSON.stringify(this.filterBmrightListOld))
    //   this.filterBmrightList = this.filterBmrightList.filter(item => {
    //     return item.label.indexOf(val) >= 0
    //   })
    // },

    
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dialog-bms-bar {
  display: flex;
  justify-content: space-between;
}
.dialog-bms-left,.dialog-bms-right {
  width: 33%;
}
.bms-tree {
  height: 400px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
}
.dialog-bms-right {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 10px;
  .bms-a {
    margin-bottom: 10px;
    margin-left: 0px;
    margin-right: 6px;
  }
  i {
    display: inline-block;
    margin-left: 3px;
    
  }
}

.dialog-bm-right-ul {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 10px;
  height: 400px;
  p {
    text-align: center;
    margin-top: 6px;
  }
}

// 
.dialog-bm-right {
  width: 30%;
}
.dialog-bm-right-loading {
  padding-top: 158px;
  text-align: center;
  font-size: 60px;
  height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
}
.dialog-bm-right-ul {
  a {
    display: block;
    line-height: 26px;
    margin-left: 10px;
    color: #666;
    i {
      display: inline-block;
      margin-right: 8px;
      line-height: 26px;
      color: #19AA8D;
    }
  }
}
</style>