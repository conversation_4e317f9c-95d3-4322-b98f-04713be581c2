<template>
  <div class="">
    <!-- 多选报事科目 -->
    <el-dialog title="设置权限" :close-on-click-modal="false" :visible.sync="modelTreeState" width="600px" top="30px" append-to-body>
      <div class="">
        <div class="fwarning">
          <i class="el-icon-info fl" style="font-size: 20px"></i>
          <div class="fl" style="margin-left: 4px; margin-top: 2px">请选择本项目需要的科目</div>
          <div class="clear"></div>
        </div>

        <!-- @check='treeCheck' -->
        <!-- highlight-current -->
        <div class="m-dialog-h" style="margin-top: 10px">
          <el-tree
            :expand-on-click-node="false"
            :data="treeData"
            show-checkbox
            default-expand-all
            ref="tree"
            node-key="id"
            :default-checked-keys="defaultSelectKey"
            :props="defaultProps"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span :title="data.name">{{ data.name }}</span>
            </span>
          </el-tree>
          <!-- <el-tree 
            :data="treeData" 
            ref="treeDom"
            default-expand-all
            :filter-node-method="filterNode"
            @node-click="nodeClick">
          </el-tree> -->
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <!-- <span class='dialog-footer-span' v-show='branchName'>当前选中：{{ branchName }}</span> -->
        <el-button @click="closeDialog" icon="el-icon-back">返回</el-button>

        <el-button type="danger" @click="removeNode" icon="el-icon-delete">清空</el-button>
        <el-button :loading="btnLoading" type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import { subjectTemplateFindSubjectTree, subjectSaveList } from '@/api/reportMan'

export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      query: {},
      // 部门树
      treeData: [],
      selectKeys: [], // 选中的节点id集合
      selectNames: [], // 选中的节点 Name
      checkChildIds: [], // 选中当前节点下的 id 集合
      childNode: {}, // 选中的当前节点

      btnLoading: false,
      isFirst: false,

      // 树过滤
      defaultProps: {
        children: 'children',
        label: 'label',
      },

      // 返回的 id + name
      backIdName: [],

      // 默认数据
      defaultSelectKey: [],

      roleSelect: [], // 角色 列表
      roleId: '', // 选中的角色ID
    }
  },
  computed: {
    ...mapGetters([
      // 'modelTreeState',
      'modelTreeKeySet',
      'modelTreeNameSet',
    ]),
    modelTreeState: {
      get: function () {
        return this.$store.getters.modelTreeState
      },
      set: function (newVal) {
        this.$store.commit('SET_MODELTREE_STATE', newVal)
      },
    },
  },
  watch: {
    modelTreeState(val) {
      if (val) {
        setTimeout(() => {
          this.query = JSON.parse(this.$store.getters.modelTreeQuery)
          this.$refs.tree.setCheckedKeys([])
        }, 20)

        subjectTemplateFindSubjectTree().then((res1) => {
          let res = res1.data
          if (res.code === '200') {
            this.treeData = JSON.parse(JSON.stringify(res.data))

            if (this.$store.getters.modelTreeKeySet) {
              setTimeout(() => {
                this.selectKeys = this.$store.getters.modelTreeKeySet.split(',')
                this.defaultSelectKey = this.$store.getters.modelTreeKeySet.split(',')

                // 设置 name
                let names = this.$store.getters.modelTreeNameSet.split(',')
                let nameArr = []
                for (let i = 0; i < this.selectKeys.length; i++) {
                  let obj = {
                    id: this.selectKeys[i],
                    label: names[i],
                  }
                  nameArr.push(obj)
                }
                this.selectNames = nameArr

                this.$refs.tree.setCheckedKeys(this.selectKeys)
              }, 50)
            }
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
  },
  created() {},
  methods: {
    // 【【 节点相关
    // 清空选中节点
    removeNode() {
      this.roleId = ''
      this.$refs.tree.setCheckedKeys([])
    },
    // 节点选中触发事件
    treeCheck(checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys) {
      // console.log('checkedNodes', checkedNodes)
      // console.log('checkedKeys', checkedKeys)
      // console.log('halfCheckedNodes', halfCheckedNodes)
      // console.log('halfCheckedKeys', halfCheckedKeys)
      // checkedNodes 当前选中的节点数据
      // checkedKeys.checkedKeys 选中的 id 数组
      // console.log(11, checkedKeys.checkedKeys.length)
      // console.log(22,this.selectKeys.length)
      // if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
      //   this.selectKeys = checkedKeys.checkedKeys
      //   this.selectNames = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
      //   // select-全选；remove-取消全选
      //   this.selectAllNode(checkedNodes.children, 'select')
      // } else {
      //   this.selectKeys = checkedKeys.checkedKeys
      //   this.selectNames = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
      //   this.selectAllNode(checkedNodes.children, 'remove')
      // }
      // setTimeout(() => {
      //   console.log('asdfasdf')
      //   console.log(this.selectKeys)
      //   this.$refs.tree.setCheckedKeys(this.selectKeys);
      // }, 50)
    },

    // selectAllNode(childrenArr, type) {

    //   for (let item of childrenArr) {
    //     // 全选，全部取消
    //     if (type == 'select') {
    //       if (!this.selectKeys.includes(item.id)) {
    //         this.selectKeys.push(item.id)
    //         this.selectNames.push(item)
    //       }
    //     } else {
    //       if (this.selectKeys.includes(item.id)) {
    //         let mIndex = this.selectKeys.indexOf(item.id)

    //         this.selectKeys.splice(mIndex, 1)
    //         this.selectNames.splice(mIndex, 1)
    //       }
    //     }
    //     if (item.children) {
    //       this.selectAllNode(item.children, type)
    //     }
    //   }
    // },

    // 【【 弹窗按钮
    // 提交
    bumenOkFunc() {
      console.log('全选', this.$refs.tree.getCheckedNodes())
      console.log('半选', this.$refs.tree.getHalfCheckedNodes())

      let arr = this.$refs.tree.getCheckedNodes()
      let halfArr = this.$refs.tree.getHalfCheckedNodes()

      let projectId = this.query.projectId

      let sendObj = []
      for (let item of arr) {
        let obj = {
          subjectId: item.id,
          type: item.type, // 类型：0:区域分类 1:点位
          name: item.name, // 科目名称
          superSubjectId: item.superSubjectId, // 上级id
          superSubjectName: item.superSubjectName, // 上级名称
          projectId, //项目id
          chooseType: '1', // 0-半选 1-全选
        }
        sendObj.push(obj)
      }
      for (let item of halfArr) {
        let obj = {
          subjectId: item.id,
          type: item.type, // 类型：0:区域分类 1:点位
          name: item.name, // 科目名称
          superSubjectId: item.superSubjectId, // 上级id
          superSubjectName: item.superSubjectName, // 上级名称
          projectId, //项目id
          chooseType: '0', // 0-半选 1-全选
        }
        sendObj.push(obj)
      }
      console.log('sendObj', sendObj)
      // return false

      subjectSaveList(sendObj).then((res1) => {
        let res = res1.data
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.msg,
          })
          this.$store.commit('SET_MODELTREE_GET', 'reload')
          this.closeDialog()
          setTimeout(() => {
            this.$store.commit('SET_MODELTREE_GET', 'empty')
            this.$store.commit('SET_MODELTREE_KEY_SET', '')
          }, 100)
        } else {
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })

      // let names = []
      // for (let item of this.selectNames) {
      //   names.push(item.label)
      // }
      // this.$store.commit('SET_MODELTREE_KEY_SET', this.selectKeys.join(','))
      // this.$store.commit('SET_MODELTREE_NAME_SET', names.join(','))
      // this.$store.commit('SET_MODELTREE_HALFKEY_SET', JSON.stringify(this.$refs.tree.getHalfCheckedKeys()))

      // this.closeDialog()

      // setTimeout(() => {
      //   this.$store.commit('SET_MODELTREE_KEY_SET', 'empty')
      //   this.$store.commit('SET_MODELTREE_NAME_SET', 'empty')
      //   this.$store.commit('SET_MODELTREE_HALFKEY_SET', 'empty')
      // }, 100)
    },

    // 关闭弹窗
    closeDialog() {
      this.isFirst = true
      this.$store.commit('SET_MODELTREE_STATE', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss">
</style>