/** 场地分支 **/
import Layout from "@/views/layout/Layout";
// component: () => import("@/views/hrMan/orgPlan/index"),
const siteRouter = {
  path: "/siteMan",
  component: Layout,
  redirect: "/examMan/course/list",
  name: "siteMan",
  meta: {
    title: "场地管理",
    icon: "siteMan",
    roles: ["ven_changdiguanli"],
  },

  children: [
    {
      path: "basicInforMan",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "basicInforMan",
      meta: {
        title: "基础信息管理",
        roles: ["ven_jichuxinxiguanli"],
      },
      children: [
        {
          path: "venueMan",
          component: () =>
            import("@/views/siteMan/basicInforMan/venueMan/index"),
          name: "venueMan",
          meta: {
            title: "场馆管理",
            roles: ["ven_changguanguanli"],
          },
          children: [],
        },

        {
          path: "venueDeviceMan",
          component: () =>
            import("@/views/siteMan/basicInforMan/venueDeviceMan/index"),
          name: "venueDeviceMan",
          meta: {
            title: "场馆设施管理",
            roles: ["ven_changguansheshiguanli"],
          },
          children: [],
        },
        {
          path: "blacklistMan",
          component: () =>
            import("@/views/siteMan/basicInforMan/blacklistMan/index"),
          name: "blacklistMan",
          meta: {
            title: "黑名单管理",
            roles: ["ven_heimingdanguanli"],
          },
          children: [],
        },

        {
          path: "venueConfig",
          component: () =>
            import("@/views/siteMan/basicInforMan/venueConfig/index"),
          name: "venueConfig",
          meta: {
            title: "场馆配置",
            roles: ["ven_changguanpeizhi"],
          },
          children: [],
        },
      ],
    },

    {
      path: "statisticsQuery",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "statisticsQuery",
      meta: {
        title: "统计查询",
        roles: ["ven_tongjichaxun"],
      },
      children: [
        {
          path: "libraryReservation",
          component: () =>
            import("@/views/siteMan/statisticsQuery/libraryReservation/index"),
          name: "libraryReservation",
          meta: {
            title: "图书馆预约统计",
            roles: ["ven_tushuguanyuyuetongji"],
          },
          children: [],
        },
        {
          path: "VenueReservation",
          component: () =>
            import("@/views/siteMan/statisticsQuery/VenueReservation/index"),
          name: "VenueReservation",
          meta: {
            title: "场馆预约统计",
            roles: ["ven_changguanyuyuetongji"],
          },
          children: [],
        },
        {
          path: "CleaningReservation",
          component: () =>
            import("@/views/siteMan/statisticsQuery/CleaningReservation/index"),
          name: "CleaningReservation",
          meta: {
            title: "清洁预约统计",
            roles: ["ven_qingjieyuyuetongji"],
          },
          children: [],
        },
      ],
    },
    //
  ],
};

export default siteRouter;
