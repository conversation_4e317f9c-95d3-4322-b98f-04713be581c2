//  作业流程模板dlg组件

const workTmplDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    workType: '',

    tmplId: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    workType: state => state.workType,

    tmplId: state => state.tmplId,

  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_WORKTYPE: (state, val) => {
      state.workType = val
    },

    SET_TMPLID: (state, val) => {
      state.tmplId = val
    },
  },

  actions: {

  }
}

export default workTmplDlg
