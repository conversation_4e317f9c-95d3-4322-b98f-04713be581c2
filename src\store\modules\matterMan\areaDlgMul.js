// 多选区域dlg组件

const areaDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    areaIds: "",

    areaNames: "",

    projectId: ""
  },

  getters: {
    dlgShow: state => state.dlgShow,

    areaIds: state => state.areaIds,

    areaNames: state => state.areaNames,

    projectId: state => state.projectId
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_AREAIDS: (state, val) => {
      state.areaIds = val;
    },

    SET_AREANAMES: (state, val) => {
      state.areaNames = val;
    },

    SET_PROJECTID: (state, val) => {
      state.projectId = val;
    }
  },

  actions: {}
};

export default areaDlgMul;
