import request from '@/utils/request'

/*
 * 被服档案
 */

// 新增/修改被服信息接口
export function saveOrUArchives(data) {
  return request({
    url: `/cloth/saveOrUArchives`,
    method: 'post',
    data
  })
}

// 修改被服信息状态（删除被服信息）
export function updateArchives(data) {
  return request({
    url: `/cloth/updateArchives`,
    method: 'post',
    data
  })
}

// 动态查询被服信息 分页
export function findArchivesDynamic(data) {
  return request({
    url: `/cloth/findArchivesDynamic`,
    method: 'post',
    data
  })
}
