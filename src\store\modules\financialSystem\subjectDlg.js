// 科目dlg组件

const subjectDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    subjectId: '',

    subjectName: '',

    subjectType: '',

    dlgType: ''
  },

  getters: {
    dlgShow: state => state.dlgShow,

    subjectId: state => state.subjectId,

    subjectName: state => state.subjectName,

    subjectType: state => state.subjectType,

    dlgType: state => state.dlgType
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_SUBJECTID: (state, val) => {
      state.subjectId = val
    },

    SET_SUBJECTNAME: (state, val) => {
      state.subjectName = val
    },

    SET_SUBJECTTYPE: (state, val) => {
      state.subjectType = val
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val
    },
  },

  actions: {

  }
}

export default subjectDlg
