<template>
  <!-- 考勤管理 - 工时调整 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="月份">
          <el-date-picker :picker-options="pickerOptions" v-model="listQuery.date" value-format="yyyy-MM" format="yyyy-MM" type="month" placeholder="月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入员工姓名' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="listQuery.state" placeholder="请选择">
            <el-option v-for="item in stateList" :key="item.id" :label="item.label" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="本岗位">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="200px" align="center">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.isUpdateHour == 0"> 无调整 </el-tag>
            <el-tag type="danger" v-if="scope.row.isUpdateHour == 1"> 有调整 </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="加项" align="center">
          <template slot-scope="scope">
            <span :class="scope.row.addWorkHours > 0 ? 'fsuccess' : ''">{{ scope.row.addWorkHours }}</span>
          </template>
        </el-table-column>

        <el-table-column label="减项" align="center">
          <template slot-scope="scope">
            <span :class="scope.row.reduceWorkHours > 0 ? 'fdanger' : ''">{{ scope.row.reduceWorkHours }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="140" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">调整</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="'工时调整卡'" :visible.sync="dlgShow" width='600px' top="30px" append-to-body>

      <el-form ref="dlgForm" :model="dlgData" label-width="120px">
        <el-form-item label="姓名">
          {{dlgData.userName}}
        </el-form-item>
        <el-form-item label="所属部门">
          {{dlgData.branchName}}
        </el-form-item>
        <el-form-item label="月份">
          {{listQuery.date}}
        </el-form-item>
        <el-form-item label="标准工时">
          {{dlgData.standardWorkHour}}
        </el-form-item>
        <el-form-item label="当前实际工时">
          {{dlgData.actualWorkHour}}
        </el-form-item>
        <el-form-item label="调整后工时">
          {{dlgData.adjustWorkHour}}
        </el-form-item>
        <el-form-item label="工时加项">
          <el-input-number v-model="dlgData.addWorkHours" @change="adjustHour" :controls='false' :min="0" :precision="0" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item label="工时加项备注">
          <el-input type="textarea" v-model="dlgData.addRemark" :autosize="{ minRows: 2, maxRows: 4}"></el-input>
        </el-form-item>
        <el-form-item label="工时减项">
          <el-input-number v-model="dlgData.reduceWorkHours" @change="adjustHour" :controls='false' :min="0" :precision="0" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item label="工时减项备注">
          <el-input type="textarea" v-model="dlgData.reduceRemark" :autosize="{ minRows: 2, maxRows: 4}"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <template v-if="dlgType == 'EDIT'">
          <el-button type='success' :loading='dlgLoading' @click="subDlg()" icon="el-icon-check">
            提交
          </el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findWorkHourPage, updateWorkHours } from '@/api/attendanceMan/abnormalClock.js'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
let maxMonth = utils.getDiffMonth("", -1)
export default {
  components: {
    Pagination
  },
  data () {
    return {
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() > new Date(maxMonth);
        },
      },
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',  // ADD\EDIT

      // 弹窗数据
      dlgData: {},
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        state: '0,1',
        date: utils.getToday().substr(0, 7),
      },
      stateList: [
        {
          id: '0,1',
          label: '全部'
        },
        {
          id: '0',
          label: '无调整'
        },
        {
          id: '1',
          label: '有调整'
        }
      ],
    }
  },
  computed: {

  },
  watch: {

  },

  created () {
    this.listQuery.date = maxMonth
  },

  methods: {
    // 调整工时
    adjustHour () {
      this.dlgData.adjustWorkHour = this.dlgData.actualWorkHour + this.dlgData.addWorkHours - this.dlgData.reduceWorkHours
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      findWorkHourPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 弹窗提交
    subDlg () {
      this.dlgLoading = true
      let postParam = {
        id: this.dlgData.id,
        addWorkHours: this.dlgData.addWorkHours,
        addRemark: this.dlgData.addRemark,
        reduceWorkHours: this.dlgData.reduceWorkHours,
        reduceRemark: this.dlgData.reduceRemark
      }
      updateWorkHours(postParam).then(res => {
        this.dlgLoading = false
        if (res.data.code == 200) {
          this.$message.success(res.data.msg)
          this.dlgShow = false
          this.getList()
        } else {
          this.$message.error(res.data.msg)
        }
      })

    },

    // 编辑
    editItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
      this.adjustHour()
    },
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-divider {
  margin-top: 50px;
}

.image-wrapper {
  /deep/ .el-image {
    width: 150px;
    height: 150px;
  }
}

.image-wrapper.absolute {
  position: absolute;
  z-index: 1;
  right: 0;
  top: 0;
}
</style>


