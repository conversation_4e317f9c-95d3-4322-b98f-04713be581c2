<template>
  <!-- 活动奖品 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <!-- <el-form-item label="关键字:">
          <el-input @keyup.enter.native="getList" placeholder="姓名/电话/身份证号" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item> -->

        <el-form-item label="选择小区:">
          <el-select v-model="listQuery.communityId" @change="getHdList" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="活动状态:">
          <el-radio-group v-model="listQuery.activityStatus" @change="getHdList">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="0">进行中</el-radio-button>
            <el-radio-button label="1">已结束</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="活动名称:">
          <el-select v-model="listQuery.activityId" filterable clearable placeholder="请选择活动">
            <el-option v-for="item in hdList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="领取状态:">
          <el-radio-group v-model="listQuery.status" @change="getHdList">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="3">未领取</el-radio-button>
            <el-radio-button label="4">已领取</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="关键字:">
          <el-input @keyup.enter.native="getList" placeholder="姓名/手机号" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>

        <el-button icon="el-icon-search" type="success" size="mini" @click="searchFunc">搜索</el-button>
        <el-button class="" @click="downLoadFunc" icon="el-icon-download" size="mini">导出</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="#" align="center" width="60">
          <template slot-scope="scope">
            {{ (listQuery.page - 1) * listQuery.limit + scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column label="业主姓名" prop="name" width="80" align="center"></el-table-column>
        <el-table-column label="业主电话" prop="phone" width="140" align="center"></el-table-column>
        <el-table-column label="身份证" prop="idCard" width="150" align="center"></el-table-column>

        <el-table-column label="楼栋" prop="floorName" width="80" align="center"></el-table-column>
        <el-table-column label="单元" prop="unitName" width="80" align="center"></el-table-column>
        <el-table-column label="房号" prop="roomName" width="140" align="center"></el-table-column>

        <el-table-column label="领取商品" prop="goodsName"></el-table-column>
        <el-table-column label="领取时间" prop="receiveTime" width="140" align="center"></el-table-column>

        <el-table-column label="状态" width="100px" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '3'" type="warning">未领取</el-tag>
            <el-tag v-else type="success">已领取</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="140" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status == '3'"
              type="primary"
              size="mini"
              icon="el-icon-edit"
              plain
              @click="tableLqFunc(scope.row)"
              >领取确认</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'

import * as utils from '@/utils'
import { postAction, getAction } from '@/api'
import { uploadImg } from '@/utils/uploadImg'
import QRCode from 'qrcode' //引入生成二维码插件

import Pagination from '@/components/Pagination'

export default {
  components: {
    Pagination,
  },
  data() {
    return {
      userInfo: '',
      // 弹窗数据
      dlgQuery: {},
      dlgState: false,
      dlgType: '', // 弹框状态add, edit
      dlgData: {},
      // 详情弹窗
      dlgInfoQuery: {},
      dlgInfoState: false,
      dlgInfoType: '', // 弹框状态add, edit
      dlgInfoData: {},

      /////

      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        label: '', //	模糊查找	body	false	string
        page: 1,
        limit: 20,
        activityId: '',
        communityId: '',

        activityStatus: '', // 0:进行中 1:已结束
        status: '', //	3:已参加活动未领取商品 4 已参加活动已领取商品
      },

      // 下拉框
      communityList: [], // 小区
      hdList: [],
    }
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.getCommunityList()
    this.getHdList()

    this.searchFunc()
  },

  methods: {
    downLoadFunc() {
      if (!this.listQuery.communityId) {
        this.$message.warning('请选择小区')
        return false
      }
      let sendObj = {
        label: this.listQuery.label, //	模糊查找	body	false	string
        activityId: this.listQuery.activityId, //	活动id	body	false	string
        communityId: this.listQuery.communityId, //	小区id	body	false	string
        status: this.listQuery.activityStatus, //	3:已参加活动未领取商品 4 已参加活动已领取商品
      }
      postAction('/unity/activity/activityOwnerUserExport', sendObj).then((res0) => {
        window.open(res0.data.data.url)
      })
    },

    tableLqFunc(data) {
      let title = '确认领取?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        getAction(`/unity/activity/receiveGoods/${data.id}`).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    //////
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
        projectId: this.userInfo.projectId,
      }
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取活动数据
    getHdList() {
      if (!this.listQuery.communityId) {
        return false
      }

      let sendObj = {
        communityId: this.listQuery.communityId,
        page: 1,
        limit: 99999,
        status: this.listQuery.activityStatus,
        label: '',
        startDate: '',
        endDate: '',
      }

      sendObj.projectId = this.userInfo.projectId

      postAction('/unity/activity/page', sendObj).then((res0) => {
        this.listLoading = false
        let res = res0.data
        if (res.code == 200) {
          this.listQuery.activityId = ''
          this.hdList = JSON.parse(JSON.stringify(res.data))
        } else {
          this.hdList = []
        }
      })
    },

    // << 列表
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    // 获取数据
    getList() {
      if (!this.listQuery.communityId) {
        this.$message.warning('请选择小区')
        return false
      }

      let sendObj = JSON.parse(JSON.stringify(this.listQuery))

      sendObj.projectId = this.userInfo.projectId

      this.listLoading = true
      postAction('/unity/activity/activityOwnerUserPage', sendObj).then((res0) => {
        this.listLoading = false
        let res = res0.data
        if (res.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data))
          let newTime = new Date().getTime()
          for (let item of list) {
            let startDateTime = new Date(item.startDate).getTime()
            if (newTime < startDateTime) {
              item.isStart = false
            } else {
              item.isStart = true
            }
          }

          this.list = JSON.parse(JSON.stringify(list))
          this.total = res.page.total ? res.page.total : 0
          // this.formatList()
        } else {
          this.list = []
          this.total = 0
          this.$message.warning(res.msg)
        }
      })
    },
    // >> 列表
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>


