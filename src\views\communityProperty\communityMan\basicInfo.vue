<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入小区名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>新增小区</el-button>
        <el-button icon="el-icon-download" size="mini" type="primary" @click="exportExcel">
          Excel导出
        </el-button>
        <el-upload class="upload-wrap" action="" :before-upload="uploadItem">
          <el-button icon="el-icon-upload" size="mini" type="primary">
            Excel导入
          </el-button>
        </el-upload>
        <el-button icon="el-icon-download" type="primary" size="mini" @click="downloadItem()">
          模板下载
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="省" width="100px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.province }}</span>
          </template>
        </el-table-column>

        <el-table-column label="市" width="100px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.city }}</span>
          </template>
        </el-table-column>

        <el-table-column label="县/区" width="100px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.area }}</span>
          </template>
        </el-table-column>

        <el-table-column label="地址" width="200px" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.address }}</span>
          </template>
        </el-table-column>

        <el-table-column label="面积（㎡）" width="100px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.communityArea }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报修电话" width="100px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.repairCall }}</span>
          </template>
        </el-table-column>

        <el-table-column label="客服电话" width="100px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.serviceCall }}</span>
          </template>
        </el-table-column>

        <el-table-column label="健康码" width="100px" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.healthCode" type="primary" size="mini" icon="el-icon-edit" plain @click="settingPic(scope.row, 'healthCode')">修改</el-button>
            <el-button v-else type="primary" size="mini" icon="el-icon-setting" plain @click="settingPic(scope.row, 'healthCode')">设置</el-button>
          </template>
        </el-table-column>

        <el-table-column label="行程码" width="100px" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.tripCode" type="primary" size="mini" icon="el-icon-edit" plain @click="settingPic(scope.row, 'tripCode')">修改</el-button>
            <el-button v-else type="primary" size="mini" icon="el-icon-setting" plain @click="settingPic(scope.row, 'tripCode')">设置</el-button>
          </template>
        </el-table-column>

        <el-table-column label="出行卡" width="100px" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.beginTime" type="primary" size="mini" icon="el-icon-edit" plain @click="settingPic(scope.row, 'travelCard')">修改</el-button>
            <el-button v-else type="primary" size="mini" icon="el-icon-setting" plain @click="settingPic(scope.row, 'travelCard')">设置</el-button>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button>
            <el-button type="success" v-if="scope.row.flag == 2" size="mini" icon="el-icon-check" plain @click="delItem(scope.row, 0)">启用</el-button>
            <el-button type="warning" v-if="scope.row.flag == 0" size="mini" icon="el-icon-minus" plain @click="delItem(scope.row, 2)">停用</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑小区信息" :visible.sync="dlgShow" width='800px' append-to-body>

      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="120px">
        <el-form-item label="小区名称" prop="name">
          <el-input v-model="dlgData.name" placeholder="请输入名称" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="省" prop="provinceCode">
              <el-select v-model="dlgData.provinceCode" placeholder="请选择省" @change="provinceChange">
                <el-option v-for="item in provinceList" :key="item.id" :label="item.areaName" :value="item.areaCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="市" prop="cityCode">
              <el-select v-model="dlgData.cityCode" placeholder="请选择市" @change="cityChange">
                <el-option v-for="item in cityList" :key="item.id" :label="item.areaName" :value="item.areaCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="县/区" prop="areaCode">
              <el-select v-model="dlgData.areaCode" placeholder="请选择县/区">
                <el-option v-for="item in areaList" :key="item.id" :label="item.areaName" :value="item.areaCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="维修基金单价" prop="fundPrice">
              <el-input-number v-model="dlgData.fundPrice" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="面积">
              <el-input-number v-model="dlgData.communityArea" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="地址" prop="address">
          <el-input v-model="dlgData.address" placeholder="请输入地址" />
        </el-form-item>

        <el-form-item label="报修电话" prop="repairCall">
          <el-input v-model="dlgData.repairCall" placeholder="请输入报修电话" />
        </el-form-item>

        <el-form-item label="客服电话" prop="serviceCall">
          <el-input v-model="dlgData.serviceCall" placeholder="请输入客服电话" />
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="缴费模式">
              <el-switch class="switchStyle" v-model="dlgData.isPay" :active-value="0" :inactive-value="1" active-text="线上" inactive-text="线下">
              </el-switch>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12" v-if="dlgData.isPay == 0">
            <el-form-item label="子商户商户ID" prop="subMchId">
              <el-input v-model="dlgData.subMchId" placeholder="请输入子商户商户ID" />
            </el-form-item>
          </el-col> -->
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal='false' :title="dlgTitle" :visible.sync="dlgShowPic" width='600px' append-to-body>
      <el-form ref="dlgFormPic" :rules="rules" :model="dlgData">
        <template v-if="dlgType === 'healthCode'">
          <el-form-item class="text-center">
            <el-upload class="avatar-uploader" action='' :show-file-list="false" :before-upload="beforeUpload">
              <el-image v-if="dlgData.healthCode" class='upload-img' :src="dlgData.healthCode" alt=""></el-image>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <i v-if="dlgData.healthCode" @click.stop='delUploadImg()' class="el-icon-error avatar_icon"></i>
            </el-upload>
            <span v-if="dlgData.healthCode" @click="onPreview(dlgData.healthCode)" class="link-type">预览</span>
          </el-form-item>
        </template>
        <template v-else-if="dlgType === 'tripCode'">
          <el-form-item class="text-center">
            <el-upload class="avatar-uploader" action='' :show-file-list="false" :before-upload="beforeUpload">
              <el-image v-if="dlgData.tripCode" class='upload-img' :src="dlgData.tripCode" alt=""></el-image>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <i v-if="dlgData.tripCode" @click.stop='delUploadImg()' class="el-icon-error avatar_icon"></i>
            </el-upload>
            <span v-if="dlgData.tripCode" @click="onPreview(dlgData.tripCode)" class="link-type">预览</span>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item prop="efDay" label-width="100px">
            <b>每户每</b>
            <el-input-number v-model="dlgData.efDay" :controls='false' :min="0" :precision="0" :step="1"></el-input-number>
            <b>天出入</b>
            <el-input-number v-model="dlgData.ogSecond" :controls='false' :min="0" :precision="0" :step="1"></el-input-number> <b>次</b>
          </el-form-item>
          <el-form-item prop="beginTime" label-width="100px">
            <b>出入时间</b>
            <el-time-picker format="HH:mm" :picker-options="{selectableRange:`00:00:00-${dlgData.endTime ? dlgData.endTime + ':00' : '23:59:59'}`}" value-format="HH:mm" v-model="dlgData.beginTime" placeholder="开始时间" style="width:140px;">
            </el-time-picker>
            <b>至</b>
            <el-time-picker format="HH:mm" :picker-options="{selectableRange:`${dlgData.beginTime ? dlgData.beginTime + ':00' : '00:00:00'}-23:59:59`}" value-format="HH:mm" v-model="dlgData.endTime" placeholder="结束时间" style="width:140px;">
            </el-time-picker>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShowPic = false" icon='el-icon-back'>取消</el-button>
        <el-button type='success' :loading='dlgLoading' @click="subDlgPic" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <el-image-viewer v-show="showViewer" :on-close="closeViewer" ref="imageViewer" :url-list="urlList" />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, threeLinkage, communityAddOrUpdate, communityDisable, importCommunity } from '@/api/communityMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import * as constConfig from '@/configs/const.js'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

let dlgDataEmpty = {
  id: '',
  address: '',
  beginTime: '',
  name: '',
  city: '',
  cityCode: '',
  communityArea: '',
  area: '',
  areaCode: '',
  efDay: '',
  endTime: '',
  healthCode: '',
  fundPrice: '',
  mapX: '',
  mapY: '',
  name: '',
  ogSecond: '',
  projectId: '',
  province: '',
  provinceCode: '',
  tripCode: '',
  serviceCall: '',
  repairCall: '',
  isPay: 0,
  subMchId: ''
}


export default {
  name: 'basicInfo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    ElImageViewer
  },
  data () {
    return {
      showViewer: false,
      urlList: [],
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgShowPic: false, // 二维码
      dlgTitle: '', // 标题

      rules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        fundPrice: [{ required: true, message: '必填字段', trigger: 'blur' }],
        // subMchId: [{ required: true, message: '必填字段', trigger: 'blur' }],
        provinceCode: [{ required: true, message: '必填字段', trigger: 'change' }],
        cityCode: [{ required: true, message: '必填字段', trigger: 'change' }],
        areaCode: [{ required: true, message: '必填字段', trigger: 'change' }],
        address: [{ required: true, message: '必填字段', trigger: 'change' }],
        efDay: [{
          required: true, validator: (rules, value, cb) => {
            let { efDay, ogSecond } = this.dlgData;
            if (!efDay) {
              return cb(new Error("必填字段"));
            }
            // if (!ogSecond) {
            //   return cb(new Error("必填字段"));
            // }
            return cb();
          }, trigger: 'change'
        }],
        beginTime: [{
          required: true, validator: (rules, value, cb) => {
            let { beginTime, endTime } = this.dlgData;
            if (!beginTime) {
              return cb(new Error("开始时间不能为空"));
            }
            if (!endTime) {
              return cb(new Error("结束时间不能为空"));
            }
            return cb();
          }, trigger: 'change'
        }],
        healthCode: [{ required: true, message: '必填字段', trigger: 'change' }],
        tripCode: [{ required: true, message: '必填字段', trigger: 'change' }],
        repairCall: [
          {
            pattern: constConfig.TEL_REG,
            message: '报修电话格式有误！',
            trigger: 'blur'
          }
        ],
        serviceCall: [
          {
            pattern: constConfig.TEL_REG,
            message: '客服电话格式有误！',
            trigger: 'blur'
          }
        ]
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
      },

      //省市区
      provinceList: [],
      cityList: [],
      areaList: [],

      userInfo: {}
    }
  },
  computed: {

  },
  watch: {

  },
  created () {
    this.getProvinceList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 预览
    onPreview (pic) {
      this.urlList = [pic]
      this.showViewer = true
    },

    // 关闭查看器
    closeViewer () {
      this.showViewer = false
    },

    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/unity/report/exportCommunity?` + param
      window.open(sendUrl)
    },

    // 下载
    downloadItem () {
      let url =
        'https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E5%AF%BC%E5%85%A5%E5%B0%8F%E5%8C%BA%E6%A8%A1%E6%9D%BF.xlsx'
      window.open(url)
    },

    // 上传
    uploadItem (file) {
      let name = file.name.split('.')
      let suffix = name[name.length - 1]

      if (suffix !== 'xls' && suffix !== 'xlsx') {
        this.$message({
          type: 'warning',
          message: '只能上传xls/xlsx文件'
        })
        return false
      }

      let loading = this.$loading({
        lock: true,
        text: '导入中',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      let postParam = {
        file
      }

      importCommunity(postParam).then(res => {
        loading.close()
        if (res.data.code == 200) {
          this.$message.success('导入成功')
          this.getList()
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })

      return false
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取省数据
    getProvinceList () {
      threeLinkage(101).then(res => {
        if (res.data.code == 200) {
          this.provinceList = res.data.data
        }
      })
    },

    // 获取市数据
    getCityList (code) {
      if (utils.isNull(code)) {
        return
      }
      this.cityList = []
      threeLinkage(202, code).then(res => {
        if (res.data.code == 200) {
          this.cityList = res.data.data
        }
      })
    },

    // 获取县数据
    getAreaList (code) {
      if (utils.isNull(code)) {
        return
      }
      this.areaList = []
      threeLinkage(303, code).then(res => {
        if (res.data.code == 200) {
          this.areaList = res.data.data
        }
      })
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      communityPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 省改变
    provinceChange () {
      this.getCityList(this.dlgData.provinceCode)
      this.dlgData.cityCode = ""
      this.dlgData.areaCode = ""
      this.areaList = []
    },

    // 市改变
    cityChange () {
      this.getAreaList(this.dlgData.cityCode)
      this.dlgData.areaCode = ""
    },

    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.province = utils.getNameById(postParam.provinceCode, this.provinceList, 'areaCode', 'areaName')
          postParam.city = utils.getNameById(postParam.cityCode, this.cityList, 'areaCode', 'areaName')
          postParam.area = utils.getNameById(postParam.areaCode, this.areaList, 'areaCode', 'areaName')
          postParam.mapX = utils.getNameById(postParam.cityCode, this.cityList, 'areaCode', 'lat')
          postParam.mapY = utils.getNameById(postParam.cityCode, this.cityList, 'areaCode', 'lon')
          this.dlgLoading = true
          communityAddOrUpdate(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 提交图片
    subDlgPic () {
      this.$refs['dlgFormPic'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          this.dlgLoading = true
          communityAddOrUpdate(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShowPic = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
      this.getCityList(this.dlgData.provinceCode)
      this.getAreaList(this.dlgData.cityCode)
    },

    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        communityDisable(data.id, flag).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 设置图片
    settingPic (data, flag) {
      this.dlgTitle = "健康码"
      if (flag == 'tripCode') {
        this.dlgTitle = '行程码'
      } else if (flag == 'travelCard') {
        this.dlgTitle = '出入卡'
      }
      this.dlgType = flag
      this.dlgShowPic = true
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.$nextTick(() => {
        this.$refs['dlgFormPic'].clearValidate()
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/basicInfo_').then(res => {
        _this.dlgData[_this.dlgType] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData[_this.dlgType] = ""
      })
    },
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
/deep/ .switchStyle .el-switch__label {
  position: absolute;
  display: none;
  color: #fff;
}
/deep/ .switchStyle .el-switch__label--left {
  z-index: 9;
  left: 24px;
}
/deep/ .switchStyle .el-switch__label--right {
  z-index: 9;
}
/deep/ .switchStyle .el-switch__label.is-active {
  display: block;
}
/deep/ .switchStyle.el-switch .el-switch__core,
/deep/ .el-switch .el-switch__label {
  width: 60px !important;
  span {
    font-size: 12px;
  }
}
</style>


