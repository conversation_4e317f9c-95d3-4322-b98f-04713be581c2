<template>
  <!-- 考勤管理 -->
  <div class="app-container" ref="schedulingMan">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="月份">
          <el-date-picker :picker-options="pickerOptions" v-model="listQuery.date" value-format="yyyy-MM" format="yyyy-MM" type="month" placeholder="月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-radio-group @change="getList" v-model="listQuery.branchType">
            <el-radio label="0">当前部门</el-radio>
            <el-radio label="1">当前及所属部门</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.branchName" @focus="showBranchDlg" :title='listQuery.branchName' placeholder="选择部门" readonly>
            <i @click='resetSearchItem(["branchId", "branchName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.label" placeholder='请输入姓名'>
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-download' type="primary" size='mini' @click='exportExcel'>导出</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="姓名" width="100px">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属部门" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="工时" align="center">
          <el-table-column label="标准" width="80px" align="center">
            <template slot-scope="scope">
              <span class="font-bold">{{ scope.row.standardWorkHour }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实际" width="80px" align="center">
            <template slot-scope="scope">
              <span class="font-bold fblur">{{ scope.row.actualWorkHour }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="出勤天数" width="80px" align="center">
          <template slot-scope="scope">
            <span :class="scope.row.actualDays == scope.row.attendanceDays ? '' : 'fdanger'">{{ scope.row.actualDays }} / {{ scope.row.attendanceDays }}</span>
          </template>
        </el-table-column>

        <el-table-column label="节假日上班数" width="70px" align="center">
          <template slot-scope="scope">
            <span :class="scope.row.holidayWorkDays > 0 ? 'fsuccess' : ''">{{ scope.row.holidayWorkDays }}</span>
          </template>
        </el-table-column>

        <el-table-column label="迟到" align="center">
          <el-table-column label="次数" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.lateCount > 0 ? 'fdanger' : ''">{{ scope.row.lateCount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="分钟" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.lateMins > 0 ? 'fdanger' : ''">{{ scope.row.lateMins }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="早退" align="center">
          <el-table-column label="次数" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.earlyCount > 0 ? 'fdanger' : ''">{{ scope.row.earlyCount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="分钟" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.earlyMins > 0 ? 'fdanger' : ''">{{ scope.row.earlyMins }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="假期" align="center">
          <el-table-column label="暑假" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.summerDays > 0 ? 'fdanger' : ''">{{ scope.row.summerDays }}</span>
            </template>
          </el-table-column>
          <el-table-column label="寒假" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.winterDays > 0 ? 'fdanger' : ''">{{ scope.row.winterDays }}</span>
            </template>
          </el-table-column>
          <el-table-column label="事假" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.compassLeaveDays > 0 ? 'fdanger' : ''">{{ scope.row.compassLeaveDays }}</span>
            </template>
          </el-table-column>
          <el-table-column label="病假" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.sickLeaveDays > 0 ? 'fdanger' : ''">{{ scope.row.sickLeaveDays }}</span>
            </template>
          </el-table-column>
          <el-table-column label="陪护假" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.accompanyLeaveDays > 0 ? 'fdanger' : ''">{{ scope.row.accompanyLeaveDays }}</span>
            </template>
          </el-table-column>
          <el-table-column label="丧假" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.funeralLeave > 0 ? 'fdanger' : ''">{{ scope.row.funeralLeave }}</span>
            </template>
          </el-table-column>
          <el-table-column label="隔离假" width="80px" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.isolationLeaveDays > 0 ? 'fdanger' : ''">{{ scope.row.isolationLeaveDays }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="休息" width="80px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.restDays }}</span>
          </template>
        </el-table-column>

        <el-table-column label="旷工" width="80px" align="center">
          <template slot-scope="scope">
            <span :class="scope.row.missDays > 0 ? 'fdanger' : ''">{{ scope.row.missDays }}</span>
          </template>
        </el-table-column>

        <el-table-column label="工时加项" width="80px" align="center">
          <template slot-scope="scope">
            <span :class="scope.row.addWorkHours > 0 ? 'fsuccess' : ''">{{ scope.row.addWorkHours }}</span>
          </template>
        </el-table-column>

        <el-table-column label="工时减项" width="80px" align="center">
          <template slot-scope="scope">
            <span :class="scope.row.reduceWorkHours > 0 ? 'fdanger' : ''">{{ scope.row.reduceWorkHours }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <DialogBranch @superFunc="superBranch" :superDlgShow.sync="dlgShowBranch" :superSelectId="listQuery.branchId" :superSelectName="listQuery.branchName" :superPermission="true" />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { attendanceSummary } from '@/api/attendanceMan/report.js'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import DialogBranch from '@/components/Dialog/platformMan/DialogBranch'

let maxMonth = utils.getDiffMonth("", -1)
export default {
  components: {
    Pagination,
    DialogBranch
  },
  data () {
    return {
      pickerOptions: {
        disabledDate (time) {
          return
          // return time.getTime() > new Date(maxMonth);
        },
      },

      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        date: '',
        branchId: '',
        branchName: '',
        branchType: '0'
      },
      userInfo: {},

      count: 0,

      dlgShowBranch: false
    }
  },
  computed: {

  },
  watch: {

  },

  created () {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.listQuery.date = maxMonth
  },

  methods: {
    // 显示部门树
    showBranchDlg () {
      this.dlgShowBranch = true
    },

    superBranch (params) {
      this.listQuery.branchId = params.selectId
      this.listQuery.branchName = params.selectName
    },

    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/workade/kaoqinhuizongdaochu?` + param
      window.open(sendUrl)
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 根据类型不同重建数组
    formatList () {

    },


    // 获取数据
    getList () {
      if (utils.isNull(this.listQuery.date)) {
        this.$message.warning("请选择日期")
        return
      }
      this.count++
      this.listLoading = true
      attendanceSummary(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          let list = res.data.data ? JSON.parse(JSON.stringify(res.data.data)) : []
          this.formatList(list)
          this.list = list
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.actual {
  color: #66b1ff;
}
</style>


