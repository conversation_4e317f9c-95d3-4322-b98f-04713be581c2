<template>
  <!-- 数据大屏 -->
  <div class="community">
    <div class="community-container clearfix">
      <div class="community-item">
        <div class="title">
          房屋
        </div>
        <div class="content" id="echart-room"></div>
      </div>
      <div class="community-item">
        <div class="title">
          小区信息
        </div>
        <div class="content community" v-if="homePage.report">
          <el-row class="double">
            <el-col :span="12" v-if="homePage.report1">
              <div class="tip">
                管理面积
              </div>
              <div class="font24">
                {{ homePage.report.communityArea.toFixed(2) }}㎡
              </div>
              <div>
                住宅
                {{
                  homePage.report1
                    .filter(item => {
                      return item.type == "1";
                    })[0]
                    ["area"].toFixed(2)
                }}
                ㎡ 商服
                {{
                  homePage.report1
                    .filter(item => {
                      return item.type == "2";
                    })[0]
                    ["area"].toFixed(2)
                }}
                ㎡
              </div>
              <span class="border"></span>
            </el-col>
            <el-col :span="12">
              <div class="tip">
                房屋总数
              </div>
              <div class="font24">{{ homePage.report.roomCount }}户</div>
              <div>
                住宅
                {{
                  homePage.report1.filter(item => {
                    return item.type == "1";
                  })[0]["total"]
                }}
                户 商服
                {{
                  homePage.report1.filter(item => {
                    return item.type == "2";
                  })[0]["total"]
                }}
                户
              </div>
            </el-col>
          </el-row>
          <el-row class="three">
            <el-col :span="8">
              <div class="tip">小区总数</div>
              <div class="font24">
                {{ homePage.report.communityCount || 0 }}个
              </div>
              <span class="border"></span>
            </el-col>
            <el-col :span="8">
              <div class="tip">楼栋总数</div>
              <div class="font24">{{ homePage.report.floorCount || 0 }}个</div>
              <span class="border"></span>
            </el-col>
            <el-col :span="8">
              <div class="tip">车库总数</div>
              <div class="font24">{{ homePage.report.garageCount || 0 }}个</div>
            </el-col>
          </el-row>
          <el-row class="three">
            <el-col :span="8">
              <div class="tip">车位数量</div>
              <div class="font24">
                {{ homePage.report.parkingCount || 0 }}个
              </div>
              <span class="border"></span>
            </el-col>
            <el-col :span="8">
              <div class="tip">人员数量</div>
              <div class="font24">{{ homePage.report.memberCount || 0 }}个</div>
              <span class="border"></span>
            </el-col>
            <el-col :span="8">
              <div class="tip">业主数量</div>
              <div class="font24">{{ homePage.report.ownerCount || 0 }}个</div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="community-item">
        <div class="title">
          费用预警
        </div>
        <div class="content notice">
          <div class="notice-title">
            <i class="icon"></i>
            欠费提醒
          </div>
          <div class="notice-content">
            <p v-for="(item, index) in homePage.qianfei" :key="index">
              {{
                item.payType == 1
                  ? "房屋"
                  : item.payType == 2
                  ? "车位"
                  : "车库"
              }}: <span>{{ item.count }}</span> 户 欠费
            </p>
          </div>
          <div class="notice-title">
            <i class="icon"></i>
            到期提醒
          </div>
          <div class="notice-content">
            <p v-for="(item, index) in homePage.daoqi" :key="index">
              {{
                item.payType == 1
                  ? "房屋"
                  : item.payType == 2
                  ? "车位"
                  : "车库"
              }}: <span>{{ item.count }}</span> 户 即将到期
            </p>
          </div>
        </div>
      </div>
      <div class="community-item">
        <div class="title">
          车位
        </div>
        <div class="content" id="echart-park"></div>
      </div>
      <div class="community-item">
        <div class="title">
          报修
        </div>
        <div class="content" id="echart-repair"></div>
      </div>
      <div class="community-item">
        <div class="title">
          费用
        </div>
        <div class="content" id="echart-cost"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { formScreen } from "@/api/reportMan";
import * as utils from "@/utils";
import * as echarts from "echarts";

export default {
  components: {},
  data() {
    return {
      homePage: {},

      echartRoom: null,

      echartPark: null,

      echartRepair: null,

      echartBill: null
    };
  },
  created() {
    this.getHomePage();
  },
  mounted() {
    window.addEventListener("resize", () => {
      if (!utils.isNull(this.echartRoom)) {
        this.echartRoom.resize();
      }
      if (!utils.isNull(this.echartPark)) {
        this.echartPark.resize();
      }
      if (!utils.isNull(this.echartRepair)) {
        this.echartRepair.resize();
      }
      if (!utils.isNull(this.echartBill)) {
        this.echartBill.resize();
      }
    });
  },
  methods: {
    // 创建room饼状图
    createRoom() {
      if (!this.homePage.roomState) {
        if (!utils.isNull(this.echartRoom)) {
          this.echartRoom.clear();
        }
        return;
      }
      let legendData = [];
      let seriesData = [];
      for (let i of this.homePage.roomState) {
        let name = i.state == "0" ? "空置" : "入住";
        legendData.push(name);
        seriesData.push({
          name,
          value: i.total
        });
      }
      // 基于准备好的dom，初始化echarts实例
      this.echartRoom = echarts.init(document.getElementById("echart-room"));
      // 指定图表的配置项和数据
      let option = {
        color: ["#657798", "#63DAAB"],
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)"
        },
        legend: {
          orient: "vertical",
          right: "10%",
          top: "38%",
          data: legendData,
          textStyle: {
            color: "#249CF9"
          }
        },
        series: [
          {
            name: "房屋入住率",
            type: "pie",
            center: ["40%", "50%"],
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: true,
              formatter: "{d}%"
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "14"
              }
            },
            data: seriesData
          }
        ]
      };

      this.echartRoom.setOption(option);
    },

    // 创建park饼状图
    createPark() {
      if (!this.homePage.parkingState) {
        if (!utils.isNull(this.echartPark)) {
          this.echartPark.clear();
        }
        return;
      }
      let legendData = [];
      let seriesData = [];
      for (let i of this.homePage.parkingState) {
        let name = i.state == "0" ? "空置" : i.state == "1" ? "已售" : "出租";
        legendData.push(name);
        seriesData.push({
          name,
          value: i.total
        });
      }
      // 基于准备好的dom，初始化echarts实例
      this.echartPark = echarts.init(document.getElementById("echart-park"));
      // 指定图表的配置项和数据
      let option = {
        color: ["#657798", "#63DAAB", "#5B8FF9"],
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)"
        },
        legend: {
          orient: "vertical",
          right: "10%",
          top: "38%",
          data: legendData,
          textStyle: {
            color: "#249CF9"
          }
        },
        series: [
          {
            name: "房屋入住率",
            type: "pie",
            center: ["40%", "50%"],
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: true,
              formatter: "{d}%"
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "14"
              }
            },
            data: seriesData
          }
        ]
      };

      this.echartPark.setOption(option);
    },

    // 创建报修图
    createRpair() {
      if (!this.homePage.repairPoolState) {
        if (!utils.isNull(this.echartRepair)) {
          this.echartRepair.clear();
        }
        return;
      }
      let legendData = [];
      let seriesData = [];
      let total = 0;
      for (let i of this.homePage.repairPoolState) {
        if (i.state == 0) {
          legendData.push("待处理");
          seriesData.push({
            name: "待处理",
            value: i.count
          });
        } else if (i.state == 1) {
          legendData.push("处理中");
          seriesData.push({
            name: "处理中",
            value: i.count
          });
        } else {
          total += i.count;
        }
      }
      legendData.push("已处理");
      seriesData.push({
        name: "已处理",
        value: total
      });
      // 基于准备好的dom，初始化echarts实例
      this.echartRepair = echarts.init(
        document.getElementById("echart-repair")
      );
      // 指定图表的配置项和数据
      let option = {
        color: ["#E8684A", "#EFBA1D", "#63DAAB"],
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)"
        },
        legend: {
          orient: "vertical",
          right: "10%",
          top: "38%",
          data: legendData,
          textStyle: {
            color: "#249CF9"
          }
        },
        series: [
          {
            name: "房屋入住率",
            type: "pie",
            center: ["40%", "50%"],
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: true,
              formatter: "{d}%"
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "14"
              }
            },
            data: seriesData
          }
        ]
      };

      this.echartRepair.setOption(option);
    },

    // 创建费用
    createBill() {
      if (!this.homePage.payFeeBills) {
        if (!utils.isNull(this.echartBill)) {
          this.echartBill.clear();
        }
        return;
      }
      let colorList = [
        "#249CF9",
        "#EB6F49",
        "#FDB628",
        "#00E4EC",
        "#69FD28",
        "#C490BF",
        "#FFF100",
        "#486A00",
        "#F6B37F",
        "#7ECEF4",
        "#22AC38",
        "#7E6B5A"
      ];
      let legendData = [];
      let xData = [];
      let seriesData = [];
      for (let i in this.homePage.payFeeBills) {
        let color = colorList[i % colorList.length];
        xData.push(this.homePage.payFeeBills[i].year);
        for (let j of this.homePage.payFeeBills[i].typeValues) {
          seriesData.push({
            name: j.type == 1 ? "房屋" : j.type == 2 ? "车位" : "车库",
            type: "line",
            stack: "总费用",
            data: [j.value],
            itemStyle: {
              normal: {
                lineStyle: {
                  color
                }
              }
            }
          });
          if (i == 0) {
            legendData.push({
              name: j.type == 1 ? "房屋" : j.type == 2 ? "车位" : "车库"
            });
          }
        }
      }
      // 基于准备好的dom，初始化echarts实例
      this.echartBill = echarts.init(document.getElementById("echart-cost"));
      // 指定图表的配置项和数据
      let option = {
        tooltip: {
          trigger: "axis"
        },
        color: colorList,
        legend: {
          data: legendData,
          orient: "horizontal",
          y: "top"
        },
        dataZoom: [
          {
            type: "inside"
          }
        ],
        grid: {
          left: "0",
          right: "2%",
          top: "10%",
          bottom: "2%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: xData
        },
        yAxis: {
          type: "value"
        },
        series: seriesData
      };

      this.echartBill.setOption(option);
    },

    // 获取首页数据
    getHomePage() {
      formScreen().then(res => {
        if (res.data.code == 200) {
          this.homePage = res.data.data;
          this.$nextTick(() => {
            this.createRoom();
            this.createPark();
            this.createRpair();
            this.createBill();
          });
        } else {
          this.$message.error(res.data.msg);
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.community {
  width: 100%;
  height: 100%;
  margin: 0;
  background: #f5f5f5;
  /deep/ .community-container {
    .community-item {
      float: left;
      width: calc(33% - 12px);
      height: 360px;
      margin-right: 24px;
      margin-bottom: 24px;
      padding: 0 24px;
      background: #ffffff;
      border-radius: 2px;
      .title {
        height: 55px;
        line-height: 55px;
        font-size: 16x;
        color: #000;
        border-bottom: 1px solid #e8e8e8;
      }
    }
    .community-item:nth-child(3),
    .community-item:last-child {
      margin-right: 0;
    }
    .content {
      height: calc(100% - 56px);
    }
    .content.community {
      background: #ffffff;
      padding: 24px 8px;
      color: #000;
    }
    .content.notice {
      padding: 16px 36px;
      .notice-title {
        color: #000;
        margin-bottom: 10px;
        i.icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 16px;
          background: url("/static/image/notice.png") no-repeat center;
          vertical-align: middle;
        }
      }
      .notice-content {
        padding-left: 56px;
        color: rgba($color: #000000, $alpha: 0.6);
        margin-bottom: 20px;
        span {
          color: #409eff;
        }
      }
    }
  }
}
.el-col {
  position: relative;
}

.tip {
  color: rgba($color: #000000, $alpha: 0.6);
  margin-bottom: 10px;
}
.font24 {
  margin-bottom: 10px;
}
.border {
  position: absolute;
  width: 1px;
  height: 34px;
  background: #e9e9e9;
  right: 0;
  top: 50%;
  margin-top: -17px;
}
.double {
  margin-bottom: 26px;
}
.double .el-col:last-child {
  padding-left: 46px;
}
.three {
  margin-bottom: 24px;
}
.three .el-col {
  text-align: center;
}
</style>
