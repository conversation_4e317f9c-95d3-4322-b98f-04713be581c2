import request from '@/utils/request'

/*
* 调度中心相关
*/

// 查询调度中心列表 
export function findTaskDynamic(data) {
	return request({
		url: `/tra/findTaskDynamic`,
		method: 'post',
		data
	})
}


// 取消任务
export function cancelTask(data) {
	return request({
		url: `/tra/cancelTask`,
		method: 'post',
		data
	})
}

// 指派 重派 接单
export function taskAssign(data) {
	return request({
		url: `/tra/taskAssign`,
		method: 'post',
		data
	})
}

// 根据taskId查看当前任务
export function findTaskByTaskId(data) {
	return request({
		url: `/tra/findTaskByTaskId`,
		method: 'post',
		data
	})
}



// 查询运送员列表 
export function findTaskUserDynamic(data) {
	return request({
		url: `/tra/findTaskUserDynamic`,
		method: 'post',
		data
	})
}


// 查询物品运送列表 
export function saveGoodsTransTask(data) {
	return request({
		url: `/tra/saveGoodsTransTask`,
		method: 'post',
		data
	})
}


// 查询今日总任务 
export function findTaskByToday(data) {
	return request({
		url: `/tra/findTaskByToday`,
		method: 'post',
		data
	})
}


// 循环任务定时接口
export function findTaskConfigEveryDay(data) {
	return request({
		url: `/tra/findTaskConfigEveryDay`,
		method: 'post',
		data
	})
}


// 配置定时任务时间
export function updateScheduled(data) {
	return request({
		url: `/tra/updateScheduled`,
		method: 'post',
		data
	})
}

// 计算运送任务时间
export function countBranchsMinutes(data) {
	return request({
		url: `/tra/countBranchsMinutes`,
		method: 'post',
		data
	})
}





