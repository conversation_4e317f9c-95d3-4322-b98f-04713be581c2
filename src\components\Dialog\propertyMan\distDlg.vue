<template>
  <el-dialog :close-on-click-modal='false' :title="'区域列表'" :visible.sync="dlgShow">
    <div class="filter-container">
      <el-form ref="searchForm" :inline="true" class='clearfix' label-width="90px" @submit.native.prevent>
        <el-form-item>
          <el-input v-model="listQuery.str" placeholder='请填写区域名称'>
            <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">
          搜索
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectDistId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>

        <el-table-column label="组织机构名称">
          <template slot-scope="scope">
            <span>{{ scope.row.projectName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="区域名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'

import { findDistrictLike } from '@/api/districtManager'

export default {
  components: {
    Pagination
  },
  data() {
    return {
      list: [],

      listQuery: {
        page: 1,
        size: 10,
        str: "",
        branchId: "",
        projectId: ''
      },

      total: 0,

      selectDistId: '',

      selectDistName: ''

    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.propertyMan.distDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('propertyMan/distDlg/SET_DLGSHOW', val)
      }
    },

    distId: {
      get: function () {
        return this.$store.state.propertyMan.distDlg.distId
      },
      set: function (val) {
        this.$store.commit('propertyMan/distDlg/SET_DISTID', val)
      }
    },

    distName: {
      get: function () {
        return this.$store.state.propertyMan.distDlg.distName
      },
      set: function (val) {
        this.$store.commit('propertyMan/distDlg/SET_DISTNAME', val)
      }
    },

    projectId: {
      get: function () {
        return this.$store.state.propertyMan.distDlg.projectId
      },
      set: function (val) {
        this.$store.commit('propertyMan/distDlg/SET_PROJECTID', val)
      }
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.distId)) {
          this.selectDistId = ""
          this.selectDistName = ""
        }
        this.getList()
      }
    },

    distId(val) {
      this.selectDistId = val
    },

    distName(val) {
      this.selectDistName = val
    },

    projectId: {
      handler(val) {
        console.log(val)
        this.listQuery.projectId = val
      },
      immediate: true
    }
  },

  methods: {
    resetStr() {
      this.listQuery.str = ""
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    rowClick(row, column, event) {
      this.selectDistId = row['id']
      this.selectDistName = row['name']
    },

    getList() {
      this.list = []
      findDistrictLike(this.listQuery).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      this.distId = this.selectDistId
      this.distName = this.selectDistName
      this.$store.commit('propertyMan/distDlg/SET_DISTID', this.distId)
      this.$store.commit('propertyMan/distDlg/SET_DISTNAME', this.distName)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('propertyMan/distDlg/SET_DLGSHOW', false)
    }
  }
}
</script>