import Cookies from 'js-cookie'

const app = {
  state: {
    sidebar: {
      opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
      withoutAnimation: false
    },
    device: 'desktop',
    language: 'zh',
    size: Cookies.get('size') || 'mini',

    // showAnim: true,  // 页面切换时，是否显示动画
  },
  mutations: {
    // 修改 是否执行动画
    // SET_SHOWANIM: (state, showAnim) => {
    //   state.showAnim = showAnim
    // },


    TOGGLE_SIDEBAR: state => {
      state.sidebar.opened = !state.sidebar.opened
      state.sidebar.withoutAnimation = false
      if (state.sidebar.opened) {
        Cookies.set('sidebarStatus', 1)
      } else {
        Cookies.set('sidebarStatus', 0)
      }
    },
    OPEN_SIDEBAR: (state, withoutAnimation) => {
      Cookies.set('sidebarStatus', 1)
      state.sidebar.opened = true
      state.sidebar.withoutAnimation = withoutAnimation
    },
    CLOSE_SIDEBAR: (state, withoutAnimation) => {
      Cookies.set('sidebarStatus', 0)
      state.sidebar.opened = false
      state.sidebar.withoutAnimation = withoutAnimation
    },
    TOGGLE_DEVICE: (state, device) => {
      state.device = device
    },
    SET_LANGUAGE: (state, language) => {
      state.language = 'zh'
      Cookies.set('language', 'zh')
    },
    SET_SIZE: (state, size) => {
      state.size = size
      Cookies.set('size', size)
    }
  },
  actions: {
    toggleSideBar ({
      commit
    }) {
      commit('TOGGLE_SIDEBAR')
    },
    openSideBar ({
      commit
    }, {
      withoutAnimation
    }) {
      commit('OPEN_SIDEBAR', withoutAnimation)
    },
    closeSideBar ({
      commit
    }, {
      withoutAnimation
    }) {
      commit('CLOSE_SIDEBAR', withoutAnimation)
    },
    toggleDevice ({
      commit
    }, device) {
      commit('TOGGLE_DEVICE', device)
    },
    setLanguage ({
      commit
    }, language) {
      commit('SET_LANGUAGE', 'zh')
    },
    setSize ({
      commit
    }, size) {
      commit('SET_SIZE', size)
    }
  }
}

export default app
