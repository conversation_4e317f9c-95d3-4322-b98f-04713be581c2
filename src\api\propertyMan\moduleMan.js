import request from '@/utils/request'

/*
 *模块相关
 */

// 模块列表 
export function findModuleListPage(data) {
  return request({
    url: `/sys/findModuleListPage`,
    method: 'post',
    data
  })
}

// 设置模块权限
export function moduleSetPermission(data) {
  return request({
    url: `/sys/moduleSetPermission`,
    method: 'post',
    data
  })
}

// 根据模块查权限
export function findModuleInfos(id) {
  return request({
    url: `/sys/findModuleInfos/${id}`,
    method: 'get'
  })
}

// 删除模块
export function delModuleInfos(id) {
  return request({
    url: `/sys/delModuleInfos/${id}`,
    method: 'get'
  })
}
