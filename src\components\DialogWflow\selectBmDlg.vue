<template>
  <!-- byUser branch role -->
  <div class="sbm">
    <el-dialog
      v-if="deviceType == 'PC'"
      class="mazhenguo"
      :title="`选择${title}`"
      :close-on-click-modal="false"
      :visible.sync="dlgState"
      append-to-body
      width="600px"
      top="30px"
    >
      <el-input placeholder="输入关键字进行过滤" v-model="filterBranch">
      </el-input>
      <el-tree
        class="mt10"
        ref="branchTree"
        highlight-current
        node-key="id"
        :data="treeData"
        @node-click="treeNodeClick"
        default-expand-all
        :filter-node-method="filterNode"
        :expand-on-click-node="false"
      >
      </el-tree>
      <div slot="footer" class="dialog-footer">
        <span class="dialog-footer-span" v-if="selectNode && selectNode.label"
          >当前选中：{{ selectNode.label }}</span
        >
        <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
        <el-button
          v-if="dlgType != 'info'"
          :loading="dlgSubLoading"
          type="success"
          @click="dlgSubFunc"
          icon="el-icon-check"
        >
          <span v-if="dlgSubLoading">确定中...</span>
          <span v-else>确定</span>
        </el-button>
      </div>
    </el-dialog>
    <popup
      v-else
      v-model="dlgState"
      :style="popupStyle"
      position="left"
      lazy-render
      safe-area-inset-bottom
    >
      <nav-bar
        title="请选择部门"
        left-text="返回"
        right-text="确定"
        left-arrow
        @click-left="closeDlg"
        @click-right="dlgSubFunc"
      />
      <div class="mobile-picker">
        <div style="padding: 15px">
          <!-- <el-input
          v-model="search"
          clearable
          prefix-icon="el-icon-search"
          placeholder="搜索人员，支持拼音、姓名"
        ></el-input> -->
          <el-input
            v-model="filterBranch"
            clearable
            prefix-icon="el-icon-search"
            placeholder="关键字"
          ></el-input>
        </div>
        <cell class="m-org-item m-org-item-tab">
          <template #title>
            <checkbox
              shape="square"
              :disabled="!multiple"
              @change="handleCheckAllChange"
              v-model="checkAll"
            >
              全选
              <span style="margin: 0 10px; color: #8c8c8c"
                >已选 [ {{ selectList.length }} ]</span
              >
              <span
                v-show="selectList.length > 0 && multiple"
                style="color:#3971f8;"
                @click.stop="clearSelected"
              >
                清空</span
              >
            </checkbox>
          </template>
          <template #right-icon v-if="deptStack.length > 0">
            <div @click="beforeNode" class="to-top">上一级</div>
          </template>
        </cell>
        <list
          v-model="listLoading"
          finished
          finished-text=""
          error-text="请求失败，点击重新加载"
        >
          <cell class="m-org-item " v-for="org in list" :key="org.id">
            <template #title>
              <div @click="selectChange(org)" style="flex:1">
                <checkbox v-model="org.selected">
                  {{ org.label }}
                  <!-- <avatar :name="org.label" :src="org.avatar"></avatar> -->
                </checkbox>
              </div>
            </template>
            <template #right-icon>
              <div
                @click.stop="nextNode(org)"
                :class="{
                  'm-org-item-next': true,
                  'm-org-item-next-disabled': org.selected
                }"
              >
                <i class="iconfont icon-map-site"></i>
                <span> 下级</span>
              </div>
            </template>
          </cell>
        </list>
      </div>
    </popup>
  </div>
</template>
<script>
import { uploadImg, uploadImg2 } from "@/utils/uploadImg";
import * as utils from "@/utils";
import * as regUtils from "@/utils/regUtils";
import { postAction, getAction } from "@/api";

// 移动端
import {
  Popup,
  List,
  Cell,
  NavBar,
  Radio,
  Checkbox,
  Dialog,
  Toast
} from "vant";
import Avatar from "@/components/workFlow/common/Avatar";

export default {
  components: {
    Popup,
    List,
    Cell,
    NavBar,
    Radio,
    Checkbox,
    Dialog,
    Toast,
    Avatar
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {
      type: Object,
      default: {}
    },
    dlgState0: {
      type: Boolean,
      default: false
    },
    dlgSelectData: {},

    treeType: {
      type: String,
      default: "branch"
    },

    isRole: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: "部门"
    },

    //  DESIGN PC MOBILE
    deviceType: {
      type: String,
      default: "PC"
    }
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val;
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          console.log("--this.dlgSelectData2222", this.dlgSelectData);
          if (this.deviceType == "PC") {
            if (this.dlgSelectData) {
              this.selectNode = this.dlgSelectData;
              this.$nextTick(() => {
                $(".tree-on").removeClass("tree-on");
                this.$refs.branchTree.setCurrentKey(this.selectNode.id);
              });
            } else {
              this.selectNode = "";
            }
          } else {
            if (this.dlgSelectData && this.dlgSelectData.id) {
              this.selectList = [this.dlgSelectData];
            } else {
              this.selectList = [];
            }
            this.getAppList();
          }
        }, 50);
      } else {
        this.$emit("closeDlg");
      }
    },

    filterBranch(val) {
      if (this.deviceType == "PC") {
        this.$refs.branchTree.filter(val);
      } else {
        let list = this.list0.filter(
          item => item.label.indexOf(val.trim()) >= 0
        );
        this.list = JSON.parse(JSON.stringify(list));
      }
    }
  },
  data() {
    return {
      filterBranch: "",
      treeData: [],
      selectNode: {},

      // 弹窗
      dlgState: false,

      dlgSubLoading: false, // 提交loading

      rootId: "",

      listLoading: false,
      selectList: [],

      branchTopId: "",
      // 移动端
      list0: [],
      list: [],

      multiple: false,
      checkAll: false,
      nowDeptId: "",
      // listLoadFinished: false, // list 加载
      deptStack: [],
      popupStyle: {
        height: "100%",
        width: "100%",
        background: "#f7f7f9"
      }
    };
  },
  created() {
    if (this.deviceType == "PC") {
      this.getTree();
    }
  },
  methods: {
    // ---- << 移动端
    selectChange(node) {
      console.log("===点击了吗", node);
      if (node.selected) {
        this.checkAll = false;
        for (let i = 0; i < this.selectList.length; i++) {
          if (this.selectList[i].id === node.id) {
            this.selectList.splice(i, 1);
            break;
          }
        }
        node.selected = false;
      } else {
        node.selected = true;
        let nodes = this.list;
        if (!this.multiple) {
          nodes.forEach(nd => {
            if (node.id !== nd.id) {
              nd.selected = false;
            }
          });
        }

        if (!this.multiple) {
          this.selectList = [node];
        } else {
          this.selectList.push(node);
        }
      }

      this.list = JSON.parse(JSON.stringify(this.list));
    },
    noSelected(index) {
      let nodes = this.list;
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === this.selectList[index].id) {
          nodes[i].selected = false;
          this.checkAll = false;
          break;
        }
      }
      // nodes = this.searchUsers;

      this.selectList.splice(index, 1);
    },
    handleCheckAllChange() {
      this.list.forEach(node => {
        if (this.checkAll) {
          if (!node.selected) {
            node.selected = true;
            this.selectList.push(node);
          }
        } else {
          node.selected = false;
          for (let i = 0; i < this.selectList.length; i++) {
            if (this.selectList[i].id === node.id) {
              this.selectList.splice(i, 1);
              break;
            }
          }
        }
      });
      this.list = JSON.parse(JSON.stringify(this.list));
    },
    clearSelected() {
      if (this.pcMode) {
        this.$confirm("您确定要清空已选中的项?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.checkAll = false;
          this.recover();
        });
      } else {
        Dialog.confirm({
          title: "提示",
          message: "您确定要清空已选项吗？"
        }).then(() => {
          this.checkAll = false;
          this.recover();
        });
      }
    },
    recover() {
      this.selectList = [];
      this.list.forEach(nd => (nd.selected = false));
    },
    // 获取列表
    getAppList() {
      if (!(this.branchTopId + "")) {
        console.log("111");
        getAction("/sys/department/findTreeByFrom").then(res0 => {
          let res = res0.data;

          if (res.code === "200") {
            this.branchTopId = res.list[0].upBranchId;
            this.nowDeptId = res.list[0].upBranchId;
            this.getAppListAjax();
          } else {
            this.$message.error(res.msg);
          }
        });
      } else {
        console.log("222");

        this.getAppListAjax();
      }
    },

    getAppListAjax() {
      this.list = [];
      this.listLoading = true;

      // /saapi/sys/wflow/department/childDept/3025

      console.log("===this.nowDeptId", this.nowDeptId);
      getAction(
        `/sys/wflow/department/childDept/${this.nowDeptId || "0"}`
      ).then(res0 => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = [];
            this.total = 0;
          } else {
            let list0 = res.data || [];

            console.log("===list0", list0);

            if (this.selectList.length > 0) {
              for (let item of list0) {
                let isHas = this.selectList.some(row => row.id == item.id);
                if (isHas) {
                  item.selected = true;
                } else {
                  item.selected = false;
                }
              }
            } else {
              for (let item of list0) {
                item.selected = false;
              }
            }
            this.list0 = [...list0];
            this.list = [...list0];
          }
        } else {
          this.total = 0;
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    nextNode(node) {
      if (!node.selected) {
        this.nowDeptId = node.id;
        this.deptStack.push(node);
        this.getAppList();
      }
    },
    beforeNode() {
      if (this.deptStack.length === 0) {
        return;
      }
      if (this.deptStack.length < 2) {
        this.nowDeptId = null;
      } else {
        this.nowDeptId = this.deptStack[this.deptStack.length - 2].id;
      }
      this.deptStack.splice(this.deptStack.length - 1, 1);
      this.getAppList();
    },

    // ---- >> 移动端
    ////////
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    getTree() {
      if (this.treeType == "byUser") {
        getAction("/sys/department/getDeptTreeByUserId").then(res0 => {
          let res = res0.data;

          if (res.code === "200") {
            console.log("----res.data", res.data);
            this.treeData = [res.data];
            // if (res.msg == '该员工暂无权限查看！') {
            //   this.$message({
            //     type: 'warning',
            //     message: '该员工暂无权限查看！',
            //   })
            // }
          } else {
            this.$message.error(res.msg);
          }
        });
      } else if (this.isRole) {
        // 根据权限
        postAction("/sys/department/findTree").then(res0 => {
          let res = res0.data;
          if (res.code === "200") {
            this.treeData = JSON.parse(JSON.stringify(res.list));

            this.rootId = this.treeData[0].id;
            if (res.msg == "该员工暂无权限查看！") {
              this.$message({
                type: "warning",
                message: "该员工暂无权限查看！"
              });
            }
          } else {
            this.$message.error(res.msg);
          }
        });
      } else if (!this.isRole) {
        // 不根据权限
        getAction("/sys/department/findTreeByFrom").then(res0 => {
          let res = res0.data;

          if (res.code === "200") {
            this.treeData = JSON.parse(JSON.stringify(res.list));
            this.rootId = this.treeData[0].id;
            if (res.msg == "该员工暂无权限查看！") {
              this.$message({
                type: "warning",
                message: "该员工暂无权限查看！"
              });
            }
          } else {
            this.$message.error(res.msg);
          }
        });
      }
    },

    treeNodeClick(data) {
      console.log("====data", data);

      $(".tree-on").removeClass("tree-on");

      if (data.id == this.rootId) {
        this.$message.warning("不能选择该节点");
        this.selectNode = "";
        return false;
      }

      setTimeout(() => {
        $(".is-current>.el-tree-node__content").addClass("tree-on");
      }, 50);
      this.selectNode = data;
    },

    dlgSubFunc() {
      if (this.deviceType == "PC") {
        this.$emit("backFunc", this.selectNode);
      } else {
        this.$emit("backFunc", this.selectList);
      }
      this.closeDlg();
    },

    closeDlg() {
      this.$emit("closeDlg");
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import "~@/assets/workFlow/theme.scss";
// === 移动端
.mobile-picker {
  /deep/ .el-input {
    border-radius: 10px;
    i {
      font-size: 1.2rem;
    }
    .el-input__inner {
      background: #ecedef;
      border: none;
      border-radius: 5px;
      font-size: 1rem;
    }
  }
  .m-org-item-tab {
    margin-bottom: 10px;
    color: $theme-primary;
  }
  .m-org-item {
    color: #303133;
    font-size: 1.1rem !important;
    padding: 15px 15px !important;
    border-bottom: 1px solid $theme-aside-bgc;
    .m-org-item-next {
      cursor: pointer;
      color: $theme-primary;
      font-size: 1.1rem;
    }
    .m-org-item-next-disabled {
      cursor: not-allowed;
      color: #c8c8c8;
    }
    i {
      font-size: 1.1rem;
    }
    .to-top {
      cursor: pointer;
      color: $theme-primary;
    }
  }
}

::-webkit-scrollbar {
  float: right;
  width: 4px;
  height: 4px;
  background-color: white;
}

::-webkit-scrollbar-thumb {
  border-radius: 16px;
  background-color: #efefef;
}

.sbm {
  .van-cell__value {
    display: flex;
    align-items: center;
  }
}
</style>
