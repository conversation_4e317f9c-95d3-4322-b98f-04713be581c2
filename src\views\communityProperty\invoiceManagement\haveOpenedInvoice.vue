<template>
  <!-- 已开发票 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="" prop="enterpriseName">
          <el-select
            style="width: 260px"
            v-model="listQuery.enterpriseName"
            filterable
            clearable
            placeholder="开票户"
            @change="enterpriseNameChange"
          >
            <el-option v-for="item in houseTypeList" :key="item.id" :label="item" :value="item"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native="getList" placeholder="业主姓名/业主手机号" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList">搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="申请时间" align="center" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开票业主" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开票户" align="center" width="250">
          <template slot-scope="scope">
            <span>{{ scope.row.enterpriseName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发票抬头" align="center" width="250">
          <template slot-scope="scope">
            <span>{{ scope.row.invoiceTitle }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发票金额" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发票性质" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.invoiceNature == 0 ? '电子发票' : '纸质发票' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开票时间" align="center" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.auditTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="infoItem(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal="false" :title="dlgTitle" :visible.sync="dlgShow" width="900px" append-to-body>
      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <div class="infoBox">
          <div style="width: 400px">
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              费用主体:<span class="dlgKey">{{ dlgData.roomName }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              发票性质:<span class="dlgKey">{{ dlgData.invoiceNature == 0 ? '电子发票' : '纸质发票' }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              发票类型:<span class="dlgKey">{{ dlgData.invoiceType == 0 ? '普通发票' : '增值税专用发票' }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              税号:<span class="dlgKey">{{ dlgData.dutyParagraph }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              基本开户账号:<span class="dlgKey">{{ dlgData.bankNum }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              固定电话:<span class="dlgKey">{{ dlgData.telephone }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              开票时间:<span class="dlgKey">{{ dlgData.auditTime }}</span>
            </div>
          </div>
          <div style="width: 300px">
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              业主姓名:<span class="dlgKey">{{ dlgData.userName }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              发票金额:<span class="dlgKey">{{ dlgData.amount }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              抬头:<span class="dlgKey">{{ dlgData.invoiceTitle }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              开户行名称:<span class="dlgKey">{{ dlgData.bankName }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              地址:<span class="dlgKey">{{ dlgData.address }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              开票人:<span class="dlgKey">{{ dlgData.userName }}</span>
            </div>
          </div>
          <div style="width: 450px">
            <div style="margin: 0 100px 30px 0; font-weight: bolder">
              业主手机号:<span class="dlgKey">{{ dlgData.phone }}</span>
            </div>
            <div style="margin: 0 100px 30px 0; font-weight: bolder">
              开票户:<span class="dlgKey">{{ dlgData.enterpriseName }}</span>
            </div>
          </div>
        </div>
        <el-form-item label="备注" class="mt20">
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.auditRemark"
            placeholder="请输入备注"
            :disabled="true"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
      </div>
    </el-dialog>
    <memberDlg />
  </div>
</template>

<script>
import { saveOrU, roomTypePage, communityPage, roomTypeDel, importAll } from '@/api/communityMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import memberDlg from '@/components/Dialog/communityMan/memberDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'
import { payInvoicePage } from '@/api/invoiceManagement'

let dlgDataEmpty = {
  projectId: '',
  id: '',
  communityName: '',
  mid: '',
  //   remark: "",
  name: '',
  communityId: '',
}

export default {
  name: 'roomInfo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    memberDlg,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false,
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        name: [{ required: true, message: '请输入房屋类型', trigger: 'change' }],
        communityId: [{ required: true, message: '请选择小区', trigger: 'change' }],
        mid: [{ required: true, message: '请输入商户号', trigger: 'change' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgDataOld: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        status: '1,2',
        enterpriseName: '',
      },
      houseTypeList: [],
      userInfo: {},
      selectRow: {},
    }
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.dlgData.projectId = this.userInfo.projectId
    if (this.$route.query.communityId) {
      this.listQuery.communityId = parseInt(this.$route.query.communityId)
    }
    this.getList()
    // this.getCommunityList();
    this.getRoomType()
  },

  methods: {
    enterpriseNameChange() {
      let enterpriseName
      if (this.dlgShow) {
        enterpriseName = this.dlgData.enterpriseName
        this.dlgData.floorId = ''
        this.dlgData.unitId = ''
      } else {
        enterpriseName = this.listQuery.enterpriseName
        this.listQuery.floorId = ''
        this.listQuery.unitId = ''
      }
      this.getList(enterpriseName)
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },
    // 获取房屋类型列表
    getRoomType() {
      let postParam = {
        page: 1,
        limit: 200,
      }
      roomTypePage(postParam).then((res) => {
        if (res.data.code == 200) {
          if (this.dlgShow) {
            this.houseTypeList = res.data.data
          } else {
            let arr = []
            for (let i of res.data.data) {
              arr.push(i.enterpriseName)
              arr = Array.from(new Set(arr))
              this.houseTypeList = arr
            }
            for (let i = 0; i < this.houseTypeList.length; i++) {
              if (this.houseTypeList[i] == '' || this.houseTypeList[i] == null || typeof this.houseTypeList[i] == 'undefined') {
                this.houseTypeList.splice(i, 1)
                i = i - 1
              }
            }
            console.log(this.houseTypeList, this.houseTypeList)
            return this.houseTypeList
          }
        }
      })
    },

    // communityChange() {
    //   let communityId;
    //   if (this.dlgShow) {
    //     communityId = this.dlgData.communityId;
    //     this.dlgData.floorId = "";
    //     this.dlgData.unitId = "";
    //   } else {
    //     communityId = this.listQuery.communityId;
    //     this.listQuery.floorId = "";
    //     this.listQuery.unitId = "";
    //   }
    //   this.getList(communityId);
    // },

    // // 获取小区列表
    // getCommunityList() {
    //   let postParam = {
    //     page: 1,
    //     limit: 200,
    //   };
    //   communityPage(postParam).then((res) => {
    //     if (res.data.code == 200) {
    //       this.communityList = res.data.data;
    //     }
    //   });
    // },

    // 获取数据
    getList() {
      this.list = []
      this.listOld = []
      this.listQuery.projectId = this.userInfo.projectId
      payInvoicePage(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.data
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 详情
    infoItem(data) {
      this.dlgTitle = '详情'
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgDataOld = JSON.parse(JSON.stringify(data))
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.dlgKey {
  font-weight: normal;
  display: inline-block;
  margin-left: 5px;
  font-size: 13px;
}
.infoBox {
  display: flex;
  justify-content: space-between;
}
</style>


