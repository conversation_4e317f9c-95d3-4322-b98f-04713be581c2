<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区：">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规则名称：">
          <el-input @keyup.enter.native='getList' placeholder='请输入规则名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>添加</el-button>
        <!-- <el-button icon="el-icon-download" size="mini" type="primary" @click="exportExcel">
          Excel导出
        </el-button>
        <el-upload class="upload-wrap" action="" :before-upload="uploadItem">
          <el-button icon="el-icon-upload" size="mini" type="primary">
            Excel导入
          </el-button>
        </el-upload>
        <el-button icon="el-icon-download" type="primary" size="mini" @click="downloadItem()">
          模板下载
        </el-button> -->
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="滞纳金名称">
          <template slot-scope="scope">
            <span>{{ scope.row.discountName	 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="滞纳金类型">
          <template slot-scope="scope">
            <span>{{ scope.row.discountTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="规则名称">
          <template slot-scope="scope">
            <span>{{ scope.row.ruleTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row, 'EDIT')">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑滞纳金设置" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :disabled="dlgType == 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="所在小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="滞纳金名称" prop="discountName">
          <el-input v-model="dlgData.discountName	" placeholder="请输入滞纳金名称" />
        </el-form-item>

        <el-form-item label="滞纳金类型" prop="discountType">
          <el-select v-model="dlgData.discountType" filterable clearable placeholder="请选择滞纳金类型">
            <el-option v-for="item in discountTypeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="规则" prop="ruleType">
          <el-select v-model="dlgData.ruleType" filterable clearable placeholder="请选择滞纳金规则">
            <el-option v-for="item in ruleTypeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="滞纳金比例" prop="unitAmount">
          <el-input-number v-model="dlgData.unitAmount" :controls='false' :min="0" :precision="6" :max="1" :step="1"></el-input-number> (请填写滞纳金0~1之间)
        </el-form-item>

        <el-form-item label="描述">
          <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.discountDesc" placeholder="请输入描述" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'
import { feeDiscountPage, addOrUpdateFeeDiscount, delFeeDiscountById } from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  communityId: '',
  communityName: '',
  discountType: '',
  discountTypeText: '',
  ruleType: '',
  ruleTypeText: '',
  unitAmount: undefined,
  discountName: '',
  discountDesc: '',
}


export default {
  name: 'discountSetup',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        discountType: [{ required: true, message: '必填字段', trigger: 'change' }],
        unitAmount: [{ required: true, message: '必填字段', trigger: 'blur' }],
        ruleType: [{ required: true, message: '必填字段', trigger: 'change' }],
        discountName: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgDataOld: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        discountType: 2
      },
      communityList: [],
      userInfo: {},

      ruleTypeList: [
        {
          id: 1,
          name: '滞纳金（按月）'
        },
        {
          id: 2,
          name: '滞纳金（按天）'
        },
      ],

      discountTypeList: [
        {
          id: 2,
          name: '违约'
        }
      ],
    }
  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/workade/kaoqinyuebaodaochu?` + param
      window.open(sendUrl)
    },

    // 下载
    downloadItem () {
      let url =
        'https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E7%97%85%E7%A7%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xls'
      window.open(url)
    },

    // 上传
    uploadItem (file) {
      let name = file.name.split('.')
      let suffix = name[name.length - 1]

      if (suffix !== 'xls' && suffix !== 'xlsx') {
        this.$message({
          type: 'warning',
          message: '只能上传xls/xlsx文件'
        })
        return false
      }

      let loading = this.$loading({
        lock: true,
        text: '导入中',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      let postParam = {
        file
      }

      importExcelDisease(postParam).then(res => {
        loading.close()
        if (res.data.code == 200) {
          this.$message.success('导入成功')
          this.getList()
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })

      return false
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    formatList () {

    },

    // 获取数据
    getList () {
      // if (utils.isNull(this.listQuery.communityId)) {
      //   this.$message.warning("请选择小区")
      //   return
      // }
      this.count++
      this.listLoading = true
      feeDiscountPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page.total ? res.data.page.total : 0
          this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 提交接口
    subAjax () {
      let postParam = JSON.parse(JSON.stringify(this.dlgData))
      postParam.projectId = this.userInfo.projectId
      postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
      postParam.discountTypeText = utils.getNameById(postParam.discountType, this.discountTypeList)
      postParam.ruleTypeText = utils.getNameById(postParam.ruleType, this.ruleTypeList)
      this.dlgLoading = true
      addOrUpdateFeeDiscount(postParam).then(res => {
        this.dlgLoading = false
        if (res.data.code == 200) {
          this.getList()
          this.dlgShow = false
          this.$message.success(res.data.msg)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          if (this.dlgType == 'EDIT') {
            if (this.dlgData.unitAmount == this.dlgDataOld.unitAmount) {
              this.subAjax()
            } else {
              const h = this.$createElement;
              this.$msgbox({
                title: '房屋信息改动',
                message: h('div', { class: 'confirmTip' }, [
                  h('p', { class: 'fwarning' }, '原滞纳金比例:' + this.dlgDataOld.unitAmount),
                  h('p', { class: 'fsuccess' }, '更改后滞纳金比例:' + this.dlgData.unitAmount),
                  h('p', null, '修改滞纳金比例后，新产生计费会有变化，是否继续?'),
                ]),
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                beforeClose: (action, instance, done) => {
                  if (action === 'confirm') {
                    this.subAjax()
                    done();
                  } else {
                    done();
                  }
                }
              })
            }
          } else {
            this.subAjax()
          }
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgDataOld = JSON.parse(JSON.stringify(this.dlgData))
      this.dlgType = type
      this.dlgShow = true
    },


    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delFeeDiscountById(data.id, flag).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


