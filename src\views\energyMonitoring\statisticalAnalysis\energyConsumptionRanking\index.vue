<template>
  <div class="app-container mazhenguo" style="margin-bottom: 32px; padding-top: 6px; padding-bottom: 10px">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="m-tabs">
      <el-tab-pane v-for="(item, index) of navList" :key="index" :label="item.label" :name="index + ''"> </el-tab-pane>
    </el-tabs>
    <components :is="testTemplate"></components>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// 工具
import * as utils from '@/utils'

import tab1 from './regional'
import tab2 from './device'

export default {
  components: {
    tab1,
    tab2,
  },

  // props: {},
  data() {
    return {
      navList: [
        { id: '1', label: '按区域', template: tab1 },
        { id: '2', label: '按设备', template: tab2 },

      ],
      activeName: '0',
      testTemplate: tab1,
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleClick(tab, event) {
      console.log('tab', tab)
      let index = parseInt(tab.index)
      this.activeName = tab.index + ''
      this.testTemplate = this.navList[index].template
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>


