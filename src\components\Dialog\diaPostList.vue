<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog
      :title="diaType1 == 'user' ? '选择员工' : '选择岗位'"
      top="30px"
      :close-on-click-modal="false"
      :visible.sync="diaPostState"
      width="1200px"
      append-to-body
    >
      <div>
        <div class="m-page-search">
          <el-input
            class="m-shaixuan-input fl"
            style="width: 200px"
            @keyup.enter.native="searchFunc"
            placeholder="岗位名称/在岗员工/岗位编码"
            v-model="listQuery.label"
          >
            <i @click="resetSearchItem(['label'])" slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>

          <el-radio-group
            class="fl ml10"
            @change="jdrChangeFunc"
            style="margin-top: 6px"
            v-if="diaType2 == 'bsgl-paigong'"
            v-model="pgState"
          >
            <el-radio :label="1">范围内</el-radio>
            <el-radio :label="2">范围外</el-radio>
          </el-radio-group>

          <!-- form_gwxxbgd 岗位信息变更单，选择同一部门 -->
          <el-input
            v-if="diaType2 != 'bsgl-paigong'"
            class="fl ml10"
            :title="listQuery.branchName"
            v-model="listQuery.branchName"
            @focus="showBmTree"
            placeholder="选择部门"
            style="width: 160px"
            :disabled="diaType2 == 'form_gwxxbgd'"
            readonly
          >
            <i @click="resetSearchItem(['branchId', 'branchName'])" slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-cascader
            class="fl ml10"
            v-if="diaType1 == 'user' && diaType2 == 'zd'"
            :props="{ multiple: true }"
            collapse-tags
            style="width: 160px"
            placeholder="选择职等"
            clearable
            v-model="listQuery.rankId"
            :options="rankSelect"
            @change="rankChange"
          ></el-cascader>
          <el-button class="search-right-btn fl ml10" icon="el-icon-search" type="success" size="mini" @click="searchFunc">搜索</el-button>
          <el-button
            class="search-right-btn fl ml10"
            v-if="diaType1 == 'user' && diaType2 == 'zd'"
            icon="el-icon-search"
            type="danger"
            size="mini"
            @click="selectAllFunc"
            >按职等全选岗位</el-button
          >
          <!-- @show="showPopover" -->
          <el-popover class="fl popDom" placement="bottom" width="800" trigger="click">
            <el-table ref="returnListRef" style="overflow: auto" max-height="400" :data="diaPostList">
              <el-table-column type="index" width="50" align="center"></el-table-column>
              <el-table-column :label="diaType1 == 'post' ? '岗位名称' : '员工姓名'">
                <template slot-scope="scope">
                  <div v-if="diaType1 == 'post'">
                    {{ scope.row.label }}<span>({{ scope.row.userName || '空岗' }})</span>
                  </div>
                  <span v-else>{{ scope.row.label }}</span>
                </template>
              </el-table-column>
              <el-table-column label="所属部门">
                <template slot-scope="scope">
                  <span>{{ scope.row.branchName }}</span>
                </template>
              </el-table-column>

              <el-table-column property="" label="操作" width="60" align="center">
                <template slot-scope="scope">
                  <el-button @click="popRemoveRow(scope.row)" type="danger" size="mini" icon="el-icon-delete" plain></el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button class="fr search-right-btn" type="primary" slot="reference" icon="el-icon-arrow-down">查看已选</el-button>
          </el-popover>
          <div class="clear"></div>
        </div>

        <!-- 多选 -->
        <el-table
          v-if="diaMulState"
          :key="diaKey"
          ref="multipleTable"
          v-loading="listLoading"
          :data="list"
          border
          fit
          highlight-current-row
          max-height="600px"
          class="m-small-table mt10"
          @select="tableSelectChange"
          @select-all="tableSelectAll"
          show-overflow-tooltip="true"
        >
          <el-table-column align="center" type="selection" width="55" fixed> </el-table-column>
          <el-table-column label="序号" prop="index" align="center" width="60" fixed>
            <template slot-scope="scope">
              <span>{{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="diaType1 == 'user'" label="员工姓名" width="90" align="center" fixed>
            <template slot-scope="scope">
              <span>{{ scope.row.label }}</span>
            </template>
          </el-table-column>

          <el-table-column v-else label="岗位名称" width="180" fixed>
            <template slot-scope="scope">
              <span>{{ scope.row.label }}({{ scope.row.userName || '空岗' }})</span>
            </template>
          </el-table-column>

          <el-table-column label="所属部门" width="180">
            <template slot-scope="scope">
              <span>{{ scope.row.branchName }}</span>
            </template>
          </el-table-column>

          <template v-if="diaType1 == 'post'">
            <el-table-column label="运营部" prop="branchOperationName" width="180"> </el-table-column>
            <el-table-column label="项目部" prop="branchProjectName" width="180"> </el-table-column>
            <el-table-column label="岗位详情1" prop="postInfoFirst" width="180"> </el-table-column>
            <el-table-column label="岗位详情2" prop="postInfoSecond" width="180"> </el-table-column>
            <el-table-column label="岗位详情3" prop="postInfoThird" width="180"> </el-table-column>
            <el-table-column label="岗位编码" prop="postCode" align="center"> </el-table-column>
            <el-table-column label="岗位工资" prop="postSalary" width="90" align="center"> </el-table-column>
            <el-table-column label="工作时间" prop="workTimeStr" width="100"> </el-table-column>
          </template>

          <el-table-column v-if="diaType1 == 'post' && diaType2 == 'zd'" label="职等" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.rankName }}</span>
            </template>
          </el-table-column>
          <template v-if="diaType1 == 'user'">
            <el-table-column label="岗位" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.postName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="岗位编码" width="100" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.postCode }}</span>
              </template>
            </el-table-column>
            <el-table-column label="岗位详情1" width="180" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.postInfoFirst }}</span>
              </template>
            </el-table-column>
            <el-table-column label="岗位详情2" width="180" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.postInfoSecond }}</span>
              </template>
            </el-table-column>
            <el-table-column label="岗位详情3" width="180" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.postInfoThird }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>

        <!-- 单选 -->
        <el-table
          v-if="!diaMulState"
          :key="diaKey"
          ref="userDom"
          v-loading="listLoading"
          :data="list"
          border
          fit
          highlight-current-row
          max-height="367px"
          class="m-small-table mt10"
          @row-click="rowClick"
          show-overflow-tooltip="true"
        >
          <el-table-column label="" align="center" width="55" fixed>
            <template slot-scope="scope">
              <el-radio v-if="diaType1 == 'post'" v-model="selectId" :label="scope.row.postId" style="width: 16px"><span></span></el-radio>
              <el-radio v-if="diaType1 == 'user'" v-model="selectId" :label="scope.row.id" style="width: 16px"><span></span></el-radio>
            </template>
          </el-table-column>

          <el-table-column label="序号" prop="index" align="center" width="60" fixed>
            <template slot-scope="scope">
              <span>{{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column :label="diaType1 == 'user' ? '员工姓名' : '岗位名称'">
            <template slot-scope="scope">
              <div v-if="diaType1 == 'post'">
                {{ scope.row.label }}<span>({{ scope.row.userName || '空岗' }})</span>
              </div>
              <span v-else>{{ scope.row.label }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所属部门"  width="180">
            <template slot-scope="scope">
              <span>{{ scope.row.branchName }}</span>
            </template>
          </el-table-column> -->

          <el-table-column v-if="diaType1 == 'user'" label="员工姓名" width="90" align="center" fixed>
            <template slot-scope="scope">
              <span>{{ scope.row.label }}</span>
            </template>
          </el-table-column>

          <el-table-column v-else label="岗位名称" width="180" fixed>
            <template slot-scope="scope">
              <span>{{ scope.row.label }}({{ scope.row.userName || '空岗' }})</span>
            </template>
          </el-table-column>

          <el-table-column label="所属部门" width="180">
            <template slot-scope="scope">
              <span>{{ scope.row.branchName }}</span>
            </template>
          </el-table-column>
          <template v-if="diaType1 == 'post'">
            <el-table-column label="运营部" prop="branchOperationName" width="180"> </el-table-column>
            <el-table-column label="岗位详情1" prop="postInfoFirst" width="180"> </el-table-column>
            <el-table-column label="岗位详情2" prop="postInfoSecond" width="180"> </el-table-column>
            <el-table-column label="岗位详情3" prop="postInfoThird" width="180"> </el-table-column>
            <el-table-column label="岗位编码" prop="postCode"> </el-table-column>
            <el-table-column label="岗位工资" prop="postSalary" width="90" align="center"> </el-table-column>
            <el-table-column label="工作时间" prop="workTimeStr" width="100"> </el-table-column>
          </template>

          <el-table-column v-if="diaType1 == 'post' && diaType2 == 'zd'" label="职等" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.rankName }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="diaType1 == 'user'" label="岗位">
            <template slot-scope="scope">
              <span>{{ scope.row.postName }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
        <div class="clear"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon="el-icon-back">取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
      <Bmtree />
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

// 数据接口
import {
  findPostByDynamic, // 获取岗位
  findRankInfoAll, // 获取职等列表
  findAllRankByPost, // 按职等查岗位
  findUserInfoByRankIds, // 查 员工
  findOrgBranchPostByCondition, // 特殊考勤查岗位
} from '@/api/postMan' // 查岗位
import {
  findUserByLabelAndNum, // 查所有员工
} from '@/api/staffMan'
import {
  findProjectPostPage, // 报事-范围内岗位
} from '@/api/reportMan'

// 页面组件
import Pagination from '@/components/Pagination'
import Bmtree from '@/components/Dialog/Bmtree' // 部门弹窗

let listQueryEmpty = {
  label: '',
  page: 1,
  size: 10,

  branchId: '',
  branchName: '',
  branchType: '0',
  kqzId: '',
  kqzName: '',
  postJob: '', // 职务

  rankId: '', // 职等id
}

let listQueryUser = {
  labelNum: '',
  page: 1,
  size: 10,
  rankIds: '', // 职等id,可多选
}

export default {
  components: {
    Pagination,
    Bmtree,
  },
  data() {
    return {
      cascaderProps: { multiple: true },
      // 列表多选
      tableSelectList: [], // 当前页复选数据
      diaPostList: [], // 所选数据集合
      diaPostQuery: '', // 查询

      // 列表查询
      list: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      otherQuery: '',

      // 职等下拉框
      rankSelect: [],

      diaType1: 'post',
      diaType2: '',
      diaKey: 0,
      diaMulState: false, // 是否多选

      // 单选
      selectId: '', // 单选行

      // 报事派工
      pgState: 1,
    }
  },
  computed: {
    ...mapGetters([
      // 部门树
      'bmTreeBranchId',
      'bmTreeBranchName',
    ]),
    diaPostState: {
      get: function () {
        let state = this.$store.getters.diaPostState
        if (state === true) {
          this.getRankSelect()
          setTimeout(() => {
            // 搜索数据
            this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
            this.diaMulState = this.$store.getters.diaPostMul

            let diaType = this.$store.getters.diaPostType
            this.diaType1 = diaType.split('-*-')[0]
            this.diaType2 = diaType.split('-*-')[1]

            if (this.diaType2 == 'bsgl-paigong') {
              this.pgState = 1
            }

            console.log(this.diaType1, this.diaType2)
            let otherQuery = this.$store.getters.diaPostQuery
            if (otherQuery) {
              this.otherQuery = JSON.parse(otherQuery)
            }

            // 选中数据
            if (this.$store.getters.diaPostGet) {
              this.diaPostList = JSON.parse(this.$store.getters.diaPostGet)
              console.log('diaPostList入值', this.diaPostList)
            } else {
              this.selectId = ''
              if (this.$store.getters.diaPostSel) {
                this.selectId = this.$store.getters.diaPostSel
              }
              this.diaPostList = []
            }

            this.getList()
            this.diaKey++
          }, 50)
        }
        return state
      },
      set: function (newVal) {
        this.$store.commit('SET_DIAPOST_STATE', newVal)
      },
    },
  },
  watch: {
    // 部门树状态
    bmTreeBranchId(val) {
      if (val === 'empty') {
        return false
      }
      this.listQuery.branchId = val
      this.listQuery.page = 1

      setTimeout(() => {
        if (this.diaPostState) {
          this.getList()
        }
      }, 50)
    },
    bmTreeBranchName(val) {
      console.log('branchName', val)
      if (val === 'empty') {
        return false
      }
      this.listQuery.branchName = val
      this.listQuery.page = 1
    },
  },
  created() {},
  methods: {
    // << 单选
    rowClick(row, column, event) {
      let diaPostList = [row]
      this.diaPostList = JSON.parse(JSON.stringify(diaPostList))

      if (this.diaType1 == 'user') {
        this.selectId = row.id
      } else if (this.diaType1 == 'post') {
        this.selectId = row.postId
      }
    },
    // >> 单选

    // << 表格 复选事件
    tableSelectChange(selection, row) {
      // 判断是否存在，存在则删除，不存在则增加
      let diaPostList = JSON.parse(JSON.stringify(this.diaPostList))
      let isHas = false
      let index = ''
      if (this.diaType1 == 'user') {
        for (let i = 0; i < this.diaPostList.length; i++) {
          if (row.id == this.diaPostList[i].id) {
            index = i
            isHas = true
            break
          }
        }
      } else if (this.diaType1 == 'post') {
        for (let i = 0; i < this.diaPostList.length; i++) {
          if (row.postId == this.diaPostList[i].postId) {
            index = i
            isHas = true
            break
          }
        }
      }

      if (isHas) {
        diaPostList.splice(index, 1)
      } else {
        diaPostList.push(row)
      }
      this.diaPostList = JSON.parse(JSON.stringify(diaPostList))
    },
    // 按职等全选岗位
    selectAllFunc() {
      if (this.listQuery.rankId == '') {
        this.$message({
          type: 'warning',
          message: '请选择职等',
        })
        return false
      }
      let tishi = this.diaType1 == 'user' ? '员工' : '岗位'

      this.$confirm(`是否快速选择该职等下所有${tishi}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (this.diaType1 == 'post') {
          findAllRankByPost(this.listQuery.rankId).then((res) => {
            this.listLoading = false
            let code = res.data.code
            let msg = res.data.msg
            if (code == '200') {
              let list = res.data.list
              for (let item of list) {
                item.postName = item.label
                let isHas = this.diaPostList.some((item2) => {
                  return item2.postId == item.postId
                })
                console.log('isHas', isHas)
                if (!isHas) {
                  this.diaPostList.push(item)
                }
              }
              setTimeout(() => {
                for (let item of this.list) {
                  let isHas = this.diaPostList.some((item2) => {
                    return item.postId == item2.postId
                  })
                  if (isHas) {
                    this.$refs.multipleTable.toggleRowSelection(item, true)
                  }
                }
              }, 50)
            } else {
              this.$message.error(msg)
            }
          })
        } else {
          let sendObj = {
            labelNum: '',
            page: 1,
            size: 9999,
            rankIds: this.listQuery.rankIds || '', // 职等id,可多选
            branchId: '',
          }

          findUserInfoByRankIds(sendObj).then((res) => {
            this.listLoading = false
            let code = res.data.code
            let msg = res.data.msg
            if (code == '200') {
              let list = res.data.list
              for (let item of list) {
                item.postName = item.label
                let isHas = this.diaPostList.some((item2) => {
                  return item2.id == item.id
                })
                console.log('isHas', isHas)
                if (!isHas) {
                  this.diaPostList.push(item)
                }
              }
              setTimeout(() => {
                for (let item of this.list) {
                  let isHas = this.diaPostList.some((item2) => {
                    return item.id == item2.id
                  })
                  if (isHas) {
                    this.$refs.multipleTable.toggleRowSelection(item, true)
                  }
                }
              }, 50)
            } else {
              this.$message.error(msg)
            }
          })
        }
      })
    },
    // 全选
    tableSelectAll(selection) {
      // 全选-selection.length!=0
      let diaPostList = JSON.parse(JSON.stringify(this.diaPostList))
      if (selection.length != 0) {
        let selectIndexArr = []
        // 全选
        if (this.diaType1 == 'user') {
          for (let i = 0; i < selection.length; i++) {
            let isHas = diaPostList.some((item) => {
              return selection[i].id == item.id
            })
            !isHas && selectIndexArr.push(i)
          }
        } else if (this.diaType1 == 'post') {
          for (let i = 0; i < selection.length; i++) {
            let isHas = diaPostList.some((item) => {
              return selection[i].postId == item.postId
            })
            !isHas && selectIndexArr.push(i)
          }
        }
        for (let index of selectIndexArr) {
          diaPostList.push(selection[index])
        }
        this.diaPostList = JSON.parse(JSON.stringify(diaPostList))
      } else {
        // 取消全选
        let removeIndexArr = []

        if (this.diaType1 == 'user') {
          for (let i = 0; i < diaPostList.length; i++) {
            let isHas = this.list.some((item) => {
              return diaPostList[i].id == item.id
            })
            isHas && removeIndexArr.push(i)
          }
        } else if (this.diaType1 == 'post') {
          for (let i = 0; i < diaPostList.length; i++) {
            let isHas = this.list.some((item) => {
              return diaPostList[i].postId == item.postId
            })
            isHas && removeIndexArr.push(i)
          }
        }

        removeIndexArr.sort(function (a, b) {
          return a - b
        })
        removeIndexArr = removeIndexArr.reverse()
        for (let index of removeIndexArr) {
          diaPostList.splice(index, 1)
        }
        this.diaPostList = JSON.parse(JSON.stringify(diaPostList))
      }
    },
    // 已选删除
    popRemoveRow(row) {
      // 选择商品弹窗，复选框同步改变
      if (this.diaType1 == 'post') {
        let diaPostList = this.diaPostList.filter((item) => {
          return item.postId != row.postId
        })
        this.diaPostList = JSON.parse(JSON.stringify(diaPostList))
        for (let item of this.list) {
          let isHas = this.diaPostList.some((item2) => {
            return item2.postId == item.postId
          })
          if (isHas) {
            this.$refs.multipleTable.toggleRowSelection(item, true)
          } else {
            this.$refs.multipleTable.toggleRowSelection(item, false)
          }
        }
      } else if (this.diaType1 == 'user') {
        let diaPostList = this.diaPostList.filter((item) => {
          return item.id != row.id
        })
        this.diaPostList = JSON.parse(JSON.stringify(diaPostList))
        for (let item of this.list) {
          let isHas = this.diaPostList.some((item2) => {
            return item2.id == item.id
          })
          if (isHas) {
            this.$refs.multipleTable.toggleRowSelection(item, true)
          } else {
            this.$refs.multipleTable.toggleRowSelection(item, false)
          }
        }
      }
    },
    // >> 表格 复选事件
    // 选职等change
    rankChange(val) {
      if (val.length == 0) {
        this.listQuery.rankIds = ''
      }
      console.log('rankChange', val)
      let rankId = []
      for (let item of val) {
        rankId.push(item[1])
      }
      this.listQuery.rankIds = rankId.join(',')
      this.getList()
    },

    // 报事 接单人 change
    jdrChangeFunc() {
      ;(this.listQuery.label = ''), (this.listQuery.page = 1)
      this.getList()
    },

    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    // 获取数据
    getList() {
      this.list = []
      this.listLoading = true

      console.log('diaType1', this.diaType1)

      if (this.diaType1 == 'post') {
        let formList = [
          'form_tskqd',
          'form_qxtskqd',
          'form_gwqy',
          'form_gwty',
          'form_jgd',
          'form_gwtxd',
          'form_rzsqd',
          'form_tgtxd',
          'form_jgsqd',
          'agent',
          'form_gwxxbgd',
        ]
        if (formList.includes(this.diaType2)) {
          // 特殊考勤单, 取消特殊考勤单 - 选岗位
          console.log('this.listQuery', this.listQuery)
          let sqType = ''
          if (this.diaType2 === 'form_qxtskqd') {
            sqType = 'quxiaoteshukaoqin'
          } else if (this.diaType2 === 'form_gwqy') {
            sqType = 'gangweiqiyong'
          } else if (this.diaType2 === 'form_gwty') {
            sqType = 'gangweitingyong'
          } else if (this.diaType2 === 'form_jgd') {
            sqType = 'jiangangshenqing'
          } else if (this.diaType2 === 'form_gwtxd') {
            sqType = 'gangweitiaoxindan'
          } else if (this.diaType2 === 'form_rzsqd') {
            sqType = 'ruzhishenqing'
          } else if (this.diaType2 === 'form_tgtxd') {
            sqType = 'tiaogangtiaoxin'
          } else if (this.diaType2 === 'form_jgsqd') {
            sqType = 'jiangangshenqinga'
          } else if (this.diaType2 === 'form_gwxxbgd') {
            // 岗位信息变更
            sqType = ''
            this.listQuery.branchId = this.otherQuery.branchId
            this.listQuery.branchName = this.otherQuery.branchName
          }
          let sendObj = {
            brId: this.listQuery.branchId, // 2068
            page: this.listQuery.page,
            size: this.listQuery.size,
            label: this.listQuery.label,
            sqType,
          }
          if (sqType === 'quxiaoteshukaoqin') {
            sendObj.teshukaoqinType = this.$store.getters.diaPostQuery
          }
          findOrgBranchPostByCondition(sendObj).then((res) => {
            this.listLoading = false
            let code = res.data.code
            let msg = res.data.msg
            if (code == '200') {
              let data = res.data.data
              let list = res.data.list
              if (data == null) {
                this.total = 0
                this.list = []
                return false
              }

              this.total = data.total
              this.list = JSON.parse(JSON.stringify(list))

              if (this.diaMulState) {
                setTimeout(() => {
                  for (let item of this.list) {
                    let isHas = this.diaPostList.some((item2) => {
                      return item.postId == item2.postId
                    })
                    console.log('isHas', isHas)
                    if (isHas) {
                      this.$refs.multipleTable.toggleRowSelection(item, true)
                    }
                  }
                }, 50)
              } else {
                if (this.selectId) {
                  for (let i of this.list) {
                    if (i.postId == this.selectId) {
                      this.diaPostList = [i]
                      break
                    }
                  }
                }
              }
            } else {
              this.$message.error(msg)
            }
          })
        } else if (this.diaType2 == 'bsgl-paigong' && this.pgState == 1) {
          let sendObj = {
            projectId: this.otherQuery.projectId,
            label: this.listQuery.label,
            page: this.listQuery.page,
            limit: this.listQuery.size,
            status: 2,
            isArrival: this.otherQuery.isArrival,
            arrivalTime: this.otherQuery.arrivalTime,
          }
          findProjectPostPage(sendObj).then((res) => {
            this.listLoading = false
            let code = res.data.code
            let msg = res.data.msg
            if (code == '200') {
              let data = res.data.page
              let list = res.data.data
              if (data == null) {
                this.total = 0
                this.list = []
                return false
              }

              this.total = data.total
              this.list = JSON.parse(JSON.stringify(list))

              if (this.diaMulState) {
                setTimeout(() => {
                  for (let item of this.list) {
                    let isHas = this.diaPostList.some((item2) => {
                      return item.postId == item2.postId
                    })
                    console.log('isHas', isHas)
                    if (isHas) {
                      this.$refs.multipleTable.toggleRowSelection(item, true)
                    }
                  }
                }, 50)
              }
            } else {
              this.$message.error(msg)
            }
          })
        } else {
          let sendObj = JSON.parse(JSON.stringify(this.listQuery))
          if (
            this.diaType2 == 'departmentMan' ||
            this.diaType2 == 'flag0' ||
            this.diaType2 == 'dispatchConfig' ||
            this.diaType2 == 'bsgl-paigong'
          ) {
            sendObj.flag = 0
          }
          if (this.diaType2 == 'bsgl-paigong') {
            sendObj.postStatus = 2
          }
          findPostByDynamic(sendObj).then((res) => {
            this.listLoading = false
            let code = res.data.code
            let msg = res.data.msg
            if (code == '200') {
              let data = res.data.data
              let list = res.data.list
              if (data == null) {
                this.total = 0
                this.list = []
                return false
              }

              this.total = data.total
              this.list = JSON.parse(JSON.stringify(list))

              if (this.diaMulState) {
                setTimeout(() => {
                  for (let item of this.list) {
                    let isHas = this.diaPostList.some((item2) => {
                      return item.postId == item2.postId
                    })
                    console.log('isHas', isHas)
                    if (isHas) {
                      this.$refs.multipleTable.toggleRowSelection(item, true)
                    }
                  }
                }, 50)
              }
            } else {
              this.$message.error(msg)
            }
          })
        }
      } else if (this.diaType1 == 'user') {
        if (this.diaType2 == 'zd') {
          let sendObj = {
            labelNum: this.listQuery.label,
            page: this.listQuery.page,
            size: this.listQuery.size,
            rankIds: this.listQuery.rankIds || '', // 职等id,可多选
            branchId: this.listQuery.branchId,
          }
          findUserInfoByRankIds(sendObj).then((res) => {
            this.listLoading = false
            let code = res.data.code
            let msg = res.data.msg
            if (code == '200') {
              let data = res.data.data
              let list = res.data.list
              if (data == null) {
                this.total = 0
                this.list = []
                return false
              }
              this.total = data.total
              this.list = JSON.parse(JSON.stringify(list))
              if (this.diaMulState) {
                setTimeout(() => {
                  for (let item of this.list) {
                    let isHas = this.diaPostList.some((item2) => {
                      return item.id == item2.id
                    })
                    console.log('isHas', isHas)
                    if (isHas) {
                      this.$refs.multipleTable.toggleRowSelection(item, true)
                    }
                  }
                  // this.diaKey++
                }, 50)
              }
            } else {
              this.$message.error(msg)
            }
          })
        } else {
          console.log(111, this.listQuery)
          let sendObj = {
            labelNum: this.listQuery.label,
            page: this.listQuery.page,
            size: this.listQuery.size,

            branchId: this.listQuery.branchId,
            branchName: this.listQuery.branchName,
            branchType: 1,
            kqzId: this.listQuery.kqzId,
            kqzName: this.listQuery.kqzName,
          }

          if (this.diaType2 == 'form_jiaband') {
            sendObj.workStateId = '0,1,2'
          }
          findUserByLabelAndNum(sendObj).then((res) => {
            this.listLoading = false
            let code = res.data.code
            let msg = res.data.msg
            if (code == '200') {
              let data = res.data.data
              let list = res.data.list
              if (data == null) {
                this.total = 0
                this.list = []
                return false
              }

              this.total = data.total
              this.list = JSON.parse(JSON.stringify(list))
              if (this.diaMulState) {
                setTimeout(() => {
                  for (let item of this.list) {
                    let isHas = this.diaPostList.some((item2) => {
                      return item.id == item2.id
                    })
                    if (isHas) {
                      this.$refs.multipleTable.toggleRowSelection(item, true)
                    }
                  }
                }, 50)
              }
            } else {
              this.$message.error(msg)
            }
          })
        }
      }
    },
    // 清空搜索条件
    resetSearchItem(arr) {
      if (this.diaType2 == 'form_gwxxbgd') return false
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.searchFunc()
    },
    // 其他弹窗
    showBmTree() {
      console.log('点击了吗')
      // bmTreeState
      this.$store.commit('SET_BMTREEISROLE', true)
      this.$store.commit('SET_BMTREESTATE', true)
    },

    // << 初始化
    // 获取职等集合
    getRankSelect() {
      findRankInfoAll().then((res1) => {
        let res = res1.data
        if (res.code == 200) {
          let list = res.list
          let dataObj = {}
          for (let item of list) {
            let keyStr = `typeId-${item.typeId}-${item.typeName}`
            if (dataObj[keyStr]) {
              let obj = {
                value: item.id,
                label: item.rankName,
              }
              dataObj[keyStr].push(obj)
            } else {
              let newArr = [
                {
                  value: item.id,
                  label: item.rankName,
                },
              ]
              dataObj[keyStr] = newArr
            }
          }
          console.log('dataObj', dataObj)
          // 拼接职等下拉框数据
          let rankSelect = []
          for (let key in dataObj) {
            let value = key.split('-')[1]
            let label = key.split('-')[2]
            // children
            let obj = {
              value: `type-${key.split('-')[1]}`,
              label: key.split('-')[2],
              children: dataObj[key],
            }
            rankSelect.push(obj)
          }
          console.log('最终的', rankSelect)
          this.rankSelect = rankSelect
        } else {
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },
    // >> 初始化
    // 弹窗提交
    bumenOkFunc() {
      console.log(this.diaPostList)
      if (this.diaType2 == 'dispatchConfig') {
        this.$confirm(`确认提交?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.$store.commit('SET_DIAPOST_GET', '')
          this.$store.commit('SET_DIAPOST_TYPE', 'post')

          setTimeout(() => {
            console.log('JSON.stringify(this.diaPostList)', JSON.stringify(this.diaPostList))
            this.$store.commit('SET_DIAPOST_GET', JSON.stringify(this.diaPostList))
            this.$store.commit('SET_DIAPOST_STATE', false)
            this.closeDialog()
          }, 100)

          setTimeout(() => {
            this.$store.commit('SET_DIAPOST_GET', '')
            this.$store.commit('SET_DIAPOST_TYPE', 'post')
          }, 1000)
        })
      } else {
        this.$store.commit('SET_DIAPOST_GET', JSON.stringify(this.diaPostList))
        setTimeout(() => {
          this.$store.commit('SET_DIAPOST_STATE', false)
          this.$store.commit('SET_DIAPOST_GET', '')
          this.$store.commit('SET_DIAPOST_TYPE', 'post')
        }, 500)
        setTimeout(() => {
          this.closeDialog()
        }, 50)
      }
    },

    // 关闭弹窗
    closeDialog() {
      this.$store.commit('SET_DIAPOST_MUL', true)
      this.$store.commit('SET_DIAPOST_STATE', false)
    },

    // 清空搜索条件
    emptyFunc() {
      this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
      this.getList()
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss">
.user-list {
  height: 400px;
  border: 1px solid red;
  border-radius: 3px;
}
</style>