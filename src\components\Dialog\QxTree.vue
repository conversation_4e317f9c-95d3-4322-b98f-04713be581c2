<template>
  <div class="">
    <!-- 多选部门 -->
    <el-dialog :close-on-click-modal='false' title="设置权限" :visible.sync="qxTreeState" width='600px' top='30px' append-to-body>
      <div class="">

        <el-input placeholder="输入关键字进行过滤" style="margin-bottom: 10px;" v-model="filterBmLeftText">
        </el-input>
        <div class='m-dialog-h'>
          <el-tree :data="treeData" :check-strictly='true' @check='treeCheck' show-checkbox default-expand-all ref="tree" node-key='id' highlight-current :default-checked-keys='defaultSelectKey' :filter-node-method="filterNode" :props="defaultProps">
          </el-tree>
          <!-- <el-tree 
            :data="treeData" 
            ref="treeDom"
            default-expand-all
            :filter-node-method="filterNode"
            @node-click="nodeClick">
          </el-tree> -->
        </div>

      </div>
      <div slot="footer" class="dialog-footer">
        <!-- <span class='dialog-footer-span' v-show='branchName'>当前选中：{{ branchName }}</span> -->
        <el-button @click="closeDialog" icon='el-icon-back'>返回</el-button>

        <el-button type="danger" @click="removeNode" icon='el-icon-delete'>清空</el-button>
        <el-button :loading="btnLoading" type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { setTimeout } from 'timers';
// import adminDashboard from './admin'
// import editorDashboard from './editor'

import {
  findPermissionTreeJYT,  // 获取 权限树
  findPerByRole,  // 获取用户权限
} from '@/api/powermanager'


export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {

      // 部门树
      treeData: [],
      selectKeys: [],  // 选中的节点id集合
      selectNames: [],  // 选中的节点 Name
      checkChildIds: [],  // 选中当前节点下的 id 集合
      childNode: {},  // 选中的当前节点

      btnLoading: false,
      isFirst: false,

      // 树过滤
      defaultProps: {
        children: 'children',
        label: 'label',
      },

      // 返回的 id + name
      backIdName: [],

      // 默认数据
      defaultSelectKey: [],

      roleSelect: [],  // 角色 列表
      roleId: '',  // 选中的角色ID

      filterBmLeftText: ''

    }
  },

  computed: {
    ...mapGetters([
      'qxTreeKeysSet',
      'qxTreeNamesSet',
      'qxTreeIsSelectChild'
    ]),
    qxTreeState: {
      get: function () {

        let state = this.$store.getters.qxTreeState
        if (state) {
          this.roleId = ''
          setTimeout(() => {

            this.$refs.tree.setCheckedKeys([])
            findPermissionTreeJYT().then(res => {
              let code = res.data.code
              let data = res.data.data
              let msg = res.data.msg

              if (code === '200') {
                this.treeData = JSON.parse(JSON.stringify(res.data.list))

                if (this.$store.getters.qxTreeKeysSet) {
                  setTimeout(() => {
                    this.selectKeys = this.$store.getters.qxTreeKeysSet.split(',')
                    this.defaultSelectKey = this.$store.getters.qxTreeKeysSet.split(',')

                    // 设置 name
                    let names = this.$store.getters.qxTreeNamesSet.split(',')
                    let nameArr = []
                    for (let i = 0; i < this.selectKeys.length; i++) {
                      let obj = {
                        id: this.selectKeys[i],
                        label: names[i],
                      }
                      nameArr.push(obj)
                    }
                    this.selectNames = nameArr
                  }, 50)
                }

              } else {
                this.$message.error(msg)
              }
            })

          }, 50)
        }

        return state
      },
      set: function (newVal) {
        this.$store.commit('SET_QXTREESTATE', newVal)
      }
    },

    qxTreekeys: {
      get: function () {
        let qxTreekeys = JSON.parse(this.$store.getters.qxTreekeys)
        this.$refs.tree.setCheckedKeys(qxTreekeys)
        return qxTreekeys
      },
      set: function (newVal) {

      }
    },
    qxTreeIsRoot: {
      get: function () {
        return qxTreeIsRoot
      },
      set: function (newVal) {

      }
    }
  },
  watch: {
    filterBmLeftText(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {

  },
  methods: {
    roleChange(val) {
      findPerByRole(val).then(res => {
        // loading.close()
        if (res.data.code == '200') {
          let qxList = res.data.list
          let permissionsArr = []  // ids
          let permissionsNameArr = []  // name s
          for (let item of qxList) {
            permissionsArr.push(item.id)
            permissionsNameArr.push(item.label)
          }
          this.$refs.tree.setCheckedKeys([])
          this.$refs.tree.setCheckedKeys(permissionsArr);

          this.selectKeys = permissionsArr
          this.selectNames = JSON.parse(JSON.stringify(qxList))

          // if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
          //   this.selectAllNode(checkedNodes.children)
          // }

          // setTimeout(() => {
          //   this.$refs.tree.setCheckedKeys(this.selectKeys);
          // }, 200)

          // this.dialogData.permissions = permissionsArr.join(',')
          // this.dialogData.permissionsName = permissionsNameArr.join(',')

        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })
    },
    // 【【 节点相关
    // 清空选中节点
    removeNode() {
      this.roleId = ''
      this.$refs.tree.setCheckedKeys([]);
    },
    // 节点选中触发事件
    treeCheck(checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys) {
      // //console.log('checkedNodes', checkedNodes)
      // //console.log('checkedKeys', checkedKeys)
      // //console.log('halfCheckedNodes', halfCheckedNodes)
      // //console.log('halfCheckedKeys', halfCheckedKeys)

      // checkedNodes 当前选中的节点数据
      // checkedKeys.checkedKeys 选中的 id 数组

      //console.log(11, checkedKeys.checkedKeys.length)
      //console.log(22,this.selectKeys.length)

      if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectNames = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        // select-全选；remove-取消全选
        this.selectAllNode(checkedNodes.children, 'select')
      } else {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectNames = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        this.selectAllNode(checkedNodes.children, 'remove')
      }
      setTimeout(() => {
        //console.log('asdfasdf')
        //console.log(this.selectKeys)
        this.$refs.tree.setCheckedKeys(this.selectKeys);
      }, 50)
    },

    selectAllNode(childrenArr, type) {

      for (let item of childrenArr) {
        // 全选，全部取消
        if (type == 'select') {
          if (!this.selectKeys.includes(item.id)) {
            this.selectKeys.push(item.id)
            this.selectNames.push(item)
          }
        } else {
          if (this.selectKeys.includes(item.id)) {
            let mIndex = this.selectKeys.indexOf(item.id)

            this.selectKeys.splice(mIndex, 1)
            this.selectNames.splice(mIndex, 1)
          }
        }
        if (item.children) {
          this.selectAllNode(item.children, type)
        }
      }
    },

    // 【【 弹窗按钮
    // 提交
    bumenOkFunc() {
      // this.btnLoading = true
      this.$store.commit('SET_QXTREEKEYS', 'empty')
      this.$store.commit('SET_QXTREENAMES', 'empty')
      this.$store.commit('SET_QXTREEHALFKEYS', '')

      setTimeout(() => {
        let names = []
        for (let item of this.selectNames) {
          names.push(item.label)
        }
        this.$store.commit('SET_QXTREEKEYS', this.selectKeys.join(','))
        this.$store.commit('SET_QXTREENAMES', names.join(','))
        this.$store.commit('SET_QXTREEHALFKEYS', JSON.stringify(this.$refs.tree.getHalfCheckedKeys()))

        this.closeDialog()
      }, 100)
    },
    // 筛选部门
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 关闭弹窗 
    closeDialog() {
      this.isFirst = true
      this.$store.commit('SET_QXTREESTATE', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.dialog-footer-span {
  font-size: 14px;
  color: #666;
  display: inline-block;
  padding-right: 10px;
}
</style>