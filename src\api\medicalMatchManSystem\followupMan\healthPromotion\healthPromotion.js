import request from '@/utils/request'

/*
*健康宣传维护
*/

// 分页查询 
export function findHealthPromotionDynamic(data) 
{
	return request({
		url: `/follow/findHealthPromotionDynamic`,
		method: 'post',
		data
	})
}

// 启用停用
export function updateHealthPromotionUseType(data)
{
	return request({
		url: `/follow/updateHealthPromotionUseType`,
		method: 'post',
		data
	})
}

// 删除
export function updateHealthPromotion(data)
{
	return request({
		url: `/follow/updateHealthPromotion`,
		method: 'post',
		data
	})
}

// 新增/修改
export function saveOrUHealthPromotion(data)
{
	return request({
		url: `/follow/saveOrUHealthPromotion`,
		method: 'post',
		data
	})
}





