import request from '@/utils/request'

/*
* 循环运送相关
*/

// 查询循环运送列表 
export function findTaskCycleDynamic(data) {
	return request({
		url: `/tra/findTaskCycleDynamic`,
		method: 'post',
		data
	})
}
// 新增或者更新循环运送
export function saveOrUTaskConfigRule(data) {
	return request({
		url: `/tra/saveOrUTaskConfigRule`,
		method: 'post',
		data
	})
}

// 删除
export function delTaskConfigRule(data) {
	return request({
		url: `/tra/delTaskConfigRule`,
		method: 'post',
		data
	})
}

// 启用禁用
export function upDateTaskConfigRuleStatus(data) {
	return request({
		url: `/tra/upDateTaskConfigRuleStatus`,
		method: 'post',
		data
	})
}





