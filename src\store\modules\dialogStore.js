// 弹窗的 状态集合
import Cookie from "js-cookie";
const dialogStore = {
  state: {
    // 【【 获取 权限树
    qxTreeState: false, // 弹窗状态
    qxTreeKeys: "", // 选中的 id 列表
    qxTreeNames: "", // 选中的 name 列表
    qxTreeHalfKeys: "", // 选中的 id 列表(半选)
    qxTreeNodes: "", // 选中的 节点 列表

    qxTreeKeysSet: "", // 设置 选中的 id 列表
    qxTreeNamesSet: "", // 设置 选中的 name 列表

    qxTreeIsSelectChild: true, // 是否全选子节点

    qxTreeIsRoot: true, // 根据 权限
    // 】】 获取 权限树

    // 【【 获取 部门科室树
    dpTreeState: false, // 弹窗状态
    dpTreeKeys: "", // 选中的 id 列表
    dpTreeNames: "", // 选中的 name 列表
    dpTreeHalfKeys: "", // 选中的 id 列表(半选)
    dpTreeNodes: "", // 选中的 节点 列表

    dpTreeKeysSet: "", // 设置 选中的 id 列表
    dpTreeNamesSet: "", // 设置 选中的 name 列表

    dpTreeIsSelectChild: true, // 是否全选子节点

    dpTreeIsRoot: true, // 根据 权限
    // 】】 获取 部门科室树

    // 【【 地图弹窗
    mapState: false, // 地图弹窗状态
    mapJWD: "", // 经纬度
    // 】】 地图弹窗

    // 【【 流程管理
    bpmnIndex: 0, // 计数，判断是否是第一次
    bpmnState: false,
    bpmnType: "add", // add / edit
    bpmnXmlSet: "", // 初始化弹窗 的值
    bpmnXmlGet: "", // 关闭弹窗的值
    // 】】 流程管理

    // 用户未读消息
    userMsgNum: "", // 未读total
    userMsgNumXx: "", // 消息未读
    userMsgNumTz: "", // 通知未读
    userMsgNumGg: "", // 公告未读

    // 商品档案分类 tree
    // sbTreeState: false,
    // sbTreeId: '',
    // sbTreeName: '',

    // // 资产分类 tree
    // zcTreeState: false,
    // zcTreeId: '',
    // zcTreeName: '',

    // 供应链，单选，资产，商品，供应商
    gylTreeOneState: false,
    gylTreeOneType: "", // zichan-资产，shangpin-商品，gongyingshang-供应商
    gylTreeOneId: "",
    gylTreeOneName: "",

    // 供应链，多选商品
    gylGoodsState: false, // 弹窗状态
    gylGoodsType: "", // 状态，商品-sp,供应商-gys
    gylGoodsArr: "", // {id, typeId, name}
    gylGoodsArrSet: "", // 弹窗初始值 {id, typeId, name}

    // 供应链，复选树
    gylTreeSelectsState: false, // 弹窗状态
    gylTreeSelectsType: "", // 弹窗类型，供应商树
    gylTreeSelectsQuery: "", // 查询条件
    gylTreeSelectsSet: "", // 设置初始值
    gylTreeSelectsGet: "", // 返回值

    // 【【 规则配置，多选岗位

    rulePostState: "", // 弹窗状态
    rulePostQuery: "", // 查询条件
    rulePostSet: "", // 设置的值
    rulePostGet: "", // 选中的值

    // import Rulepostdialog from '@/components/Dialog/Rulepostdialog'  // 多选岗位
    // showPostsDialog(row) {
    //   console.log('adsf', this.addData)

    //   let query = {
    //     configId: '',  // 规则id
    //     payRuleType: '',  // 1:缺卡规则 2:旷工规则 3:迟到早退规则
    //   }
    //   this.$store.commit('SET_RULEPOSTQUERY', query)
    //   this.$store.commit('SET_RULEPOSTSET', [])
    //   this.$store.commit('SET_RULEPOSTSTATE', true)
    // },
    // computed: {
    //   ...mapGetters([
    //     'rulePostSet',
    //     'rulePostQuery'
    //   ]),

    //   rulePostState: {
    //     get: function() {
    //       let state = this.$store.getters.rulePostState
    //       if (state == false) {
    //         this.returnList = []
    //       } else {
    //         setTimeout(() => {
    //           this.listQuery = this.$store.getters.rulePostQuery
    //           this.getList()
    //         }, 50)
    //       }
    //       return state
    //     },
    //     set: function(newVal) {
    //       this.$store.commit('SET_RULEPOSTSTATE', newVal)
    //     }
    //   },

    // },

    // 】】 规则配置，多选岗位

    // 供应链，多选商品
    gylGoodsState: false, // 弹窗状态
    gylGoodsIsMultiple: true, // 是否多选
    gylGoodsHasChangyong: false, // + 是否显示部门常用筛选下拉框
    gylGoodsType: "", // 状态，商品-sp,供应商-gys
    gylGoodsArr: "", // {id, typeId, name}
    gylGoodsArrSet: "", // 弹窗初始值 {id, typeId, name}
    gylGoodsQuery: "" // 附加插叙条件
  },

  mutations: {
    // 规则设置
    SET_RULEPOSTSTATE: (state, val) => {
      state.rulePostState = val;
    },
    SET_RULEPOSTQUERY: (state, val) => {
      state.rulePostQuery = val;
    },
    SET_RULEPOSTSET: (state, val) => {
      state.rulePostSet = val;
    },
    SET_RULEPOSTGET: (state, val) => {
      state.rulePostGet = val;
    },

    // 供应链，多选
    SET_GYLTREESELECTSSTATE: (state, val) => {
      state.gylTreeSelectsState = val;
    },
    SET_GYLTREESELECTSTYPE: (state, val) => {
      state.gylTreeSelectsType = val;
    },
    SET_GYLTREESELECTSQUERY: (state, val) => {
      state.gylTreeSelectsQuery = val;
    },
    SET_GYLTREESELECTSSET: (state, val) => {
      state.gylTreeSelectsSet = val;
    },
    SET_GYLTREESELECTSGET: (state, val) => {
      state.gylTreeSelectsGet = val;
    },

    // 供应链，单选，
    SET_GYLTREEONESTATE: (state, val) => {
      state.gylTreeOneState = val;
    },
    SET_GYLTREEONETYPE: (state, val) => {
      state.gylTreeOneType = val;
    },
    SET_GYLTREEONEID: (state, val) => {
      state.gylTreeOneId = val;
    },
    SET_GYLTREEONENAME: (state, val) => {
      state.gylTreeOneName = val;
    },

    // 供应链，多选商品
    SET_GYLGOODSSTATE: (state, val) => {
      state.gylGoodsState = val;
    },
    SET_GYLGOODSTYPE: (state, val) => {
      state.gylGoodsType = val;
    },
    SET_GYLGOODSARR: (state, val) => {
      state.gylGoodsArr = val;
    },
    SET_GYLGOODSARRSET: (state, val) => {
      state.gylGoodsArrSet = val;
    },

    // 【【 获取 权限树
    SET_QXTREESTATE: (state, val) => {
      state.qxTreeState = val;
    },
    SET_QXTREEKEYS: (state, val) => {
      state.qxTreeKeys = val;
    },
    SET_QXTREENAMES: (state, val) => {
      state.qxTreeNames = val;
    },
    SET_QXTREEHALFKEYS: (state, val) => {
      state.qxTreeHalfKeys = val;
    },
    SET_QXTREENODES: (state, val) => {
      state.qxTreeNodes = val;
    },
    SET_QXTREEKEYSSET: (state, val) => {
      state.qxTreeKeysSet = val;
    },

    SET_QXTREENAMESSET: (state, val) => {
      state.qxTreeNamesSet = val;
    },

    SET_QXTREEISSELECTCHILD: (state, val) => {
      state.qxTreeIsSelectChild = val;
    },
    SET_QXTREEISROOT: (state, val) => {
      state.qxTreeIsRoot = val;
    },
    // 】】 获取 权限树

    // 【【 获取 部门科室树
    SET_DPTREESTATE: (state, val) => {
      state.dpTreeState = val;
    },
    SET_DPTREEKEYS: (state, val) => {
      state.dpTreeKeys = val;
    },
    SET_DPTREENAMES: (state, val) => {
      state.dpTreeNames = val;
    },
    SET_DPTREEHALFKEYS: (state, val) => {
      state.dpTreeHalfKeys = val;
    },
    SET_DPTREENODES: (state, val) => {
      state.dpTreeNodes = val;
    },
    SET_DPTREEKEYSSET: (state, val) => {
      state.dpTreeKeysSet = val;
    },

    SET_DPTREENAMESSET: (state, val) => {
      state.dpTreeNamesSet = val;
    },

    SET_DPTREEISSELECTCHILD: (state, val) => {
      state.dpTreeIsSelectChild = val;
    },
    SET_DPTREEISROOT: (state, val) => {
      state.dpTreeIsRoot = val;
    },
    // 】】 获取 部门科室树

    // 【【 流程管理

    SET_BPMNINDEX: (state, val) => {
      state.bpmnIndex += 1;
    },
    SET_BPMNSTATE: (state, val) => {
      state.bpmnState = val;
    },
    SET_BPMNTYPE: (state, val) => {
      state.bpmnType = val;
    },
    SET_BPMNXMLSET: (state, val) => {
      state.bpmnXmlSet = val;
    },
    SET_BPMNXMLGET: (state, val) => {
      state.bpmnXmlGet = val;
    },
    // 】】 流程管理

    // 用户未读消息
    SET_USERMSGNUM: (state, val) => {
      Cookie.set("userMsgNum", val);
      state.userMsgNum = val;
    },
    SET_USERMSGNUMXX: (state, val) => {
      Cookie.set("userMsgNumXx", val);
      state.userMsgNumXx = val;
    },
    SET_USERMSGNUMTZ: (state, val) => {
      Cookie.set("userMsgNumTz", val);
      state.userMsgNumTz = val;
    },
    SET_USERMSGNUMGG: (state, val) => {
      Cookie.set("userMsgNumGg", val);
      state.userMsgNumGg = val;
    },

    // 供应链，多选商品
    SET_GYLGOODSQUERY: (state, val) => {
      state.gylGoodsQuery = val;
    },
    SET_GYLGOODSSTATE: (state, val) => {
      state.gylGoodsState = val;
    },
    SET_GYLGOODSTYPE: (state, val) => {
      state.gylGoodsType = val;
    },
    SET_GYLGOODSARR: (state, val) => {
      state.gylGoodsArr = val;
    },
    SET_GYLGOODSARRSET: (state, val) => {
      state.gylGoodsArrSet = val;
    },
    SET_GYLGOODSISMULTIPLE: (state, val) => {
      state.gylGoodsIsMultiple = val;
    },
    SET_GYLGOODSHASCHANGYONG: (state, val) => {
      state.gylGoodsHasChangyong = val;
    }
  },

  actions: {}
};

export default dialogStore;
