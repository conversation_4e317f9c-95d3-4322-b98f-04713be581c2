<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="归属小区：">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native="getList" placeholder="请输入房号/车位编号/车库编号" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="addItem">添加</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="#" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="操作日期" align="center" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作人" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.createName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="审核人" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.auditorName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.auditState == 0" type="warning">待审核</el-tag>
            <el-tag v-if="scope.row.auditState == 1" type="success">已审核</el-tag>
            <el-tag v-if="scope.row.auditState == 2" type="danger">未通过</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="小区房号">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }} {{ scope.row.roomName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计费摘要">
          <template slot-scope="scope" v-if="scope.row.billDesc">
            <el-popover trigger="hover" placement="top">
              <p v-for="(item, index) of scope.row.billDesc.split(',')" :key="index">{{ item }}</p>
              <div slot="reference" class="name-wrapper elli">
                {{ scope.row.billDesc }}
              </div>
            </el-popover>

            <!-- <div v-for="item in scope.row.billDesc.split(',')">{{ item }}</div> -->
          </template>
        </el-table-column>

        <el-table-column label="原计费金额" width="100" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.payableAmount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="优惠方式">
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row.applyDetailList" :key="index">
              {{ item.discountWay == 1 ? '折扣' + item.discountValue + '%' : '优惠' + item.discountValue + '元' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="优惠后金额" width="100" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.discountedAmount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="优惠备注">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal="false" title="新增优惠申请" :visible.sync="dlgShow" width="600px" append-to-body>
      <el-form
        ref="dlgForm"
        :disabled="dlgType == 'VIEW'"
        :rules="rules"
        :model="dlgData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="归属小区" prop="communityId">
          <el-select
            v-model="dlgData.communityId"
            filterable
            clearable
            placeholder="请选择小区"
            @change="communityChange"
          >
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="优惠类型" prop="payType">
          <el-radio-group v-model="dlgData.payType" @change="radioChange">
            <el-radio :label="1">房屋</el-radio>
            <el-radio :label="2">车位</el-radio>
            <el-radio :label="3">车库</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="选择房屋" prop="roomId" v-if="dlgData.payType == 1">
          <el-input v-model="dlgData.roomName" @focus="showRoomDlg" placeholder="请选择房屋" readonly> </el-input>
        </el-form-item>

        <el-form-item label="选择车位" prop="roomId" v-if="dlgData.payType == 2">
          <el-select v-model="dlgData.roomId" filterable clearable placeholder="请选择车位" @change="parkingChange">
            <el-option v-for="item in parkingList" :key="item.id" :label="item.numStr" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="选择车库" prop="roomId" v-if="dlgData.payType == 3">
          <el-select v-model="dlgData.roomId" filterable clearable placeholder="请选择车库" @change="garageChange">
            <el-option v-for="item in garageList" :key="item.id" :label="item.numStr" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="计费摘要" prop="sumIds">
          <el-select
            v-model="dlgData.sumIds"
            filterable
            multiple
            clearable
            placeholder="请选择计费摘要"
            @change="billChange"
          >
            <el-option v-for="item in billList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="应缴金额(元)" prop="payableAmount">
          <el-input-number
            v-model="dlgData.payableAmount"
            disabled
            :controls="false"
            :min="0"
            :precision="2"
            :step="1"
          ></el-input-number>
        </el-form-item>

        <el-form-item
          v-for="(item, index) in dlgData.applyDetailList"
          :key="index"
          :label="item.billName + '(元)'"
          :prop="'applyDetailList.' + index + '.discountValue'"
          :rules="rules.discountValue"
        >
          <el-input-number
            v-model="item.billAmount"
            disabled
            :controls="false"
            :min="0"
            :precision="2"
            :step="1"
          ></el-input-number>
          <el-select v-model="item.discountWay" placeholder="优惠方式" style="width: 120px" @change="discountChange">
            <el-option v-for="item in discountList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-input-number
            v-model="item.discountValue"
            :controls="false"
            :min="0"
            :max="item.discountWay == 1 ? 100 : item.billAmount"
            :precision="2"
            :step="1"
            @change="discountChange"
          >
          </el-input-number>
          <span v-if="item.discountWay == 1"> % </span>
        </el-form-item>

        <el-form-item label="优惠后金额(元)" prop="discountedAmount">
          <el-input-number
            v-model="dlgData.discountedAmount"
            disabled
            :controls="false"
            :min="0"
            :precision="2"
            :step="1"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type="success" :loading="dlgLoading" @click="subDlg" icon="el-icon-check">
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <roomDlg />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, cogaragePage, coparkingPage } from '@/api/communityMan'
import {
  findBillDiscountApplyPage,
  addOrUpdateDiscountApplyConfig,
  findPayBillSumPageYouhuishenqing,
  findAllPayBillInfosBySumIds,
} from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import roomDlg from '@/components/Dialog/communityMan/roomDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  payType: 1,
  communityId: '',
  communityName: '',
  roomId: '',
  roomName: '',
  sumIds: [],
  applyDetailList: [],
  payableAmount: undefined,
  discountedAmount: undefined,
  remark: '',
}

export default {
  name: 'discountApply',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    roomDlg,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        payType: [{ required: true, message: '必填字段', trigger: 'change' }],
        roomId: [{ required: true, message: '必填字段', trigger: 'change' }],
        sumIds: [{ required: true, message: '必填字段', trigger: 'change' }],
        payableAmount: [{ required: true, message: '必填字段', trigger: 'blur' }],
        discountedAmount: [{ required: true, message: '必填字段', trigger: 'change' }],
        discountValue: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
      },
      communityList: [],
      userInfo: {},
      parkingList: [],
      garageList: [],
      billList: [],
      costTypeList: [],
      discountList: [
        {
          id: 1,
          name: '折扣',
        },
        {
          id: 2,
          name: '优惠',
        },
      ],
    }
  },

  computed: {
    ...mapGetters('communityMan/roomDlg', {
      roomId: 'roomId',
      roomName: 'roomName',
    }),
  },

  watch: {
    roomId(val) {
      console.log(val)
      this.dlgData.roomId = val
      this.dlgData.sumIds = []
      this.dlgData.applyDetailList = []
      this.getBillList()
    },

    roomName(val) {
      this.dlgData.roomName = val
    },
  },

  created() {
    this.getCommunityList()
    utils.getDataDict(this, 'costType', 'costTypeList')
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 选择房号
    showRoomDlg() {
      if (utils.isNull(this.dlgData.communityId)) {
        this.$message.warning('请先选择小区')
        return
      }
      this.$store.commit('communityMan/roomDlg/SET_COMMUNITYID', this.dlgData.communityId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMID', this.dlgData.roomId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMNAME', this.dlgData.roomName)
      this.$store.commit('communityMan/roomDlg/SET_DLGSHOW', true)
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    communityChange() {
      this.dlgData.roomId = ''
      this.dlgData.roomName = ''
      this.dlgData.sumIds = []
      this.dlgData.applyDetailList = []
      this.getGarageList()
      this.getParkingList()
    },

    radioChange() {
      this.dlgData.roomId = ''
      this.dlgData.roomName = ''
      this.dlgData.sumIds = []
      this.dlgData.applyDetailList = []
      this.getGarageList()
      this.getParkingList()
    },

    parkingChange(e) {
      this.dlgData.sumIds = []
      this.dlgData.applyDetailList = []
      this.getBillList()
    },

    garageChange() {
      this.dlgData.sumIds = []
      this.dlgData.applyDetailList = []
      this.getBillList()
    },

    // 计费摘要change
    billChange() {
      let amount = 0
      for (let i of this.billList) {
        if (this.dlgData.sumIds.includes(i.id)) {
          amount += i.amount
        }
      }
      this.dlgData.payableAmount = amount
      this.getBillDesc()
    },

    // 计算优惠后金额
    discountChange() {
      let total = 0
      for (let i of this.dlgData.applyDetailList) {
        let amount = 0
        if (i.discountWay == 1) {
          // 折扣
          amount = (i.billAmount * (i.discountValue || 0)) / 100
        } else {
          amount = i.billAmount - (i.discountValue || 0)
        }
        total += amount
      }
      this.dlgData.discountedAmount = total
    },

    // 获取详细费用
    getBillDesc() {
      if (this.dlgData.sumIds.length == 0) {
        this.dlgData.applyDetailList = []
        return
      }
      findAllPayBillInfosBySumIds(this.dlgData.sumIds.join(',')).then((res) => {
        if (res.data.code == 200) {
          for (let i of res.data.data) {
            i.billName = utils.getNameById(i.feeType, this.costTypeList)
            i.billAmount = i.receivableAmount
            i.discountWay = 1
          }
          this.dlgData.applyDetailList = JSON.parse(JSON.stringify(res.data.data))
          this.$forceUpdate()
        }
      })
    },

    // 获取计费摘要
    getBillList() {
      this.billList = []
      let postParam = {
        page: 1,
        limit: 200,
        status: '0',
        payType: this.dlgData.payType,
        roomId: this.dlgData.roomId,
      }
      findPayBillSumPageYouhuishenqing(postParam).then((res) => {
        if (res.data.code == 200) {
          let list = res.data.data ? res.data.data : []
          for (let i of list) {
            i.name = i.configName + '(' + i.startDate + '~' + i.endDate + ')'
          }
          this.billList = list
        }
      })
    },

    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
      }
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取车库列表
    getGarageList() {
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.dlgData.communityId,
      }
      cogaragePage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.garageList = res.data.data
        }
      })
    },

    // 获取车位列表
    getParkingList() {
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.dlgData.communityId,
      }
      coparkingPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.parkingList = res.data.data
        }
      })
    },

    formatList() {},

    // 获取数据
    getList() {
      // if (utils.isNull(this.listQuery.communityId)) {
      //   this.$message.warning("请选择小区")
      //   return
      // }
      this.count++
      this.listLoading = true
      findBillDiscountApplyPage(this.listQuery).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page.total ? res.data.page.total : 0
          this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    addItem() {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg() {
      if(this.dlgData.discountedAmount<=0){
          this.$message({
          type: "warning",
          message: "优惠后金额必须大于0",
        });
        return false;
      }
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.createName = this.userInfo.label
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          if (postParam.payType == 2) {
            postParam.roomName = utils.getNameById(postParam.roomId, this.parkingList, 'id', 'numStr')
          } else if (postParam.payType == 3) {
            postParam.roomName = utils.getNameById(postParam.roomId, this.garageList, 'id', 'numStr')
          }
          let billDesc = ''
          for (let i of this.billList) {
            if (postParam.sumIds.includes(i.id)) {
              billDesc += i.configName + '(' + i.startDate + '~' + i.endDate + '),'
            }
          }
          postParam.billDesc = billDesc.substring(0, billDesc.length - 1)
          postParam.sumIds = postParam.sumIds.join(',')
          for (let i of postParam.applyDetailList) {
            i.billId = i.id
            if (i.discountWay == 1) {
              // 折扣
              i.discountedAmount = (i.billAmount * (i.discountValue || 0)) / 100
              i.discountAmount = (i.billAmount - i.discountedAmount).toFixed(2)
            } else {
              i.discountAmount = i.discountValue
              i.discountedAmount = (i.billAmount - i.discountAmount).toFixed(2)
            }
          }
          this.dlgLoading = true
          addOrUpdateDiscountApplyConfig(postParam).then((res) => {
            setTimeout(() => {
              this.dlgLoading = false
            }, 100)
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem(data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgType = type
      this.dlgShow = true
    },

    // 启用停用
    delItem(data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        delFeeDiscountById(data.id, flag).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload(file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then((res) => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg() {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


