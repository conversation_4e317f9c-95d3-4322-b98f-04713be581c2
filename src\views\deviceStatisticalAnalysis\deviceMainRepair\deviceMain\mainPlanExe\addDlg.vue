<template>
  <el-dialog class="mazhenguo" :title="dlgType === 'add' ? '添加计划执行' : (dlgType === 'edit' ? '编辑计划执行' : '计划执行')"
    :close-on-click-modal="false" :visible.sync="dlgState" append-to-body width="800px" top="30px">
    <el-form ref="dlgDataForm" :rules="dlgRules" :model="dlgData" label-position="right" label-width="120px"
      style="width: 750px" size="mini" @submit.native.prevent :disabled="dlgType == 'info'">
      <!-- 保养计划、保养计划执行、检修计划、检修计划执行   新增名称字段
    保养计划  name
    保养计划执行  maintenanceName
    检修计划  name
    检修计划执行  checkName -->
      <el-form-item label="计划名称" prop="maintenanceName">
        <el-input v-model="dlgData.maintenanceName" placeholder="请输入" :disabled="dlgType !== 'add'" />
      </el-form-item>

      <formBase ref="formBaseRef" v-model="dlgData"  :pzbyzqSelect="pzbyzqSelect"
        :dlgType="dlgType">
      </formBase>

      <formExe v-model="dlgData" :axztSelect="axztSelect" :optionSelect="optionSelect" :dlgType="dlgType"
        :isZhixing="isZhixing">
      </formExe>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button v-if="dlgType != 'info'" :loading="dlgSubLoading" type="success" @click="dlgSubFunc"
        icon="el-icon-check">
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import * as utils from "@/utils";

import { postAction, getAction, putAction } from "@/api";
import { getDataDict } from "@/utils";

import formBase from "./form/formBase.vue";
import formExe from "./form/formExe.vue";
let dlgDataEmpty = {
  maintenanceName: "",
  id: undefined,

  maintenanceId: undefined, // 保养计划id
  equId: undefined, // 设备id
  equName: undefined,
  equModel: undefined, // 型号
  equPosition: undefined, // 设备所在位置
  fileUrl: undefined, // 附件地址

  invokeStatus: '1', // = 执行状态  0待执行 1已执行
  invokeStatusStr: undefined,
  // invokeStatusInfo: undefined, // 执行情况备注
  invokeTime: undefined, // 保养执行时间
  picUrl: undefined, // 图片地址
  maintenanceItem: [{ name: "", value: "" }], // 保养事项json
  accessoryJson: [{ optionId: "", option: "", unit: "", num: undefined }], // 配件json
  invokeUserId: undefined, // 执行人id
  invokeUserName: null,
  info: undefined, // 备注

  firstTime: undefined, // 初次保养时间
  projectId: undefined, // 项目
  projectName: undefined,
  maintenanceDate: undefined // 计划保养日期
};
export default {
  components: {
    formBase,
    formExe
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {
      type: Object,
      default: {}
    },

    dlgData0: {
      type: Object,
      default: {}
    },
    projectList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {},
  watch: {
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          if (this.dlgType == "add") {
            this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
            this.$nextTick(() => {
              this.$refs["dlgDataForm"].clearValidate();
            });
          } else {
            this.getInit();
          }
        }, 50);
      } else {
        this.closeDlg();
      }
    }
  },
  data() {
    return {
      isZhixing: false,
      dlgState: false,
      dlgLoading: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        projectId: [{ required: true, message: "必填字段", trigger: "change" }],
        equId: [{ required: true, message: "必填字段", trigger: "change" }],
        equModel: [{ required: true, message: "必填字段", trigger: "blur" }],
        equPosition: [
          { required: true, message: "必填字段", trigger: "blur" }
        ],
        maintenanceDate: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        maintenanceItem: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        invokeStatus: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        // invokeStatusInfo: [
        //   { required: true, message: "必填字段", trigger: "blur" }
        // ],
        invokeUserName: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        accessoryJson: [
          { required: true, message: "必填字段", trigger: "change" }
        ]
      },
      dlgSubLoading: false, // 提交loading

      deviceSelect: [],
      pzbyzqSelect: [],
      optionSelect: [],
      axztSelect: [{ id: 0, name: "待执行" }, { id: 1, name: "已执行" }]
    };
  },
  created() {
    getDataDict(this, "maintainPlanOptions", "optionSelect");

  },
  methods: {
    async getInit() {
      console.log("====dlgData0", this.dlgData0);
      let id = this.dlgData0.id;
      try {
        let res0 = await getAction(
          "/green/equ/maintenance-invoke/get?id=" + id
        );
        let res = res0.data;
        if (res && res.code == 200) {
          // this.$refs.formBaseRef.getDeviceSelect(res.data.projectId || "");
          let dlgData = JSON.parse(JSON.stringify(res.data));
          if (dlgData.maintenanceItem) {
            dlgData.maintenanceItem = JSON.parse(dlgData.maintenanceItem);
          } else {
            dlgData.maintenanceItem = [];
          }
          console.log("===dlgData.accessoryJson", dlgData.accessoryJson);
          if (dlgData.accessoryJson && dlgData.accessoryJson !== '[]') {
            dlgData.accessoryJson = JSON.parse(dlgData.accessoryJson);
          } else {
            dlgData.accessoryJson = [{ optionId: "", option: "", unit: "", num: undefined }];
          }
          if (dlgData.fileUrl) {
            dlgData.fileUrl = JSON.parse(dlgData.fileUrl);
          } else {
            dlgData.fileUrl = [];
          }
          if (dlgData.picUrl) {
            dlgData.picUrl = JSON.parse(dlgData.picUrl);
          } else {
            dlgData.picUrl = [];
          }
          dlgData.invokeUserId += "";
          dlgData.invokeStatus = "1"

          this.dlgData = JSON.parse(JSON.stringify(dlgData));
          console.log("====dlgData0", this.dlgData);

        }
      } catch (err) {
        console.log("====错误", err);
        this.$message.error(err.msg);
      }
    },
    dlgSubFunc() {
      this.$refs["dlgDataForm"].validate(valid => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));

          let maintenanceItem = sendObj.maintenanceItem.filter(item => {
            return item.name || item.value;
          });
          if (maintenanceItem.length == 0) {
            this.$message.warning("保养内容不能为空");
            return false;
          }
          let accessoryJson = sendObj.accessoryJson.filter(item => {
            return item.optionId || item.unit || item.num;
          });
          if (sendObj.invokeStatus === '1') {
            if (accessoryJson.length == 0) {
              this.$message.warning("配件列表不能为空");
              return false;
            }
            for (let item of accessoryJson) {
              item.option = utils.arrId2Name(this.optionSelect, item.optionId);
            }
          }


          sendObj.projectName = utils.arrId2Name(
            this.projectList,
            sendObj.projectId
          );
          sendObj.invokeStatusStr = utils.arrId2Name(
            this.axztSelect,
            sendObj.invokeStatus
          );

          sendObj.maintenanceItem = JSON.stringify(maintenanceItem);
          sendObj.accessoryJson = JSON.stringify(accessoryJson);
          sendObj.fileUrl = JSON.stringify(sendObj.fileUrl);
          sendObj.picUrl = JSON.stringify(sendObj.picUrl);

          let url = "";
          let func = "";
          if (this.dlgType == "add") {
            url = "/green/equ/maintenance-invoke/create";
            func = postAction;
          } else {
            if (this.isZhixing) {
              url = "/green/equ/maintenance-invoke/updateStatus";
              func = putAction;
            } else {
              url = "/green/equ/maintenance-invoke/update";
              func = putAction;
            }
          }

          this.dlgSubLoading = true;
          func(url, sendObj).then(res0 => {
            this.dlgSubLoading = false;
            let res = res0.data;

            if (res.code == 200) {
              this.$message.success(res.msg);
              this.dlgState = false;
              this.$emit("getList");
              this.closeDlg();
              this.$emit("upList1");
            } else {
              this.$message({
                type: "warning",
                message: res.msg
              });
            }
          });
        }
      });
    },

    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.dlgState = false;
      // this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.$refs["dlgDataForm"].resetFields();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
