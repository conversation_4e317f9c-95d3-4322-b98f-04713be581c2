// 人员dlg组件

const memberDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    dlgType: '',

    memberId: '',

    memberName: '',

    memberInfo: {},
  },

  getters: {
    dlgShow: state => state.dlgShow,

    dlgType: state => state.dlgType,

    memberId: state => state.memberId,

    memberName: state => state.memberName,

    memberInfo: state => state.memberInfo

  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val
    },

    SET_MEMBERID: (state, val) => {
      state.memberId = val
    },

    SET_MEMBERNAME: (state, val) => {
      state.memberName = val
    },

    SET_MEMBERINFO: (state, val) => {
      state.memberInfo = val
    }

  },

  actions: {

  }
}

export default memberDlg
