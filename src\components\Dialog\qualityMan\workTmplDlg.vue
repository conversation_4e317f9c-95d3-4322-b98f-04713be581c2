<template>
  <el-dialog :close-on-click-modal='false' :title="'选择作业流程模板'" :visible.sync="dlgShow" width="766px" append-to-body>
    <div class="table-container">
      <el-table class='m-small-table' :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="#" align="center" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectTmplId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="模板名称" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="工作项数" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.rulesNum }}</span>
          </template>
        </el-table-column>
        <el-table-column label="工作规程数" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.rulesSettingNum }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-close">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'

export default {
  components: {
    Pagination
  },
  data() {
    return {
      list: [],

      listQuery: {
        page: 1,
        size: 10,
        workType: ""
      },

      total: 0,

      selectTmplId: '',

    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.qualityMan.workTmplDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('qualityMan/workTmplDlg/SET_DLGSHOW', val)
      }
    },

    tmplId: {
      get: function () {
        return this.$store.state.qualityMan.workTmplDlg.tmplId
      },
      set: function (val) {
        this.$store.commit('qualityMan/workTmplDlg/SET_TMPLID', val)
      }
    },

    workType: {
      get: function () {
        return this.$store.state.qualityMan.workTmplDlg.workType
      },
      set: function (val) {
        this.$store.commit('qualityMan/workTmplDlg/SET_WORKTYPE', val)
      }
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.tmplId)) {
          this.selectTmplId = ""
        }
        this.getList()
      }
    },

    workType(val) {
    },

    tmplId(val) {
      this.selectTmplId = val
    },
  },

  methods: {

    rowClick(row, column, event) {
      this.selectTmplId = row['id']
    },

    getList() {
      this.list = []
      this.listQuery['workType'] = this.workType
      wpmPage(this.listQuery).then(res => {
        if (res.data.code == 200) {
          this.list = utils.isNull(res.data.data) ? [] : res.data.data
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    subDlg() {
      this.tmplId = this.selectTmplId
      this.$store.commit('qualityMan/workTmplDlg/SET_TMPLID', this.tmplId)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('qualityMan/workTmplDlg/SET_DLGSHOW', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.filter-container {
  height: 50px;
}
/deep/ .filter-container .el-input {
  width: 220px;
}

/deep/ .filter-container .el-button {
  height: 28px;
}
</style>