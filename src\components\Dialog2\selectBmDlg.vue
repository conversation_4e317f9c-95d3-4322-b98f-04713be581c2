<template>
  <!-- byUser branch role -->
  <el-dialog
    class="mazhenguo"
    :title="`选择${title}`"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="600px"
    top="30px"
  >
    <el-input placeholder="输入关键字进行过滤" v-model="filterBranch"> </el-input>
    <el-tree
      class="mt10"
      ref="branchTree"
      highlight-current
      node-key="id"
      :data="treeData"
      @node-click="treeNodeClick"
      default-expand-all
      :filter-node-method="filterNode"
      :expand-on-click-node="false"
    >
    </el-tree>
    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-span" v-if="selectNode && selectNode.label">当前选中：{{ selectNode.label }}</span>
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button v-if="dlgType != 'info'" :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">确定中...</span>
        <span v-else>确定</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
import * as utils from '@/utils'
import * as regUtils from '@/utils/regUtils'
import { postAction, getAction } from '@/api'

export default {
  components: {},
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgSelectData: {},

    treeType: {
      type: String,
      default: 'branch',
    },

    isRole: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '部门',
    },
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          console.log('--this.dlgSelectData2222', this.dlgSelectData)
          if (this.dlgSelectData) {
            this.selectNode = this.dlgSelectData
            this.$nextTick(() => {
              $('.tree-on').removeClass('tree-on')
              this.$refs.branchTree.setCurrentKey(this.selectNode.id)
            })
          } else {
            this.selectNode = ''
          }
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },

    filterBranch(val) {
      this.$refs.branchTree.filter(val)
    },
  },
  data() {
    return {
      filterBranch: '',
      treeData: [],
      selectNode: {},

      // 弹窗
      dlgState: false,

      dlgSubLoading: false, // 提交loading

      rootId: '',
    }
  },
  created() {
    this.getTree()
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    getTree() {
      if (this.treeType == 'byUser') {
        getAction('/sys/department/getDeptTreeByUserId').then((res0) => {
          let res = res0.data

          if (res.code === '200') {
            console.log('----res.data', res.data)
            this.treeData = [res.data]
            // if (res.msg == '该员工暂无权限查看！') {
            //   this.$message({
            //     type: 'warning',
            //     message: '该员工暂无权限查看！',
            //   })
            // }
          } else {
            this.$message.error(res.msg)
          }
        })
      } else if (this.isRole) {
        // 根据权限
        postAction('/sys/department/findTree').then((res0) => {
          let res = res0.data
          if (res.code === '200') {
            this.treeData = JSON.parse(JSON.stringify(res.list))

            this.rootId = this.treeData[0].id
            if (res.msg == '该员工暂无权限查看！') {
              this.$message({
                type: 'warning',
                message: '该员工暂无权限查看！',
              })
            }
          } else {
            this.$message.error(res.msg)
          }
        })
      } else if (!this.isRole) {
        // 不根据权限
        getAction('/sys/department/findTreeByFrom').then((res0) => {
          let res = res0.data

          if (res.code === '200') {
            this.treeData = JSON.parse(JSON.stringify(res.list))
            this.rootId = this.treeData[0].id
            if (res.msg == '该员工暂无权限查看！') {
              this.$message({
                type: 'warning',
                message: '该员工暂无权限查看！',
              })
            }
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },

    treeNodeClick(data) {
      console.log('====data', data)

      $('.tree-on').removeClass('tree-on')

      if (data.id == this.rootId) {
        this.$message.warning('不能选择该节点')
        this.selectNode = ''
        return false
      }

      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)
      this.selectNode = data
    },

    dlgSubFunc() {
      this.$emit('backFunc', this.selectNode)
      this.closeDlg()
    },

    closeDlg() {
      this.$emit('closeDlg')
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>