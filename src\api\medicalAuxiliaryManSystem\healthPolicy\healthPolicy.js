import request from '@/utils/request'

/*
*陪诊陪检
*/

// 动态查询医保政策 
export function findYiBaoZhengCeDynamic(data) 
{
	return request({
		url: `/u/usapi/yb/findYiBaoZhengCeDynamic`,
		method: 'post',
		data
	})
}

// 保存修改医保政策
export function saveYiBaoZhengCe(data)
{
	return request({
		url: `/u/usapi/yb/saveYiBaoZhengCe`,
		method: 'post',
		data
	})
}

// 删除医保政策
export function delYiBaoZhengCe(data)
{
	return request({
		url: `/u/usapi/yb/delYiBaoZhengCe`,
		method: 'post',
		data
	})
}





