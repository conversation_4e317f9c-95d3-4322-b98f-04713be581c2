/** 报事系统 **/
import Layout from "@/views/layout/Layout";

const matterManRouter = {
  path: "/matterMan",
  component: Layout,
  name: "matterMan",
  meta: {
    title: "报事管理",
    icon: "dash-kqjg",
    roles: ["baoshi<PERSON><PERSON><PERSON>"]
  },

  children: [
    {
      path: "matterConfig",
      component: () => import("@/views/matterMan/matterConfig/index"),
      meta: {
        title: "报事设置",
        roles: ["baoshishezhi"]
      },
      children: [
        {
          path: "matterArea",
          component: () => import("@/views/matterMan/matterConfig/matterArea"),
          name: "报事区域",
          meta: {
            title: "报事区域",
            roles: ["baoshiquyu"]
          }
        },
        {
          path: "matterSubjectModel",
          component: () =>
            import("@/views/matterMan/matterConfig/matterSubjectModel"),
          name: "报事科目设置",
          meta: {
            title: "报事科目设置",
            roles: ["baoshikemu"]
          }
        },
        {
          path: "matterSubject",
          component: () =>
            import("@/views/matterMan/matterConfig/matterSubject"),
          name: "项目报事科目",
          meta: {
            title: "项目报事科目",
            roles: ["baoshikemu"]
          }
        },
        {
          path: "dispatchConfig",
          component: () =>
            import("@/views/matterMan/matterConfig/dispatchConfig"),
          name: "派工设置",
          meta: {
            title: "派工设置",
            roles: ["paigongshezhi"]
          }
        },
        {
          path: "workOrderStep",
          component: () =>
            import("@/views/matterMan/matterConfig/workOrderStep"),
          name: "工单步骤",
          meta: {
            title: "工单步骤",
            roles: ["gongdanbuzhou"]
          }
        }
      ]
    },
    {
      path: "matterManagement",
      component: () => import("@/views/matterMan/matterManagement/index"),
      name: "报事管理",
      meta: {
        title: "报事管理",
        roles: ["baoshiguanlichakan"]
      },
      children: []
    },
    //品质23年7月改
    {
      path: "reportSuperviseNew",
      component: () => import("@/views/reportMan/reportSuperviseNew/index"),
      name: "报事统计",
      meta: {
        title: "报事统计",
        roles: ["baoshitongji_old"]
      }
    },
    {
      path: "statisticalAnalysisNew",
      component: () => import("@/views/reportMan/statisticalAnalysisNew"),
      name: "统计分析",
      meta: {
        title: "统计分析",
        roles: ["baoshifenxi_old"]
      }
    },
    {
      path: "orderRecords",
      component: () => import("@/views/reportMan/orderRecords"),
      name: "接单提示记录",
      meta: {
        title: "接单提示记录",
        roles: ["jiedantishijilu"]
      }
    }
  ]
};

export default matterManRouter;
