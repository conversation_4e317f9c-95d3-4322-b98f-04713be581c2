<template>
  <div class="app-container" style="padding-top: 10px; padding-bottom: 10px">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="m-tabs">
      <el-tab-pane label="设备维保统计" name="first">
        <basicledgerMain></basicledgerMain>
      </el-tab-pane>
      <el-tab-pane label="设备保养" name="second">
        <monthVue></monthVue>
      </el-tab-pane>
      <el-tab-pane label="设备检修" name="third">
        <yearVue></yearVue>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import basicledgerMain from "../basicledgerMain/index.vue";
import monthVue from "./components/monthVue.vue";
import yearVue from "./components/yearVue.vue";

export default {
  components: {
    monthVue,
    yearVue,
    basicledgerMain
  },
  data() {
    return {
      activeName: "first",
    };
  },
  created() {},
  methods: {},
};
</script>

<style rel="stylesheet/scss" lang="scss" scope></style>
