import request from '@/utils/request'
// ------ << 财务管理 ------

// EMS 查询
export function findEMSListPage(data) {
  return request({
    url: `/case/mailInfo/findEMSListPage`,
    method: 'post',
    data
  })
}
// 日结汇总
export function daySettleSumListCW(data) {
  return request({
    url: `/case/detailed/daySettleSumListCW`,
    method: 'post',
    data
  })
}
// 日结明细
export function daySettleListCW(data) {
  return request({
    url: `/case/detailed/daySettleListCW`,
    method: 'post',
    data
  })
}

// 已退查询
export function findRefundRecordPage(data) {
  return request({
    url: `/case/findRefundRecordPage`,
    method: 'post',
    data
  })
}

//  聚合支付查询
export function findJHPayRecordPage(data) {
  return request({
    url: `/case/findJHPayRecordPage`,
    method: 'post',
    data
  })
}
// 微信支付查询
export function findWXPayRecordPage(data) {
  return request({
    url: `/case/findWXPayRecordPage`,
    method: 'post',
    data
  })
}

//  聚合对账查询
export function findJHPayRecord(payDate) {
  return request({
    url: `/case/findJHPayRecord/${payDate}`,
    method: 'get'
  })
}

// 对账查询
export function downloadWechatBillOfNew(data) {
  return request({
    url: `/case/downloadWechatBillOfNew`,
    method: 'post',
    data
  })
}

// 手动对账处理
export function manualReconciliation(data) {
  return request({
    url: `/case/shoudongduizhang`,
    method: 'post',
    data
  })
}