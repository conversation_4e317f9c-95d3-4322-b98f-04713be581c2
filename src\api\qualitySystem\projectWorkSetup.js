import request from '@/utils/request'
import {
  requestExcel
} from '@/utils'
/*
 *项目作业设置
 */

// 按照个人权限加载作业区域树
export function loadWorkRegionBigTree() {
  return request({
    url: `/report/region/loadWorkRegionBigTree`,
    method: 'get',
  })
}

// 加载作业区域树 
export function loadWorkRegionTree(data) {
  return request({
    url: `/report/region/loadWorkRegionTree`,
    method: 'post',
    data
  })
}


// 新增修改工作区域
export function addOrUpdateInfo(data) {
  return request({
    url: `/report/region/addOrUpdateInfo`,
    method: 'post',
    data
  })
}

// 删除工作区域
export function removeWorkRegion(id) {
  return request({
    url: `/report/region/remove/${id}`,
    method: 'get'
  })
}


// 移动工作区域
export function upDownMoveRegion(up, down) {
  return request({
    url: `/report/region/upDownMove/${up}/${down}`,
    method: 'get'
  })
}

// 查询作业区域详情
export function findInfoById(id) {
  return request({
    url: `/report/region/findInfoById/${id}`,
    method: 'get'
  })
}

// 按岗位查询业务范围
export function findRangeBusinessByPostId(id) {
  return request({
    url: `/report/wrb/findRangeBusinessByPostId/${id}`,
    method: 'get'
  })
}


// 按岗位查添加业务范围
export function addOrUpdate(data) {
  return request({
    url: `/report/wrb/addOrUpdate`,
    method: 'post',
    data
  })
}

// 批量查询区域关系
export function findRegionByIds(data) {
  return request({
    url: `/report/wrb/findRegionByIds`,
    method: 'post',
    data
  })
}


// 根据区域id 查询流程列表
export function processPage(data) {
  return request({
    url: `/report/process/page`,
    method: 'post',
    data
  })
}

// 点位作业流程列表按岗位查
export function processPageByPost(data) {
  return request({
    url: `/report/process/pageByPost`,
    method: 'post',
    data
  })
}

// 点位作业流程列表显示作业人员
export function pageByRegion(data) {
  return request({
    url: `/report/process/pageByRegion`,
    method: 'post',
    data
  })
}

// 新增修改作业流程
export function processAddOrUpdate(data) {
  return request({
    url: `/report/process/addOrUpdate`,
    method: 'post',
    data
  })
}

// 删除点位作业流程
export function processDel(id) {
  return request({
    url: `/report/process/del/${id}`,
    method: 'get'
  })
}

// 点位作业流程详情
export function processInfo(id) {
  return request({
    url: `/report/process/info/${id}`,
    method: 'get'
  })
}

// 应用至
export function processApplyTo(data) {
  return request({
    url: `/report/process/applyTo`,
    method: 'post',
    data
  })
}

// << mzg 0810 品质需求变更
// 作业流程列表  page，size，branchId，status
export function orgPagePostUser(data) {
  return request({
    url: `/sys/pagePostUser`,
    method: 'post',
    data
  })
}
// 获取用户项目
export function findUserProject(postId) {
  return request({
    url: `/sys/findUserProject/${postId}`,
    method: "get"
  });
}

// 获取工种
export function findUserWorkType(userId) {
  return request({
    url: `/report/userWorkType/list/${userId}`,
    method: "get"
  });
}
// 起停用
export function reportProcessEnable(data) {
  return request({
    url: `/report/process/enable`,
    method: 'post',
    data
  })
}
// 导入
export function regionImport(data) {
  return requestExcel('/report/region/import', data)
}
// 移动至
export function regionMoveTo(data) {
  return request({
    url: `/report/region/moveTo`,
    method: 'post',
    data
  })
}

// 报事移动至
export function areaMoveRArea(data) {
  return request({
    url: `/report/area/moveRArea`,
    method: 'post',
    data
  })
}
// >> mzg 0810 品质需求变更
