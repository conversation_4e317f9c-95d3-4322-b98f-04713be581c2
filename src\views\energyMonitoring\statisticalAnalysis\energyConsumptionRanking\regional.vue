<template>
  <div class="app-container" style="padding: 0;">
    <div class="filter-container">
      <el-form
        inline
        size="small"
        ref="searchForm"
        class=""
        :model="listQuery"
        label-width="90px"
        @submit.native.prevent
      >
        <el-form-item label="采集时间">
          <el-date-picker
            style="width: 250px"
            @change="getList"
            v-model="listQuery.dateRange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="small"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="能耗类型">
          <el-select
            v-model="listQuery.energyType"
            placeholder="能耗类型"
            style="width: 100px"
          >
            <el-option
              v-for="item of statusSelect"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="primary"
          size="small"
          @click="getList"
          >搜索</el-button
        >
      </el-form>
    </div>
    <div class="echart-container" id="echart-panel" style="width: 100%"></div>
  </div>
</template>
<script>
import { postAction, getAction } from "@/api";
import { mapGetters } from "vuex";
import * as echarts from "echarts";
import * as utils from "@/utils";

export default {
  components: {},
  data() {
    return {
      listQuery: {
        dateRange: [],
        // label: "",
        startTime: "",
        endTime: "",
        queryType: "area",
        energyType: "water"
      },
      exceptionData: {},
      statusSelect: [
        { id: "water", name: "用水" },
        { id: "electricity", name: "用电" }
      ],
      echartPanel: null // echart面板
    };
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    this.listQuery.dateRange = utils.returnToMonth();
    this.getList();
  },
  methods: {
    getList() {
      if (utils.isNull(this.listQuery.dateRange)||this.listQuery.dateRange.length<=0) {
        this.$message.warning("请先选择日期");
        return;
      }
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      if (sendObj.dateRange && sendObj.dateRange.length > 0) {
        sendObj.startTime = sendObj.dateRange[0];
        sendObj.endTime = sendObj.dateRange[1];
      }
      sendObj.projectId = this.userInfo.projectId;
      postAction(
        `/iot/energy-data/energyConsumptionRankingAnalysis`,
        sendObj
      ).then(res => {
        if (res.data.code == 200) {
          this.exceptionData = res.data.list;
          this.$nextTick(() => {
            this.createChart();
          });
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 创建图表
    createChart() {
      if (this.exceptionData.length == 0) {
        if (!utils.isNull(this.echartPanel)) {
          this.echartPanel.clear();
        }
        return;
      }
      let legendData = [];
      let xAxisData = [];
      let seriesData = [];
      for (let i of this.exceptionData) {
        legendData.push(i.area);
        xAxisData.push(i.area);
        seriesData.push(i.standardCoal);
      }
      // 基于准备好的dom，初始化echarts实例
      this.echartPanel = echarts.init(document.getElementById("echart-panel"));
      // 指定图表的配置项和数据
      let option = {
        // color: function (params) {
        //   let colorList = ['#249CF9', '#EB6F49', '#FDB628', '#00E4EC', '#69FD28', '#C490BF', '#FFF100', '#486A00', '#F6B37F', '#7ECEF4', '#22AC38', '#7E6B5A'];
        //   return colorList[params.dataIndex % colorList.length]
        // },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        formatter: val => {
          this.clickIndex = val[0].dataIndex;
          return val[0]["value"] + "<br>" + val[0]["axisValue"];
        },
        dataZoom: [
          {
            type: "inside"
          }
        ],
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "10%",
          containLabel: true
        },
        legend: {
          data: legendData
        },
        xAxis: {
          type: "value" // 改为数值轴
        },
        yAxis: {
          type: "category", // 改为分类轴
          data: xAxisData,
          inverse: true,
          axisPointer: {
            type: "shadow"
          }
        },
        series: [
          {
            type: "bar",
            coordinateSystem: "cartesian2d", // 关键配置
            barWidth: "30%",
            data: seriesData,
            itemStyle: {
              // 不同颜色
              normal: {
                label: {
                  show: true,
                  position: "right"
                },
                color: function(params) {
                  var colorList0 = [
                    // '#42d2b9',
                    // '#4cb5f2',
                    // '#ddb146',
                    // '#19AA8D',
                    // '#cdbed1',

                    "#249CF9",
                    "#EB6F49",
                    "#FDB628",
                    "#00E4EC",
                    "#69FD28",
                    "#C490BF",
                    "#FFF100",
                    "#486A00",
                    "#F6B37F",
                    "#7ECEF4",
                    "#22AC38",
                    "#7E6B5A"
                  ];
                  let colorList = [];
                  if (seriesData.length > colorList0.length) {
                    for (let i = 0; i < seriesData.length; i++) {
                      let index = i % colorList0.length;

                      colorList.push(colorList0[index]);
                    }
                  } else {
                    colorList = colorList0;
                  }
                  return colorList[params.dataIndex];
                }
              }
            }

            // itemStyle: {
            //   normal: {
            //     label: {
            //       show: true,
            //       position: 'top',
            //     },
            //   },
            // },
          }
        ]
      };

      this.echartPanel.setOption(option);
      console.log(option, "option");

      window.addEventListener("resize", () => {
        this.echartPanel.resize();
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.echart-container {
  height: 90%;
}
</style>
