<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog
      :close-on-click-modal="false"
      title="请选择"
      :visible.sync="usersOrBranState"
      width="900px"
      top="30px"
      icon-class="el-icon-info"
      append-to-body
    >
      <div class="dialog-bms-bar">
        <!-- 部门 -->
        <el-row>
          <el-col :span="11">
            <div class="dialog-bms-left">
              <!-- 左上 -->
              <el-input placeholder="输入关键字进行过滤" style="margin-bottom: 10px" v-model="bmsText"> </el-input>
              <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
              <div class="bms-tree">
                <el-tree
                  :data="bmsData"
                  ref="bmsDom"
                  default-expand-all
                  :filter-node-method="bmsFilter"
                  @node-click="bmsClick"
                >
                  <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span v-if="usersOrBrancanSelectBm" @dblclick="selectBm(data)">{{ node.label }}</span>
                    <span v-else>{{ node.label }}</span>
                    <!-- <span>
                      <el-button
                        type="text"
                        size="mini"
                        @click="() => append(data)">
                        Append
                      </el-button>
                      <el-button
                        type="text"
                        size="mini"
                        @click="() => remove(node, data)">
                        Delete
                      </el-button>
                    </span> -->
                  </span>
                </el-tree>
              </div>

              <!-- 左下 -->
              <!-- 选择选择岗位或员工 -->
              <div style="margin-top: 16px; margin-bottom: 10px">
                <el-radio-group @change="changeRadio" v-model="radioVal">
                  <el-radio label="0" :disabled="!usersOrBranTypeDis">岗位</el-radio>
                  <el-radio label="1" :disabled="!usersOrBranTypeDis">员工</el-radio>
                </el-radio-group>
                <span v-show="selectNode.label" style="margin-left: 10px; color: #666"
                  >（已选部门：{{ selectNode.label }}）</span
                >
              </div>

              <el-input
                placeholder="输入关键字进行过滤"
                style="margin-bottom: 10px"
                @input="filterUser($event)"
                v-model="filterBmrightText"
              >
              </el-input>
              <div class="dialog-bm-right-loading" v-show="filterBmrightLoading">
                <i class="el-icon-loading"></i>
              </div>
              <div class="dialog-bm-right-ul" v-show="!filterBmrightLoading">
                <p v-if="filterBmrightList.length == 0">暂无数据</p>
                <a
                  href="javascript:void(0)"
                  v-for="(item, index) of filterBmrightList"
                  :key="index"
                  @click="selectUser(item, '0')"
                >
                  <i class="el-icon-news"></i>
                  {{ item.label }}
                </a>
              </div>
            </div>
          </el-col>
          <el-col :span="2">
            <div class="dialog-bms-con">
              <i class="el-icon-d-arrow-right jiantou1"></i>
              <i @click="jiantou2SelectAll" class="el-icon-d-arrow-right jiantou2"></i>
            </div>
          </el-col>
          <el-col :span="11">
            <div class="dialog-bms-right">
              <!-- 已选部门 -->
              <div v-show="bmArr.length != 0" class="dialog-bms-right-item">
                <p class="dialog-bms-right-title">已选部门：</p>
                <el-button v-for="(item, index) of bmArr" :key="index" class="bms-a" type="success" size="mini" plain>
                  {{ item.label }}
                  <i class="el-icon-close" @click="delGwFunc(item.id, item.label, item.type)"></i>
                </el-button>
              </div>

              <!-- 已选岗位 -->
              <div v-show="gwArr.length != 0" class="dialog-bms-right-item">
                <p class="dialog-bms-right-title">已选岗位：</p>
                <el-button v-for="(item, index) of gwArr" :key="index" class="bms-a" type="success" size="mini" plain>
                  {{ item.label }}
                  <i class="el-icon-close" @click="delGwFunc(item.id, item.label, item.type)"></i>
                </el-button>
              </div>

              <!-- 已选员工 -->
              <div v-show="ygArr.length != 0" class="dialog-bms-right-item">
                <p class="dialog-bms-right-title">已选员工：</p>
                <el-button v-for="(item, index) of ygArr" :key="index" class="bms-a" type="success" size="mini" plain>
                  {{ item.label }}
                  <i class="el-icon-close" @click="delGwFunc(item.id, item.label, item.type)"></i>
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon="el-icon-back">取消</el-button>
        <el-button type="primary" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

// 接口
import { findOrgBranchAll, findOrgBranchAll2 } from '@/api/dataDic'
import { findUserByLabelAndNum } from '@/api/staffMan'

import { findOrgBranchPostByCondition } from '@/api/postMan' // 是否登台查询
// import { findPostByDynamic } from '@/api/postMan'

export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      // 部门树
      bmsData: [],
      bmsSelect: {},
      bmsText: '', // 部门左侧筛选

      // 中间岗位
      filterBmrightLoading: false, // 右侧loading
      filterBmrightText: '', // 部门右侧筛选
      filterBmrightList: [
        // 部门右侧 列表
      ],
      filterBmrightListOld: [],

      // 右侧选中的样式
      bmsIds: [],
      bmsNames: [],
      usersArr2: [],

      selectNode: '', // 选中的节点
      radioVal: '', // 单选框
      syncObj: {}, // 动态查询条件

      // 右侧显示的 部门，岗位，员工数据集合
      bmArr: [],
      gwArr: [],
      ygArr: [],
    }
  },
  computed: {
    ...mapGetters([
      'usersOrBranArr2',
      'usersOrBranType', // 单选框选中类型 0 岗位, 1 员工
      'usersOrBranTypeDis', // 是否可改变单选框状态
      'usersOrBrancanSelectBm', // 是否允许部门单机时间
      'usersOrBranRoleState', // 是否根据权限，读取登录人的下属员工或岗位
    ]),

    usersOrBranState: {
      get: function () {
        let state = this.$store.getters.usersOrBranState

        if (state == false) {
          this.filterBmrightText = ''
          this.filterBmrightList = []
          this.filterBmrightListOld = []
          this.usersArr2 = []

          this.bmArr = []
          this.gwArr = []
          this.ygArr = []
        }

        return state
      },
      set: function (newVal) {
        if (!newVal) {
          window.sessionStorage.pageName = ''
        }

        this.$store.commit('SET_USERSORBRANSTATE', newVal)
      },
    },
    // bmsArr: {
    //   get: function() {
    //     let bmsArr = this.$store.getters.bmsArr

    //     return bmsArr
    //   },
    //   set: function(newVal) {
    //   }
    // },
  },
  watch: {
    bmsText(val) {
      this.$refs.bmsDom.filter(val)
    },
    usersOrBranArr2(val) {
      console.log('dddddd', val)

      if (val == '') {
        return false
      }
      this.gwArr = []
      this.ygArr = []
      this.usersArr2 = []

      setTimeout(() => {
        this.usersArr2 = val
        if (this.usersOrBranType == '0') {
          // 岗位
          this.gwArr = val
        } else if (this.usersOrBranType == '1') {
          // 员工
          this.ygArr = val
        }
      }, 50)
    },
    usersOrBranState(val) {
      if (val == true) {
        this.filterBmrightLoading = false

        this.findOrgBranchAll()
        setTimeout(() => {
          this.radioVal = this.$store.getters.usersOrBranType
          this.syncObj = this.$store.getters.usersOrBranSync
        }, 200)
      }
    },
  },
  created() {
    this.findOrgBranchAll()
  },
  methods: {
    // 点 下方箭头，将数据全放到右边
    jiantou2SelectAll() {
      console.log(this.filterBmrightList) // 左下侧列表
      console.log(this.radioVal) // 0-岗位，1-员工

      for (let item of this.filterBmrightList) {
        this.selectUser(item, '1')
      }

      //
    },
    // 双击选中部门
    selectBm(data) {
      console.log(data)
      let id = data.id
      let type = '2'
      let obj = {
        id,
        label: data.label,
        type,
      }

      let isHas = this.usersArr2.every((item) => {
        return item.id != id || item.type != type
      })

      if (isHas) {
        this.bmArr.push(obj)
        this.usersArr2.push(obj)
      } else {
        this.$message({
          type: 'warning',
          message: '所选部门已在列表中，请勿重复添加。',
        })
      }
    },
    // 单选框 切换
    changeRadio(val) {
      this.filterBmrightList = []
      this.filterBmrightListOld = []
      // console.log(this.selectNode)

      if (this.selectNode) {
        this.getBmUsers(this.selectNode.id)
      }
    },
    // 【【 左侧相关
    // 获取部门
    findOrgBranchAll() {
      this.bmsData = []

      // 通知公告
      if (window.sessionStorage.pageName == 'tongZhiGongGao') {
        findOrgBranchAll2().then((res) => {
          let code = res.data.code
          let data = res.data.data
          let msg = res.data.msg

          if (code == '200') {
            this.bmsData = JSON.parse(JSON.stringify(res.data.list))
          } else {
            this.$message.error(msg)
          }
        })
        return false
      }
      // 原来的
      findOrgBranchAll().then((res) => {
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code == '200') {
          this.bmsData = JSON.parse(JSON.stringify(res.data.list))
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 节点左键点击事件
    bmsClick(data, node, mNode) {
      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      this.selectNode = data
      // 判断单选框是 部门 还是 岗位

      this.getBmUsers(data.id)

      // let radioVal = this.radioVal
      // if (radioVal == '0') {  // 岗位
      //   this.getBmGws(data.id)
      // } else if (radioVal == '1') {  // 员工
      //   this.getBmUsers(data.id)
      // }
      // 获取部门 下 岗位
    },

    // 选择部门后，获取部门岗位
    getBmUsers(branchId) {
      this.postName = ''
      this.filterBmrightLoading = true
      this.filterBmrightList = []
      this.filterBmrightListOld = []

      let radioVal = this.radioVal
      if (radioVal == '1') {
        // 员工
        let sendObj = {
          page: 1,
          size: 9999,
          labelNum: '',
          branchId,
          postId: '',
        }
        findUserByLabelAndNum(sendObj).then((res) => {
          this.filterBmrightLoading = false
          let code = res.data.code
          let msg = res.data.msg
          let list = res.data.list
          if (list && code == '200') {
            this.filterBmrightList = res.data.list
            this.filterBmrightListOld = res.data.list
          } else if (code == '201') {
            console.log('aa')
          } else {
            this.$message.error(msg)
          }
        })
      } else if (radioVal == '0') {
        let sendObj = {
          brId: branchId,
          page: 1,
          sqType: this.$store.getters.usersOrBranSqType,
          size: 5000,
        }
        findOrgBranchPostByCondition(sendObj).then((res) => {
          this.filterBmrightLoading = false
          let code = res.data.code
          let msg = res.data.msg

          if (code == '200') {
            if (res.data.list) {
              let list = res.data.list
              for (let item of list) {
                if (item.userName) {
                  item.label = `${item.label}(${item.userName})`
                }
              }

              this.filterBmrightList = JSON.parse(JSON.stringify(list))
              this.filterBmrightListOld = JSON.parse(JSON.stringify(list))
            } else {
              this.filterBmrightList = []
              this.filterBmrightListOld = []

              this.$message({
                type: 'warning',
                message: res.data.msg,
              })
            }
          } else {
            this.$message.error(msg)
          }
        })
      }
    },

    // 筛选部门
    bmsFilter(value, data) {
      if (!value) return true
      return data.label.indexOf(value) != -1
    },
    // 】】 左侧相关

    // 【【 左下相关,dytype:0-单个，1-多个
    selectUser(data, dytype) {
      let type = this.radioVal
      let id = ''
      let label = data.label
      let msg = ''

      let isHas = ''

      if (type == '0') {
        // 选择岗位
        id = data.postId
        msg = '所选岗位已在列表中，请勿重复添加。'
        // console.log('this.gwArr', this.gwArr)

        isHas = this.gwArr.every((item) => {
          // console.log('item.id', item.id)
          // console.log('id', id)

          // console.log('item.type', item.type)
          // console.log('type', type)
          return item.id != id || item.type != type
        })
      } else if (type == '1') {
        // 员工
        id = data.id
        msg = '所选员工已在列表中，请勿重复添加。'
        isHas = this.ygArr.every((item) => {
          return item.id != id || item.type != type
        })
      } else {
        msg = '所选部门已在列表中，请勿重复添加。'
        isHas = this.bmArr.every((item) => {
          return item.id != id || item.type != type
        })
      }

      if (isHas) {
        console.log('yyyy', data)
        let branchId = data.branchId
        let branchName = data.branchName
        if (type == '1') {
          // 选择员工
          let obj = { id, label, branchId, branchName, type, phone: data.account }
          this.ygArr.push(obj)
        }
        if (type == '0') {
          // 选择岗位
          // console.log(data)
          let obj = { id, label, branchId, branchName, type, postJob: data.postJob, postJobText: data.postJobText }
          this.gwArr.push(obj)
        }
      } else {
        if (dytype == 0) {
          this.$message({
            type: 'warning',
            message: msg,
          })
        }
      }
    },
    // 筛选岗位/员工
    filterUser(val) {
      this.filterBmrightList = JSON.parse(JSON.stringify(this.filterBmrightListOld))
      this.filterBmrightList = this.filterBmrightList.filter((item) => {
        return item.label.indexOf(val) >= 0
      })
    },
    // 】】 左下相关

    // 【【 右侧相关
    // 右侧删除部门方法
    delGwFunc(id, label, type) {
      if (type == '1') {
        // 选择员工
        let mArr = this.ygArr.filter((item) => {
          return item.id != id
        })
        this.ygArr = JSON.parse(JSON.stringify(mArr))
      }
      if (type == '0') {
        // 选择岗位
        let mArr = this.gwArr.filter((item) => {
          return item.id != id
        })
        this.gwArr = JSON.parse(JSON.stringify(mArr))
      }
      if (type == '2') {
        // 选择部门
        let mArr = this.bmArr.filter((item) => {
          return item.id != id
        })
        this.bmArr = JSON.parse(JSON.stringify(mArr))
      }

      // 集合
      let nArr = this.usersArr2.filter((item) => {
        return item.id != id
      })
      this.usersArr2 = JSON.parse(JSON.stringify(nArr))

      console.log(this.ygArr)
    },
    // 】】 右侧相关

    // 【【 其他
    // 选择部门提交
    bumenOkFunc() {
      console.log('this.ygArr', this.ygArr)

      let message = ''
      if (this.usersOrBranType == 0) {
        // 岗位
        message = '请至少选择一个岗位'
      } else {
        message = '请至少选择一个员工'
      }
      this.usersArr2 = [...this.bmArr, ...this.gwArr, ...this.ygArr]

      if (this.usersArr2.length == 0) {
        this.$message({
          type: 'warning',
          message,
        })
      } else {
        this.$store.commit('SET_USERSORBRANARR', this.usersArr2)
        this.closeDialog()
      }
    },
    // 关闭弹窗
    closeDialog() {
      window.sessionStorage.pageName = ''
      this.$store.commit('SET_USERSORBRANSTATE', false)
    },
    // 】】 其他
  },
  beforeDestroy() {
    this.$store.commit('SET_USERSORBRANARR2', '')
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dialog-bms-bar {
  width: 100%;
}
.dialog-bms-right {
  height: 520px;
  overflow: auto;
}
// .dialog-bms-left,.dialog-bms-right {
//   width: 48%;
// }
// 中间箭头
.dialog-bms-con {
  flex: 1;
  .jiantou1,
  .jiantou2 {
    display: block;

    line-height: 200px;
    font-size: 20px;
    color: #67c23a;
    text-align: center;
  }
  .jiantou1 {
    margin-top: 38px;
  }
  .jiantou2 {
    margin-top: 82px;
    cursor: pointer;
  }
}
.bms-tree {
  height: 200px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
}
.dialog-bms-right {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 10px;
  display: block;
  .bms-a {
    margin-bottom: 10px;
    margin-left: 0px;
    margin-right: 6px;
  }
  i {
    display: inline-block;
    margin-left: 3px;
  }
  .dialog-bms-right-item {
    margin-bottom: 6px;
  }
  .dialog-bms-right-title {
    font-weight: bold;
    margin-top: 0px;
    margin-bottom: 10px;
  }
}

.dialog-bm-right-ul {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 10px;
  height: 200px;
  overflow: auto;
  p {
    text-align: center;
    margin-top: 6px;
  }
  a {
    display: block;
    line-height: 26px;
    margin-left: 10px;
    color: #666;
    i {
      display: inline-block;
      margin-right: 8px;
      line-height: 26px;
      color: #19aa8d;
    }
  }
}

//
.dialog-bm-right {
  width: 30%;
}
.dialog-bm-right-loading {
  padding-top: 60px;
  text-align: center;
  font-size: 60px;
  height: 200px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
}
</style>