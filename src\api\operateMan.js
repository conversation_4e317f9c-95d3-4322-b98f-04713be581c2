// 运维管理 - http://192.168.1.175:9527/#/platformManSystem/operateMan
import request from '@/utils/request'
// 1 修改密码
export function findUpdatePwdLogByUserId(data) {
  return request({
    url: `/sys/findUpdatePwdLogByUserId`,
    method: 'post',
    data
  })
}

// 2 考勤组变更查询
export function findGroupLogByUserId(data) {
  return request({
    url: `/ade/findGroupLogByUserId`,
    method: 'post',
    data
  })
}

// 3 打卡对比
// findPunchRecordByUserId
export function findPunchLogByUserId(data) {
  return request({
    url: `/ade/findPunchLogByUserId`,
    method: 'post',
    data
  })
}

// 4 排班日志查询
export function findSchedulingLogByUserId(data) {
  return request({
    url: `/ade/findSchedulingLogByUserId`,
    method: 'post',
    data
  })
}

// 4 社保日志查询
export function findUpdateSecurityFamilyLogByUserId(data) {
  return request({
    url: `/sys/findUpdateSecurityFamilyLogByUserId`,
    method: 'post',
    data
  })
}

// 5 岗位变更日志查询
export function findPostLog(data) {
  return request({
    url: `/sys/findPostLog`,
    method: 'post',
    data
  })
}
// 6 部门变更日志查询
export function findBranchLog(data) {
  return request({
    url: `/sys/findBranchLog`,
    method: 'post',
    data
  })
}
// 7 员工变更日志查询
export function findUserLog(data) {
  return request({
    url: `/sys/findUserLog`,
    method: 'post',
    data
  })
}


