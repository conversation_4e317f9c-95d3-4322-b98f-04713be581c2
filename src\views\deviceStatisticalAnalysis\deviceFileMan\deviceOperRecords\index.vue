<template>
  <div class="app-container mazhenguo" style="margin-bottom: 32px; padding-bottom: 10px">
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <!-- <div class="search-item-label lh28">选择项目：</div> -->
            <!-- <el-select
              ref="projectSelect"
              class="fl"
              style="width: 220px"
              v-model="searchForm.projectId"
              placeholder="选择项目"
              @change="projectChange"
              filterable
              clearable
            >
              <el-option
                v-for="item of projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>

          <div class="search-item"> -->
            <div class="search-item-label lh28">选择模板：</div>
            <el-select v-model="searchForm.modelId" placeholder="请选择模板" class="fl" style="width: 220px"
              @change="searchFunc" filterable clearable>
              <el-option v-for="item in equModelsOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>

          <div class="search-item">
            <div class="search-item-label lh28">日期：</div>
            <el-date-picker v-model="searchForm.createTime" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" class="fl"
              style="width: 220px">
            </el-date-picker>

            <el-button type="primary" icon="el-icon-search" @click="searchFunc" class="fl ml10">查询</el-button>
            <!-- <el-button
              type="success"
              @click="showDlg('add')"
              icon="el-icon-plus"
              class="fl ml10"
              >添加</el-button
            > -->
          </div>
        </div>
      </div>
    </div>

    <el-table :data="tableData" height="calc(100vh - 290px)" ref="tableBar" class="m-small-table"
      v-loading="listLoading" border fit highlight-current-row style="width: 100%; height: auto" :key="tableKey">
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="modelName" label="模板名称" width="auto" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column width="auto" align="center" :label="item.title" :prop="item.id" v-for="item in dataInfoJson"
        :key="item.id" show-overflow-tooltip>
      </el-table-column>

      <el-table-column prop="createUserName" label="上报人" width="auto" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="140" align="center">
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" align="center" width="140">
      </el-table-column>
      <el-table-column label="操作" width="280" align="center">
        <template slot-scope="scope">
          <el-button @click="showDlg('info', scope.row)" icon="el-icon-document" size="mini" type="success" title="详情"
            plain>详情</el-button>
          <!-- <el-button
            type="primary"
            size="mini"
            @click="showDlg('edit', scope.row)"
            plain
            icon="el-icon-edit"
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="delFunc(scope.row)"
            plain
            icon="el-icon-delete"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination class="mt10" :total="total" :page.sync="searchForm.pageNo" :limit.sync="searchForm.pageSize"
      @pagination="searchFunc()" />
    <div class="clear"></div>

    <addEdit ref="addEdit" :dlgType="dlgType"></addEdit>
    <!-- <infoView ref="infoView"></infoView> -->
  </div>
</template>

<script>
import Pagination from "@/components/Pagination"; // 分页
import { getAction, deleteAction } from "@/api";
import addEdit from "./addEdit";

export default {
  components: {
    Pagination,
    addEdit,
  },
  data() {
    return {
      searchForm: {
        createTime: "",
        projectId: JSON.parse(window.localStorage.userInfo).projectId,
        modelId: "",
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      total: 0,
      listLoading: false,
      dlgType: "add",
      equModelsOptions: [],
      dataInfoJson: [],
      tableKey: false,
    };
  },
  created() { },
  mounted() {
    this.getEquModelsOptions()
  },
  methods: {
    showDlg(type, row) {
      let addEdit = this.$refs.addEdit;
      this.dlgType = type;
      if (type == "add") {
        addEdit.title = "添加设备运行记录";
      } else if (type == "info") {
        addEdit.title = "设备运行记录详情";
      } else {
        addEdit.title = "编辑设备运行记录";
      }
      if (type !== "add") {
        getAction(`/green/equ/operation-record/get?id=${row.id}`).then(
          (res) => {
            // console.log(res.data);
            let { code, data } = res.data;
            if (code == 200) {
              addEdit.getEquModelsOptions(this.searchForm.projectId);
              setTimeout(() => {
                addEdit.formData.projectName = JSON.parse(window.localStorage.userInfo).projectName;
                addEdit.formData.modelId = data ? data.modelId : "";
                addEdit.formData.modelName = data ? data.modelName : "";
                addEdit.formData.id = data ? data.id : "";
                addEdit.formData.projectId = this.searchForm.projectId
                  ? this.searchForm.projectId
                  : "";
                addEdit.modelChange(data.modelId);

                let dataInfoJson = JSON.parse(data.dataInfoJson);
                // console.log(dataInfoJson);
                let customFormData = {};
                dataInfoJson.map((item) => {
                  // console.log(item);
                  customFormData[item.id] = item.value;
                });
                // console.log(customFormData, "customFormData");
                addEdit.customFormData = customFormData;
              }, 100);
            } else {
              this.$message.error(res.data.msg);
            }
          }
        );
      }
      this.$refs.addEdit.dialogVisible = true;
    },
    searchFunc() {
      let { modelId, pageNo, pageSize, projectId, createTime } =
        this.searchForm;
      if (modelId == "" || projectId == "") {
        this.$message.warning("请先选择模板");
        return;
      }
      this.listLoading = true;

      // console.log(createTime, "createTime");
      if (createTime == null) {
        createTime = "";
      }
      getAction(
        `/green/equ/operation-record/page?createTime=${createTime}&projectId=${projectId}&modelId=${modelId}&pageNo=${pageNo}&pageSize=${pageSize}`
      ).then((res) => {
        this.listLoading = false;
        let { code, data } = res.data;
        if (code === "200") {
          let { list } = data;
          if (list.length == 0) {
            this.tableData = [];
            return;
          }
          // console.log(list, "list");
          this.dataInfoJson = JSON.parse(list[0].dataInfoJson);
          console.log(this.dataInfoJson, "this.dataInfoJson");
          // this.dataInfoJson.map(item => {
          //   list[0][item.id] = item.value
          // })
          // list[0].field4242949858245 = list[0].field4242949858245.toString()

          for (let i = 0; i < list.length; i++) {
            let dataInfoJson = JSON.parse(list[i].dataInfoJson);
            for (let j = 0; j < dataInfoJson.length; j++) {
              // console.log(dataInfoJson[j]);
              //单独处理图片
              if (
                dataInfoJson[j].name == "ImageUpload" &&
                dataInfoJson[j].value
              ) {
                let DeptPickerValue = [];
                dataInfoJson[j].value.map((item) => {
                  DeptPickerValue.push(item.name);
                });
                list[i][dataInfoJson[j].id] = DeptPickerValue.join();
              } else {
                list[i][dataInfoJson[j].id] = dataInfoJson[j].value;
              }
            }
          }

          // console.log(list);
          this.tableData = data.list
            ? JSON.parse(JSON.stringify(data.list))
            : [];
          // console.log(this.tableData, "this.tableData");
          this.total = data.total ? data.total : 0;
          this.tableKey = !this.tableKey;
          this.$nextTick(() => {
            this.$refs.tableBar.doLayout();
          });
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAction(`/green/equ/operation-record/delete?id=${row.id}`).then(
          (res) => {
            if (res.data.code === "200") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.searchFunc();
            } else {
              this.$message.error(res.data.msg);
            }
          }
        );
      });
    },
    getEquModelsOptions() {
      getAction(
        `/green/equ/models/page?projectId=${this.searchForm.projectId}&pageNo=1&pageSize=1000&`
      ).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          this.equModelsOptions = data.list ? data.list : [];
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
  },
};
</script>

<style></style>