import request from '@/utils/request'

// 分页 
export function page (data) {
  return request({
    url: `/nbiot/building/page`,
    method: 'post',
    data
  })
}

// 新增
export function saveOrUpdate (data) {
  return request({
    url: `/nbiot/building/saveOrU`,
    method: 'post',
    data
  })
}


// 删除
export function del (id) {
  return request({
    url: `/nbiot/building/del/${id}`,
    method: 'get'
  })
}