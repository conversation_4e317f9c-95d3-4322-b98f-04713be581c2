// 考勤组相关接口
import request from '@/utils/request'

// << 设备间管理
// 列表  page, limit label  isEquipRoom-0设备间 1设备 不传查所有
export function findEquioPage(data) {
  return request({
    url: '/iot/findEquioPage',
    method: 'post',
    data
  })
}
// 新增  isEquipRoom-0设备间 1设备
export function eqiAddOrUpdInfo(data) {
  return request({
    url: '/iot/eqi/addOrUpdInfo',
    method: 'post',
    data
  })
}
// 详情
export function findEquioById(id) {
  return request({
    url: `/iot/findEquioById/${id}`,
    method: 'get'
  })
}
// 删除
export function delEquioById(data) {
  return request({
    url: '/iot/delEquioById',
    method: 'post',
    data
  })
}
// 维修记录
export function getMaintenanceRecord(data) {
  return request({
    url: '/iot/repair/record/page',
    method: 'post',
    data
  })
}
// 新增维修记录
export function putRecord(data) {
  return request({
    url: '/iot/repair/record/put',
    method: 'post',
    data
  })
}
// 导出
export function equipExport(data) {
  return request({
    url: '/iot/equipExport',
    method: 'post',
    data
  })
}

// 获取设备间设备
export function findListByEquipId(equipId) {
  return request({
    url: `/iot/findListByEquipId/${equipId}`,
    method: 'get'
  })
}
// 更新坐标
export function updateCoordinate(data) {
  return request({
    url: `/iot/updateCoordinate`,
    method: 'post',
    data
  })
}
// 更新背景图
export function updateImgUrl(data) {
  return request({
    url: `/iot/updateImgUrl`,
    method: 'post',
    data
  })
}
// >> 设备间管理

// << 传感器节点
// 列表  page, limit label
export function findEquipNodePage(data) {
  return request({
    url: '/iot/findEquipNodePage',
    method: 'post',
    data
  })
}
// 新增 
export function eqinAddOrUpdInfo(data) {
  return request({
    url: '/iot/eqin/addOrUpdInfo',
    method: 'post',
    data
  })
}
// 详情
export function findEquipNodeById(id) {
  return request({
    url: `/iot/findEquipNodeById/${id}`,
    method: 'get'
  })
}
// 删除
export function delEquipNodeById(data) {
  return request({
    url: '/iot/delEquipNodeById',
    method: 'post',
    data
  })
}
// 设备档案 列表  page, limit label
export function findNodelArchivesPage(data) {
  return request({
    url: '/iot/findNodelArchivesPage',
    method: 'post',
    data
  })
}
// 设备档案 详情
export function findNodelArchivesById(id) {
  return request({
    url: `/iot/findNodelArchivesById/${id}`,
    method: 'get'
  })
}
// 初始化断路器地址
export function setDLQAddr(data) {
  return request({
    url: '/iot/setDLQAddr',
    method: 'post',
    data
  })
}
// 报警列表
export function protocolLoran(data) {
  return request({
    url: '/iot/protocolLoran',
    method: 'post',
    data
  })
}
// 关联摄像头
export function addCameraNode(data) {
  return request({
    url: '/iot/addCameraNode',
    method: 'post',
    data
  })
}
// 获取已关联摄像头
export function findCamerasByNodeId(id) {
  return request({
    url: `/iot/findCamerasByNodeId/${id}`,
    method: 'get'
  })
}
// 启用
// saapi/iot/open/{id}
// saapi/iot/stop/{id}
export function iotOpen(id) {
  return request({
    url: `/iot/open/${id}`,
    method: 'put'
  })
}
// 停用
export function iotStop(id) {
  return request({
    url: `/iot/stop/${id}`,
    method: 'put'
  })
}
// >> 传感器节点
// << 传感器执行计划
// 列表
export function planPage(data) {
  return request({
    url: '/iot/plan/page',
    method: 'post',
    data
  })
}
// 详情
export function planLoadNodePlanById(id) {
  return request({
    url: `/iot/plan/loadNodePlanById?id=${id}`,
    method: 'get'
  })
}
// 删除
export function planDelNodePlanById(id) {
  return request({
    url: `/iot/plan/delNodePlanById?id=${id}`,
    method: 'delete'
  })
}
// 新增/修改
export function planSaveOrUpdate(data) {
  return request({
    url: '/iot/plan/saveOrUpdate',
    method: 'post',
    data
  })
}
// 执行计划
export function planOpenNodePlan(id) {
  return request({
    url: `/iot/plan/openNodePlan?id=${id}`,
    method: 'get'
  })
}
// >> 传感器执行计划
// << 开关设备
export function findSwitchPage(data) {
  return request({
    url: '/iot/findSwitchPage',
    method: 'post',
    data
  })
}
// 打开、关闭开关
export function openOrCloseXy(data) {
  return request({
    url: '/iot/openOrCloseXy',
    method: 'post',
    data
  })
}

// >> 开关设备

// << 运行监控
// 列表
export function operationMonitoring(data) {
  return request({
    url: '/iot/operationMonitoring',
    method: 'post',
    data
  })
}
// 获取传感器信息
export function findNodeConfigs(id) {
  return request({
    url: `iot/findNodeConfigs/${id}`,
    method: 'get'
  })
}
// 获取 告警接口
export function findAbnormalAlarm(data) {
  return request({
    url: `iot/findAbnormalAlarm`,
    method: 'post',
    data
  })
}


// << 运行监控

// << 设备间配置
// 列表
export function equipRoomConfigPage(data) {
  return request({
    url: '/iot/equipRoomConfigPage',
    method: 'post',
    data
  })
}
// 详情
export function equipRoomConfig(id) {
  return request({
    url: `iot/equipRoomConfig/${id}`,
    method: 'get'
  })
}
// 删除
export function equipRoomConfigDel(id) {
  return request({
    url: `iot/equipRoomConfig/del/${id}`,
    method: 'get'
  })
}
// 新增，更新
export function equipRoomConfigAddOrUp(data) {
  return request({
    url: '/iot/equipRoomConfig/addOrUpd',
    method: 'post',
    data
  })
}

// >> 设备间配置

// << 系统遥测
export function findNodeTelemetryPage(data) {
  return request({
    url: '/iot/findNodeTelemetryPage',
    method: 'post',
    data
  })
}
// 单个读取
export function readData(data) {
  return request({
    url: '/iot/readData',
    method: 'post',
    data
  })
}
// 全部读取
export function readDataAll() {
  return request({
    url: '/iot/readDataAll',
    method: 'get'
  })
}
// >> 系统遥测
// << 摄像头管理
// 列表
export function cameraPage(data) {
  return request({
    url: '/iot/cameraPage',
    method: 'post',
    data
  })
}
// 新增
export function cameraAddOrUpd(data) {
  return request({
    url: '/iot/camera/addOrUpd',
    method: 'post',
    data
  })
}
// 删除
export function cameraDel(id) {
  return request({
    url: `/iot/camera/del/${id}`,
    method: 'get'
  })
}
// 详情
export function findNodesByCameraId(cameraId) {
  return request({
    url: `/iot/findNodesByCameraId/${cameraId}`,
    method: 'get'
  })
}
// >> 摄像头档案
// 新增
export function addCameraArchive(data) {
  return request({
    url: '/iot/addCameraArchive',
    method: 'post',
    data
  })
}
// 列表
export function pageCameraArchive(data) {
  return request({
    url: '/iot/pageCameraArchive',
    method: 'post',
    data
  })
}
// 删除
export function deleteCameraArchive(id) {
  return request({
    url: `/iot/deleteCameraArchive/${id}`,
    method: 'get'
  })
}
// >> 摄像头管理新
// 列表
export function pageFireCamera(data) {
  return request({
    url: '/iot/pageFireCamera',
    method: 'post',
    data
  })
}
// 详情
export function getFireCamera(id) {
  return request({
    url: `/iot/getFireCamera/${id}`,
    method: 'get',
  })
}
//新增
export function addFireCamera(data) {
  return request({
    url: '/iot/addFireCamera',
    method: 'post',
    data
  })
}
//重置密码
export function resetPasswordFireCamera(data) {
  return request({
    url: '/iot/resetPasswordFireCamera',
    method: 'post',
    data
  })
}
//校验密码
export function checkPassword(data) {
  return request({
    url: '/iot/checkPassword',
    method: 'post',
    data
  })
}
// 删除
export function deleteFireCamera(id) {
  return request({
    url: `/iot/deleteFireCamera/${id}`,
    method: 'get'
  })
}
// 本地录像
export function recordhistories(data) {
  return request({
    url: `/iot/recordhistories`,
    method: 'post',
    data
  })
}
// >> 网关管理
//新增
export function addOrUpdateGateway(data) {
  return request({
    url: '/iot/addOrUpdateGateway',
    method: 'post',
    data
  })
}
// 列表
export function pageGateway(data) {
  return request({
    url: '/iot/pageGateway',
    method: 'post',
    data
  })
}
// 删除
export function deleteGateway(id) {
  return request({
    url: `/iot/deleteGateway/${id}`,
    method: 'get'
  })
}
//获取报事点位树
export function findAreaTree(projectId) {
  return request({
    url: `/report/area/findAreaTree/${projectId}`,
    method: "get"
  });
}
//传感器绑定节点提交
export function equipNodeBindPoint(data) {
  return request({
    url: `/iot/equip-node/equipNodeBindPoint`,
    method: 'post',
    data
  });
}
//绑定节点详情
export function queryNodeBindPoint(id,type) {
  return request({
    url: `/iot/equip-node/queryNodeBindPoint?equipNodeId=${id}&alarmType=${type}`,
    method: 'get',
  });
}