<template>
  <!--消毒方式设置-->
  <div class="app-container">
    <div class="filter-container clearfix">
      <el-form inline @submit.native.prevent>
        <el-form-item label="关键字：">
          <el-input
            @keyup.enter.native="getList"
            placeholder="请输入名称"
            v-model="listQuery.label"
          >
            <i
              slot="suffix"
              @click="resetSearchItem(['label'])"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchItem"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="addItem"
          >添加</el-button
        >
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="calc(100vh - 372px)"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="医院名称">
          <template slot-scope="scope">
            <span>{{ scope.row.projectName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="320"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              type="success"
              size="mini"
              icon="el-icon-view"
              plain
              @click="infoItem(scope.row)"
              >详情</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              plain
              @click="editItem(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              plain
              @click="delItem(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        class="mt10"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
      <div class="clear"></div>
    </div>

    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="dlgShow"
      width="600px"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :rules="rules"
        :model="dlgData"
        label-position="right"
        label-width="50px"
      >
        <el-form-item label="名称" prop="name" label-width="70px">
          <el-input
            :disabled="this.dlgType == 'INFO'"
            v-model="dlgData.name"
            placeholder="请输入名称"
          />
        </el-form-item>

        <el-form-item label="备注" label-width="70px">
          <el-input
            type="textarea"
            :disabled="this.dlgType == 'INFO'"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button
          type="success"
          :loading="dlgLoading"
          @click="subDlg"
          icon="el-icon-check"
          v-if="this.dlgType != 'INFO'"
        >
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <branchDlgMul />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Cookie from "js-cookie";

import Pagination from "@/components/Pagination";
import * as utils from "@/utils";
import { medicalWasteConst } from "@/utils/const";
import branchDlgMul from "@/components/Dialog/wasteMan/branchDlgMul";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import {
  dismodePage,
  dismodeAdd,
  dismodeDel,
} from "@/api/disinfectMan/disinfectConfig";

let dlgDataEmpty = {
  id: "",
  name: "",
  remark: "",
  projectId: "",
  projectName: "",
};
export default {
  components: {
    Pagination,
    branchDlgMul,
    ElImageViewer,
  },
  data() {
    return {
      list: [],
      dlgShow: false, // 新增
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
      },
      total: 0,
      listLoading: false,
      totalItem: {},
      fields: [],
      stateList: medicalWasteConst.stateList,
      wasteTypeList: [],
      batchList: medicalWasteConst.batchList,
      userInfo: {},
      branchType: "0",
      dlgLoading: false,
      dlgType: "",
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      rules: {
        name: [{ required: true, message: "必填字段", trigger: "blur" }],
        branchNames: [
          { required: true, message: "必填字段", trigger: "change" },
        ],
      },
      dlgTotal: 0,

      choiceDate: "",
      title:""
    };
  },

  computed: {},

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    this.getList();
  },

  methods: {
    // 显示弹窗
    addItem() {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgType = "ADD";
      this.title="添加消毒方式"
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },
    getList() {
      dismodePage(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.data;
          this.total = res.data.page.total;
        }
      });
    },

    // 搜索框 清空单个条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.getList();
    },

    searchItem() {
      this.getList();
    },
    subDlg() {
      this.$refs.dlgForm.validate((valid) => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));
          sendObj.projectId = this.userInfo.projectId;
          sendObj.projectName = this.userInfo.projectName;
          dismodeAdd(sendObj).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.msg);
              this.getList();
              this.dlgShow = false;
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    // 编辑
    editItem(data) {
      let row = JSON.parse(JSON.stringify(data));
      this.dlgData.name = row.name;
      this.dlgData.id = row.id;
      this.dlgData.remark = row.remark;
      this.dlgType = "EDIT";
      this.title="修改消毒方式"
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },
    infoItem(data) {
      let row = JSON.parse(JSON.stringify(data));
      this.dlgData.name = row.name;
      this.dlgData.id = row.id;
      this.dlgData.remark = row.remark;
      this.dlgType = "INFO";
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },
    //删除
    delItem(data) {
      let title = "确认删除?";
      this.$confirm(title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        dismodeDel(data.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.getList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-image {
  display: block;
  width: 32px;
  height: 32px;
  margin: 0 auto;

  /deep/ .el-image__error {
    font-size: 12px;
  }
}

/deep/ .el-image-viewer__img {
  background: #ffffff;
}

.el-table--border /deep/ td.border-right,
.el-table--border /deep/ th.border-right {
  border-right: 2px solid #cccccc !important;
}
</style>