import request from '@/utils/request'
import { objToParam } from '@/utils/index'

// 获取列表
export function modelList(data) {
//   let paramStr = objToParam(data)
  return request({
    url: '/process/service/modelList',
    method: 'post',
    data
  })
}

// 创建流程
export function createModel(data) {
  return request({
    url: '/process/service/createModel',
    method: 'post',
    // headers: {"Content-Type": "multipart/form-data"},
		data
  })
}

// 添加流程
export function modelSave(data) {
  // get
  // let paramStr = objToParam(data)
  // return request({
  //   url: '/model/save' + paramStr,
  //   method: 'get'
  // })

  // post
  return request({
    url: '/process/service/model/save',
    method: 'post',
    data
  })
}

// 删除流程
export function fcDelete(modelId) {
  return request({
    url: '/process/service/delete?modelId=' + modelId,
    method: 'get'
  })
}

// 部署流程

export function deploy(modelId) {
    return request({
      url: '/process/service/deploy?modelId=' + modelId,
      method: 'get'
    })
  }
// 获取工作流
export function getGzl(modelId) {
  return request({
    url: '/process/service/model/json',
    method: 'post',
    data: { modelId: modelId }
  })
}

// 获取xml
export function modelXml(modelId) {
  let sendStr = objToParam({ modelId })
  return request({
    url: '/process/service/modelXml' + sendStr,
    method: 'get'
  })
}