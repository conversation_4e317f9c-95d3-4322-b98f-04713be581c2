<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item class="fr">
          <el-button icon='el-icon-back' size='mini' @click='backItem'>返回</el-button>
          <el-button icon='el-icon-plus' type="primary" size='mini' v-if="list.length == 0" @click='addItem'>添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="费用项名称">
          <template slot-scope="scope">
            <span>{{ scope.row.feeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="滞纳金名称">
          <template slot-scope="scope">
            <span>{{ scope.row.discountName	}}</span>
          </template>
        </el-table-column>

        <el-table-column label="滞纳金类型">
          <template slot-scope="scope">
            <span>{{ scope.row.discountTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑计费滞纳金设置" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :disabled="dlgType == 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="滞纳金类型" prop="discountType">
          <el-select v-model="dlgData.discountType" filterable clearable placeholder="请选择滞纳金类型">
            <el-option v-for="item in discountTypeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="滞纳金名称" prop="discountId">
          <el-select v-model="dlgData.discountId" filterable clearable placeholder="请选择滞纳金名称">
            <el-option v-for="item in discountList" :key="item.id" :label="item.discountName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { feeDiscountPage, findInfosById, addOrUpdate, delPayFeeConfigDiscountById } from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  discountType: '',
  discountId: '',
  configId: ''
}


export default {
  name: 'billingSetupDesc',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {
      id: '',
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        discountId: [{ required: true, message: '必填字段', trigger: 'change' }],
        discountType: [{ required: true, message: '必填字段', trigger: 'change' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      formData: {},
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        feeTypeCd: ''
      },
      discountList: [],
      userInfo: {},
      costTypeList: [],
      computingFormulaList: [],

      discountTypeList: [
        {
          id: 2,
          name: '违约'
        }
      ],
    }
  },

  created () {
    if (this.$route.params.id) {
      this.id = this.$route.params.id
      this.getList()
    }
    this.getDiscountList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取小区列表
    getDiscountList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      feeDiscountPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.discountList = res.data.data
        }
      })
    },

    formatList (data) {
      for (let i of this.list) {
        i.feeName = data.feeName
      }
    },

    // 获取数据
    getList () {
      // if (utils.isNull(this.listQuery.communityId)) {
      //   this.$message.warning("请选择小区")
      //   return
      // }
      this.count++
      this.listLoading = true
      findInfosById(this.id).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.formData = JSON.parse(JSON.stringify(res.data.data))
          this.list = res.data.data.feeDiscountList ? JSON.parse(JSON.stringify(res.data.data.feeDiscountList)) : []
          this.formatList(res.data.data)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 返回
    backItem () {
      this.$router.push({ path: `/communityProperty/costMan/billingSetup` })
    },


    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.configId = this.id
          postParam.communityId = this.formData.communityId
          this.dlgLoading = true
          addOrUpdate(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgType = type
      this.dlgShow = true
    },


    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delPayFeeConfigDiscountById(this.id, data.id).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


