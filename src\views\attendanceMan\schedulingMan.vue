<template>
  <!-- 考勤管理 - 人脸审核 -->
  <div class="app-container" ref="schedulingMan">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="排班月份">
          <el-date-picker v-model="listQuery.date" value-format="yyyy-MM" format="yyyy-MM" type="month" placeholder="排班月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-radio-group @change="getList" v-model="listQuery.branchType">
            <el-radio label="0">当前部门</el-radio>
            <el-radio label="1">当前及所属部门</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.branchName" @focus="showBranchDlg" :title='listQuery.branchName' placeholder="选择部门" readonly>
            <i @click='resetSearchItem(["branchId", "branchName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.label" placeholder='请输入姓名'>
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-date' type="primary" size='mini' @click='addItem'>排班</el-button>
        <el-button icon='el-icon-download' type="primary" size='mini' @click='exportExcel'>导出</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' ref="schedulingTable" height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="姓名" width="80px" align="center" fixed>
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属部门" width="150px" show-overflow-tooltip fixed>
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="考勤组" width="150px" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.adeGroupName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="排班" align="center">
          <el-table-column align="center" v-for="(item, index) in dateList" :key="index" :label="item.day + '/' + item.week" :width="item.isSplit == 1 ? 75 : 50" :render-header="renderheader">
            <template slot-scope="scope">
              <span class="label-item" v-if="scope.row.schedulingList[index] && scope.row.schedulingList[index].isNull"></span>
              <template v-else>
                <template v-if="scope.row.schedulingList[index] && scope.row.schedulingList[index].isSplit == 0">
                  <span @click.stop="editItem(scope.row.schedulingList[index], 0, $event)" class="label-item" :class="'label' + scope.row.schedulingList[index].schedulingType + (scope.row.schedulingList[index].adeGroupId == scope.row.schedulingList[index].schedulingGroupId ? '' : ' diff')">
                    {{ scope.row.schedulingList[index].schedulingLabel}}
                  </span>
                </template>
                <template v-else>
                  <span @click.stop="editItem(scope.row.schedulingList[index], 1, $event)" class="label-item" :class="'label' + scope.row.schedulingList[index].firstHalfType + (scope.row.schedulingList[index].adeGroupId == scope.row.schedulingList[index].firstHalfGroupId ? '' : ' diff')">
                    {{ scope.row.schedulingList[index].schedulingLabel.split(",")[0]}}
                  </span>
                  <span @click.stop="editItem(scope.row.schedulingList[index], 2, $event)" class="label-item" :class="'label' + scope.row.schedulingList[index].secondHalfType + (scope.row.schedulingList[index].adeGroupId == scope.row.schedulingList[index].secondHalfGroupId ? '' : ' diff')">
                    {{ scope.row.schedulingList[index].schedulingLabel.split(",")[1]}}
                  </span>
                </template>
              </template>
            </template>
          </el-table-column>
        </el-table-column>

      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-menu ref="schedulingMenu" class="scheduling-menu" :collapse="true">
      <el-submenu index="1">
        <template slot="title">
          <span>修改考勤组 <i class="el-icon-arrow-right"></i></span>
        </template>
        <el-menu-item-group>
          <el-menu-item @click="updateSchedule(1, item.id)" v-for="(item,index) in groupList" :index="'g_' + item.id" :key="index">{{item.groupName}}</el-menu-item>
          <!-- <el-menu-item>
            <el-select v-model="value" placeholder="请选择考勤组" filterable>
              <el-option v-for="item in groupList" :key="item.id" :label="item.groupName" :value="item.id">
              </el-option>
            </el-select> 
          </el-menu-item> -->
        </el-menu-item-group>
      </el-submenu>
      <el-submenu index="2">
        <template slot="title">
          <span>修改假期 <i class="el-icon-arrow-right"></i></span>
        </template>
        <el-menu-item-group>
          <el-menu-item @click="updateSchedule(2, item.id)" v-for="(item,index) in holidayList" :index="'h_' + item.id" :key="index">{{item.name}}</el-menu-item>
        </el-menu-item-group>
      </el-submenu>
      <el-menu-item index="3" v-if="selectCol.isSplit == 0" @click="updateSchedule(3)">
        <span>拆分半天班</span>
      </el-menu-item>
      <el-menu-item index="4" v-else @click="updateSchedule(4)">
        <span>合并半天班</span>
      </el-menu-item>
    </el-menu>

    <el-dialog :close-on-click-modal='false' :title="'排班'" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :model="dlgData" :rules="dlgRules" label-width="120px">
        <el-form-item label="选择员工" prop="userName">
          <el-input v-model="dlgData.userName" @focus="showUserDlg" placeholder="选择员工" readonly style="width:300px;">
          </el-input>
        </el-form-item>
        <el-form-item label="日期" prop="createDate">
          <el-date-picker v-model="dlgData.createDate" value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="date" placeholder="排班日期" style="width:300px;">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button type='success' :loading='dlgLoading' @click="subDlg()" icon="el-icon-check">
          确定
        </el-button>
      </div>
    </el-dialog>

    <userDlg />
    <DialogBranch @superFunc="superBranch" :superDlgShow.sync="dlgShowBranch" :superSelectId="listQuery.branchId" :superSelectName="listQuery.branchName" :superPermission="true" />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { schedulingPage, saveSchedulingByUserId, updateSchedule } from '@/api/attendanceMan/schedulingMan.js'
import { findGroupByDynamic } from '@/api/attendanceMan/groupMan.js'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import userDlg from '@/components/Dialog/platformMan/userDlg'
import DialogBranch from '@/components/Dialog/platformMan/DialogBranch'

let dlgDataEmpty = {
  userId: '',
  userName: '',
  createDate: ''
}

export default {
  components: {
    Pagination,
    userDlg,
    DialogBranch
  },
  data () {
    return {
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',  // ADD\EDIT
      dlgRules: {
        userName: [{ required: true, message: '必填字段', trigger: 'change' }],
        createDate: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      showPos: '',
      timer: null,
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        date: utils.getToday().substr(0, 7),
        branchId: '',
        branchName: '',
        branchType: '0'
      },
      dateList: [],

      selectRow: {},
      selectCol: {},
      schedulingType: '',
      halfType: '',
      type: '',

      groupList: [],
      holidayList: [],

      userInfo: {},
      count: 0,

      dlgShowBranch: false
    }
  },
  computed: {
    ...mapGetters('platformMan/userDlg', {
      userId: 'userId',
      userName: 'userName'
    }),
  },
  watch: {
    userId (val) {
      this.dlgData.userId = val
    },

    userName (val) {
      this.dlgData.userName = val
    },
  },

  created () {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.getGroupList()
    utils.getDataDict(this, 'holidayType', 'holidayList')
    this.getDateList()
  },

  mounted () {
    this.$nextTick(() => {
      this.$refs.schedulingMan.addEventListener('click', () => {
        this.hideMenu()
      })
    })
  },

  methods: {
    // 表格头换行
    renderheader (h, { column, $index }) {
      return h('span', {}, [
        h('span', {}, column.label.split('/')[0]),
        h('br'),
        h('span', {}, column.label.split('/')[1])
      ])
    },

    // 显示部门树
    showBranchDlg () {
      this.dlgShowBranch = true
    },

    superBranch (params) {
      this.listQuery.branchId = params.selectId
      this.listQuery.branchName = params.selectName
    },


    // 显示人员dlg
    showUserDlg () {
      let userId = this.dlgData.userId
      let userName = this.dlgData.userName
      this.$store.commit('platformMan/userDlg/SET_USERID', userId)
      this.$store.commit('platformMan/userDlg/SET_USERNAME', userName)
      this.$store.commit('platformMan/userDlg/SET_DLGSHOW', true)
    },

    // 获取考勤组数据
    getGroupList () {
      let postParam = {
        page: 1,
        limit: 100
      }
      findGroupByDynamic(postParam).then(res => {
        if (res.data.code == 200) {
          this.groupList = JSON.parse(JSON.stringify(res.data.data))
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 修改排班menu位置
    editItem (col, halfType, event) {
      this.selectCol = JSON.parse(JSON.stringify(col))
      this.halfType = halfType
      this.$refs.schedulingMenu.$el.style.display = 'block'
      this.$refs.schedulingMenu.$el.style.left = event.clientX + 'px'
      this.$refs.schedulingMenu.$el.style.top = event.clientY + 'px'
      if (event.clientX + 200 > document.documentElement.clientWidth) {
        this.$refs.schedulingMenu.$el.style.left = event.clientX - 200 + 'px'
      }
    },

    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/workade/exportSchedulings?` + param
      console.log(sendUrl)
      window.open(sendUrl)
    },

    // 修改排班
    updateSchedule (type, id) {
      this.schedulingType = type
      let postParam = {
        schedulingId: this.selectCol.id,
        type: this.schedulingType
      }

      if (this.schedulingType == 2) {
        postParam.type = this.schedulingType
        postParam.schedulingType = id
        postParam.halfType = this.halfType
      }
      else if (this.schedulingType == 1) {
        postParam.type = this.schedulingType
        postParam.groupId = id
        postParam.halfType = this.halfType
      }
      updateSchedule(postParam).then(res => {
        if (res.data.code == 200) {
          this.hideMenu()
          this.getList()
          this.$message.success(res.data.msg)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 隐藏菜单
    hideMenu () {
      this.$refs.schedulingMenu.$el.style.display = 'none'
    },

    // 重置
    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取日期数组
    getDateList () {
      this.dateList = []
      let maxDate = utils.getMaxDate(this.listQuery.date)
      for (let i = 1; i <= maxDate; i++) {
        let day = i < 10 ? '0' + i : i.toString()
        let date = this.listQuery.date + '-' + day
        this.dateList.push({
          date,
          day,
          week: utils.getWeek(date)
        })
      }
    },

    // 根据数据格式化日期数据
    formatDateList (list) {
      for (let i of list) {
        let diff = this.dateList.length - i.schedulingList.length
        if (diff > 0) {
          for (let j = diff; j > 0; j--) {
            i.schedulingList.unshift({
              isSplit: 0,
              schedulingLabel: '',
              isNull: true,
              goWorkDate: this.listQuery.date + "-" + (j < 10 ? '0' + j : j)
            })
          }
        }
      }
      // console.log('===this.dateList', this.dateList)
      // console.log('===list', list)
      for (let i =0;i<this.dateList.length;i++) {
        for (let j=0;j<list.length;j++) {
          // console.log('============',i,j)

          // console.log(list[j])


          // console.log('list[j].schedulingList[i]', list[j].schedulingList[i])
          // console.log('list[j].schedulingList[i].isSplit',  list[j].schedulingList[i].isSplit)
          if (list[j].schedulingList[i] && list[j].schedulingList[i].isSplit == 1) {
            this.dateList[i].isSplit = 1
            break
          }
        }
      }
    },

    // 获取数据
    getList () {
      if (utils.isNull(this.listQuery.date)) {
        this.$message.warning("请选择排班月份")
        return
      }
      this.getDateList()
      this.count++
      this.listLoading = true
      schedulingPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          let list = res.data.data ? JSON.parse(JSON.stringify(res.data.data)) : []
          this.formatDateList(list)
          this.list = list
          this.total = res.data.page ? res.data.page.total : 0
          this.$nextTick(() => {
            this.$refs.schedulingTable.doLayout();
          });
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 排班
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          this.dlgLoading = true
          saveSchedulingByUserId(postParam.userId, postParam.createDate).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })

    },
  },

}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.label-item {
  display: inline-block;
  font-size: 12px;
  font-weight: bold;
  width: 24px;
  height: 24px;
  background: #ccc;
  color: #333;
  cursor: pointer;
  white-space: nowrap;
  border-radius: 4px;
  overflow: hidden;
}

.label-item.label0,
.label-item.label7 {
  background: #47aa8d;
  color: #fff;
}

.label-item.diff {
  background: #33ffff;
  color: #333;
}

.label-item.label1,
.label-item.label2,
.label-item.label3,
.label-item.label4,
.label-item.label5,
.label-item.label6 {
  background: #ccc;
  color: #333;
}

.label-item.split {
  display: inline-block;
  margin-right: 4px;
}
.label-item.split:last-child {
  margin-right: 0;
}

.el-dropdown.split {
  display: inline-block;
}

.el-dropdown-menu--mini .el-dropdown-menu__item {
  text-align: center;
}

.el-menu--collapse {
  width: 150px;
}

.el-menu--collapse > .el-menu-item span,
.el-menu--collapse > .el-submenu > .el-submenu__title span {
  width: 100%;
  height: auto;
  visibility: visible;
}

.scheduling-menu {
  display: none;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
}

/deep/ .el-table td {
  border-bottom: 1px solid #666;
}

/deep/ .m-small-table {
  .el-table__fixed {
    height: auto !important; // 此处的important表示优先于element.style
    bottom: 18px; // 改为自动高度后，设置与父容器的底部距离，则高度会动态改变
  }
  .el-table__fixed-body-wrapper {
    padding-bottom: 18px;
  }
}

/deep/ .el-table th.gutter {
  display: table-cell !important;
}
</style>


