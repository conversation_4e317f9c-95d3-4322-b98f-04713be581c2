import request from "@/utils/request";

// <<<<<<<<<<<<<< 系统设备检测

// << 1 系统设备检测
export function equipmentTestingPage (data) {
  return request({
    url: `/iot/equipmentTestingPage`,
    method: "post",
    data
  });
}
// >> 1 系统设备检测
// << 5 短信提醒查询
export function findSmsAlertPage (data) {
  return request({
    url: `/iot/findSmsAlertPage`,
    method: "post",
    data
  });
}
// >> 5 短信提醒查询

// << 异常告警管理
// 列表分页
export function findAbnormalAlarmPage (data) {
  return request({
    url: `/iot/findAbnormalAlarmPage`,
    method: "post",
    data
  });
}

// 列表分页 根据类型
export function pageAbnormalOfType (data) {
  return request({
    url: `/iot/pageAbnormalOfType`,
    method: "post",
    data
  });
}

// 报警详情列表
export function findAbnormalAlarmById (id) {
  return request({
    url: `/iot/findAbnormalAlarmById/${id}`,
    method: "get"
  });
}
// >> 异常告警管理
// >>>>>>>>>>>>>> 系统设备检测

// <<<<<<<<<<<<<< 设备用能管理
export function equipmentEnergyQuery (data) {
  return request({
    url: `/iot/equipmentEnergyQuery`,
    method: "post",
    data
  });
}
// >>>>>>>>>>>>>> 设备用能管理

// >> 设备安全监控 - 异常告警处理
export function handleAlarm (data) {
  return request({
    url: `/iot/handleAlarm`,
    method: 'post',
    data
  })
}

// >> 设备安全监控 - 异常告警忽略
export function ignoreAlarm (data) {
  return request({
    url: `/iot/ignoreAlarm`,
    method: 'post',
    data
  })
}
