<template>
  <el-dialog :close-on-click-modal='false' :title="'选择工作项'" :visible.sync="dlgShow" width="766px">
    <div class="clear-container clearfix">
      <div class="tree-container">
        <div class="filter-container">
          <span class="fbold">待选</span>
        </div>
        <el-tree ref="workItemTree" show-checkbox highlight-current node-key="id" :props="defaultProps" @check="treeCheck" :check-strictly='true' :default-expanded-keys="defaultOpenList" :data="list" :filter-node-method="filterNode" @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" :expand-on-click-node="false">
        </el-tree>
      </div>
      <div class="table-container">
        <span class="fbold cBlue">已选</span>
        <el-table ref="tableBar" class='m-small-table' :data="selectTableList" border fit highlight-current-row>
          <el-table-column type="index" label="序号" align="center" width="50">
          </el-table-column>
          <el-table-column label="工作项" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.rulesName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="规程内容" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.rulesSettingNum }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button icon="el-icon-delete" type="danger" size="mini" plain @click="delItem(scope.$index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-close" @click="closeDlg">
        取 消
      </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'


export default {
  components: {
  },
  data() {
    return {

      filterBranch: "",

      list: [],

      selectKeys: [],

      selectTableList: [],

      defaultOpenList: [],  // 默认展开
      defaultProps: {
        children: 'children',
        label: 'name'
      },
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.qualityMan.workItemDlgMul.dlgShow
      },
      set: function (val) {
        this.$store.commit('qualityMan/workItemDlgMul/SET_DLGSHOW', val)
      }
    },

    workType: {
      get: function () {
        return this.$store.state.qualityMan.workItemDlgMul.workType
      },
      set: function (val) {
        this.$store.commit('qualityMan/workItemDlgMul/SET_WORKTYPE', val)
      }
    },

    workName: {
      get: function () {
        return this.$store.state.qualityMan.workItemDlgMul.workName
      },
      set: function (val) {
        this.$store.commit('qualityMan/workItemDlgMul/SET_WORKNAME', val)
      }
    },

    dlgType: {
      get: function () {
        return this.$store.state.qualityMan.workItemDlgMul.dlgType
      },
      set: function (val) {
        this.$store.commit('qualityMan/workItemDlgMul/SET_DLGTYPE', val)
      }
    },

    workItemList: {
      get: function () {
        return this.$store.state.qualityMan.workItemDlgMul.list
      },
      set: function (val) {
        this.$store.commit('qualityMan/workItemDlgMul/SET_LIST', val)
      }
    },

  },

  watch: {
    filterBranch(val) {
      this.$refs.workItemTree.filter(val);
    },

    dlgShow(val) {
      if (val) {
        if (this.dlgType == 'workFlowTmpl') {
          this.selectTableList = JSON.parse(JSON.stringify(this.workItemList))
        } else {
          this.selectTableList = []
        }
        this.getList()
      }
    },

    workItemList(val) {
    },

  },

  created() {
  },

  methods: {

    // 树节点展开
    handleNodeExpand(data) {
      // 保存当前展开的节点
      let flag = false
      this.defaultOpenList.some(item => {
        if (item === data.id) { // 判断当前节点是否存在， 存在不做处理
          flag = true
          return true
        }
      })
      if (!flag) { // 不存在则存到数组里
        this.defaultOpenList.push(data.id)
      }
    },

    // 树节点关闭
    handleNodeCollapse(data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i
        }
      })
    },

    // 递归全选当前下的节点
    selectAllNode(childrenList, type) {
      for (let item of childrenList) {
        // 全选，全部取消
        if (type == 'select') {
          if (!this.selectKeys.includes(item.id)) {
            if (item.state == 0) {
              this.selectKeys.push(item.id)
              this.selectList.push(item)
            }
          }
        } else {
          if (this.selectKeys.includes(item.id)) {
            let mIndex = this.selectKeys.indexOf(item.id)

            this.selectKeys.splice(mIndex, 1)
            this.selectList.splice(mIndex, 1)
          }
        }
        if (item.children) {
          this.selectAllNode(item.children, type)
        }
      }
    },

    // 树节点点击
    treeCheck(checkedNodes, checkedKeys) {
      if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        // select-全选；remove-取消全选
        this.selectAllNode(checkedNodes.children, 'select')
      } else {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        this.selectAllNode(checkedNodes.children, 'remove')
      }
      this.$refs.workItemTree.setCheckedKeys(this.selectKeys)

      let checkNodes = this.$refs.workItemTree.getCheckedNodes()
      if (checkNodes.length > 0) {
        this.getByWorkIds()
      } else {
        this.selectTableList = []
      }
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    setCheckedKeys() {
      this.$nextTick(() => {
        let currentKey = []
        for (let i in this.selectTableList) {
          currentKey.push(parseInt(this.selectTableList[i].workItemId))
        }
        this.selectKeys = currentKey
        this.$refs.workItemTree.setCheckedKeys(currentKey)
      })
    },

    // 删除项
    delItem(idx) {
      this.selectTableList.splice(idx, 1)
      this.setCheckedKeys()
    },

    // 批量获取工作项内容
    getByWorkIds() {
      let workIdList = []
      let workNameList = []
      for (let i of this.selectList) {
        if (i.type == 1) {
          workIdList.push(i.id)
          workNameList.push(i.name)
        }
      }
      if (workIdList.length == 0) {
        return
      }

      let postParam = {
        workIds: workIdList.join(","),
        workNames: workNameList.join(","),
        workName: this.workName,
        work: this.workType
      }
      getByWorkIds(postParam).then(res => {
        if (res.data.code == 200) {
          if (this.selectList.length == 0) {
            return
          }
          this.selectTableList = res.data.data
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 递归
    recursiveList(list) {
      let fn = (list) => {
        for (let i of list) {
          i.disabled = i.state == 1
          if (i.children.length > 0) {
            fn(i.children)
          }
        }
      }
      fn(list)
    },

    // 获取树列表
    getList() {
      this.list = []
      let postParam = {
        name: this.workName,
        value: this.workType
      }
      return
      loadWorkItemsTree(postParam).then(res => {
        if (res.data.code == 200) {
          this.recursiveList(res.data.data)
          this.list = JSON.parse(JSON.stringify(res.data.data))
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = [res.data.data[0].id]
          }
          this.setCheckedKeys()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    subDlg() {
      if (this.selectTableList.length == 0) {
        this.$message.error("请选择工作项")
        return
      }
      this.$store.commit('qualityMan/workItemDlgMul/SET_LIST', JSON.parse(JSON.stringify(this.selectTableList)))
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('qualityMan/workItemDlgMul/SET_DLGSHOW', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 660px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

.clear-container {
  height: 100%;
  .tree-container {
    float: left;
    width: 180px;
    height: 100%;
    .el-tree {
      background: #f2f2f2;
      height: calc(100% - 26px);
      margin-top: 10px;
      overflow-y: auto;
      padding-top: 10px;
    }
  }

  .table-container {
    float: right;
    width: calc(100% - 200px);
    height: 100%;
    .el-table {
      margin-top: 10px;
    }
  }
}
</style>