<template>
  <div class="app-container bpmn-viewer" style="padding: 0">
    <div class="containers" style="height: 100%">
      <div class="canvas" ref="canvas" style="height: 100%"></div>
    </div>
  </div>
</template>

<script>
import * as utils from '@/utils'
import { postAction, getAction, formAction } from '@/api'

// -- << bpmnViewer
import BpmnViewer from 'bpmn-js/lib/Viewer'
import MoveModule from 'diagram-js/lib/features/move'
import ModelingModule from 'bpmn-js/lib/features/modeling'
import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas'
import zoomScroll from '/static/bpmnjs/components/zoomScroll'

import '/static/bpmnjs/css/diagram-js.css'
import '/static/bpmnjs/vendor/bpmn-font/css/bpmn-embedded.css'
import '/static/bpmnjs/css/app.css'
// >>

export default {
  components: {},
  data() {
    return {
      processData: '',

      bpmnXML: '',
      historyNodeMap: {},
    }
  },

  created() {
    this.processData = this.$route.query
  },
  mounted() {
    this.getBpmnXml()
  },

  methods: {
    // 获取bpmnXML
    getBpmnXml() {
      let sendObj = {
        deploymentId: this.processData.deploymentId,
        resourceName: this.processData.sourceName,
        // deploymentId: 'ece95bf9-37c3-11ed-9566-20040fea4e80',
        // resourceName: '%E4%BC%98%E6%83%A0%E7%94%B3%E8%AF%B7.bpmn',
      }
      let sendObjStr = utils.objToParam(sendObj)
      getAction(`/act/process/getXml` + sendObjStr).then((res0) => {
        this.bpmnXML = res0.data
        this.getHistoryNode()
      })
    },
    // 获取走完的节点
    getHistoryNode() {
      getAction(`/act/process/show/` + this.processData.processInstanceId).then((res0) => {
        let res = res0.data
        if (res.code == '200') {
          this.historyNodeMap = res.data
          this.bpmnInitFunc(this.bpmnXML)
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 绘制bpmn
    bpmnInitFunc(diagramXML) {
      console.log('----bpmnInitFunc')
      let canvas = this.$refs.canvas
      this.bpmnViewer = new BpmnViewer({
        container: canvas,
        additionalModules: [
          // MoveModule, // 可以调整元素
          ModelingModule, // 基础工具 MoveModule、SetColor 等依赖于此
          MoveCanvasModule, // 移动整个画布
          zoomScroll, // 放大缩小
        ],
      })
      this.bpmnViewer.importXML(diagramXML).then(() => {
        const canvas2 = this.bpmnViewer.get('canvas')
        canvas2.zoom('fit-viewport', 'auto') // 居中
        this.setBpmnColor() // 设置颜色
      })
    },
    // 设置颜色
    setBpmnColor() {
      // viewer模式下设置节点颜色
      var canvas = this.bpmnViewer.get('canvas')

      for (let typeKey in this.historyNodeMap) {
        let itemList = this.historyNodeMap[typeKey]
        console.log('----typeKey', typeKey)
        for (let nodeId of itemList) {
          if (!utils.isNull(nodeId)) {
            canvas.addMarker(nodeId, typeKey)
            console.log('nodeId', nodeId)
            console.log('typeKey', typeKey)
          }
        }
      }

      // 编辑模式下设置节点颜色
      // keyList = ['StartEvent_1', 'Flow_1ynejj9', 'Activity_0rnalyu', 'Flow_1q9nhsn', 'Activity_1muncf5']
      // let elementRegistry = this.bpmnViewer.get('elementRegistry')
      // let modeling = this.bpmnViewer.get('modeling')

      // console.log('-------elementRegistry', elementRegistry)
      // console.log('-------modeling', modeling)

      // let shapes = []
      // keyList.forEach((id) => {
      //   let shape = elementRegistry.get(id)
      //   shapes.push(shape)
      // })
      // let colorJson = { stroke: color, fill: 'yellow' }
      // modeling.setColor(shapes, colorJson)
    },
  },
  beforeDestroy() {
    this.bpmnViewer.destroy()
  },
}
</script>
<!-- scoped -->
<style rel="stylesheet/scss" lang="scss">
.bpmn-viewer .highLine .djs-visual > :nth-child(1) {
  stroke: #39b54a !important;
  // fill: rgba(0, 80, 0, 0.4) !important;
  fill: #d7f0db !important;
}
.bpmn-viewer .highPoint .djs-visual > :nth-child(1) {
  stroke: #39b54a !important;
  fill: #d7f0db !important;
}
.bpmn-viewer .waitingToDo .djs-visual > :nth-child(1) {
  stroke: #fbbd08 !important;
  fill: #fef2ce !important;
}
.bpmn-viewer .iDo .djs-visual > :nth-child(1) {
  stroke: #0081ff !important;
  fill: #cce6ff !important;
}

.bjs-powered-by {
  display: none !important;
}
</style>


