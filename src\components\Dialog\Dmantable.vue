<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' title="选择员工" top='30px' :visible.sync="dmanTableState" width='900px' append-to-body>
      <div>
        <!-- 搜索 -->
        <div class="filter-container">
          <el-form ref="searchForm" class='n-search' :model="listQuery" label-width="90px" @submit.native.prevent>
            <div class="n-search-bar">
              <div class='n-search-item fl'>
                <el-form-item label="关键字：">
                  <el-input @keyup.enter.native='searchFunc' class='m-shaixuan-input' placeholder='员工姓名/工号' v-model="listQuery.str" style="width:200px">
                    <i @click='resetSearchItem(["str"])' slot="suffix" class="el-input__icon el-icon-error"></i>
                  </el-input>
                  <el-select v-model="listQuery.state" placeholder="请选择状态" style="width: 180px;">
                    <el-option v-for="item in gwSelect" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class='n-search-item n-search-item-r fr'>

                <el-button icon='el-icon-search' type="success" size='mini' class="search-right-btn" @click='searchFunc'>搜索</el-button>
                <el-button type="danger" size='mini' class="search-right-btn" @click='emptyFunc'>重置</el-button>
              </div>
              <div class="clear"></div>
            </div>

          </el-form>
        </div>

        <!-- 员工 -->
        <el-table ref="userDom" v-loading="listLoading" :data="list" border fit highlight-current-row max-height='367px' class='m-small-table' @row-click='rowClick' show-overflow-tooltip='true'>
          <el-table-column label="" align="center" width="50">
            <template slot-scope="scope">
              <el-radio v-model="selectedId" :label="scope.row.id" style="width: 16px;"><span></span></el-radio>
            </template>
          </el-table-column>
          <el-table-column label="序号" prop="index" align="center" width="60">
            <template slot-scope="scope">
              <span>{{ scope.row.index }}</span>
            </template>
          </el-table-column>

          <el-table-column label="员工姓名" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.userName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="状态" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.stateText }}</span>
            </template>
          </el-table-column>

          <el-table-column label="定位" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.punchBranch }}</span>
            </template>
          </el-table-column>
          <el-table-column label="定位时间" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.lastPunchTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="本日任务数" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.taskNum }}</span>
            </template>
          </el-table-column>
        </el-table>

        <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
        <div class="clear"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
      <Bmtree />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

// 数据接口
import { findOrgBranchAll, findSysUserAll } from '@/api/dataDic'
import { findTaskUserDynamic } from "@/api/dispatchCenter"  // 查部门，根据部门查员工
import { findPostLike } from '@/api/postMan'  // 查岗位

// 页面组件
import Pagination from '@/components/Pagination'
import Bmtree from '@/components/Dialog/Bmtree'  // 部门弹窗

let listQueryEmpty = {
  page: 1,
  size: 10,
  userId: "",
  str: '',
  state: ""
}

export default {
  components: { Pagination, Bmtree },
  data() {

    return {
      isShow: false,
      list: [],
      listLoading: false,
      total: 0,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      selectedId: '',
      gwSelect: [{ "name": "离线", "id": "1" }, { "name": "在线", "id": "2" }, { "name": "忙碌", "id": "3" }]
    }
  },
  computed: {
    ...mapGetters([

    ]),
    dmanTableState: {
      get: function () {
        let state = this.$store.getters.dmanTableState
        if (state === true) {
          this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
          this.dmanTableUserId = ''
          this.dmanTableUserName = ''
          this.getList()
        }
        return state
      },
      set: function (newVal) {
        this.$store.commit('SET_DMANTABLESTATE', newVal)
      }
    },
    // id
    dmanTableUserId: {
      get: function () {
        let userId = this.$store.getters.dmanTableUserId
        console.log("userId", userId)
        if (userId) {
          this.selectedId = userId
        }
        return userId
      },
      set: function (newVal) {
      }
    },
  },
  watch: {

  },
  created() {
  },
  methods: {
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },
    // 搜索事件
    searchFunc() {
      // alert('search')
      this.listQuery.page = 1
      this.listQuery.size = 20
      this.getList()
    },
    // 清空搜索条件
    emptyFunc() {
      this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
      this.gwSelect = []
      this.getList()
    },

    // 表格行 点击事件
    rowClick(row, column, event) {
      this.selectedId = row.id
    },
    // 获取数据
    getList() {
      this.list = []
      this.selectedId = ''
      this.listLoading = true
      // return false
      findTaskUserDynamic(this.listQuery).then(res => {

        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg

        if (code === '200') {
          let data = res.data.data
          let list = res.data.list
          this.total = data.total
          for (let i = 0; i < list.length; i++) {
            let item = list[i]
            item.index = (this.listQuery.page - 1) * this.listQuery.size + i + 1
          }
          this.list = JSON.parse(JSON.stringify(list))

        } else {
          this.$message.error(msg)
        }

      })
    },

    // 选择部门提交
    bumenOkFunc() {
      if (this.selectedId === '') {
        this.$message({
          type: 'warning',
          message: '请选择员工'
        })
      } else {
        let selectedObj = this.list.filter(item => {
          return item.id === this.selectedId
        })

        this.$store.commit('SET_DMANTABLEUSERID', '')
        this.$store.commit('SET_DMANTABLEUSERNAME', '')
        setTimeout(() => {
          this.$store.commit('SET_DMANTABLEUSERID', selectedObj[0].userId)
          this.$store.commit('SET_DMANTABLEUSERNAME', selectedObj[0].userName)
          this.closeDialog()
        }, 50)
      }
    },
    // 关闭弹窗 
    closeDialog() {
      this.isShow = false
      this.$store.commit('SET_DMANTABLESTATE', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.user-list {
  height: 400px;
  border: 1px solid red;
  border-radius: 3px;
}
</style>