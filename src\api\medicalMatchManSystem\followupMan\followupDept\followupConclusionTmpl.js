import request from '@/utils/request'

/*
*随访结论模板
*/

// 分页查询 
export function findConclusionTemplateDynamic(data) 
{
	return request({
		url: `/follow/findConclusionTemplateDynamic`,
		method: 'post',
		data
	})
}

// 删除
export function updateConclusionTemplate(data)
{
	return request({
		url: `/follow/updateConclusionTemplate`,
		method: 'post',
		data
	})
}

// 新增/修改
export function saveOrUConclusionTemplate(data)
{
	return request({
		url: `/follow/saveOrUConclusionTemplate`,
		method: 'post',
		data
	})
}








