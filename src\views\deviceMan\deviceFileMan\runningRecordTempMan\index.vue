<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">

          <!-- <div class="search-item">
            <div class="search-item-label lh28">选择项目：</div>
            <el-select
              class="fl"
              style="width: 220px"
              v-model="listQuery.projectId"
              placeholder="选择项目"
              @change="searchFunc"
              filterable
              clearable
            >
              <el-option
                v-for="item of projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div> -->

          <div class="search-item" v-if="roleAdd">
            <div class="search-item-label lh28">模板类型：</div>
            <el-select
              class="fl"
              style="width: 220px"
              v-model="listQuery.type"
              placeholder="模板类型"
              filterable
              clearable
            >
              <el-option
                v-for="item of typeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>

          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input
              class="fl"
              style="width: 160px"
              v-model="listQuery.label"
              @keyup.enter.native="searchFunc"
              placeholder="关键字"
              clearable
            >
            </el-input>

            <el-button
              class="fl ml10"
              @click="searchFunc"
              icon="el-icon-search"
              type="primary"
              >查询</el-button
            >
            <el-button
              class="fl ml10"
              @click="showDlg('add', {})"
              icon="el-icon-plus"
              type="success"
              >添加</el-button
            >
            <el-button
              v-if="roleAdd"
              class="fl ml10"
              @click="showDlg('addType', {})"
              icon="el-icon-plus"
              type="success"
              >添加公有模板</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <el-table
      height="calc(100vh - 290px)"
      ref="tableRef"
      class="m-small-table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row

    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="模板名称" align="center" prop="name"  show-overflow-tooltip/>
      <el-table-column v-if="listQuery.type === '0'" label="巡检频率" align="center" prop="frequency"  show-overflow-tooltip/>
      <el-table-column label="备注" align="center" prop="remark" width="400" show-overflow-tooltip  />
      <el-table-column label="创建人" align="center" width="100" prop="createUserName" >
      
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="240" align="center">
        <template slot-scope="scope">

          <el-button
            @click="showDlg('copy', scope.row)"
            icon="el-icon-document-copy"
            size="mini"
            type="primary"
            title="拷贝"
            plain
            >拷贝</el-button
          >

          <el-button
            @click="showDlg('edit', scope.row)"
            icon="el-icon-edit"
            size="mini"
            type="primary"
            title="编辑"
            plain
            >编辑</el-button
          >
          <el-button
            @click="delFunc(scope.row)"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            title="删除"
            plain
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      class="mt10"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="getList"
    />
    <div class="clear"></div>

  </div>
</template>

<script>
import * as utils from "@/utils";
import { getAction, deleteAction } from "@/api";

// 组件
import Pagination from "@/components/Pagination"; // 分页


let listQueryEmpty = {
  label: "", //	模糊查询	body	false	string
  page: 1,
  size: 20,
  projectId: '',
  type: '',
};
export default {
  components: {
    Pagination,

  },
  props: {},
  data() {
    return {
      roleAdd: false,
      userInfo: JSON.parse(window.localStorage.userInfo),
      projectList: [],

      // 页面数据
      searchMoreState: false, // 更多筛选
      tableKey: 0,
      list: [],
      selectList: [], // 选中
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      typeList: [
        {
          name:'项目模板',
          id:'0'
        },
        {
          name:'公有模板',
          id:'1'
        },
      ]
    };
  },


  created() {
   
    // 设置权限
    let userRoles = JSON.parse(decodeURI(window.localStorage.userRoles));
    if (userRoles.includes("equ_gongyoumobanxinzeng_jyt")) {
        this.roleAdd = true;
      }
  },
  mounted() {
    this.listQuery.projectId = JSON.parse(window.localStorage.userInfo).projectId
    this.searchFunc();
  },
  methods: {
    parseTime(time) {
      return time.replace(/T/g,' ')
    },
    // ------ 列表
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },
    searchFunc() {
      this.listQuery.page = 1;

      this.getList();
    },
    getList() {
      // if (!this.listQuery.projectId && this.listQuery.type !== '1') {
      //   this.$message.warning("请选择项目")
      //   return false
      // }
      this.list = [];

      let sendObj = JSON.parse(JSON.stringify(this.listQuery));

      sendObj.name = sendObj.label;
      delete sendObj.label;
      sendObj.pageNo = sendObj.page;
      delete sendObj.page;
      sendObj.pageSize = sendObj.size;
      delete sendObj.size;

      this.listLoading = true;
      getAction("sa/green/equ/models/page", sendObj).then(res0 => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = [];
            this.total = 0;
          } else {
            this.list =  res.data.list;
            this.total = res.data.total;
            this.$nextTick(() => {
              this.$refs.tableRef.doLayout();
            });
          }
        } else {
          this.total = 0;
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    // -- 删除
    delFunc(row) {
      this.delAjax(row.id);
    },
    // 批量删除
    batchDelFunc() {
      if (!this.selectList.length) return false;
      this.delAjax(sendObj);
    },
    delAjax(id) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {

        deleteAction("sa/green/equ/models/delete?id=" + id).then(res0 => {
          let res = res0.data;
          if (res.code == "200") {
            this.$message({
              message: res.msg,
              type: "success"
            });
            this.getList();
          } else {
            this.$message({
              message: res.msg,
              type: "error"
            });
          }
        });
      });
    },
    showDlg(type, row) {
      if (type == "add" || type == "addType") {
        let newWin = this.$router.resolve({
          path: "/deviceMan/deviceFileMan/formProcessDesign",
          query: { id: '',type,from:'deviceMan' }
        });
        window.open(newWin.href, "_blank");
      } else {
        let newWin = this.$router.resolve({
          path: "/deviceMan/deviceFileMan/formProcessDesign",
          query: { id: row.id, type ,from:'deviceMan'}
        });
        window.open(newWin.href, "_blank");
      }
    },

  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
