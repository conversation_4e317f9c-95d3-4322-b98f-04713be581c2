<template>
  <!-- 弹窗 新增/编辑 -->
  <el-dialog
    class="mazhenguo"
    :title="dlgType === 'add' ? '添 加' : '修 改'"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="500px"
    top="30px"
  >
    <el-form
      ref="dlgDataForm"
      :rules="dlgRules"
      :model="dlgData"
      label-position="right"
      label-width="100px"
      style="width: 440px"
      size="mini"
      @submit.native.prevent
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="dlgData.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="优惠金额" prop="discount">
        <el-input-number
          v-model="dlgData.discount"
          :min="0"
          placeholder="请输入"
          controls-position="right"
          style="width: 320px"
          precision="2"
        />
        元
      </el-form-item>
      <el-form-item label="截止日期" prop="endTime">
        <el-date-picker
          v-model="dlgData.endTime"
          type="date"
          style="width: 100%"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="选择小区" prop="communityId">
        <el-select v-model="dlgData.communityId" clearable placeholder="请选择" @change="xqSelectChange">
          <el-option v-for="item of xqSelect" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="收费项目" prop="configId">
        <el-select v-model="dlgData.configId" clearable placeholder="请选择">
          <el-option v-for="item of sxxmSelect" :key="item.id" :label="item.feeName" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="优惠券数量" prop="total">
        <el-input-number
          v-model="dlgData.total"
          :min="0"
          placeholder="请输入"
          controls-position="right"
          style="width: 320px"
        />
        张
      </el-form-item>
      <!-- <el-form-item label="备注" prop="aaaa">
        <el-input
          :autosize="{ minRows: 3, maxRows: 4 }"
          v-model="dlgData.aaaa"
          type="textarea"
          placeholder="请输入"
          style="width: 100%"
        />
      </el-form-item> -->
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
// import Tinymce from '@/components/Tinymce' // 富文本组件
// 工具
// import { phoneReg } from '@/utils/regUtil'
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
// 接口
import { isNull, getDbItems, arrId2Name, arrId2Name2 } from '@/utils'
import { postAction, getAction } from '@/api'
import { communityPage } from '@/api/communityMan'
import { payFeeConfigPage } from '@/api/costMan'

let dlgDataEmpty = {
  id: 0, // 费用ID
  name: '', // 优惠券名称
  discount: '', // 优惠金额，
  endTime: '', // 使用券截至时间
  communityId: '', // 小区id
  communityName: '', // 小区名称
  configId: '', // 费用项id
  configName: '', // 费用项名称
  total: '', // 优惠券数量
  flag: '0', // 0:正常 1:删除

  // status: '', // 优惠券状态，如果是0则是正常可用；如果是1则是过期; 如果是2则是下架。
  // createTime: '', // 创建时间
  // updateTime: '', // 更新时间
  // useNum: '', // 使用数量
  // projectId: '', // 项目id
}
export default {
  components: {
    // Tinymce,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          let dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
          this.sxxmSelect = []
          if (this.dlgType != 'add') {
            dlgData = this.dlgQuery
            // dlgData.rollId = this.dlgQuery.rollId
            // dlgData.userId = this.dlgQuery.userId
            // dlgData.name = this.dlgQuery.staffName
            // dlgData.branchName = this.dlgQuery.staffName
            // 净领额

            setTimeout(() => {
              this.getSfxxAjax()
            }, 500)
          }
          this.dlgData = JSON.parse(JSON.stringify(dlgData))

          this.$nextTick(() => {
            this.$refs['dlgDataForm'].clearValidate()
          })
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      userInfo: '',
      // 弹窗
      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        discount: [{ required: true, message: '必填字段', trigger: 'change' }],
        endTime: [{ required: true, message: '必填字段', trigger: 'change' }],
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        configId: [{ required: true, message: '必填字段', trigger: 'change' }],
        total: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      dlgSubLoading: false, // 提交loading

      // 下拉框
      xqSelect: [], // 小区
      sxxmSelect: [], // 收费项目

      dateRange: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 1000 * 60 * 60 * 24
        },
      },
    }
  },
  created() {
    console.log('window.localStorage.userInfo', window.localStorage.userInfo)
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    console.log(this.userInfo)
    // this.getDataDict()
    this.getXqSelect()
  },
  methods: {
    // 获取小区列表
    getXqSelect() {
      let postParam = {
        page: 1,
        limit: 99999,
      }
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.xqSelect = res.data.data
        }
      })
    },
    // 小区change 获取小区收费项
    xqSelectChange() {
      this.sxxmSelect = []
      this.dlgData.configId = '' // 清空 收费项目
      this.dlgData.configName = '' // 清空 收费项目

      if (this.dlgData.communityId === '') {
        this.$message.warning('请选择小区')
        return false
      }
      this.getSfxxAjax()
    },
    // 获取收费项目
    getSfxxAjax() {
      let postParam = {
        page: 1,
        limit: 99999,
        communityId: this.dlgData.communityId, // 小区
        feeTypeCd: '',
      }
      payFeeConfigPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.sxxmSelect = JSON.parse(JSON.stringify(res.data.data))
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    /////

    // 数据字典

    // 弹窗提交 ------
    dlgSubFunc() {
      this.$refs['dlgDataForm'].validate((valid) => {
        if (valid) {
          this.dlgSubLoading = true

          let sendObj = JSON.parse(JSON.stringify(this.dlgData))
          sendObj.communityName = arrId2Name(this.xqSelect, sendObj.communityId)
          sendObj.configName = arrId2Name2(this.sxxmSelect, sendObj.configId, 'id', 'feeName')

          sendObj.projectId = this.userInfo.projectId
          sendObj.createByName = this.userInfo.label
          // console.log('sendObj', sendObj)
          // return false
          postAction('/unity/coupon/addOrUpdate', sendObj).then((res0) => {
            let res = res0.data
            this.dlgSubLoading = false
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.dlgState = false
              this.$emit('getList')
              this.$emit('closeDlg')
              this.$emit('upList1')
            } else {
              this.$message({
                type: 'warning',
                message: res.msg,
              })
            }
          })
        }
      })
    },

    closeDlg() {
      this.dlgLoading = false
      this.dlgSubLoading = false
      this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')

      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.$nextTick(() => {
        this.$refs['dlgDataForm'].clearValidate()
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>