/** 运营管理系统 路由 **/

import Layout from "@/views/layout/Layout";

const propertyManRouter = {
  path: "/propertyMan",
  component: Layout,
  name: "propertyMan",
  meta: {
    title: "运营管理",
    icon: "yygl",
    roles: ["yunyingguanli"]
  },
  alwaysShow: true,
  children: [
    {
      path: "wasteMaintenance",
      component: () => import("@/views/propertyMan/wasteMaintenance"),
      name: "医废暂存点维护",
      meta: {
        title: "医废暂存点维护",
        roles: ["yifeizancundianweihu"]
      }
    },
    {
      path: "whiteList",
      component: () => import("@/views/propertyMan/whiteList"),
      name: "医废白名单维护",
      meta: {
        title: "医废白名单维护",
        roles: ["baimingdanweihu"]
      }
    }
  ]
};

export default propertyManRouter;
