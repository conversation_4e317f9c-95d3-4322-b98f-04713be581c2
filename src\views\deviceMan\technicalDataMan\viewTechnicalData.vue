<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">项目：</div>
            <el-select
              class="fl"
              style="width: 150px"
              v-model="searchForm.projectId"
              placeholder="请选择项目"
              @change="handleProjectChange"
              filterable
              clearable
            >
              <el-option
                v-for="item of projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>


          <div class="search-item">
            <div class="search-item-label lh28">部门：</div>
            <el-input
              class="fl"
              style="width: 150px"
              v-model="searchForm.branchName"
              placeholder="请选择部门"
              readonly
              @focus="showBmTree"
            >
              <i
                @click="resetSearchItem(['creatorBranchId', 'branchName'])"
                slot="suffix"
                class="el-input__icon el-icon-error"
              ></i>
            </el-input>
          </div>

          <div class="search-item">
            <div class="search-item-label lh28">图纸类型：</div>
            <el-select
              class="fl"
              style="width: 150px"
              v-model="searchForm.drawingType"
              :placeholder="
                searchForm.projectId ? '请选择图纸类型' : '请先选择项目'
              "
              @change="searchFunc"
              filterable
              clearable
              :disabled="!searchForm.projectId"
            >
              <el-option
                v-for="item of drawingTypeOptions"
                :key="item.itemValue"
                :label="item.itemText"
                :value="item.itemValue"
              />
            </el-select>
          </div>

          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input
              v-model="searchForm.keyword"
              placeholder="图纸名称或关键词"
              clearable
              class="fl"
              style="width: 180px"
              @change="searchFunc"
            />
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
            >
              查询
            </el-button>

          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc(100vh - 270px)"
      ref="tableBar"
      class="m-small-table"
      v-loading="listLoading"
      :key="tableKey"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="50" fixed="left">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column
        label="项目"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.projectName || "-" }}
        </template>
      </el-table-column>

      <el-table-column
        label="事业部"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.branchOperationName || "-" }}
        </template>
      </el-table-column>

      <el-table-column
        label="项目部"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.branchProjectName || "-" }}
        </template>
      </el-table-column>

      <el-table-column
        label="部门"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.branchName || "-" }}
        </template>
      </el-table-column>

      <el-table-column
        prop="name"
        label="名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="premises"
        label="楼宇"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.premises || "-" }}
        </template>
      </el-table-column>

      <el-table-column
        prop="storey"
        label="楼层"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.storey || "-" }}
        </template>
      </el-table-column>

      <el-table-column
        prop="createTime"
        label="创建时间"
        width="150"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column label="操作" width="100" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            @click="showDlg('info', scope.row)"
            icon="el-icon-document"
            size="mini"
            type="primary"
            title="详情"
            plain
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="searchFunc()"
    />
    <div class="clear"></div>

    <addEdit ref="addEdit" :dlgType="dlgType" :useGetItem="true" @close-dialog="handleCloseDialog" />



    <!-- 部门树选择组件 -->
    <Bmtree />
  </div>
</template>

<script>
import addEdit from "./components/addEdit";
import { postAction } from "@/api";
import Bmtree from "@/components/Dialog/Bmtree";
import {
  getPage,
  getTypeMap,
} from "@/api/technicalData";

export default {
  name: "TechnicalDataManagement",
  components: {
    addEdit,
    Bmtree,
  },

  data() {
    return {
      // 表格相关
      tableKey: 0,
      tableData: [],
      total: 0,
      listLoading: true,
      dlgType: "",

      // 查询参数
      searchForm: {
        pageNo: 1,
        pageSize: 20,
        projectId: "",
        projectName: "",
        branchId: "",
        branchName: "",
        // 移除 businessUnit 和 projectDept
        drawingType: "",
        building: "",
        keyword: "",
      },

      // 选项数据
      projectList: [],
      // 移除 businessUnitOptions 和 projectDeptOptions
      drawingTypeOptions: [],


    };
  },

  computed: {
    // 部门树选择结果
    bmTreeBranchId() {
      return this.$store.getters.bmTreeBranchId;
    },
    bmTreeBranchName() {
      return this.$store.getters.bmTreeBranchName;
    },
  },

  watch: {
    // 监听部门选择
    bmTreeBranchId(val) {
      if (val === "empty") {
        return false;
      }
      this.searchForm.branchId = val;
    },
    bmTreeBranchName(val) {
      if (val === "empty") {
        return false;
      }
      this.searchForm.branchName = val;
    },
  },

  created() {
    this.loadInitialData();
    // 图纸类型选项将在选择项目后动态获取
  },

  methods: {
    resetSearchItem(arr) {
      for (let item of arr) {
        this.searchForm[item] = "";
      }
      this.searchFunc();
    },
    /**
     * 加载初始数据
     */
    async loadInitialData() {
      try {
        await Promise.all([
          this.loadProjects(),
          this.searchFunc(),
        ]);
      } catch (error) {
        this.handleError(error, "初始化失败");
      }
    },

    /**
     * 加载项目列表
     */
    loadProjects() {
      try {
        // 从localStorage获取项目列表
        const userInfo = JSON.parse(window.localStorage.ERPUserInfo || "{}");
        this.projectList = userInfo.projects || [];
      } catch (error) {
        console.error("获取项目列表失败:", error);
        this.projectList = [];
      }
    },


    /**
     * 搜索功能
     */
    async searchFunc() {
      // 验证项目ID必填
      if (!this.searchForm.projectId) {
        this.$message.warning("请先选择项目");
        this.listLoading = false;
        return;
      }

      this.listLoading = true;
      try {
        // 准备查询参数
        const params = {
          pageNo: this.searchForm.pageNo,
          pageSize: this.searchForm.pageSize,
          projectId: this.searchForm.projectId,
          branchId: this.searchForm.branchId || undefined,
          // 移除 branchOperationId 和 branchProjectId
          type: this.searchForm.drawingType || undefined,
          premises: this.searchForm.building || undefined,
          name: this.searchForm.keyword || undefined,
          creatorPostId: this.searchForm.creatorPostId || undefined,
          userPostId: JSON.parse(window.localStorage.ERPUserInfo).postId,
        };

        // 过滤掉空值参数
        Object.keys(params).forEach((key) => {
          if (params[key] === undefined || params[key] === "") {
            delete params[key];
          }
        });

        console.log("查询参数:", params);

        // 调用API获取数据
        const { data } = await getPage(params);

        if (data.code === "200" || data.code === 200) {
          this.tableData = data.data.list || [];
          this.total = data.data.total || 0;
        } else {
          this.$message.error(data.msg || "查询失败");
          this.tableData = [];
          this.total = 0;
        }
      } catch (error) {
        console.error("查询失败:", error);
        this.handleError(error, "查询失败");
        this.tableData = [];
        this.total = 0;
      } finally {
        this.listLoading = false;
      }
    },

    /**
     * 显示对话框
     * @param {string} type - 对话框类型，如 'add', 'edit', 'info'
     * @param {Object} [row] - 表格行数据，编辑或查看详情时需要
     */
    showDlg(type, row) {
      this.dlgType = type;
      this.$nextTick(() => {
        // 检查 this.$refs.addEdit 是否存在
        if (this.$refs.addEdit) {
          this.$refs.addEdit.showDlg(row);
        }
      });
    },



    /**
     * 项目变化处理
     */
    handleProjectChange() {
      // 获取选中项目的名称
      const selectedProject = this.projectList.find(
        function (item) {
          return item.id === this.searchForm.projectId;
        }.bind(this)
      );

      if (selectedProject) {
        this.searchForm.projectName = selectedProject.name;
      } else {
        this.searchForm.projectName = "";
      }

      // 清空图纸类型选择
      this.searchForm.drawingType = "";

      // 重新获取图纸类型选项
      this.getDrawingTypeOptions();

      // 执行搜索
      this.searchFunc();
    },

    /**
     * 获取当前用户可见的图纸类型
     */
    async getDrawingTypeOptions() {
      if (!this.searchForm.projectId) {
        this.drawingTypeOptions = [];
        return;
      }

      try {
        const userInfo = JSON.parse(window.localStorage.ERPUserInfo || "{}");
        const postId = userInfo.postId || 0;

        const params = {
          projectId: this.searchForm.projectId,
          queryType: 2,
          postId: postId,
        };

        const { data } = await getTypeMap(params);

        if (data && data.code === "200") {
          console.log("图纸类型选项:", data);
          this.drawingTypeOptions = data.data || [];
        } else {
          console.warn("获取图纸类型失败:", data ? data.msg : "获取失败");
          this.drawingTypeOptions = [];
        }
      } catch (error) {
        console.error("获取图纸类型失败:", error);
        this.drawingTypeOptions = [];
      }
    },

    /**
     * 显示部门树选择对话框
     */
    showBmTree() {
      this.$store.commit("SET_BMTREEISROLE", true);
      this.$store.commit("SET_BMTREESTATE", true);
    },

    /**
     * 事业部变化处理
     */
    handleBusinessUnitChange() {
      // 清空项目部选择
      this.searchForm.projectDept = "";
      // 重新加载项目部数据
      this.loadProjectDepts();
      // 执行搜索
      this.searchFunc();
    },





    /**
     * 处理关闭对话框事件
     */
    handleCloseDialog() {
      this.dlgType = "";
    },

    /**
     * 错误处理
     */
    handleError(error, defaultMessage = "操作失败") {
      const message =
        (error &&
          error.response &&
          error.response.data &&
          error.response.data.msg) ||
        (error && error.message) ||
        defaultMessage;
      this.$message.error(message);
      console.error("Error:", error);
    },
  },
};
</script>

<style lang="scss" scoped>
.drawing-name {
  display: flex;
  justify-content: center;
  align-items: center;
  .file-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #409eff;
  }

  .name-link {
    color: #409eff;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}

.no-data {
  color: #c0c4cc;
  font-size: 12px;
}
</style>
