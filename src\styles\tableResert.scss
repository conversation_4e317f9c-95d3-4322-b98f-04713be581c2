// 表格样式重置

// 分页
.pagination-container {
  text-align: right;
}

// 表格样式重置
.m-small-table {
  thead th {
    background-color: #f2f2f2;
    color: #19aa8d;
    font-size: 14px;
    font-weight: 700;
  }
  .fixed-width .el-button--mini {
    padding: 4px 10px;
  }
  tbody td {
    padding: 4px 0;
  }
  // 排序
  .caret-wrapper {
    height: 20px;
  }
  .sort-caret.ascending {
    top: -1px;
  }
  .sort-caret.descending {
    bottom: -1px;
  }
  .descending .sort-caret.descending {
    border-top-color: #19aa8d;
  }
  .ascending .sort-caret.ascending {
    border-bottom-color: #19aa8d;
  }
  // 表格复选框
  .el-table-column--selection .cell {
    padding-right: 10px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner,
  .el-switch.is-checked .el-switch__core {
    background-color: #19aa8d;
    border-color: #19aa8d;
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #f2f6fc;
    border-color: #dcdfe6;
  }

  .el-table__fixed {
    height: auto !important; // 此处的important表示优先于element.style
    bottom: 18px; // 改为自动高度后，设置与父容器的底部距离，则高度会动态改变
    
  }
  .el-table__fixed-body-wrapper {
    padding-bottom: 18px;
  }
}
// 表格样式 小
.m-small-table.small {
  thead th {
    background-color: #f2f2f2;
    color: #19aa8d;
    font-size: 12px;
  }
  .fixed-width .el-button--mini {
    padding: 4px 10px;
  }
  tbody td {
    padding: 4px 0;
  }
  // 排序
  .caret-wrapper {
    height: 20px;
  }
  .sort-caret.ascending {
    top: -1px;
  }
  .sort-caret.descending {
    bottom: -1px;
  }
  .descending .sort-caret.descending {
    border-top-color: #19aa8d;
  }
  .ascending .sort-caret.ascending {
    border-bottom-color: #19aa8d;
  }
  // 表格复选框
  .el-table-column--selection .cell {
    padding-right: 10px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #19aa8d;
    border-color: #19aa8d;
  }

  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #f2f6fc;
    border-color: #dcdfe6;
  }
}

// 分页样式重置
.el-pager li.active {
  color: #19aa8d;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #19aa8d;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #e5e8ec;
}

.mazhenguo {
  .el-table .el-table__fixed {

    height: auto !important; // 此处的important表示优先于element.style
    bottom: 18px; // 改为自动高度后，设置与父容器的底部距离，则高度会动态改变
    z-index: 10;
  }

  .el-button--mini,
  .el-button--mini.is-round {
    padding: 6px 10px;
    height: 28px
  }

  .el-table {
    .el-button--mini,
    .el-button--mini.is-round {
      padding: 4px 10px;
      height: 22px
    }
  }


}
