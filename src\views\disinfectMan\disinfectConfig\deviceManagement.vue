<template>
  <!--消毒设备管理-->
  <div class="app-container">
    <div class="filter-container clearfix">
      <el-form inline @submit.native.prevent>
        <el-form-item label="">
          <el-input @focus="showKeshiDlg()" v-model="listQuery.departmentName" readonly placeholder="请选择科室">
            <i @click="resetSearchItem(['departmentId', 'departmentName'])" slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item label="关键字:">
          <el-input style="width: 220px" @keyup.enter.native="getList" placeholder="请输入设备名称" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>

        <el-button icon="el-icon-search" type="success" size="mini" @click="searchItem">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="addItem">添加</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class="m-small-table" height="calc(100vh - 372px)" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="所属部门科室">
          <template slot-scope="scope">
            <span>{{ scope.row.departmentName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="设备名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="累计用时">
          <template slot-scope="scope">
            <span>{{ scope.row.totalTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="410" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button plain icon="el-icon-picture" type="primary" @click="qrcodeItem(scope.row, scope.$index)">二维码</el-button>
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="infoItem(scope.row)">详情</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row)">删除</el-button>
            <el-button type="warning" size="mini" icon="el-icon-minus" plain @click="clearItem(scope.row)">清零</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
      <div class="clear"></div>
    </div>

    <el-dialog :close-on-click-modal="false" title="消毒设备维护" :visible.sync="dlgShow" width="600px" append-to-body>
      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="所属部门科室" prop="departmentName" label-width="120px">
          <el-input
            :disabled="this.dlgType == 'INFO'"
            @focus="showKeshiDlg"
            v-model="dlgData.departmentName"
            readonly
            placeholder="请选择科室"
          ></el-input>
        </el-form-item>
        <el-form-item label="设备名称" prop="name" label-width="120px">
          <el-input :disabled="this.dlgType == 'INFO'" v-model="dlgData.name" placeholder="请输入名称" />
        </el-form-item>

        <el-form-item label="签字人数" prop="signType" label-width="120px">
          <el-radio-group v-model="dlgData.signType" :disabled="this.dlgType == 'INFO'">
            <el-radio :label="0">单人</el-radio>
            <el-radio :label="1">多人</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="小时预警" prop="hourWar" label-width="120px" >
          <el-input-number v-model="dlgData.hourWar" controls-position="right" :min="0" :precision="0" :step="1" :disabled="this.dlgType == 'INFO'"></el-input-number>
        </el-form-item>

        <el-form-item label="备注" label-width="120px">
          <el-input
            :disabled="this.dlgType == 'INFO'"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button type="success" :loading="dlgLoading" @click="subDlg" icon="el-icon-check" v-if="this.dlgType != 'INFO'">
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" title="二维码详情" :visible.sync="dialogVisible">
      <div class="qrcode-dom" id="qr-box">
        <canvas id="QRCode" class="qrcode-canvas"></canvas>
        <div>{{ dialogData.qrCode }}</div>
        <div>{{ dialogData.name }}</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" icon="el-icon-back">关闭</el-button>
        <el-button type="success" @click="qrPrint" icon="el-icon-download">导出</el-button>
      </div>
    </el-dialog>

    <selectBmDlg
      :dlgState0="dlgKeshiState"
      :dlgType="dlgKeshiType"
      :dlgQuery="dlgKeshiQuery"
      :dlgSelectData="dlgKeshiSelectData"
      :isRole="false"
      title="科室"
      @closeDlg="closeKeshiDlg"
      @backFunc="dlgKeshibackFunc"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import { medicalWasteConst } from '@/utils/const'
import selectBmDlg from '@/components/Dialog2/selectBmDlg'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { disEquipmentPage, disEquipmentAdd, disEquipmentDel } from '@/api/disinfectMan/disinfectConfig'
import QRCode from 'qrcode' //引入生成二维码插件
import { html2canvasToImage } from '@/utils/domToImage'
let dlgDataEmpty = {
  id: '',
  departmentName: '',
  departmentId: '',
  name: '',
  remark: '',
  projectId: '',
  projectName: '',
  signType: 0,
  hourWar: 0,
}
export default {
  components: {
    Pagination,
    selectBmDlg,
    ElImageViewer,
  },
  data() {
    return {
      // 科室弹窗
      dlgKeshiQuery: {},
      dlgKeshiState: false,
      dlgKeshiType: '', // 弹框状态add, edit
      dlgKeshiSelectData: { id: '', label: '' },

      list: [],
      dlgShow: false, // 新增
      dlgType: '',
      listQuery: {
        page: 1,
        limit: 20,
        departmentName: '',
        departmentId: '',
      },
      total: 0,
      listLoading: false,
      totalItem: {},
      fields: [],
      stateList: medicalWasteConst.stateList,
      wasteTypeList: [],
      batchList: medicalWasteConst.batchList,
      userInfo: {},
      branchType: '0',
      dlgLoading: false,
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      rules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        signType: [{ required: true, message: '必填字段', trigger: 'change' }],
        departmentName: [{ required: true, message: '必填字段', trigger: 'change' }],

        hourWar: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      dlgListQuery: {
        page: 1,
        limit: 20,
        userId: Cookie.get('userId'),
      },

      dialogData: {},
      qrcodeMsg: '', //生成二维码信息
      dialogVisible: false,
    }
  },

  computed: {},

  watch: {
     // 通过监听获取数据
    qrcodeMsg(val) {
      console.log(val)
      this.$nextTick(() => {
        // 获取页面的canvas
        let qrcode = document.getElementById('QRCode')
        // 将获取到的数据（val）画到qrcode（canvas）上
        QRCode.toCanvas(
          qrcode,
          val,
          {
            scale: 24,
          },
          function (error) {
            console.log(error)
          }
        )
      })
    },
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.getList()
  },

  methods: {
     //二维码
    qrcodeItem(data, idx) {
      this.dialogVisible = true
      this.qrcodeMsg = data.qrCode
      // console.log('location', location.href)
      // if (location.href.indexOf('jyt.wlines.cn')>=0) {
      //   this.qrcodeMsg = 'https://user.jyt.wlines.cn/jyt/?id=' + data.qrCode
      // } else {
      //   this.qrcodeMsg = 'https://user.jyt.wlines.cn/jyt/?id=' + data.qrCode
      // }
      this.dialogData = data
    },
    //导出二维码
    qrPrint() {
      let params = {
        node: document.getElementById('qr-box'),
        pngName: this.qrTitle,
        shouldNotDownload: false,
        responseResultMethod: (response) => {
          console.log(response)
          // loading.close()
          if (response.resultCode != 200) {
            _this.$message.error('导出失败！请使用80以上版本的谷歌浏览器')
          }
        },
      }
      html2canvasToImage(params)
    },
    showKeshiDlg() {
      if (this.listQuery.departmentName) {
        this.dlgKeshiSelectData = {
          id: this.listQuery.departmentId,
          label: this.listQuery.departmentName,
        }
      } else {
        this.dlgKeshiSelectData = { id: '', label: '' }
      }
      this.dlgKeshiState = true
    },
    dlgKeshibackFunc(data) {
      if (this.dlgShow) {
        this.dlgData.departmentName = data.label
        this.dlgData.departmentId = data.id
      } else {
        this.listQuery.departmentId = data.id
        this.listQuery.departmentName = data.label
      }
      // this.searchFunc()
    },
    closeKeshiDlg() {
      this.dlgKeshiState = false
    },
    // 显示弹窗
    addItem() {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 搜索框 清空单个条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    getList() {
      disEquipmentPage(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.data
          this.total = res.data.page.total
        }
      })
    },
    subDlg() {
      this.$refs.dlgForm.validate((valid) => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData))
          sendObj.projectId = this.userInfo.projectId
          sendObj.projectName = this.userInfo.projectName
          disEquipmentAdd(sendObj).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.msg)
              this.getList()
              this.dlgShow = false
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },
    // 编辑
    editItem(data) {
      let row = JSON.parse(JSON.stringify(data))
      this.dlgData.name = row.name
      this.dlgData.id = row.id
      this.dlgData.remark = row.remark
      this.dlgData.departmentName = row.departmentName
      this.dlgData.departmentId = row.departmentId
      this.dlgData.hourWar = row.hourWar
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },
    // 详情
    infoItem(data) {
      let row = JSON.parse(JSON.stringify(data))
      this.dlgData.name = row.name
      this.dlgData.id = row.id
      this.dlgData.remark = row.remark
      this.dlgData.departmentName = row.departmentName
      this.dlgData.departmentId = row.departmentId
      this.dlgData.hourWar = row.hourWar
      this.dlgType = 'INFO'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },
    clearItem(data) {
      let title = '确认清零?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let sendObj = {
          id: data.id,
          totalTime: 0,
        }
        disEquipmentAdd(sendObj).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },
    delItem(data) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        disEquipmentDel(data.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-image {
  display: block;
  width: 32px;
  height: 32px;
  margin: 0 auto;

  /deep/ .el-image__error {
    font-size: 12px;
  }
}

/deep/ .el-image-viewer__img {
  background: #ffffff;
}

.el-table--border /deep/ td.border-right,
.el-table--border /deep/ th.border-right {
  border-right: 2px solid #cccccc !important;
}

.qrcode-dom {
  text-align: center;
  padding-bottom: 30px;
  .qrcode-canvas {
    width: 200px !important;
    height: 200px !important;
  }
}
</style>