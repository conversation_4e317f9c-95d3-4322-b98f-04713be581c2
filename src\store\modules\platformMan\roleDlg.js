// 角色dlg组件

const roleDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    roleId: '',

    roleName: '',

  },

  getters: {
    dlgShow: state => state.dlgShow,

    roleId: state => state.roleId,

    roleName: state => state.roleName,

  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_ROLEID: (state, val) => {
      state.roleId = val
    },

    SET_ROLENAME: (state, val) => {
      state.roleName = val
    },

  },

  actions: {

  }
}

export default roleDlg
