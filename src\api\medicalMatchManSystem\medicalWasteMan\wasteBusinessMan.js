import request from "@/utils/request";

/*
 * 医废参数设置
 */

// 医废科室汇总查询
export function officCollectWorkloadMedicalWasteInfo(data) {
  return request({
    url: `/me/yf/officCollectWorkloadMedicalWasteInfo`,
    method: "post",
    data
  });
}

// 医废产出汇总查询
export function medicalProduceSummary(data) {
  return request({
    url: `/me/yf/medicalProduceSummary`,
    method: "post",
    data
  });
}

// 医废运出汇总查询
export function medicalShipOutSummary(data) {
  return request({
    url: `/me/yf/medicalShipOutSummary`,
    method: "post",
    data
  });
}

// 医废运出明细查询
export function transDetailMedicalWasteInfo(data) {
  return request({
    url: `/me/yf/transDetailMedicalWasteInfo`,
    method: "post",
    data
  });
}

// 医废综合明细查询
export function medicalComprehensiveInfo(data) {
  return request({
    url: `/me/yf/medicalComprehensiveInfo`,
    method: "post",
    data
  });
}

// 医废综合明细导出
export function medicalComprehensiveInfoExport(data) {
  return request({
    url: `/me/yf/medicalComprehensiveInfoExport`,
    method: "post",
    data
  });
}

// 医废综合明细导出(儿童医院)
export function medicalComprehensiveInfoExportV1(data) {
  return request({
    url: `/me/yf/medicalComprehensiveInfoExportV1`,
    method: "post",
    data
  });
}

// 医废处理工作量统计
export function medicalHandlingWorkload(data) {
  return request({
    url: `/me/yf/medicalHandlingWorkload`,
    method: "post",
    data
  });
}

// 医废超时处理查询
export function medicalProcessingTimeout(data) {
  return request({
    url: `/me/yf/medicalProcessingTimeout`,
    method: "post",
    data
  });
}

// 超时记录查询
export function medicalProcessingTimeoutList(data) {
  return request({
    url: `/me/yf/medicalProcessingTimeoutList`,
    method: "post",
    data
  });
}

// 医废收取记录查询
export function medicalWasteHandoverRecord(data) {
  return request({
    url: `/me/yf/medicalWasteHandoverRecord`,
    method: "post",
    data
  });
}

// 医废运出记录查询
export function medicalWasteSendRecord(data) {
  return request({
    url: `/me/yf/medicalWasteSendRecord`,
    method: "post",
    data
  });
}

// 我的导出删除全部  TYPE 0 全删 1 单删
export function medicalComprehensiveInfoExportRemove(id, type) {
  return request({
    url: `/me/yf/medical/remove/${id}/${type}`,
    method: "get"
  });
}

// 医废汇总统计
export function medicalStatisticsByCreateTime(data) {
  return request({
    url: `/me/yf/medicalStatisticsByCreateTime`,
    method: "post",
    data
  });
}

// 医废选择暂存间
export function listBranchWhiteTreeByUserId(userId) {
  return request({
    url: `/sys/listBranchWhiteTreeByUserId/${userId}`,
    method: "get"
  });
}
// 医废选择科室
export function findTreeByFromAndProjectId(data) {
  return request({
    url: `/sys/department/findTreeByFromAndProjectId`,
    method: "post",
    data
  });
}

// 医疗废弃物日账
export function medicalDailyAccount(data) {
  return request({
    url: `/me/yf/medicalDailyAccount`,
    method: "post",
    data
  });
}

// 医疗废弃物月账/年帐
export function medicalDailyMonthOrYearAccount(data) {
  return request({
    url: `/me/yf/medicalDailyMonthOrYearAccount`,
    method: "post",
    data
  });
}

// 未运出查询 实时
export function notShippedOutMedical(data) {
  return request({
    url: `/me/yf/notShippedOutMedical`,
    method: "post",
    data
  });
}

// 我的导出功能
export function medicalComprehensiveInfoExportLogPage(data) {
  return request({
    url: `/me/yf/medicalComprehensiveInfoExportLogPage`,
    method: "post",
    data
  });
}

// 获取医废类别
// 按项目查 医废类别
export function medicalTypeList(projectId) {
  return request({
    url: `/me/medical/type/list/${projectId}`,
    method: "get"
  });
}

// 医废修改日志
export function updateLogPage(data) {
  return request({
    url: `/me/updateLog/page`,
    method: "post",
    data
  });
}

// 医废修改签字
export function updateLogPicPage(data) {
  return request({
    url: `/me/updateLog/picPage`,
    method: "post",
    data
  });
}
// 医废蓝牙秤查询
export function scalePage(data) {
  return request({
    url: `/me/scale/page`,
    method: "post",
    data
  });
}
// 医废蓝牙秤修改
export function scaleAdd(data) {
  return request({
    url: `/me/scale/add`,
    method: "post",
    data
  });
}
// 医废蓝牙秤删除
export function scaleDel(id) {
  return request({
    url: `/me/scale/del/${id}`,
    method: "get"
  });
}
// 医废PDA查询
export function pdaPage(data) {
  return request({
    url: `/me/pda/page`,
    method: "post",
    data
  });
}
// 医废PDA修改
export function pdaAdd(data) {
  return request({
    url: `/me/pda/add`,
    method: "post",
    data
  });
}
// 医废PDA删除
export function pdaDel(id) {
  return request({
    url: `/me/pda/del/${id}`,
    method: "get"
  });
}
// 危险废物入库台账查询
export function tempCountExportIn (data) {
  return request({
    url: `/me/Export/tempCountExportPageIn`,
    method: 'post',
    data
  })
}
// 危险废物出库台账查询
export function tempCountExportOut (data) {
  return request({
    url: `/me/Export/tempCountExportPageOut`,
    method: 'post',
    data
  })
}
