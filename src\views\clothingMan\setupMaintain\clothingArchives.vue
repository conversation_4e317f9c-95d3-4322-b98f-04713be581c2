<template>
  <!--被服档案-->
  <div class="app-container">
    <div class="filter-container">
      <el-form ref="searchForm" class='n-search' label-width="90px" @submit.native.prevent>
        <div class='fr'>
          <el-select v-model="listQuery.clothType" filterable placeholder="请选择被服类型">
            <el-option v-for="item in clothTypeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <el-input v-model="listQuery.str" placeholder='请输入名称\品牌\供货商\洗涤次数'>
            <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">搜索</el-button>
          <el-button icon='el-icon-refresh' type="primary" size='mini' @click="resetItem">重置</el-button>
          <el-button icon='el-icon-plus' type="primary" size='mini' @click="addItem">新增</el-button>
        </div>
      </el-form>
    </div>

    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="类型">
          <template slot-scope="scope">
            <span>{{ scope.row.clothTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="材质">
          <template slot-scope="scope">
            <span>{{ scope.row.clothMaterialText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="规格">
          <template slot-scope="scope">
            <span>{{ scope.row.clothSpecificationText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计量">
          <template slot-scope="scope">
            <span>{{ scope.row.clothUnitText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="寿命(月)">
          <template slot-scope="scope">
            <span>{{ scope.row.clothLife }}</span>
          </template>
        </el-table-column>

        <el-table-column label="洗涤次数(次)">
          <template slot-scope="scope">
            <span>{{ scope.row.washTimes }}</span>
          </template>
        </el-table-column>

        <el-table-column label="品牌">
          <template slot-scope="scope">
            <span>{{ scope.row.brand }}</span>
          </template>
        </el-table-column>

        <el-table-column label="供货商">
          <template slot-scope="scope">
            <span>{{ scope.row.manufacturer }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="editItem(scope.row, scope.$index)" plain>编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="delItem(scope.row, scope.$index)" plain>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="dlgTitle" :visible.sync="dlgShow">
      <el-form ref="dlgForm" :rules="dlgRules" :model="dlgData" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="name">
              <el-input v-model="dlgData.name" placeholder="请填写被服名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="clothType">
              <el-select v-model="dlgData.clothType" filterable placeholder="请选择被服类型">
                <el-option v-for="item in clothTypeList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="规格" prop="clothSpecification">
              <el-select v-model="dlgData.clothSpecification" filterable placeholder="请选择被服规格">
                <el-option v-for="item in clothSpecificationList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="材质" prop="clothMaterial">
              <el-select v-model="dlgData.clothMaterial" filterable placeholder="请选择被服材质">
                <el-option v-for="item in clothMaterialList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="计量单位" prop="clothUnit">
              <el-select v-model="dlgData.clothUnit" filterable placeholder="请选择计量单位">
                <el-option v-for="item in clothUnitList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用寿命" prop="clothLife">
              <el-input-number v-model="dlgData.clothLife" :precision="0" :step="1" :min="1" label="月"></el-input-number> 月
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="洗涤次数" prop="washTimes">
              <el-input-number v-model="dlgData.washTimes" :precision="0" :step="1" :min="1" label="次"></el-input-number> 次
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="dlgData.brand" placeholder="请填写品牌"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="供应商" prop="manufacturer">
          <el-input v-model="dlgData.manufacturer" placeholder="请填写供应商"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" :autosize="{ minRows: 5, maxRows: 10}" placeholder="请输入备注" v-model="dlgData.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-back" @click="dlgShow = false">取 消</el-button>
        <el-button icon="el-icon-check" :disabled="!subEnable" type="success" @click="subDlg()">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import {
  findArchivesDynamic,
  updateArchives,
  saveOrUArchives
} from '@/api/medicalMatchManSystem/clothingMan/clothingArchives'

export default {
  components: {
    Pagination
  },
  data () {
    return {
      dlgRules: {
        name: [
          { required: true, message: '被服名称必填', trigger: 'blur' },
        ],
        clothType: [
          { required: true, message: '被服类型必填', trigger: 'change' }
        ],
        clothMaterial: [
          { required: true, message: '被服材质必填', trigger: 'change' }
        ],
        clothSpecification: [
          { required: true, message: '被服规格必填', trigger: 'change' }
        ],
        clothUnit: [
          { required: true, message: '被服单位必填', trigger: 'change' }
        ],
        clothLife: [
          { required: true, message: '被服寿命必填', trigger: 'blur' }
        ],
        washTimes: [
          { required: true, message: '洗涤次数必填', trigger: 'blur' }
        ],
        brand: [
          { required: true, message: '品牌必填', trigger: 'blur' }
        ],
        manufacturer: [
          { required: true, message: '供应商必填', trigger: 'blur' }
        ],
      },
      clothTypeList: [],
      clothMaterialList: [],
      clothSpecificationList: [],
      clothUnitList: [],
      list: [

      ],
      listQuery: {
        page: 1,
        size: 20,
        str: '',
        clothType: ''
      },
      total: 0,
      listLoading: false,
      dlgShow: false,
      dlgTitle: "",
      dlgType: "",
      dlgData: {
        name: '',
        clothType: '',
        clothMaterial: '',
        clothSpecification: '',
        clothUnit: '',
        clothLife: '',
        washTimes: '',
        manufacturer: '',
        brand: '',
        remark: ''
      },
      subEnable: true
    }
  },

  created () {
    this.getList()
    utils.getDataDict(this, 'clothType', 'clothTypeList')
    utils.getDataDict(this, 'clothMaterial', 'clothMaterialList')
    utils.getDataDict(this, 'clothSpecification', 'clothSpecificationList')
    utils.getDataDict(this, 'clothUnit', 'clothUnitList')
  },
  methods: {
    getList () {
      this.list = []
      this.listLoading = true
      findArchivesDynamic(this.listQuery).then(res => {
        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    resetStr () {
      this.listQuery.str = ""
      this.getList()
    },

    resetItem () {
      this.listQuery.str = ""
      this.listQuery.clothType = ""
      this.getList()
    },

    searchItem () {
      this.getList()
    },

    addItem () {
      this.dlgData = {
        name: '',
        clothType: '',
        clothMaterial: '',
        clothSpecification: '',
        clothUnit: '',
        clothLife: '',
        washTimes: '',
        manufacturer: '',
        brand: '',
        remark: ''
      }
      this.dlgShow = true
      this.dlgType = "add"
      this.dlgTitle = "新增被服档案"
      this.$nextTick(() => {
        this.$refs.dlgForm.clearValidate()
      })
    },

    editItem (data, idx) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgShow = true
      this.dlgType = "edit"
      this.dlgTitle = "编辑被服档案"
    },

    delItem (data, idx) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let postParam = {
            id: data.id
          }
          updateArchives(postParam).then(res => {
            let code = res.data.code
            let msg = res.data.msg
            if (code == 200) {
              this.$message({
                type: 'success',
                message: msg
              })
              this.getList()
            } else {
              this.$message.error(msg)
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    subDlg () {
      this.$refs['dlgForm'].validate(valid => {
        if (valid) {
          let postParam = this.dlgData
          if (this.dlgType == 'add') {
            postParam['id'] = '0'
          } else {
            postParam['id'] = this.dlgData.id
          }
          // 类型
          let clothTypeItem = this.clothTypeList.filter(item => {
            return item['id'] == postParam['clothType']
          })
          if (clothTypeItem.length > 0) {
            postParam['clothTypeText'] = clothTypeItem[0]['name']
          }
          // 材质
          let clothMaterialItem = this.clothMaterialList.filter(item => {
            return item['id'] == postParam['clothMaterial']
          })
          if (clothMaterialItem.length > 0) {
            postParam['clothMaterialText'] = clothMaterialItem[0]['name']
          }
          // 规格
          let clothSpecificationItem = this.clothSpecificationList.filter(item => {
            return item['id'] == postParam['clothSpecification']
          })
          if (clothSpecificationItem.length > 0) {
            postParam['clothSpecificationText'] = clothSpecificationItem[0]['name']
          }
          // 单位
          let clothUnitItem = this.clothUnitList.filter(item => {
            return item['id'] == postParam['clothUnit']
          })
          if (clothUnitItem.length > 0) {
            postParam['clothUnitText'] = clothUnitItem[0]['name']
          }
          this.subEnable = false
          saveOrUArchives(postParam).then(res => {
            this.subEnable = true
            let code = res.data.code
            let msg = res.data.msg
            if (code == 200) {
              this.$message({
                type: 'success',
                message: msg
              })
              this.dlgShow = false
              this.getList()
            } else {
              this.$message.error(msg)
            }
          })
        } else {
          return false
        }
      })
    },
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>