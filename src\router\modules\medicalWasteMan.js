/** 医疗配套管理系统 **/

// ==== 导出  /me/medicalReport/export
// 综合明细导出 /me/yf/medicalComprehensiveInfoExport    0
// 医废明细查询 /me/yf/medicalDetailsExport  1
// 医废汇总统计 /api/me/yf/medicalStatisticsByCreateTimeExport  2
// 处理超时查询 /api/me/yf/medicalProcessingTimeoutExport  3
// 收取记录 /api/me/yf/medicalWasteHandoverRecordExport  4
// 运出记录 /api/me/yf/medicalWasteSendRecordExport  5
// 工作量统计 /api/me/yf/medicalHandlingWorkloadExport  6
// 医废转移联单 /me/xf/medicalTransferOrderExport  7
// 医废产生处置 /me/xf/medicalGenerationDisposalExport  8
// 医疗废弃物日账 /api/me/yf/medicalDailyAccountExport  9
// 医疗废弃物月账 /api/me/yf/medicalDailyMonthOrYearAccountExport  10
// 医疗废弃物年账 /api/me/yf/medicalDailyMonthOrYearAccountExport  11
// 未运出查询 /api/me/yf/notShippedOutMedicalExport  12
// 医大一医废明细导出  13

import Layout from "@/views/layout/Layout";

const medicalWasteManRouter = {
  path: "/medicalWasteMan",
  component: Layout,
  name: "medicalWasteMan",
  meta: {
    title: "医废管理",
    icon: "yfgl",
    roles: ["yifeiguanli"]
  },
  children: [
    {
      path: "wasteParamSetting",
      component: () => import("@/views/medicalWasteMan/wasteParamSetting"),
      name: "参数设置",
      meta: {
        title: "参数设置",
        roles: ["yifeicanshushezhi"]
      },
      children: []
    },
    {
      path: "deptTieIssue",
      component: () => import("@/views/medicalWasteMan/deptTieIssue"),
      name: "扎带管理",
      meta: {
        title: "扎带管理",
        roles: ["keshizadaimingxi"]
      },
      children: []
    },
    {
      path: "comprehensiveDetailQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/comprehensiveDetailQuery"),
      name: "综合明细查询",
      meta: {
        title: "综合明细查询",
        roles: ["yifeimingxizonghechaxun"]
      },
      children: []
    },
    // 儿童医院 综合明细查询
    {
      path: "comprehensiveDetailQueryV1",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/comprehensiveDetailQueryV1"),
      name: "综合明细查询",
      meta: {
        title: "综合明细查询",
        roles: ["yifeimingxizonghechaxunV1"]
      },
      children: []
    },

    // 综合明细查询(无签字)
    {
      path: "comprehensiveDetailQueryNoSign",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/comprehensiveDetailQueryNoSign"),
      name: "医废明细查询",
      meta: {
        title: "医废明细查询",
        roles: ["yifeimingxizonghechaxunwuqianzi"]
      },
      children: []
    },
    // 医废明细查询-医大一
    {
      path: "comprehensiveDetailQueryNoSignYd1",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/comprehensiveDetailQueryNoSignYd1"),
      name: "医废明细查询",
      meta: {
        title: "医废明细查询",
        roles: ["yifeimingxizonghechaxunwuqianziydy"]
      },
      children: []
    },
    {
      path: "medicalSummaryStatistical",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/medicalSummaryStatistical"),
      name: "医废汇总统计",
      meta: {
        title: "医废汇总统计",
        roles: ["yifeihuizongtongji"]
      },
      children: []
    },
    {
      path: "medicalWasteParameter",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/medicalWasteParameter"),
      name: "医废转移联单",
      meta: {
        title: "医废转移联单",
        roles: ["yifeizhuanyiliandan_web"]
      },
      children: []
    },
    {
      path: "medicalWasteDisposalTable",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/medicalWasteDisposalTable"),
      name: "医废产生处置表",
      meta: {
        title: "医废产生处置表",
        roles: ["yifeichanshengchuzhibiao_web"]
      },
      children: []
    },
    {
      path: "handleOvertimeQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/handleOvertimeQuery"),
      name: "处理超时查询",
      meta: {
        title: "处理超时查询",
        roles: ["yifeichulichaoshichaxun"]
      },
      children: []
    },
    {
      path: "deptSummaryQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/deptSummaryQuery"),
      name: "科室汇总查询",
      meta: {
        title: "科室汇总查询",
        roles: ["yifeikeshihuizongchaxun"]
      },
      children: []
    },
    {
      path: "collectionRecordQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/collectionRecordQuery"),
      name: "收取记录",
      meta: {
        title: "收取记录",
        roles: ["yifeishouqujiluchaxun"]
      },
      children: []
    },
    {
      path: "outofRecordQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/outofRecordQuery"),
      name: "运出记录",
      meta: {
        title: "运出记录",
        roles: ["yifeiyunchujiluchaxun"]
      },
      children: []
    },
    {
      path: "handleWorkloadStatistical",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/handleWorkloadStatistical"),
      name: "工作量统计",
      meta: {
        title: "工作量统计",
        roles: ["yifeichuligongzuoliangtongji"]
      },
      children: []
    },
    {
      path: "wasteDailyAccount",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/wasteDailyAccount"),
      name: "医疗废弃物日账",
      meta: {
        title: "医疗废弃物日账",
        roles: ["yfrizhang"]
      },
      children: []
    },
    {
      path: "wasteMonthAccount",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/wasteMonthAccount"),
      name: "医疗废弃物月账",
      meta: {
        title: "医疗废弃物月账",
        roles: ["yfyuezhang"]
      },
      children: []
    },
    {
      path: "wasteYearAccount",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/wasteYearAccount"),
      name: "医疗废弃物年账",
      meta: {
        title: "医疗废弃物年账",
        roles: ["yfnianzhang"]
      },
      children: []
    },
    {
      path: "wasteStorageAccount",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/wasteStorageAccount"),
      name: "危险废物入库台账",
      meta: {
        title: "危险废物入库台账",
        roles: ["weixianfeiwurukutaizhang"]
      },
      children: []
    },
    {
      path: "wasteDischargeAccount",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/wasteDischargeAccount"),
      name: "危险废物出库台账",
      meta: {
        title: "危险废物出库台账",
        roles: ["weixianfeiwuchukutaizhang"]
      },
      children: []
    },
    {
      path: "unshippedQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/unshippedQuery"),
      name: "未运出查询",
      meta: {
        title: "未运出查询",
        roles: ["yfweiyunchuchaxun"]
      },
      children: []
    },
    {
      path: "updateQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/updateQuery"),
      name: "医废修改日志",
      meta: {
        title: "医废修改日志",
        roles: ["yfxiugairizhi"]
      },
      children: []
    },
    {
      path: "bluetoothScaleQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/bluetoothScaleQuery"),
      name: "蓝牙设备查询",
      meta: {
        title: "蓝牙设备查询",
        roles: ["lanyachengchaxun"]
      },
      children: []
    },
    {
      path: "pdaQuery",
      component: () => import("@/views/medicalWasteMan/businessMan/pdaQuery"),
      name: "PDA查询",
      meta: {
        title: "PDA查询",
        roles: ["pdachaxun"]
      },
      children: []
    },
    {
      path: "outputSummaryQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/outputSummaryQuery"),
      hidden: true,
      name: "医废产出汇总查询",
      meta: {
        title: "医废产出汇总查询",
        roles: ["yifeichanchuhuizongchaxun"]
      },
      children: []
    },
    {
      path: "outofSummaryQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/outofSummaryQuery"),
      hidden: true,
      name: "医废运出汇总查询",
      meta: {
        title: "医废运出汇总查询",
        roles: ["yifeiyunchuhuizongchaxun"]
      },
      children: []
    },
    {
      path: "outofDetailQuery",
      component: () =>
        import("@/views/medicalWasteMan/businessMan/outofDetailQuery"),
      hidden: true,
      name: "医废运出明细查询",
      meta: {
        title: "医废运出明细查询",
        roles: ["yifeiyunchumingxichaxun"]
      },
      children: []
    }
  ]
};

export default medicalWasteManRouter;
