import request from "@/utils/request";

/*
 *获取综合平台监控数据
 */
export function integratedOperationMonitoring () {
  return request({
    url: `/iot/integratedOperationMonitoring/v2`,
    method: "get"
  });
}
/*
 *获取业务统计
 */
export function reportInspectionMedical () {
  return request({
    url: `/iot/reportInspectionMedical`,
    method: "get"
  });
}
/*
 *获取项目楼宇数
 */
export function getBuildingCount (projectId) {
  return request({
    url: `/nbiot/building/getCount/${projectId}`,
    method: "get"
  });
}
/*
 *获取供热测温饼图
 */
export function getSum (projectId) {
  return request({
    url: `/nbiot/equipment/getSum/${projectId}`,
    method: "get"
  });
}
/*
 *获取供热测温柱状图
 */
export function projectTemp (data) {
  return request({
    url: `/nbiot/equipment/projectTemp`,
    method: "post",
    data
  });
}
/*
 *获取供热测温详情柱状图
 */
export function buildingTemp (data) {
  return request({
    url: `/nbiot/equipment/buildingTemp`,
    method: "post",
    data
  });
}
/*
 *获取供热测温折线图
 */
export function projectTempCount (data) {
  return request({
    url: `/nbiot/equipment/projectTempCount`,
    method: "post",
    data
  });
}
/*
 *获取供热测温折线图表格
 */
export function projectTempPage (data) {
  return request({
    url: `/nbiot/equipment/projectTempPage`,
    method: "post",
    data
  });
}

/*
 *获取综合平台报警提示
 */
export function integratedAbnormalAlarms () {
  return request({
    url: `/iot/integratedAbnormalAlarms`,
    method: "get"
  });
}

/*
 *获取综合平台 异常趋势分析
 */
export function integratedABj () {
  return request({
    url: `/iot/integratedABj`,
    method: "get"
  });
}

/*
 *获取综合平台 异常统计
 */
export function integratedATj () {
  return request({
    url: `/iot/integratedATj`,
    method: "get"
  });
}

/*
 *获取综合平台监控详情
 */
export function findEquipNodeMonitorConfig (equipId) {
  return request({
    url: `/iot/findEquipNodeMonitorConfig/${equipId}`,
    method: "get"
  });
}

// 区域树
export function regionTree () {
  return request({
    url: `/iot/regionTree`,
    method: "get"
  });
}

// 添加区域
export function addRegion (data) {
  return request({
    url: `/iot/addRegion`,
    method: "post",
    data
  });
}

// 删除区域
export function removeRegion (id) {
  return request({
    url: `/iot/region/del/${id}`,
    method: "get"
  });
}

//报事查erp人员
export function spusPage (data) {
  return request({
    url: `/org/spusPage`,
    method: "post",
    data
  });
}
//报事提交
export function alarmPageROrder (data) {
  return request({
    url: `/report/order/alarmPageROrder`,
    method: "post",
    data
  });
}