<template>
  <!--盘点管理-->
  <div class="app-container">
    <div class="filter-container">
      <el-form ref="searchForm" class='clearfix' label-width="90px" @submit.native.prevent>
        <div class="fl">
          <el-form-item label="盘点日期：">
            <el-date-picker v-model="listQuery.createTime" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class='fr'>
          <el-radio-group v-model="listQuery.inventoryType" @change="radioChange">
            <el-radio-button label="1">库房盘点</el-radio-button>
            <el-radio-button label="2">科室盘点</el-radio-button>
          </el-radio-group>
          <el-input v-if="listQuery.inventoryType == 1" @focus="showStorageDlg()" v-model="listQuery.warehouseName" readonly placeholder="请选择库房">
            <i slot="suffix" @click="resetStr('warehouse')" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-input v-else-if="listQuery.inventoryType == 2" @focus="showBranchDlg()" v-model="listQuery.branchName" readonly placeholder="请选择部门">
            <i slot="suffix" @click="resetStr('branch')" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-input v-model="listQuery.str" placeholder='请输入盘点单号\盘点人'>
            <i slot="suffix" @click="resetStr('str')" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-button @click="searchItem" icon='el-icon-search' type="success" size='mini'>搜索</el-button>
          <el-button @click="resetItem" icon='el-icon-refresh' type="primary" size='mini'>重置</el-button>
        </div>
      </el-form>
    </div>

    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="盘点单号" width="300">
          <template slot-scope="scope">
            <span @click="viewItem(scope.row)" class="inventoryCode">{{ scope.row.inventoryCode }}</span>
          </template>
        </el-table-column>

        <el-table-column label="盘点日期" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.inventoryDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="盘点人" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.inventoryUserName }}</span>
          </template>
        </el-table-column>

        <el-table-column v-if="listQuery.inventoryType == 1" label="盘点库房">
          <template slot-scope="scope">
            <span>{{ scope.row.storageName }}</span>
          </template>
        </el-table-column>

        <el-table-column v-else-if="listQuery.inventoryType == 2" label="盘点科室">
          <template slot-scope="scope">
            <span>{{ scope.row.storageName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="盘点备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="盘点结果" width="150">
          <template slot-scope="scope">
            <span v-if="scope.row.isLoss == 0">盈{{ scope.row.lossNum }}亏0</span>
            <span v-else>盈0亏{{ scope.row.lossNum }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button icon="el-icon-view" type="success" size="mini" @click="viewItem(scope.row)" plain>查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination v-if="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="查看详情" :visible.sync="dlgShow">
      <el-form label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="盘点单号:">{{dlgData.inventoryCode}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="盘点数量:">{{dlgData.totalInventory}}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="盘点人:">{{dlgData.inventoryUserName}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="盘点日期:">{{dlgData.inventoryDate}}</el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注:">{{dlgData.remark}}</el-form-item>
      </el-form>
      <div class="table-container">
        <el-table class='m-small-table' :data="detailList" border fit highlight-current-row show-summary>
          <el-table-column label="序号" type="index" width="50" align="center">
          </el-table-column>

          <el-table-column label="商品名称">
            <template slot-scope="scope">
              <span>{{ scope.row.archivesName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="材质">
            <template slot-scope="scope">
              <span>{{ scope.row.clothMaterialText }}</span>
            </template>
          </el-table-column>

          <el-table-column label="规格">
            <template slot-scope="scope">
              <span>{{ scope.row.clothSpecificationText }}</span>
            </template>
          </el-table-column>

          <el-table-column label="总数量" prop="totalInventory">
            <template slot-scope="scope">
              <span>{{ scope.row.totalInventory }}</span>
            </template>
          </el-table-column>

          <el-table-column label="在库数量" prop="totalActual">
            <template slot-scope="scope">
              <span>{{ scope.row.totalActual }}</span>
            </template>
          </el-table-column>

          <el-table-column label="盘点数量" prop="totalActual">
            <template slot-scope="scope">
              <span>{{ scope.row.totalActual }}</span>
            </template>
          </el-table-column>

          <el-table-column label="盘亏数量(在库)" prop="lossNum">
            <template slot-scope="scope">
              <span>{{ scope.row.lossNum }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="page-container">
        <pagination v-show="detailTotal>0" :total="detailTotal" :page.sync="detailListQuery.page" :limit.sync="detailListQuery.size" @pagination="getDetailList" />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false">关闭</el-button>
      </div>
    </el-dialog>

    <storageDlg />
    <branchDlg />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import branchDlg from '@/components/Dialog/platformMan/branchDlg'
import storageDlg from '@/components/Dialog/clothingMan/storageDlg'

import {
  findInventoryDynamic,
  findInventoryInfo
} from '@/api/medicalMatchManSystem/clothingMan/checkMan'

export default {
  components: {
    Pagination,
    branchDlg,
    storageDlg
  },
  data () {
    return {
      // 主表格相关
      list: [
      ],
      listQuery: {
        page: 1,
        size: 20,
        str: '',
        createTime: "",
        inventoryType: '',
        warehouseId: '',
        warehouseName: '',
        branchId: '',
        branchName: ''
      },
      total: 0,
      listLoading: false,

      dlgShow: false,
      dlgData: {},
      detailListQuery: {
        page: 1,
        size: 10
      },
      detailTotal: 0,
      detailList: [
      ]
    }
  },

  computed: {
    ...mapGetters('platformMan/branchDlg', {
      branchId: 'branchId',
      branchName: 'branchName'
    }),
    ...mapGetters('clothingMan/storageDlg', {
      storageId: 'storageId',
      storageName: 'storageName'
    }),
  },

  watch: {
    branchId (val) {
      this.listQuery.branchId = val
    },
    branchName (val) {
      this.listQuery.branchName = val
    },
    storageId (val) {
      this.listQuery.warehouseId = val
    },
    storageName (val) {
      this.listQuery.warehouseName = val
    }
  },

  created () {
    this.getList()
  },

  methods: {
    // 科室相关
    showBranchDlg () {
      let branchId = this.listQuery.branchId
      let branchName = this.listQuery.branchName
      this.$store.commit('platformMan/branchDlg/SET_BRANCHID', branchId)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', branchName)
      this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', true)
    },

    // 库房相关
    showStorageDlg () {
      this.$store.commit('clothingMan/storageDlg/SET_STORAGEID', this.listQuery.warehouseId)
      this.$store.commit('clothingMan/storageDlg/SET_STORAGENAME', this.listQuery.warehouseName)
      this.$store.commit('clothingMan/storageDlg/SET_DLGSHOW', true)
    },

    // 主表格相关
    radioChange (val) {
      this.listQuery.inventoryType = val
      this.getList()
    },

    resetStr (flag) {
      if (flag == 'str') {
        this.listQuery.str = ""
      }
      else if (flag == 'warehouse') {
        this.listQuery.warehouseId = ""
        this.listQuery.warehouseName = ""
      }
      else if (flag == 'branch') {
        this.listQuery.branchId = ""
        this.listQuery.branchName = ""
      }
      this.getList()
    },

    searchItem () {
      this.getList()
    },

    resetItem () {
      this.listQuery.str = ""
      this.listQuery.createTime = ""
      this.listQuery.inventoryType = ""
      this.listQuery.warehouseId = ""
      this.listQuery.warehouseName = ""
      this.listQuery.branchId = ""
      this.listQuery.branchName = ""
      this.getList()
    },

    getList () {
      if (utils.isNull(this.listQuery.createTime)) {
        this.listQuery.startTime = ""
        this.listQuery.endTime = ""
      }
      else {
        this.listQuery.startTime = this.listQuery.createTime[0]
        this.listQuery.endTime = this.listQuery.createTime[1]
      }
      if (this.listQuery.inventoryType == 1) {
        this.listQuery.storageId = this.listQuery.warehouseId
      } else if (this.listQuery.inventoryType == 2) {
        this.listQuery.storageId = this.listQuery.branchId
      } else {
        this.listQuery.storageId = ''
      }
      this.list = []
      this.listLoading = true
      findInventoryDynamic(this.listQuery).then(res => {
        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    getDetailList () {
      let postParam = this.detailListQuery
      postParam['inventoryId'] = this.dlgData.id
      this.detailList = []
      this.detailTotal = 0
      findInventoryInfo(postParam).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let data = res.data.data
          this.detailList = utils.isNull(res.data.list) ? [] : res.data.list
          this.detailTotal = utils.isNull(data) ? 0 : data.total
          for (let i in this.detailList) {
            if (this.detailList[i]['isLoss'] == 0) {
              this.detailList[i]['lossNum'] = 0
            }
          }
        } else {
          this.$message.error(msg)
        }
      })
    },

    viewItem (data) {
      this.dlgData = data
      this.dlgShow = true
      this.getDetailList()
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.inventoryCode {
  color: #409eff;
  text-decoration: underline;
  cursor: pointer;
}
</style>