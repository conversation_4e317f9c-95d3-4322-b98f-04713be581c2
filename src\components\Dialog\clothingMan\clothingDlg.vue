<template>
  <el-dialog :close-on-click-modal='false' :title="'被服列表'" :visible.sync="dlgShow">
    <div class="filter-container">
      <div class='fr'>
        <el-select v-model="listQuery.clothType" filterable clearable placeholder="请选择被服类型">
          <el-option v-for="item in clothTypeList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
        <el-input v-model="listQuery.str" placeholder='请填写被服名称'>
          <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
        </el-input>
        <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">
          搜索
        </el-button>
        <el-button icon='el-icon-refresh' type="primary" size='mini' @click="resetItem">重置</el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectClothingId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>

        <el-table-column label="名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="类型">
          <template slot-scope="scope">
            <span>{{ scope.row.clothTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="材质">
          <template slot-scope="scope">
            <span>{{ scope.row.clothMaterialText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="规格">
          <template slot-scope="scope">
            <span>{{ scope.row.clothSpecificationText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计量">
          <template slot-scope="scope">
            <span>{{ scope.row.clothUnitText }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'

import { findArchivesDynamic } from '@/api/medicalMatchManSystem/clothingMan/clothingArchives'


export default {
  components: {
    Pagination
  },
  data() {
    return {
      clothTypeList: [],

      list: [],

      listQuery: {
        page: 1,
        size: 10,
        str: "",
        clothType: ""
      },

      total: 0,

      selectClothingId: '',

      selectClothingName: ''

    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.clothingMan.clothingDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('clothingMan/clothingDlg/SET_DLGSHOW', val)
      }
    },

    clothingId: {
      get: function () {
        return this.$store.state.clothingMan.clothingDlg.clothingId
      },
      set: function (val) {
        this.$store.commit('clothingMan/clothingDlg/SET_CLOTHINGID', val)
      }
    },

    clothingName: {
      get: function () {
        return this.$store.state.clothingMan.clothingDlg.clothingName
      },
      set: function (val) {
        this.$store.commit('clothingMan/clothingDlg/SET_CLOTHINGNAME', val)
      }
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.clothingId)) {
          this.selectClothingId = ""
          this.selectClothingName = ""
        }
        this.getList()
        utils.getDataDict(this, 'clothType', 'clothTypeList')
      }
    },

    clothingId(val) {
      this.selectClothingId = val
    },

    clothingName(val) {
      this.selectClothingName = val
    }
  },

  methods: {
    resetStr() {
      this.listQuery.str = ""
      this.getList()
    },

    resetItem() {
      this.listQuery.str = ""
      this.listQuery.clothType = ""
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    rowClick(row, column, event) {
      this.selectClothingId = row['id']
      this.selectClothingName = row['name']
    },

    getList() {
      this.list = []
      findArchivesDynamic(this.listQuery).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      this.clothingId = this.selectClothingId
      this.clothingName = this.selectClothingName
      this.$store.commit('clothingMan/clothingDlg/SET_CLOTHINGID', this.clothingId)
      this.$store.commit('clothingMan/clothingDlg/SET_CLOTHINGNAME', this.clothingName)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('clothingMan/clothingDlg/SET_DLGSHOW', false)
    }
  }
}
</script>