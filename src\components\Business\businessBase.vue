<template>

</template>

<script>
import { mapGetters } from 'vuex'
import * as utils from '@/utils'

export default {
  name: 'BusinessBase',

  data () {
    return {
      listQueryCache: {},

      userInfo: JSON.parse(window.localStorage.ERPUserInfo)
    }
  },

  computed: {
    listQueryStore: {
      get: function () {
        return this.$store.state.businessBase.listQueryStore
      },
      set: function (val) {
        this.$store.commit('businessBase/SET_LISTQUERY', val)
      }
    },
  },

  watch: {
    listQueryStore: {
      handler (val) {

      },
      deep: true,
      immediate: true
    }
  },

  created () {
    if (!utils.isNull(this.listQueryStore) && !utils.isEmptyObject(this.listQueryStore)) {
      this.listQueryCache = this.listQueryStore[this.$options.name]
    }
  },

  beforeDestroy () {
    console.log('beforeDestroy')
    this.$store.commit('businessBase/SET_NAME', this.$options.name)
    this.$store.commit('businessBase/SET_LISTQUERY', this.listQuery)
  },

  methods: {
    // 搜索触发 分页置1
    searchFunc () {
      this.listQuery.page = 1
      this.getList()
    },

    // 清空搜索条件
    clearQuery (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.searchFunc()
    },
  }
}
</script>