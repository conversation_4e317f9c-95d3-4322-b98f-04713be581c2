<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' 
      title="选择部门" 
      
      :append-to-body='true'
      :visible.sync="bmsState" 
      width='900px' 
      top='30px'
      icon-class='el-icon-info'>
      <div class="dialog-bms-bar">
        <!-- 部门 -->
        <div class='dialog-bms-left'>
          <el-input
            placeholder="输入关键字进行过滤"
            style="margin-bottom: 10px;"
            v-model="bmsText">
          </el-input>
          <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
          <div class="bms-tree">
            <el-tree 
              :data="bmsData" 
              ref="bmsDom"
              default-expand-all
              :filter-node-method="bmsFilter"
              :props="defaultProps"
              node-key="id"
              @node-click="bmsClick">
            </el-tree>
          </div>
        </div>
        <div class='dialog-bms-right'>
          <!-- <el-button v-for='(item, index) of bmsArr2' :key='index' class='bms-a' type='success' size='mini' plain>{{ item.relationName }}<i class="el-icon-close" @click='delBmFunc(item.relationId, item.relationName)'></i></el-button> -->
          <el-button v-for='(item, index) of bmsArr2' :key='index' class='bms-a' type='success' size='mini' plain>{{ item.relationName }}</el-button>

        </div>
        <!-- 部门多选框 -->
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { 
  supplierPage,  // 获取供应商列表
} from '@/api/supplyChainApi'

export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      // 树
      treeData: [],
      treeSelect: {},
      treeText: '',  // 部门左侧筛选

      // 弹窗相关
      treeType: '',  // 树类型，供应商-gys
      treeQuery: '',  // 查询条件
      treeSet: '',  // 设置初始值
      treeGet: '',  // 返回值


      // bmsArr2

      // bmsData: [],
      // bmsSelect: {},
      // bmsText: '',  // 部门左侧筛选

      // 右侧选中的样式
      // bmsIds: [],
      // bmsNames: [],
      // bmsArr2: [],

      // 树过滤
      // defaultProps: {
      //   children: 'children',
      //   label: 'label',
      // }

    }
  },
  computed: {
    // ...mapGetters([
    // ]),
    
    gylTreeSelectsState: {
      get: function() {
        let state = this.$store.getters.gylTreeSelectsState

        if (state === false) {
          this.bmsArr2 = []
        }

        setTimeout(() => {
          this.treeType = this.$store.getters.gylTreeSelectsType
          this.treeQuery = this.$store.getters.gylTreeSelectsQuery
          this.treeSet = this.$store.getters.gylTreeSelectsSet
        }, 50)
        
        return state
      },
      set: function(newVal) {
        this.$store.commit('SET_BMSSTATE', newVal)
      }
    }
    // bmsArr: {
    //   get: function() {
    //     let bmsArr = this.$store.getters.bmsArr
        
    //     return bmsArr
    //   },
    //   set: function(newVal) {
    //   }
    // },

  },
  watch: {
    bmsText(val) {
      this.$refs.bmsDom.filter(val);
    },
    gylTreeSelectsSet(val) {
      this.bmsArr2 = JSON.parse(JSON.stringify(val))
    }
  },
  created() {
    this.findOrgBranchAll()
  },
  methods: {
    // 获取树
    getTree() {
      supplierPage().then(res => {
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code === '200') {
          this.bmsData = JSON.parse(JSON.stringify(res.data.list))
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 【【 左侧相关
    
    // 获取部门
    findOrgBranchAll() {
      findOrgBranchAll().then(res => {
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code === '200') {
          this.bmsData = JSON.parse(JSON.stringify(res.data.list))
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 节点左键点击事件
    bmsClick(data, node, mNode) {
      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      let relationId = data.id
      let relationName = data.label
      let isHas = this.bmsArr2.every(item => {
        return item.relationId !== relationId
      })

      if (isHas) {
        this.bmsArr2.push({relationId, relationName})
      } else {
        this.$message({
          type: 'warning',
          message: '所选部门已在列表中，请勿重复添加。'
        })
      }
    },
    // 筛选部门
    bmsFilter(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 】】 左侧相关

    // 【【 右侧相关
    // 右侧删除部门方法
    delBmFunc(relationId, relationName) {
      let nArr = this.bmsArr2.filter(item => {
        return item.relationId !== relationId
      })
      this.bmsArr2 = JSON.parse(JSON.stringify(nArr))
    },
    // 】】 右侧相关

    // 【【 其他
    // 选择部门提交
    bumenOkFunc() {
      this.$store.commit('SET_BMSARR', this.bmsArr2)
      this.closeDialog()
    },
    // 关闭弹窗 
    closeDialog() {
      this.$store.commit('SET_BMSSTATE', false)
    }
    // 】】 其他
    
    

    // 筛选岗位
    // filterBmrightListOld
    // filterGangwei(val) {
    //   this.filterBmrightList = JSON.parse(JSON.stringify(this.filterBmrightListOld))
    //   this.filterBmrightList = this.filterBmrightList.filter(item => {
    //     return item.label.indexOf(val) >= 0
    //   })
    // },

    
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dialog-bms-bar {
  display: flex;
  justify-content: space-between;
}
.dialog-bms-left,.dialog-bms-right {
  width: 49%;
}
.bms-tree {
  height: 400px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
}
.dialog-bms-right {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 10px;
  .bms-a {
    margin-bottom: 10px;
    margin-left: 0px;
    margin-right: 6px;
  }
  i {
    display: inline-block;
    margin-left: 3px;

  }
}
</style>