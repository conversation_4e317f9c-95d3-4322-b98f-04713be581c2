<template>
  <div class="app-container print">
    <div
      ref="paperContainer"
      :style="`width:${paperWidth}px;height:${paperHeight}px;`"
      class="paper-container clearfix"
      style="margin-top: -16px"
    >
      <div class="tac" style="font-size: 17px; letter-spacing: 3px">东方万弘物业管理有限公司专用票据</div>
      <div class="tac" style="margin-top: 2px; font-size: 12px; position: relative">
        <div class="tac">票据号：PJ-1458964141235655785214445</div>
        <div class="printDate">{{ printDate }}</div>
      </div>
      <table class="m-table" style="width: 100%; margin-top: 2px">
        <tr>
          <td colspan="4">
            <div class="flex">
              <div class="" style="padding-left: 35px">今收到</div>
              <div>哈尔滨万线科技有限责任公司3</div>
              <div>A2-1202</div>
              <div>刘老根</div>
              <div class="" style="padding-right: 35px">交到下列款项</div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="text-bold tac" style="width: 180px">费用名称</td>
          <td class="text-bold tac" colspan="2">描述</td>
          <td class="text-bold tac" style="width: 160px">金额</td>
        </tr>
        <tr v-for="(item, index) of fieldList" :key="index">
          <td style="height: 17px">
            {{ item.name }}
          </td>
          <td colspan="2">{{ item.desc }}</td>
          <td class="tac">{{ item.money }}</td>
        </tr>
        <tr>
          <td class="tac">合 计</td>
          <td class="text-bold tac" style="width: 110px">人民币(大写)</td>
          <td>{{ dealBigMoney(moneyTotal) }}</td>
          <td class="tac">{{ moneyTotal }}</td>
        </tr>

        <tr>
          <td class="tac">备 注</td>
          <td colspan="3">
            {{ remark }}
            <!-- <div class="flex">
              <div class="flex1">{{ remark }}</div>
              <div style="width: 200px">
                <span class="text-bold">优惠金额：</span>
                {{ yhMoney }}
              </div>
            </div> -->
          </td>
        </tr>
      </table>
      <div class="flex" style="margin-top: 2px; font-size: 12px">
        <div class="tac" style="width: 180px">请妥善保管，谢谢合作</div>
        <div class="flex1 tac">收款人：{{ skr }}</div>
        <div style="width: 180px" class="tac">交款人：{{ jkr }}</div>
      </div>

      <!-- 图片 -->
      <img src="/static/image/print/print_logo.png" class="imgLogo" />
      <img src="/static/image/print/print_zhang.png" class="imgZhang" />

      <!-- <div
        v-for="(item, index) in fieldList"
        :key="index"
        class="field"
        :style="`left:${item.left}px;top:${item.top}px;`"
      >
        {{ item.label }}
      </div> -->
    </div>
    <el-button class="no-print" icon="el-icon-printer" type="primary" size="medium" @click="printFunc()">
      打印</el-button
    >
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import * as utils from '@/utils'
import { return2Num } from '@/utils/calendarData'

// import {
//   findInfoById,
// } from '@/api/jyt/drugMail'

export default {
  components: {},
  data() {
    return {
      paperHeight: 0,
      paperWidth: 0,
      fieldList: [
        { name: '住宅物业管理费', desc: '111', money: '10' },
        { name: '电梯费', desc: '222', money: '20' },
        { name: '', desc: '', money: '' },
        { name: '', desc: '', money: '' },
      ],
      id: '',

      // 合计
      moneyTotal: '19540.21', // 合计
      moneyTotalStr: '叁拾圆整', // 合计大写
      remark: '一条备注',
      yhMoney: '10', // 优惠金额
      skr: '张三', // 收款人
      jkr: '李四', // 交款人

      printDate: '',
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.setPrintDate()
      // console.log('utils.getDpiWidth(227)', utils.getDpiWidth(227))
      // this.paperWidth = utils.getDpiWidth(227)
      // this.paperHeight = utils.getDpiHeight(122)

      this.paperWidth = utils.getDpiWidth(188)
      this.paperHeight = utils.getDpiHeight(160)

      // this.paperWidth = utils.getDpiWidth(210)
      // this.paperHeight = utils.getDpiHeight(140)
      this.id = this.$route.query.id
      // this.getOrderInfo()
    })
  },
  methods: {
    setPrintDate() {
      let today = new Date()
      let year = today.getFullYear()
      let month = return2Num(today.getMonth() + 1)
      let day = return2Num(today.getDate())
      this.printDate = `${year} 年 ${month} 月 ${day} 日`
    },

    dealBigMoney(val) {
      console.log('之后的', utils.dealBigMoney(val))
      return utils.dealBigMoney(val)
    },

    // 获取订单信息
    getOrderInfo() {
      if (utils.isNull(this.id)) {
        this.$message.warning('暂无该邮寄订单')
        return
      }
      findInfoById(this.id).then((res) => {
        if (res.data.code == 200) {
          // let data = res.data.data
          // fieldList.push([28, 53, data.receiveName])
          // fieldList.push([70, 53, data.receivePhoneFirst])
          // fieldList.push([92, 53, data.receivePhoneSecond])
          // fieldList.push([26, 65, `${data.province} ${data.city} ${data.county} ${data.addr}`])
          // fieldList.push([30, 80, data.city])
          // if (data.isInsuredprice == 1) {
          //   fieldList.push([26, 109, '✓'])
          //   fieldList.push([100, 109, data.insuredAmount])
          // } else {
          //   fieldList.push([34, 109, '✓'])
          // }
          // this.renderFields()
        } else {
          this.$message.warning('暂无该邮寄订单')
        }
      })
    },

    // 渲染字段
    // renderFields() {
    //   for (let i of fieldList) {
    //     this.fieldList.push({
    //       left: utils.getDpiWidth(i[0]),
    //       top: utils.getDpiHeight(i[1]),
    //       label: i[2],
    //     })
    //   }
    // },

    // 打印
    printFunc() {
      window.print()
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-container.print {
  font-size: 14px;
}
.app-container {
  font-size: 12px;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 0;

  .paper-container {
    // background: url('/static/image/paper.jpg') no-repeat center;
    // background-size: 100%;
    background: #fff;
    position: relative;
    box-sizing: border-box;
    padding: 16px;
    // padding-top: 16px;
    .field {
      position: absolute;
      max-width: 360px;
      word-break: break-all;
    }
  }
  .el-button.no-print {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

@media print {
  .no-print {
    display: none;
  }
}

//
// 表格
.tac {
  text-align: center;
}
.tar {
  text-align: right;
}
.text-bold {
  font-weight: bold;
}
.mt16 {
  margin-top: 16px;
}
.m-table {
  // background:#666;
  border-top: 1px solid #666;
  border-left: 1px solid #666;
  border-spacing: 0px;
  font-size: 12px;
  td {
    background: #fff;
    min-height: 30px;
    box-sizing: border-box;
    padding: 1px;
    border-right: 1px solid #666;
    border-bottom: 1px solid #666;
  }
  .mh {
    min-height: 36px;
  }
  .m-input .el-input__inner {
    border: none;
    border-radius: 0;
    border-bottom: 1px solid #666;
    padding: 0;
    height: 18px;
    line-height: 18px;
  }
}

// 页面
.imgLogo {
  position: absolute;
  left: 30px;
  top: 17px;
  width: 40px;
  height: 36px;
}
.imgZhang {
  position: absolute;
  left: 20px;
  top: 204px;
  width: 240px;
  height: 40px;
}
.printDate {
  position: absolute;
  right: 0px;
  top: 0px;
}
</style>