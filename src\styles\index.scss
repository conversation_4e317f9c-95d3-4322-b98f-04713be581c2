@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./btn.scss";
@import "./resert.scss";

@import "./dialog.scss";
@import "./formResert.scss";
@import "./searchResert.scss";
@import "./tableResert.scss";
@font-face {
  font-family: 'DIN-Dlglb';
  src: url('../font/DS-DIGI-1.ttf')
};
::-webkit-scrollbar {
  width: 6px;
  // height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #d0d0d0;
  border-radius: 4px;
}

.el-menu--vertical {
  max-height: 600px;
  overflow-y: auto;
}

body {
  font-size: 14px;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.upload-wrap {
  display: inline-block;
}

.avatar_icon {
  position: absolute;
  cursor: pointer;
  right: 0;
  top: 0;
  font-size: 20px;
  color: #f56c6c;
}

.el-image-viewer__wrapper {
  z-index: 99999 !important;
}

code {
  background: #eef1f6;
  padding: 15px 16px;
  margin-bottom: 20px;
  display: block;
  line-height: 36px;
  font-size: 15px;
  font-family: "Source Sans Pro", "Helvetica Neue", Arial, sans-serif;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.warn-content {
  background: rgba(66, 185, 131, 0.1);
  border-radius: 2px;
  padding: 16px;
  padding: 1rem;
  line-height: 1.6rem;
  word-spacing: 0.05rem;

  a {
    color: #42b983;
    font-weight: 600;
  }
}

//main-container全局样式
.app-container {
  position: relative;
  height: 100%;
  padding: 20px;
  background: #fff;
}

.tab-container {
  height: 100%;

  .el-tabs__content {
    height: calc(100% - 55px);

    .el-tab-pane {
      height: 100%;

      .tab-panel {
        height: 100%;
      }
    }
  }
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.font24 {
  font-size: 24px;
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    opacity: 0.7;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}
