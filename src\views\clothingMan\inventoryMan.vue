<template>
  <!--库存管理-->
  <div class="app-container">
    <div class="filter-container">
      <el-form ref="searchForm" class='clearfix' label-width="90px" @submit.native.prevent>
        <div class="n-search-bar">
          <div class='n-search-item n-search-item-r fr'>
            <el-input @focus="showStorageDlg()" v-model="listQuery.storageName" readonly placeholder="请选择库房"></el-input>
            <el-input @focus="showClothDlg()" v-model="listQuery.archivesName" readonly placeholder="请选择被服档案"></el-input>
            <el-input v-model="listQuery.strweb" placeholder='请输入被服名称\材质\规格'>
              <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
            </el-input>
            <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">搜索</el-button>
            <el-button icon='el-icon-refresh' type="primary" size='mini' @click="resetItem">重置</el-button>
          </div>
        </div>
      </el-form>
    </div>

    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="品名">
          <template slot-scope="scope">
            <span>{{ scope.row.archives.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="类型">
          <template slot-scope="scope">
            <span>{{ scope.row.archives.clothTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="材质">
          <template slot-scope="scope">
            <span>{{ scope.row.archives.clothMaterialText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="规格">
          <template slot-scope="scope">
            <span>{{ scope.row.archives.clothSpecificationText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计量">
          <template slot-scope="scope">
            <span>{{ scope.row.archives.clothUnitText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="品牌">
          <template slot-scope="scope">
            <span>{{ scope.row.archives.brand }}</span>
          </template>
        </el-table-column>

        <el-table-column label="供应商">
          <template slot-scope="scope">
            <span>{{ scope.row.archives.manufacturer }}</span>
          </template>
        </el-table-column>

        <el-table-column label="总数量">
          <template slot-scope="scope">
            <span>{{ scope.row.count }}</span>
          </template>
        </el-table-column>

        <el-table-column label="在库数量">
          <template slot-scope="scope">
            <span>{{ scope.row.storageCount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="在用数量">
          <template slot-scope="scope">
            <span>{{ scope.row.useCount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button icon="el-icon-view" type="success" size="mini" @click="viewItem(scope.row)" plain>查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="查看详情" :visible.sync="dlgShow" width="80%">
      <el-form label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称:">
              {{dlgData.name}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型:">
              {{dlgData.archives.clothTypeText}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="规格:">
              {{dlgData.archives.clothSpecificationText}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="材质:">
              {{dlgData.archives.clothMaterialText}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注:">
          {{dlgData.archives.remark}}
        </el-form-item>
      </el-form>
      <div class="table-container">
        <el-table class='m-small-table' :data="detailList" border fit highlight-current-row>
          <el-table-column label="序号" type="index" width="50" align="center">
          </el-table-column>

          <el-table-column label="商品名称">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="材质">
            <template slot-scope="scope">
              <span>{{ scope.row.clothMaterialText }}</span>
            </template>
          </el-table-column>

          <el-table-column label="规格">
            <template slot-scope="scope">
              <span>{{ scope.row.clothSpecificationText }}</span>
            </template>
          </el-table-column>

          <el-table-column label="使用寿命(月)" width="120px">
            <template slot-scope="scope">
              <span>{{ scope.row.serviceLife }}</span>
            </template>
          </el-table-column>

          <el-table-column label="洗涤次数(次)" width="120px">
            <template slot-scope="scope">
              <span>{{ scope.row.washTotalCount }}</span>
            </template>
          </el-table-column>

          <el-table-column label="入库日期" width="150px">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime }}</span>
            </template>
          </el-table-column>

          <el-table-column label="RFID" width="240px">
            <template slot-scope="scope">
              <span>{{ scope.row.rfid }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="page-container">
        <pagination v-show="detailTotal>0" :total="detailTotal" :page.sync="detailListQuery.page" :limit.sync="detailListQuery.size" @pagination="getDetailList" />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false">关闭</el-button>
      </div>
    </el-dialog>

    <clothingDlg />
    <storageDlg />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import storageDlg from '@/components/Dialog/clothingMan/storageDlg'
import clothingDlg from '@/components/Dialog/clothingMan/clothingDlg'

import {
  findStockCountByStorageWeb,
  findStockNoRfid
} from '@/api/medicalMatchManSystem/clothingMan/inventoryMan'

export default {
  components: {
    Pagination,
    storageDlg,
    clothingDlg
  },
  data () {
    return {
      //主表格
      list: [],
      listQuery: {
        page: 1,
        size: 20,
        strweb: '',
        storageId: '',
        storageName: '',
        archivesId: '',
        archivesName: ''
      },
      total: 0,
      listLoading: false,
      dlgShow: false,
      dlgData: {
        archives: {}
      },
      detailListQuery: {
        page: 1,
        size: 10
      },
      detailTotal: 0,
      detailList: [
      ]
    }
  },

  computed: {
    ...mapGetters('clothingMan/storageDlg', {
      storageId: 'storageId',
      storageName: 'storageName'
    }),
    ...mapGetters('clothingMan/clothingDlg', {
      clothingId: 'clothingId',
      clothingName: 'clothingName'
    }),
  },

  watch: {
    storageId (val) {
      this.listQuery.storageId = val
    },
    storageName (val) {
      this.listQuery.storageName = val
    },
    clothingId (val) {
      this.listQuery.archivesId = val
    },
    clothingName (val) {
      this.listQuery.archivesName = val
    }
  },

  created () {
    this.getList()
  },

  methods: {
    // 库房相关
    showStorageDlg () {
      this.$store.commit('clothingMan/storageDlg/SET_STORAGEID', this.listQuery.storageId)
      this.$store.commit('clothingMan/storageDlg/SET_STORAGENAME', this.listQuery.storageName)
      this.$store.commit('clothingMan/storageDlg/SET_DLGSHOW', true)
    },

    // 被服相关
    showClothDlg () {
      this.$store.commit('clothingMan/clothingDlg/SET_CLOTHINGID', this.listQuery.archivesId)
      this.$store.commit('clothingMan/clothingDlg/SET_CLOTHINGNAME', this.listQuery.archivesName)
      this.$store.commit('clothingMan/clothingDlg/SET_DLGSHOW', true)
    },


    // 主表格相关
    searchItem () {
      this.getList()
    },

    resetItem () {
      this.listQuery.strweb = ""
      this.listQuery.storageId = ""
      this.listQuery.storageName = ""
      this.listQuery.archivesId = ""
      this.listQuery.archivesName = ""
      this.getList()
    },

    resetStr () {
      this.listQuery.strweb = ""
      this.getList()
    },

    getList () {
      this.list = []
      this.listLoading = true
      findStockCountByStorageWeb(this.listQuery).then(res => {
        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    getDetailList (option) {
      this.detailListQuery['archivesId'] = this.dlgData.archivesId
      this.detailListQuery['storageId'] = this.listQuery.storageId
      this.detailList = []
      findStockNoRfid(this.detailListQuery).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.detailList = utils.isNull(res.data.list) ? [] : res.data.list
          this.detailTotal = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    viewItem (data) {
      this.dlgData = data
      this.dlgShow = true
      this.$nextTick(() => {
        this.getDetailList()
      })
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>