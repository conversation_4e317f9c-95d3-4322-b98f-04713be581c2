/**
 * 技术资料管理 API
 * 设备管理 - 技术资料管理
 */
import request from "@/utils/request";

// 获得技术资料全部数据分页
export function getAllPage(data) {
  return request({
    url: `/sa/green/equ/technical-information/allPage`,
    method: "post",
    data,
  });
}

// 创建技术资料
export function createTechnicalData(data) {
  return request({
    url: `/sa/green/equ/technical-information/create`,
    method: "post",
    data,
  });
}

// 删除技术资料
export function deleteTechnicalData(id) {
  return request({
    url: `/sa/green/equ/technical-information/delete?id=${id}`,
    method: "delete",
  });
}

// 获得技术资料（包含原始文件和附件）
export function getAllItem(id) {
  return request({
    url: `/sa/green/equ/technical-information/getAllItem?id=${id}`,
    method: "get",
  });
}

// 获得技术资料（不含原始文件和附件）
export function getItem(id) {
  return request({
    url: `/sa/green/equ/technical-information/getItem?id=${id}`,
    method: "get",
  });
}

// 获得技术资料修改记录
export function getOldList(query) {
  return request({
    url: `/sa/green/equ/technical-information/getOldList`,
    method: "get",
    params: query,
  });
}

// 获得技术资料类型
export function getTypeMap(query) {
  return request({
    url: `/sa/green/equ/technical-information/getTypeMap`,
    method: "get",
    params: query,
  });
}

// 获得技术资料部分数据分页（不含原始文件和附件）
export function getPage(data) {
  return request({
    url: `/sa/green/equ/technical-information/page`,
    method: "post",
    data,
  });
}

// 更新技术资料
export function updateTechnicalData(data) {
  return request({
    url: `/sa/green/equ/technical-information/update`,
    method: "put",
    data,
  });
}
