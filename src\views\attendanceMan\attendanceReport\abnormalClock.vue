<template>
  <!-- 考勤管理 - 异常打卡 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="考勤日期：">
          <el-date-picker v-model="listQuery.rangeDate" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入员工姓名' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="listQuery.state" placeholder="请选择">
            <el-option v-for="item in stateList" :key="item.id" :label="item.label" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="部门">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="异常卡次" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.punchInfo }}</span>
          </template>
        </el-table-column>

        <el-table-column label="异常次数" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.errorNum }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="200px" align="center">
          <template slot-scope="scope">
            <el-tag type="info" v-if="scope.row.isAudit == 0">未处理</el-tag>
            <el-tag type="success" v-if="scope.row.isAudit == 1"> 确认正常</el-tag>
            <el-tag type="danger" v-if="scope.row.isAudit == 2">确认异常</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="140" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-if="scope.row.isAudit != 0" type="success" size="mini" icon="el-icon-view" plain @click="viewItem(scope.row)">查看</el-button>
            <el-button v-else type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">处理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="'异常打卡情况'" :visible.sync="dlgShow" width='600px' top="30px" append-to-body>

      <el-form ref="dlgForm" :model="dlgData">
        <el-row>
          <el-col :span="8">
            <el-form-item label="姓名">
              {{dlgData.userName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门">
              {{dlgData.branchName}}
            </el-form-item>
          </el-col>
          <el-col :span="8" class="image-wrapper absolute">
            <el-form-item label="录入照片">
              <el-image :src="dlgData.imgUrl" @click="onPreview(dlgData.imgUrl)">
                <div class="el-image__error" slot="error">
                  暂无录入照片
                </div>
              </el-image>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="异常卡次">
              {{dlgData.punchInfo}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="异常次数">
              {{dlgData.errorNum}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-tag type="info" v-if="dlgData.isAudit == 0">未处理</el-tag>
              <el-tag type="success" v-if="dlgData.isAudit == 1"> 确认正常</el-tag>
              <el-tag type="danger" v-if="dlgData.isAudit == 2">确认异常</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider>疑似异常打卡照片</el-divider>
        <el-row class="image-wrapper-list">
          <el-col :span="8" class="image-wrapper" v-for="item in dlgData.punchList" :key="item.id">
            <el-form-item>
              <el-image :src="item.faceUrl" @click="onPreview(item.faceUrl)">
              </el-image>
              <div>打卡时间：{{item.punchDate.split(" ")[1]}}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <template v-if="dlgType == 'EDIT'">
          <el-button type='danger' :loading='dlgLoading' @click="subDlg(2)" icon="el-icon-close">
            确认异常
          </el-button>
          <el-button type='success' :loading='dlgLoading' @click="subDlg(1)" icon="el-icon-check">
            确认正常
          </el-button>
        </template>
      </div>
    </el-dialog>
    <el-image-viewer v-show="showViewer" :on-close="closeViewer" ref="imageViewer" :url-list="urlList" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findErrorPunchList, findErrorPunchInfo, updateErrorPunchStatus } from '@/api/attendanceMan/abnormalClock.js'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { uploadImg } from '@/utils/uploadImg'

export default {
  components: {
    Pagination,
    ElImageViewer
  },
  data () {
    return {
      showViewer: false,
      urlList: [],

      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',  // ADD\EDIT

      // 弹窗数据
      dlgData: {},
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        state: '0,1,2',
        rangeDate: ''
      },
      stateList: [
        {
          id: '0,1,2',
          label: '全部'
        },
        {
          id: '0',
          label: '未处理'
        },
        {
          id: '1',
          label: '确认正常'
        },
        {
          id: '2',
          label: '确认异常'
        }
      ],
      count: 0
    }
  },
  computed: {

  },
  watch: {

  },

  created () {

  },

  mounted () {
    this.$nextTick(() => {
      $(this.$refs.imageViewer.$el).children('.el-image-viewer__mask')[0].addEventListener('click', () => {
        this.closeViewer()
      })
    })
  },

  methods: {

    onPreview (pic) {
      this.urlList = [pic]
      this.showViewer = true
    },

    // 关闭查看器
    closeViewer () {
      this.showViewer = false
    },


    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      this.listQuery.startDate = ''
      this.listQuery.endDate = ''
      if (!utils.isNull(this.listQuery.rangeDate)) {
        this.listQuery.startDate = this.listQuery.rangeDate[0]
        this.listQuery.endDate = this.listQuery.rangeDate[1]
      }
      findErrorPunchList(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    viewItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgType = 'VIEW'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
      findErrorPunchInfo(data.id).then(res => {
        if (res.data.code == 200) {
          this.dlgData = JSON.parse(JSON.stringify(res.data.data))
        }
      })
    },

    // 弹窗提交
    subDlg (status) {
      let confirmTitle = status == 1 ? '确认正常?' : '确认异常?'
      this.$confirm(confirmTitle, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dlgLoading = true
        updateErrorPunchStatus(this.dlgData.id, status).then(res => {
          this.dlgLoading = false
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.dlgShow = false
            this.getList()
            utils.setBubble()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })

    },

    // 编辑
    editItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
      findErrorPunchInfo(data.id).then(res => {
        if (res.data.code == 200) {
          this.dlgData = JSON.parse(JSON.stringify(res.data.data))
        }
      })
    },
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-divider {
  margin-top: 50px;
}

.image-wrapper {
  /deep/ .el-image {
    width: 150px;
    height: 150px;
  }

  .el-image + div {
    width: 150px;
    text-align: center;
  }
}

.image-wrapper.absolute {
  position: absolute;
  z-index: 1;
  right: 0;
  top: 0;
}

.image-wrapper-list {
  height: 480px;
  overflow-y: auto;
}

.el-image-viewer__wrapper {
  z-index: 99999 !important;
}
</style>


