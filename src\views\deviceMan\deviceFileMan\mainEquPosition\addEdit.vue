<template>
  <!-- 设备管理-设备档案管理-设备地址维护 -->
  <div>
    <el-dialog
      @close="onClose"
      :title="title"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        :disabled="dlgType == 'info'"
      >
        <el-form-item label="设备所在位置" prop="equPosition">
          <el-input
            type="textarea"
            v-model="formData.equPosition"
            placeholder="请输入设备所在位置"
            clearable
            :rows="5"
            maxlength="500"
            @input="handleInput"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          v-show="dlgType !== 'info'"
          type="success"
          @click="handleConfirm"
          :loading="btnLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { postAction, getAction, deleteAction, putAction } from "@/api";
import { getDataDict } from "@/utils";
import { mapGetters } from "vuex";

export default {
  inheritAttrs: false,
  components: {},
  props: {
    dlgType: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      title: "添加",
      dialogVisible: false,
      btnLoading: false,
      formData: {
        projectId: JSON.parse(window.localStorage.userInfo).projectId,
        projectName: JSON.parse(window.localStorage.userInfo).projectName,
        equPosition: undefined,
      },
      rules: {
        equPosition: [
          {
            required: true,
            message: "必填字段",
            trigger: "change",
          },
        ],
      },
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleInput(value) {
      // 移除换行符
      this.formData.equPosition = value.replace(/\r?\n|\r/g, "");
    },
    onClose() {
      this.$refs["elForm"].resetFields();
      this.dialogVisible = false;
    },

    handleConfirm() {
      if (this.dlgType === "info") {
        this.resetDialog();
        return;
      }

      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          const postData = { ...this.formData };
          delete postData.createTime;

          this.btnLoading = true;
          if (this.dlgType === "add") delete postData.id;
          const apiCall =
            this.dlgType === "edit"
              ? putAction(`sa/green/equ/equ-position/update`, postData)
              : postAction(`sa/green/equ/equ-position/create`, postData);

          apiCall.then((res) => {
            this.btnLoading = false;
            if (res.data.code === "200") {
              this.showSuccessMessage(
                this.dlgType === "edit" ? "编辑成功！" : "添加成功！"
              );
              this.resetDialog();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    resetDialog() {
      this.$refs["elForm"].resetFields();
      this.dialogVisible = false;
      this.$parent.searchFunc();
    },
    showSuccessMessage(message) {
      this.$message({
        type: "success",
        message,
      });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-dialog {
  /* 表格input不对齐 */
  .formStyle .el-form-item {
    margin: 0;
  }

  /* “设备参数”和表格对齐 */
  .formStyle .el-form-item__label {
    margin-top: 7px;
  }
}
</style>
