// 平台管理
import request from "@/utils/request";

/*
 * 组织机构维护
 */

// 获取列表
export function findProjectLike(data) {
  return request({
    url: `/sys/findProjectLike`,
    method: "post",
    data
  });
}

// 新增列表
export function saveSysProject(data) {
  return request({
    url: `/sys/saveSysProject`,
    method: "post",
    data
  });
}

// 修改列表
export function upDateSysProject(data) {
  return request({
    url: `/sys/upDateSysProject`,
    method: "post",
    data
  });
}

// 删除列表
export function delSysProject(data) {
  return request({
    url: `/sys/delSysProject`,
    method: "post",
    data
  });
}

// 查询通知表头
export function receiveNoticeHeaderByUserId({ page, size }) {
  return request({
    url: `/msg/receiveNoticeHeaderByUserId/${page}/${size}`,
    method: "get"
  });
}

// 接收消息通告
export function findNoticeByDynamic(data) {
  return request({
    url: `/msg/findNoticeByDynamic`,
    method: "post",
    data
  });
}

//查询部门树
export function findTree() {
  return request({
    url: `/sys/findTree`,
    method: "post"
  });
}

// 查询通知表头
export function findSysUserByBId({ bId, page, size }) {
  return request({
    url: `/sys/findSysUserByBId/${bId}/${page}/${size}`,
    method: "get"
  });
}
// 发送通知公告
export function sendNotice(data) {
  return request({
    url: `/msg/sendNotice`,
    method: "post",
    data
  });
}
// 查询通知详情
export function receiveNotice(msgId) {
  return request({
    url: `/msg/receiveNotice/${msgId}`,
    method: "get"
  });
}

// 【【 平台管理 - 消息管理
// 查询消息  page, size, label, type, dates
export function findMsgByDynamic(data) {
  return request({
    url: "/msg/findMsgByDynamic",
    method: "post",
    data
  });
}

// 2 已读，删除消息 1 未读  2 已读，3 删除
export function updeteStatus(msgId, status) {
  return request({
    url: `/msg/updeteStatus`,
    method: "post",
    data: {
      msgId,
      status
    }
  });
}

// 3 已读，删除消息 1 未读  2 已读，3 删除
export function updeteNoticeStatus(noticeId, status) {
  return request({
    url: `/msg/updeteNoticeStatus`,
    method: "post",
    data: {
      noticeId,
      status
    }
  });
}

// 获取消息范围
export function findNoticeUserName(data) {
  return request({
    url: `/msg/findNoticeUserName`,
    method: "post",
    data
  });
}
// 获取消息详情

// 设置已读

export function updeteStatusAll() {
  return request({
    url: `/msg/updeteStatusAll/0`,
    method: "get"
  });
}

// 】】 平台管理 - 消息管理

// 组织机构维护

// 】】 mac 地址

////// [[ 通知公告 已读未读
// 查看公告的已读未读情况   noticeId
export function findReadUserByNoticeId(data) {
  return request({
    url: `/sys/findReadUserByNoticeId`,
    method: "post",
    data
  });
}

// 按部门 查看公告的已读未读情况   noticeId，branchId
export function findReadUserByBranch(data) {
  return request({
    url: `/msg/findReadUserByBranch`,
    method: "post",
    data
  });
}

////// ]] 通知公告 已读未读
