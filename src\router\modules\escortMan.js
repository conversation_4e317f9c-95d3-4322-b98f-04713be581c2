/** 陪护服务 **/

import Layout from "@/views/layout/Layout";

const escortManRouter = {
  path: "/escortMan",
  component: Layout,
  name: "escortMan",
  meta: {
    title: "陪护服务",
    icon: "phfw",
    roles: ["p<PERSON><PERSON><PERSON><PERSON>"]
  },
  children: [
    {
      path: "escortSetting",
      component: () => import("@/views/escortMan/escortSetting"),
      name: "陪护设置",
      meta: {
        title: "陪护设置",
        roles: ["peihus<PERSON><PERSON>"]
      },
      children: []
    },
    {
      path: "careAudit",
      component: () => import("@/views/escortMan/careAudit"),
      name: "护工审核",
      meta: {
        title: "护工审核",
        roles: ["hugongguanli"]
      },
      children: []
    },
    {
      path: "careMan",
      component: () => import("@/views/escortMan/careMan"),
      name: "护工管理",
      meta: {
        title: "护工管理",
        roles: ["hugongguanli"]
      },
      children: []
    },
    {
      path: "evaluationMan",
      component: () => import("@/views/escortMan/evaluationMan"),
      name: "评价管理",
      meta: {
        title: "评价管理",
        roles: ["hgpingjiaguanli"]
      },
      children: []
    },
    {
      path: "orderMan",
      component: () => import("@/views/escortMan/orderMan"),
      name: "订单管理",
      meta: {
        title: "订单管理",
        roles: ["hgdingdanguanli"]
      },
      children: []
    }
  ]
};

export default escortManRouter;
