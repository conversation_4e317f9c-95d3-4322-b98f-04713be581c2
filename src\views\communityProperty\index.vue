<template>
  <!-- 系统管理员 首页 -->
  <div class="adminPermission community">
    <div class="community-container clearfix">
      <div class="community-item">
        <div class="title">
          <i class="icon"></i>
          小区信息
        </div>
        <div class="content" v-if="homePage.report">
          <div><span class="tip">占地总面积</span>{{homePage.report.communityArea ? homePage.report.communityArea.toFixed(2) : 0}}㎡</div>
          <div><span class="tip">小区总人数</span>{{homePage.report.memberCount}}人</div>
        </div>
      </div>
      <div class="community-item">
        <div class="title">
          <i class="icon floor"></i>
          楼栋信息
        </div>
        <div class="content" v-if="homePage.report">
          <div><span class="tip">楼房总数</span>{{homePage.report.floorCount}}栋</div>
          <div><span class="tip">户数</span>{{homePage.report.roomCount}}户</div>
          <div><span class="tip">总面积</span>{{homePage.report.floorArea ? homePage.report.floorArea.toFixed(2) : 0}}㎡</div>
        </div>
      </div>
      <div class="community-item">
        <div class="title">
          <i class="icon room"></i>
          房屋信息
        </div>
        <div class="content" v-if="homePage.report1">
          <el-row v-for="(item, index) in homePage.report1" :key="index">
            <el-col v-if="item.type == 2" :span="12">
              <div><span class="tip">商服总面积</span>
                {{item.area.toFixed(2)}}
                ㎡
              </div>
            </el-col>
            <el-col v-if="item.type == 2" :span="12">
              <div><span class="tip">商服总户数</span>
                {{item.total}}
                户
              </div>
            </el-col>
            <el-col v-if="item.type == 1" :span="12">
              <div><span class="tip">住宅总面积</span>
                {{item.area.toFixed(2)}}
                ㎡
              </div>
            </el-col>
            <el-col v-if="item.type == 1" :span="12">
              <div><span class="tip">住宅总户数</span>
                {{item.total}}
                户
              </div>

            </el-col>
          </el-row>
        </div>
      </div>
      <div class="community-item">
        <div class="title">
          <i class="icon park"></i>
          车位信息
        </div>
        <div class="content" v-if="homePage.report">
          <el-row>
            <el-col :span="12">
              <div><span class="tip">车库总面积</span>{{homePage.report.garageArea ? homePage.report.garageArea.toFixed(2) : 0}}㎡</div>
            </el-col>
            <el-col :span="12">
              <div><span class="tip">车库总数量</span>{{homePage.report.garageCount}}个</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div><span class="tip">车位总面积</span>{{homePage.report.parkingArea ? homePage.report.parkingArea.toFixed(2) : 0}}㎡</div>
            </el-col>
            <el-col :span="12">
              <div><span class="tip">车位总数量</span>{{homePage.report.parkingCount}}个</div>
            </el-col>
          </el-row>

        </div>
      </div>
    </div>
    <div class="todo-container">
      <div class="title">待办事项</div>
      <div class="filter-container">
        <el-form inline :model="listQuery" @submit.native.prevent>
          <el-form-item>
            <el-input @keyup.enter.native='getList' placeholder='请输入关键字' v-model="listQuery.label">
              <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-date-picker v-model="listQuery.rangeDate" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        </el-form>
      </div>
      <div class="table-container">
        <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="报修人">
            <template slot-scope="scope">
              <span>{{ scope.row.repairName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="联系方式">
            <template slot-scope="scope">
              <span>{{ scope.row.tel }}</span>
            </template>
          </el-table-column>

          <el-table-column label="预约时间">
            <template slot-scope="scope">
              <span>{{ scope.row.appointmentTime }}</span>
            </template>
          </el-table-column>

          <el-table-column label="报修类型">
            <template slot-scope="scope">
              <span>{{ scope.row.repairTypeText }}</span>
            </template>
          </el-table-column>

          <el-table-column label="维修费用(元)">
            <template slot-scope="scope">
              <span>{{ scope.row.priceScope }}</span>
            </template>
          </el-table-column>

          <el-table-column label="维修内容">
            <template slot-scope="scope">
              <span>{{ scope.row.context }}</span>
            </template>
          </el-table-column>

          <el-table-column label="位置">
            <template slot-scope="scope">
              <div>小区:{{ scope.row.communityName }}</div>
              <div>房屋:{{ scope.row.fangwu }}</div>
            </template>
          </el-table-column>

          <el-table-column label="状态">
            <template slot-scope="scope">
              <span>{{ scope.row.stateName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">处理</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>

import {
  repairpoolPage,
} from '@/api/repairMan'
import {
  formScreen,
} from '@/api/reportMan'
import * as utils from '@/utils'

export default {
  components: {
  },
  data () {
    return {
      listQuery: {
        page: 1,
        limit: 99,
        label: '',
        rangeDate: '',
        status: '1',
        repairType: '',
        logType: '1',
        type: ''
      },
      list: [],
      listLoading: false,
      stateList: [
        {
          id: '0',
          name: '未派单'
        },
        {
          id: '1',
          name: '已接单'
        },
        {
          id: '2',
          name: '待评价'
        },
        {
          id: '3',
          name: '待回访'
        },
        {
          id: '4',
          name: '待评价待回访'
        },
        {
          id: '5',
          name: '待评价已回访'
        },
        {
          id: '6',
          name: '已评价待回访'
        },
        {
          id: '7',
          name: '已结束'
        },
      ],
      homePage: {},
      userRoles: []
    }
  },
  created () {
    this.userRoles = JSON.parse(decodeURI(window.localStorage.userRoles))
    if (this.userRoles.includes('xiaoquguanli')) {
      this.getList()
      this.getHomePage()
    }
  },
  mounted () {
  },
  methods: {

    // 获取首页数据
    getHomePage () {
      formScreen().then(res => {
        if (res.data.code == 200) {
          this.homePage = res.data.data
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    formatList () {
      for (let i of this.list) {
        i.stateName = utils.getNameById(i.state, this.stateList)
      }
    },

    // 获取数据
    getList () {
      this.listLoading = true
      this.listQuery.startDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[0]
      this.listQuery.endDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[1]
      repairpoolPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = res.data.data ? JSON.parse(JSON.stringify(res.data.data)) : []
          this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    editItem () {
      this.$router.push({ path: `/communityProperty/repairMan/repairTodo` })
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.adminPermission.community {
  width: 100%;
  height: 100%;
  margin: 0;
  background: #f5f5f5;
  /deep/ .community-container {
    margin-bottom: 24px;
    .community-item {
      float: left;
      width: calc(25% - 24px);
      height: 230px;
      margin-right: 24px;
      padding: 0 24px;
      background: #ffffff;
      border-radius: 2px;
      .title {
        height: 92px;
        line-height: 92px;
        font-size: 30px;
        color: #000;
        border-bottom: 1px solid #e8e8e8;
        i.icon {
          background: url('/static/image/dashboard/community.png') no-repeat center;
          display: inline-block;
          width: 48px;
          height: 48px;
          border-radius: 10px;
          vertical-align: middle;
          margin-right: 12px;
        }
        i.icon.floor {
          background-image: url('/static/image/dashboard/floor.png');
        }
        i.icon.room {
          background-image: url('/static/image/dashboard/room.png');
        }
        i.icon.park {
          background-image: url('/static/image/dashboard/park.png');
        }
      }
    }
    .content {
      padding-top: 24px;
      div {
        margin-bottom: 10px;
      }
    }
  }
}

.todo-container {
  height: calc(100% - 254px);
  background: #ffffff;
  border-radius: 2px;
  .title {
    height: 55px;
    line-height: 55px;
    border-bottom: 1px solid #e8e8e8;
    font-size: 16px;
    color: #000;
    padding-left: 24px;
  }
  .filter-container {
    padding: 8px 24px 0;
  }
  .table-container {
    height: calc(100% - 116px);
    padding: 0 24px;
    .el-table {
      height: calc(100% - 10px);
    }
  }
}

.tip {
  margin-right: 10px;
  color: rgba($color: #000000, $alpha: 0.6);
}
</style>