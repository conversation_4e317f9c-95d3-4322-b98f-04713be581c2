<template>
  <div class="base-setup" @click="showIconSelect = false">
    <el-form
      ref="baseSetting"
      :model="setup"
      label-position="top"
      label-width="80px"
    >
    
      <el-form-item
        v-if="isShow"
        label="选择项目"
        prop="projectId"
        :rules="getRule('请选择项目')"
      >
        <el-select
          v-model="setup.projectId"
          placeholder="选择项目"
          filterable
          clearable
        >
          <el-option
            v-for="item of projectList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="isShow"
        label="选择公有模板"
        prop="model"
        :rules="getRule('请选择公有模板')"
      >
        <el-select
          v-model="setup.model"
          placeholder="选择公有模板"
          filterable
          clearable
          @change="equModelsChange"
        >
          <el-option
            v-for="item of equModelsList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="模板名称" prop="name" :rules="getRule('请输入')">
        <el-input v-model="setup.name" placeholder="请输入模板名称" />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          :autosize="{ minRows: 5, maxRows: 6 }"
          v-model="setup.remark"
          type="textarea"
          placeholder="请输入"
          style="width: 100%"
      /></el-form-item>
    </el-form>
  </div>
</template>
<script>
import iconfont from "@/assets/workFlow/iconfont/iconfont.json";
import { getAction } from "@/api";
import * as utils from "@/utils";

export default {
  name: "FormBaseSetting",
  components: {},
  data() {
    return {
      projectList: [],
      nowUserSelect: null,
      showIconSelect: false,
      select: [],
      newGroup: "",
      fromGroup: [],
      colors: [
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "hsl(181, 100%, 37%)",
        "hsla(209, 100%, 56%, 0.73)",
        "#c7158577",
      ],
      icons: [
        "el-icon-delete-solid",
        "el-icon-s-tools",
        "el-icon-s-goods",
        "el-icon-warning",
        "el-icon-circle-plus",
        "el-icon-camera-solid",
        "el-icon-s-promotion",
        "el-icon-s-cooperation",
        "el-icon-s-platform",
        "el-icon-s-custom",
        "el-icon-s-data",
        "el-icon-s-check",
        "el-icon-s-claim",
      ],
      rules: {
        projectId: [],
        name: [],
        type: [],
      },
      formType: "",
      equModelsList: [],
    };
  },
  computed: {
    setup() {
      console.log("===this.$store.state.design", this.$store.state.design);
      return this.$store.state.design;
    },
    //公有模板添加编辑不显示项目和公有模板
    isShow(){
      if(this.formType == "addType"){
        return false
      }else if(this.formType == "edit" && this.setup.type == 1){
        return false
      }else if(this.formType == "copy" && this.setup.type == 1){
        return false}
      else{
        return true
      }
    }
  },
  created() {
    this.projectList = JSON.parse(window.localStorage.ERPUserInfo).projects;
    this.loadIconfont();
    this.formType = this.$route.query.type || "add";
    console.log(this.formType, "this.formType");
    if (this.formType !== "addType") {
      this.getEquModelsPage();
    }
  },
  mounted() {},
  methods: {
    equModelsChange(val) {
      if(!val){
        return
      }
      getAction("/green/equ/models/get?id=" + val)
        .then((rsp) => {
          this.loading = false;
          let form = this.setup;
          form.logo = {
            icon: "el-icon-eleme",
            background: "#1e90ff",
          };
          let formItems = JSON.parse(rsp.data.data.formItems);
          for (let item of formItems) {
            if (!item.props.showLogic) {
              item.props.showLogic = [];
            }
          }
          form.formItems = formItems;
          this.$store.commit("loadForm", form);
        })
        .catch((err) => {
          this.loading = false;
          this.$err(err, "获取流程信息失败");
        });
    },
    getEquModelsPage() {
      let sendObj = {
        pageNo: 1,
        pageSize: 1000,
        type: "1",
      };
      getAction("/green/equ/models/page", sendObj).then((res0) => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.equModelsList = [];
          } else {
            this.equModelsList = res.data.list;
          }
        } else {
          this.$message({
            type: "warning",
            message: res.msg,
          });
        }
      });
    },
    getRule(msg) {
      if (msg == "请选择公有模板") {
        return [{ required: false, message: msg, trigger: "change" }];
      } else {
        return [{ required: true, message: msg, trigger: "change" }];
      }
    },
    loadIconfont() {
      if (iconfont && iconfont.id) {
        iconfont.glyphs.forEach((icon) => {
          this.icons.push(
            `${iconfont.font_family} ${iconfont.css_prefix_text}${icon.font_class}`
          );
        });
      }
    },
    selected(select) {
      this.$set(this.setup.settings, this.nowUserSelect, select);
    },

    validate() {
      this.$refs.baseSetting.validate();
      let err = [];
      if (!this.$isNotEmpty(this.setup.projectId)) {
        if(this.isShow){
          err.push("项目未设置");
        }
      }
      if (!this.$isNotEmpty(this.setup.name)) {
        err.push("模板名称未设置");
      }

      return err;
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-select-dropdown {
  display: none;
}

.icon-select {
  display: flex;
  flex-wrap: wrap;
  i {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 20px;
    width: 20px;
    height: 20px;
    padding: 10px;
    max-width: 38px !important;
    &:hover {
      box-shadow: 0 0 10px 2px #c2c2c2;
    }
  }
}

/deep/ .select-u {
  width: 100%;
}

.base-setup {
  overflow: auto;
  margin: 0 auto;
  width: 640px;
  height: calc(100vh - 105px);
  background: #ffffff;
  // margin-top: 10px;
  padding: 15px 20px;

  i:first-child {
    position: relative;
    cursor: pointer;
    font-size: xx-large;
    color: #ffffff;
    border-radius: 10px;
    padding: 10px;
  }

  .change-icon {
    margin-left: 20px;

    span {
      font-size: small;
      color: #7a7a7a;
      margin-right: 15px;
    }

    i {
      cursor: pointer;
      color: #7a7a7a;
      font-size: x-large;
    }
  }

  /deep/ .el-form-item__label {
    padding: 0;
    font-weight: bold;
  }

  /deep/ .el-form-item {
    margin-bottom: 5px;
  }
}

/deep/ .group {
  .el-select {
    width: calc(100% - 130px);
  }

  .el-button {
    margin-left: 10px;
    width: 120px;
  }
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #f8f8f8;
}

::-webkit-scrollbar-thumb {
  border-radius: 16px;
  background-color: #e8e8e8;
}
</style>
