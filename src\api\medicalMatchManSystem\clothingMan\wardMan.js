import request from '@/utils/request'

/*
 * 病房管理
 */

// 新增/修改病房信息接口
export function saveOrUWard(data) {
  return request({
    url: `/sys/saveOrUWard`,
    method: 'post',
    data
  })
}

// 修改病房信息状态（删除病房信息）
export function updateWard(data) {
  return request({
    url: `/sys/updateWard`,
    method: 'post',
    data
  })
}

// 动态查询病房信息 分页
export function findWardDynamic(data) {
  return request({
    url: `/sys/findWardDynamic`,
    method: 'post',
    data
  })
}
