<template>
    <div class="app-container">
        <div class="filter-container">
            <el-form inline :model="listQuery" @submit.native.prevent>
                <el-form-item label="关键字：">
                    <el-input @keyup.enter.native='getList' clearable class='m-shaixuan-input' placeholder='请输入业主姓名/流水号'
                        v-model="listQuery.label"></el-input>
                </el-form-item>
                <el-form-item label="归属小区：" prop="communityId">
                    <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
                        <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="类型：">
                    <el-select v-model="listQuery.payType" filterable clearable placeholder="请选择类型" @change="payTypeChange">
                        <el-option v-for="item in payTypeList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label='' prop="">
                    <el-input style="width: 200px" v-model="backName" placeholder="选择房屋/车位/车库" readonly @focus="showDlg">
                    </el-input>
                </el-form-item>
                <el-form-item label="生成日期">
                    <el-date-picker style="width: 220px" v-model="listQuery.dateRange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="截止日期">
                    </el-date-picker>
                </el-form-item>
                <el-button icon="el-icon-search" type="success" size="mini" @click="getList">查询</el-button>
            </el-form>
        </div>
        <div class="table-container">
            <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit
                highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
                <el-table-column label="序号" type="index" align="center" width="60">
                </el-table-column>

                <el-table-column label="业主姓名">
                    <template slot-scope="scope">
                        <span>{{ scope.row.ownerName }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="类型">
                    <template slot-scope="scope">
                        <span v-if="scope.row.type=='room'">房屋</span>
                        <span v-if="scope.row.type=='parking'">车位</span>
                        <span v-if="scope.row.type=='garage'">车库</span>
                    </template>
                </el-table-column>

                <el-table-column label="房屋名称">
                    <template slot-scope="scope">
                        <span>{{ scope.row.roomName }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="小区名称">
                    <template slot-scope="scope">
                        <span>{{ scope.row.communityName }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="批次号">
                    <template slot-scope="scope">
                        <span>{{ scope.row.batchNum }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="生成时间">
                    <template slot-scope="scope">
                        <span>{{ scope.row.createTime }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
                    <template slot-scope="scope">
                        <el-button type="primary" size="mini" icon="el-icon-more" plain
                            @click="infoFun(scope.row)">详情</el-button>
                        <el-button type="primary" size="mini" icon="el-icon-printer" plain
                            @click="printingFun(scope.row)">打印收据</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="page-container">
            <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
        </div>

        <el-dialog title="详情" :close-on-click-modal="false" :visible.sync="infoDlgState" width="1000px" append-to-body>
            <div class="table-container">
                <div style="margin-bottom: 10px;font-weight: bolder;">房屋: {{ dlgName }}</div>
                <el-table class="m-small-table" style="max-height: 600px;overflow-y: auto;" v-loading="infoListLoading"
                    :data="infoList" border fit highlight-current-row>

                    <el-table-column label="费用类型" width="90">
                        <template slot-scope="scope">
                            <span>{{ scope.row.feeName }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="费用项目">
                        <template slot-scope="scope">
                            <span>{{ scope.row.configName }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="费用标识" width="90">
                        <template slot-scope="scope">
                            <span>{{ scope.row.feeFlagName }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="实收金额" width="90">
                        <template slot-scope="scope">
                            <span>{{ num2Round(scope.row.receivedAmount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="建账时间" width="145">
                        <template slot-scope="scope">
                            <span>{{ scope.row.createTime }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="计费开始时间">
                        <template slot-scope="scope">
                            <span>{{ scope.row.startDate }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="计费结束时间">
                        <template slot-scope="scope">
                            <span>{{ scope.row.endDate }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="说明">
                        <template slot-scope="scope">
                            <span>{{ scope.row.remark }}</span>
                        </template>
                    </el-table-column>


                </el-table>
            </div>

            <!-- <div class="page-container">
                <pagination :total="infoTotal" :page.sync="infoListQuery.page" :limit.sync="infoListQuery.limit"
                    @pagination="getInfoList" />
            </div> -->

            <div slot="footer" class="dialog-footer">
                <el-button @click="infoDlgState = false" icon="el-icon-close">关闭</el-button>
            </div>
        </el-dialog>

        <typeDlg :dlgState0="dlgState" :dlgType="dlgType" @closeDlg="closeDlg" @backFunc="backFunc" />
    </div>
</template>
<script>
import { communityPage, cofloorCommunity, buildingunitFloor, buildingroomPage, roomTypePage } from '@/api/communityMan'
import { payBillReportPrint, payBillReportPrintGetInfo } from '@/api/costMan'
import typeDlg from '../pendingReceipt/typeDlg.vue'
import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
export default {
    components: {
        typeDlg,
        Pagination
    },
    data() {
        return {
            userInfo: JSON.parse(window.localStorage.userInfo),
            dlgState: false,
            dlgType: '', // 弹框状态1-房屋 2-车位 3-车库
            backName: '',//选择的名字
            // islabel: "选择房屋",
            payTypeList: [
                {
                    id: "1",
                    name: "房屋"
                },
                {
                    id: "2",
                    name: "车位"
                },
                {
                    id: "3",
                    name: "车库"
                }
            ],
            listQuery: {
                roomId: '',
                page: 1,
                limit: 20,
                label: "",
                payType: "",
                type:'',
                dateRange: [],
                startDate: '', //		body	false	string
                endDate: '', //		body	false	string
            },
            list: [],
            total: 0,
            count: 0,
            listLoading: false,
            infoListLoading: false,
            infoListQuery: {
                page: 1,
                limit: 20,
                label: "",
            },
            infoDlgState: false,
            // infoTotal: 0,
            infoList: [],
            feeFlagList: [
                {
                    id: '1',
                    name: '周期性费用',
                },
                {
                    id: '2',
                    name: '一次性费用',
                },
            ],
            costTypeList: [],
            dlgName: '',
            communityList: [],
        };
    },
    created() {
        this.getCommunityList()
        utils.getDataDict(this, 'costType', 'costTypeList')
        this.getList();
        // this.userInfo = JSON.parse(window.localStorage.userInfo)
    },
    methods: {
        printingFun(row){
            let newWin = this.$router.resolve({ path: '/receiptPrintPaper', query: { batchNum: row.batchNum }})
            window.open(newWin.href, '_blank')

        },
        // 获取小区列表
        getCommunityList() {
            let postParam = {
                page: 1,
                limit: 200,
            }
            communityPage(postParam).then((res) => {
                if (res.data.code == 200) {
                    this.communityList = res.data.data
                }
            })
        },
        formatList() {
            for (let i of this.infoList) {
                i.feeName = utils.getNameById(i.feeType, this.costTypeList)
                i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList)
            }
        },
        num2Round(num, digit = 2) {
            return utils.num2Round(num, digit)
        },
        payTypeChange() {
            this.backName = ''
            this.listQuery.parkingId=''
            this.listQuery.roomId=''
            this.listQuery.garageId=''
            // if (this.listQuery.payType == 1) {
            //     this.islabel = '选择房屋'
            // } else if (this.listQuery.payType == 2) {
            //     this.islabel = '选择车位'
            // } else {
            //     this.islabel = '选择车库'
            // }
        },
        showDlg() {
            if (this.listQuery.payType == '') {
                this.$message({
                    type: 'warning',
                    message: '请先选择类型'
                })
                return false
            }
            this.dlgType = this.listQuery.payType
            this.dlgState = true
        },
        // 关闭弹窗
        closeDlg() {
            this.dlgState = false
        },
        backFunc(data) {
            if (this.listQuery.payType == 1) {
                this.backName = data.roomFullName
                this.listQuery.roomId = data.id
            } else if (this.listQuery.payType == 2) {
                this.backName = data.numStr
                this.listQuery.roomId = data.id
            } else {
                this.backName = data.numStr
                this.listQuery.roomId = data.id
            }
            this.getList()
        },
        getList() {
            // if (utils.isNull(this.listQuery.payType)) {
            //     this.$message.error("请先选择类型")
            //     return
            // }
            // if (utils.isNull(this.backName)) {
            //     this.$message.error("请先选择房屋/车位/车库")
            //     return
            // }
            this.count++
            this.listLoading = true
            let sendObj = JSON.parse(JSON.stringify(this.listQuery));
            if (sendObj.dateRange && sendObj.dateRange.length > 0) {
                sendObj.startDate = sendObj.dateRange[0];
                sendObj.endDate = sendObj.dateRange[1];
            } else {
                sendObj.startDate = "";
                sendObj.endDate = "";
            }
            if (sendObj.payType == 1) {
                sendObj.type = 'room'
            }else if (sendObj.payType == 2) {
                sendObj.type = 'parking'
            }else if(sendObj.payType == 3){
                sendObj.type = 'garage'
            }else{
                sendObj.type = ''
            }
            sendObj.projectId = this.userInfo.projectId
            delete sendObj.dateRange;
            delete sendObj.payType;
            payBillReportPrint(sendObj).then(res => {
                this.listLoading = false
                if (res.data.code == 200) {
                    this.list = res.data.data
                    this.total = res.data.page.total
                } else {
                    this.$message.error(res.data.msg)
                }

                console.log(res, 'res');
            })
        },
        infoFun(row) {
            this.infoDlgState = true
            this.dlgName = row.roomName
            this.getInfoList(row.batchNum)
        },
        getInfoList(batchNum) {
            this.infoList=[]
            this.infoListLoading = true
            payBillReportPrintGetInfo(batchNum).then((res) => {
                this.infoListLoading = false
                if (res.data.code == 200) {
                    this.infoDlgState = true
                    this.infoList = res.data.data.payFeeBillSums
                    // this.infoTotal=res.data.page.total
                    this.formatList()
                } else {
                    this.$message.error(res.data.msg)
                }
                console.log(res, 'res');
            })
        }
    }
};
</script>
