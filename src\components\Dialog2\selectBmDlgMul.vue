<template>
  <!-- byUser branch role -->
  <el-dialog
    class="mazhenguo"
    :title="`选择${title}`"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="600px"
    top="30px"
  >
    <el-input placeholder="输入关键字进行过滤" v-model="filterBranch">
    </el-input>
    <el-tree
      class="mt10"
      ref="branchTree"
      highlight-current
      node-key="id"
      :data="treeData"
      @node-click="treeNodeClick"
      default-expand-all
      :filter-node-method="filterNode"
      show-checkbox
      :check-strictly="true"
      @check-change="checkChange"
      :expand-on-click-node="true"
      :props="treeProps"
    >
    </el-tree>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button
        v-if="dlgType != 'info'"
        :loading="dlgSubLoading"
        type="success"
        @click="dlgSubFunc"
        icon="el-icon-check"
      >
        <span v-if="dlgSubLoading">确定中...</span>
        <span v-else>确定</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { uploadImg, uploadImg2 } from "@/utils/uploadImg";
import * as utils from "@/utils";
import * as regUtils from "@/utils/regUtils";
import { postAction, getAction } from "@/api";

export default {
  components: {},
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {
      type: Object,
      default: {}
    },
    dlgState0: {
      type: Boolean,
      default: false
    },
    dlgSelectData: {},

    treeType: {
      type: String,
      default: "branch"
    },

    isRole: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: "部门"
    }
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val;
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          this.idMapList = [];
          this.$refs.branchTree.setCheckedKeys([]);
          this.filterBranch = "";
          this.getTree();
        }, 50);
      } else {
        this.$emit("closeDlg");
      }
    },

    filterBranch(val) {
      this.$refs.branchTree.filter(val);
    }
  },
  data() {
    return {
      filterBranch: "",
      treeData: [],
      selectNode: {},

      // 弹窗
      dlgState: false,

      dlgSubLoading: false, // 提交loading

      rootId: "",

      idMapList: [],
      setTreeState: false, // 是否为设置树节点状态
      treeProps: {
        disabled: (data, node) => {
          return data.id == this.rootId;
        }
      }
    };
  },
  created() {},
  methods: {
    // 节点点击
    checkChange(data, isSelected, hasChildSelected) {
      if (data.id == this.rootId) {
        this.$message.warning("不能选择该节点");
        return false;
      }

      // 如不关联父子节点，以下注释掉
      if (this.setTreeState) return false;

      if (isSelected) {
        let selectIdListDown = [];
        for (let item of this.idMapList) {
          let reg = data.idMap + "-";
          if (item.indexOf(reg) >= 0) {
            let nextStr = item.split(reg)[1];
            let nextIdArr = nextStr.split("-");
            for (let nextIdArrId of nextIdArr) {
              if (selectIdListDown.indexOf(nextIdArrId) < 0) {
                selectIdListDown.push(nextIdArrId);
              }
            }
          }
        }
        let selectIdList = [...selectIdListDown]; // idMapArr 上级， selectIdListDown 下级

        this.setTreeState = true;
        setTimeout(() => {
          this.setTreeState = false;
        }, 80);
        for (let itemId of selectIdList) {
          this.$refs.branchTree.setChecked(itemId, true);
        }
      } else {
        let selectIdListDown = [];
        for (let item of this.idMapList) {
          let reg = data.idMap + "-";
          if (item.indexOf(reg) >= 0) {
            let nextStr = item.split(reg)[1];
            let nextIdArr = nextStr.split("-");
            for (let nextIdArrId of nextIdArr) {
              if (selectIdListDown.indexOf(nextIdArrId) < 0) {
                selectIdListDown.push(nextIdArrId);
              }
            }
          }
        }

        this.setTreeState = true;
        setTimeout(() => {
          this.setTreeState = false;
        }, 80);
        for (let itemId of selectIdListDown) {
          this.$refs.branchTree.setChecked(itemId, false);
        }
      }
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    getTree() {
      if (this.treeType == "byUser") {
        getAction("/sys/department/getDeptTreeByUserId").then(res0 => {
          let res = res0.data;

          if (res.code === "200") {
            console.log("----res.data", res.data);
            this.treeData = [res.data];
            // if (res.msg == '该员工暂无权限查看！') {
            //   this.$message({
            //     type: 'warning',
            //     message: '该员工暂无权限查看！',
            //   })
            // }
          } else {
            this.$message.error(res.msg);
          }
        });
      } else if (this.isRole) {
        // 根据权限
        postAction("/sys/department/findTree").then(res0 => {
          let res = res0.data;
          if (res.code === "200") {
            this.treeData = JSON.parse(JSON.stringify(res.list));

            this.rootId = this.treeData[0].id;
            if (res.msg == "该员工暂无权限查看！") {
              this.$message({
                type: "warning",
                message: "该员工暂无权限查看！"
              });
            }

            let openIdList = [];
            for (let item of this.treeData) {
              openIdList.push(item.id);
            }
            this.defaultOpenList = openIdList;

            this.setTreeIdMap();
          } else {
            this.$message.error(res.msg);
          }
        });
      } else if (!this.isRole) {
        // 不根据权限
        getAction("/sys/department/findTreeByFrom").then(res0 => {
          let res = res0.data;

          if (res.code === "200") {
            this.treeData = JSON.parse(JSON.stringify(res.list));
            this.rootId = this.treeData[0].id;
            if (res.msg == "该员工暂无权限查看！") {
              this.$message({
                type: "warning",
                message: "该员工暂无权限查看！"
              });
            }
            let openIdList = [];
            for (let item of this.treeData) {
              openIdList.push(item.id);
            }
            this.defaultOpenList = openIdList;

            this.setTreeIdMap();
          } else {
            this.$message.error(res.msg);
          }
        });
      }
    },

    setTreeIdMap() {
      let preStr = "idMap";
      this.setTreeIdMapItem(preStr, this.treeData);
      setTimeout(() => {
        this.treeData = JSON.parse(JSON.stringify(this.treeData));

        this.$nextTick(() => {
          this.setTreeState = true;
          setTimeout(() => {
            this.setTreeState = false;
          }, 80);

          let idArr = this.dlgSelectData || [];
          console.log("-----idArr", idArr);
          this.$refs.branchTree.setCheckedKeys(idArr);
        });
      }, 100);
    },
    setTreeIdMapItem(preStr, treeArr) {
      for (let i = 0; i < treeArr.length; i++) {
        let item = treeArr[i];
        let preStr1 = preStr + "-" + item.id;
        item.idMap = preStr1;
        this.idMapList.push(preStr1);
        if (item.children && item.children.length > 0) {
          this.setTreeIdMapItem(preStr1, item.children);
        }
      }
    },

    treeNodeClick(data) {
      console.log("====data", data);

      $(".tree-on").removeClass("tree-on");

      if (data.id == this.rootId) {
        this.$message.warning("不能选择该节点");
        this.selectNode = "";
        return false;
      }

      setTimeout(() => {
        $(".is-current>.el-tree-node__content").addClass("tree-on");
      }, 50);
      this.selectNode = data;
    },

    dlgSubFunc() {
      // this.$emit('backFunc', this.selectNode)
      // this.closeDlg()

      ///////
      let checkedNodes = this.$refs.branchTree.getCheckedNodes();
      let halfCheckedNodes = this.$refs.branchTree.getHalfCheckedNodes();

      let selectList0 = [...checkedNodes, ...halfCheckedNodes];
      let idArr = [];
      let nameArr = [];
      for (let item of selectList0) {
        idArr.push(item.id);
        nameArr.push(item.label);
      }

      let returnObj = {
        ids: idArr.join(","),
        names: nameArr.join(",")
      };

      this.$emit("backFunc", returnObj);
      this.closeDlg();
    },

    closeDlg() {
      this.$emit("closeDlg");
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
