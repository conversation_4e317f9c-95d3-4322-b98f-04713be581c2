<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' title="选择员工" top='30px' :visible.sync="usersTableState" width='900px' append-to-body>
      <div>
        <!-- 搜索 -->
        <div class="filter-container">
          <el-form ref="searchForm" class='n-search' :model="listQuery" label-width="90px" @submit.native.prevent>
            <div class="n-search-bar">
              <div class='n-search-item fl'>
                <el-form-item label="关键字：">
                  <el-input @keyup.enter.native='searchFunc' class='m-shaixuan-input' placeholder='员工姓名/工号' v-model="listQuery.str">
                    <i @click='resetSearchItem(["str"])' slot="suffix" class="el-input__icon el-icon-error"></i>
                  </el-input>
                </el-form-item>
              </div>
              <div class='n-search-item n-search-item-r fr'>
                <el-input v-model="listQuery.branchName" @focus="showBmTree" placeholder="选择部门" style="width: 180px;" readonly>
                  <i @click='resetSearchItem(["branchId", "branchName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
                </el-input>

                <el-button icon='el-icon-search' type="success" size='mini' class="search-right-btn" @click='searchFunc'>搜索</el-button>
                <el-button type="danger" size='mini' class="search-right-btn" @click='emptyFunc'>重置</el-button>
              </div>
              <div class="clear"></div>
            </div>

          </el-form>
        </div>

        <!-- 员工 -->
        <el-table ref="multipleUser" v-loading="listLoading" :data="list" border fit highlight-current-row max-height='367px' class='m-small-table' @selection-change="userSelectionChange" show-overflow-tooltip='true'>
          <el-table-column label="" align="center" type="selection" width="50">
          </el-table-column>
          <el-table-column label="序号" prop="index" align="center" width="60">
            <template slot-scope="scope">
              <span>{{ scope.row.index }}</span>
            </template>
          </el-table-column>

          <el-table-column label="员工姓名" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.label }}</span>
            </template>
          </el-table-column>

          <el-table-column label="账号（手机号/邮箱）" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.account }}</span>
            </template>
          </el-table-column>

          <el-table-column label="岗位名称" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.postName }}</span>
            </template>
          </el-table-column>

        </el-table>

        <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
        <div class="clear"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
      <Bmtree />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

// 数据接口
import { findOrgBranchAll, findSysUserAll } from '@/api/dataDic'
import { findUserLike } from '@/api/staffMan'  // 查部门，根据部门查员工
import { findPostLike } from '@/api/postMan'  // 查岗位

// 页面组件
import Pagination from '@/components/Pagination'
import Bmtree from '@/components/Dialog/Bmtree'  // 部门弹窗

let listQueryEmpty = {
  page: 1,
  size: 10,

  str: '',
  branchId: '',
  branchName: '',
  sqType: '',
}

export default {
  components: { Pagination, Bmtree },
  data() {

    return {
      list: [],
      listLoading: false,
      total: 0,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      multipleSelection: [],
      userIds: ''
    }
  },
  computed: {
    ...mapGetters([
      // 部门树
      'bmTreeBranchId',
      'bmTreeBranchName',
    ]),
    usersTableState: {
      get: function () {
        let state = this.$store.getters.usersTableState
        if (state === true) {
          this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
          this.getList()
        }
        return state
      },
      set: function (newVal) {
        this.$store.commit('SET_USERSTABLESTATE', newVal)
      }
    },
    usersTableUserIds: {
      get: function () {
        let userIds = this.$store.getters.usersTableUserIds
        if (userIds != "") {
          this.userIds = userIds
        }
        return userIds
      },
      set: function (newVal) {
        this.$store.commit('SET_USERSTABLEUSERIDS', newVal)
      }
    },
  },
  watch: {
    // 部门树状态
    bmTreeBranchId(val) {
      if (val === 'empty') {
        return false
      }
      this.listQuery.postId = ''
      this.listQuery.postName = ''
      this.listQuery.branchId = val
    },
    bmTreeBranchName(val) {
      this.listQuery.branchName = val
    },
    usersTableUserIds(val) {
      console.log(val)
    }
  },
  created() {

  },
  methods: {
    userSelectionChange(val) {
      this.multipleSelection = val;
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },
    // 搜索事件
    searchFunc() {
      this.listQuery.page = 1
      this.listQuery.size = 20
      this.getList()
    },
    // 清空搜索条件
    emptyFunc() {
      this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
      this.getList()
    },

    // 获取数据
    getList() {
      this.list = []
      this.selectedId = ''
      this.listLoading = true

      findUserLike(this.listQuery).then(res => {

        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg

        if (code === '200') {
          let data = res.data.data
          let list = res.data.list

          this.total = data.total
          for (let i = 0; i < list.length; i++) {
            let item = list[i]
            item.index = (this.listQuery.page - 1) * this.listQuery.size + i + 1
          }
          this.list = JSON.parse(JSON.stringify(list))
          let userIdArr = this.userIds.split(",")
          if (userIdArr.length > 0) {
            this.$nextTick(() => {
              this.$refs.multipleUser.clearSelection();
              this.list.forEach(item => {
                for (let i in userIdArr) {
                  if (userIdArr[i] == item.id) {
                    this.$refs.multipleUser.toggleRowSelection(item, true);
                  }
                }
              });
            })
          }
          else {
            this.$nextTick(() => {
              this.$refs.multipleUser.clearSelection();
            })
          }
        } else {
          this.$message.error(msg)
        }

      })
    },

    // 其他弹窗
    showBmTree() {
      this.$store.commit('SET_BMTREEISROLE', true)
      this.$store.commit('SET_BMTREESTATE', true)
    },

    // 选择部门提交
    bumenOkFunc() {
      let userIdArr = []
      let userNameArr = []
      for (let item of this.multipleSelection) {
        userIdArr.push(item['id'])
        userNameArr.push(item['label'])
      }
      let userIds = userIdArr.join(",")
      let userNames = userNameArr.join(",")
      if (userIdArr.length == 0) {
        this.$message({
          type: 'warning',
          message: '请选择员工'
        })
      } else {
        this.$store.commit('SET_USERSTABLEUSERIDS', userIds)
        this.$store.commit('SET_USERSTABLEUSERNAMES', userNames)
        this.closeDialog()
      }
    },
    // 关闭弹窗 
    closeDialog() {
      this.$store.commit('SET_USERSTABLESTATE', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.user-list {
  height: 400px;
  border: 1px solid red;
  border-radius: 3px;
}
</style>