<template>
  <el-dialog :close-on-click-modal='false' :title="'选择科目'" :visible.sync="dlgShow" append-to-body>
    <el-input placeholder="输入科目进行过滤" v-model="filterSubject">
    </el-input>
    <el-tree ref="subjectTree" highlight-current node-key="id" :data="list" @node-click="treeNodeClick" :default-expanded-keys="defaultOpenList" :filter-node-method="filterNode" :expand-on-click-node="false" @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse">
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span :title='data.name'>{{ data.name }}</span>
        <span v-if="data.type==0" class="fdanger">(分类)</span>
        <span v-if="data.type==1" class="fsuccess">(科目)</span>
        <span v-if="data.status==0" class="fsuccess">(启用)</span>
        <span v-if="data.status==1" class="fdanger">(停用)</span>
      </span>
    </el-tree>

    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-span" v-if="selectSubjectName">当前选中：{{selectSubjectName}}</span>
      <el-button icon="el-icon-back" @click="closeDlg">
        取 消
      </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'

import { findSubjectTree } from '@/api/reportMan'

export default {
  components: {
  },
  data () {
    return {
      filterSubject: "",

      list: [],

      selectSubjectId: '',

      selectSubjectName: '',

      selectSubjectType: '',

      defaultOpenList: []

    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.customerCenter.subjectDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('customerCenter/subjectDlg/SET_DLGSHOW', val)
      }
    },

    subjectId: {
      get: function () {
        return this.$store.state.customerCenter.subjectDlg.subjectId
      },
      set: function (val) {
        this.$store.commit('customerCenter/subjectDlg/SET_SUBJECTID', val)
      }
    },

    subjectName: {
      get: function () {
        return this.$store.state.customerCenter.subjectDlg.subjectName
      },
      set: function (val) {
        this.$store.commit('customerCenter/subjectDlg/SET_SUBJECTNAME', val)
      }
    },

    subjectType: {
      get: function () {
        return this.$store.state.customerCenter.subjectDlg.subjectType
      },
      set: function (val) {
        this.$store.commit('customerCenter/subjectDlg/SET_SUBJECTTYPE', val)
      }
    },

    dlgType: {
      get: function () {
        return this.$store.state.customerCenter.subjectDlg.dlgType
      },
      set: function (val) {
        this.$store.commit('customerCenter/subjectDlg/SET_DLGTYPE', val)
      }
    },

    projectId: {
      get: function () {
        return this.$store.state.customerCenter.subjectDlg.projectId
      },
      set: function (val) {
        this.$store.commit('customerCenter/subjectDlg/SET_PROJECTID', val)
      }
    }
  },

  watch: {
    filterSubject (val) {
      this.$refs.subjectTree.filter(val);
    },

    dlgShow (val) {
      if (val) {
        if (utils.isNull(this.subjectId)) {
          this.selectSubjectId = ""
          this.selectSubjectName = ""
          this.selectSubjectType = ""
        }
        this.getList()
      }
    },

    subjectId (val) {
      this.selectSubjectId = val
    },

    subjectName (val) {
      this.selectSubjectName = val
    },

    subjectType (val) {
      this.selectSubjectType = val
    },

    dlgType (val) {
    }
  },

  methods: {
    // 筛选节点搜索
    filterNode (value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 树节点展开
    handleNodeExpand (data) {
      // 保存当前展开的节点
      let flag = false
      this.defaultOpenList.some(item => {
        if (item === data.id) { // 判断当前节点是否存在， 存在不做处理
          flag = true
          return true
        }
      })
      if (!flag) { // 不存在则存到数组里
        this.defaultOpenList.push(data.id)
      }
    },

    // 树节点关闭
    handleNodeCollapse (data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i
        }
      })
    },

    // 重置树状态
    resetTree () {
      this.$nextTick(() => {
        if (utils.isNull(this.selectSubjectId)) {
          this.$refs.subjectTree.setCurrentKey()
        } else {
          this.$refs.subjectTree.setCurrentKey(this.selectSubjectId)
        }
      })
    },

    // 树节点点击事件
    treeNodeClick (data) {
      if (data.status == 1) {
        this.resetTree()
        this.$message({
          type: "warning",
          message: "当前科目已停用，无法选择"
        })
        return false
      }
      if (data.type == 0) {
        this.resetTree()
        this.$message({
          type: "warning",
          message: "只能选择科目节点"
        })
        return false
      }

      this.selectSubjectId = data.id
      this.selectSubjectName = data.name
    },

    getList () {
      if (utils.isNull(this.projectId)) {
        return
      }
      this.list = []
      findSubjectTree(this.projectId).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let list = res.data.data
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = [list[0].id]
          }
          this.list = list
          this.$nextTick(() => {
            this.$refs.subjectTree.setCurrentKey(this.selectSubjectId)
          })
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg () {
      this.subjectId = this.selectSubjectId
      this.subjectName = this.selectSubjectName
      this.subjectType = this.selectSubjectType
      if (utils.isNull(this.subjectId)) {
        this.$message.warning("请选择一个科目")
        return
      }
      this.$store.commit('customerCenter/subjectDlg/SET_SUBJECTTYPE', this.subjectType)
      this.$store.commit('customerCenter/subjectDlg/SET_SUBJECTNAME', this.subjectName)
      this.$store.commit('customerCenter/subjectDlg/SET_SUBJECTID', this.subjectId)
      this.closeDlg()
    },

    closeDlg () {
      this.$store.commit('customerCenter/subjectDlg/SET_DLGTYPE', '')
      this.$store.commit('customerCenter/subjectDlg/SET_DLGSHOW', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

/deep/ .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f0f7ff;
}

/deep/ .el-tree-node:focus > .el-tree-node__content {
  background-color: transparent;
}
</style>