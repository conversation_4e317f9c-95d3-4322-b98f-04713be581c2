<template>
  <!-- 电话报警查询 -->
  <div
    class="app-container zhangkexin"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <el-form
      size="small"
      ref="searchForm"
      class=""
      :model="listQuery"
      label-width="90px"
      @submit.native.prevent
    >
      <el-form-item label="筛选条件：">
        <el-input
          @keyup.enter.native="searchFunc"
          class="m-shaixuan-input fl"
          placeholder="请输入"
          v-model="listQuery.label"
          style="width: 200px"
          size="small"
        >
          <i
            @click="resetSearchItem(['label'])"
            slot="suffix"
            class="el-input__icon el-icon-error"
          ></i>
        </el-input>
        <!-- <el-date-picker
          class="fl ml10"
          style="width: 250px"
          @change="searchFunc"
          v-model="listQuery.dateRange"
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          size="small"
        >
        </el-date-picker> -->

        <el-button
          icon="el-icon-search"
          type="primary"
          size="small"
          class="fl ml10"
          @click="searchFunc"
          >搜索</el-button
        >
      </el-form-item>
      <div class="clear"></div>
    </el-form>
    <el-table
      height="calc(100vh - 320px)"
      ref="tableBar"
      class="m-small-table"
      v-loading="listLoading"
      :key="tableKey"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="序号" align="center" width="60">
        <template slot-scope="scope">
          {{ (listQuery.page - 1) * listQuery.limit + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="时间">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="电话">
        <template slot-scope="scope">
          <span>{{ scope.row.phone }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报警内容">
        <template slot-scope="scope">
          <span>{{ scope.row.content }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="操作"
        width="160"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="showEdit(scope.row)"
            plain
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="delFun(scope.row)"
            plain
            >删除</el-button
          >
          <el-button
            type="info"
            size="mini"
            icon="el-icon-more"
            @click="showDesc(scope.row)"
            plain
            >详情</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>
    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import Pagination from "@/components/Pagination"; // Secondary package based on el-pagination
import { postAction, getAction } from "@/api";
import jsCookie from "js-cookie";
import {
  isNull,
  getDbItems,
  objToParam,
  arrId2Name,
  arrIds2Names,
  getPreMonth
} from "@/utils";
let dlgDataEmpty = {
  // 测试数据
  id: "",
  name: "",
  remark: ""
};
export default {
  components: {
    Pagination
  },
  data() {
    return {
      // 弹窗
      title: "",
      dlgState: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        name: [{ required: true, message: "必填字段", trigger: "change" }]
      },

      list: [],

      listQuery: {
        // dateRange: [],
        label: "",
        page: 1,
        limit: 20
        // beginDate: "",
        // endDate: ""
      },
      total: 0,
      tableKey: 0,
      listLoading: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 清空搜索条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },
    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    getList() {
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      //   // 日期范围
      //   sendObj.beginDate = "";
      //   sendObj.endDate = "";
      //   if (
      //     !isNull(this.listQuery.dateRange) &&
      //     this.listQuery.dateRange.length > 0
      //   ) {
      //     sendObj.beginDate = this.listQuery.dateRange[0];
      //     sendObj.endDate = this.listQuery.dateRange[1];
      //   }
      postAction("/iot/sendMsgConfig/phonePage", sendObj).then(res1 => {
        console.log(res1);
        let res = res1.data;
        if (res.code == 200) {
          this.list = res.data;
          this.total=res.page.total
        } else {
          this.$message.error(res.msg);
        }
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
