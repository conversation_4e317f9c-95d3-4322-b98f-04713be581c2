<template>
  <el-dialog :close-on-click-modal='false' :title="'选择区域'" :visible.sync="dlgShow">
    <el-input placeholder="输入区域名称进行过滤" v-model="filterArea"></el-input>
    <el-tree ref="areaTreeMul" :props="defaultProps" show-checkbox highlight-current node-key="id" @check="treeCheck" :check-strictly="true" :data="list" :default-expanded-keys="defaultOpenList" :filter-node-method="filterNode" @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" :expand-on-click-node="false">
    </el-tree>

    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-back" @click="closeDlg">
        取 消
      </el-button>
      <el-button icon="el-icon-delete" type="danger" @click="clearDlg">
        清 空
      </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'

import {
  findAreaTree,
} from '@/api/reportMan'


export default {
  components: {
  },
  data () {
    return {
      filterArea: "",

      list: [],

      selectKeys: [],

      selectList: [],

      defaultOpenList: [],

      selectAreas: [],

      selectAreaIds: "",

      selectAreaNames: "",

      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.reportMan.areaDlgMul.dlgShow
      },
      set: function (val) {
        this.$store.commit('reportMan/areaDlgMul/SET_DLGSHOW', val)
      }
    },

    areaIds: {
      get: function () {
        return this.$store.state.reportMan.areaDlgMul.areaIds
      },
      set: function (val) {
        this.$store.commit('reportMan/areaDlgMul/SET_AREAIDS', val)
      }
    },

    areaNames: {
      get: function () {
        return this.$store.state.reportMan.areaDlgMul.areaNames
      },
      set: function (val) {
        this.$store.commit('reportMan/areaDlgMul/SET_AREANAMES', val)
      }
    },

    projectId: {
      get: function () {
        return this.$store.state.reportMan.areaDlgMul.projectId
      },
      set: function (val) {
        this.$store.commit('reportMan/areaDlgMul/SET_PROJECTID', val)
      }
    },
  },

  watch: {
    filterArea (val) {
      this.$refs.areaTreeMul.filter(val);
    },

    dlgShow (val) {
      if (val) {
        this.selectAreaIds = this.areaIds
        this.selectAreaNames = this.areaNames
        this.selectKeys = []
        this.getList()
      }
    },

    areaIds (val) {
      this.selectAreaIds = val
    },

    areaNames (val) {
      this.selectAreaNames = val
    }
  },

  methods: {

    // 树节点展开
    handleNodeExpand (data) {
      // 保存当前展开的节点
      let flag = false
      this.defaultOpenList.some(item => {
        if (item === data.id) { // 判断当前节点是否存在， 存在不做处理
          flag = true
          return true
        }
      })
      if (!flag) { // 不存在则存到数组里
        this.defaultOpenList.push(data.id)
      }
    },

    // 树节点关闭
    handleNodeCollapse (data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i
        }
      })
    },

    // 递归全选当前下的节点
    selectAllNode (childrenList, type) {
      for (let item of childrenList) {
        // 全选，全部取消
        if (type == 'select') {
          if (!this.selectKeys.includes(item.id)) {
            this.selectKeys.push(item.id)
            this.selectList.push(item)
          }
        } else {
          if (this.selectKeys.includes(item.id)) {
            let mIndex = this.selectKeys.indexOf(item.id)

            this.selectKeys.splice(mIndex, 1)
            this.selectList.splice(mIndex, 1)
          }
        }
        if (item.children) {
          this.selectAllNode(item.children, type)
        }
      }
    },

    // 树节点点击
    treeCheck (checkedNodes, checkedKeys) {
      if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        // select-全选；remove-取消全选
        this.selectAllNode(checkedNodes.children, 'select')
      } else {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        this.selectAllNode(checkedNodes.children, 'remove')
      }
      this.$refs.areaTreeMul.setCheckedKeys(this.selectKeys)
    },

    filterNode (value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    setCheckedKeys () {
      this.selectAreas = []
      if (utils.isNull(this.selectAreaIds)) {
        this.$refs.areaTreeMul.setCheckedKeys(this.selectAreas)
        return
      }
      this.$nextTick(() => {
        let currentKey = []
        let selectAreaIdList = this.selectAreaIds.split(",")
        let selectAreaNameList = this.selectAreaNames.split(",")
        for (let i in selectAreaIdList) {
          currentKey.push(parseInt(selectAreaIdList[i]))
          this.selectAreas.push({
            id: selectAreaIdList[i],
            name: selectAreaNameList[i]
          })
        }
        this.$refs.areaTreeMul.setCheckedKeys(currentKey)
      })
    },

    getList () {
      this.list = []
      findAreaTree(this.projectId).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = res.data.data ? res.data.data : []
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = this.list.length > 0 ? [this.list[0]['id']] : []
          }
          this.setCheckedKeys()
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg () {
      let checkNodes = this.$refs.areaTreeMul.getCheckedNodes()
      let halfCheckNodes = this.$refs.areaTreeMul.getHalfCheckedNodes()
      this.selectAreas = checkNodes

      let areaIdList = []
      let areaNameList = []
      for (let i of this.selectAreas) {
        areaIdList.push(i['id'])
        areaNameList.push(i['name'])
      }
      this.areaIds = areaIdList.join(",")
      this.areaNames = areaNameList.join(",")
      this.$store.commit('reportMan/areaDlgMul/SET_AREAIDS', this.areaIds)
      this.$store.commit('reportMan/areaDlgMul/SET_AREANAMES', this.areaNames)
      this.closeDlg()
    },

    clearDlg () {
      this.selectAreaIds = ""
      this.selectAreaNames = ""
      this.setCheckedKeys()
    },

    closeDlg () {
      this.$store.commit('reportMan/areaDlgMul/SET_DLGSHOW', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}
.filter-container {
  .fl {
    width: 100%;
  }
}
</style>