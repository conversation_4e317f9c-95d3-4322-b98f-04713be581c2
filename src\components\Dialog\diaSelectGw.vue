<template>
  <div class="">
    <!-- 新增 弹窗 选择岗位 -->
    <el-dialog :close-on-click-modal='false' title="选择员工" top='30px' :visible.sync="userTreeState" width='900px' append-to-body>
      <div>
        <!-- 搜索 -->
        <div class="filter-container">
          <el-form ref="searchForm" class='n-search' :model="listQuery" label-width="90px" @submit.native.prevent>
            <div class="n-search-bar">
              <div class='n-search-item fl'>
                <el-form-item label="关键字：">
                  <el-input @keyup.enter.native='searchFunc' class='m-shaixuan-input' placeholder='岗位名称/所属部门' v-model="listQuery.labelNum"></el-input>
                </el-form-item>
              </div>
              <div class='n-search-item n-search-item-r fr'>
                <el-input v-model="listQuery.branchName" @focus="showBmTree" placeholder="选择部门" style="width: 180px;" readonly>
                </el-input>

                <el-select v-model="listQuery.postId" placeholder="选择岗位" style="width: 180px;">
                  <el-option v-for="item in gwSelect" :key="item.postId" :label="item.label" :value="item.postId">
                  </el-option>
                </el-select>
                <el-button icon='el-icon-search' type="success" size='mini' class="search-right-btn" @click='searchFunc'>搜索</el-button>
                <el-button type="danger" size='mini' class="search-right-btn" @click='emptyFunc'>重置</el-button>
              </div>
              <div class="clear"></div>
            </div>

          </el-form>
        </div>

        <!-- 员工 -->
        <el-table ref="postDom" v-loading="listLoading" :data="list" border fit highlight-current-row max-height='367px' class='m-small-table' @row-click='rowClick' show-overflow-tooltip='true'>
          <el-table-column label="" align="center" width="50">
            <template slot-scope="scope">
              <el-radio v-model="selectedId" :label="scope.row.id" style="width: 16px;"><span></span></el-radio>
            </template>
          </el-table-column>
          <el-table-column label="序号" prop="index" align="center" width="60">
            <template slot-scope="scope">
              <span>{{ scope.row.index }}</span>
            </template>
          </el-table-column>

          <el-table-column label="岗位名称" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.label }}</span>
            </template>
          </el-table-column>

          <el-table-column label="所属部门">
            <template slot-scope="scope">
              <span>{{ scope.row.branchName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="在岗员工" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.account }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column label="工号" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.workNum }}</span>
            </template>
          </el-table-column> -->

        </el-table>

        <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
        <div class="clear"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

// 数据接口
import { findPostByDynamic } from '@/api/postMan'  // 查部门，根据部门查员工

// 页面组件
import Pagination from '@/components/Pagination'

let listQueryEmpty = {
  page: 1,
  size: 20,
  label: '',  // 岗位名称
  postJob: '',  // 岗位职务值
  branchId: '',  // 部门Id
  branchName: '',
  branchType: '0'
}

export default {
  components: { Pagination },
  data() {

    return {
      isShow: false,
      list: [],
      listLoading: false,
      total: 0,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      selectedId: '',
      gwSelect: []
    }
  },
  computed: {
    ...mapGetters([
      // 部门树
      'bmTreeBranchId',
      'bmTreeBranchName',
    ]),
    userTreeState: {
      get: function () {
        let state = this.$store.getters.userTreeState
        if (state === true) {
          if (this.isShow === false) {
            this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
            this.userTreeUserId = ''
            this.userTreeUserName = ''
            this.getList()
            this.isShow = true
          }

        }
        return state
      },
      set: function (newVal) {

        this.$store.commit('SET_USERTREESQTYPE', '')
        this.$store.commit('SET_USERTREESTATE', newVal)
      }
    },
    // id
    userTreeUserId: {
      get: function () {
        let userId = this.$store.getters.userTreeUserId
        if (userId) {
          this.selectedId = userId
        }
        return userId
      },
      set: function (newVal) {
      }
    },
  },
  watch: {
    // 部门树状态
    bmTreeBranchId(val) {
      // alert(val)
      if (val === 'empty') {
        return false
      }
      this.gwSelect = []
      this.listQuery.postId = ''
      this.listQuery.postName = ''
      this.listQuery.branchId = val
      let sendObj = {
        branchId: val,
        str: '',
        page: 1,
        size: 500,
        sqType: ''
      }
      findPostLike(sendObj).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        console.log(res.data)
        if (code === '200') {
          this.gwSelect = res.data.list
        }
      })

    },
    bmTreeBranchName(val) {
      this.listQuery.branchName = val
    },
  },
  created() {
  },
  methods: {
    // 搜索事件
    searchFunc() {
      // alert('search')
      this.listQuery.page = 1
      this.listQuery.size = 20
      this.getList()
    },
    // 清空搜索条件
    emptyFunc() {
      this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
      this.gwSelect = []
      this.getList()
    },

    // 表格行 点击事件
    rowClick(row, column, event) {
      this.selectedId = row.id
    },
    // 获取数据
    getList() {

      this.list = []
      this.selectedId = ''
      this.listLoading = true
      // return false
      findPostByDynamic(this.listQuery).then(res => {
        // 接口：findUserByLabelAndNum
        // 加字段：filterTypes: [1, 2, 3]  // 关键字
        // 1 入职  （可以）
        // 2 转正
        // 3 兼岗
        // 4 取消兼岗
        // 5 社保申请
        // 6 离职
        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg

        if (code === '200') {
          let data = res.data.data
          let list = res.data.list

          this.total = data.total
          for (let i = 0; i < list.length; i++) {
            let item = list[i]
            item.index = (this.listQuery.page - 1) * this.listQuery.size + i + 1
          }
          this.list = JSON.parse(JSON.stringify(list))

        } else {
          this.$message.error(msg)
        }

      })
    },

    // 其他弹窗
    showBmTree() {
      // bmTreeState
      this.$store.commit('SET_BMTREEISROLE', true)
      this.$store.commit('SET_BMTREESTATE', true)
    },

    // 选择部门提交
    bumenOkFunc() {
      if (this.selectedId === '') {
        this.$message({
          type: 'warning',
          message: '请选择员工'
        })
      } else {
        let selectedObj = this.list.filter(item => {
          return item.id === this.selectedId
        })

        this.$store.commit('SET_USERTREEUSERID', '')
        this.$store.commit('SET_USERTREEUSERNAME', '')
        setTimeout(() => {
          this.$store.commit('SET_USERTREEUSERID', this.selectedId)
          this.$store.commit('SET_USERTREEUSERNAME', selectedObj[0].label)
          this.closeDialog()
        }, 50)
      }
    },
    // 关闭弹窗 
    closeDialog() {
      this.isShow = false
      this.$store.commit('SET_USERTREESQTYPE', '')
      this.$store.commit('SET_USERTREESTATE', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.user-list {
  height: 400px;
  border: 1px solid red;
  border-radius: 3px;
}
</style>