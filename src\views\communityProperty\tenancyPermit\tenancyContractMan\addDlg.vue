<template>
  <!-- 弹窗 新增/编辑 -->
  <el-dialog
    class="mazhenguo"
    :title="dlgType === 'add' ? '添加承租合同' : '承租合同详情'"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="600px"
    top="30px"
  >
    <el-form
      ref="dlgDataForm"
      :rules="dlgRules"
      :model="dlgData"
      label-position="right"
      label-width="100px"
      style="width: 550px"
      size="mini"
      @submit.native.prevent
    >
      <el-form-item label="归属小区" prop="communityId">
        <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区" @change="communityIdChange">
          <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择房屋" prop="roomFullName">
        <el-input v-model="dlgData.roomFullName" @focus="showRoomDlg" placeholder="请选择房屋" readonly> </el-input>
      </el-form-item>
      <p v-if="dlgData.roomFullName" class="m-item-box-btips" style="margin-left: 100px">
        <span>建筑面积：{{ dlgData.builtUpArea }}</span>
        <span>套内面积：{{ dlgData.insideArea }}</span>
        <span>房间用途：{{ dlgData.typeName }}</span>
      </p>

      <el-form-item label="租户名" prop="userName">
        <!-- <el-select @change="userIdChange" v-model="dlgData.userId" clearable placeholder="请选择" style="width: 300px">
          <el-option v-for="item of zhSelect" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select> -->
        <el-input disabled v-model="dlgData.userName" placeholder="" />
        <!-- <div class="fr">
          <span style="color: #666; font-size: 12px">没有租户</span>
          <el-button class="" @click="showZhDlg('add')" icon="el-icon-plus" type="primary" plain>新增租户</el-button>
        </div> -->
      </el-form-item>

      <el-form-item label="编号" prop="contractNo">
        <el-input v-model="dlgData.contractNo" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="签章日期" prop="signDate">
        <el-date-picker
          v-model="dlgData.signDate"
          type="date"
          style="width: 100%"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="合同有效期" prop="startDateRange">
        <el-date-picker
          style="width: 100%"
          v-model="dlgData.startDateRange"
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
        >
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="租金" prop="price">
        <el-input-number v-model="dlgData.price" :min="0" placeholder="请输入" controls-position="right" style="width: 140px" />
        元/每月每平方米
      </el-form-item> -->
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
    <roomDlg @backFunc="roomBackFunc" />
    <addZhDlg
      :dlgState0="dlgZhState"
      :dlgData0="dlgZhData"
      :dlgType="dlgZhType"
      :dlgQuery="dlgZhQuery"
      @closeDlg="closeZhDlg"
      @getList="getCzrInfo"
      :roomData="roomData"
    />
  </el-dialog>
</template>
<script>
// 组件
import roomDlg from '@/components/Dialog/communityMan/roomDlg'
import addZhDlg from './addZhDlg'
// 工具
// import { phoneReg } from '@/utils/regUtil'
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
// 接口
import * as utils from '@/utils'
import { postAction, getAction, formAction } from '@/api'

let dlgDataEmpty = {
  id: '0',

  communityId: '', // 小区ID
  communityName: '',
  roomNum: '', // 房屋编号

  floorId: '', // 楼栋id
  floorName: '', //
  unitId: '', // 单元ID
  unitName: '', //

  roomFullName: '', // 根据楼 单元生成的全名  例：1#2单元2203

  builtUpArea: '', // 建筑面积
  insideArea: '', // 套内面积
  type: '', // 房屋性质数据字典
  typeName: '',

  contractNo: '', // 合同编号
  signDate: '', // 签章日期
  startDateRange: [],
  startDate: '', // 有效开始日期
  endDate: '', // 有效结束日期
  price: '0', // 单价 改为0

  layer: '', // 层数
  projectId: '', // 项目id
  projectName: '', // 项目名称
  userId: '', // 承租人id
  userName: '', // 承租人姓名
  phone: '', // 电话
}
export default {
  components: {
    roomDlg,
    addZhDlg,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},

    communityList: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          this.zhSelect = []

          let dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))

          dlgData.communityId = this.dlgQuery.communityId || ''

          if (this.dlgType != 'add') {
            dlgData = JSON.parse(JSON.stringify(this.dlgQuery))
            dlgData.startDateRange = [dlgData.startDate, dlgData.endDate]
          }
          this.dlgData = JSON.parse(JSON.stringify(dlgData))
          this.$nextTick(() => {
            this.$refs['dlgDataForm'].clearValidate()

            if (this.dlgType != 'add') {
              this.getCzrInfo()
            }
          })
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      roomData: {},
      // 弹窗
      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        roomFullName: [{ required: true, message: '必填字段', trigger: 'change' }],
        contractNo: [{ required: true, message: '必填字段', trigger: 'blur' }],
        // userId: [{ required: true, message: '必填字段', trigger: 'change' }],
        signDate: [{ required: true, message: '必填字段', trigger: 'change' }],
        startDateRange: [{ required: true, message: '必填字段', trigger: 'change' }],
        // price: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      dlgSubLoading: false, // 提交loading

      // 下拉框
      zhSelect: [], // 租户

      // 弹窗数据
      dlgZhQuery: {},
      dlgZhState: false,
      dlgZhType: '', // 弹框状态add, edit
      dlgZhData: {},
    }
  },
  created() {
    // this.getDataDict()
  },
  methods: {
    // -- 选择房屋弹窗
    // 选择房号
    showRoomDlg() {
      if (utils.isNull(this.dlgData.communityId)) {
        this.$message.warning('请先选择小区')
        return
      }
      this.$store.commit('communityMan/roomDlg/SET_COMMUNITYID', this.dlgData.communityId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMID', this.dlgData.roomId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMNAME', this.dlgData.roomName)
      this.$store.commit('communityMan/roomDlg/SET_DLGSHOW', true)
    },
    roomBackFunc(data) {
      this.roomData = data
      console.log('---data', JSON.stringify(data))

      this.dlgData.communityId = data.communityId
      this.dlgData.communityName = data.communityName
      this.dlgData.roomNum = data.roomNum
      this.dlgData.floorId = data.floorId
      this.dlgData.floorName = data.floorName
      this.dlgData.unitId = data.unitId
      this.dlgData.unitName = data.unitName

      

      this.dlgData.roomId = data.id
      this.dlgData.roomNum = data.roomNum
      this.dlgData.roomFullName = data.roomFullName

      this.dlgData.builtUpArea = data.builtUpArea
      this.dlgData.insideArea = data.insideArea
      this.dlgData.type = data.type
      this.dlgData.typeName = data.typeName
      this.dlgData.layer = data.layer
      this.dlgData.projectId = data.projectId
      this.dlgData.projectName = data.projectName || ''

      this.dlgData.userId=data.memberId  //租户名就是业主名
      this.dlgData.userName=data.memberName
      this.dlgData.phone=data.memberPhone
      this.dlgData = JSON.parse(JSON.stringify(this.dlgData))

      // this.getCzrInfo()
    },
    // 获取租户信息
    getCzrInfo() {
      console.log('----this.roomData', this.roomData)
      getAction(`/unity/buildingmember/room/${this.dlgData.roomId}/3`).then((res0) => {
        let res = res0.data
        this.dlgSubLoading = false
        if (res.code == 200) {
          this.zhSelect = res.data
          console.log('this.zhSelect', this.zhSelect)
          if (this.dlgType == 'add') {
            if (this.zhSelect.length == 1) {
              this.dlgData.userId = this.zhSelect[0].id
              this.dlgData.userName = this.zhSelect[0].name
              this.dlgData.phone = this.zhSelect[0].phone
            } else {
              this.dlgData.userId = ''
              this.dlgData.userName = ''
              this.dlgData.phone = ''
            }
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
          }
        } else {
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },

    communityIdChange(val) {
      this.dlgData.communityName = utils.arrId2Name(this.communityList, val)
      console.log('----this.dlgData.communityName', this.dlgData.communityName)

      this.dlgData.roomNum = ''
      this.dlgData.floorId = ''
      this.dlgData.floorName = ''
      this.dlgData.unitId = ''
      this.dlgData.unitName = ''

      this.dlgData.roomId = ''
      this.dlgData.roomNum = ''
      this.dlgData.roomFullName = ''

      this.dlgData.builtUpArea = ''
      this.dlgData.insideArea = ''
      this.dlgData.type = ''
      this.dlgData.typeName = ''
      this.dlgData.layer = ''
      this.dlgData.projectId = ''
      this.dlgData.projectName = ''

      this.dlgData.userId = ''
      this.dlgData.userName = ''
      this.dlgData.phone = ''

      this.dlgData = JSON.parse(JSON.stringify(this.dlgData))

      this.zhSelect = []
    },
    // 租户change
    userIdChange(val) {
      let row = ''
      for (let item of this.zhSelect) {
        if (item.id == val) {
          row = item
          break
        }
      }
      this.dlgData.userId = row.id
      this.dlgData.userName = row.name
      this.dlgData.phone = row.phone
      this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
    },

    // -- 新增租户
    showZhDlg(type, row) {
      if (!this.dlgData.communityId) {
        this.$message.warning('请选择小区')
        return false
      }
      if (!this.dlgData.roomFullName) {
        this.$message.warning('请选择房屋')
        return false
      }
      this.dlgZhQuery = { id: 0 }
      this.dlgZhType = type
      this.dlgZhState = true
    },
    // 关闭弹窗
    closeZhDlg() {
      this.dlgZhState = false
    },

    // 弹窗提交 ------
    dlgSubFunc() {
      this.$refs['dlgDataForm'].validate((valid) => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData))

          sendObj.startDate = sendObj.startDateRange[0]
          sendObj.endDate = sendObj.startDateRange[1]
          delete sendObj.startDateRange

          // sendObj.userName = utils.arrId2Name(this.zhSelect, this.dlgData.userId)

          this.dlgSubLoading = true
          postAction('/unity/roomContract/save', sendObj).then((res0) => {
            let res = res0.data
            this.dlgSubLoading = false
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.dlgState = false
              this.$emit('getList')
              this.$emit('closeDlg')
            } else {
              this.$message({
                type: 'warning',
                message: res.msg,
              })
            }
          })
        }
      })
    },

    closeDlg() {
      this.dlgLoading = false
      this.dlgSubLoading = false
      this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')

      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.$nextTick(() => {
        this.$refs['dlgDataForm'].clearValidate()
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>