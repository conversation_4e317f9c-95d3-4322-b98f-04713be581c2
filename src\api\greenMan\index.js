import request from "@/utils/request";

/*
 *绿化统计相关
 */
// 工作项执行次数统计接口
export function implementWorkItemCount(data) {
  return request({
    url: `/green/curing/task-execution/implementWorkItemCount`,
    method: "post",
    data,
  });
}
// 工作项执行人工量
export function implementUserCount(data) {
  return request({
    url: `/green/curing/task-execution/implementUserCount`,
    method: "post",
    data,
  });
}
// 工作项关联苗木统计
export function WorkItemTreeTypeCount(data) {
  return request({
    url: `/green/curing/task-execution/workItemTreeTypeCount`,
    method: "post",
    data,
  });
}

// 病虫害记录统计
export function insectPestCount(data) {
  return request({
    url: `/green/curing/insect-pest/insectPestCount`,
    method: "post",
    data,
  });
}

// 施肥记录统计接口
export function fertilizationCount(data) {
  return request({
    url: `/green/curing/fertilization/fertilizationCount`,
    method: "post",
    data,
  });
}
