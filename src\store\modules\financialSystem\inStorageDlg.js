// 入库单dlg组件

const inStorageDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    list: [],
    list2: [],

    code: "",
    type: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    list: state => state.list,
    list2: state => state.list2,

    code: state => state.code,

    type: state => state.type,
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_LIST: (state, val) => {
      state.list = val
    },
    // 弹窗返回值
    SET_LIST2: (state, val) => {
      state.list2 = val
    },

    SET_CODE: (state, val) => {
      state.code = val
    },
    SET_TYPE: (state, val) => {
      state.type = val
    }
  },

  actions: {

  }
}

export default inStorageDlg
