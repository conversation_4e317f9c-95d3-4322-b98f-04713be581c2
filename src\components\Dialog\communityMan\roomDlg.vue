<template>
  <el-dialog :close-on-click-modal="false" :title="'选择房屋'" :visible.sync="dlgShow" append-to-body width="1100px">
    <div class="filter-container">
      <el-form inline>
        <el-form-item prop="communityId">
          <el-select
            :disabled="communityId != ''"
            v-model="listQuery.communityId"
            filterable
            clearable
            placeholder="请选择小区"
            @change="communityChange"
          >
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="floorId">
          <el-select v-model="listQuery.floorId" filterable clearable placeholder="请选择楼栋" @change="floorChange">
            <el-option v-for="item in buildingList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="unitId">
          <el-select v-model="listQuery.unitId" filterable clearable placeholder="请选择单元">
            <el-option v-for="item in unitList" :key="item.id" :label="item.unitName" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.label" placeholder="请输入房屋名称">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList"> 搜索 </el-button>
      </el-form>
    </div>
    <el-table class="m-small-table" height="392px" :data="list" @row-click="rowClick" border fit highlight-current-row>
      <el-table-column label="#" width="60">
        <template slot-scope="scope">
          <el-radio v-model="selectRoomId" :label="scope.row.id">
            <i></i>
          </el-radio>
        </template>
      </el-table-column>

      <el-table-column label="小区名称">
        <template slot-scope="scope">
          <span>{{ scope.row.communityName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="房屋">
        <template slot-scope="scope">
          <span>{{ scope.row.roomFullName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="业主">
        <template slot-scope="scope">
          <span>{{ scope.row.memberName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="电话">
        <template slot-scope="scope">
          <span>{{ scope.row.memberPhone }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back"> 取 消 </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import { communityPage, cofloorCommunity, buildingunitFloor, buildingroomPage } from '@/api/communityMan'

export default {
  components: {
    Pagination,
  },
  data() {
    return {
      list: [],

      listQuery: {
        limit: 10,
        label: '',
        page: 1,
        communityId: '',
        floorId: '',
        unitId: '',
      },

      communityList: [],
      buildingList: [],
      unitList: [],

      total: 0,

      selectRoomId: '',

      selectRoomName: '',

      selectRoomInfo: {},
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.communityMan.roomDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('communityMan/roomDlg/SET_DLGSHOW', val)
      },
    },

    communityId: {
      get: function () {
        return this.$store.state.communityMan.roomDlg.communityId
      },
      set: function (val) {
        this.$store.commit('communityMan/roomDlg/SET_COMMUNITYID', val)
      },
    },

    roomState: {
      get: function () {
        return this.$store.state.communityMan.roomDlg.roomState
      },
      set: function (val) {
        this.$store.commit('communityMan/roomDlg/SET_ROOMSTATE', val)
      },
    },

    roomId: {
      get: function () {
        return this.$store.state.communityMan.roomDlg.roomId
      },
      set: function (val) {
        this.$store.commit('communityMan/roomDlg/SET_ROOMID', val)
      },
    },

    roomName: {
      get: function () {
        return this.$store.state.communityMan.roomDlg.roomName
      },
      set: function (val) {
        this.$store.commit('communityMan/roomDlg/SET_ROOMNAME', val)
      },
    },

    roomInfo: {
      get: function () {
        return this.$store.state.communityMan.roomDlg.roomInfo
      },
      set: function (val) {
        this.$store.commit('communityMan/roomDlg/SET_ROOMINFO', val)
      },
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.roomId)) {
          this.selectRoomId = ''
          this.selectRoomName = ''
          this.selectRoomInfo = {}
        }
        this.getCommunityList()
        this.getList()
      }
    },

    communityId: {
      handler(val) {
        this.listQuery.communityId = val
        this.getBuildingList(val)
      },
      deep: true,
      immediate: true,
    },

    roomId(val) {
      this.selectRoomId = val
    },

    roomName(val) {
      this.selectRoomName = val
    },

    roomInfo(val) {
      this.selectRoomInfo = val
    },
  },

  methods: {
    communityChange() {
      let communityId = this.listQuery.communityId
      this.listQuery.floorId = ''
      this.listQuery.unitId = ''
      this.getBuildingList(communityId)
    },

    floorChange() {
      let floorId = this.listQuery.floorId
      this.listQuery.unitId = ''
      this.getUnitList(floorId)
    },

    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
      }
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取楼栋列表
    getBuildingList(id) {
      if (utils.isNull(id)) {
        return
      }
      cofloorCommunity(id).then((res) => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data
        }
      })
    },

    // 获取单元列表
    getUnitList(id) {
      if (utils.isNull(id)) {
        return
      }
      buildingunitFloor(id).then((res) => {
        if (res.data.code == 200) {
          this.unitList = res.data.data
        }
      })
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    rowClick(row, column, event) {
      this.selectRoomId = row['id']
      this.selectRoomName = row['roomFullName']
      this.selectRoomInfo = JSON.parse(JSON.stringify(row))
    },

    getList() {
      this.list = []
      this.listQuery.roomState = this.roomState
      buildingroomPage(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data ? res.data.data : []
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    subDlg() {
      this.roomId = this.selectRoomId
      this.roomName = this.selectRoomName
      this.roomInfo = this.selectRoomInfo
      this.$store.commit('communityMan/roomDlg/SET_ROOMID', this.roomId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMNAME', this.roomName)
      this.$store.commit('communityMan/roomDlg/SET_ROOMINFO', this.roomInfo)

      this.$emit('backFunc', this.roomInfo)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('communityMan/roomDlg/SET_ROOMSTATE', '')
      this.$store.commit('communityMan/roomDlg/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>