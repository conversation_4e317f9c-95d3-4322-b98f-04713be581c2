<template>
  <!-- 考勤管理 - 规则设置 -->
  <div class="app-container">
    <div class="form-container">
      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="150px">
        <el-form-item label="缺卡扣工时百分比" prop="configValue">
          <el-input-number v-model="dlgData.configValue" :controls='false' :min="0" :max="100" :step="1"></el-input-number> %
          <el-button icon='el-icon-check' type="success" class="ml10" @click='subForm' :loading='dlgLoading'>保存</el-button>
        </el-form-item>
      </el-form>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findAdeConfig, updateAdeConfig } from '@/api/attendanceMan/rulesSet.js'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  projectId: '',
  type: 1,
  configValue: 0
}


export default {
  name: 'rulesSet',
  extends: WorkSpaceBase,
  components: {
    Pagination
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',  // ADD\EDIT

      rules: {
        configValue: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
      },
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.getData()
  },

  methods: {
    subForm() {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          delete postParam.updateTime
          this.dlgLoading = true
          updateAdeConfig(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    getData() {
      findAdeConfig().then(res => {
        if (res.data.code == 200) {
          this.dlgData = JSON.parse(JSON.stringify(res.data.data))
        } else {
          this.$message.error(res.data.msg)
        }
      })
    }

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>


