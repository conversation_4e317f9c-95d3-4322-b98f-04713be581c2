import request from '@/utils/request'
import { requestExcel } from '@/utils'

/*
*医嘱维护
*/

// 分页查询 
export function findDoctorAdviceDynamic(data) 
{
	return request({
		url: `/follow/findDoctorAdviceDynamic`,
		method: 'post',
		data
	})
}

// 删除
export function updateDoctorAdvice(data)
{
	return request({
		url: `/follow/updateDoctorAdvice`,
		method: 'post',
		data
	})
}

// 新增/修改
export function saveOrUDoctorAdvice(data)
{
	return request({
		url: `/follow/saveOrUDoctorAdvice`,
		method: 'post',
		data
	})
}

// 导入
export function importExcelDoctorAdvice(data)
{
	return requestExcel('/follow/importExcelDoctorAdvice', data)
}




