<template>
  <div class="app-container" style="padding: 0;">
    <div class="filter-container">
      <el-form
        inline
        size="small"
        ref="searchForm"
        class=""
        :model="listQuery"
        label-width="90px"
        @submit.native.prevent
      >
        <el-form-item label="选择年度">
          <el-date-picker
            v-model="listQuery.queryYear"
            type="year"
            format="yyyy"
            value-format="yyyy"
            placeholder="选择年"
            style="width: 140px"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="能耗类型">
          <el-select
            v-model="listQuery.energyType"
            placeholder="能耗类型"
            style="width: 100px"
          >
            <el-option
              v-for="item of statusSelect"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="primary"
          size="small"
          @click="getList"
          >搜索</el-button
        >
        <el-button
          class="search-right-btn"
          @click="downLoadFunc"
          icon="el-icon-download"
          size="mini"
          >导出</el-button
        >
      </el-form>
    </div>
    <p
      v-loading="listLoading"
      class="text-center fs12 finfo"
      v-if="list.length == 0"
    >暂无数据</p>
    <template v-else>
      <div class="table-container" style="height: 80%;">
        <el-table
          class="m-small-table"
          height="calc(100% - 46px)"
          v-loading="listLoading"
          :data="list"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" type="index" width="60" align="center">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in fields"
            :key="index"
            :label="item.name"
          >
            <template slot-scope="scope">
              <span>{{ scope.row[item.value] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-container">
        <pagination
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
      </div>
    </template>
  </div>
</template>
<script>
import { postAction, getAction } from "@/api";
import { mapGetters } from "vuex";
import * as echarts from "echarts";
import * as utils from "@/utils";
import Pagination from "@/components/Pagination";
export default {
  components: {
    Pagination
  },
  data() {
    return {
      listLoading: false,
      listQuery: {
        // label: "",
        queryYear: "",
        queryType: "equip",
        energyType: "water",
        page: 1,
        size: 20
      },
      total: 0,
      list: [],
      statusSelect: [
        { id: "water", name: "用水" },
        { id: "electricity", name: "用电" }
      ],
      fields: [
        { name: "设备名称", value: "equipName", width: "150" },
        { name: "区域", value: "area", width: "150" },
        { name: "设备间", value: "equipRoomName", width: "150" },
        { name: "1月", value: "month1", width: "100" },
        { name: "2月", value: "month2", width: "100" },
        { name: "3月", value: "month3", width: "100" },
        { name: "4月", value: "month4", width: "100" },
        { name: "5月", value: "month5", width: "100" },
        { name: "6月", value: "month6", width: "100" },
        { name: "7月", value: "month7", width: "100" },
        { name: "8月", value: "month8", width: "100" },
        { name: "9月", value: "month9", width: "100" },
        { name: "10月", value: "month10", width: "100" },
        { name: "11月", value: "month11", width: "100" },
        { name: "12月", value: "month12", width: "100" }
      ]
    };
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    const currentYear = new Date().getFullYear().toString();
    this.listQuery.queryYear = currentYear;
    this.getList();
  },
  methods: {
    getList() {
      if (utils.isNull(this.listQuery.queryYear)) {
        this.$message.warning("请先选择年度");
        return;
      }
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      if (sendObj.dateRange && sendObj.dateRange.length > 0) {
        sendObj.startTime = sendObj.dateRange[0];
        sendObj.endTime = sendObj.dateRange[1];
      }
      sendObj.projectId = this.userInfo.projectId;
      this.list = [];
      this.listLoading = true;
      postAction(`/iot/energy-data/energyConsumptionStatistics`, sendObj).then(
        res => {
          this.listLoading = false;
          if (res.data.code == 200) {
            this.list = res.data.data.list.map(item => {
              const months = item.totalStandardCoal.split(",");
              for (let i = 0; i < 12; i++) {
                item[`month${i + 1}`] = months[i] || "0.00";
              }
              return item;
            });
            this.total = res.data.data.data.total;
          } else {
            this.$message.error(res.data.msg);
          }
        }
      );
    },
    //导出
    downLoadFunc() {
      if (utils.isNull(this.listQuery.queryYear)) {
        this.$message.warning("请先选择年度");
        return;
      }
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      if (sendObj.dateRange && sendObj.dateRange.length > 0) {
        sendObj.startTime = sendObj.dateRange[0];
        sendObj.endTime = sendObj.dateRange[1];
      }
      sendObj.projectId = this.userInfo.projectId;
      delete sendObj.dateRange;
      delete sendObj.page;
      delete sendObj.size;
      let loading = this.$loading({
        lock: true,
        text: "导出中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      postAction(
        "/iot/energy-data/energyConsumptionStatisticsExcel",
        sendObj
      ).then(res0 => {
        loading.close();
        window.open(res0.data.data.url);
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.echart-container {
  height: 90%;
}
</style>
