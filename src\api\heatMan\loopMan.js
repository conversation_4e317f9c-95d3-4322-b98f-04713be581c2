import request from '@/utils/request'

// 分页 
export function page (data) {
  return request({
    url: `/nbiot/loop/page`,
    method: 'post',
    data
  })
}

// 新增
export function saveOrUpdate (data) {
  return request({
    url: `/nbiot/loop/saveOrU`,
    method: 'post',
    data
  })
}
// 设置开度
export function settingValve (data) {
  return request({
    url: `/nbiot/loop/settingValve`,
    method: 'post',
    data
  })
}
// 推送日志
export function balanceValvePage (data) {
  return request({
    url: `/nbiot/balance/valve/page`,
    method: 'post',
    data
  })
}
// 开度日志
export function settingPaage (data) {
  return request({
    url: `/nbiot/valve/setting/page`,
    method: 'post',
    data
  })
}
// 曲线图
export function tempByLoopAndDay (data) {
  return request({
    url: `/nbiot/loopLog/tempByLoopAndDay`,
    method: 'post',
    data
  })
}


// 删除
export function del (id) {
  return request({
    url: `/nbiot/loop/del/${id}`,
    method: 'get'
  })
}