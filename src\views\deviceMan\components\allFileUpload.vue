<template>
  <div>
    <el-upload
      class="upload-demo"
      ref="upload"
      action="#"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :beforeUpload="beforeUpload"
      :file-list="fileList"
      :http-request="handleUpload"
      :limit="limit"
      :multiple="limit > 1"
      :auto-upload="true"
      :on-exceed="handleExceed"
    >
      <el-button slot="trigger" size="small" type="primary">上传附件</el-button>
      <!-- <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传到服务器</el-button> -->
      <div
        slot="tip"
        class="el-upload__tip"
        v-if="!(onlyImage && dlgType == 'info')"
      >
        最多上传{{ limit }}个{{ onlyImage ? "图片" : "附件" }}，且不超过{{
          maxSize
        }}MB
      </div>
    </el-upload>
  </div>
</template>
   
  <script>
import { put, signatureUrl, getFileNameUUID } from "@/utils/tool.js";
export default {
  name: "App",
  props: {
    fileList0: {
      type: Array,
      default: [],
    },
    limit: {
      type: Number,
      default: 5,
    },
    maxSize: {
      type: Number,
      default: 5,
    },
    dlgType: {
      type: String,
      default: "add",
    },
    onlyImage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fileList: [],
    };
  },
  watch: {
    fileList0: {
      handler(val) {
        this.fileList = val;
      },
    },
  },
  methods: {
    getFileName(name) {
      return name.substring(name.lastIndexOf(".")); //.txt
    },
    beforeUpload(file) {
      const fileExtension = this.getFileName(file.name);
      const allowedExtensions = [
        ".png",
        ".jpg",
        ".jpeg",
        ".doc",
        ".docx",
        ".pdf",
        ".xls",
        ".xlsx",
        ".ppt",
        ".pptx",
        ".txt",
        ".zip",
        ".rar"
      ];
      const isWithinSizeLimit = file.size / 1024 / 1024 <= this.maxSize;

      if (!allowedExtensions.includes(fileExtension)) {
        this.$message.error(
          `上传文件类型只能是 ${allowedExtensions.join(", ")} 格式！`
        );
      }
      if (!isWithinSizeLimit) {
        this.$message.error(`上传文件大小不能超过 ${this.maxSize}MB！`);
      }

      return allowedExtensions.includes(fileExtension) && isWithinSizeLimit;
    },

    handleUpload(option) {
      var obj = this.timestamp();
      let objName = obj + this.getFileName(option.file.name);

      put(`ERP_web/reportMan/${objName}`, option.file)
        .then((res) => {
          console.log(res, "res");
          const newFile = { name: option.file.name, url: res.url };
          if (!this.fileList.some((file) => file.name === newFile.name)) {
            this.$nextTick(() => {
              this.fileList = [...this.fileList, newFile];
              this.$emit("successBack", this.fileList);
            });
          }
        })
        .catch((error) => {
          console.error("Upload failed:", error);
          this.$message.error("文件上传失败，请重试！");
        });
    },
    timestamp() {
      var time = new Date();
      var y = time.getFullYear();
      var m = time.getMonth() + 1;
      var d = time.getDate();
      var h = time.getHours();
      var mm = time.getMinutes();
      var s = time.getSeconds();
      return (
        "" +
        y +
        this.add0(m) +
        this.add0(d) +
        this.add0(h) +
        this.add0(mm) +
        this.add0(s)
      );
    },
    add0(m) {
      return m < 10 ? "0" + m : m;
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleExceed() {
      this.$message.warning("最多上传" + this.limit + "个附件");
    },
    handlePreview(file) {
      if (file.url) {
        window.open(file.url);
      }
    },
    handleRemove(file, fileList) {
      this.getFileList();
    },
    handleSuccess(file0, fileList) {
      this.getFileList();
    },
    getFileList() {
      const files = this.$refs.upload.uploadFiles;
      const fileList = files
        .filter((fileItem) => fileItem.url && fileItem.name)
        .map((fileItem) => ({ name: fileItem.name, url: fileItem.url }));

      this.$emit("successBack", fileList);
    },
  },
};
</script>
   
<style>
</style>