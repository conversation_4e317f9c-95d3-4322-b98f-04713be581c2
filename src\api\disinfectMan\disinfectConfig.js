import request from '@/utils/request'

/*
 * 消毒管理基础信息维护
 */

// 消毒方式分页 
export function dismodePage (data) {
  return request({
    url: `/cloth/dis-mode/page`,
    method: 'post',
    data
  })
}
// 添加消毒方式 
export function dismodeAdd (data) {
  return request({
    url: `/cloth/dis-mode/add`,
    method: 'post',
    data
  })
}


//消毒方式删除 
export function dismodeDel (id) {
  return request({
    url: `/cloth/dis-mode/del/${id}`,
    method: 'get'
  })
}


// 消毒设备管理分页 
export function disEquipmentPage (data) {
    return request({
      url: `/cloth/dis-equipment/page`,
      method: 'post',
      data
    })
}
// 消毒设备管理新增
export function disEquipmentAdd (data) {
    return request({
      url: `/cloth/dis-equipment/add`,
      method: 'post',
      data
    })
}
// 消毒设备管理删除
export function disEquipmentDel (id) {
    return request({
      url: `/cloth/dis-equipment/del/${id}`,
      method: 'get',
    })
}



// 点位类型设置分页
export function disPointTypePage (data) {
    return request({
      url: `/cloth/dis-point-type/page`,
      method: 'post',
      data
    })
}
// 点位类型设置新增
export function disPointTypeAdd (data) {
    return request({
      url: `/cloth/dis-point-type/add`,
      method: 'post',
      data
    })
}
// 点位类型设置删除
export function disPointTypeDel (id) {
    return request({
      url: `/cloth/dis-point-type/del/${id}`,
      method: 'get',
    })
}

// 点位类型设置消毒项
export function disPointTypeSet (data) {
  return request({
    url: `/cloth/dis-point-type/set`,
    method: 'post',
    data
  })
}
// 点位类型设置消毒项详情
export function disPointTypeInfo (id) {
  return request({
    url: `/cloth/dis-point-type/info/${id}`,
    method: 'get',
  })
}


// 点位设置分页
export function disPointPage (data) {
  return request({
    url: `/cloth/dis-point/page`,
    method: 'post',
    data
  })
}
// 点位设置二维码下载
export function exportQrImages (data) {
  return request({
    url: `/cloth/dis-point/exportQrImages?${data}`,
    method: 'get',
  })
}
// 新增点位维护
export function disPointAdd (data) {
  return request({
    url: `/cloth/dis-point/add`,
    method: 'post',
    data
  })
}
// 点位维护删除
export function disPointDel (id) {
  return request({
    url: `/cloth/dis-point/del/${id}`,
    method: 'get',
  })
}
// 点位维护详情
export function disPointInfo (id) {
  return request({
    url: `/cloth/dis-point/info/${id}`,
    method: 'get',
  })
}



