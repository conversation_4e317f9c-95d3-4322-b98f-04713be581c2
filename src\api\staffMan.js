import request from "@/utils/request";
import { requestExcel } from "@/utils";

// 员工列表 动态
export function findUserByLabelAndNum (data) {
  return request({
    url: `/sys/findUserByLabelAndNum`,
    method: "post",
    data
  });
}

// 员工列表 动态
export function findUserByLabelAndNumV2 (data) {
  return request({
    url: `/sys/findUserByLabelAndNum/v2`,
    method: "post",
    data
  });
}

// 员工列表 动态
export function findUserLike (data) {
  return request({
    url: `/sys/findUserLike`,
    method: "post",
    data
  });
}

// 新增员工 基本信息
export function saveSysUser (data) {
  return request({
    url: `/sys/saveSysUser`,
    method: "post",
    data
  });
}

// 校园端查人
export function findUserInfoPage (data) {
  return request({
    url: `/sys/user/findUserInfoPage`,
    method: "post",
    data
  });
}

// 根据考勤组查人
export function findUserByLabelAndNumByGroup (data) {
  return request({
    url: `/sys/findUserByLabelAndNumByGroup`,
    method: "post",
    data
  });
}

// 删除员工
export function delSysUser (data) {
  return request({
    url: `/sys/delSysUser`,
    method: "post",
    data
  });
}

// 查找用户信息
export function findSysUser (id) {
  return request({
    url: `/sys/findSysUser/${id}`,
    method: "get"
  });
}
// 查找是否有报事按钮
export function getProjectInfo (id) {
  return request({
    url: `/sys/getProjectInfo?id=${id}`,
    method: "get"
  });
}

// 离职单据查询用户信息
findSysUser;
export function findSysUserByLZ (data) {
  return request({
    url: `/sys/findSysUserByLZ`,
    method: "post",
    data
  });
}

// 查找用户扩展信息
export function findUserInformation (id) {
  return request({
    url: `/sys/findUserInformation/${id}`,
    method: "get"
  });
}

// 修改用户信息
export function upDateSysUser (data) {
  return request({
    url: `/sys/upDateSysUser`,
    method: "post",
    data
  });
}

// 修改员工 扩展信息
export function upDateSysUserInformation (data) {
  return request({
    url: `/sys/upDateSysUserInformation`,
    method: "post",
    data
  });
}

// 【【 员工操作
// 员工转正 / 离职
export function saveStaffStateRecord (data) {
  return request({
    url: `/sys/saveStaffStateRecord`,
    method: "post",
    data
  });
}

// 根据ID 获取岗位信息
export function findPostByPostId (id) {
  return request({
    url: `/sys/findPostByPostId/${id}`,
    method: "get"
  });
}
// 提交调岗操作
export function updateUserPost (data) {
  return request({
    url: `/sys/updateUserPost`,
    method: "post",
    data
  });
}

// 获取兼岗列表
export function findPostByUserId (id) {
  return request({
    url: `/sys/findPostByUserId/${id}`,
    method: "get"
  });
}

// 新增兼岗
export function saveUserPost (data) {
  return request({
    url: `/sys/saveUserPost`,
    method: "post",
    data
  });
}
// 取消兼岗
export function cancelUserPost (data) {
  return request({
    url: `/sys/cancelUserPost`,
    method: "post",
    data
  });
}

// 社保管理
export function saveSecurity (data) {
  return request({
    url: `/sys/saveSecurity`,
    method: "post",
    data
  });
}

//  根据部门查员工
export function findSysUserByBId (id) {
  return request({
    url: `/sys/findSysUserByBId/${id}`,
    method: "get"
  });
}

// 获取职业生涯
export function findCRById (id) {
  return request({
    url: `/sys/findCRById/${id}`,
    method: "get"
  });
}

// 重置密码

export function updatePwdByUserId (data) {
  return request({
    url: `/sys/updatePwdByUserId`,
    method: "post",
    data
  });
}

// 【【 社保查询
//
export function findSecurityFamilyByDynamic (data) {
  return request({
    url: `/sys/findSecurityFamilyByDynamic`,
    method: "post",
    data
  });
}

// 】】 社保查询

export function importUserInfo (data) {
  return requestExcel("/sys/importUserInfo", data);
}
