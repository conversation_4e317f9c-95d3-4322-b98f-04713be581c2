/** 六院 流调管理 **/

import Layout from "@/views/layout/Layout";

const flowTabManRouter = {
  path: "/flowToneTabMan",
  component: Layout,
  name: "flowToneTabMan",
  meta: {
    title: "流调表管理",
    icon: "form",
    roles: ["liu<PERSON><PERSON><PERSON><PERSON><PERSON>"]
  },
  children: [
    // 六院 流调表
    {
      path: "questionnaires",
      name: "流调表",
      component: () => import("@/views/flowTabMan/questionnaires"),
      meta: {
        title: "流调表",
        roles: ["liudiaobiao"]
      },
      children: []
    },

    {
      path: "sportsRehabilitationHospitalQuestionnaires",
      name: "流调表（体育康复）",
      component: () =>
        import(
          "@/views/flowTabMan/sportsRehabilitationHospitalQuestionnaires/index"
        ),
      meta: {
        title: "流调表（体育康复）",
        roles: ["liudiaobiao_tykf"]
      },
      children: []
    }
  ]
};

export default flowTabManRouter;
