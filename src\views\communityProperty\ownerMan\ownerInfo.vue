<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="关键字：">
          <el-input
            @keyup.enter.native="getList"
            placeholder="请输入业主名称"
            v-model="listQuery.label"
          >
            <i
              slot="suffix"
              @click="resetSearchItem(['label'])"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="getList"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="addItem"
          >新增业主</el-button
        >
        <el-button
          icon="el-icon-download"
          size="mini"
          type="primary"
          @click="exportExcel"
        >
          Excel导出
        </el-button>
        <el-upload class="upload-wrap" action="" :before-upload="uploadItem">
          <el-button icon="el-icon-upload" size="mini" type="primary">
            Excel导入
          </el-button>
        </el-upload>
        <el-button
          icon="el-icon-download"
          type="primary"
          size="mini"
          @click="downloadItem()"
        >
          模板下载
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="性别">
          <template slot-scope="scope">
            <span>{{ scope.row.sex }}</span>
          </template>
        </el-table-column>

        <el-table-column label="年龄">
          <template slot-scope="scope">
            <span>{{ scope.row.age }}</span>
          </template>
        </el-table-column>

        <el-table-column label="身份证">
          <template slot-scope="scope">
            <span>{{ scope.row.idCard }}</span>
          </template>
        </el-table-column>

        <el-table-column label="联系方式">
          <template slot-scope="scope">
            <span>{{ scope.row.phone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="微信">
          <template slot-scope="scope">
            <span>{{ scope.row.wchat }}</span>
          </template>
        </el-table-column>

        <el-table-column label="QQ">
          <template slot-scope="scope">
            <span>{{ scope.row.qq }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="320"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              type="success"
              size="mini"
              icon="el-icon-check"
              plain
              @click="bindItem(scope.row, 'BIND')"
              >入住</el-button
            >
            <el-button
              type="warning"
              size="mini"
              icon="el-icon-close"
              plain
              @click="bindItem(scope.row, 'UNBIND')"
              >解绑</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              plain
              @click="editItem(scope.row, 'EDIT')"
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              plain
              @click="delItem(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>

    <el-dialog
      :close-on-click-modal="false"
      title="新增/编辑业主信息"
      :visible.sync="dlgShow"
      width="800px"
      top="30px"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :rules="rules"
        :model="dlgData"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="手机" prop="phone">
          <el-input
            v-model="dlgData.phone"
            placeholder="请填写手机号码"
            @input="getOwnerInfo"
          ></el-input>
        </el-form-item>

        <el-form-item label="姓名" prop="name">
          <el-input v-model="dlgData.name" placeholder="请填写姓名"></el-input>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex">
              <el-radio-group v-model="dlgData.sex">
                <el-radio :label="'男'">男</el-radio>
                <el-radio :label="'女'">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄">
              <el-input-number
                v-model="dlgData.age"
                :controls="false"
                :min="0"
                :precision="0"
                :step="1"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="身份证">
          <el-input
            v-model="dlgData.idCard"
            placeholder="请填写身份证号码"
          ></el-input>
        </el-form-item>

        <el-form-item label="微信">
          <el-input v-model="dlgData.wchat" placeholder="请填写微信"></el-input>
        </el-form-item>

        <el-form-item label="QQ">
          <el-input v-model="dlgData.qq" placeholder="请填写QQ"></el-input>
        </el-form-item>

        <el-form-item label="照片">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :before-upload="beforeUpload"
          >
            <el-image
              v-if="dlgData.photo"
              class="upload-img"
              :src="dlgData.photo"
              alt=""
            ></el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <i
              v-if="dlgData.photo"
              @click.stop="delUploadImg()"
              class="el-icon-error avatar_icon"
            ></i>
          </el-upload>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-divider content-position="left">房屋信息</el-divider>
        <div class="clearfix">
          <el-button
            type="primary"
            size="mini"
            class="fr"
            icon="el-icon-plus"
            plain
            @click="showRoomDlgMul()"
            >添加房屋</el-button
          >
        </div>
        <el-table
          class="m-small-table mt10"
          :data="dlgData.rooms"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="小区名称">
            <template slot-scope="scope">
              <span>{{ scope.row.communityName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="房屋名称">
            <template slot-scope="scope">
              <span>{{ scope.row.roomFullName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-close"
                circle
                @click="delRoom(scope.$index)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-divider content-position="left">车辆信息</el-divider>
        <div class="clearfix">
          <el-button
            type="primary"
            size="mini"
            class="fr"
            icon="el-icon-plus"
            plain
            @click="addCar()"
            >添加车辆</el-button
          >
        </div>
        <el-table
          class="m-small-table mt10"
          :data="dlgData.memberCars"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="车辆型号">
            <template slot-scope="scope">
              <el-form-item
                label-width="0"
                :prop="'memberCars.' + scope.$index + '.carModel'"
                :rules="[
                  { required: true, message: '车辆型号必填', trigger: 'blur' }
                ]"
              >
                <el-input
                  v-model="scope.row.carModel"
                  placeholder="请填写车辆型号"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="车牌号">
            <template slot-scope="scope">
              <el-form-item
                label-width="0"
                :prop="'memberCars.' + scope.$index + '.carNum'"
                :rules="[
                  { required: true, message: '车牌号必填', trigger: 'blur' }
                ]"
              >
                <el-input
                  v-model="scope.row.carNum"
                  placeholder="请填写车牌号"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="绑定车库">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-plus"
                circle
                @click="bindCar(scope.$index, 'GARAGE')"
              ></el-button>
              <el-popover
                placement="right"
                width="400"
                trigger="click"
                v-if="scope.row.garageList.length > 0"
              >
                <el-table :data="scope.row.garageList">
                  <el-table-column label="小区名称">
                    <template slot-scope="scope1">
                      {{ scope1.row.communityName }}
                    </template>
                  </el-table-column>
                  <el-table-column label="车库区域">
                    <template slot-scope="scope1">
                      {{ scope1.row.area }}
                    </template>
                  </el-table-column>
                  <el-table-column label="车库编号">
                    <template slot-scope="scope1">
                      {{ scope1.row.numStr }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="80">
                    <template slot-scope="scope1">
                      <el-button
                        type="danger"
                        size="mini"
                        icon="el-icon-close"
                        circle
                        @click="
                          delBindCar(scope.$index, scope1.$index, 'GARAGE')
                        "
                      ></el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button slot="reference" type="success"
                  >已绑定车库</el-button
                >
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="绑定车位">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-plus"
                circle
                @click="bindCar(scope.$index, 'PARKING')"
              ></el-button>
              <el-popover
                placement="right"
                width="400"
                trigger="click"
                v-if="scope.row.parkingList.length > 0"
              >
                <el-table :data="scope.row.parkingList">
                  <el-table-column label="小区名称">
                    <template slot-scope="scope1">
                      {{ scope1.row.communityName }}
                    </template>
                  </el-table-column>
                  <el-table-column label="车位区域">
                    <template slot-scope="scope1">
                      {{ scope1.row.area }}
                    </template>
                  </el-table-column>
                  <el-table-column label="车位编号">
                    <template slot-scope="scope1">
                      {{ scope1.row.numStr }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="80">
                    <template slot-scope="scope1">
                      <el-button
                        type="danger"
                        size="mini"
                        icon="el-icon-close"
                        circle
                        @click="
                          delBindCar(scope.$index, scope1.$index, 'PARKING')
                        "
                      ></el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button slot="reference" type="success"
                  >已绑定车位</el-button
                >
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-close"
                plain
                @click="delCar(scope.$index)"
                >解绑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button
          type="success"
          :loading="dlgLoading"
          @click="subDlg"
          icon="el-icon-check"
        >
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      :title="dlgType == 'BIND' ? '入住房屋' : '解绑房屋'"
      :visible.sync="dlgShowBind"
      width="800px"
      append-to-body
    >
      <el-form :rules="rules" :model="dlgData" label-position="right">
        <el-divider content-position="left">业主信息</el-divider>
        <el-row>
          <el-col :span="6">
            <el-image
              v-if="dlgData.photo"
              class="upload-img"
              :src="dlgData.photo"
              alt=""
            ></el-image>
          </el-col>
          <el-col :span="18">
            <el-row>
              <el-col :span="12">
                <el-form-item label="姓名">
                  {{ dlgData.name }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  {{ dlgData.sex }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="身份证">
                  {{ dlgData.idCard }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话">
                  {{ dlgData.phone }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注">
              {{ dlgData.remark }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">房屋信息</el-divider>
        <div class="clearfix">
          <el-button
            v-if="dlgType == 'BIND'"
            type="primary"
            size="mini"
            class="fr"
            icon="el-icon-plus"
            plain
            @click="showRoomDlgMul()"
            >添加房屋</el-button
          >
        </div>
        <el-table
          class="m-small-table mt10"
          :data="dlgData.roomMembers"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="小区名称">
            <template slot-scope="scope">
              <span>{{ scope.row.communityName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="房屋名称">
            <template slot-scope="scope">
              <span>{{ scope.row.roomFullName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            v-if="dlgType === 'UNBIND'"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                plain
                @click="unbindRoom(scope.row, scope.$index)"
                >解除绑定</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShowBind = false" icon="el-icon-back"
          >取消</el-button
        >
        <el-button
          v-if="dlgType === 'BIND'"
          type="success"
          :loading="dlgLoading"
          @click="subDlgBind"
          icon="el-icon-check"
        >
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <roomDlgMul />
    <garageDlgMul />
    <parkingDlgMul />
  </div>
</template>

<script>
import Cookie from "js-cookie";
import { mapGetters } from "vuex";
import {
  buildingmemberPageView,
  buildingmemberAddOrUpdate,
  buildingmemberOwner,
  buildingmemberPhone,
  buildingmemberInfo,
  buildingmemberBinding,
  buildingmemberUnbundling,
  importBuildRoomMember,
  findGarageParkingByMemberId
} from "@/api/ownerMan";
import * as utils from "@/utils";
import Pagination from "@/components/Pagination";
import { uploadImg } from "@/utils/uploadImg";
import roomDlgMul from "@/components/Dialog/communityMan/roomDlgMul";
import garageDlgMul from "@/components/Dialog/communityMan/garageDlgMul";
import parkingDlgMul from "@/components/Dialog/communityMan/parkingDlgMul";
import WorkSpaceBase from "@/components/WorkSpace/WorkSpaceBase";

let dlgDataEmpty = {
  id: "",
  age: "",
  name: "",
  phone: "",
  photo: "",
  remark: "",
  idCard: "",
  sex: "男",
  wchat: "",
  qq: "",
  roomMembers: [],
  rooms: [],
  memberCars: []
};

export default {
  name: "ownerInfo",
  extends: WorkSpaceBase,
  components: {
    Pagination,
    roomDlgMul,
    garageDlgMul,
    parkingDlgMul
  },
  data() {
    return {
      // 弹窗 状态
      dlgShowBind: false,
      dlgShow: false, // 新增
      dlgType: "", // ADD\EDIT
      dlgTitle: "", // 标题

      rules: {
        sex: [{ required: true, message: "必填字段", trigger: "blur" }],
        name: [{ required: true, message: "必填字段", trigger: "change" }],
        phone: [
          { required: true, message: "必填字段", trigger: "blur" },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: "手机号码格式有误！",
            trigger: "blur"
          }
        ],
        idCard: [
          { required: true, message: "必填字段", trigger: "blur" },
          {
            pattern: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
            message: "证件号码格式有误！",
            trigger: "blur"
          }
        ]
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      selectIdx: -1,
      listQuery: {
        page: 1,
        limit: 20,
        label: ""
      },
      userInfo: {}
    };
  },

  computed: {
    ...mapGetters("communityMan/roomDlgMul", {
      roomList: "list"
    }),
    ...mapGetters("communityMan/garageDlgMul", {
      garageList: "list"
    }),
    ...mapGetters("communityMan/parkingDlgMul", {
      parkingList: "list"
    })
  },

  watch: {
    roomList(val) {
      let list = JSON.parse(JSON.stringify(val));
      if (this.dlgType === "ADD" || this.dlgType === "EDIT") {
        let rooms = this.dlgData.rooms;
        let idList = rooms.map(item => item.id);
        for (let i of list) {
          if (!idList.includes(i.id)) {
            this.dlgData.rooms.push(i);
          }
        }
      } else if (this.dlgType === "BIND") {
        let rooms = this.dlgData.roomMembers;
        let idList = rooms.map(item => item.id);
        for (let i of list) {
          if (!idList.includes(i.id)) {
            this.dlgData.roomMembers.push(i);
          }
        }
      }
      this.$forceUpdate();
    },

    garageList(val) {
      let list = JSON.parse(JSON.stringify(val));
      let garageList = this.dlgData.memberCars[this.selectIdx].garageList;
      let idList = garageList.map(item => item.id);
      for (let i of list) {
        if (!idList.includes(i.id)) {
          this.dlgData.memberCars[this.selectIdx].garageList.push(i);
        }
      }
      this.$forceUpdate();
    },

    parkingList(val) {
      let list = JSON.parse(JSON.stringify(val));
      let parkingList = this.dlgData.memberCars[this.selectIdx].parkingList;
      let idList = parkingList.map(item => item.id);
      for (let i of list) {
        if (!idList.includes(i.id)) {
          this.dlgData.memberCars[this.selectIdx].parkingList.push(i);
        }
      }
      this.$forceUpdate();
    }
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
  },

  methods: {
    // 导出
    exportExcel() {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery));
      exportParam.userId = this.userInfo.id;
      exportParam.projectId = this.userInfo.projectId;
      let param = Object.keys(exportParam)
        .map(function(key) {
          return (
            encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key])
          );
        })
        .join("&");

      let sendUrl =
        location.protocol +
        "//" +
        location.host +
        `/saapi/unity/report/exportBuildRoomMember?` +
        param;
      window.open(sendUrl);
    },

    // 下载
    downloadItem() {
      let url =
        "https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E5%AF%BC%E5%85%A5%E4%B8%9A%E4%B8%BB%E6%A8%A1%E6%9D%BF.xlsx";
      window.open(url);
    },

    // 上传
    uploadItem(file) {
      let name = file.name.split(".");
      let suffix = name[name.length - 1];

      if (suffix !== "xls" && suffix !== "xlsx") {
        this.$message({
          type: "warning",
          message: "只能上传xls/xlsx文件"
        });
        return false;
      }

      let loading = this.$loading({
        lock: true,
        text: "导入中",
        background: "rgba(0, 0, 0, 0.7)"
      });

      let postParam = {
        file
      };

      importBuildRoomMember(postParam).then(res => {
        loading.close();
        if (res.data.code == 200) {
          this.$message.success("导入成功");
          this.getList();
        } else {
          this.$message({
            type: "warning",
            message: res.data.msg
          });
        }
      });

      return false;
    },

    // 添加车辆
    addCar() {
      this.dlgData.memberCars.push({
        carModel: "",
        carNum: "",
        garageList: [],
        parkingList: []
      });
    },

    // 绑定车辆
    bindCar(idx, type) {
      this.selectIdx = idx;
      let item = this.dlgData.memberCars[idx];
      if (type == "GARAGE") {
        this.$store.commit(
          "communityMan/garageDlgMul/SET_MEMBERID",
          this.dlgData.id
        );
        this.$store.commit(
          "communityMan/garageDlgMul/SET_LIST",
          JSON.parse(JSON.stringify(item.garageList))
        );
        this.$store.commit("communityMan/garageDlgMul/SET_DLGSHOW", true);
      } else {
        this.$store.commit(
          "communityMan/parkingDlgMul/SET_MEMBERID",
          this.dlgData.id
        );
        this.$store.commit(
          "communityMan/parkingDlgMul/SET_LIST",
          JSON.parse(JSON.stringify(item.parkingList))
        );
        this.$store.commit("communityMan/parkingDlgMul/SET_DLGSHOW", true);
      }
    },

    // 删除绑定车库 车位
    delBindCar(rowIdx, idx, type) {
      if (type == "GARAGE") {
        this.dlgData.memberCars[rowIdx].garageList.splice(idx, 1);
      } else {
        this.dlgData.memberCars[rowIdx].parkingList.splice(idx, 1);
      }
    },

    // 删除车辆
    delCar(idx) {
      this.dlgData.memberCars.splice(idx, 1);
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
    },

    // 显示人员对话框
    showRoomDlgMul() {
      let list = JSON.parse(JSON.stringify(this.dlgData.rooms));
      if (this.dlgType === "BIND") {
        list = JSON.parse(JSON.stringify(this.dlgData.roomMembers));
      }
      this.$store.commit("communityMan/roomDlgMul/SET_LIST", list);
      this.$store.commit("communityMan/roomDlgMul/SET_DLGSHOW", true);
    },

    // 获取业主信息
    getOwnerInfo() {
      if (this.dlgData.phone.length == 11 && this.dlgType == "ADD") {
        let phone = this.dlgData.phone;
        buildingmemberPhone(this.dlgData.phone).then(res => {
          this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
          this.dlgData.phone = phone;
          if (res.data.code == 200) {
            this.dlgData = Object.assign(
              this.dlgData,
              JSON.parse(JSON.stringify(res.data.data))
            );
            this.dlgData.rooms = utils.isNull(res.data.data.rooms)
              ? []
              : res.data.data.rooms;
            this.dlgData.memberCars = utils.isNull(res.data.data.memberCars)
              ? []
              : res.data.data.memberCars;
          }
        });
      }
    },

    // 获取数据
    getList() {
      this.count++;
      this.listLoading = true;
      buildingmemberPageView(this.listQuery).then(res => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data));
          this.total = res.data.page ? res.data.page.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 显示弹窗
    addItem() {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgType = "ADD";
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },

    // 弹窗提交
    subDlg() {
      this.$refs["dlgForm"].validate(valid => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData));
          if (postParam.rooms.length == 0) {
            this.$message.warning("请添加房屋");
            return;
          }
          postParam.projectId = this.userInfo.projectId;
          postParam.type = 1;
          let memberCars = [];
          for (let i of postParam.memberCars) {
            memberCars.push({
              carModel: i.carModel,
              carNum: i.carNum,
              // garages: i.garageList.map((item) => item.id).join(','),
              parkings: i.parkingList.map(item => item.id).join(",")
            });
          }
          postParam.memberCars = memberCars;
          this.dlgLoading = true;
          buildingmemberAddOrUpdate(postParam).then(res => {
            this.dlgLoading = false;
            if (res.data.code == 200) {
              this.getList();
              this.dlgShow = false;
              this.$message.success(res.data.msg);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },

    // 提交入住/解绑
    subDlgBind() {
      if (this.dlgType === "BIND") {
        let roomMembers = [];
        for (let i of this.dlgData.roomMembers) {
          roomMembers.push({
            communityId: i.communityId,
            memberId: this.dlgData.id,
            roomId: i.id,
            roomName: i.roomFullName,
            type: 1
          });
        }
        if (roomMembers.length == 0) {
          this.$message.warning("请添加房屋");
          return;
        }
        let postParam = {
          roomMembers
        };
        buildingmemberBinding(postParam).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.dlgShowBind = false;
            this.getList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      }
    },

    // 编辑
    editItem(data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgData = Object.assign(
        this.dlgData,
        JSON.parse(JSON.stringify(data))
      );
      this.dlgType = type;
      this.dlgShow = true;
      buildingmemberInfo(data.id).then(res => {
        if (res.data.code == 200) {
          this.dlgData.rooms = res.data.data.rooms;
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.msg);
        }
      });
      findGarageParkingByMemberId(data.id).then(res => {
        if (res.data.code == 200) {
          this.dlgData.memberCars = JSON.parse(JSON.stringify(res.data.data));
          this.$forceUpdate();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 绑定
    bindItem(data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgData = Object.assign(
        this.dlgData,
        JSON.parse(JSON.stringify(data))
      );
      this.dlgType = type;
      this.dlgShowBind = true;
      buildingmemberInfo(data.id).then(res => {
        if (res.data.code == 200) {
          this.dlgData.roomMembers = res.data.data.rooms;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 删除房屋
    delRoom(idx) {
      this.dlgData.rooms.splice(idx, 1);
      this.$forceUpdate();
    },

    // 解除绑定
    unbindRoom(data, idx) {
      this.$confirm("确认解除绑定?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        buildingmemberUnbundling(data.id, this.dlgData.id).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.dlgData.roomMembers.splice(idx, 1);
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },

    // 启用停用
    delItem(data) {
      let title = "确认删除?";
      this.$confirm(title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        buildingmemberOwner(data.id).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.getList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },

    // 上传对话框图片
    beforeUpload(file) {
      let _this = this;
      uploadImg(file, "jianyitong/web/ownerInfo_").then(res => {
        _this.dlgData["photo"] = res;
      });
      return false;
    },

    // 删除上传照片
    delUploadImg() {
      let _this = this;
      this.$confirm("是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        _this.dlgData["photo"] = "";
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
.el-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
