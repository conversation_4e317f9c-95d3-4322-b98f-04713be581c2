import request from '@/utils/request'

// 动态查询陪护床使用记录
export function findBedUseInfoDynamic(data) {
  return request({
    url: `/u/usapi/ph/findBedUseInfoDynamic`,
    method: 'post',
    data
  })
}

// 动态查询陪护床使用记录
export function udpateUseStatus(id, useStatus) {
  return request({
    url: `/u/usapi/ph/udpateUseStatus/${id}/${useStatus}`,
    method: 'get'
  })
}

// 代送被褥冒泡
export function daisongdamaopao(id) {
  return request({
    url: `/u/usapi/ph/daisongdamaopao/${id}`,
    method: 'get'
  })
}

// 查询被褥历史 /u/usapi/ph/findGxcUseInfoDynamic
export function findGxcUseInfoDynamic(data) {
  return request({
    url: `/u/usapi/ph/findGxcUseInfoDynamic`,
    method: 'post',
    data
  })
}