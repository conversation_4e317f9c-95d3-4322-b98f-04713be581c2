import request from '@/utils/request'
import { requestExcel } from '@/utils'

/*
*药品信息
*/

// 动态查询药品信息 分页
export function findDrugDynamic(data) 
{
	return request({
		url: `/follow/findDrugDynamic`,
		method: 'post',
		data
	})
}

// 删除药品信息
export function updateDrug(data)
{
	return request({
		url: `/follow/updateDrug`,
		method: 'post',
		data
	})
}

// 新增/修改药品信息接口
export function saveOrUDrug(data)
{
	return request({
		url: `/follow/saveOrUDrug`,
		method: 'post',
		data
	})
}

// 导入
export function importExcelDrug(data)
{
	return requestExcel('/follow/importExcelDrug', data)
}



