<template>
  <!-- 考勤管理 - 人脸审核 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="筛选状态：">
          <el-checkbox-group v-model="listQuery.statusList">
            <el-checkbox label="0,1,2">全部</el-checkbox>
            <el-checkbox label="0">未审核</el-checkbox>
            <el-checkbox label="1">审核通过</el-checkbox>
            <el-checkbox label="2">审核不通过</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入关键字' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>

        <el-table-column label="部门">
          <template slot-scope="scope">
            <span>{{ scope.row.bLabel }}</span>
          </template>
        </el-table-column>

        <el-table-column label="提交时间" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.input_time }}</span>
          </template>
        </el-table-column>

        <el-table-column label="录入原因">
          <template slot-scope="scope">
            <span>{{ scope.row.input_cause_type }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="200px" align="center">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.status == '审核通过'">{{ scope.row.status }}</el-tag>
            <el-tag type="warning" v-if="scope.row.status == '提交未审核'">{{ scope.row.status }}</el-tag>
            <el-tag type="danger" v-if="scope.row.status == '审核不通过'">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="140" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-if="['审核通过', '审核不通过'].includes(scope.row.status)" type="success" size="mini" icon="el-icon-view" plain @click="viewItem(scope.row)">查看</el-button>
            <el-button v-else type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">处理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="'人脸审核'" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :model="dlgData">
        <el-row>
          <el-col :span="8">
            <el-form-item label="姓名">
              {{dlgData.label}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门">
              {{dlgData.bLabel}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="录入原因">
              {{dlgData.input_cause_type}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          {{dlgData.remark}}
        </el-form-item>
        <el-row>
          <el-col :span="12" class="image-wrapper">
            <el-form-item label="身份证头像面">
              <el-image :src="dlgData.id_front_img" @click="onPreview(dlgData.id_front_img)">
                <div class="el-image__error" slot="error">
                  暂无身份证头像面
                </div>
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="12" class="image-wrapper">
            <el-form-item label="本次申请照片">
              <el-image v-if="dlgData.face_img_url" :src="dlgData.face_img_url" @click="onPreview(dlgData.face_img_url)">
              </el-image>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <template v-if="dlgType == 'EDIT'">
          <el-button type='danger' :loading='dlgLoading' @click="subDlg(2)" icon="el-icon-close">
            审核不通过
          </el-button>
          <el-button type='success' :loading='dlgLoading' @click="subDlg(1)" icon="el-icon-check">
            审核通过
          </el-button>
        </template>
      </div>
    </el-dialog>
    <el-image-viewer v-show="showViewer" :on-close="closeViewer" ref="imageViewer" :url-list="urlList" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { faceAuditPage, updateFaceEntryStatus } from '@/api/punchMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { uploadImg } from '@/utils/uploadImg'

export default {
  components: {
    Pagination,
    ElImageViewer
  },
  data () {
    return {
      showViewer: false,
      urlList: [],

      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',  // ADD\EDIT

      // 弹窗数据
      dlgData: {},
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        statusList: []
      },
    }
  },
  computed: {

  },
  watch: {

  },

  created () {
  },

  mounted () {
    this.$nextTick(() => {
      $(this.$refs.imageViewer.$el).children('.el-image-viewer__mask')[0].addEventListener('click', () => {
        this.closeViewer()
      })
    })
  },


  methods: {

    onPreview (pic) {
      this.urlList = [pic]
      this.showViewer = true
    },

    // 关闭查看器
    closeViewer () {
      this.showViewer = false
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      this.listQuery.status = this.listQuery.statusList.join(",")
      faceAuditPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    viewItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgType = 'VIEW'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg (status) {
      this.$confirm('确认审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dlgLoading = true
        updateFaceEntryStatus(this.dlgData.id, status).then(res => {
          this.dlgLoading = false
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.dlgShow = false
            this.getList()
            utils.setBubble()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })

    },

    // 编辑
    editItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })

    },
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.image-wrapper {
  width: calc(50% - 10px);

  /deep/ .el-form-item {
    height: 360px;
  }

  /deep/ .el-form-item__label {
    float: none;
    display: block;
    text-align: center;
    width: 100%;
  }

  /deep/ .el-form-item__content {
    height: calc(100% - 30px);
    .el-image {
      width: 100%;
      height: 100%;
    }
  }
}

.image-wrapper:last-child {
  float: right;
}

.el-image-viewer__wrapper {
  z-index: 99999 !important;
}
</style>


