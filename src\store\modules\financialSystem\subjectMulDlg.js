// 多选科目dlg组件

const subjectMulDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    subjectIds: '',

    subjectNames: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    subjectIds: state => state.subjectIds,

    subjectNames: state => state.subjectNames
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_SUBJECTIDS: (state, val) => {
      state.subjectIds = val
    },

    SET_SUBJECTNAMES: (state, val) => {
      state.subjectNames = val
    }
  },

  actions: {

  }
}

export default subjectMulDlg
