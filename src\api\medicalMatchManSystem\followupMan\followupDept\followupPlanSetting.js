import request from '@/utils/request'

/*
 *随访计划
 */

// 分页查询 
export function findFollowDynamic(data) {
  return request({
    url: `/follow/findFollowDynamic`,
    method: 'post',
    data
  })
}

// 删除
export function updateFollowIsDel(data) {
  return request({
    url: `/follow/updateFollowIsDel`,
    method: 'post',
    data
  })
}

// 新增/修改调查问卷接口
export function saveOrUFollow(data) {
  return request({
    url: `/follow/saveOrUFollow`,
    method: 'post',
    data
  })
}

// 修改随访取消状态
export function updateFollowIsCancel(data) {
  return request({
    url: `/follow/updateFollowIsCancel`,
    method: 'post',
    data
  })
}

// 执行随访
export function updatePerformFollow(data) {
  return request({
    url: `/follow/updatePerformFollow`,
    method: 'post',
    data
  })
}

// 查询患者体征数据
export function findUserSigns(data) {
  return request({
    url: `/follow/findUserSigns`,
    method: 'post',
    data
  })
}

// 关联查询就诊信息，患者信息，标签信息
export function findSickUserAndTagInfo(data) {
  return request({
    url: `/follow/findSickUserAndTagInfo`,
    method: 'post',
    data
  })
}

// 查询标签类别包含标签属性
export function findTagTypeAndTag(data) {
  return request({
    url: `/follow/findTagTypeAndTag`,
    method: 'post',
    data
  })
}

// 修改用户标签信息
export function updateUserTag(data) {
  return request({
    url: `/follow/updateUserTag`,
    method: 'post',
    data
  })
}

// 修改复诊时间段
export function updateVisitDate(data) {
  return request({
    url: `/follow/updateVisitDate`,
    method: 'post',
    data
  })
}

// 查询复诊修改记录
export function findVisitLogByFollowId(data) {
  return request({
    url: `/follow/findVisitLogByFollowId`,
    method: 'post',
    data
  })
}
