<template>
  <!-- 考勤管理 -->
  <div class="app-container" ref="schedulingMan">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="日期">
          <el-date-picker v-model="listQuery.date" value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="date" placeholder="日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-radio-group @change="getList" v-model="listQuery.branchType">
            <el-radio label="0">当前部门</el-radio>
            <el-radio label="1">当前及所属部门</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.branchName" @focus="showBranchDlg" :title='listQuery.branchName' placeholder="选择部门" readonly>
            <i @click='resetSearchItem(["branchId", "branchName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.label" placeholder='请输入姓名'>
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="姓名" width="100px">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属部门" width="200px" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="本岗位">
          <template slot-scope="scope">
            <span>{{ scope.row.groupName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="日期" width="150px">
          <template slot-scope="scope">
            <span>{{ scope.row.date }}</span>
          </template>
        </el-table-column>

        <el-table-column label="排班" :show-overflow-tooltip="true">
          <template slot-scope="scope" v-if="scope.row.schedulingGroupName">
            <div v-for="item in scope.row.schedulingGroupName.split('@#')" :key="item">{{ item }}</div>
          </template>
        </el-table-column>

        <el-table-column label="出勤情况" width="200px">
          <template slot-scope="scope">
            <div>上班：{{ scope.row.goWorkPunchTime }}</div>
            <div>下班：{{ scope.row.offWorkPunchTime }}</div>
            <div class="font-bold workHour">工时：{{ scope.row.workHour }}</div>
          </template>
        </el-table-column>

        <el-table-column label="计算结果" width="150px" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.schedulingResultType.toString().split(',')[index] == 1 ? 'danger' : 'success' " v-for="(item, index) in scope.row.schedulingResult.split(',')" :key="index">{{ item }}</el-tag>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <DialogBranch @superFunc="superBranch" :superDlgShow.sync="dlgShowBranch" :superSelectId="listQuery.branchId" :superSelectName="listQuery.branchName" :superPermission="true" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { attendanceDaily } from '@/api/attendanceMan/report.js'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import DialogBranch from '@/components/Dialog/platformMan/DialogBranch'

export default {
  components: {
    Pagination,
    DialogBranch
  },
  data () {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        date: utils.getToday(),
        branchId: '',
        branchName: '',
        branchType: '0'
      },
      count: 0,

      dlgShowBranch: false
    }
  },
  computed: {

  },
  watch: {

  },

  created () {

  },

  methods: {

    // 显示部门树
    showBranchDlg () {
      this.dlgShowBranch = true
    },

    superBranch (params) {
      this.listQuery.branchId = params.selectId
      this.listQuery.branchName = params.selectName
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 根据类型不同重建数组
    formatList () {

    },


    // 获取数据
    getList () {
      if (utils.isNull(this.listQuery.date)) {
        this.$message.warning("请选择日期")
        return
      }
      this.count++
      this.listLoading = true
      attendanceDaily(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          let list = res.data.data ? JSON.parse(JSON.stringify(res.data.data)) : []
          this.formatList(list)
          this.list = list
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.workHour {
  position: absolute;
  right: 20px;
  top: 50%;
  height: 40px;
  line-height: 40px;
  margin-top: -20px;
}
</style>


