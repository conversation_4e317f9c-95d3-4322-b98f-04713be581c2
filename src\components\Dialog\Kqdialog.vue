<template>
  <!-- 弹窗 考勤组 -->
  <el-dialog :close-on-click-modal='false' title="选择考勤组" :visible.sync="kqState" width='800px' top='30px' append-to-body>
    <div class="dialog-hdd-bar">
      <!-- 搜索 -->
      <div class="filter-container">
        <el-form ref="searchForm" class='n-search' :model="listQuery" label-width="90px" @submit.native.prevent>
          <div class="n-search-bar">
            <div class='n-search-item fl'>
              <el-form-item label="关键字：">
                <el-input @keyup.enter.native='searchFunc' class='m-shaixuan-input' placeholder='考勤组名称' v-model="listQuery.label"></el-input>
              </el-form-item>
            </div>
            <div class='n-search-item n-search-item-r fr'>
              <el-select @change='searchFunc' clearable v-model="listQuery.typeId" placeholder="选择考勤分类" style="width: 160px;">
                <el-option v-for="item of kqflSelect" :key="item.id" :label="item.groupType" :value="item.id">
                </el-option>
              </el-select>
              <el-button icon='el-icon-search' type="success" size='mini' class="search-right-btn" @click='searchFunc'>搜索</el-button>
            </div>
            <div class="clear"></div>
          </div>

        </el-form>
      </div>
      <div class="table-container">
        <el-table class='m-small-table' height="100%" v-loading="listLoading" :key="tableKey" :data="list" border fit highlight-current-row @row-click='rowClick'>
          <el-table-column label="" align="center" width="50">
            <template slot-scope="scope">
              <el-radio v-model="selectedId" :label="scope.row.id" style="width: 16px;"><span></span></el-radio>
            </template>
          </el-table-column>
          <el-table-column label="序号" prop="index" align="center" width="50">
            <template slot-scope="scope">
              <span>{{ scope.row.index }}</span>
            </template>
          </el-table-column>

          <el-table-column label="考勤组名称" prop="groupName" sortable='custom'>
            <template slot-scope="scope">
              <span>{{ scope.row.groupName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" prop="ts" sortable='custom' width="200">
            <template slot-scope="scope">
              <span>{{ scope.row.ts }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-container">
        <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
      <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import { findGroupTypeByDynamic, findGroupByDynamic } from '@/api/attendanceGroupMan'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination

let listQueryEmpty = {
  page: 1,
  size: 10,
  label: '',
  typeId: '',
  sort: '-,ts'
}

export default {
  components: {
    Pagination
  },
  // components: { adminDashboard, editorDashboard },
  data () {
    return {
      isShow: false,

      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      selectedId: '',

      kqflSelect: []  // 考勤分类

    }
  },
  computed: {
    ...mapGetters([
      'role'
    ]),
    kqState: {
      get: function () {
        let state = this.$store.getters.kqState
        if (state && !this.isShow) {
          this.listQuery.label = ''
          this.listQuery.typeId = ''


          this.getList()
          this.isShow = true
        }

        return this.$store.getters.kqState
      },
      set: function (newVal) {
        this.$store.commit('SET_KQSTATE', newVal)
      }
    },
    kqId: {
      get: function () {
        return this.$store.getters.kqId
      },
      set: function (newVal) {

      }
    },
    kqName: {
      get: function () {
        return this.$store.getters.kqName
      },
      set: function (newVal) {

      }
    }
  },
  watch: {
  },
  created () {

  },
  methods: {
    // 表格行 点击事件
    rowClick (row, column, event) {
      this.selectedId = row.id
    },
    // 搜索事件
    searchFunc () {
      this.listQuery.page = 1
      this.getList()
    },
    // 获取数据
    getList () {
      this.selectedId = ''
      this.listLoading = true
      if (this.listQuery.typeId === null) {
        this.listQuery.typeId = ''
      }

      findGroupByDynamic(this.listQuery).then(res => {
        let code = res.data.code
        let data = res.data.data
        let list = res.data.list
        let msg = res.data.msg

        this.total = data.total

        if (list) {
          for (let i = 0; i < list.length; i++) {
            let item = list[i]
            item.index = (this.listQuery.page - 1) * this.listQuery.size + i + 1
          }
        } else {
          list = []
        }

        this.list = JSON.parse(JSON.stringify(list))
        this.listLoading = false
      })
    },

    // 获取考勤组列表
    getKqflSelect () {
      let sendObj = {
        page: 1,
        size: 500,
        label: '',
        sort: '',
      }
      findGroupTypeByDynamic(sendObj).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code === '200') {
          this.kqflSelect = res.data.list
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })
    },

    // 选择部门提交
    bumenOkFunc () {
      if (this.selectedId === '') {
        this.$message({
          type: 'warning',
          message: '请选择考勤组'
        })
      } else {
        let selectedObj = this.list.filter(item => {
          return item.id === this.selectedId
        })
        // this.kqId = this.selectedId
        // this.kqName = selectedObj[0].groupName

        this.$store.commit('SET_KQID', '')
        this.$store.commit('SET_KQNAME', '')

        setTimeout(() => {
          this.$store.commit('SET_KQID', this.selectedId)
          this.$store.commit('SET_KQNAME', selectedObj[0].groupName)
          this.closeDialog()
        }, 50)
      }
    },

    // 关闭弹窗 
    closeDialog () {
      this.kqState = false
      this.isShow = false
      this.$store.commit('SET_KQSTATE', false)
    },

  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
</style>