<template>
  <el-dialog
    class="mazhenguo"
    title="选择供应商"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="800px"
    top="30px"
  >
    <div class="clearfix">
      <el-input @keyup.enter.native="searchFunc" class="fl" placeholder="关键字" v-model="listQuery.label" style="width: 200px">
        <i @click="resetSearchItem(['label'])" slot="suffix" class="el-input__icon el-icon-error"></i>
      </el-input>

      <el-button icon="el-icon-search" type="primary" class="fl ml10" @click="getList" size="mini" style="padding: 7px 10px"
        >搜索</el-button
      >
    </div>

    <el-table
      height="400"
      ref="tableRef"
      class="m-small-table mt10"
      v-loading="listLoading"
      :key="tableKey"
      :data="list"
      border
      fit
      highlight-current-row
      @row-click="tableRowClick"
    >
      <el-table-column label="" align="center" width="60">
        <template slot-scope="scope">
          <el-radio v-model="selectData.id" :label="scope.row.id" style="width: 16px"><span></span></el-radio>
        </template>
      </el-table-column>

      <el-table-column label="名称">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="地址">
        <template slot-scope="scope">
          <span>{{ scope.row.address }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系人" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.contacts }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系方式" width="120px">
        <template slot-scope="scope">
          <span>{{ scope.row.phone }}</span>
        </template>
      </el-table-column>

      <!-- <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button @click="showDlg('info', scope.row)" icon="el-icon-document" size="mini" type="primary" title="详情" plain
            >详情</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>

    <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limt" @pagination="getList" />

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button v-if="dlgType != 'info'" :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">提交中...</span>
        <span v-else>确定</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
// 工具
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
import Pagination from '@/components/Pagination'
// 接口
import * as utils from '@/utils'
import * as regUtils from '@/utils/regUtils'

import { postAction, getAction } from '@/api'

let listQueryEmpty = {
  label: '', //	模糊查询	body	false	string
  page: 1,
  limit: 20,

  projectId: '', //		body	false	string
}

export default {
  components: {
    Pagination,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
    selectList0: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))

          if (this.dlgData0) {
            this.selectData = { ...this.dlgData0 }
          } else {
            this.selectData = { id: '' }
          }
          this.getList()
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),

      selectData: { id: '' },

      tableKey: 0,
      list: [],
      selectList: [], // 选中
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      dlgSubLoading: false,

      dlgState: false,
    }
  },
  created() {
    // this.getDataDict()
  },
  methods: {
    dlgSubFunc() {
      if (utils.isNull(this.selectData) || utils.isNull(this.selectData.id)) {
        this.$emit('backFunc', '')
      } else {
        this.$emit('backFunc', this.selectData)
      }
      this.closeDlg()
    },
    closeDlg() {
      this.$emit('closeDlg')
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.searchFunc()
    },
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.list = []

      let sendObj = JSON.parse(JSON.stringify(this.listQuery))

      sendObj.projectId = this.userInfo.projectId
      this.listLoading = true
      postAction('/schain/supplier/page', sendObj).then((res0) => {
        let res = res0.data
        this.listLoading = false
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = []
            this.total = 0
          } else {
            this.list = res.data || []
            this.total = res.page.total

            this.$nextTick(() => {
              this.$refs.tableRef.doLayout()
            })

            let list = this.list
            this.$nextTick(() => {
              console.log('this.selectList', this.selectList)
              if (this.selectList.length > 0) {
                for (let item of list) {
                  let isHas = this.selectList.some((row) => row.id == item.id)
                  if (isHas) {
                    this.$refs.tableRef.toggleRowSelection(item, true)
                  } else {
                    this.$refs.tableRef.toggleRowSelection(item, false)
                  }
                }
              } else {
                this.$refs.tableRef.clearSelection()
              }
            })
          }
        } else {
          this.total = 0
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },

    tableRowClick(row, column, event) {
      this.selectData = row
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>