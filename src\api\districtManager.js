import request from '@/utils/request'

/*
* 区域管理
*/

// 查询区域列表 
export function findDistrictLike(data) {
	return request({
		url: `/sys/findDistrictLike`,
		method: 'post',
		data
	})
}

// 新增/修改区域
export function saveOrUDistrict(data) {
	return request({
		url: `/sys/saveOrUDistrict`,
		method: 'post',
		data
	})
}

// 删除区域
export function delDistrict(data) {
	return request({
		url: `/sys/delDistrict`,
		method: 'post',
		data
	})
}

// 根据区域id查询关联信息
export function findDistrictRelevanceById(data) {
	return request({
		url: `/sys/findDistrictRelevanceById`,
		method: 'post',
		data
	})
}







