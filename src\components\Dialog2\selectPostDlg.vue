<template>
  <el-dialog
    class="mazhenguo"
    title="选择岗位"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="800px"
    top="30px"
  >
    <div class="clearfix">
      <el-input
        v-if="listQuery.branchName"
        class="fl"
        style="width: 200px; margin-right: 10px"
        v-model="listQuery.branchName"
        placeholder="请选择部门"
        disabled
        :title="listQuery.branchName"
      >
      </el-input>
      <el-input
        @keyup.enter.native="searchFunc"
        class="fl "
        placeholder="关键字"
        v-model="listQuery.label"
        style="width: 200px"
      >
        <i
          @click="resetSearchItem(['label'])"
          slot="suffix"
          class="el-input__icon el-icon-error"
        ></i>
      </el-input>

      <el-button
        icon="el-icon-search"
        type="primary"
        class="fl ml10"
        @click="getList"
        size="mini"
        style="padding: 7px 10px"
        >搜索</el-button
      >
    </div>

    <el-table
      height="400"
      ref="tableRef"
      class="m-small-table mt10"
      v-loading="listLoading"
      :key="tableKey"
      :data="list"
      border
      fit
      highlight-current-row
      @row-click="tableRowClick"
    >
      <!-- 单选 -->
      <el-table-column label="" align="center" width="60">
        <template slot-scope="scope">
          <el-radio
            v-model="selectData.id"
            :label="scope.row.id"
            style="width: 16px"
            ><span></span
          ></el-radio>
        </template>
      </el-table-column>

      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column label="岗位名称" prop="label"></el-table-column>
      <el-table-column label="部门" prop="branchName"></el-table-column>
    </el-table>

    <pagination
      class="mt10"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="getList"
    />

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button
        v-if="dlgType != 'info'"
        :loading="dlgSubLoading"
        type="success"
        @click="dlgSubFunc"
        icon="el-icon-check"
      >
        <span v-if="dlgSubLoading">提交中...</span>
        <span v-else>确定</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
// 工具
import { uploadImg, uploadImg2 } from "@/utils/uploadImg";
import Pagination from "@/components/Pagination";
// 接口
import * as utils from "@/utils";
import * as regUtils from "@/utils/regUtils";

import selectBmDlg from "@/components/Dialog2/selectBmDlg";

import { postAction, getAction } from "@/api";

let listQueryEmpty = {
  label: "", //	模糊查询	body	false	string
  page: 1,
  size: 20
};

export default {
  components: {
    Pagination
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {
      type: Object,
      default: {}
    },
    dlgState0: {
      type: Boolean,
      default: false
    },
    dlgData0: {},
    selectList0: {}
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val;
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          this.listQuery = { ...listQueryEmpty, ...this.dlgQuery };

          if (this.dlgData0) {
            this.selectData = { ...this.dlgData0 };
          } else {
            this.selectData = { id: "" };
          }
          this.getList();
        }, 50);
      } else {
        this.$emit("closeDlg");
      }
    }
  },
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),

      selectData: { id: "" },

      tableKey: 0,
      list: [],
      selectList: [], // 选中
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      dlgSubLoading: false,

      // 弹窗
      dlgState: false
    };
  },
  created() {
    // this.getDataDict()
  },
  methods: {
    // 弹窗提交 ------
    dlgSubFunc() {
      if (utils.isNull(this.selectData) || utils.isNull(this.selectData.id)) {
        this.$emit("backFunc", "");
      } else {
        this.$emit("backFunc", this.selectData);
      }

      this.closeDlg();
    },
    closeDlg() {
      this.$emit("closeDlg");
    },

    // ------ 列表
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },
    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    getList() {
      this.list = [];

      let sendObj = { ...this.listQuery, ...this.dlgQuery };
      sendObj.label = sendObj.label.trim();

      this.listLoading = true;
      postAction("/sys/pagePostUser", sendObj).then(res0 => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = [];
            this.total = 0;
          } else {
            let list0 = res.data || [];
            this.total = res.page.total;

            for (let item of list0) {
              item.id = item.postId;
            }

            this.list = JSON.parse(JSON.stringify(list0));

            this.$nextTick(() => {
              this.$refs.tableRef.doLayout();
            });
          }
        } else {
          this.total = 0;
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    // 点击行
    tableRowClick(row, column, event) {
      this.selectData = row;
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
