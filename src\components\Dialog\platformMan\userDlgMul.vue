<template>
  <el-dialog :close-on-click-modal='false' :title="'选择人员'" :visible.sync="dlgShow" append-to-body>
    <div class="filter-container">
      <el-form inline>
        <el-form-item label="关键字">
          <el-input v-model="listQuery.label" placeholder='请输入员工姓名'>
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item label="职务">
          <el-select v-model="listQuery.postJob" filterable clearable placeholder="请选择职务">
            <el-option v-for="item in postJobList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.departmentName" @focus="showBranchDlg" :title='listQuery.departmentName' placeholder="选择部门" readonly>
            <el-select @change="getList" style="width: 130px" v-model="listQuery.branchType" slot="prepend" placeholder="请选择">
              <el-option label="当前部门" value="0"></el-option>
              <el-option label="当前及所属部门" value="1"></el-option>
            </el-select>
            <i @click='resetSearchItem(["departmentId", "departmentName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>

        <el-button icon='el-icon-search' type="success" size='mini' @click="getList">
          搜索
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table max-height="347px" class='m-small-table' ref="multipleTable" :data="list" :row-key="getRowKeys" @selection-change="selectionChange" border fit highlight-current-row>
        <el-table-column label="#" align="center" type="selection" :reserve-selection='true' :selectable="selectable" width="50">
        </el-table-column>
        <el-table-column label="员工姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属部门">
          <template slot-scope="scope">
            <span>{{ scope.row.departmentName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考勤组">
          <template slot-scope="scope">
            <span>{{ scope.row.groupName }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check">
        确 定
      </el-button>
    </div>
    <DialogBranch @superFunc="superBranch" :superDlgShow.sync="dlgShowBranch" :superSelectId="listQuery.departmentId" :superSelectName="listQuery.departmentName" :superPermission="true" />
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import DialogBranch from '@/components/Dialog/platformMan/DialogBranch'
import { findUserByLabelAndNumByGroup } from '@/api/staffMan'

export default {
  components: {
    Pagination,
    DialogBranch,
  },
  data () {
    return {
      list: [],

      listQuery: {
        size: 10,
        label: "",
        page: 1,
        departmentId: '',
        departmentName: '',
        branchType: '0',
        postJob: '',
      },

      total: 0,

      selectUserList: [],

      selectList: [],

      selectIdList: [],

      postJobList: [],

      dlgShowBranch: false
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.platformMan.userDlgMul.dlgShow
      },
      set: function (val) {
        this.$store.commit('platformMan/userDlgMul/SET_DLGSHOW', val)
      }
    },

    userList: {
      get: function () {
        return this.$store.state.platformMan.userDlgMul.list
      },
      set: function (val) {
        this.$store.commit('platformMan/userDlgMul/SET_LIST', val)
      }
    },

  },

  created () {
    utils.getDataDict(this, 'postJob', 'postJobList')
  },

  watch: {

    dlgShow (val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.multipleTable.clearSelection();
          this.listQuery.label = ""
          this.getList()
        })
      }
    },

    userList (val) {
      this.selectUserList = JSON.parse(JSON.stringify(val))
    },

  },

  methods: {
    // 显示部门树
    showBranchDlg () {
      this.dlgShowBranch = true
    },

    superBranch (params) {
      this.listQuery.departmentId = params.selectId
      this.listQuery.departmentName = params.selectName
    },

    selectable (row, index) {
      return !this.selectIdList.includes(row.id)

    },

    getRowKeys (row) {
      return row.id
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    selectionChange (val) {
      this.selectList = JSON.parse(JSON.stringify(val));
    },

    // 设置选中行
    toggleRowSelection () {
      let idList = []
      for (let i of this.selectUserList) {
        if (!idList.includes(i['id'])) {
          idList.push(i['id'])
        }
      }
      this.selectIdList = idList
      console.log(idList)
      if (idList.length == 0) {
        this.$refs.multipleTable.clearSelection();
      } else {
        this.$nextTick(() => {
          this.list.forEach(item => {
            for (let i in idList) {
              if (idList[i] == item.id) {
                this.$refs.multipleTable.toggleRowSelection(item, true);
              }
            }
          });
        })
      }
    },

    getList () {
      this.list = []
      findUserByLabelAndNumByGroup(this.listQuery).then(res => {
        if (res.data.code == 200) {
          this.list = res.data ? res.data.data : []
          this.total = res.data.page ? res.data.page.total : 0
          this.toggleRowSelection()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    subDlg () {
      this.$store.commit('platformMan/userDlgMul/SET_LIST', this.selectList)
      this.closeDlg()
    },

    closeDlg () {
      this.$store.commit('platformMan/userDlgMul/SET_DLGSHOW', false)
    }


  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-table td {
  border-bottom: 1px solid #ebeef5 !important;
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>