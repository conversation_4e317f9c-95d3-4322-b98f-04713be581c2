<template>
  <!--陪诊陪检参数设置-->
  <div class="tab-panel">
    <el-form label-width="120px">
      <el-form-item label="是否有偿：">
        <el-radio-group v-model="pzIsPay">
          <el-radio :label="'1'">有偿</el-radio>
          <el-radio :label="'0'">无偿</el-radio>
        </el-radio-group>
        <el-button type="success" @click="saveCompensation()">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'
import { findPzIsPay, saveOrUPzIsPay } from "@/api/platformManSystem/systemConfig/accompanyMan"

export default {
  components: {

  },

  data() {
    return {
      pzIsPayId: "0",
      pzIsPay: "1"
    }
  },
  created() {
    this.getList()
  },
  methods: {
    insertPzIsPay() {
      let post_param = {
        isPay: this.pzIsPay,
        id: this.pzIsPayId
      }
      saveOrUPzIsPay(post_param).then(res => {
        let code = res.data.code;
        let msg = res.data.msg;
        if (code == "200") {
          this.pzIsPayId = res.data.id.toString()
          this.pzIsPay = res.data.isPay.toString()
        }
        else {
          this.$message.error(msg);
        }
      })
    },
    getList() {
      findPzIsPay({}).then(res => {
        let code = res.data.code;
        let msg = res.data.msg;
        if (code === "200") {
          let data = res.data.data;
          if (data === null) 	//直接新增
          {
            this.insertPzIsPay()
          }
          else {
            this.pzIsPayId = data.id.toString()
            this.pzIsPay = data.isPay.toString()
          }
        }
        else {
          this.$message.error(msg);
        }
      })
    },
    saveCompensation() {
      let post_param = {
        isPay: this.pzIsPay,
        id: this.pzIsPayId
      }
      saveOrUPzIsPay(post_param).then(res => {
        let code = res.data.code;
        let msg = res.data.msg;
        if (code == "200") {
          this.$message.success(msg);
        }
        else {
          this.$message.error(msg);
        }
      })
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>