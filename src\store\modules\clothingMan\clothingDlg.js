// 被服dlg组件

const clothingDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    clothingId: '',

    clothingName: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    clothingId: state => state.clothingId,

    clothingName: state => state.clothingName
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_CLOTHINGID: (state, val) => {
      state.clothingId = val
    },

    SET_CLOTHINGNAME: (state, val) => {
      state.clothingName = val
    }
  },

  actions: {

  }
}

export default clothingDlg
