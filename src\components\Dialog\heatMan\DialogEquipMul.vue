<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="'选择设备'"
    :visible.sync="dlgShow"
    @close="closeDlg"
    width="1200px"
    top="30px"
    append-to-body
  >
    <div class="filter-container">
      <el-form ref="dlgForm" inline :model="listQuery" @submit.native.prevent>
        <el-form-item>
          <el-input
            @keyup.enter.native="searchFunc"
            :placeholder="'设备名称'"
            v-model="listQuery.label"
          >
            <i
              @click="clearQuery(['label'])"
              slot="suffix"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchFunc"
          >查询</el-button
        >
        <el-popover placement="bottom-end" width="800" trigger="click">
          <el-table max-height="400" :data="selectList">
            <el-table-column label="设备名称">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="设备imei号码">
              <template slot-scope="scope">
                <span>{{ scope.row.imei }}</span>
              </template>
            </el-table-column>

            <el-table-column label="所属楼宇">
              <template slot-scope="scope">
                <span>{{ scope.row.buildingName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="所属环路">
              <template slot-scope="scope">
                <span>{{ scope.row.loopName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="所属点位">
              <template slot-scope="scope">
                <span>{{ scope.row.pointName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="上报间隔">
              <template slot-scope="scope">
                <span>{{ scope.row.reportInterval || "未设置" }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="60" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="delItem(scope.$index)"
                  type="danger"
                  size="mini"
                  icon="el-icon-delete"
                  plain
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button type="primary" slot="reference" icon="el-icon-arrow-down"
            >查看已选</el-button
          >
        </el-popover>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        height="100%"
        :data="list"
        :row-key="getRowKeys"
        @selection-change="selectionChange"
        border
        fit
        highlight-current-row
      >
        <el-table-column
          label="选中"
          align="center"
          type="selection"
          :reserve-selection="true"
          width="60"
        >
        </el-table-column>
        <el-table-column label="设备名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="设备imei号码">
          <template slot-scope="scope">
            <span>{{ scope.row.imei }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属楼宇">
          <template slot-scope="scope">
            <span>{{ scope.row.buildingName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属环路">
          <template slot-scope="scope">
            <span>{{ scope.row.loopName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属点位">
          <template slot-scope="scope">
            <span>{{ scope.row.pointName }}</span>
          </template>
        </el-table-column>
         <el-table-column label="上报间隔">
              <template slot-scope="scope">
                <span>{{ scope.row.reportInterval || "未设置" }}</span>
              </template>
            </el-table-column>
      </el-table>
    </div>
    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back"> 取消 </el-button>
      <el-button type="success" icon="el-icon-check" @click="submitDlg">
        保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import Cookie from "js-cookie";

import Pagination from "@/components/Pagination";
import * as utils from "@/utils";
import { page } from "@/api/heatMan/equipMan";

export default {
  components: {
    Pagination,
  },

  props: {
    superDlgShow: {
      type: Boolean,
      required: true,
      default: false,
    },

    superSelectList: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
  },

  data() {
    return {
      dlgShow: this.superDlgShow,

      list: [],

      listQuery: {
        limit: 20,
        label: "",
        page: 1,
      },

      total: 0,

      selectList: [],
    };
  },

  computed: {},

  watch: {
    superDlgShow: {
      immediate: true,
      handler(val) {
        this.dlgShow = val;
        if (val) {
          this.$nextTick(() => {
            this.listQuery.label = "";
            this.selectList = JSON.parse(JSON.stringify(this.superSelectList));
            this.getList();
          });
        }
      },
    },
  },

  methods: {
    clearQuery(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },

    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },

    // 删除项
    delItem(idx) {
      this.selectList.splice(idx, 1);
      this.toggleRowSelection();
    },

    // 获取表格数据
    getList() {
      let userInfo = JSON.parse(window.localStorage.userInfo);
      this.listQuery.projectId = userInfo.projectId;
      page(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.data.records;
          this.total = res.data.data.total;
          this.toggleRowSelection();
        }
      });
    },

    getRowKeys(row) {
      return row.id;
    },

    selectionChange(val) {
      this.selectList = val;
    },

    // 设置选中行
    toggleRowSelection() {
      let idListSel = this.selectList.map((item) => item.id);
      if (idListSel.length == 0) {
        this.$refs.multipleTable.clearSelection();
      } else {
        this.$nextTick(() => {
          this.list.forEach((item) => {
            this.$refs.multipleTable.toggleRowSelection(
              item,
              idListSel.includes(item.id)
            );
          });
        });
      }
    },

    submitDlg() {
      if (this.selectList.length == 0) {
        this.$message.warning("请选择设备");
        return;
      }
      let superParam = {
        selectList: JSON.parse(JSON.stringify(this.selectList)),
      };
      this.$emit("superFunc", superParam);
      this.closeDlg();
    },

    closeDlg() {
      this.$emit("update:superDlgShow", false);
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 800px;

  .el-dialog__body {
    height: calc(100% - 110px);

    .el-tree {
      margin-top: 10px;
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }

  .role-tip {
    display: inline-block;
    background: #409eff;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  th.DisableSelection {
    div.cell {
      visibility: hidden;
    }
  }
}
</style>