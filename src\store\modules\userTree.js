
const userTree = {
  state: {
    userTreeState: false,
    userTreeBranchId: '',
    userTreeBranchName: '',
    userTreeUserId: '',
    userTreeUserName: '',

    userTreeSqType: ''
  },

  mutations: {
    SET_USERTREESTATE: (state, userTreeState) => {
      state.userTreeState = userTreeState
    },
    SET_USERTREEBRANCHID: (state, userTreeBranchId) => {
      state.userTreeBranchId = userTreeBranchId
    },
    SET_USERTREEBRANCHNAME: (state, userTreeBranchName) => {
      state.userTreeBranchName = userTreeBranchName
    },
    SET_USERTREEUSERID: (state, userTreeUserId) => {
      state.userTreeUserId = userTreeUserId
    },
    SET_USERTREEUSERNAME: (state, userTreeUserName) => {
      state.userTreeUserName = userTreeUserName
    },
    SET_USERTREESQTYPE: (state, val) => {
      state.userTreeSqType = val
    }
  },

  actions: {
    
  }
}

export default userTree
