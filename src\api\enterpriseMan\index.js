import request from "@/utils/request";
const Qs = require("qs");
// 企业分页
export function syscompanyPage(data) {
  return request({
    url: `/sys/syscompany/page`,
    method: "post",
    data
  });
}

// 新增修改企业
export function syscompanyAddOrUpdate(data) {
  return request({
    url: `/sys/syscompany/addOrUpdate`,
    method: "post",
    data
  });
}

//自助签约分页
export function communitySignPage(data) {
  return request({
    url: `/unity/communitySign/page`,
    method: "post",
    data
  });
}
//所属支行查询
export function branchBankList(data) {
  return request({
    url: `/unity/communitySign/branchBankList`,
    method: "post",
    data
  });
}
//获取行业信息
export function communitySignMcc(data) {
  return request({
    url: `/unity/communitySign/mcc`,
    method: "get",
    data
  });
}
//详细采集档案资料上传接口
export function complexUpload(data) {
  return request({
    url: `/unity/communitySign/complexUpload`,
    method: "post",
    data
  });
}
//签约接口
export function agreementSign(data) {
  return request({
    url: `/unity/communitySign/agreementSign/${data}`,
    method: "get",
    data
  });
}
//对公打款验证
export function requestAccountVerify(data) {
  return request({
    url: `/unity/communitySign/requestAccountVerify`,
    method: "post",
    data
  });
}
//对公账户认证
export function companyAccountVerify(data) {
  return request({
    url: `/unity/communitySign/companyAccountVerify`,
    method: "post",
    data
  });
}
//商户信息变更
export function complexAlterAcctinfo(data) {
  return request({
    url: `/unity/communitySign/complexAlterAcctinfo`,
    method: "post",
    data
  });
}
//商户信息变更记录
export function alterPage(data) {
  return request({
    url: `/unity/communitySign/alterPage`,
    method: "post",
    data
  });
}
//存草稿
export function complexUploadStaging(data) {
  return request({
    url: `/unity/communitySign/complexUploadStaging`,
    method: "post",
    data
  });
}
//上传图片
export function picUpload(data) {
  let formData = new FormData();
  for (let key in data) {
    let value = data[key];
    formData.append(key, value);
  }
  return request({
    url: `/unity/communitySign/picUpload`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data"
    },
    data: formData
  });
}
//获取省市区
export function threeLinkage(type, code) {
  return request({
    url: `/unity/community/threeLinkage/${type}/${code}`,
    method: "get"
  });
}
