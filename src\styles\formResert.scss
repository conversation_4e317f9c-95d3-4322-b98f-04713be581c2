// 表单 样式重置

.el-select {
  width: 100%;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #19aa8d;
  background: #19aa8d;
}

.el-radio__label {
  padding-left: 6px;
}

.el-dialog__body {
  padding: 10px 20px;
}

.el-button--mini,
.el-button--mini.is-round {
  padding: 4px 10px;
}

.el-tree-node:focus > .el-tree-node__content {
  background-color: #dcdfe6;
  // color: #fff;
}

.tree-on.el-tree-node__content {
  background: #dcdfe6;
  // color: #fff;
}

// 表单复选框复选框
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  border-color: #19aa8d;
  background: #19aa8d;
}

.el-checkbox__inner:hover,
.el-radio__inner:hover,
.el-textarea__inner:focus,
.el-input.is-active .el-input__inner,
.el-range-editor.is-active,
.el-range-editor.is-active:hover,
.el-select .el-input__inner:focus,
.el-select .el-input.is-focus .el-input__inner,
.el-pagination__sizes .el-input .el-input__inner:hover,
// .el-input-number__decrease:hover,
// .el-input-number__increase:hover,
.el-input__inner:focus {
  border-color: #19aa8d;
}

.el-checkbox__input.is-checked + .el-checkbox__label,
.el-select-dropdown__item.selected,
.el-upload-list__item.is-success .el-upload-list__item-name:focus,
.el-upload-list__item.is-success .el-upload-list__item-name:hover,
.el-input-number__decrease:hover,
.el-input-number__increase:hover,
.el-date-table td.available:hover {
  color: #19aa8d;
}

.el-radio__input.is-checked + .el-radio__label {
  border-color: #19aa8d;
  color: #19aa8d;
}

.el-date-table td.end-date span,
.el-date-table td.start-date span {
  background: #19aa8d;
}

// 弹窗 tab 切换
.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: 2px solid #19aa8d;
}
.el-menu--horizontal.blue > .el-menu-item.is-active {
  border-bottom: 2px solid #1989fa!important;
}

// 表单上传
// 弹窗上传图片
.dialog-upload-item {
  width: 146px;

  // margin: 0 2px;
  .dialog-upload-item-img {
    width: 100%;
  }

  p {
    text-align: center;
  }
}

// 【【 图片上传 样式
.avatar-uploader {
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 148px;
  height: 148px;

  img {
    width: 146px;
    height: 146px;
  }
}



.avatar-uploader .el-upload:hover {
  border-color: #19aa8d;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 146px;
  height: 146px;
  line-height: 146px;
  text-align: center;
}

// .avatar
.upload-bar {
  position: relative;
  float: left;
  margin-right: 10px;
  margin-bottom: 10px;
  width: 148px;
  height: 148px;
  border: 1px solid #dcdfe6;
  border-radius: 3px;

  .avatar {
    width: 146px;
    height: 146px;
    display: block;
    cursor: pointer;
  }

  i {
    position: absolute;
    cursor: pointer;
    right: -7px;
    top: -7px;
    font-size: 20px;
    color: #f56c6c;
  }
}

.avatar-canbig {
  width: 146px;
  height: 146px;
  border-radius: 3px;
  cursor: pointer;
}

// 】】 图片上传 样式

.m-item-box-group1 {
  margin-left: 50px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;

  .el-radio-group {
    margin-top: 3px;
  }
}

// 上传
// .el-upload-list__item:first-child {
//   margin-top: 0px;
// }

// 图片上传样式 新
.imageUploadBox {
  position: relative;
  width: 146px;
  height: 146px;
  overflow: hidden;
  border: 1px solid #d9d9d9;
}

.imageUploadBox:hover {
  border-color: #19aa8d;
}

.upload-img {
  cursor: pointer;
  width: 146px;
  height: 146px;
}

.imageUploadBox-icon-upload {
  position: absolute;
  right: 4px;
  top: 4px;
}

// 上传传感器
.drop-right .avatar-uploader .el-upload {
  border: 0;
  border-radius: 0px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 658px;
  height: 658px;

  img {
    width: 656px;
    height: 656px;
  }
}
.avatar-uploader-ghtp .el-upload {
  border: 0;
  border-radius: 0px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 23px;
}

.el-button--mini.is-circle {
  padding: 4px;
}


// mzg重置样式
.mzgform .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;

  img {
    width: 98px;
    height: 98px;
  }
}
.mzgform {
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 98px;
    height: 98px;
    line-height: 98px;
    text-align: center;
  }


  .upload-bar {
    position: relative;
    float: left;
    margin-right: 10px;
    margin-bottom: 10px;
    width: 100px;
    height: 100px;
    border: 1px solid #dcdfe6;
    border-radius: 3px;
  
    .avatar {
      width: 98px;
      height: 98px;
      display: block;
      cursor: pointer;
    }
  
    i {
      position: absolute;
      cursor: pointer;
      right: -7px;
      top: -7px;
      font-size: 20px;
      color: #f56c6c;
    }
  }
}