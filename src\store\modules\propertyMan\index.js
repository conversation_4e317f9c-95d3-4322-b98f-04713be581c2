import distDlg from "./distDlg.js";
import projectDlg from "./projectDlg.js";
import branchDlg from "./branchDlg.js";
import branchDlgMul from "./branchDlgMul.js";
import permissionDlgMul from "./permissionDlgMul.js";
import roleDlg from "./roleDlg.js";
import postDlgMul from "./postDlgMul.js";
import postDlg from "./postDlg.js";

const propertyMan = {
  namespaced: true,
  modules: {
    distDlg,
    projectDlg,
    branchDlg,
    branchDlgMul,
    permissionDlgMul,
    roleDlg,
    postDlgMul,
    postDlg
  }
};

export default propertyMan;
