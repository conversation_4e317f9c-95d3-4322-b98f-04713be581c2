import request from "@/utils/request";

// 获取权限部门树
export function findOrgBranchAll() {
  return request({
    url: `/sys/department/findTree`,
    method: "post"
  });
}

// 获取全部的部门树
export function findTreeByFrom() {
  return request({
    url: `/sys/department/findTreeByFrom`,
    method: "get"
  });
}

// 新增部门
export function saveOrgDepartment(data) {
  return request({
    url: `/sys/department/saveOrgDepartment`,
    method: "post",
    data
  });
}

// 删除部门
export function delOrgDepartment(data) {
  return request({
    url: `/sys/department/delOrgDepartment`,
    method: "post",
    data
  });
}

// 修改部门
export function upDateOrgDepartment(data) {
  return request({
    url: `/sys/department/upDateOrgDepartment`,
    method: "post",
    data
  });
}

// 部门移动
export function updateBranchByOrderNum(data) {
  return request({
    url: `/sys/department/updateBranchByOrderNum`,
    method: "post",
    data
  });
}