<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item v-if="list.length > 0">
          <span v-if="listQuery.payType == 1"> 房屋：{{ list[0]['roomName'] }} </span>
          <span v-if="listQuery.payType == 2"> 车位：{{ list[0]['parkingName'] }} </span>
          <span v-if="listQuery.payType == 3"> 车库：{{ list[0]['garageName'] }} </span>
        </el-form-item>
        <el-form-item class="fr">
          <el-button icon="el-icon-search" type="success" size="mini" @click="getList">查询</el-button>
          <el-button icon="el-icon-back" size="mini" @click="backItem">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-container">
    <!--   @selection-change="selectionChange" -->
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <!-- <el-table-column label="#" align="center" type="selection" width="50"> </el-table-column> -->

        <el-table-column label="费用类型">
          <template slot-scope="scope">
            <span>{{ scope.row.feeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="费用项目">
          <template slot-scope="scope">
            <span>{{ scope.row.configName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="费用标识">
          <template slot-scope="scope">
            <span>{{ scope.row.feeFlagName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计费开始时间">
          <template slot-scope="scope">
            <span>{{ scope.row.startDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计费结束时间">
          <template slot-scope="scope">
            <span>{{ scope.row.endDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="欠费金额">
          <template slot-scope="scope">
            <span>{{ scope.row.amount.toFixed(2) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">

            <el-button
              @click="addItem(scope.row)"
              icon="el-icon-money"
              size="mini"
              type="primary"
              title="缴费"
              plain
              >缴费</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- <div class="page-container">
      <el-form>
        <el-form-item class="fr" label="缴费金额：" label-width="120px">
          <span class="fdanger font24">{{ totalMoney.toFixed(2) }}元</span>
          <el-button class="ml10" type="primary" size="medium" @click="addItem" icon="el-icon-check">缴费</el-button>
        </el-form-item>
      </el-form>
    </div> -->

    <el-dialog :close-on-click-modal="false" title="收费确认" :visible.sync="dlgShow" width="600px" append-to-body>
      <el-form ref="dlgForm" :disabled="dlgType == 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="140px">
        <el-form-item :label="listQuery.payType == 1 ? '房屋：' : listQuery.payType == 2 ? '车位：' : '车库：'" v-if="list.length > 0">
          <span v-if="listQuery.payType == 1">
            {{ list[0]['roomName'] }}
          </span>
          <span v-if="listQuery.payType == 2">
            {{ list[0]['parkingName'] }}
          </span>
          <span v-if="listQuery.payType == 3">
            {{ list[0]['garageName'] }}
          </span>
        </el-form-item>
        <el-form-item label="金额：">
          <span>{{ totalMoney.toFixed(2) }}</span>
        </el-form-item>
        <!-- <el-form-item label="收费方式：" prop="offlinePayWay">
          <el-select v-model="dlgData.offlinePayWay" placeholder="收费方式">
            <el-option v-for="item in payWayList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="收费方式：" prop="offlinePayWayArr">
          <el-checkbox-group v-model="dlgData.offlinePayWayArr" @change="offlinePayWayArrChange">
            <el-checkbox v-for="item of payWayList" :key="item.id" :label="item.id" name="type">{{
              item.name
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item v-for="(item,index) of dlgData.offlinePayWayList" :label="item.label" :key="index">
          <el-input-number @change="itemMoneyChange" v-model="item.money" :precision="2" :step="0.01"  placeholder="金额"></el-input-number> 元
        </el-form-item>

        <el-form-item label="合计收费金额：">
          <div>
            <el-input v-model="dlgData.totalMoney" placeholder="合计金额" style="width: 100px" disabled /> 元
          </div>
          <!-- <div>
            合计缴费金额应在 {{ smBatchPaymentMoney }} 至 {{ bigBatchPaymentMoney }} 元之间
          </div> -->
        </el-form-item>


        <el-form-item label="备注：" prop="remark">
          <el-input :autosize="{ minRows: 4, maxRows: 6 }" v-model="dlgData.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type="success" :loading="dlgLoading" @click="subDlg" icon="el-icon-check">
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { getPayBillPage, payfeebillPayOweAmount } from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  billIds: '',
  communityId: '',
  payType: '',
  remark: '',
  roomId: '',
  roomName: '',
  offlinePayWay: '',

  offlinePayWayArr: [],
  offlinePayWayList:[],
  totalMoney: ''
}

export default {
  name: 'payCost',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data() {
    return {
      id: '',
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        offlinePayWayArr: [{ required: true, message: '必填字段', trigger: 'change' }],
        remark: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 99,
        label: '',
        isOver: '1',
        status: '0',
        roomId: '',
        parkingId: '',
        garageId: '',
        payType: '',
      },
      totalMoney: 0,
      discountList: [],
      userInfo: {},
      costTypeList: [],
      selectList: [],
      feeFlagList: [
        {
          id: '1',
          name: '周期性费用',
        },
        {
          id: '2',
          name: '一次性费用',
        },
      ],
      payWayList: [
        {
          id: '1',
          name: '微信',
        },
        {
          id: '2',
          name: '支付宝',
        },
        {
          id: '3',
          name: '现金',
        },
      ],
    }
  },

  created() {
    this.listQuery.payType = this.$route.params.type
    this.id = this.$route.params.id
    if (this.$route.params.type == 1) {
      this.listQuery.roomId = this.$route.params.id
    } else if (this.$route.params.type == 2) {
      this.listQuery.parkingId = this.$route.params.id
    } else if (this.$route.params.type == 3) {
      this.listQuery.garageId = this.$route.params.id
    }
    // utils.getDataDict(this, 'costType', 'costTypeList')
    utils.getDataDict2('costType').then((res) => {
      this.costTypeList = res
      this.getList()
    })

    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    itemMoneyChange(){
      let totalMoney = 0
      for (let item of this.dlgData.offlinePayWayList) {
        if (item.money) totalMoney += item.money
      }
      this.dlgData.totalMoney = utils.num2Round(totalMoney)
    },
    offlinePayWayArrChange(value) {
      let offlinePayWayList = []
      let old_offlinePayWayList = JSON.parse(JSON.stringify(this.dlgData.offlinePayWayList))
      let totalMoney = 0
      if (value.indexOf("1")>=0) {
        let obj = {
          key:'wxPay',
          label: '微信收费金额：',
          money:undefined,
        }
        let index = old_offlinePayWayList.findIndex(item=>item.key=='wxPay')
        if (index >=0){
          obj.money = old_offlinePayWayList[index].money
          totalMoney += obj.money 
        }
        offlinePayWayList.push(obj)
        
      }
      if (value.indexOf("2")>=0) {
        let obj = {
          key:'zfbPay',
          label: '支付宝收费金额：',
          money:undefined,
        }
        let index = old_offlinePayWayList.findIndex(item=>item.key=='zfbPay')
        if (index >=0){
          obj.money = old_offlinePayWayList[index].money
          totalMoney += obj.money 
        }
        offlinePayWayList.push(obj)
      }
      if (value.indexOf("3")>=0) {
        let obj = {
          key:'cashPay',
          label: '现金收费金额：',
          money:undefined,
        }
        let index = old_offlinePayWayList.findIndex(item=>item.key=='cashPay')
        if (index >=0){
          obj.money = old_offlinePayWayList[index].money
          totalMoney += obj.money 
        }
        offlinePayWayList.push(obj)
      }
      this.dlgData.offlinePayWayList = offlinePayWayList
      this.dlgData.totalMoney = utils.num2Round(totalMoney)
    },

    selectionChange(val) {
      this.selectList = JSON.parse(JSON.stringify(val))
      this.calcMoney()
    },

    calcMoney() {
      let money = 0
      for (let i of this.selectList) {
        money += parseFloat(i.amount)
      }
      this.totalMoney = money
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    formatList(data) {
      for (let i of this.list) {
        i.feeName = utils.getNameById(i.feeType, this.costTypeList)
        i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList)
        i.amount = i.receivableAmount + i.zhinajin - i.preferentialAmount
      }
    },

    // 获取数据
    getList() {
      this.count++
      this.listLoading = true
      getPayBillPage(this.listQuery).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = res.data.data ? JSON.parse(JSON.stringify(res.data.data)) : []
          this.formatList(res.data.data)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 返回
    backItem() {
      this.$router.go(-1)
    },

    // 显示弹窗
    addItem(row) {
      this.selectList = [row]
      this.calcMoney()
      // if (this.selectList.length == 0) {
      //   this.$message.warning('请选择收费项')
      //   return
      // }
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg() {
     setTimeout(() => {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          if (utils.isNull(this.dlgData.totalMoney) || this.dlgData.totalMoney == 0) {
            this.$message.warning("请输入收费金额")
            return false
          }

          let min = utils.num2Round(this.totalMoney-1)
          let max = utils.num2Round(this.totalMoney+1)
          if(this.dlgData.totalMoney<min || this.dlgData.totalMoney>max){
            this.$message.warning(`收费总金额应在 ${min} 至 ${max} 之间`)
            return false
          }

          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          let billIds = []
          for (let i of this.selectList) {
            billIds.push(i.id)
          }

          postParam.payWay = 3
          postParam.offlinePayWay=1
          postParam.billIds = billIds.join(',')
          postParam.payType = this.listQuery.payType
          postParam.roomId = this.selectList[0]['roomId']
          postParam.roomName = this.selectList[0]['roomName']
          postParam.communityId = this.selectList[0]['communityId']
          postParam.creator = Cookie.get('userId')
          postParam.createName = this.userInfo.label

          let payInfoJson = {}
          for (let item of postParam.offlinePayWayList) {
            if (utils.isNull(item.money || item.money == 0)) {
              continue;
            }
            payInfoJson[item.key]=item.money
          }
          postParam.payInfoJson = JSON.stringify(payInfoJson)

          delete postParam.offlinePayWayArr
          delete postParam.offlinePayWayList
          delete postParam.totalMoney
          // console.log('===发送数据', postParam)
          // return false
          this.dlgLoading = true
          payfeebillPayOweAmount(postParam).then((res) => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.calcMoney()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
     }, 10)
    },

    // 上传对话框图片
    beforeUpload(file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then((res) => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg() {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


