// 职等职级管理
import request from '@/utils/request'

// 获取 职等职级 树状图


// 【【 1 分类相关
// 1.1 获取 列表
export function findRankTypeAll({page, size}) {
  return request({
    url: `/sys/findRankTypeAll/${page}/${size}`,
    method: 'get'
  })
}
// 1.2 新增 职等类别
export function saveRankType(data) {
  return request({
    url: `/sys/saveRankType`,
    method: 'post',
    data
  })
}

// 1.3 修改 职等类别
export function updateRankType(data) {
  return request({
    url: `/sys/updateRankType`,
    method: 'post',
    data
  })
}

// 1.4 删除 职等类别
export function delRankType(id) {
  return request({
    url: `/sys/delRankType/${id}`,
    method: 'get'
  })
}
// 】】 1 分类相关

// 【【 2 职等相关
// 2.1 根据职位分类 查 查找职等列表
export function findRankAllByTypeId({id, page, size}) {
  return request({
    url: `/sys/findRankAllByTypeId/${id}/${page}/${size}`,
    method: 'get'
  })
}

// 2.2 新增职等
export function saveRankLevel(data) {
  return request({
    url: `/sys/saveRankLevel`,
    method: 'post',
    data
  })
}
// 2.3 修改 职等
export function updateRankById(data) {
  return request({
    url: `/sys/updateRankById`,
    method: 'post',
    data
  })
}

// 2.4 删除 职等
export function delRankById(id) {
  return request({
    url: `/sys/delRankById/${id}`,
    method: 'get'
  })
}

// 】】 2 职等相关

// 【【 3 职级相关
// 3.1 查找职级列表
export function findLevelByRId({zdId, page, size}) {
  return request({
    url: `/sys/findLevelByRId/${zdId}/${page}/${size}`,
    method: 'get'
  })
}

// 3.2 新增职级
export function saveRanlLevelOne(data) {
  return request({
    url: `/sys/saveRanlLevelOne`,
    method: 'post',
    data
  })
}
// 3.3 修改 职级 
export function updateLevelById(data) {
  return request({
    url: `/sys/updateLevelById`,
    method: 'post',
    data
  })
}

// 3.4 删除 职级
export function delLevelById(id) {
  return request({
    url: `/sys/delLevelById/${id}`,
    method: 'get'
  })
}

// 】】 3 职级相关

