// 区域dlg组件

const distDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    distId: '',

    distName: '',

    projectId: ''
  },

  getters: {
    dlgShow: state => state.dlgShow,

    distId: state => state.distId,

    distName: state => state.distName,

    projectId: state => state.projectId,
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_DISTID: (state, val) => {
      state.distId = val
    },

    SET_DISTNAME: (state, val) => {
      state.distName = val
    },
    
    SET_PROJECTID: (state, val) => {
      state.projectId = val
    }
  },

  actions: {

  }
}

export default distDlg
