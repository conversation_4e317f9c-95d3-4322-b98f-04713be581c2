// 场地管理
import request from "@/utils/request";
import { objToParam } from "@/utils/index";

//创建场地管理
export function createVenues(data) {
  return request({
    url: `/green/venues/create`,
    method: "post",
    data,
  });
}
//删除场地管理
export function deleteVenues(id) {
  return request({
    url: `/green/venues/delete?id=${id}`,
    method: "delete",
  });
}
//获得场地管理
export function getVenues(id) {
  return request({
    url: `/green/venues/get?id=${id}`,
    method: "get",
  });
}
//场馆信息分页
export function pageVenues(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/venues/page${paramStr}`,
    method: "get",
  });
}
//编辑场地管理
export function updateVenues(data) {
  return request({
    url: `/green/venues/update`,
    method: "put",
    data,
  });
}
//修改场馆状态
export function updateStatusVenues(data) {
  return request({
    url: `/green/venues/updateStatus`,
    method: "put",
    data,
  });
}

//创建场馆区域信息
export function createUesArea(data) {
  return request({
    url: `/green/ven/ues-area/create`,
    method: "post",
    data,
  });
}
//删除场馆区域信息
export function deleteUesArea(id) {
  return request({
    url: `/green/ven/ues-area/delete?id=${id}`,
    method: "delete",
  });
}
//获得场馆区域信息
export function getUesArea(id) {
  return request({
    url: `/green/ven/ues-area/get?id=${id}`,
    method: "get",
  });
}
//场馆区域信息信息分页
export function pageUesArea(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/ues-area/page${paramStr}`,
    method: "get",
  });
}
//编辑场馆区域信息
export function updateUesArea(data) {
  return request({
    url: `/green/ven/ues-area/update`,
    method: "put",
    data,
  });
}

//场馆配置分页
export function getUesConfig(id) {
  return request({
    url: `/green/ven/ues-config/get?venuesId=${id}`,
    method: "get",
  });
}
//新增或编辑场馆配置
export function saveOrUpdateUesConfig(data) {
  return request({
    url: `/green/ven/ues-config/saveOrUpdate`,
    method: "post",
    data,
  });
}

//创建场馆区域信息
export function createUesEquipment(data) {
  return request({
    url: `/green/ven/ues-equipment/create`,
    method: "post",
    data,
  });
}
//删除场馆区域信息
export function deleteUesEquipment(id) {
  return request({
    url: `/green/ven/ues-equipment/delete?id=${id}`,
    method: "delete",
  });
}
//获得场馆区域信息
export function getUesEquipment(id) {
  return request({
    url: `/green/ven/ues-equipment/get?id=${id}`,
    method: "get",
  });
}
//场馆区域信息信息分页
export function pageUesEquipment(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/ues-equipment/page${paramStr}`,
    method: "get",
  });
}
//编辑场馆区域信息
export function updateUesEquipment(data) {
  return request({
    url: `/green/ven/ues-equipment/update`,
    method: "put",
    data,
  });
}

//解除黑名单
export function liftedBlackUser(id) {
  return request({
    url: `/green/ven/black-user/lifted?id=${id}`,
    method: "put",
  });
}
//黑名单记录分页
export function pageBlackUser(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/black-user/page${paramStr}`,
    method: "get",
  });
}

//创建场地管理-场地清洁预约
export function createCleanOrder(data) {
  return request({
    url: `/green/ven/clean-order/create`,
    method: "post",
    data,
  });
}
//删除 场地清洁预约 status ==0,5,99时允许删除
export function deleteCleanOrder(id) {
  return request({
    url: `/green/ven/clean-order/delete?id=${id}`,
    method: "delete",
  });
}
//获得场地管理-场地清洁预约
export function getCleanOrder(id) {
  return request({
    url: `/green/ven/clean-order/get?id=${id}`,
    method: "get",
  });
}
//获得场地管理-场地清洁预约分页
export function pageCleanOrder(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/clean-order/page${paramStr}`,
    method: "get",
  });
}
//更新场地清洁预约状态 审批、接单、反馈、评价、取消
export function updateStatusCleanOrder(data) {
  return request({
    url: `/green/ven/clean-order/updateStatus`,
    method: "put",
    data,
  });
}

//获得场地管理-场馆预约分页
export function pageAppointedOrder(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/appointed-order/page${paramStr}`,
    method: "get",
  });
}
//场馆预约统计
export function statisticAppointedOrder(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/appointed-order/statistic${paramStr}`,
    method: "get",
  });
}
//场地清洁预约统计
export function statisticCleanOrder(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/clean-order/statistic${paramStr}`,
    method: "get",
  });
}
//创建区域座位
export function createUesAreaSeat(data) {
  return request({
    url: `/green/ven/ues-area-seat/create`,
    method: "post",
    data,
  });
}
//删除 区域座位
export function deleteUesAreaSeat(id) {
  return request({
    url: `/green/ven/ues-area-seat/delete?id=${id}`,
    method: "delete",
  });
}

//获得区域座位
export function getUesAreaSeat(id) {
  return request({
    url: `/green/ven/ues-area-seat/get?id=${id}`,
    method: "get",
  });
}
//获得区域座位
export function pageUesAreaSeat(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/ues-area-seat/list${paramStr}`,
    method: "get",
  });
}
//编辑区域座位
export function updateUesAreaSeat(data) {
  return request({
    url: `/green/ven/ues-area-seat/update`,
    method: "put",
    data,
  });
}


//获得场地管理-图书馆预约分页
export function pageLibraryOrdert(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/library-order/page${paramStr}`,
    method: "get",
  });
}
//图书馆预约统计
export function statisticLibraryOrdert(data) {
  let paramStr = objToParam(data);
  return request({
    url: `/green/ven/library-order/statistic${paramStr}`,
    method: "get",
  });
}