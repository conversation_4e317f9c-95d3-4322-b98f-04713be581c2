<template>
  <!-- 设备安全监控 - 传感器节点 -->
  <div class="app-container">
    <!-- 搜索 -->
    <div class="filter-container">
      <el-form
        ref="searchForm"
        class="n-search"
        :model="listQuery"
        @submit.native.prevent
      >
        <div class="n-search-bar">
          <el-form-item label="">
            <el-input
              @keyup.enter.native="handleQuery"
              class="m-shaixuan-input fl"
              placeholder="请输入传感器名称"
              v-model="listQuery.name"
              style="width: 200px"
            >
              <i
                @click="resetSearchItem(['name'])"
                slot="suffix"
                class="el-input__icon el-icon-error"
              ></i>
            </el-input>
            <el-cascader
              class="fl ml10"
              ref="refSubCat"
              clearable
              placeholder="请选择区域"
              :props="{ value: 'id', label: 'name', emitPath: false }"
              :show-all-levels="false"
              @change="areaChange"
              v-model="listQuery.areaId"
              :options="areaList"
            ></el-cascader>
            <el-select
              style="width: 180px"
              class="fl ml10"
              @change="handleQuery"
              v-model="listQuery.equipRoomId"
              clearable
              placeholder="请选择设备间"
            >
              <el-option
                v-for="item of equipRoomList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <!-- <el-select
              style="width: 180px"
              class="fl ml10"
              @change="getList"
              v-model="listQuery.equipType"
              clearable
              placeholder="请选择分类"
            >
              <el-option
                v-for="item of equipTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select> -->
            <el-select
              style="width: 180px"
              class="fl ml10"
              @change="handleQuery"
              v-model="listQuery.states"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              filterable
              placeholder="请选择设备状态"
            >
              <el-option
                v-for="item of stateList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <!-- <el-select
              style="width: 140px"
              class="fl ml10"
              @change="getList"
              v-model="listQuery.flag"
              clearable
              placeholder="请选择启停状态"
            >
              <el-option
                v-for="item of flagList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select> -->
            <el-select
              style="width: 180px"
              class="fl ml10"
              @change="handleQuery"
              v-model="listQuery.dataType"
              clearable
              placeholder="请选择数据类型"
            >
              <el-option
                v-for="item of dataTypeList"
                :key="item.id"
                :label="item.dataTypeText"
                :value="item.dataType"
              >
              </el-option>
            </el-select>
            <div class="fl ml10">
              <el-input-number
                placeholder="开始值"
                @change="beginValueChange"
                :controls="false"
                v-model="listQuery.beginValue"
              ></el-input-number>
              &nbsp;~&nbsp;
              <el-input-number
                placeholder="结束值"
                @change="endValueChange"
                :controls="false"
                v-model="listQuery.endValue"
              ></el-input-number>
            </div>
            <el-button
              icon="el-icon-search"
              type="success"
              size="mini"
              class="search-right-btn fl"
              @click="handleQuery"
              >搜索</el-button
            >
          </el-form-item>

          <div class="clear"></div>
        </div>
      </el-form>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <el-table
        ref="tableBar"
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :key="tableKey"
        :data="list"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="名称" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="数据类型" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.dataType }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="分类" show-overflow-tooltip width="90">
          <template slot-scope="scope">
            <span>{{ scope.row.equipTypeStr }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="数值" width="140">
          <template slot-scope="scope">
            <span
              >{{ scope.row.dataValue
              }}{{ scope.row.dataValue ? scope.row.unit : "" }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="设备标识" width="140">
          <template slot-scope="scope">
            <span class="m-a" @click="showBjDia(scope.row)">{{
              scope.row.devEui
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="安装位置" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.installAddr }}</span>
          </template>
        </el-table-column>
        <el-table-column label="区域" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.area }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属设备间" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.equipRoomName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属设备" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.equipName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="通讯状态" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.state == 2" class="fdanger fbold">异常</span>
            <span v-else class="fsuccess fbold">正常</span>
          </template>
        </el-table-column>

        <el-table-column label="报警状态" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.state == 1" class="fdanger fbold">异常</span>
            <span v-else class="fsuccess fbold">正常</span>
          </template>
        </el-table-column>

        <el-table-column label="电池状态" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.eqStatus == 0" class="fsuccess fbold"
              >正常</span
            >
            <span v-if="scope.row.eqStatus == 1" class="fdanger fbold"
              >低电</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="最近上报时间"
          width="180"
          prop="lastReportTime"
          align="center"
        />
        <!-- flag=0 启用，flag=2 停用 -->
        <!-- <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.flag == 0" class="fsuccess fbold"
              >已启用</span
            >
            <span v-if="scope.row.flag == 2" class="fdanger fbold">已停用</span>
          </template>
        </el-table-column> -->
      </el-table>
    </div>

    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <div class="clear"></div>

    <!-- << 弹窗 报警信息 -->
    <el-dialog
      title="历史记录"
      :close-on-click-modal="false"
      :append-to-body="true"
      :visible.sync="diaBjState"
      width="1200px"
      top="30px"
      icon-class="el-icon-info"
    >
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="列表" name="liebiao">
          <el-date-picker
            style="width: 300px"
            class="fl"
            @change="getBjList"
            v-model="diaBjQuery.dateRange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            class="search-right-btn fl"
            @click="getBjList"
            >搜索</el-button
          >

          <div class="clear"></div>

          <el-table
            ref="tableBar"
            class="m-small-table mt10"
            v-loading="listLoading"
            :key="diaBjTableKey"
            :data="diaBjList"
            border
            fit
            highlight-current-row
            style="width: 100%"
            max-height="500px"
          >
            <el-table-column label="#" type="index" align="center" width="70">
              <template slot-scope="scope">
                <span>{{
                  (diaBjQuery.page - 1) * diaBjQuery.limit + scope.$index + 1
                }}</span>
              </template>
            </el-table-column>

            <el-table-column
              v-for="(item, index) of diaBjTHList"
              :key="index"
              :label="item.label"
            >
              <template slot-scope="scope">
                <span>{{ scope.row[item.key] }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <pagination
            class="mt10"
            v-show="diaBjTotal > 0"
            :total="diaBjTotal"
            :page.sync="diaBjQuery.page"
            :limit.sync="diaBjQuery.limit"
            @pagination="getBjList"
          />
        </el-tab-pane>
        <el-tab-pane label="趋势" name="qushi">
          <el-date-picker
            class="fl ml10"
            style="width: 350px"
            @change="getZxt"
            v-model="zxtQuery.dateRange"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="mini"
          >
          </el-date-picker>
          <el-select
            @change="handleSelectChange"
            class="fl ml10"
            style="width: 200px"
            v-model="zxtQuery.disRespVos"
            multiple
            collapse-tags
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.type"
              :label="item.name"
              :value="`${item.type},${item.name}`"
            >
            </el-option>
          </el-select>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            class="search-right-btn fl"
            @click="getZxt"
            >搜索</el-button
          >
          <div class="clear"></div>

          <div
            v-if="showChart2"
            id="echart-bar2"
            style="height: 500px; margin-top: 16px"
          ></div>
        </el-tab-pane>
      </el-tabs>
      <div class="clear"></div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="diaBjState = false" icon="el-icon-back"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Cookie from "js-cookie";

// 接口
import {
  findEquipNodePage,
  eqinAddOrUpdInfo,
  findEquipNodeById,
  delEquipNodeById,
  findEquioPage, // 设备间/设备列表接口
  findNodelArchivesPage, // 设备档案
  findNodelArchivesById, // 设备档案详情
  setDLQAddr, // 初始化断路器地址
  findSwitchPage, // 获取开关列表
  protocolLoran, // 报警信息
  cameraPage, // 分页获取摄像头
  addCameraNode, // 关联摄像头
  findCamerasByNodeId, // 获取节点关联的摄像头
  iotOpen,
  iotStop
} from "@/api/safetyMonitoringApi";

import {
  arrId2Name, // 根据id 获取name
  isNull,
  getDataDictOther
} from "@/utils";
import * as echarts from "echarts";
import * as utils from "@/utils";
import Pagination from "@/components/Pagination"; // 分页
import moment, { localeData } from "moment"; //导入文件
import { getAction, postAction } from "../../api";

let listQueryEmpty = {
  name: "",
  pageNo: 1,
  pageSize: 10,
  equipRoomId: "",
  states: [],
  dataType: "",
  beginValue: undefined,
  endValue: undefined,
  createTime: "",
  areaId: "",
  equipType: 1,
  area: ""
};

// 报警信息弹窗
let diaBjQueryEmpty = {
  id: "",
  label: "",
  page: 1,
  limit: 10,
  dateRange: []
};

//折线图
let zxtQueryEmpty = {
  disRespVos: [],
  nodeId: "",
  startTime: "",
  endTime: "",
  dateRange: []
};
export default {
  components: {
    Pagination
  },
  data() {
    return {
      equipTypeList: [],
      // ---- search type
      searchMoreState: false, // 更多条件展开状态

      // 表格
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,

      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      stateList: [
        { id: "3", name: "正常" },
        { id: "1", name: "参数异常(报警异常)" },
        { id: "2", name: "通讯异常" }
      ],
      flagList: [
        { id: 0, name: "启用" },
        { id: 2, name: "停用" }
      ],
      equipRoomList: [], // 设备间
      dataTypeList: [], // 设备档案列表
      areaList: [], //区域
      // >> 数据字典

      // >> 新增编辑弹窗 表格开关

      // << 弹窗-报警信息
      diaBjTableKey: 0,
      diaBjState: false,
      diaBjTHList: [],
      diaBjList: [],
      diaBjTotal: 0,
      diaBjQuery: JSON.parse(JSON.stringify(diaBjQueryEmpty)),
      // >> 弹窗-报警信息

      // << 弹窗 关联摄像头

      // << 弹窗 设置计划
      activeName: "liebiao",
      zxtQuery: JSON.parse(JSON.stringify(zxtQueryEmpty)),
      options: [],
      bjDiaRow: {},
      showChart2: false,
      echartRoom2: null,
      zxtSelect: []
    };
  },
  created() {
    getDataDictOther(this, "iot_equip_type", "equipTypeList"); // 业务分类
    this.getList();
    this.getEquipRoomList(); // 获取设备间
      this.getDataTypeList();
  },

  methods: {
    handleQuery() {
      if (
        (this.listQuery.beginValue && utils.isNull(this.listQuery.endValue)) ||
        (this.listQuery.beginValue == 0 &&
          utils.isNull(this.listQuery.endValue))
      ) {
        this.$message.warning("请选择结束值");
        return false;
      }
      if (
        (this.listQuery.endValue && utils.isNull(this.listQuery.beginValue)) ||
        (this.listQuery.endValue == 0 &&
          utils.isNull(this.listQuery.beginValue))
      ) {
        this.$message.warning("请选择开始值");
        return false;
      }
      if (this.listQuery.beginValue > this.listQuery.endValue) {
        this.$message.warning("结束值不能小于开始值");
        return;
      }
      this.total = 0;
      this.listQuery.pageNo = 1;
      this.getList();
    },
    getDataTypeList() {
      getAction(`/iot/equip-node/getDataType`).then(res1 => {
        if (res1.data.code == 200) {
          this.dataTypeList = res1.data.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    beginValueChange() {
      if (!utils.isNull(this.listQuery.endValue)) {
        if (this.listQuery.beginValue > this.listQuery.endValue) {
          this.listQuery.beginValue = null;
          this.$message({
            type: "warning",
            message: "结束值不能小于开始值"
          });
        }
      }
    },
    endValueChange() {
      if (!utils.isNull(this.listQuery.beginValue)) {
        if (this.listQuery.endValue < this.listQuery.beginValue) {
          this.listQuery.endValue = this.listQuery.beginValue+1
          this.$message({
            type: "warning",
            message: "结束值不能小于开始值"
          });
        }
      }
    },
    areaChange(selectedValues) {
      if (selectedValues) {
        this.listQuery.equipRoomId = "";
        this.listQuery.equipRoomName = "";
        this.listQuery.area = this.$refs[
          "refSubCat"
        ].getCheckedNodes()[0].label; //获取选中name
      }
      // this.getEquipRoomList();
    },
    //区域
    getAreaList() {
      this.areaList = [];
      let sendObj = {
        page: 1,
        size: 9999
      };
      regionTree(sendObj).then(res => {
        if (res.data.code == "200") {
          this.areaList = this.getTreeData(res.data.data);
        } else {
          this.$message({
            type: "warning",
            message: res.data.msg
          });
        }
      });
    },
    //tab切换
    handleClick(tab, event) {
      this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty));
      this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
      this.diaBjQuery.id = this.bjDiaRow.id;
      this.zxtSelect = [];
      console.log(tab, event);
      if (tab.name == "liebiao") {
        this.getBjList();
      } else {
        const now = moment();
        const start = moment(now).subtract(3, "hours");
        const end = moment().format("YYYY-MM-DD HH:mm");
        let startTime = moment(start).format("YYYY-MM-DD HH:mm");
        // let endTime = moment(end).format('YYYY-MM-DD HH:mm');
        this.zxtQuery.dateRange = [startTime, end];
        this.getDxList();
        this.getZxt();
      }
    },
    getDxList() {
      getAction(`/iot/trend/nodeDataDis/${this.bjDiaRow.id}`).then(res1 => {
        let res = res1.data;
        if (res.code == 200) {
          this.options = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleSelectChange() {
      this.zxtSelect = [];
      this.zxtQuery.disRespVos.forEach(element => {
        console.log(element, "element");
        let [type, name] = element.split(",");
        this.zxtSelect.push({ type, name });
      });
    },
    getZxt() {
      this.showChart2 = false;
      if (
        isNull(this.zxtQuery.dateRange) ||
        this.zxtQuery.dateRange.length <= 0
      ) {
        this.$message.warning("请先选择起止时间");
        return false;
      }
      let sendObj = JSON.parse(JSON.stringify(this.zxtQuery));
      // 日期范围
      sendObj.startTime = "";
      sendObj.endTime = "";
      if (
        !isNull(this.zxtQuery.dateRange) &&
        this.zxtQuery.dateRange.length > 0
      ) {
        sendObj.startTime = this.zxtQuery.dateRange[0];
        sendObj.endTime = this.zxtQuery.dateRange[1];
      }
      sendObj.disRespVos = this.zxtSelect;
      sendObj.nodeId = this.bjDiaRow.id;
      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      postAction("/iot/trend/nodeDataTrend", sendObj).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == 200) {
          if (!utils.isNull(res.data) && res.data.list.length > 0) {
            this.showChart2 = true;
            // this.list = res.data;
            setTimeout(() => {
              this.setEchartBar2(res.data.list, res.data.times);
              //   this.createRoom(res.data.list)
            }, 100);
          }
        }
      });
    },
    //创建折线图
    setEchartBar2(arr, dataMap) {
      console.log(arr, "arr");
      if (this.showChart2 == false) {
        // if (!utils.isNull(arr)) {
        //   this.echartRoom2.clear();
        // }
        return;
      }
      // << 本月1号到当天
      let xList = [];
      let xList0 = [];
      // let dateObj = new Date();
      // console.log("dateObj.getDate()", dateObj.getDate());
      // console.log(this.getEveryDayDateByBetweenDate(this.listQuery.dateRange[0],this.listQuery.dateRange[1]),'时间间隔');
      // let dayNum = parseInt(dateObj.getDate());
      // let month = utils.return2Num2(dateObj.getMonth() + 1);
      // let year = dateObj.getFullYear();
      // for (let i = 0; i < dayNum; i++) {
      //   let key = `${year}-${month}-${utils.return2Num2(i + 1)}`;
      //   xList.push(key);
      //   xList0.push(`${year}-${month}-${utils.return2Num2(i + 1)}`);
      // }

      // 拼接数据
      let data = [];
      let listData = [];
      for (let index = 0; index < dataMap.length; index++) {
        let obj = {
          yearMonthDate: dataMap[index],
          count: 0,
          type: ""
        };
        listData.push(obj);
      }
      for (let i = 0; i < arr.length; i++) {
        let itemLine = arr[i];
        let lineObj = {
          name: itemLine.name,
          type: "line",
          stack: "",
          data: []
        };
        let map = itemLine.list;
        // console.log('111111111map', map)
        // console.log('111111111listData', listData)
        for (let key = 0; key < map.length; key++) {
          for (let k = 0; k < listData.length; k++) {
            if (map[key].time == listData[k].yearMonthDate) {
              lineObj.data.push(map[key].value);
              // listData[k].value = map[key].value;
              // listData[k].type = map[key].type;
            }
          }
        }
        data.push(lineObj);
        // let arrData = [];
        // console.log("==listData", listData);
        // for (let o = 0; o < listData.length; o++) {
        //   arrData.push(listData[o].value);
        // }
        // console.log(arrData, "arrData");
        // lineObj.data = arrData;
        // data.push(lineObj);
      }
      console.log(data, "data--------------");
      // xList0.push(map[key].yearMonthDate)
      xList0 = dataMap;
      // 绘制图标
      var myChart = echarts.init(document.getElementById("echart-bar2"));
      var option = {
        title: {
          text: ""
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },

        legend: {
          left: 10
        },
        grid: {
          left: "2%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false, // true-刻度中间 false-刻度线上
          data: xList0
        },
        yAxis: {
          type: "value"
          // name: '单位（吨）',
          // nameTextStyle: {
          //   color: '#aaa',
          //   nameLocation: 'start',
          // },
        },
        series: data
        // series: [[1,2,3],[12,22,32],[13,23,33]]
      };
      myChart.clear();
      myChart.setOption(option);
      myChart.on("click", param => {
        // console.log('param', param)
        // // componentIndex
        // // dataIndex
        // let msg = `${this.echartLineData[param.componentIndex].name}：${
        //   this.echartLineData[param.componentIndex].data[param.dataIndex]
        // }`
        // alert(msg)
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },

    // << 下拉框
    // 获取设备间
    getEquipRoomList() {
      let sendObj = {
        label: "",
        page: 1,
        limit: 9999,
        isEquipRoom: "0" // 0设备间 1设备 不传查所有
      };
      if (this.$route.path == "/electricalFireMonitoring/sensorEquip") {
        sendObj.equipType = 2;
      }
      if (this.$route.path == "/equipSafeMan/sensorEquip") {
        sendObj.equipType = 1;
      }
      if (this.$route.path == "/environmentalSafety/sensorEquip") {
        sendObj.equipType = 3;
      }
      findEquioPage(sendObj).then(res1 => {
        // this.listLoading = false
        let res = res1.data;
        if (res.code == "200") {
          this.equipRoomList = JSON.parse(JSON.stringify(res.list));
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    // >> 外部弹窗

    // 搜索框 清空单个条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.getList();
    },

    // 获取数据
    getList() {
      this.list = [];
      this.listLoading = true;
      console.log("发送数据", this.listQuery);
      console.log(this.$route.path, "this.$route.path");
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      sendObj.states = sendObj.states.join(",");
      postAction("/iot/equip-node/param/page", sendObj).then(res1 => {
        this.listLoading = false;
        let res = res1.data;
        console.log(res, "res");

        if (res.code == "200") {
          this.total = res.data.total;
          this.list = JSON.parse(JSON.stringify(res.data.list));
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    // >> 弹窗 初始化断路器

    // >> 弹窗 开关列表

    // << 弹窗-报警信息
    showBjDia(row) {
      // 调用接口
      (this.activeName = "liebiao"),
        (this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty)));
      this.diaBjQuery.id = row.nodeId;
      this.bjDiaRow = row;
      this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
      this.getBjList();
    },
    // 获取列表
    getBjList() {
      let beginTime = "";
      let endTime = "";
      if (
        this.diaBjQuery.dateRange != null &&
        this.diaBjQuery.dateRange.length != 0
      ) {
        beginTime = this.diaBjQuery.dateRange[0];
        endTime = this.diaBjQuery.dateRange[1];
      }
      let sendObj = {
        page: this.diaBjQuery.page,
        limit: this.diaBjQuery.limit,
        id: this.diaBjQuery.id,
        beginTime,
        endTime
      };

      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      protocolLoran(sendObj).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == "200") {
          if (res.data == null) {
            this.diaBjTotal = 0;
            this.$message({
              type: "warning",
              message: "暂无报警信息"
            });
            return false;
          }
          // 表头
          let diaBjTHList = [];
          for (let key in res.data.field) {
            let label = res.data.field[key];
            let obj = {
              key,
              label
            };
            diaBjTHList.push(obj);
          }
          this.diaBjTHList = diaBjTHList;

          // 表格数据
          this.diaBjTotal = res.data.total;

          this.diaBjList = res.list;
          this.diaBjState = true;
          this.diaBjTableKey++;
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    }
    // >> 弹窗-报警信息
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
