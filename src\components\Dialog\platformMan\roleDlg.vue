<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="'选择角色'"
    :visible.sync="dlgShow"
    top="5vh"
    append-to-body
  >
    <!-- <div class="filter-container">
      <div class='fr'>
        <el-input v-model="listQuery.str" placeholder='请填写角色名称'>
          <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
        </el-input>
        <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">
          搜索
        </el-button>
      </div>
    </div> -->
    <div class="clearfix">
      <el-popover
        class="fr"
        placement="bottom"
        width="400"
        @show="showPopover"
        trigger="click"
      >
        <el-table
          ref="multipleTable"
          style="height: 300px; overflow: auto"
          :data="selectList"
        >
          <el-table-column
            type="index"
            width="50"
            align="center"
          ></el-table-column>

          <el-table-column label="角色名称">
            <template slot-scope="scope">
              <span>{{ scope.row.label }}</span>
            </template>
          </el-table-column>

          <el-table-column property="" label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button
                @click="popRemoveRow(scope.row)"
                type="danger"
                size="mini"
                icon="el-icon-delete"
                plain
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button
          class="fr search-right-btn"
          type="success"
          slot="reference"
          icon="el-icon-arrow-down"
          >查看已选</el-button
        >
      </el-popover>

      <el-button
        class="fr search-right-btn"
        icon="el-icon-search"
        type="success"
        @click="searchItem"
        
      >
        搜索
      </el-button>
      <el-input
        class="fr"
        v-model="listQuery.str"
        placeholder="请填写角色名称"
        style="width: 180px"
      >
        <i
          slot="suffix"
          @click="resetStr"
          class="el-input__icon el-icon-error"
        ></i>
      </el-input>
    </div>
    <div class="table-container mt10">
      <el-table
        v-if="isMul"
        ref="tableRef"
        class="m-small-table"
        :data="list"
        border
        fit
        highlight-current-row
        @select="tableSelectChange"
        @select-all="tableSelectAll"
      >
        <el-table-column type="selection" width="55" align="center">
        </el-table-column>

        <el-table-column label="角色名称" width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="220px">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
      </el-table>

      <el-table
        v-else
        class="m-small-table"
        :data="list"
        @row-click="rowClick"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="#" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectRoleId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="角色名称" width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="220px">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.size"
        @pagination="getList"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import Cookie from "js-cookie";

import Pagination from "@/components/Pagination";
import * as utils from "@/utils";

import { findRoleLike } from "@/api/powermanager.js";

export default {
  props: {
    isMul: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],

      listQuery: {
        size: 10,
        str: "",
        page: 1
      },

      total: 0,

      selectRoleId: "",

      selectRoleName: "",
      // 多选
      selectList: []
    };
  },

  computed: {
    dlgShow: {
      get: function() {
        return this.$store.state.platformMan.roleDlg.dlgShow;
      },
      set: function(val) {
        this.$store.commit("platformMan/roleDlg/SET_DLGSHOW", val);
      }
    },

    roleId: {
      get: function() {
        return this.$store.state.platformMan.roleDlg.roleId;
      },
      set: function(val) {
        this.$store.commit("platformMan/roleDlg/SET_ROLEID", val);
      }
    },

    roleName: {
      get: function() {
        return this.$store.state.platformMan.roleDlg.roleName;
      },
      set: function(val) {
        this.$store.commit("platformMan/roleDlg/SET_ROLENAME", val);
      }
    }
  },

  watch: {
    dlgShow(val) {
      if (val) {
        this.list = [];
        this.listQuery = {
          size: 10,
          page: 1,
          str: ""
        };
        if (utils.isNull(this.roleId)) {
          this.selectRoleId = "";
          this.selectRoleName = "";
          this.selectList = [];
        }

        if (this.isMul) {
          setTimeout(() => {
            let selectList = [];
            let roleIdArr = [];
            let roleNameArr = [];
            if (this.selectRoleId) {
              roleIdArr = this.selectRoleId.split(",");
              roleNameArr = this.selectRoleName.split(",");
            }

            for (let i = 0; i < roleIdArr.length; i++) {
              selectList.push({
                id: roleIdArr[i],
                label: roleNameArr[i]
              });
            }
            this.selectList = selectList;
            this.getList();
          }, 200);
        } else {
          this.getList();
        }
      }
    },

    roleId(val) {
      this.selectRoleId = val;
    },

    roleName(val) {
      this.selectRoleName = val;
    }
  },

  methods: {
    popRemoveRow(row) {
      let selectList = this.selectList.filter(item => {
        return item.id != row.id;
      });
      this.selectList = JSON.parse(JSON.stringify(selectList));
      for (let item of this.list) {
        let isHas = row.id == item.id;
        if (isHas) {
          this.$refs.tableRef.toggleRowSelection(item, false);
        }
      }
    },
    tableSelectChange(arr, row) {
      this.tableCheckBaseFunc(row);
    },

    tableCheckBaseFunc(row) {
      let isCheck = !this.selectList.some(item => item.id == row.id);

      if (isCheck) {
        this.selectList.push(row);
      } else {
        let selectList = this.selectList.filter(item => {
          return item.id != row.id;
        });
        this.selectList = JSON.parse(JSON.stringify(selectList));
      }
    },
    tableSelectAll(arr) {
      let len = arr.length;
      let list = JSON.parse(JSON.stringify(this.list));
      let selectList = JSON.parse(JSON.stringify(this.selectList));
      if (len == 0) {
        let newList = [];
        for (let item of selectList) {
          let hasId = list.some(item2 => item2.id == item.id);
          if (!hasId) {
            newList.push(item);
          }
        }
        selectList = JSON.parse(JSON.stringify(newList));
      } else {
        for (let item of list) {
          let hasId = selectList.some(item2 => item2.id == item.id);
          if (!hasId) {
            selectList.push(item);
          }
        }
      }
      this.selectList = selectList;
    },

    resetStr() {
      this.listQuery.str = "";
      this.getList();
    },

    searchItem() {
      this.getList();
    },

    rowClick(row, column, event) {
      this.selectRoleId = row["id"];
      this.selectRoleName = row["label"];
    },

    getList() {
      this.list = [];
      findRoleLike(this.listQuery).then(res => {
        let code = res.data.code;
        let msg = res.data.msg;
        if (code == 200) {
          let data = res.data.data;
          if (utils.isNull(data)) {
            this.total = 0;
            return;
          }
          let list = res.data.list;
          this.list = list;
          this.total = res.data.data.total;

          if (this.isMul) {
            let list = this.list;
            this.$nextTick(() => {
              if (this.selectList.length > 0) {
                for (let item of list) {
                  let isHas = this.selectList.some(row => row.id == item.id);
                  if (isHas) {
                    this.$refs.tableRef.toggleRowSelection(item, true);
                  } else {
                    this.$refs.tableRef.toggleRowSelection(item, false);
                  }
                }
              } else {
                this.$refs.tableRef.clearSelection();
              }
            });
          }
        } else {
          this.$message.error(msg);
        }
      });
    },

    subDlg() {
      if (this.isMul) {
        if (this.selectList.length) {
          let idArr = this.selectList.map(item => item.id);
          let nameArr = this.selectList.map(item => item.label);
          console.log("===idArr", idArr);
          console.log("===nameArr", nameArr);

          this.$store.commit("platformMan/roleDlg/SET_ROLEID", idArr.join(","));
          this.$store.commit(
            "platformMan/roleDlg/SET_ROLENAME",
            nameArr.join(",")
          );
        } else {
          this.$store.commit("platformMan/roleDlg/SET_ROLEID", "");
          this.$store.commit("platformMan/roleDlg/SET_ROLENAME", "");
        }
      } else {
        this.roleId = this.selectRoleId;
        this.roleName = this.selectRoleName;
        this.$store.commit("platformMan/roleDlg/SET_ROLEID", this.roleId);
        this.$store.commit("platformMan/roleDlg/SET_ROLENAME", this.roleName);
      }

      this.closeDlg();
    },

    closeDlg() {
      this.$store.commit("platformMan/roleDlg/SET_DLGSHOW", false);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>
