<template>
  <!--库房管理-->
  <div class="app-container">
    <div class="filter-container">
      <el-form ref="searchForm" class='clearfix' label-width="90px" @submit.native.prevent>
        <div class='fr'>
          <el-input v-model="listQuery.str" placeholder='请填写库房名称'>
            <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">
            搜索
          </el-button>
          <el-button icon='el-icon-plus' type="primary" size='mini' @click="addItem">
            新增
          </el-button>
        </div>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="库房名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属科室" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="180">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="editItem(scope.row, scope.$index)" plain>
              编辑
            </el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="delItem(scope.row, scope.$index)" plain>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="dlgTitle" :visible.sync="dlgShow">
      <el-form ref="dlgForm" :rules="dlgRules" :model="dlgData" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="库房名称" prop="name">
              <el-input v-model="dlgData.name" placeholder="请填写库房名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属科室" prop="branchName">
              <el-input @focus="showBranchDlgMul()" v-model="dlgData.branchName" readonly placeholder="请选择所属科室"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input type="textarea" :autosize="{ minRows: 5, maxRows: 10}" v-model="dlgData.remark" placeholder="请填写备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-back" @click="dlgShow = false">取 消</el-button>
        <el-button icon="el-icon-check" :disabled="!subEnable" type="success" @click="subDlg()">保 存</el-button>
      </div>
    </el-dialog>

    <branchDlgMul />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import branchDlgMul from '@/components/Dialog/platformMan/branchDlgMul'

import {
  findStorageDynamic,
  updateStorage,
  saveOrUStorage,
} from '@/api/medicalMatchManSystem/clothingMan/storageMan'


export default {
  components: {
    Pagination,
    branchDlgMul
  },
  data () {
    return {
      dlgRules: {
        name: [
          { required: true, message: '库房名称必填', trigger: 'blur' },
          { min: 1, max: 18, message: '病房名称最多18个字符', trigger: 'blur' }
        ],
        branchName: [{ required: true, message: '所属科室必填', trigger: 'change' }],
      },
      list: [],
      listQuery: {
        page: 1,
        size: 20,
        str: ''
      },
      total: 0,
      listLoading: false,
      subEnable: true,
      dlgShow: false,
      dlgTitle: '',
      dialogType: '',
      dlgData: {
        name: '',
        branchName: '',
        remark: '',
      },
    }
  },
  computed: {
    ...mapGetters('platformMan/branchDlgMul', {
      branchIds: 'branchIds',
      branchNames: 'branchNames'
    }),
  },

  watch: {
    branchIds (val) {
      this.dlgData['branchId'] = val
    },
    branchNames (val) {
      this.dlgData['branchName'] = val
    },
  },

  created () {
    this.getList()
  },

  methods: {

    showBranchDlgMul () {
      this.$store.commit('platformMan/branchDlgMul/SET_BRANCHIDS', this.dlgData.branchId)
      this.$store.commit('platformMan/branchDlgMul/SET_BRANCHNAMES', this.dlgData.branchName)
      this.$store.commit('platformMan/branchDlgMul/SET_DLGSHOW', true)
    },

    getList () {
      this.list = []
      this.listLoading = true
      findStorageDynamic(this.listQuery).then(res => {
        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    resetStr () {
      this.listQuery.str = ''
      this.getList()
    },

    searchItem () {
      this.getList()
    },

    addItem () {
      this.dlgData = {
        name: '',
        branchId: '',
        branchName: '',
        remark: ''
      }
      this.dlgShow = true
      this.dialogType = 'add'
      this.dlgTitle = '新增库房信息'
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    editItem (data, idx) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      let branchs = this.dlgData.branchs
      if (branchs.length > 0) {
        let branchIds = []
        let branchNames = []
        for (let i of branchs) {
          branchIds.push(i['branchId'])
          branchNames.push(i['branchName'])
        }
        this.dlgData['branchId'] = branchIds.join(",")
        this.dlgData['branchName'] = branchNames.join(",")
      }
      this.dlgShow = true
      this.dialogType = 'edit'
      this.dlgTitle = '编辑库房信息'
    },

    delItem (data, idx) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let postParam = {
            id: data.id
          }
          updateStorage(postParam).then(res => {
            let code = res.data.code
            let msg = res.data.msg
            if (code === '200') {
              this.$message({
                type: 'success',
                message: msg
              })
              this.getList()
            } else {
              this.$message.error(msg)
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    subDlg () {
      this.$refs['dlgForm'].validate(valid => {
        if (valid) {
          this.subEnable = false
          let postParam = this.dlgData
          if (this.dialogType === 'add') {
            postParam['id'] = '0'
          } else {
            postParam['id'] = this.dlgData.id
          }
          let branchs = []
          let branchIds = this.dlgData.branchId.split(",")
          let branchNames = this.dlgData.branchName.split(",")
          for (let i in branchIds) {
            branchs.push({
              branchId: branchIds[i],
              branchName: branchNames[i],
            })
          }
          postParam['branchs'] = branchs
          saveOrUStorage(postParam).then(res => {
            this.subEnable = true
            let code = res.data.code
            let msg = res.data.msg
            if (code == 200) {
              this.$message({
                type: 'success',
                message: msg
              })
              this.dlgShow = false
              this.getList()
            } else {
              this.$message.error(msg)
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>