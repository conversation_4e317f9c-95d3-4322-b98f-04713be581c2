import request from '@/utils/request'

/*
*反馈查询
*/

// 查询陪诊陪检列表 
export function findPzConfigLike(data) 
{
	return request({
		url: `/sys/findPzConfigLike`,
		method: 'post',
		data
	})
}

// 保存修改陪诊陪检配置
export function savePzConfig(data)
{
	return request({
		url: `/sys/savePzConfig`,
		method: 'post',
		data
	})
}

// 删除陪诊陪检配置
export function delPzConfig(data)
{
	return request({
		url: `/sys/delPzConfig`,
		method: 'post',
		data
	})
}

// 根据登陆人项目id查找是否有偿配置
export function findPzIsPay(data)
{
	return request({
		url: `/u/usapi/task/findPzIsPay`,
		method: 'post',
		data
	})
}

// 保存是否有偿配置（每个项目只能新增一次，无删除，可修改）
export function saveOrUPzIsPay(data)
{
	return request({
		url: `/u/usapi/task/saveOrUPzIsPay`,
		method: 'post',
		data
	})
}





