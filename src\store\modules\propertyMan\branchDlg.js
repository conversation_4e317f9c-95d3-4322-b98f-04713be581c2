// 部门dlg组件

const branchDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    branchId: '',

    branchName: '',

    projectId: ''
  },

  getters: {
    dlgShow: state => state.dlgShow,

    branchId: state => state.branchId,

    branchName: state => state.branchName,

    projectId: state => state.projectId,
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_BRANCHID: (state, val) => {
      state.branchId = val
    },

    SET_BRANCHNAME: (state, val) => {
      state.branchName = val
    },

    SET_PROJECTID: (state, val) => {
      state.projectId = val
    },
  },

  actions: {

  }
}

export default branchDlg
