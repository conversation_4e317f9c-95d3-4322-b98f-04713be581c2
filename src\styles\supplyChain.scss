// .app-container {
//   height: calc(100vh - 138px);
// }
.org-main {
  height: 520px;
  width: 100%;
  border: 3px dashed #19aa8d;
  border-radius: 5px;
}

.departMan-main {
  height: calc(100% - 108px);
  position: relative;
  display: flex;
  justify-content: space-between;
}
//  左侧树状图样式
.departMan-left {
  width: 320px;
  box-sizing: border-box;
  margin-right: 10px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  height: 100%;
  overflow: auto;
  .button-container {
    height: 30px;
  }
  .el-tree {
    height: 100%;
    overflow: auto;
  }
  .button-container + .el-tree {
    height: calc(100% - 30px);
  }
  .custom-tree-node {
    display: flex;
    justify-content: space-between;
    flex: 1;
    align-items: center;
    span:nth-child(1) {
      display: inline-block;
      flex: 1;
      width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    span:nth-child(2) {
      display: inline-block;
      width: 44px;
    }
  }
}
// 右侧表单
.departMan-right {
  flex: 1;
  box-sizing: border-box;
  border: 1px solid #dcdfe6;
  padding: 16px;
  overflow: auto;
  .departMan-right-title {
    font-size: 20px;
    color: #333;
    text-align: left;
    margin-top: 0px;
    margin-bottom: 30px;
    font-weight: bold;
  }
  .m-item-box {
    display: flex;
    justify-content: space-between;
    .el-form-item {
      width: 44%;
    }
  }
  .p-red {
    color: #f56c6c;
    font-size: 14px;
  }
  .input-tips {
    position: absolute;
    left: 0px;
    bottom: -18px;
    color: #f56c6c;
    font-size: 14px;
    line-height: 16px;
    margin: 0px;
    background: #fff;
    z-index: 999;
  }
  .form-btn-bar {
    padding-left: 120px;
    padding-bottom: 30px;
  }
}

.dialog-radio-bar {
  padding-left: 30px;
  margin-bottom: 14px;
}
.dialog-radio-bar-input {
  width: 160px;
}
