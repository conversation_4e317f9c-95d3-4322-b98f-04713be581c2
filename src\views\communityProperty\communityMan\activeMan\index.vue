<template>
  <!-- 活动管理 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <!-- <el-form-item label="关键字:">
          <el-input @keyup.enter.native="getList" placeholder="姓名/电话/身份证号" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item> -->

        <el-form-item label="活动时间:">
          <el-date-picker
            style="width: 220px"
            v-model="listQuery.dateRange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="选择小区:">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="活动状态:">
          <el-radio-group v-model="listQuery.status" @change="searchFunc">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="0">进行中</el-radio-button>
            <el-radio-button label="1">已结束</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="searchFunc">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="showDlg('add')">添加</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="#" align="center" width="60">
          <template slot-scope="scope">
            {{ (listQuery.page - 1) * listQuery.limit + scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column label="活动名称" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="所属小区" prop="communityName" show-overflow-tooltip></el-table-column>
        <el-table-column label="发布时间" prop="createTime" width="140" align="center"></el-table-column>
        <el-table-column label="活动开始时间" prop="startDate" width="140" align="center"></el-table-column>
        <el-table-column label="活动结束时间" prop="endDate" width="140" align="center"></el-table-column>
        <el-table-column label="状态" width="80px" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '1'" type="info">已结束</el-tag>
            <el-tag v-else type="success">进行中</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="参与人数" width="80px" align="center">
          <template slot-scope="scope">
            <span class="m-a" @click="showInfoDlg(scope.row)">{{ scope.row.userCount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="showQrDlg(scope.row)"
              >活动二维码</el-button
            >
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="showDlg('edit', scope.row)"
              >修改</el-button
            >

            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <!-- 弹窗 二维码 -->
    <el-dialog
      :title="qrTitle + '-活动二维码'"
      top="30px"
      :close-on-click-modal="false"
      :visible.sync="qrState"
      width="500px"
      append-to-body
    >
      <div class="bpmn-body" id="qr-box">
        <!-- <img id="test-logo" src="static/image/wh-logo.png" alt="" />
        <div class="tac" style="margin-top: -10px; margin-bottom: -2px">{{ qrTitle }}</div> -->
        <canvas style="display: block; margin: 0 auto" id="QRCode2" class="qrcode-canvas"></canvas>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="qrState = false" icon="el-icon-back">关闭</el-button>
        <!-- <el-button type="success" @click="qrPrint" icon="el-icon-check">导出</el-button> -->
      </div>
    </el-dialog>

    <!-- 弹窗 新增/编辑 -->
    <addDlg
      :dlgState0="dlgState"
      :dlgData0="dlgData"
      :dlgType="dlgType"
      :dlgQuery="dlgQuery"
      @closeDlg="closeDlg"
      @getList="getList"
      :communityList="communityList"
    />
    <userListDlg
      :dlgState0="dlgInfoState"
      :dlgData0="dlgInfoData"
      :dlgType="dlgInfoType"
      :dlgQuery="dlgInfoQuery"
      @closeDlg="closeInfoDlg"
      @getList="getList"
    />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'

import * as utils from '@/utils'
import { postAction, getAction } from '@/api'
import { uploadImg } from '@/utils/uploadImg'
import QRCode from 'qrcode' //引入生成二维码插件

import Pagination from '@/components/Pagination'
import addDlg from './addDlg'
import userListDlg from './userListDlg'
export default {
  components: {
    Pagination,
    addDlg,
    userListDlg,
  },
  data() {
    return {
      userInfo: '',
      // 弹窗数据
      dlgQuery: {},
      dlgState: false,
      dlgType: '', // 弹框状态add, edit
      dlgData: {},
      // 详情弹窗
      dlgInfoQuery: {},
      dlgInfoState: false,
      dlgInfoType: '', // 弹框状态add, edit
      dlgInfoData: {},

      /////

      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        endDate: '', //		body	false	string
        label: '', //	模糊查找	body	false	string
        page: 1,
        limit: 20,
        dateRange: [],
        startDate: '', //		body	false	string
        communityId: '', // 小区id
        communityName: '',
        status: '', //	0:进行中 1:已结束
      },

      // 下拉框
      communityList: [], // 小区

      // 二维码
      qrState: false,
      qrData: '',
      qrTitle: '',
    }
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.getCommunityList()

    this.searchFunc()
  },

  methods: {
    // << 弹窗 二维码
    showQrDlg(row) {
      this.qrData = row
      this.qrTitle = row.name
      this.qrState = true
      setTimeout(() => {
        // row.qrCodeStr
        this.setQrcode2(row.qrCodeUrl)
      }, 200)
    },
    setQrcode2(val) {
      let qrcode = document.getElementById('QRCode2')
      // 将获取到的数据（val）画到qrcode（canvas）上
      QRCode.toCanvas(
        qrcode,
        val,
        {
          scale: 6,
        },
        function (error) {
          console.log(error)
        }
      )
    },
    // >> 弹窗 二维码
    // << --- 弹窗 ---
    // -- 表单弹窗
    showDlg(type, row) {
      if (type == 'add') {
        this.dlgQuery = { id: 0 }
      } else {
        if (row.status == '1') {
          type = 'info'
        }
        this.dlgQuery = row
      }
      this.dlgType = type
      this.dlgState = true
    },
    // 关闭弹窗
    closeDlg() {
      this.dlgState = false
    },

    // 详情弹窗
    // -- 表单弹窗
    showInfoDlg(row) {
      this.dlgInfoQuery = row
      this.dlgInfoType = 'info'
      this.dlgInfoState = true
    },
    // 关闭弹窗
    closeInfoDlg() {
      this.dlgInfoState = false
    },
    // >> --- 弹窗 ---
    //////
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
        projectId: this.userInfo.projectId,
      }
      console.log('postParam', postParam)
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // << 列表
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    // 获取数据
    getList() {
      let sendObj = JSON.parse(JSON.stringify(this.listQuery))
      sendObj.projectId = this.userInfo.projectId
      if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
        sendObj.startDate = sendObj.dateRange[0]
        sendObj.endDate = sendObj.dateRange[1]
      }
      delete sendObj.dateRange

      this.listLoading = true
      postAction('/unity/activity/page', sendObj).then((res0) => {
        this.listLoading = false
        let res = res0.data
        if (res.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data))

          this.list = JSON.parse(JSON.stringify(list))
          this.total = res.page.total ? res.page.total : 0
          // this.formatList()
        } else {
          this.list = []
          this.total = 0
          this.$message.warning(res.msg)
        }
      })
    },
    // >> 列表

    // 删除
    delItem(data, flag) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        getAction(`/unity/activity/del/${data.id}`).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
.qrcode-dom {
  text-align: center;
  .qrcode-canvas {
    width: 200px !important;
    height: 200px !important;
  }
}
</style>


