<template>
  <div class="mazhenguo">
    <div class="header">
      <el-menu
        :default-active="value"
        class="el-menu-demo blue"
        mode="horizontal"
        @select="handleSelect"
      >
        <el-menu-item index="baseSetting" @click="to('baseSetting')"
          >① 基础信息</el-menu-item
        >
        <el-menu-item index="formSetting" @click="to('formSetting')"
          >② 内容模板</el-menu-item
        >

      </el-menu>
      <div class="publish">

        <el-button size="mini" type="primary" @click="publish"
          ><i class="el-icon-s-promotion"></i>发布</el-button
        >
      </div>
      <div class="back">
        <el-button
          @click="exit"
          size="medium"
          icon="el-icon-arrow-left"
          circle
        ></el-button>
        <span>
          <i
            :class="setup.logo.icon"
            :style="'background:' + setup.logo.background"
          ></i>
          <span>{{ setup.name }}</span>
        </span>
      </div>
    </div>

    <el-dialog
      title="请使用手机扫码预览"
      :visible.sync="viewCode"
      width="300px"
      :close-on-click-modal="false"
      center
    >
      <img src="@/assets/workFlow/image/code.png" width="250" height="250" />
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "LayoutHeader",
  props: {
    value: {
      type: String,
      default: "baseSetup"
    }
  },
  data() {
    return {
      viewCode: false
    };
  },
  computed: {
    setup() {
      return this.$store.state.design;
    }
  },
  created() {
    this.check();
  },
  mounted() {
    console.log(document.body.offsetWidth);
    if (document.body.offsetWidth <= 970) {
      this.$msgbox.alert(
        "本设计器未适配中小屏幕，建议您在PC电脑端浏览器进行操作"
      );
    }
    this.listener();
  },
  methods: {
    publish() {
      this.$emit("publish");
    },
    save() {
      this.$emit("save");
    },
    exit() {
      this.$confirm("未发布的内容将不会被保存，是否直接退出 ?", "提示", {
        confirmButtonText: "退出",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {

        this.$router.push("/deviceMan/deviceFileMan/runningRecordTempMan");
      });
    },
    to(path) {
      this.$emit("input", path);
    },
    handleSelect(key, keyPath) {
      console.log(key, keyPath);
    },
    listener() {
      window.onunload = this.closeBefore();
      window.onbeforeunload = this.closeBefore();

    },
    closeBefore() {
      //alert("您将要离开本页")
      return false;
    },
    check() {
      if (this.$store.state.isEdit === null) {
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~@/assets/workFlow/theme.scss";
/deep/ .header {
  min-width: 980px;
  position: relative;

  .el-menu {
    top: 0;
    z-index: 999;
    display: flex;
    justify-content: center;
    width: 100%;

    .el-menu-item.is-active {
      color: $theme-primary;
    }
  }

  .publish {
    position: absolute;
    top: 15px;
    right: 20px;
    z-index: 1000;

    i {
      margin-right: 6px;
    }

    button {
      border-radius: 15px;
    }
  }

  .back {
    position: absolute;
    z-index: 1000;
    top: 10px;
    left: 20px;
    font-size: small;

    span {
      i {
        border-radius: 10px;
        padding: 7.8px;
        font-size: 20px;
        color: #ffffff;
        margin: 0 10px;
      }
    }
  }
}
</style>
