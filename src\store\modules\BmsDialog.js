const bmsDialog = {
  state: {
    bmsState: false,
    bmsArr: [], // [{id: '', label: ''}]
    bmsType: "",
    bmsOtherQuery: ""
    // bmsIds: '',
    // bmsNames: ''
  },

  mutations: {
    SET_BMSSTATE: (state, val) => {
      state.bmsState = val;
    },
    SET_BMSARR: (state, val) => {
      state.bmsArr = JSON.parse(JSON.stringify(val));
    },
    SET_BMS_TYPE: (state, val) => {
      state.bmsType = JSON.parse(JSON.stringify(val));
    },
    SET_BMS_OTHERQUERY: (state, val) => {
      state.bmsOtherQuery = JSON.parse(JSON.stringify(val));
    }
  },

  actions: {}
};

export default bmsDialog;
