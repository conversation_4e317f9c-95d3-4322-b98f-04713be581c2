// 病房dlg组件

const wardDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    branchId: '',

    wardId: '',

    wardName: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    branchId: state => state.branchId,

    wardId: state => state.wardId,

    wardName: state => state.wardName
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_BRANCHID: (state, val) => {
      state.branchId = val
    },

    SET_WARDID: (state, val) => {
      state.wardId = val
    },

    SET_WARDNAME: (state, val) => {
      state.wardName = val
    }
  },

  actions: {

  }
}

export default wardDlg
