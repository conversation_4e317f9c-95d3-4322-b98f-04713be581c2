import onBudgetApplyDlg from './onBudgetApplyDlg.js' // 预算变更dlg
import onReimburseApplyDlg from './onReimburseApplyDlg.js' // 报销申请dlg
import onAccountApplyDlg from './onAccountApplyDlg.js' // 挂账申请dlg
import onBorrowApplyDlg from './onBorrowApplyDlg.js' // 借款申请dlg
import onRefundApplyDlg from './onRefundApplyDlg.js' // 还款申请dlg
import onReturnApplyDlg from './onReturnApplyDlg.js' // 回款申请dlg
import supplierDlg from './supplierDlg.js' // 供应商dlg
import inStorageDlg from './inStorageDlg.js' // 入库单dlg
import purchaseDlg from './purchaseDlg.js' // 采购单
import onBorrowDlg from './onBorrowDlg.js' // 借款单dlg
import onAccountDlg from './onAccountDlg.js' // 挂账单dlg
import subjectDlg from './subjectDlg.js' // 科目dlg
import subjectMulDlg from './subjectMulDlg.js' // 科目多选dlg
import branchDlg from './branchDlg.js' // 部门dlg
import branchMulDlg from './branchMulDlg.js' // 部门多选dlg
import userDlg from './userDlg.js' // 用户dlg
import commonPayDlg from './commonPayDlg.js' // 常用支付方式dlg
import cashierDlg from './cashierDlg.js' // 生成出纳dlg
import budgetWageDlg from './budgetWageDlg.js' // 预算工资dlg
import budgetMaterialDlg from './budgetMaterialDlg.js' // 预算材料dlg

const financialSystem = {
  namespaced: true,
  modules: {
    onBudgetApplyDlg,
    onReimburseApplyDlg,
    onAccountApplyDlg,
    onBorrowApplyDlg,
    onRefundApplyDlg,
    onReturnApplyDlg,
    supplierDlg,
    inStorageDlg,
    purchaseDlg,
    onBorrowDlg,
    onAccountDlg,
    subjectDlg,
    subjectMulDlg,
    branchDlg,
    branchMulDlg,
    userDlg,
    commonPayDlg,
    cashierDlg,
    budgetWageDlg,
    budgetMaterialDlg
  }
}

export default financialSystem
