<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="选择小区">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报修类型">
          <el-select v-model="listQuery.repairType" filterable clearable placeholder="请选择报修类型">
            <el-option v-for="item in repairTypeListQuery" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="时间筛选：">
          <el-date-picker v-model="listQuery.rangeDate" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item> -->
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入报修人、报修电话、位置' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="报修人">
          <template slot-scope="scope">
            <span>{{ scope.row.repairName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="联系方式">
          <template slot-scope="scope">
            <span>{{ scope.row.tel }}</span>
          </template>
        </el-table-column>

        <el-table-column label="预约时间">
          <template slot-scope="scope">
            <span>{{ scope.row.appointmentTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报修类型">
          <template slot-scope="scope">
            <span>{{ scope.row.repairTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="维修费用(元)">
          <template slot-scope="scope">
            <span>{{ scope.row.priceScope }}</span>
          </template>
        </el-table-column>

        <el-table-column label="维修内容">
          <template slot-scope="scope">
            <span>{{ scope.row.context }}</span>
          </template>
        </el-table-column>

        <el-table-column label="位置">
          <template slot-scope="scope">
            <div>小区:{{ scope.row.communityName }}</div>
            <div>房屋:{{ scope.row.fangwu }}</div>
          </template>
        </el-table-column>

        <el-table-column label="状态">
          <template slot-scope="scope">
            <span>{{ scope.row.stateName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')">详情</el-button>
            <el-button type="primary" size="mini" icon="el-icon-check" plain @click="sendItem(scope.row)">回单</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="处理/查看工单池" :visible.sync="dlgShow" width='800px' append-to-body top="30px">

      <el-form ref="dlgForm" :disabled="dlgType === 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <template v-if="dlgType === 'EDIT'">
          <el-form-item label="报修范围" prop="repairObjType">
            <el-select v-model="dlgData.repairObjType" filterable clearable placeholder="请选择报修范围">
              <el-option v-for="item in objTypeList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="归属小区" prop="communityId" v-if="dlgData.repairObjType >= 1">
            <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
              <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属楼栋" prop="loudongId" v-if="dlgData.repairObjType >= 2">
            <el-select v-model="dlgData.loudongId" filterable clearable placeholder="请选择楼栋" @change="floorChange">
              <el-option v-for="item in buildingList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属单元" prop="danyuanId" v-if="dlgData.repairObjType >= 3">
            <el-select v-model="dlgData.danyuanId" filterable clearable placeholder="请选择单元" @change="unitChange">
              <el-option v-for="item in unitList" :key="item.id" :label="item.unitName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="归属房屋" prop="fangwuId" v-if="dlgData.repairObjType >= 4">
            <el-select v-model="dlgData.fangwuId" filterable clearable placeholder="请选择房屋">
              <el-option v-for="item in roomList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="报修类型" prop="repairType">
            <el-select v-model="dlgData.repairType" filterable clearable placeholder="请选择报修类型" @change="repairTypeChange">
              <el-option v-for="item in repairTypeList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="报修人" prop="repairName">
            <el-input v-model="dlgData.repairName" placeholder="请填写报修人姓名"></el-input>
          </el-form-item>

          <el-form-item label="联系方式" prop="tel">
            <el-input v-model="dlgData.tel" placeholder="请填写联系方式"></el-input>
          </el-form-item>

          <el-form-item label="预约时间" prop="appointmentTime">
            <el-date-picker :picker-options="pickerOptions" v-model="dlgData.appointmentTime" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" placeholder="预约时间">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="维修费(元)" prop="priceScope">
            <el-input-number v-model="dlgData.priceScope" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
          </el-form-item>

          <el-form-item label="报修内容" prop="context">
            <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.context" placeholder="请输入说明" />
          </el-form-item>
        </template>
        <template v-else>
          <el-row>
            <el-col :span="8">
              <el-form-item label="工单编码:">
                {{dlgData.id}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报修类型:">
                {{dlgData.repairTypeText}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报修人:">
                {{dlgData.repairName}}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="联系方式:">
                {{dlgData.tel}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="位置:">
                {{dlgData.communityName}} {{dlgData.fangwu}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预约时间:">
                {{dlgData.appointmentTime}}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="状态:">
                {{dlgData.stateName}}
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="报修内容:">
                {{dlgData.context}}
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="left">工单流转</el-divider>
          <el-table class='m-small-table' :data="dlgData.list" border fit highlight-current-row>
            <el-table-column label="序号" type="index" align="center" width="60">
            </el-table-column>

            <el-table-column label="处理人">
              <template slot-scope="scope">
                <span>{{ scope.row.staffName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="状态">
              <template slot-scope="scope">
                <span>{{ stateMap[scope.row.state] }}</span>
              </template>
            </el-table-column>

            <el-table-column label="处理时间">
              <template slot-scope="scope">
                <span>{{ scope.row.createTime }}</span>
              </template>
            </el-table-column>

            <el-table-column label="耗时(分钟)">
              <template slot-scope="scope">
                <span>{{ scope.row.useTime }}</span>
              </template>
            </el-table-column>

            <el-table-column label="意见">
              <template slot-scope="scope">
                <span>{{ scope.row.remark }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-divider content-position="left">附件信息</el-divider>
          <el-form-item label="语音:" v-if="dlgData.voiceUrl">
            <audio controls style="width: 280px; height:36px" :src="dlgData.voiceUrl"></audio>
          </el-form-item>
          <el-form-item label="照片:" v-if="dlgData.imgUrl">
            <el-image v-for="(item, index) in dlgData.imgUrl.split(',')" :key="index" :src="item" :preview-src-list="[item]">
            </el-image>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal='false' title="回单" :visible.sync="dlgShowSend" width='600px' append-to-body>

      <el-form ref="dlgFormSend" :rules="rules" :model="dlgData" label-position="right" label-width="120px">
        <el-form-item label="应收费用(元)：">
          {{dlgData.priceScope}}
        </el-form-item>
        <el-form-item label="实收费用(元)：" prop="payMoney" v-if="dlgData.isPay === 'T'">
          <el-input-number v-model="dlgData.payMoney" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item label="处理结果：" prop="remark">
          <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.remark" placeholder="请输入处理结果" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShowSend = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlgSend" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <userDlg />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, cofloorCommunity, buildingunitFloor, buildingroomPage } from '@/api/communityMan'

import {
  repairSettingPage,
  repairpoolPage,
  repairpoolAddRepairPool,
  updateRepairPoolFlag,
  repairpoolHuidan,
  repairpoolFindOne,
} from '@/api/repairMan'

import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import userDlg from '@/components/Dialog/communityMan/userDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: 0,
  communityId: '',
  communityName: '',
  danyuan: '',
  danyuanId: '',
  fangwu: '',
  fangwuId: '',
  loudong: '',
  loudongId: '',
  priceScope: '',
  repairName: '',
  repairObjName: '',
  repairObjType: '',
  repairType: '',
  repairTypeText: '',
  tel: '',
  type: '',
  userName: '',
  userId: '',
  remark: '',
  payMoney: '',
  list: [],
}


export default {
  name: 'repairTodo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    userDlg,
  },
  data () {
    return {

      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - (1000 * 60 * 60 * 24);
        },
      },
      // 弹窗 状态
      dlgShowSend: false,
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        danyuanId: [{ required: true, message: '必填字段', trigger: 'change' }],
        fangwuId: [{ required: true, message: '必填字段', trigger: 'change' }],
        loudongId: [{ required: true, message: '必填字段', trigger: 'change' }],
        payMoney: [{ required: true, message: '必填字段', trigger: 'blur' }],
        repairName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        context: [{ required: true, message: '必填字段', trigger: 'blur' }],
        repairObjType: [{ required: true, message: '必填字段', trigger: 'change' }],
        repairType: [{ required: true, message: '必填字段', trigger: 'change' }],
        appointmentTime: [{ required: true, message: '必填字段', trigger: 'change' }],
        userName: [{ required: true, message: '必填字段', trigger: 'change' }],
        remark: [{ required: true, message: '必填字段', trigger: 'blur' }],
        tel: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: '号码格式有误！',
            trigger: 'blur'
          }
        ],
      },
      stateMap: {
        "0": '提交',
        "1": '派人',
        "2": '接单',
        "3": '回单',
        "4": '评价',
        "5": '回访',
        "8": '修改',
        "9": '作废',
        "10": '重派给其他人',
      },
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        rangeDate: '',
        status: '1',
        repairType: '',
        logType: '1',
        type: ''
      },
      userInfo: {},
      communityList: [],
      buildingList: [],
      unitList: [],
      roomList: [],
      repairTypeList: [],
      repairTypeListQuery: [],
      objTypeList: [
        {
          id: '1',
          name: '小区'
        },
        {
          id: '2',
          name: '楼栋'
        },
        {
          id: '3',
          name: '单元'
        },
        {
          id: '4',
          name: '房屋'
        },
      ],
      stateList: [
        {
          id: '0',
          name: '未派单'
        },
        {
          id: '1',
          name: '已接单'
        },
        {
          id: '2',
          name: '待评价'
        },
        {
          id: '3',
          name: '待回访'
        },
        {
          id: '4',
          name: '待评价待回访'
        },
        {
          id: '5',
          name: '待评价已回访'
        },
        {
          id: '6',
          name: '已评价待回访'
        },
        {
          id: '7',
          name: '已结束'
        },
      ],
    }
  },

  computed: {
    ...mapGetters('communityMan/userDlg', {
      userId: 'userId',
      userName: 'userName',
    }),
  },
  watch: {
    userId (val) {
      this.dlgData.userId = val
    },

    userName (val) {
      this.dlgData.userName = val
    },

  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 显示人员dlg
    showUserDlg () {
      let userId = this.dlgData.userId
      let userName = this.dlgData.name
      this.$store.commit('communityMan/userDlg/SET_USERID', userId)
      this.$store.commit('communityMan/userDlg/SET_USERNAME', userName)
      this.$store.commit('communityMan/userDlg/SET_DLGSHOW', true)
    },

    communityChange () {
      let communityId = this.dlgData.communityId
      if (this.dlgShow) {
        this.dlgData.loudongId = ""
        this.dlgData.danyuanId = ""
        this.dlgData.repairType = ""
      } else {
        communityId = this.listQuery.communityId
        this.listQuery.repairType = ""
      }
      this.getBuildingList(communityId)
      this.getRepairTypeList()
      this.getRoomList()
    },

    floorChange () {
      let floorId = this.dlgData.loudongId
      this.dlgData.danyuanId = ""
      this.getUnitList(floorId)
      this.getRoomList()
    },

    unitChange () {
      this.getRoomList()
    },

    repairTypeChange () {
      let item = this.repairTypeList.filter(item => {
        return item.id = this.dlgData.repairType
      })
      if (item.length > 0) {
        this.dlgData.priceScope = item[0]['priceScope']
      }
    },

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取楼栋列表
    getBuildingList (id) {
      if (utils.isNull(id)) {
        return
      }
      cofloorCommunity(id).then(res => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data
        }
      })
    },

    // 获取单元列表
    getUnitList (id) {
      if (utils.isNull(id)) {
        return
      }
      buildingunitFloor(id).then(res => {
        if (res.data.code == 200) {
          this.unitList = res.data.data
        }
      })
    },

    // 获取房屋列表
    getRoomList () {
      if (this.dlgData.repairObjType != 4) {
        return
      }
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.dlgData.communityId,
        floorId: this.dlgData.loudongId,
        unitId: this.dlgData.danyuanId
      }
      buildingroomPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.roomList = res.data.data
        }
      })
    },

    // 获取报修类型
    getRepairTypeList () {
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.listQuery.communityId
      }
      if (this.dlgShow) {
        postParam.communityId = this.dlgData.communityId
      }
      repairSettingPage(postParam).then(res => {
        if (res.data.code == 200) {
          if (this.dlgShow) {
            this.repairTypeList = res.data.data
          } else {
            this.repairTypeListQuery = res.data.data
          }
        }
      })
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    formatList () {
      for (let i of this.list) {
        i.stateName = utils.getNameById(i.state, this.stateList)
      }
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      this.listQuery.startDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[0]
      this.listQuery.endDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[1]
      repairpoolPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.formatList()
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 派工提交
    subDlgSend () {
      this.$refs['dlgFormSend'].validate((valid) => {
        if (valid) {
          let postParam = {
            userId: Cookie.get("userId"),
            userName: Cookie.get("userName"),
            repairId: this.dlgData.id,
            remark: this.dlgData.remark,
            payMoney: this.dlgData.payMoney
          }
          this.dlgLoading = true
          repairpoolHuidan(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShowSend = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          postParam.danyuan = utils.getNameById(postParam.danyuanId, this.unitList)
          postParam.loudong = utils.getNameById(postParam.loudongId, this.buildingList)
          postParam.fangwu = utils.getNameById(postParam.fangwuId, this.roomList, 'id', 'roomFullName')
          postParam.repairObjName = utils.getNameById(postParam.repairObjType, this.objTypeList)
          postParam.repairTypeText = utils.getNameById(postParam.repairType, this.repairTypeList)
          postParam.creator = Cookie.get('userId')
          postParam.staffName = Cookie.get('userName')
          this.dlgLoading = true
          repairpoolAddRepairPool(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgType = type
      this.dlgShow = true
      if (type === 'VIEW') {
        repairpoolFindOne(data.id).then(res => {
          if (res.data.code == 200) {
            this.dlgData.list = JSON.parse(JSON.stringify(res.data.data.logList))
          }
        })
      } else {
        this.getBuildingList(this.dlgData.communityId)
        this.getUnitList(this.dlgData.loudongId)
        this.getRoomList()
        this.getRepairTypeList()
      }
    },

    // 派单
    sendItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgShowSend = true
      this.dlgType = 'SEND'
      this.$nextTick(() => {
        this.$refs['dlgFormSend'].clearValidate()
      })
    },

    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateRepairPoolFlag(data.id, flag).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/ownerInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-image {
  width: 150px;
  height: 150px;
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>


