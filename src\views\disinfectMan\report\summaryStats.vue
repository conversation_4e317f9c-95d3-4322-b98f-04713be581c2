<!-- 汇总统计 -->
<template>
  <div class="app-container" style="overflow: auto">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="选择科室:">
          <el-input
            @focus="showKeshiDlg"
            class=""
            placeholder="请选择科室"
            v-model="listQuery.departmentName"
            :title="listQuery.departmentName"
            style="width: 200px"
            readonly
          >
          </el-input>
        </el-form-item>
        <el-form-item label="消毒时间:">
          <el-date-picker
            class=""
            style="width: 230px"
            v-model="listQuery.dateRange"
            @change="searchFunc"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="mini"
          >
          </el-date-picker>
        </el-form-item>

        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchFunc"
          >搜索</el-button
        >
      </el-form>
    </div>

    <div v-if="showChart1">
      <div style="margin: 20px 0; font-weight: bold; font-size: 16px">
        消毒类型统计
      </div>
      <div
        v-if="showChart1"
        id="echart-bar1"
        style="height: 400px; margin-top: 16px"
      ></div>
    </div>
    <div v-if="showChart2">
      <div style="margin: 20px 0; font-weight: bold; font-size: 16px">
        点位消毒次数统计
      </div>
      <div id="echart-bar2" style="height: 400px; margin-top: 16px"></div>
    </div>

    <selectBmDlgMul
      :dlgState0="dlgKeshiState"
      :dlgType="dlgKeshiType"
      :dlgQuery="dlgKeshiQuery"
      :dlgSelectData="dlgKeshiSelectData"
      :isRole="false"
      title="科室"
      @closeDlg="closeKeshiDlg"
      @backFunc="dlgKeshibackFunc"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import * as utils from "@/utils";
import { postAction, getAction } from "@/api";
import selectBmDlgMul from "@/components/Dialog2/selectBmDlgMul";

export default {
  components: {
    selectBmDlgMul
  },
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),

      ////
      // 弹窗
      dlgKeshiQuery: {},
      dlgKeshiState: false,
      dlgKeshiType: "", // 弹框状态add, edit
      dlgKeshiSelectData: "",

      ////

      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        departmentId: "",
        departmentName: "",
        dateRange: []
      },

      showChart1: false,
      showChart2: false
    };
  },

  created() {
    // console.log('==', JSON.parse(window.localStorage.userInfo))
    let userInfo = JSON.parse(window.localStorage.userInfo);
    // console.log('===userInfo', userInfo)
    this.listQuery.departmentId = userInfo.departmentId + "";
    this.listQuery.departmentName = userInfo.departmentName + "";

    this.listQuery.dateRange = utils.returnToMonth();
  },
  mounted() {
    this.searchFunc();
  },

  methods: {
    /////////////
    showKeshiDlg() {
      this.dlgKeshiSelectData = this.listQuery.departmentId
        ? this.listQuery.departmentId.split(",")
        : "";
      this.dlgKeshiState = true;
    },
    closeKeshiDlg() {
      this.dlgKeshiState = false;
    },
    dlgKeshibackFunc(data) {
      this.listQuery.departmentId = data.ids;
      this.listQuery.departmentName = data.names;
      this.listQuery.pointEquipId = "";
      this.searchFunc();
    },
    // 柱状图
    setEchartBar1(arr) {
      // arr = [
      //   { name: 'aa', averageTemp: 10 },
      //   { name: 'bb', averageTemp: 50 },
      //   { name: 'cc', averageTemp: 100 },
      // ]
      //
      let xData = [];
      let yList = [];
      for (let item of arr) {
        xData.push(item.pointTypeName);
        yList.push(item.count);
      }

      var myChart = echarts.init(document.getElementById("echart-bar1"));

      var option = {
        grid: {
          top: "10",
          left: "4%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        tooltip: {
          trigger: "axis"
          // axisPointer: {
          //   type: 'shadow',
          // },
        },
        xAxis: {
          type: "category",
          boundaryGap: true,
          data: xData,

          axisLabel: {
            rotate: xData.length > 5 ? 45 : 0
          }
        },
        yAxis: [
          {
            type: "value",

            nameTextStyle: {
              color: "#aaa"
            }
          }
        ],

        series: [
          {
            name: "",
            type: "bar",
            barWidth: "30",
            data: yList,
            itemStyle: {
              // 不同颜色
              normal: {
                color: function(params) {
                  var colorList0 = [
                    "#3BA0FF",
                    "#36CBCB",
                    "#4DCB73",
                    "#FAD337",
                    "#F2637B",
                    "#9560E0",
                    "#5D7092",
                    "#FF9845",
                    "#F6BD16",
                    "#6DC8EC"
                  ];
                  let colorList = [];
                  if (xData.length > colorList0.length) {
                    for (let i = 0; i < xData.length; i++) {
                      let index = i % colorList0.length;

                      colorList.push(colorList0[index]);
                    }
                  } else {
                    colorList = colorList0;
                  }
                  return colorList[params.dataIndex];
                }
              }
            }
          }
        ]
      };

      myChart.setOption(option);

      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    setEchartBar2(arr) {
      // arr = [
      //   { name: 'aa', averageTemp: 10 },
      //   { name: 'bb', averageTemp: 50 },
      //   { name: 'cc', averageTemp: 100 },
      // ]
      // //
      let xData = [];
      let yList = [];
      for (let item of arr) {
        xData.push(item.pointName);
        yList.push(item.count);
      }

      var myChart = echarts.init(document.getElementById("echart-bar2"));

      var option = {
        grid: {
          top: "10",
          left: "4%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        tooltip: {
          trigger: "axis"
          // axisPointer: {
          //   type: 'shadow',
          // },
        },
        xAxis: {
          type: "category",
          boundaryGap: true,
          data: xData,

          axisLabel: {
            interval: 0,
            rotate: xData.length > 5 ? 45 : 0
          }
        },
        yAxis: [
          {
            type: "value",

            nameTextStyle: {
              color: "#aaa"
            }
          }
        ],

        series: [
          {
            name: "",
            type: "bar",
            barWidth: "30",
            data: yList,
            itemStyle: {
              normal: {
                color: function(params) {
                  var colorList0 = [
                    "#3BA0FF",
                    "#36CBCB",
                    "#4DCB73",
                    "#FAD337",
                    "#F2637B",
                    "#9560E0",
                    "#5D7092",
                    "#FF9845",
                    "#F6BD16",
                    "#6DC8EC"
                  ];
                  let colorList = [];
                  if (xData.length > colorList0.length) {
                    for (let i = 0; i < xData.length; i++) {
                      let index = i % colorList0.length;

                      colorList.push(colorList0[index]);
                    }
                  } else {
                    colorList = colorList0;
                  }
                  return colorList[params.dataIndex];
                }
              }
            }
          }
        ]
      };
      myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
    },

    // << 列表
    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    // 获取数据
    getList() {
      this.showChart1 = this.showChart2 = false;
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));

      sendObj.beginDate = "";
      sendObj.endDate = "";
      if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
        sendObj.beginDate = sendObj.dateRange[0];
        sendObj.endDate = sendObj.dateRange[1];
      }
      delete sendObj.dateRange;

      sendObj.departmentIds = sendObj.departmentId;
      sendObj.departmentNames = sendObj.departmentName;
      delete sendObj.departmentId;
      delete sendObj.departmentName;

      this.listLoading = true;
      postAction("/cloth/dis-record/summaryStatistics", sendObj).then(res0 => {
        this.listLoading = false;
        let res = res0.data;
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.$message.warning("暂无数据");
            return false;
          }

          if (
            !utils.isNull(res.data.pointTypeCounts) &&
            res.data.pointTypeCounts.length > 0
          ) {
            this.showChart1 = true;
            setTimeout(() => {
              this.setEchartBar1(res.data.pointTypeCounts);
            }, 100);
          }
          if (
            !utils.isNull(res.data.pointCounts) &&
            res.data.pointCounts.length > 0
          ) {
            this.showChart2 = true;
            setTimeout(() => {
              this.setEchartBar2(res.data.pointCounts);
            }, 100);
          }
        } else {
          this.$message.error(res.msg);
        }
      });
    }
    // >> 列表
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>
