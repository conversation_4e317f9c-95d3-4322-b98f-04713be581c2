/** 品质系统 **/
import Layout from "@/views/layout/Layout";
const qualitySystemRouter = {
  path: "/qualitySystem",
  component: Layout,
  name: "qualitySystem",
  meta: {
    title: "品质系统",
    icon: "pzxt",
    roles: ["pinzhixitong"]
  },
  alwaysShow: true,
  children: [
    {
      path: "teamConfig",
      component: () => import("@/views/qualitySystem/teamConfig/index"),
      name: "班组管理",
      meta: {
        title: "班组管理",
        roles: ["banzuguanli"]
      },
      children: []
    },

    // {
    //   path: "workInstruction",
    //   component: () => import("@/views/qualitySystem/workInstruction/index"),
    //   meta: {
    //     title: "作业指导书"
    //     // roles: ["aaaa"]
    //   },
    //   alwaysShow: true,
    //   children: [
    //     {
    //       path: "workItemSetup",
    //       component: () =>
    //         import("@/views/qualitySystem/workInstruction/workItemSetup"),
    //       name: "工作项设置",
    //       meta: {
    //         title: "工作项设置"
    //         // roles: ["aaaa"]
    //       },
    //       children: []
    //     },
    //     {
    //       path: "workScheduleSetup",
    //       component: () =>
    //         import("@/views/qualitySystem/workInstruction/workScheduleSetup"),
    //       name: "作业规程设置",
    //       meta: {
    //         title: "作业规程设置"
    //         // roles: ["aaaa"]
    //       },
    //       children: []
    //     },
    //     {
    //       path: "workFlowTmpl",
    //       component: () =>
    //         import("@/views/qualitySystem/workInstruction/workFlowTmpl"),
    //       name: "作业流程模板",
    //       meta: {
    //         title: "作业流程模板"
    //         // roles: ["aaaa"]
    //       },
    //       children: []
    //     }
    //   ]
    // },
    {
      path: "projectWorkSetup",
      component: () => import("@/views/qualitySystem/projectWorkSetup/index"),
      meta: {
        title: "项目作业设置",
        roles: ["xiangmuzuoyeshezhi"]
      },
      alwaysShow: true,
      children: [
        // {
        //   path: "workArea",
        //   component: () =>
        //     import("@/views/qualitySystem/projectWorkSetup/workArea"),
        //   name: "作业区域",
        //   meta: {
        //     title: "作业区域"
        //     // roles: ["aaaa"]
        //   },
        //   children: []
        // },
        {
          path: "permissionMan",
          component: () =>
            import("@/views/qualitySystem/projectWorkSetup/permissionMan"),
          name: "管理权限",
          meta: {
            title: "管理权限",
            roles: ["guanliquanxian"]
            // keepAlive: true
          },
          children: []
        },
        {
          path: "workFlow",
          component: () =>
            import("@/views/qualitySystem/projectWorkSetup/workFlow/index"),
          name: "作业流程",
          meta: {
            title: "作业流程",
            roles: ["zuoyeliucheng"]
            // keepAlive: true
          },
          children: []
        },
        {
          path: "batchWorkStartstop",
          component: () =>
            import(
              "@/views/qualitySystem/projectWorkSetup/batchWorkStartstop/index"
            ),
          name: "批量任务启停",
          meta: {
            title: "批量任务启停",
            roles: ["piliangrenwuqiting"]
          }
        }
      ]
    },
    {
      path: "projectPerform",
      component: () => import("@/views/qualitySystem/projectPerform/index"),
      meta: {
        title: "项目执行",
        roles: ["xiangmuzhixing"]
      },
      alwaysShow: true,
      children: [
        {
          path: "projectWorkOutline",
          component: () =>
            import("@/views/qualitySystem/projectPerform/projectWorkOutline"),
          name: "项目作业大纲",
          meta: {
            title: "项目作业大纲",
            roles: ["renwuguanli"]
          },
          children: []
        },
        {
          path: "performMan",
          component: () =>
            import("@/views/qualitySystem/projectPerform/performMan"),
          name: "作业执行管理",
          meta: {
            title: "作业执行管理",
            roles: ["zhixingguanli"]
          },
          children: []
        },
        {
          path: "taskMan",
          component: () =>
            import("@/views/qualitySystem/projectPerform/taskMan"),
          name: "作业任务管理",
          meta: {
            title: "作业任务管理",
            roles: ["renwuguanli"],
            keepAlive: true
          },
          children: []
        },
        //品质7月改
        // {
        //   path: "taskManNew",
        //   component: () =>
        //     import("@/views/qualitySystem/projectPerform/taskManNew"),
        //   name: "作业任务管理",
        //   meta: {
        //     title: "作业任务管理",
        //     roles: ["renwuguanli_old"],
        //     keepAlive: true
        //   },
        //   children: []
        // }
      ]
    },

    {
      path: "statisticalAnalysis",
      component: () => import("@/views/qualitySystem/statisticalAnalysis"),
      name: "统计分析",
      meta: {
        title: "统计分析",
        roles: ["pinzhifenxi_web"]
      }
    },
    //品质7月改
    // {
    //   path: "statisticalAnalysisNew",
    //   component: () => import("@/views/qualitySystem/statisticalAnalysisNew"),
    //   name: "统计分析",
    //   meta: {
    //     title: "统计分析",
    //     roles: ["pinzhifenxi_old"]
    //   }
    // }
  ]
};

export default qualitySystemRouter;
