import axios from "axios";
import { Message } from "element-ui";
import store from "@/store";
import Cookie from "js-cookie";

import router from "@/router";

// import { BASIC_TOKEN } from "@/configs/const";
let BASIC_TOKEN = "d2ViLWFwcDphc2Q0cnhjeA==";

// create an axios instance
let service = axios.create({
  baseURL: "/saapi", // saapi 的 base_url
  timeout: 30000 // request timeout
});

// request interceptor
service.interceptors.request.use(
  config => {
    let noTokenUrl = [
      "sendSms", // 发送验证码
      "checkUserPhone" // 验证手机号是否重复
    ];
    let isNoToken = noTokenUrl.some(item => {
      return config.url.indexOf(item) >= 0;
    });
    console.log('===isNoToken', isNoToken)

    if (!isNoToken) {
      let Token = Cookie.get("Token");
      console.log('=====Token', Token)
      if (Token != BASIC_TOKEN && Token) {
        config.headers["Authorization"] = "Bearer " + Token;
      }
    }

    return config;
  },
  error => {
    // Do something with request error
    Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  // 306
  response => response,

  /**
   * 下面的注释为通过在response里，自定义code来标示请求状态
   * 当code返回如下情况则说明权限有问题，登出并返回到登录页
   * 如想通过 xmlhttprequest 来状态码标识 逻辑可写在下面error中
   * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
   */
  // response => {
  //   const res = response.data
  //   if (res.code == 306) {
  //     Message({
  //       type: 'warning',
  //       message: '您已在其他地方登录，如非本人操作，请及时修改密码^_^',
  //       offset: 20
  //     })
  //     router.push('/login')
  //     Cookie.set('Token', '')
  //     return Promise.reject('error')
  //   } else {
  //     return response
  //   }
  // },

  error => {
    console.log("error", error.response.data);
    console.log(Object.prototype.toString.apply(error.response.data));
    let code = error.response.data.code;
    if (code == 306) {
      Cookie.set("resCode", 306);
      Cookie.set("Token", "");
      router.push("/login");
      return Promise.reject("error");
    } else if (code == 401) {
      Cookie.set("resCode", 401);
      Cookie.set("Token", "");
      router.push("/login");
    } else if (
      Object.prototype.toString.apply(error.response.data).indexOf("String") >=
      0
    ) {
      return Promise.reject(error);
    } else {
      Message({
        message: "服务器错误，请联系系统管理员",
        type: "error",
        duration: 5 * 1000
      });
      return Promise.reject(error);
      // return false
    }
  }
);

export default service;
