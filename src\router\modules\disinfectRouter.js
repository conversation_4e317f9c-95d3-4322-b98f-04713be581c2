/** 消毒管理 **/
import Layout from "@/views/layout/Layout";

const disinfectRouter = {
  path: "/disinfectRouter",
  component: Layout,
  name: "disinfectRouter",
  meta: {
    title: "消毒管理",
    icon: "xdgl",
    roles: ["xia<PERSON><PERSON><PERSON><PERSON>"]
  },

  children: [
    {
      path: "disinfectConfig",
      component: () => import("@/views/disinfectMan/disinfectConfig/index"),
      meta: {
        title: "基础信息维护",
        roles: ["jichuxinxiweihu"]
      },
      children: [
        {
          path: "pointType",
          component: () =>
            import("@/views/disinfectMan/disinfectConfig/pointType"),
          name: "点位类型设置",
          meta: {
            title: "点位类型设置",
            roles: ["dianweileixingshezhi"]
          }
        },
        {
          path: "disinfectionPoint",
          component: () =>
            import("@/views/disinfectMan/disinfectConfig/disinfectionPoint"),
          name: "消毒点位设置",
          meta: {
            title: "消毒点位设置",
            roles: ["xiaodudianweishezhi"]
          }
        },
        {
          path: "deviceManagement",
          component: () =>
            import("@/views/disinfectMan/disinfectConfig/deviceManagement"),
          name: "消毒设备管理",
          meta: {
            title: "消毒设备管理",
            roles: ["xiaodushebeiguanli"]
          }
        },
        {
          path: "disinfectionMethod",
          component: () =>
            import("@/views/disinfectMan/disinfectConfig/disinfectionMethod"),
          name: "消毒方式设置",
          meta: {
            title: "消毒方式设置",
            roles: ["xiaodufangshishezhi"]
          }
        }
      ]
    },

    {
      path: "disinfectionStatisticsQuery",
      component: () =>
        import("@/views/disinfectMan/disinfectionStatisticsQuery/index"),
      name: "消毒统计查询",
      meta: {
        title: "消毒统计查询",
        roles: ["xiaodutongjichaxun"]
      }
    },
    {
      path: "disinfectionDeviceStats",
      component: () =>
        import("@/views/disinfectMan/disinfectionDeviceStats/index"),
      name: "消毒设备统计",
      meta: {
        title: "消毒设备统计",
        roles: ["xiaodutongjichaxun"]
      }
    },

    {
      path: "summaryStats",
      component: () => import("@/views/disinfectMan/report/summaryStats"),
      name: "汇总统计",
      meta: {
        title: "汇总统计",
        roles: ["xiaodutongjichaxun"]
      }
    },
    {
      path: "trendAnalysis",
      component: () => import("@/views/disinfectMan/report/trendAnalysis"),
      name: "趋势分析",
      meta: {
        title: "趋势分析",
        roles: ["xiaodutongjichaxun"]
      }
    }

    // {
    //   path: "matterManagement",
    //   component: () => import("@/views/disinfectMan/matterManagement/index"),
    //   name: "报事管理",
    //   meta: {
    //     title: "报事管理",
    //     roles: ["baoshiguanlichakan"]
    //   },
    //   children: []
    // }
  ]
};

export default disinfectRouter;
