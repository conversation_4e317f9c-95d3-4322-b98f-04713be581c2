<template>
  <!-- 弹窗 新增/编辑 -->
  <el-dialog
    class="mazhenguo"
    :title="dlgType === 'add' ? '新增租户' : '租户详情'"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="500px"
    top="30px"
  >
    <el-form
      ref="dlgDataForm"
      :rules="dlgRules"
      :model="dlgData"
      label-position="right"
      label-width="80px"
      style="width: 440px"
      size="mini"
      @submit.native.prevent
    >
      <el-form-item label="手机" prop="phone">
        <el-input v-model="dlgData.phone" placeholder="请填写手机号码"></el-input>
      </el-form-item>

      <el-form-item label="姓名" prop="name">
        <el-input v-model="dlgData.name" placeholder="请填写姓名"></el-input>
      </el-form-item>

      <el-row>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="dlgData.sex">
              <el-radio :label="'男'">男</el-radio>
              <el-radio :label="'女'">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年龄">
            <el-input-number
              v-model="dlgData.age"
              :min="0"
              placeholder="请输入"
              controls-position="right"
              style="width: 100px"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="身份证" prop="idCard">
        <el-input v-model="dlgData.idCard" placeholder="请填写身份证号码"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
// 工具
// import { phoneReg } from '@/utils/regUtil'
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
// 接口
import * as utils from '@/utils'
import * as regUtils from '@/utils/regs'

import { postAction, getAction, formAction } from '@/api'

let dlgDataEmpty = {
  id: '0',
  phone: '',
  name: '',
  sex: '',
  age: '',
  idCard: '',
}
export default {
  components: {},
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},

    roomData: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          let dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))

          this.dlgData = JSON.parse(JSON.stringify(dlgData))
          this.$nextTick(() => {
            this.$refs['dlgDataForm'].clearValidate()
          })
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      userInfo: '',
      // 弹窗
      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        sex: [{ required: true, message: '必填字段', trigger: 'blur' }],
        name: [{ required: true, message: '必填字段', trigger: 'change' }],
        phone: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1\d{10}))$/,
            message: '手机号码格式有误！',
            trigger: 'blur',
          },
        ],
        idCard: [
          {
            pattern: regUtils.sfzReg,
            message: '证件号码格式有误！',
            trigger: 'blur',
          },
        ],
      },
      dlgSubLoading: false, // 提交loading
    }
  },
  created() {
    // this.getDataDict()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },
  methods: {
    // 弹窗提交 ------
    dlgSubFunc() {
      this.$refs['dlgDataForm'].validate((valid) => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData))
          sendObj.projectId = this.userInfo.projectId
          sendObj.rooms = [this.roomData]
          sendObj.type = 3
          this.dlgSubLoading = true
          postAction('/unity/buildingmember/add/owner/member', sendObj).then((res0) => {
            let res = res0.data
            this.dlgSubLoading = false
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.dlgState = false
              this.$emit('getList')
              this.$emit('closeDlg')
            } else {
              this.$message({
                type: 'warning',
                message: res.msg,
              })
            }
          })
        }
      })
    },

    closeDlg() {
      this.dlgLoading = false
      this.dlgSubLoading = false
      this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')

      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.$nextTick(() => {
        this.$refs['dlgDataForm'].clearValidate()
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>