<template>
  <div>
    <el-dialog
      class="mazhenguo"
      :title="dlgType === 'add' ? '添加' : '编辑'"
      :close-on-click-modal="false"
      :visible.sync="dlgState"
      append-to-body
      width="800px"
      top="30px"
    >
      <el-form
        ref="dlgDataForm"
        :rules="dlgRules"
        :model="dlgData"
        label-position="right"
        label-width="120px"
        style="width: 750px"
        size="mini"
        @submit.native.prevent
        :disabled="dlgType == 'info'"
      >
        <el-form-item label="计划名称" prop="name">
          <el-input v-model="dlgData.name" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="选择项目" prop="projectId">
          <el-input
            v-model="dlgData.projectName"
            disabled
            v-if="dlgType != 'add'"
          >
          </el-input>
          <el-select
            v-else
            :disabled="dlgType != 'add'"
            v-model="dlgData.projectId"
            placeholder="选择项目"
            @change="projectIdChange"
            filterable
            clearable
          >
            <el-option
              v-for="item of projectList"
              :key="item.id"
              :label="item.name"
              :value="parseInt(item.id)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称" prop="equId">
          <el-input v-model="dlgData.equName" disabled v-if="dlgType != 'add'">
          </el-input>
          <el-select
            v-else
            v-model="dlgData.equId"
            @change="equIdChange"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item of deviceSelect"
              :key="item.id"
              :label="item.equName2"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="型号" prop="equModel">
          <el-input v-model="dlgData.equModel" placeholder="请输入" disabled />
        </el-form-item>
        <el-form-item label="设备位置" prop="equPosition">
          <el-input
            v-model="dlgData.equPosition"
            placeholder="请输入"
            disabled
          />
        </el-form-item>

        <el-form-item label="设备保养周期" prop="maintenanceCycle">
          <!-- maintenanceCycle
             maintenanceCycleStr -->
          <el-select
            v-model="dlgData.maintenanceCycle"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item of pzbyzqSelect"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="初次保养时间" prop="firstTime">
          <el-date-picker
            v-model="dlgData.firstTime"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="请选择"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="保养内容" prop="maintenanceItem">
          <el-button @click="addItem" icon="el-icon-plus" type="primary" plain
            >添加</el-button
          >
          <el-table
            class="mt10"
            :data="dlgData.maintenanceItem"
            fit
            border
            highlight-current-row
          >
            <el-table-column label="#" type="index" align="center" width="60">
            </el-table-column>
            <el-table-column label="保养项" width="130">
              <template slot-scope="scope">
                <div v-if="dlgType == 'info'">{{ scope.row.name }}</div>
                <el-input
                  v-else
                  v-model="scope.row.name"
                  placeholder="请输入"
                />
              </template>
            </el-table-column>
            <el-table-column label="保养内容及要求">
              <template slot-scope="scope">
                <div v-if="dlgType == 'info'">{{ scope.row.value }}</div>
                <el-input
                  v-else
                  v-model="scope.row.value"
                  placeholder="请输入"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="dlgType != 'info'"
              label="操作"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  @click="delItem(scope.$index)"
                  icon="el-icon-delete"
                  size="mini"
                  type="danger"
                  title="删除"
                  plain
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <!-- <el-form-item label="保养方法" prop="maintenanceMethod">
        <el-input v-model="dlgData.maintenanceMethod" placeholder="请输入" />
      </el-form-item> -->

        <el-form-item label="岗位" prop="maintenancePersonName">
          <el-input
            v-model="dlgData.maintenancePersonName"
            :title="dlgData.maintenancePersonName"
            @focus="showPostDlg"
            placeholder="请选择"
          />
        </el-form-item>

        <el-form-item label="附件" prop="fileUrl">
          <qiniuUpload
            :fileList0="dlgData.fileUrl"
            :onlyImage="true"
            ref="qiniuUploadRef"
            :limit="5"
            :maxSize="5"
            @successBack="successBack"
            :dlgType="dlgType"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
        <el-button
          v-if="dlgType != 'info'"
          :loading="dlgSubLoading"
          type="success"
          @click="dlgSubFunc"
          icon="el-icon-check"
        >
          <span v-if="dlgSubLoading">保存中...</span>
          <span v-else>保存</span>
        </el-button>
      </div>
    </el-dialog>
    <diaPostList
      :diaPostState2="diaPostState2"
      @closeDlg="closeSelectPostDlg"
      :hiddenTime="false"
      :fromBaoshi="true"
    />
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import * as utils from "@/utils";
import { postAction, getAction, putAction } from "@/api";
import qiniuUpload from "@/views/greenMan/components/qiniuUpload";
import diaPostList from "@/components/Dialog/diaPostList"; // 选人选岗 通用弹窗

let dlgDataEmpty = {
  name: "", // 计划名称
  projectId: undefined,
  id: undefined,
  equId: undefined, // 设备名称
  equName: undefined,
  equModel: undefined, // 型号
  equPosition: undefined, // 设备所在位置
  maintenancePersonId: "", // 保养人员id
  maintenancePersonName: "",
  fileUrl: [],
  maintenanceCycle: undefined, // 设备保养周期
  maintenanceCycleStr: undefined,
  firstTime: "",
  maintenanceItem: [{ name: "", value: "" }], // 保养事项
  // maintenanceMethod: undefined // 保养方法
};
export default {
  components: {
    qiniuUpload,
    diaPostList,
  },
  props: {
    dlgType: {
      type: String,
      default: "add",
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgData0: {
      type: Object,
      default: {},
    },
    projectList: {
      type: Array,
      default() {
        return []
      }
    },
  },
  watch: {
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          if (this.dlgType == "add") {
            this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
            this.$nextTick(() => {
              this.$refs["dlgDataForm"].clearValidate();
            });
          } else {
            this.getInit();
          }
        }, 50);
      } else {
        this.closeDlg();
      }
    },
    diaPostGet(val) {
      if (!val) {
        return false;
      }
      let postList = JSON.parse(val);
      console.log("+++弹窗返回", postList);
      this.selectDlgData = JSON.parse(JSON.stringify(postList));
      let maintenancePersonId = [];
      let maintenancePersonName = [];
      postList.map((item) => {
        maintenancePersonId.push(item.postId);
        maintenancePersonName.push(item.label);
      });
      this.dlgData.maintenancePersonId = maintenancePersonId.toString();
      this.dlgData.maintenancePersonName = maintenancePersonName.toString();
    },
  },
  computed: {
    // 岗位列表
    diaPostGet: {
      get: function () {
        return this.$store.getters.diaPostGet;
      },
      set: function (val) {
        this.$store.commit("SET_DIAPOST_GET", "");
      },
    },
  },
  data() {
    return {
      selectDlgData: [],
      // 弹窗
      diaPostState2: false,
      dlgState: false,
      dlgLoading: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        name: [{ required: true, message: "必填字段", trigger: "blur" }],
        projectId: [{ required: true, message: "必填字段", trigger: "change" }],
        equId: [{ required: true, message: "必填字段", trigger: "change" }],
        maintenanceCycle: [
          { required: true, message: "必填字段", trigger: "change" },
        ],
        firstTime: [{ required: true, message: "必填字段", trigger: "change" }],
        maintenanceItem: [
          { required: true, message: "必填字段", trigger: "change" },
        ],
        maintenancePersonName: [
          { required: true, message: "必填字段", trigger: "change" },
        ],
        // maintenanceMethod: [
        //   { required: true, message: "必填字段", trigger: "blur" }
        // ]
      },
      dlgSubLoading: false, // 提交loading

      deviceSelect: [],
      pzbyzqSelect: [],
    };
  },
  created() {
    // this.getDataDict();
  },
  methods: {
    closeSelectPostDlg() {
      this.diaPostState2 = false;
    },
    // 设置权限
    showPostDlg() {
      this.$store.commit("SET_DIAPOST_TYPE", 'post-*-');
      // this.$store.commit("SET_DIAPOST_SEL", this.dlgData.postId);

      // let query = {
      //   branchId: this.postParam.branchId,
      //   branchName: this.postParam.branchName
      // };
      // this.$store.commit("SET_DIAPOST_QUERY", JSON.stringify(query));
      if (this.selectDlgData.length !== 0) {
        this.$store.commit(
          "SET_DIAPOST_GET",
          JSON.stringify(this.selectDlgData)
        );
      } else {
        this.$store.commit("SET_DIAPOST_GET", "");
      }
      this.$store.commit("SET_DIAPOST_MUL", true);
      this.diaPostState2 = true;
    },
    projectIdChange(val) {
      this.dlgData.equId = "";
      this.dlgData.equName = "";
      this.dlgData.equModel = "";
      this.dlgData.equPosition = "";
      this.getDeviceSelect(val);
    },
    successBack(fileList) {
      this.dlgData.fileUrl = fileList;
    },
    async getDeviceSelect(projectId = "") {
      try {
        let res0 = await getAction(
          `/green/equipment-manage/page?responsiblePersonId=${window.localStorage.userId}&equName=&pageNo=1&pageSize=200&projectId=${projectId}`
        );
        let res = res0.data;
        if (res.code == 200) {
          let list = res.data.list;
          for (let item of list) {
            item.equName2 = item.equName + "（" + item.equModel + "）";
          }
          this.deviceSelect = list;
        }
      } catch (err) {
        this.$message.error(err.msg);
      }
    },
    equIdChange(val) {
      let item = this.deviceSelect.filter((item) => item.id == val)[0];
      console.log("====item", item);
      this.dlgData.equName = item.equName;
      this.dlgData.equModel = item.equModel;
      this.dlgData.equPosition = item.equPosition;
      this.dlgData.projectId = item.projectId;
      this.dlgData.projectName = item.projectName;
      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    },
    getDataDict() {
      let keyList = (this.keyMap = [
        { dbKey: "maintenanceCycle", pageSelectKey: "pzbyzqSelect" }, // 配置保养周期
      ]);
      utils.getDbItems(this, keyList);
    },
    addItem() {
      this.dlgData.maintenanceItem.push({
        name: "",
        value: "",
      });
      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    },

    delItem(index) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.dlgData.maintenanceItem.splice(index, 1);
        this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      });
    },

    async getInit() {
      console.log("====dlgData0", this.dlgData0);
      let id = this.dlgData0.id;
      try {
        let res0 = await getAction("/green/equ/maintenance/get?id=" + id);
        let res = res0.data;
        if (res && res.code == 200) {
          // this.getDeviceSelect(res.data.projectId || "");

          let dlgData = JSON.parse(JSON.stringify(res.data));

          if (dlgData.maintenanceItem) {
            dlgData.maintenanceItem = JSON.parse(dlgData.maintenanceItem);
          } else {
            dlgData.maintenanceItem = [];
          }

          if (dlgData.fileUrl) {
            dlgData.fileUrl = JSON.parse(dlgData.fileUrl);
          } else {
            dlgData.fileUrl = [];
          }
          dlgData.maintenancePersonId += "";

          this.dlgData = JSON.parse(JSON.stringify(dlgData));
        }
      } catch (err) {
        console.log("====错误", err);
        this.$message.error(err.msg);
      }
    },
    dlgSubFunc() {
      this.$refs["dlgDataForm"].validate((valid) => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));

          let maintenanceItem = sendObj.maintenanceItem.filter((item) => {
            return item.name || item.value;
          });
          if (maintenanceItem.length == 0) {
            this.$message.warning("保养内容不能为空");
            return false;
          }

          sendObj.maintenanceItem = JSON.stringify(maintenanceItem);
          sendObj.fileUrl = JSON.stringify(sendObj.fileUrl);

          sendObj.maintenanceCycleStr = utils.arrId2Name(
            this.pzbyzqSelect,
            sendObj.maintenanceCycle
          );

          let url = "";
          let func = postAction;
          if (this.dlgType == "add") {
            url = "/green/equ/maintenance/create";
          } else if (this.dlgType == "copy") {
            delete sendObj.id;
            url = "/green/equ/maintenance/create";
          } else {
            url = "/green/equ/maintenance/update";
          }

          this.dlgSubLoading = true;
          func(url, sendObj).then((res0) => {
            this.dlgSubLoading = false;
            let res = res0.data;

            if (res.code == 200) {
              this.$message.success(res.msg);
              this.dlgState = false;
              this.$emit("getList");
              this.closeDlg();
            } else {
              this.$message({
                type: "warning",
                message: res.msg,
              });
            }
          });
        }
      });
    },

    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.dlgState = false;
      this.$refs["dlgDataForm"].resetFields();
      this.dlgData.maintenancePersonId = ""
      this.selectDlgData = [];
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
