/** 系统平台管理 **/

import Layout from "@/views/layout/Layout";

const platformManRouter = {
  path: "/platformMan",
  component: Layout,
  name: "platformMan",
  meta: {
    title: "平台管理",
    icon: "form",
    roles: ["pingtaiguanli"]
  },
  children: [
    {
      path: "dataRoom",
      name: "大屏管理",
      component: () => import("@/views/platformMan/dataRoom/index"),
      meta: {
        title: "大屏管理",
        roles: ["gangweiguanli"]
      }
    },
    {
      path: "postMan",
      name: "岗位管理",
      component: () => import("@/views/platformMan/postMan/index"),
      meta: {
        title: "岗位管理",
        roles: ["gangweiguanli"]
      }
    },
    //////////
    {
      path: "branchMan",
      name: "部门管理(三院)",
      component: () => import("@/views/platformMan/branchMan"),
      meta: {
        title: "部门管理",
        roles: ["bumenkeshish<PERSON><PERSON>"]
      }
    },
    {
      path: "deptMan",
      name: "部门管理(二院)",
      component: () => import("@/views/platformMan/deptMan"),
      meta: {
        title: "部门管理",
        roles: ["bumenkeshishezhiv2"]
      }
    },
    {
      path: "staffMan",
      component: () => import("@/views/platformMan/staffMan"),
      name: "人员管理(三院)",
      meta: {
        title: "人员管理",
        roles: ["yuangongguanli"]
      }
    },
    {
      path: "roleMan",
      name: "角色权限",
      component: () => import("@/views/platformMan/roleMan"),
      meta: {
        title: "角色权限",
        roles: ["juesequanxian"]
      }
    },

    {
      path: "districtMan",
      name: "区域管理",
      component: () => import("@/views/platformMan/districtMan"),
      meta: {
        title: "区域管理",
        roles: ["quyuguanli"]
      }
    },
    {
      path: "dataDic",
      name: "数据字典设置",
      component: () => import("@/views/platformMan/dataDic"),
      meta: {
        title: "数据字典设置",
        roles: ["shujuzidian"]
      }
    },

    {
      path: "dataDicEdit/:groupText/:groupChar",
      name: "编辑",
      component: () => import("@/views/platformMan/dataDicEdit"),
      hidden: true,
      meta: {
        title: "编辑数据字典",
        roles: ["shujuzidian"]
      }
    },

    {
      path: "messageMan",
      name: "消息管理",
      component: () => import("@/views/platformMan/messageMan"),
      meta: {
        title: "消息管理",
        roles: ["xiaoxiguanli"]
      }
    },
    {
      path: "noticeMan",
      component: () => import("@/views/platformMan/noticeMan/index"),
      meta: {
        title: "通知公告",
        roles: ["tongzhigonggao"]
      },
      children: [
        {
          path: "noticeAnnouncement",
          name: "通知公告",
          component: () =>
            import("@/views/platformMan/noticeMan/noticeAnnouncement"),
          meta: {
            title: "通知公告",
            roles: ["tongzhigonggao_web"]
          }
        },
        {
          path: "receiveNotice",
          name: "接收通知公告",
          component: () =>
            import("@/views/platformMan/noticeMan/receiveNotice"),
          meta: {
            title: "接收通知公告",
            roles: ["jieshoutongzhigonggao"]
          }
        },
        {
          path: "communityNotice",
          component: () =>
            import("@/views/platformMan/noticeMan/communityNotice"),
          name: "小区通告",
          meta: {
            title: "小区通告",
            roles: ["xiaoqugonggao"]
          }
        },
        {
          path: "schoolNotice",
          component: () => import("@/views/platformMan/noticeMan/schoolNotice"),
          name: "校园通告",
          meta: {
            title: "校园通告",
            roles: ["xiaoyuangonggao"]
          }
        }
      ]
    },

    // 0815
    {
      path: "regionMan",
      component: () => import("@/views/equipSafeMan/regionMan"),
      name: "区域配置",
      meta: {
        title: "区域配置",
        roles: ["shebeiquyuguanli"]
      }
    },
    {
      path: "equipmentRoomMan",
      component: () => import("@/views/equipSafeMan/equipmentRoomMan"),
      name: "设备机房",
      meta: {
        title: "设备机房",
        roles: ["shebeijianguanli"]
      }
    },
    {
      path: "equipmentMan",
      component: () => import("@/views/equipSafeMan/equipmentMan"),
      name: "设备管理",
      meta: {
        title: "设备管理",
        roles: ["shebeitaizhangguanli"]
      }
    },
    {
      path: "abnormalAlarmOffline",
      component: () => import("@/views/equipSafeMan/abnormalAlarmOffline"),
      name: "离线报警日志",
      meta: {
        title: "离线报警日志",
        // roles: ["shebeitaizhangguanli"]
      }
    },
    {
      path: "earlyWarningManagement",
      component: () => import("@/views/equipSafeMan/earlyWarningManagement"),
      name: "预警管理",
      meta: {
        title: "预警管理",
        // roles: ["shebeitaizhangguanli"]
      }
    },
    {
      path: "alarmConfiguration",
      component: () => import("@/views/platformMan/noticeMan/index"),
      meta: {
        title: "报警配置",
        roles: ["dianqihuozai_baojingpeizhi"]
      },
      children: [
        {
          path: "ruleConfiguration",
          component: () => import("@/views/alarmConfiguration/ruleConfiguration"),
          name: "规则配置",
          meta: {
            title: "规则配置",
            roles: ["baojingpeizhi_guizepeizhi"]
          }
        },
        {
          path: "smokeDetector",
          component: () => import("@/views/alarmConfiguration/alarmConfiguration"),
          name: "报警配置",
          meta: {
            title: "报警配置",
            roles: ["baojingpeizhi_baojingpeizhi"]
          }
        },

        
        // {
        //   path: "alarmQuery",
        //   component: () => import("@/views/alarmConfiguration/alarmQuery/index"),
        //   meta: {
        //     title: "报警查询",
        //     roles: ["baojingpeizhi_baojingchaxun"]
        //   },
        //   children: [
        //     {
        //       path: "telephoneAlarmQuery",
        //       component: () => import("@/views/alarmConfiguration/alarmQuery/telephoneAlarmQuery"),
        //       name: "电话报警查询",
        //       meta: {
        //         title: "电话报警查询",
        //         roles: ["baojingpeizhi_dianhuabaojingchaxun"]
        //       },
        //       children: []
        //     },
        //     {
        //       path: "smsAlarmQuery",
        //       component: () => import("@/views/alarmConfiguration/alarmQuery/smsAlarmQuery"),
        //       name: "短信报警查询",
        //       meta: {
        //         title: "短信报警查询",
        //         roles: ["baojingpeizhi_duanxinbaojingchaxun"]
        //       },
        //       children: []
        //     },
        //   ]
        // }
    
      ]
    }

  ]
};

export default platformManRouter;
