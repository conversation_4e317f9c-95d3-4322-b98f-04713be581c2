<template>
  <div class="app-container print">
    <div
      ref="paperContainer"
      :style="`width:${paperWidth}px;height:${paperHeight}px;`"
      class="paper-container clearfix"
      style="margin-top: -16px; border-bottom: 1px solid #000"
    >
      <!-- <div class="tac text-bold" style="font-size: 17px; letter-spacing: 3px">{{ title }}</div> -->

      <table class="m-table" style="width: 100%; margin-top: 4px">
        <thead>
          <tr>
            <td colspan="9999" class="tac text-bold">{{ tableHead0[0][0] }}</td>
          </tr>
          <tr v-for="index in lenMax">
            <template v-for="(item2, index2) of tableHead0">
              <td
                class="tac"
                v-if="(item2[index] && index2 != 0 && item2[index] != tableHead0[index2 - 1][index]) || (item2[index] && index2 == 0)"
                :colspan="colspanFunc(index, index2)"
                :rowspan="!item2[index + 1] && index != lenMax - 1 ? '2' : '1'"
              >
                {{ item2[index] }}
              </td>
            </template>
          </tr>
        </thead>

        <tbody>
          <tr v-for="(item, index) of list">
            <!-- :rowspan="rowsFunc(index, index2)" -->
            <template v-if="index < list.length - 1">
              <td v-if="rowsFunc(index, index2)" v-for="(item2, index2) of item" class="tac" :rowspan="rowsFunc(index, index2)">
                <span v-if="index2 < item.length - signImgNum"> {{ item2 }}</span>
                <div v-else>
                  <span v-if="item2 + '' === 'null' || item2 == ''"></span>

                  <el-image
                    v-else
                    style="width: 60px; height: 30px"
                    :preview-src-list="[`data:image/jpg;base64,${item2}`]"
                    :z-index="9999"
                    :src="`data:image/jpg;base64,${item2}`"
                    alt=""
                  ></el-image>
                </div>
                <!-- <img v-else :src="`data:image/jpg;base64,${item2}`" style="width: 60px; height: 30px; display: inline-block" /> -->
              </td>
            </template>

            <td v-else-if="item[0]" style="padding: 10px; line-height: 1.1" colspan="9999">{{ item[0] }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <el-button class="no-print" style="top: 20px" icon="el-icon-printer" type="primary" size="medium" @click="printFunc()"> 打印</el-button>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import * as utils from '@/utils'
import { return2Num } from '@/utils/calendarData'

// import {
//   findInfoById,
// } from '@/api/jyt/drugMail'

export default {
  components: {},
  data() {
    return {
      paperHeight: 0,
      paperWidth: 0,

      title: '',
      list: [],
      tableData: [],

      printDate: '',

      tableHead0: [],
      tableHead: [],
      lenMax: 0,
      signImgNum: 0,
    }
  },
  computed: {},
  created() {
    let xdtjcxPrintDate = JSON.parse(window.sessionStorage.xdtjcxPrintDate)
    this.title = xdtjcxPrintDate.thead[0][0]
    this.list = xdtjcxPrintDate.list
    this.lenMax = xdtjcxPrintDate.theadLenMax
    this.tableHead0 = xdtjcxPrintDate.thead
    this.signImgNum = xdtjcxPrintDate.signImgNum
  },
  mounted() {
    this.$nextTick(() => {
      // A4纸参考 高39.7cm 280、宽21cm 188
      this.setPrintDate()

      this.paperWidth = utils.getDpiWidth(188)
      this.paperHeight = utils.getDpiHeight(280)

      // this.paperWidth = utils.getDpiWidth(210)
      // this.paperHeight = utils.getDpiHeight(140)
      this.id = this.$route.query.id
      // this.getOrderInfo()
    })
  },
  methods: {
    // 表头跨行
    colspanFunc(rowIndex, colIndex) {
      let tStr = this.tableHead0[colIndex][rowIndex]

      let returnNum = 1
      for (let i = colIndex + 1; i < this.tableHead0.length; i++) {
        if (tStr == this.tableHead0[i][rowIndex]) {
          returnNum++
        }
      }
      return returnNum
    },
    // 表内容跨行
    rowsFunc(index, index2) {
      if (index2 == 0) {
        if (index != 0 && this.list[index][index2] == this.list[index - 1][index2]) {
          return 0
        } else {
          let num = 1
          let i = index
          for (let j = i + 1; j < this.list.length; j++) {
            if (this.list[i][0] == this.list[j][0]) {
              num++
            }
          }

          return num
        }
      } else {
        return 1
      }
    },
    setPrintDate() {
      let today = new Date()
      let year = today.getFullYear()
      let month = return2Num(today.getMonth() + 1)
      let day = return2Num(today.getDate())
      this.printDate = `${year} 年 ${month} 月 ${day} 日`
    },

    dealBigMoney(val) {
      console.log('之后的', utils.dealBigMoney(val))
      return utils.dealBigMoney(val)
    },

    // 获取订单信息
    getOrderInfo() {
      if (utils.isNull(this.id)) {
        this.$message.warning('暂无该邮寄订单')
        return
      }
      findInfoById(this.id).then((res) => {
        if (res.data.code == 200) {
          // let data = res.data.data
          // fieldList.push([28, 53, data.receiveName])
          // fieldList.push([70, 53, data.receivePhoneFirst])
          // fieldList.push([92, 53, data.receivePhoneSecond])
          // fieldList.push([26, 65, `${data.province} ${data.city} ${data.county} ${data.addr}`])
          // fieldList.push([30, 80, data.city])
          // if (data.isInsuredprice == 1) {
          //   fieldList.push([26, 109, '✓'])
          //   fieldList.push([100, 109, data.insuredAmount])
          // } else {
          //   fieldList.push([34, 109, '✓'])
          // }
          // this.renderFields()
        } else {
          this.$message.warning('暂无该邮寄订单')
        }
      })
    },

    // 渲染字段
    // renderFields() {
    //   for (let i of fieldList) {
    //     this.fieldList.push({
    //       left: utils.getDpiWidth(i[0]),
    //       top: utils.getDpiHeight(i[1]),
    //       label: i[2],
    //     })
    //   }
    // },

    // 打印
    printFunc() {
      window.print()
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-container.print {
  font-size: 14px;
  overflow: visible; // 预览时可看多页
}
.app-container {
  font-size: 12px;
  position: relative;
  width: 100%;
  // 不限制高度时去掉
  // height: 100%;
  // overflow: auto;
  padding: 0;

  .paper-container {
    // background: url('/static/image/paper.jpg') no-repeat center;
    // background-size: 100%;
    background: #fff;
    position: relative;
    box-sizing: border-box;
    padding: 20px;
    .field {
      position: absolute;
      max-width: 360px;
      word-break: break-all;
    }
  }
  .el-button.no-print {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

@media print {
  .no-print {
    display: none;
  }
}

//
// 表格
.tac {
  text-align: center;
}
.tar {
  text-align: right;
}
.text-bold {
  font-weight: bold;
}
.mt16 {
  margin-top: 16px;
}
.m-table {
  // background:#666;
  border-top: 1px solid #666;
  border-left: 1px solid #666;
  border-spacing: 0px;
  font-size: 12px;
  td {
    background: #fff;
    min-height: 30px;
    box-sizing: border-box;
    padding: 1px;
    border-right: 1px solid #666;
    border-bottom: 1px solid #666;
  }
  thead td {
    padding: 6px 1px;
  }
  .mh {
    min-height: 36px;
  }
  .m-input .el-input__inner {
    border: none;
    border-radius: 0;
    border-bottom: 1px solid #666;
    padding: 0;
    height: 18px;
    line-height: 18px;
  }
}

// 页面
.imgLogo {
  position: absolute;
  left: 30px;
  top: 17px;
  width: 40px;
  height: 36px;
}
.imgZhang {
  position: absolute;
  left: 20px;
  top: 204px;
  width: 240px;
  height: 40px;
}
.printDate {
  position: absolute;
  right: 0px;
  top: 0px;
}
</style>