<!-- 趋势分析 -->
<template>
  <div class="app-container" style="overflow: auto">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="选择科室:">
          <el-input
            @focus="showKeshiDlg"
            class=""
            placeholder="请选择科室"
            v-model="listQuery.departmentName"
            style="width: 140px"
            readonly
          >
          </el-input>
        </el-form-item>

        <el-form-item label-width="0">
          <el-select
            style="width: 100px"
            v-model="listQuery.pointEquipType"
            @change="pointEquipTypeChange"
            placeholder="请选择"
          >
            <el-option label="选择点位" value="0"></el-option>
            <el-option label="选择设备" value="1"></el-option>
          </el-select>
          <el-select
            v-model="listQuery.pointEquipIds"
            clearable
            placeholder="请选择"
            style="width: 300px"
            multiple
            collapse-tags
          >
            <el-option
              v-for="item of navList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label-width="0">
          <el-select
            style="width: 100px"
            v-model="listQuery.dateType"
            @change="dateTypeChange"
            placeholder="请选择"
          >
            <el-option label="按年" value="0"></el-option>
            <el-option label="按月" value="1"></el-option>
          </el-select>

          <el-date-picker
            v-if="listQuery.dateType + '' === '0'"
            v-model="listQuery.date"
            type="year"
            format="yyyy"
            value-format="yyyy"
            placeholder="选择年"
            style="width: 140px"
          >
          </el-date-picker>

          <el-date-picker
            v-if="listQuery.dateType + '' === '1'"
            v-model="listQuery.date"
            type="month"
            format="yyyy-MM"
            value-format="yyyy-MM"
            placeholder="选择月份"
            style="width: 140px"
          >
          </el-date-picker>
        </el-form-item>

        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchFunc"
          >搜索</el-button
        >
      </el-form>
    </div>

    <div v-if="isShowChart" id="echart-line" style="height: 400px"></div>

    <selectBmDlg
      :dlgState0="dlgKeshiState"
      :dlgType="dlgKeshiType"
      :dlgQuery="dlgKeshiQuery"
      :dlgSelectData="dlgKeshiSelectData"
      :isRole="false"
      title="科室"
      @closeDlg="closeKeshiDlg"
      @backFunc="dlgKeshibackFunc"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import * as utils from "@/utils";
import { postAction, getAction } from "@/api";
import selectBmDlg from "@/components/Dialog2/selectBmDlg";

export default {
  components: {
    selectBmDlg
  },
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),

      navList: [],
      myChart: "",

      ////
      // 弹窗
      dlgKeshiQuery: {},
      dlgKeshiState: false,
      dlgKeshiType: "", // 弹框状态add, edit
      dlgKeshiSelectData: { id: "", label: "" },

      ////

      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        departmentId: "",
        departmentName: "",
        // dateRange: [],

        pointEquipType: "0",
        pointEquipIds: [],

        dateType: "0",
        date: ""
      },

      isShowChart: false
    };
  },

  created() {
    // console.log('==', JSON.parse(window.localStorage.userInfo))
    let userInfo = JSON.parse(window.localStorage.userInfo);
    // console.log('===userInfo', userInfo)
    this.listQuery.departmentId = userInfo.departmentId;
    this.listQuery.departmentName = userInfo.departmentName;

    // this.listQuery.dateRange = utils.returnToMonth()

    this.getNavList();
  },
  mounted() {
    this.searchFunc();
  },

  methods: {
    pointEquipTypeChange() {
      this.getNavList();
    },
    dateTypeChange() {
      this.listQuery.date = "";
    },

    getNavList() {
      if (utils.isNull(this.listQuery.departmentName)) {
        this.$message.warning("请选择科室");
        return false;
      }
      let sendObj,
        sendUrl = "";
      this.listQuery.pointEquipIds = [];

      if (this.listQuery.pointEquipType + "" == "0") {
        // 点位
        sendObj = {
          departmentId: this.listQuery.departmentId,
          departmentName: this.listQuery.departmentName,
          label: "",
          page: 1,
          limit: 99999,

          pointTypeId: ""
        };
        sendUrl = "/cloth/dis-point/page";
      } else {
        // 设备
        sendObj = {
          departmentId: this.listQuery.departmentId,
          departmentName: this.listQuery.departmentName,
          page: 1,
          limit: 99999
        };
        sendUrl = "/cloth/dis-equipment/page";
      }

      this.navList = [];

      postAction(sendUrl, sendObj).then(res0 => {
        let res = res0.data;
        if (res.code == 200) {
          if (!utils.isNull(res.data)) {
            let list = res.data;
            for (let item of list) {
              item.id += "";
            }
            this.navList = list;
          }
        }
        // window.open(res0.data.data)
      });
    },

    /////////////
    showKeshiDlg() {
      if (this.listQuery.departmentName) {
        this.dlgKeshiSelectData = {
          id: this.listQuery.departmentId,
          label: this.listQuery.departmentName
        };
      } else {
        this.dlgKeshiSelectData = { id: "", label: "" };
      }
      this.dlgKeshiState = true;
    },
    closeKeshiDlg() {
      this.dlgKeshiState = false;
    },
    dlgKeshibackFunc(data) {
      if (this.myChart) {
        this.myChart.clear();
      }
      this.listQuery.departmentId = data.id;
      this.listQuery.departmentName = data.label;
      this.getNavList();
      this.searchFunc();
    },

    setEchartLine(data0) {
      let data = [];
      for (let itemLine of data0) {
        let lineObj = {
          name: itemLine.name,
          type: "line",
          stack: "",
          data: []
        };
        for (let item of itemLine.list) {
          lineObj.data.push(item.count);
        }
        data.push(lineObj);
      }

      this.echartLineData = data;
      let xList = [];
      for (let item of data0[0].list) {
        xList.push(item.date);
      }

      // 绘制图标
      this.myChart = echarts.init(document.getElementById("echart-line"));
      var option = {
        title: {
          text: ""
        },
        tooltip: {
          trigger: "axis"
        },
        legend: {
          type: "plain",
          left: "center",
          bottom: 0
        },
        grid: {
          left: "2%",
          right: "40",
          bottom: "40",
          containLabel: true
        },
        xAxis: {
          type: "category", // value category time log
          boundaryGap: false, // true-刻度中间 false-刻度线上
          axisTick: { show: false }, // 是否显示刻度竖线
          data: xList
        },
        yAxis: {
          type: "value",

          nameTextStyle: {
            color: "#aaa",
            nameLocation: "start"
          }
        },
        series: data
      };
      this.myChart.clear();
      this.myChart.setOption(option);

      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
    },

    // << 列表
    searchFunc() {
      // this.listQuery.page = 1
      this.getList();
    },
    // 获取数据
    getList() {
      if (utils.isNull(this.listQuery.departmentName)) {
        this.$message.warning("请选择科室");
        return false;
      }
      if (
        utils.isNull(this.listQuery.pointEquipIds) ||
        this.listQuery.pointEquipIds.length == 0
      ) {
        if (this.listQuery.pointEquipType + "" === "0") {
          this.$message.warning("请选择点位");
        } else {
          this.$message.warning("请选择设备");
        }
        return false;
      }

      if (utils.isNull(this.listQuery.date)) {
        if (this.listQuery.dateType + "" === "0") {
          this.$message.warning("请选择年");
        } else {
          this.$message.warning("请选择月份");
        }
        return false;
      }
      this.isShowChart = false;

      this.list = [];
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));

      let nameArr = [];
      for (let id of sendObj.pointEquipIds) {
        let row = utils.arrId2Row(this.navList, id);
        nameArr.push(row.name);
      }

      sendObj.pointEquipIds = sendObj.pointEquipIds.join(",");
      sendObj.pointEquipNames = nameArr.join(",");
      this.listLoading = true;
      postAction("/cloth/dis-record/trendAnalysis", sendObj).then(res0 => {
        this.listLoading = false;
        let res = res0.data;
        if (res.code == 200) {
          this.isShowChart = true;
          setTimeout(() => {
            this.setEchartLine(res.data);
          }, 100);
        } else {
          this.$message.error(res.msg);
        }
      });
    }
    // >> 列表
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>
