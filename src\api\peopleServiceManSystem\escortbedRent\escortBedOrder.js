import request from '@/utils/request'

// 动态查询陪护床订单
export function findBedOrderDynamic(data) {
  return request({
    url: `/u/usapi/ph/findBedOrderDynamic`,
    method: 'post',
    data
  })
}

// 订单退款
export function bedOrderRefund(data) {
  return request({
    url: `/u/usapi/ph/bedOrderRefund`,
    method: 'post',
    data
  })
}

// 退款 新
export function updateIsUserRefund(data) {
  return request({
    url: `/u/usapi/ph/updateIsUserRefund`,
    method: 'post',
    data
  })
}