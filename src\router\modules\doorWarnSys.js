/** 门磁报警系统 **/

import Layout from "@/views/layout/Layout";

// doorWarnSys
const doorWarnSysRouter = {
  path: "/doorWarnSys",
  component: Layout,
  name: "doorWarnSys",
  meta: {
    title: "门磁报警系统",
    icon: "icon_mencibaojing",
    roles: ["mencibaojingxitong"]
  },
  children: [
    {
      path: "deviceMan",
      name: "设备管理",
      component: () => import("@/views/doorWarnSys/deviceMan/index"),
      meta: {
        title: "设备管理",
        roles: ["mc_shebeiguanli"]
      }
    },
    {
      path: "warnConfig",
      name: "报警设置",
      component: () => import("@/views/doorWarnSys/warnConfig/index"),
      meta: {
        title: "报警设置",
        roles: ["mc_baojingshezhi"]
      }
    },
    {
      path: "warnStatistics",
      name: "报警统计",
      component: () => import("@/views/doorWarnSys/warnStatistics/index"),
      meta: {
        title: "报警统计",
        roles: ["mc_baojingtongji"]
      }
    },
    {
      path: "warnHistoryQuery",
      name: "报警记录",
      component: () => import("@/views/doorWarnSys/warnHistoryQuery/index"),
      meta: {
        title: "报警记录",
        roles: ["mc_baojingjilu"]
      }
    },
    {
      path: "systemTestSelf",
      name: "系统自检",
      component: () => import("@/views/doorWarnSys/systemTestSelf/index"),
      meta: {
        title: "系统自检",
        roles: ["mc_xitongzijian"]
      }
    }
  ]
};

export default doorWarnSysRouter;
