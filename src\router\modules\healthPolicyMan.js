/** 医保政策 **/

import Layout from "@/views/layout/Layout";

const healthPolicyManRouter = {
  path: "/healthPolicyMan",
  component: Layout,
  name: "healthPolicyMan",
  meta: {
    title: "医保政策",
    icon: "form",
    roles: ["yibaozhengce"]
  },
  children: [
    {
      path: "healthPolicyMaintain",
      component: () => import("@/views/healthPolicyMan/healthPolicyMaintain"),
      name: "医保政策维护",
      meta: {
        title: "医保政策维护",
        roles: ["yibaozhengceweihu"]
      },
      children: []
    },
    {
      path: "healthPolicyQuery",
      component: () => import("@/views/healthPolicyMan/healthPolicyQuery"),
      name: "医保政策查询",
      meta: {
        title: "医保政策查询",
        roles: ["yibaozhengcechaxun"]
      },
      children: []
    }
  ]
};

export default healthPolicyManRouter;
