import request from "@/utils/request";

import { objToParam } from "@/utils/index";
// const Qs = require("qs");

//post
export function postAction(url, parameter) {
  return request({
    url: url,
    method: "post",
    data: parameter
  });
}
//get
export function getAction(url,parameter = {}) {
  let paramStr = objToParam(parameter)
  return request({
    url: url + paramStr,
    method: "get"
  });
}

//delete
export function deleteAction(url) {
  // let paramStr = objToParam(parameter)
  return request({
    url: url,
    method: "delete"
  });
}
//put
export function putAction(url, parameter={}) {
  // let paramStr = objToParam(parameter)
  return request({
    url: url,
    method: "put",
    data: parameter
  });
}

// form
export function formAction(url, data) {
  let formData = new FormData();
  for (let key in data) {
    let value = data[key];
    formData.append(key, value);
  }
  return request({
    url,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data"
    },
    data: formData
  });
}
