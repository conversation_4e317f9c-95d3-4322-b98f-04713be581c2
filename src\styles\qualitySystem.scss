.app-container {
  height: calc(100vh - 138px);
}

.filter-container {
  height: 50px;

  .el-form {
    /deep/ .el-select {
      width: 140px;
    }
  }
}

.departMan-main {
  position: relative;
  height: 100%;
}

//  左侧树状图样式
.departMan-left {
  float: left;
  width: 296px;
  height: 100%;
  box-sizing: border-box;
  .departMan-left-title {
    font-size: 14px;
    color: #595959;
    text-align: left;
    margin-top: 0px;
    margin-bottom: 24px;
    font-weight: bold;
  }

  .el-form {
    position: relative;

    .import-button {
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  .el-tree {
    width: 100%;
    height: calc(100% - 47px);
    overflow: auto;
    background: #f2f2f2;
    padding: 10px;

    /deep/ .el-tree-node {
      font-size: 14px;
    }

    /deep/ & > .el-tree-node {
      display: inline-block;
      min-width: 100%;
    }

    .custom-tree-node {
      display: flex;
      justify-content: space-between;
      font-size: 14px;

      span:nth-child(1) {
        display: inline-block;
        flex: 1;
        width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      span:nth-child(2) {
        display: inline-block;
        width: 44px;

        .el-button {
          padding: 0;

          /deep/ i {
            font-weight: bold;
          }

          /deep/ .el-icon-plus {
            color: #42b983;
          }

          /deep/ .el-icon-close {
            color: #f56c6c;
          }
        }
      }
    }

    /deep/ .el-tree-node.is-current > .el-tree-node__content {
      color: #fff;
      background: #409eff;

      .custom-tree-node {
        span:last-child {
          .el-button {
            .el-icon-plus {
              color: #fff;
            }

            .el-icon-edit {
              color: #fff;
            }

            .el-icon-close {
              color: #fff;
            }
          }
        }
      }
    }
  }

  // 重写系统部分样式
  .el-select {
    width: 140px;
  }
}

.departMan-right {
  float: right;
  width: calc(100% - 310px);
  height: 100%;
  box-sizing: border-box;
}

.custom-tree-node {
  display: flex;
  justify-content: space-between;
  flex: 1;
  align-items: center;
  font-size: 14px;
}

// 右侧表单
.departMan-right {
  .departMan-right-title {
    font-size: 14px;
    color: #595959;
    text-align: left;
    margin-top: 0px;
    margin-bottom: 24px;
    font-weight: bold;
  }

  .m-item-box {
    display: flex;
    justify-content: space-between;

    .el-form-item {
      width: 44%;
    }
  }

  .p-red {
    color: #f56c6c;
    font-size: 14px;
  }

  .input-tips {
    position: absolute;
    left: 0px;
    bottom: -18px;
    color: #f56c6c;
    font-size: 14px;
    line-height: 16px;
    margin: 0px;
    background: #fff;
    z-index: 999;
  }
}

.symbol {
  margin: 0 4px;
}

.page-container {
  .pagination-container {
    margin-top: 0 !important;
  }
}
