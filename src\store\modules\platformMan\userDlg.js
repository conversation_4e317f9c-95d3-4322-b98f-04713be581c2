// 人员dlg组件

const userDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    userId: '',

    userName: '',

  },

  getters: {
    dlgShow: state => state.dlgShow,

    userId: state => state.userId,

    userName: state => state.userName,

  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_USERID: (state, val) => {
      state.userId = val
    },

    SET_USERNAME: (state, val) => {
      state.userName = val
    },

  },

  actions: {

  }
}

export default userDlg
