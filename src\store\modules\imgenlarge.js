
const gwTree = {
  state: {
    imgEnlargeState: false,
    imgEnlargeUrl: '',
    imgEnlargeIsProcess: false
  },

  mutations: {
    SET_ENLARGESTATE: (state, imgEnlargeState) => {
      state.imgEnlargeState = imgEnlargeState
    },
    SET_ENLARGEURL: (state, imgEnlargeUrl) => {
      state.imgEnlargeUrl = imgEnlargeUrl
    },
    SET_ENLARGEISPROCESS: (state, val) => {
      state.imgEnlargeIsProcess = val
    }
  },

  actions: {
    
  }
}

export default gwTree
