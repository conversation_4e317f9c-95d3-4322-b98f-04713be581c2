/** 进销存管理 **/

import Layout from "@/views/layout/Layout";
import emptyView from "@/views/platformMan/noticeMan/index";

const invoicingRouter = {
  path: "/invoicingMan",
  component: Layout,
  name: "invoicingMan",
  meta: {
    title: "库存管理（档案管理）",
    icon: "kcgl",
    roles: ["jinxiaocunguanli"]
  },
  children: [
    // 基础数据
    {
      path: "baseDataSet",
      component: emptyView,
      meta: {
        title: "基础数据",
        roles: ["jxc_jichushuju"]
      },
      children: [
        {
          path: "goodsMan",
          name: "商品管理",
          component: () => import("@/views/invoicingMan/baseDataSet/goodsMan"),
          meta: {
            title: "商品管理",
            roles: ["jxc_shangpinguanli"]
          }
        },
        {
          path: "warehouseMan",
          name: "仓库管理",
          component: () =>
            import("@/views/invoicingMan/baseDataSet/warehouseMan"),
          meta: {
            title: "仓库管理",
            roles: ["jxc_cangkuguanli"]
          }
        },
        {
          path: "initialize",
          name: "期初维护",
          component: () =>
            import("@/views/invoicingMan/baseDataSet/initialize"),
          meta: {
            title: "期初维护",
            roles: ["jxc_qichuweihu"]
          }
        },
        {
          path: "supplierManagement",
          name: "供应商管理",
          component: () =>
            import("@/views/invoicingMan/baseDataSet/supplierManagement"),
          meta: {
            title: "供应商管理",
            roles: ["jxc_gongyingshangguanli"]
          }
        }
      ]
    },

    // 采购管理
    {
      path: "procurementMan",
      component: emptyView,
      meta: {
        title: "采购管理",
        roles: ["jxc_caigouguanli"]
      },
      children: [
        {
          path: "materialDemandSheetQuery",
          name: "物资需求单查询",
          component: () =>
            import(
              "@/views/invoicingMan/statisticsQuery/materialDemandSheetQuery/index"
            ),
          meta: {
            title: "物资需求单查询",
            roles: ["jxc_wuzixuqiudanchaxun"]
          }
        },

        {
          path: "planListQuery",
          name: "采购计划单查询",
          component: () =>
            import(
              "@/views/invoicingMan/statisticsQuery/purchasingManagement/planListQuery"
            ),
          meta: {
            title: "采购计划单查询",
            roles: ["jxc_caigoujihuadanchaxun"]
          }
        },
        {
          path: "returnedPlanQuery",
          name: "采购退货单查询",
          component: () =>
            import(
              "@/views/invoicingMan/statisticsQuery/returnedPlanQuery/index"
            ),
          meta: {
            title: "采购退货单查询",
            roles: ["jxc_caigoutuihuodanchaxun"]
          }
        }
      ]
    },

    // 库房管理
    {
      path: "storeMan",
      component: emptyView,
      meta: {
        title: "库房管理",
        roles: ["jxc_kufangguanli"]
      },
      children: [
        {
          path: "inStoreMan",
          name: "入库管理",
          component: () =>
            import("@/views/invoicingMan/storeMan/inStoreMan/index"),
          meta: {
            title: "入库管理",
            roles: ["jxc_rukuguanli"]
          }
        },
        {
          path: "outStoreMan",
          name: "出库管理",
          component: () =>
            import("@/views/invoicingMan/storeMan/outStoreMan/index"),
          meta: {
            title: "出库管理",
            roles: ["jxc_chukuguanli"]
          }
        },
        {
          path: "inventoryMan",
          name: "盘点管理",
          component: () =>
            import("@/views/invoicingMan/storeMan/inventoryMan/index"),
          meta: {
            title: "盘点管理",
            roles: ["jxc_pandianguanli"]
          }
        },

        {
          path: "income",
          name: "损益单查询",
          component: () => import("@/views/invoicingMan/storeMan/income/index"),
          meta: {
            title: "损益单查询",
            roles: ["jxc_sunyidanchaxun"]
          }
        },
        {
          path: "inventoryBook",
          name: "库存台账",
          component: () =>
            import("@/views/invoicingMan/storeMan/inventoryBook"),
          meta: {
            title: "库存台账",
            roles: ["jxc_kucuntaizhang"]
          }
        },
        {
          path: "repertoryQuery",
          name: "库存查询",
          component: () =>
            import("@/views/invoicingMan/statisticsQuery/repertoryQuery/index"),
          meta: {
            title: "库存查询",
            roles: ["jxc_kucunchaxun_web"]
          }
        }
      ]
    },

    // 财务管理
    {
      path: "financeMan",
      component: emptyView,
      meta: {
        title: "财务管理",
        roles: ["jxc_caiwuguanli"]
      },
      children: [
        {
          path: "repertoryQuery",
          name: "库存查询",
          component: () =>
            import("@/views/invoicingMan/statisticsQuery/repertoryQuery/index"),
          meta: {
            title: "库存查询",
            roles: ["jxc_kucunchaxun"]
          }
        },

        {
          path: "financeSettlement",
          name: "财务结算",
          component: () =>
            import("@/views/invoicingMan/financeMan/financeSettlement/index"),
          meta: {
            title: "财务结算",
            roles: ["jxc_caiwujiesuan"]
          }
        }
      ]
    },

    // 统计查询
    {
      path: "statisticsQuery",
      component: emptyView,
      meta: {
        title: "统计查询",
        roles: ["jxc_tongjichaxun"]
      },
      children: [
        {
          path: "materialDemandSheetQuery",
          name: "物资需求单查询",
          component: () =>
            import(
              "@/views/invoicingMan/statisticsQuery/materialDemandSheetQuery/index"
            ),
          meta: {
            title: "物资需求单查询",
            roles: ["jxc_wuzixuqiudanchaxun_web"]
          }
        },

        {
          path: "planListQuery",
          name: "采购计划单查询",
          component: () =>
            import(
              "@/views/invoicingMan/statisticsQuery/purchasingManagement/tjPlanListQuery"
            ),
          meta: {
            title: "采购计划单查询",
            roles: ["jxc_caigoujihuadanchaxun_web"]
          }
        },
        {
          path: "returnedPlanQuery",
          name: "采购退货单查询",
          component: () =>
            import(
              "@/views/invoicingMan/statisticsQuery/returnedPlanQuery/index"
            ),
          meta: {
            title: "采购退货单查询",
            roles: ["jxc_caigoutuihuodanchaxun_web"]
          }
        },
        {
          path: "inventoryQuery",
          name: "期初查询",
          component: () =>
            import("@/views/invoicingMan/statisticsQuery/inventoryQuery/index"),
          meta: {
            title: "期初查询",
            roles: ["jxc_qichuchaxun"]
          }
        },
        {
          path: "inStoreQuery",
          name: "入库查询",
          component: () =>
            import("@/views/invoicingMan/statisticsQuery/inStoreQuery/index"),
          meta: {
            title: "入库查询",
            roles: ["jxc_rukudanchaxun"]
          }
        },
        {
          path: "outStoreQuery",
          name: "出库查询",
          component: () =>
            import("@/views/invoicingMan/statisticsQuery/outStoreQuery/index"),
          meta: {
            title: "出库查询",
            roles: ["jxc_chukudanchaxun"]
          }
        },
        {
          path: "InventoryListQuery",
          name: "盘点单查询",
          component: () =>
            import(
              "@/views/invoicingMan/statisticsQuery/InventoryListQuery/index"
            ),
          meta: {
            title: "盘点单查询",
            roles: ["jxc_pandiandanchaxun"]
          }
        },
        {
          path: "incomeStatementQuery",
          name: "损益单查询",
          component: () =>
            import(
              "@/views/invoicingMan/statisticsQuery/incomeStatementQuery/index"
            ),
          meta: {
            title: "损益单查询",
            roles: ["jxc_sunyidanchaxun_web"]
          }
        },
        {
          path: "repertoryQuery",
          name: "库存查询",
          component: () =>
            import("@/views/invoicingMan/statisticsQuery/repertoryQuery/index"),
          meta: {
            title: "库存查询",
            roles: ["jxc_kucunchaxun_web"]
          }
        },
        {
          path: "electronicLedger",
          name: "电子台账报表",
          component: () =>
            import("@/views/invoicingMan/statisticsQuery/electronicLedger"),
          meta: {
            title: "电子台账报表",
            roles: ["jxc_dianzitaizhangbaobiao"]
          }
        }
      ]
    }
  ]
};

export default invoicingRouter;
