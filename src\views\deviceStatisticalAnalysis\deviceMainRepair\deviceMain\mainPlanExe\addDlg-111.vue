<template>
  <el-dialog
    class="mazhenguo"
    :title="dlgType === 'add' ? '添加计划执行' : '编辑计划执行'"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="800px"
    top="30px"
  >
    <el-form
      ref="dlgDataForm"
      :rules="dlgRules"
      :model="dlgData"
      label-position="right"
      label-width="120px"
      style="width: 750px"
      size="mini"
      @submit.native.prevent
      :disabled="dlgType == 'info'"
    >
      <el-form-item label="选择项目" prop="projectId">
        <el-select
          v-model="dlgData.projectId"
          placeholder="选择项目"
          @change="projectIdChange"
          filterable
          clearable
        >
          <el-option
            v-for="item of projectList"
            :key="item.id"
            :label="item.name"
            :value="parseInt(item.id)"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备名称" prop="equId">
        <el-select
          v-model="dlgData.equId"
          @change="equIdChange"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item of deviceSelect"
            :key="item.id"
            :label="item.equName2"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备型号" prop="equModel">
        <el-input
          v-model="dlgData.equModel"
          placeholder="请输入型号"
          disabled
        />
      </el-form-item>
      <el-form-item label="设备位置" prop="equPosition">
        <el-input
          v-model="dlgData.equPosition"
          placeholder="请输入设备所在位置"
          disabled
        />
      </el-form-item>
      <el-form-item label="配置保养周期" prop="maintenanceCycle">
        <!-- maintenanceCycle
             maintenanceCycleStr -->
        <el-select
          v-model="dlgData.maintenanceCycle"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item of pzbyzqSelect"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="计划保养日期" prop="maintenanceDate">
        <el-date-picker
          v-model="dlgData.maintenanceDate"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="请选择"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="保养内容" prop="maintenanceItem">
        <el-button
          @click="addItem('bynr')"
          icon="el-icon-plus"
          type="primary"
          plain
          >添加</el-button
        >
        <el-table
          class="mt10"
          :data="dlgData.maintenanceItem"
          fit
          border
          highlight-current-row
        >
          <el-table-column label="#" type="index" align="center" width="60">
          </el-table-column>
          <el-table-column label="保养项" width="130">
            <template slot-scope="scope">
              <div v-if="dlgType == 'info'">{{ scope.row.name }}</div>
              <el-input v-else v-model="scope.row.name" placeholder="请输入" />
            </template>
          </el-table-column>
          <el-table-column label="保养内容及要求">
            <template slot-scope="scope">
              <div v-if="dlgType == 'info'">{{ scope.row.value }}</div>
              <el-input v-else v-model="scope.row.value" placeholder="请输入" />
            </template>
          </el-table-column>
          <el-table-column
            v-if="dlgType != 'info'"
            label="操作"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                @click="delItem('bynr', scope.$index)"
                icon="el-icon-delete"
                size="mini"
                type="danger"
                title="删除"
                plain
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="执行情况" prop="invokeStatus">
        <!-- 0待执行 1已执行 -->
        <!-- invokeStatus
             invokeStatusStr -->
        <el-radio-group v-model="dlgData.invokeStatus">
          <el-radio
            v-for="item of axztSelect"
            :key="item.id"
            :label="item.id"
            >{{ item.name }}</el-radio
          >
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="dlgData.invokeStatus == '0'" label="执行情况备注" prop="invokeStatusInfo">
        <el-input
          :autosize="{ minRows: 4, maxRows: 5 }"
          v-model="dlgData.invokeStatusInfo"
          type="textarea"
          placeholder="请输入"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="更换配件" prop="accessoryJson">
        <el-button
          @click="addItem('ghpj')"
          icon="el-icon-plus"
          type="primary"
          plain
          >添加</el-button
        >
        <el-table
          class="mt10"
          :data="dlgData.accessoryJson"
          fit
          border
          highlight-current-row
        >
          <el-table-column label="#" type="index" align="center" width="60">
          </el-table-column>
          <el-table-column label="选项">
            <template slot-scope="scope">
              <div v-if="dlgType == 'info'">{{ scope.row.option }}</div>

              <el-select
                v-else
                v-model="scope.row.option"
                filterable
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item of optionSelect"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="单位" width="130">
            <template slot-scope="scope">
              <div v-if="dlgType == 'info'">{{ scope.row.unit }}</div>
              <el-input v-else v-model="scope.row.unit" placeholder="请输入" />
            </template>
          </el-table-column>
          <el-table-column label="数量" width="140">
            <template slot-scope="scope">
              <div v-if="dlgType == 'info'">{{ scope.row.num }}</div>

              <el-input-number
                v-model="scope.row.num"
                :min="0"
                placeholder="请输入"
                controls-position="right"
                style="width: 110px"
              />
            </template>
          </el-table-column>

          <el-table-column
            v-if="dlgType != 'info'"
            label="操作"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                @click="delItem('ghpj', scope.$index)"
                icon="el-icon-delete"
                size="mini"
                type="danger"
                title="删除"
                plain
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="执行人" prop="invokeUserName">
        <!-- invokeUserId   invokeUserName -->
        <el-input
          v-model="dlgData.invokeUserName"
          :title="dlgData.invokeUserName"
          @focus="showUserTree"
          placeholder="请选择"
        />
      </el-form-item>

      <el-form-item label="备注" prop="info">
        <el-input
          :autosize="{ minRows: 4, maxRows: 5 }"
          v-model="dlgData.info"
          type="textarea"
          placeholder="请输入"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="附件" prop="fileUrl">
        <qiniuUpload
          :file-list0="dlgData.fileUrl"
          ref="qiniuUploadRef"
          :limit="5"
          :max-size="5"
          @successBack="successBack"
          :dlg-type="dlgType"
        />
      </el-form-item>
      <!-- /////////////// -->
      <!-- <el-form-item label="保养计划id" prop="maintenanceId">
        <el-input
          v-model="dlgData.maintenanceId"
          placeholder="请输入保养计划id"
        />
      </el-form-item> -->
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button
        v-if="dlgType != 'info'"
        :loading="dlgSubLoading"
        type="success"
        @click="dlgSubFunc"
        icon="el-icon-check"
      >
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
    <Usertree :is-role="false" />
  </el-dialog>
</template>
<script>
import * as utils from "@/utils";
import { mapGetters } from "vuex";
import { postAction, getAction, putAction } from "@/api";
import qiniuUpload from "@/views/greenMan/components/qiniuUpload";
import Usertree from "@/components/Dialog/Usertree"; // 员工弹窗
let dlgDataEmpty = {
  id: undefined,
  maintenanceId: undefined, // 保养计划id
  equId: undefined, // 设备id
  equName: undefined,
  equModel: undefined, // 型号
  equPosition: undefined, // 设备所在位置
  fileUrl: undefined, // 附件地址

  invokeStatus: undefined, // = 执行状态  0待执行 1已执行
  invokeStatusStr: undefined,
  invokeStatusInfo: undefined, // 执行情况备注
  invokeTime: undefined, // 保养执行时间
  picUrl: undefined, // 图片地址
  maintenanceItem: [{ name: "", value: "" }], // 保养事项json
  accessoryJson: [{ option: "", optionStr: "", unit: "", num: undefined }], // 配件json
  invokeUserId: undefined, // 执行人id
  invokeUserName: undefined,
  info: undefined, // 备注

  maintenanceCycle: undefined, // = 保养周期
  maintenanceCycleStr: undefined,
  firstTime: undefined, // 初次保养时间
  projectId: undefined, // 项目
  projectName: undefined,
  maintenanceDate: undefined // 计划保养日期
};
export default {
  components: {
    qiniuUpload,
    Usertree
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {
      type: Object,
      default: {}
    },

    dlgData0: {
      type: Object,
      default: {}
    },
    projectList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {
    ...mapGetters([
      // 员工
      "userTreeGet"
    ])
  },
  watch: {
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          if (this.dlgType == "add") {
            this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
            this.$nextTick(() => {
              this.$refs["dlgDataForm"].clearValidate();
            });
          } else {
            this.getInit();
          }
        }, 50);
      } else {
        this.closeDlg();
      }
    },
    userTreeGet(val) {
      console.log("===返回的啥", val);
      if (val == "empty") {
        return false;
      }
      let list = JSON.parse(val);
      this.dlgData.invokeUserId = list[0].id;
      this.dlgData.invokeUserName = list[0].label;

      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    }
  },
  data() {
    return {
      dlgState: false,
      dlgLoading: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        projectId: [{ required: true, message: "必填字段", trigger: "change" }],
        equId: [{ required: true, message: "必填字段", trigger: "change" }],
        equModel: [{ required: true, message: "必填字段", trigger: "change" }],
        equPosition: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        maintenanceCycle: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        maintenanceDate: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        maintenanceItem: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        invokeStatus: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        invokeStatusInfo: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        invokeUserName: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        accessoryJson: [
          { required: true, message: "必填字段", trigger: "change" }
        ]
      },
      dlgSubLoading: false, // 提交loading

      deviceSelect: [],
      pzbyzqSelect: [],
      optionSelect: [],
      axztSelect: [{ id: 0, name: "待执行" }, { id: 1, name: "已执行" }]
    };
  },
  created() {
    this.getDataDict();
  },
  methods: {
    projectIdChange(val) {
      this.dlgData.equId = "";
      this.dlgData.equName = "";
      this.dlgData.equModel = "";
      this.dlgData.equPosition = "";

      this.getDeviceSelect(val);
    },
    // -- 表格事件
    addItem(type) {
      if (type == "bynr") {
        this.dlgData.maintenanceItem.push({
          name: "",
          value: ""
        });
      }
      if (type == "ghpj") {
        this.dlgData.accessoryJson.push({
          option: "",
          optionStr: "",
          unit: "",
          num: undefined
        });
      }

      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    },

    delItem(type, index) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        if (type == "bynr") {
          this.dlgData.maintenanceItem.splice(index, 1);
        }
        if (type == "ghpj") {
          this.dlgData.accessoryJson.splice(index, 1);
        }

        this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      });
    },
    // 数据字典
    getDataDict() {
      let keyList = (this.keyMap = [
        { dbKey: "maintenanceCycle", pageSelectKey: "pzbyzqSelect" }, // 配置保养周期
        { dbKey: "maintainPlanOptions", pageSelectKey: "optionSelect" } // 配件选项
      ]);
      utils.getDbItems(this, keyList);
    },
    successBack(fileList) {
      this.dlgData.fileUrl = fileList;
    },
    // 设备列表
    async getDeviceSelect(projectId = "") {
      try {
        let res0 = await getAction(
          `/green/equipment-manage/page?responsiblePersonId=${window.localStorage.userId}&equName=&pageNo=1&pageSize=200&projectId=${projectId}`
        );
        let res = res0.data;
        if (res.code == 200) {
          let list = res.data.list;
          for (let item of list) {
            item.equName2 = item.equName + "（" + item.equModel + "）";
          }
          this.deviceSelect = list;
        }
      } catch (err) {
        this.$message.error(err.msg);
      }
    },

    equIdChange(val) {
      let item = this.deviceSelect.filter(item => item.id == val)[0];
      console.log("====item", item);

      this.dlgData.equName = item.equName;
      this.dlgData.equModel = item.equModel;
      this.dlgData.equPosition = item.equPosition;

      this.dlgData.projectId = item.projectId;
      this.dlgData.projectName = item.projectName;

      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    },
    showUserTree() {
      this.$store.commit("SET_USERTREE_ISS", false); // 是否多选
      this.$store.commit("SET_USERTREE_DIATYPE", "");
      this.$store.commit("SET_USERTREESQTYPE", "");
      this.$store.commit("SET_USERTREESTATE", true);
    },
    //////////////////////////
    async getInit() {
      console.log("====dlgData0", this.dlgData0);
      let id = this.dlgData0.id;
      try {
        let res0 = await getAction(
          "/green/equ/maintenance-invoke/get?id=" + id
        );
        let res = res0.data;
        if (res && res.code == 200) {
          this.getDeviceSelect(res.data.projectId || "");
          let dlgData = JSON.parse(JSON.stringify(res.data));
          if (dlgData.maintenanceItem) {
            dlgData.maintenanceItem = JSON.parse(dlgData.maintenanceItem);
          } else {
            dlgData.maintenanceItem = []
          }
          console.log('===dlgData.accessoryJson', dlgData.accessoryJson)
          if (dlgData.accessoryJson) {
            dlgData.accessoryJson = JSON.parse(dlgData.accessoryJson);
          } else {
            dlgData.accessoryJson = []
          }
          if (dlgData.fileUrl) {
            dlgData.fileUrl = JSON.parse(dlgData.fileUrl);
          } else {
            dlgData.fileUrl = [];
          }
          this.dlgData = JSON.parse(JSON.stringify(dlgData));
        }
      } catch (err) {
        console.log("====错误", err);
        this.$message.error(err.msg);
      }
    },
    // 弹窗提交 ------
    dlgSubFunc() {
      this.$refs["dlgDataForm"].validate(valid => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));

          let maintenanceItem = sendObj.maintenanceItem.filter(item => {
            return item.name || item.value;
          });
          if (maintenanceItem.length == 0) {
            this.$message.warning("保养内容不能为空");
            return false;
          }

          let accessoryJson = sendObj.accessoryJson.filter(item => {
            return item.option || item.unit || item.num;
          });
          if (accessoryJson.length == 0) {
            this.$message.warning("配件列表不能为空");
            return false;
          }
          for (let item of accessoryJson) {
            item.optionStr = utils.arrId2Name(this.optionSelect, item.option);
          }

          sendObj.projectName = utils.arrId2Name(
            this.projectList,
            sendObj.projectId
          );

          sendObj.maintenanceCycleStr = utils.arrId2Name(
            this.pzbyzqSelect,
            sendObj.maintenanceCycle
          );
          sendObj.invokeStatusStr = utils.arrId2Name(
            this.axztSelect,
            sendObj.invokeStatus
          );

          sendObj.maintenanceItem = JSON.stringify(maintenanceItem);
          sendObj.accessoryJson = JSON.stringify(accessoryJson);
          sendObj.fileUrl = JSON.stringify(sendObj.fileUrl);

          let url = "";
          let func = "";
          if (this.dlgType == "add") {
            url = "/green/equ/maintenance-invoke/create";
            func = postAction;
          } else {
            url = "/green/equ/maintenance-invoke/update";
            func = putAction;
          }

          this.dlgSubLoading = true;
          func(url, sendObj).then(res0 => {
            this.dlgSubLoading = false;
            let res = res0.data;

            if (res.code == 200) {
              this.$message.success(res.msg);
              this.dlgState = false;
              this.$emit("getList");
              this.closeDlg();
              this.$emit("upList1");
            } else {
              this.$message({
                type: "warning",
                message: res.msg
              });
            }
          });
        }
      });
    },

    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.dlgState = false;
      // this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.$refs["dlgDataForm"].resetFields();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
