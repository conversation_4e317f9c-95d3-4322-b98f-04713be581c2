import request from '@/utils/request'

//基本参数
// 新增修改 
export function paramAdd (data) {
  return request({
    url: `/nbiot/base/param/add`,
    method: 'post',
    data
  })
}

// 分页
export function getByProjectId (projectId) {
  return request({
    url: `/nbiot/base/param/getByProjectId/${projectId}`,
    method: 'get',
  })
}


//假期参数
// 新增修改 
export function leaveAdd (data) {
    return request({
      url: `/nbiot/leave/param/add`,
      method: 'post',
      data
    })
  }

//分页
export function leavePage (data) {
    return request({
      url: `/nbiot/leave/param/page`,
      method: 'post',
      data
    })
  }
//详情
export function leaveInfo (id) {
    return request({
      url: `/nbiot/leave/param/info/${id}`,
      method: 'get',
    })
  }
//删除
export function leaveDel (id) {
    return request({
      url: `/nbiot/leave/param/del/${id}`,
      method: 'get',
    })
  }


//周末参数
// 新增修改 
export function weekAdd (data) {
    return request({
      url: `/nbiot/week/param/add`,
      method: 'post',
      data
    })
  }
// 分页
export function weekByProjectId (projectId) {
    return request({
      url: `/nbiot/week/param/getByProjectId/${projectId}`,
      method: 'get',
    })
  }

//分时参数
// 新增修改 
export function strategyAdd (data) {
    return request({
      url: `/nbiot/strategy/param/add`,
      method: 'post',
      data
    })
  }
// 分页
export function strategyPage (data) {
    return request({
      url: `/nbiot/strategy/param/page`,
      method: 'post',
      data
    })
  }
// 详情
export function strategyInfo (id) {
    return request({
      url: `/nbiot/strategy/param/info/${id}`,
      method: 'get',
    })
  }
// 删除
export function strategyDel (id) {
    return request({
      url: `/nbiot/strategy/param/del/${id}`,
      method: 'get',
    })
  }

//综合运行监控
// 楼宇温度
export function tempByBuilding (data) {
  return request({
    url: `/nbiot/loopLog/tempByBuilding`,
    method: 'post',
    data
  })
}
// 平均温度
export function tempByLoop (data) {
  return request({
    url: `/nbiot/loopLog/tempByLoop/v2`,
    method: 'post',
    data
  })
}
// 阀门信息
export function loopPage (data) {
  return request({
    url: `/nbiot/loop/page`,
    method: 'post',
    data
  })
}

