<template>
  <el-dialog
    class="mazhenguo"
    title="执行计划"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="800px"
    top="30px"
  >
    <el-form
      ref="dlgDataForm"
      :rules="dlgRules"
      :model="dlgData"
      label-position="right"
      label-width="120px"
      style="width: 750px"
      size="mini"
      @submit.native.prevent
      :disabled="dlgType == 'info'"
    >
      <formExe
        v-model="dlgData"
        :axztSelect="axztSelect"
        :optionSelect="optionSelect"
      >
      </formExe>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button
        v-if="dlgType != 'info'"
        :loading="dlgSubLoading"
        type="success"
        @click="dlgSubFunc"
        icon="el-icon-check"
      >
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import * as utils from "@/utils";

import {  getAction, putAction } from "@/api";

import formBase from "./form/formBase.vue";
import formExe from "./form/formExe.vue";
let dlgDataEmpty = {
  id: undefined,

  checkId: undefined, // 检修计划id
  equId: undefined, // 设备id
  equName: undefined,
  equModel: undefined, // 型号
  equPosition: undefined, // 设备所在位置
  fileUrl: undefined, // 附件地址

  invokeStatus: undefined, // = 执行状态  0待执行 1已执行
  invokeStatusStr: undefined,
  invokeStatusInfo: undefined, // 执行情况备注
  invokeTime: undefined, // 检修执行时间
  picUrl: undefined, // 图片地址
  checkItem: [{ name: "", value: "" }], // 检修事项json
  accessoryJson: [{ option: "", optionStr: "", unit: "", num: undefined }], // 配件json
  invokeUserId: undefined, // 执行人id
  invokeUserName: undefined,
  info: undefined, // 备注

  checkCycle: undefined, // = 检修周期
  checkCycleStr: undefined,
  firstTime: undefined, // 初次检修时间
  projectId: undefined, // 项目
  projectName: undefined,
  checkDate: undefined // 计划检修日期
};
export default {
  components: {
    formBase,
    formExe
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {
      type: Object,
      default: {}
    },

    dlgData0: {
      type: Object,
      default: {}
    },
    projectList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {},
  watch: {
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          if (this.dlgType == "add") {
            this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
            this.$nextTick(() => {
              this.$refs["dlgDataForm"].clearValidate();
            });
          } else {
            this.getInit();
          }
        }, 50);
      } else {
        this.closeDlg();
      }
    }
  },
  data() {
    return {
      dlgState: false,
      dlgLoading: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        projectId: [{ required: true, message: "必填字段", trigger: "change" }],
        equId: [{ required: true, message: "必填字段", trigger: "change" }],
        equModel: [{ required: true, message: "必填字段", trigger: "change" }],
        equPosition: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        checkCycle: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        checkDate: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        checkItem: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        invokeStatus: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        invokeStatusInfo: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        invokeUserName: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        accessoryJson: [
          { required: true, message: "必填字段", trigger: "change" }
        ]
      },
      dlgSubLoading: false, // 提交loading

      deviceSelect: [],
      pzbyzqSelect: [],
      optionSelect: [],
      axztSelect: [{ id: 0, name: "待执行" }, { id: 1, name: "已执行" }]
    };
  },
  created() {
    this.getDataDict();
  },
  methods: {
    getDataDict() {
      let keyList = (this.keyMap = [
        { dbKey: "checkCycle", pageSelectKey: "pzbyzqSelect" }, // 配置检修周期
        { dbKey: "maintainPlanOptions", pageSelectKey: "optionSelect" } // 配件选项
      ]);
      utils.getDbItems(this, keyList);
    },

    async getInit() {
      console.log("====dlgData0", this.dlgData0);
      let id = this.dlgData0.id;
      try {
        let res0 = await getAction(
          "/green/equ/check-invoke/get?id=" + id
        );
        let res = res0.data;
        if (res && res.code == 200) {
          let dlgData = JSON.parse(JSON.stringify(res.data));
          if (dlgData.checkItem) {
            dlgData.checkItem = JSON.parse(dlgData.checkItem);
          } else {
            dlgData.checkItem = [];
          }
          console.log("===dlgData.accessoryJson", dlgData.accessoryJson);
          if (dlgData.accessoryJson) {
            dlgData.accessoryJson = JSON.parse(dlgData.accessoryJson);
          } else {
            dlgData.accessoryJson = [];
          }
          if (dlgData.fileUrl) {
            dlgData.fileUrl = JSON.parse(dlgData.fileUrl);
          } else {
            dlgData.fileUrl = [];
          }

          dlgData.invokeUserId += "";

          // invokeUserId   invokeUserName

          this.dlgData = JSON.parse(JSON.stringify(dlgData));
        }
      } catch (err) {
        console.log("====错误", err);
        this.$message.error(err.msg);
      }
    },
    dlgSubFunc() {
      this.$refs["dlgDataForm"].validate(valid => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));

          let checkItem = sendObj.checkItem.filter(item => {
            return item.name || item.value;
          });
          if (checkItem.length == 0) {
            this.$message.warning("检修内容不能为空");
            return false;
          }

          let accessoryJson = sendObj.accessoryJson.filter(item => {
            return item.option || item.unit || item.num;
          });
          if (accessoryJson.length == 0) {
            this.$message.warning("配件列表不能为空");
            return false;
          }
          for (let item of accessoryJson) {
            item.optionStr = utils.arrId2Name(this.optionSelect, item.option);
          }

          sendObj.checkCycleStr = utils.arrId2Name(
            this.pzbyzqSelect,
            sendObj.checkCycle
          );
          sendObj.invokeStatusStr = utils.arrId2Name(
            this.axztSelect,
            sendObj.invokeStatus
          );

          sendObj.checkItem = JSON.stringify(checkItem);
          sendObj.accessoryJson = JSON.stringify(accessoryJson);
          sendObj.fileUrl = JSON.stringify(sendObj.fileUrl);

          let url = "/green/equ/check-invoke/updateStatus";

          this.dlgSubLoading = true;
          putAction(url, sendObj).then(res0 => {
            this.dlgSubLoading = false;
            let res = res0.data;

            if (res.code == 200) {
              this.$message.success(res.msg);
              this.dlgState = false;
              this.$emit("getList");
              this.closeDlg();
              this.$emit("upList1");
            } else {
              this.$message({
                type: "warning",
                message: res.msg
              });
            }
          });
        }
      });
    },

    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.dlgState = false;
      // this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.$refs["dlgDataForm"].resetFields();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
