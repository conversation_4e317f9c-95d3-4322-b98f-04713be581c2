<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">


          <div class="search-item">
            <el-select
              v-model="searchForm.modelId"
              placeholder="请选择运行记录模板"
              class="fl"
              style="width: 220px"
              filterable
              clearable
            >
              <el-option
                v-for="item in equModelsOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-item">
            <el-date-picker
              v-model="timeRange"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="fl"
              style="width: 220px"
            >
            </el-date-picker>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
            <!-- <el-button type="success" @click="showDlg('add')" icon="el-icon-plus" class="fl ml10">添加</el-button> -->
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc((100vh - 230px)/2)"
      ref="tableBar"
      class="m-small-table"
      v-loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column
        label="#"
        align="center"
        width="60"
        v-if="tableData.length !== 0"
      >
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(key, index) in objectKeys"
        :key="index"
        :prop="key"
        :label="formatLabel(key)"
        align="center"
      ></el-table-column>
    </el-table>
    <!-- 分页 -->
    <!-- <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="searchFunc()"
    /> -->
    <div class="clear"></div>
    <div id="lineChartId" style="height: calc((100vh - 230px) / 2)"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { postAction, getAction, deleteAction } from "@/api";
import { getDefaultDateRange } from "@/utils";

import Pagination from "@/components/Pagination"; // 分页
import { MxDfProject } from "@/mixins/MxDfProject.js";
export default {
  components: {
    Pagination,
    //   addEdit
  },
  mixins: [MxDfProject],

  data() {
    return {
      timeRange: undefined,
      chartInstance: null,
      chartData: {},
      searchForm: {
        statisticStartTime: "",
        statisticEndTime: "",
        projectId: JSON.parse(window.localStorage.userInfo).projectId,
        modelId: "",
        pageNo: 1,
        pageSize: 20,
      },
      originalData: {},
      equModelsOptions: [],
      tableData: [],
      total: 0,
      listLoading: false,
      projectList: [],
    };
  },
  computed: {
    // 计算属性，返回原始数据对象的所有键名
    objectKeys() {
      return Object.keys(this.originalData);
    },
  },
  created() {
    // this.projectList = this.userProjectsList;
  },
  mounted() {
    this.initChart();
    window.addEventListener('resize', this.handleResize);
    this.timeRange = getDefaultDateRange();

    this.getEquModelsOptions(JSON.parse(window.localStorage.userInfo).projectId);
  },
  methods: {
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    },
    initChart() {
      this.chartInstance = echarts.init(document.getElementById("lineChartId"));
    },
    updateChart() {
      if (
        !this.chartData ||
        !this.chartData.lineChartData ||
        this.chartData.lineChartData.length === 0
      ) {
        this.chartInstance.clear();
        return; // 提前返回，不执行后续的代码
      }else{
        this.chartInstance.clear();
      }
      const option = {
        title: {
          top: 15,
          left: 15,
          text: "运行记录模板折线图",
          textStyle: {
            color: "#262626",
            fontWeight: 500,
            fontSize: 18,
          },
        },
        legend: {
          top: "15", // 图例距离容器顶部的距离
          left: "200", // 图例距离容器左侧的距离
          data: this.chartData.lineChartData.map((item) => item.name),
        },
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          axisLabel: {
            color: "#666",
            interval: 0,
            // rotate: 10, // 文本倾斜度
            // textStyle: {
            //   align: "right", // 文本对齐方式
            //   verticalAlign: "middle", // 垂直对齐方式
            // },
          },
          axisTick: {
            show: false, //x轴刻度线
          },
          axisLine: {
            //X轴线
            show: true,
            lineStyle: {
              color: "#E0E0E0",
            },
          },
          data: this.chartData.category,
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#666",
          },
          splitLine: {
            lineStyle: {
              color: "#fff", // 虚线颜色
            },
          }, //去除背景网格线
        },
        grid: {
          top: "80px",
          left: "50px",
          right: "24px",
          bottom: "60px",
        },
        series: this.chartData.lineChartData.map((item, index) => ({
          name: item.name,
          type: "line",
          data: item.data,
          smooth: true, // 折线图是否平滑显示
          itemStyle: {
            color: this.getColor(index), // 自定义颜色函数，根据索引返回颜色值
          },
        })),
        //滚动条
        dataZoom: [
          {
            type: 'slider',
            show: this.chartData.category.length > 12 ? true : false,
            height: 32,
            bottom: 0,
            startValue: 0, //起始值
            endValue: 9, //结束值
            showDetail: false,
            fillerColor: "rgba(1, 132, 213, 0.4)", // 滚动条颜色
            borderColor: "rgba(17, 100, 210, 0.12)",
            backgroundColor: "rgba(221, 220, 107, .1)", //两边未选中的滑动条区域的颜色
            handleSize: 1, //两边手柄尺寸
            zoomLock: true, //是否只平移不缩放
            moveOnMouseMove: false, //鼠标移动能触发数据窗口平移
            // minValueSpan: 5,  // 放大到最少几个
            // maxValueSpan: 5,  //  缩小到最多几个
          },
          {
            type: "inside", // 支持内部鼠标滚动平移
            start: 0,
            // end: 20,
            startValue: 0, // 从头开始。
            endValue: 10, // 最多5个
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
          },
        ],
      };
      this.chartInstance.setOption(option);
    },
    getColor(index) {
      const predefinedColors = [
        "#FFBC00",
        "#FF4E00",
        "#5BF9C7",
        "#487DE8",
        "#60E272",
      ];
      if (index <= 5) {
        return predefinedColors[index]; // 直接使用预定义的颜色
      } else {
        // 生成随机颜色
        function getRandomColor() {
          const letters = "0123456789ABCDEF";
          let color = "#";
          for (let i = 0; i < 6; i++) {
            color += letters[Math.floor(Math.random() * 16)];
          }
          return color;
        }
        return getRandomColor(); // 返回随机生成的颜色
      }
    },
    formatLabel(key) {
      // 根据需要实现格式化逻辑，例如去除冒号和多余的空格
      return key.replace(/：/g, "").trim();
    },

    getEquModelsOptions(id) {
      getAction(
        `/green/equ/models/page?projectId=${id}&pageNo=1&pageSize=1000&`
      ).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          this.equModelsOptions = data.list ? data.list : [];
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    searchFunc() {
      this.searchForm.pageNo = 1;
      this.validateAndFetchData();
    },
    validateAndFetchData() {
      if (this.searchForm.projectId == "") {
        this.$message.warning("请先选择项目");
        return;
      }
      if (this.searchForm.modelId == "") {
        this.$message.warning("请先选择运行记录模板");
        return;
      }
      this.getTableData();
      this.getChartData();
    },
    getTableData() {
      let {
        modelId,
        pageNo,
        pageSize,
        projectId,
        statisticStartTime,
        statisticEndTime,
      } = this.searchForm;
      if (this.timeRange) {
        statisticStartTime = this.timeRange[0];
        statisticEndTime = this.timeRange[1];
      }

      this.listLoading = true;
      getAction(
        `/green/equ/operation-record/model-avg?projectId=${projectId}&modelId=${modelId}&pageNo=${pageNo}&pageSize=${pageSize}&statisticStartTime=${statisticStartTime}&statisticEndTime=${statisticEndTime}`
      ).then((res) => {
        this.listLoading = false;
        let { code, data } = res.data;
        if (code === "200") {
          this.originalData = data ? data : [];
          if (Object.keys(data).length === 0) {
            this.tableData = []; // 如果data是空对象，则设置为空数组
          } else {
            this.tableData = [data]; // 否则，将data包装成数组
          }
          this.total = data.total ? data.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    getChartData() {
      let {
        modelId,
        pageNo,
        pageSize,
        projectId,
        statisticStartTime,
        statisticEndTime,
      } = this.searchForm;
      if (this.timeRange) {
        statisticStartTime = this.timeRange[0];
        statisticEndTime = this.timeRange[1];
      }
      getAction(
        `/green/equ/operation-record/lineChart?projectId=${projectId}&modelId=${modelId}&pageNo=${pageNo}&pageSize=${pageSize}&statisticStartTime=${statisticStartTime}&statisticEndTime=${statisticEndTime}`
      ).then((res) => {
        this.listLoading = false;
        let { code, data } = res.data;
        if (code === "200") {
          this.chartData = data ? data : [];
          // console.log(this.chartData, "chartData");
          this.updateChart();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
</style>