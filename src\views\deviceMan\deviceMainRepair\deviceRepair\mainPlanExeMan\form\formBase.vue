<template>
  <div>
    <el-form-item label="选择项目" prop="projectId">
      <el-input v-model="dlgData.projectName" disabled> </el-input>
    </el-form-item>
    <el-form-item label="设备名称" prop="equId">
      <el-input v-model="dlgData.equName" disabled v-if="dlgType != 'add'">
      </el-input>
      <el-select
        v-else
        v-model="dlgData.equId"
        @change="equIdChange"
        filterable
        clearable
        placeholder="请选择"
        :disabled="dlgType !== 'add'"
      >
        <el-option
          v-for="item of deviceSelect"
          :key="item.id"
          :label="item.equName2"
          :value="item.id"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="设备型号" prop="equModel">
      <el-input v-model="dlgData.equModel" placeholder="请输入型号" disabled />
    </el-form-item>
    <el-form-item label="设备位置" prop="equPosition">
      <el-input
        v-model="dlgData.equPosition"
        placeholder="请输入设备所在位置"
        disabled
      />
    </el-form-item>
    <!-- <el-form-item label="配置检修周期" prop="checkCycle">
      <el-select
        v-model="dlgData.checkCycle"
        filterable
        clearable
        placeholder="请选择"
        :disabled="dlgType !== 'add'"
      >
        <el-option
          v-for="item of pzbyzqSelect"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
    </el-form-item> -->

    <el-form-item label="计划检修日期" prop="checkDate">
      <el-date-picker
        v-model="dlgData.checkDate"
        type="date"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        placeholder="请选择"
        style="width: 100%"
        :disabled="dlgType !== 'add'"
      />
    </el-form-item>
  </div>
</template>

<script>
import { postAction, getAction, putAction } from "@/api";

export default {
  props: {
    value: {
      type: Object,
      default: {},
    },
    pzbyzqSelect: {
      type: Array,
      default: [],
    },
    dlgType: {
      type: String,
      default: "add",
    },
  },

  data() {
    return {
      deviceSelect: [],
    };
  },
  computed: {
    dlgData: {
      get: function () {
        return this.value;
      },
      set: function (val) {
        this.$emit("input", val);
      },
    },
  },
  created() {
    this.getDeviceSelect(this.dlgData.projectId)
  },
  methods: {
    projectIdChange(val) {
      this.dlgData.equId = "";
      this.dlgData.equName = "";
      this.dlgData.equModel = "";
      this.dlgData.equPosition = "";
      this.getDeviceSelect(val);
    },
    async getDeviceSelect(projectId = "") {
      try {
        let res0 = await getAction(
          `/green/equipment-manage/page?responsiblePersonId=${JSON.parse(window.localStorage.userInfo).id}&equName=&pageNo=1&pageSize=200&projectId=${projectId}`
        );
        let res = res0.data;
        if (res.code == 200) {
          let list = res.data.list;
          for (let item of list) {
            item.equName2 = item.equName + "（" + item.equModel + "）";
          }
          this.deviceSelect = list;
        }
      } catch (err) {
        this.$message.error(err.msg);
      }
    },
    equIdChange(val) {
      let item = this.deviceSelect.filter((item) => item.id == val)[0];
      console.log("====item", item);

      this.dlgData.equName = item.equName;
      this.dlgData.equModel = item.equModel;
      this.dlgData.equPosition = item.equPosition;

      this.dlgData.projectId = item.projectId;
      this.dlgData.projectName = item.projectName;

      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    },
  },
};
</script>

<style lang="scss" scoped></style>
