<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区" prop="communityId">
          <el-select
            v-model="listQuery.communityId"
            filterable
            clearable
            placeholder="请选择小区"
            @change="communityChange"
          >
            <el-option
              v-for="item in communityList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在楼栋" prop="floorId">
          <el-select
            v-model="listQuery.floorId"
            filterable
            clearable
            placeholder="请选择楼栋"
            @change="floorChange"
          >
            <el-option
              v-for="item in buildingList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在单元" prop="unitId">
          <el-select
            v-model="listQuery.unitId"
            filterable
            clearable
            placeholder="请选择单元"
          >
            <el-option
              v-for="item in unitList"
              :key="item.id"
              :label="item.unitName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="房产类型" prop="roomTypeId">
          <el-select
            v-model="listQuery.roomTypeId"
            filterable
            clearable
            placeholder="请选择房产类型"
          >
            <el-option
              v-for="item in houseTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input
            @keyup.enter.native="getList"
            placeholder="请输入房屋名称"
            v-model="listQuery.label"
          >
            <i
              slot="suffix"
              @click="resetSearchItem(['label'])"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="getList"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="addItem"
          >新增房屋</el-button
        >
        <el-button
          icon="el-icon-download"
          size="mini"
          type="primary"
          @click="exportExcel"
        >
          Excel导出
        </el-button>
        <el-upload class="upload-wrap" action="" :before-upload="uploadItem">
          <el-button icon="el-icon-upload" size="mini" type="primary">
            Excel导入
          </el-button>
        </el-upload>
        <el-button
          icon="el-icon-download"
          type="primary"
          size="mini"
          @click="downloadItem()"
        >
          模板下载
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="房屋">
          <template slot-scope="scope">
            <span>{{ scope.row.roomFullName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="楼层">
          <template slot-scope="scope">
            <span>{{ scope.row.layer }}</span>
          </template>
        </el-table-column>

        <el-table-column label="业主">
          <template slot-scope="scope">
            <span>{{ scope.row.memberName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="电话">
          <template slot-scope="scope">
            <span>{{ scope.row.memberPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="房屋户型">
          <template slot-scope="scope">
            <span
              >{{ scope.row.sectionName }}{{ scope.row.apartmentName }}</span
            >
          </template>
        </el-table-column>

        <el-table-column label="建筑面积">
          <template slot-scope="scope">
            <span>{{ scope.row.builtUpArea }}</span>
          </template>
        </el-table-column>

        <el-table-column label="房屋类别">
          <template slot-scope="scope">
            <span>{{ scope.row.rommTypeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="房产类型">
          <template slot-scope="scope">
            <span>{{ scope.row.roomTypeName }}</span>
          </template>
        </el-table-column>
        <!-- 
        <el-table-column label="算费系数">
          <template slot-scope="scope">
            <span>{{ scope.row.feeCoefficient }}</span>
          </template>
        </el-table-column> -->

        <el-table-column label="房屋状态" align="center">
          <template slot-scope="scope">
            <span class="fsuccess" v-if="scope.row.state == 1">已入住</span>
            <span class="fwarning" v-else>未入住</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="240"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.state == 1"
              type="warning"
              size="mini"
              icon="el-icon-close"
              plain
              @click="unbindItem(scope.row)"
              >解绑</el-button
            >
            <el-button
              v-else
              type="success"
              size="mini"
              icon="el-icon-check"
              plain
              @click="bindItem(scope.row)"
              >绑定</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              plain
              @click="editItem(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              plain
              @click="delItem(scope.row, 1)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>

    <el-dialog
      :close-on-click-modal="false"
      title="新增/编辑房屋信息"
      :visible.sync="dlgShow"
      width="600px"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :rules="rules"
        :model="dlgData"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="类别" prop="type">
          <el-select
            v-model="dlgData.type"
            filterable
            clearable
            placeholder="请选择类别"
          >
            <el-option
              v-for="item in categoryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="所在小区" prop="communityId">
          <el-select
            v-model="dlgData.communityId"
            filterable
            clearable
            placeholder="请选择小区"
            @change="communityChange"
          >
            <el-option
              v-for="item in communityList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在楼栋" prop="floorId">
          <el-select
            v-model="dlgData.floorId"
            filterable
            clearable
            placeholder="请选择楼栋"
            @change="floorChange"
          >
            <el-option
              v-for="item in buildingListDlg"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在单元" prop="unitId">
          <el-select
            v-model="dlgData.unitId"
            filterable
            clearable
            placeholder="请选择单元"
          >
            <el-option
              v-for="item in unitListDlg"
              :key="item.id"
              :label="item.unitName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="房屋楼层" prop="layer">
          <!-- <el-input v-model="dlgData.layer" placeholder="请输入楼层" /> -->
          <el-input-number
            v-model="dlgData.layer"
            :min="0"
            :step-strictly="true"
            placeholder="请输入楼层"
            controls-position="right"
          />
        </el-form-item>

        <el-form-item label="房屋编号" prop="roomNum">
          <el-input v-model="dlgData.roomNum" placeholder="请输入编号" />
        </el-form-item>

        <el-form-item label="建筑面积">
          <el-input-number
            v-model="dlgData.builtUpArea"
            :controls="false"
            :min="0"
            :precision="2"
            :step="1"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="套内建筑面积">
          <el-input-number
            v-model="dlgData.insideArea"
            :controls="false"
            :min="0"
            :precision="2"
            :step="1"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="房屋户型">
          <el-select
            v-model="dlgData.section"
            filterable
            clearable
            placeholder="请选择室"
            style="width: 200px"
          >
            <el-option
              v-for="item in sectionList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="dlgData.apartment"
            filterable
            clearable
            placeholder="请选择厅"
            style="width: 200px"
          >
            <el-option
              v-for="item in apartmentList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 
        <el-form-item label="算费系数">
          <el-input-number v-model="dlgData.feeCoefficient" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
        </el-form-item> -->

        <el-form-item label="房屋类型" prop="roomType">
          <el-select
            v-model="dlgData.roomType"
            filterable
            clearable
            placeholder="请选择类别"
          >
            <el-option
              v-for="item in typeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="房产类型" prop="roomTypeId">
          <el-select
            @change="roomTypeChange"
            v-model="dlgData.roomTypeId"
            filterable
            clearable
            placeholder="请选择类别"
          >
            <el-option
              v-for="item in roomTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button
          type="success"
          :loading="dlgLoading"
          @click="subDlg"
          icon="el-icon-check"
        >
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <memberDlg />
  </div>
</template>

<script>
import Cookie from "js-cookie";
import { mapGetters } from "vuex";
import {
  roomTypePage,
  communityPage,
  cofloorCommunity,
  buildingunitFloor,
  buildingroomPage,
  buildingroomAddOrUpdate,
  buildingroomDisable,
  buildingmemberBinding,
  buildingmemberUnbundling,
  importAll,
} from "@/api/communityMan";
import * as utils from "@/utils";
import Pagination from "@/components/Pagination";
import { uploadImg } from "@/utils/uploadImg";
import memberDlg from "@/components/Dialog/communityMan/memberDlg";
import WorkSpaceBase from "@/components/WorkSpace/WorkSpaceBase";

let dlgDataEmpty = {
  id: "",
  communityId: "",
  communityName: "",
  floorId: "",
  floorName: "",
  apartment: "",
  apartmentName: "",
  builtUpArea: "",
  feeCoefficient: "",
  insideArea: "",
  layer: "",
  roomTypeId: "",
  roomTypeName: "",
  section: "",
  sectionName: "",
  type: "",
  typeName: "",
  unitId: "",
  unitName: "",
  roomNum: "",
  roomFullName: "",
  remark: "",
};

export default {
  name: "roomInfo",
  extends: WorkSpaceBase,
  components: {
    Pagination,
    memberDlg,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: "", // ADD\EDIT
      dlgTitle: "", // 标题

      rules: {
        unitId: [{ required: true, message: "必填字段", trigger: "change" }],
        floorId: [{ required: true, message: "必填字段", trigger: "change" }],
        communityId: [
          { required: true, message: "必填字段", trigger: "change" },
        ],
        type: [{ required: true, message: "必填字段", trigger: "change" }],
        roomTypeId: [
          { required: true, message: "请选择房产类型", trigger: "change" },
        ],
        layer: [{ required: true, message: "必填字段", trigger: "blur" }],
        roomNum: [{ required: true, message: "必填字段", trigger: "blur" }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgDataOld: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
        communityId: "",
        floorId: "",
        unitId: "",
      },
      communityList: [],
      buildingList: [],
      buildingListDlg: [],
      unitList: [],
      unitListDlg: [],
      userInfo: {},
      selectRow: {},
      // 字典
      categoryList: [],
      typeList: [],
      sectionList: [],
      apartmentList: [],

      houseTypeList:[],//房屋类型里
      roomTypeList:[],//房屋类型外
    };
  },
  computed: {
    ...mapGetters("communityMan/memberDlg", {
      memberId: "memberId",
    }),
  },
  watch: {
    memberId(val) {
      if (val && val != this.selectRow.memberId) {
        this.bindMember(val);
      }
    },
  },
  created() {
    this.getCommunityList();
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    utils.getDataDictMap(this, "roomCategory", "categoryList", 0);
    utils.getDataDict(this, "roomType", "typeList");
    utils.getDataDict(this, "roomSection", "sectionList");
    utils.getDataDict(this, "roomApartment", "apartmentList");
    if (
      this.$route.query.communityId &&
      this.$route.query.floorId &&
      this.$route.query.unitId
    ) {
      this.listQuery.communityId = parseInt(this.$route.query.communityId);
      this.listQuery.floorId = parseInt(this.$route.query.floorId);
      this.listQuery.unitId = parseInt(this.$route.query.unitId);
      cofloorCommunity(this.listQuery.communityId).then((res) => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data;
        }
      });
      buildingunitFloor(this.listQuery.floorId).then((res) => {
        if (res.data.code == 200) {
          this.unitList = res.data.data;
        }
      });
      this.addItem();
    }
  },

  methods: {
    // 导出
    exportExcel() {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery));
      exportParam.userId = this.userInfo.id;
      exportParam.projectId = this.userInfo.projectId;
      let param = Object.keys(exportParam)
        .map(function (key) {
          return (
            encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key])
          );
        })
        .join("&");

      let sendUrl =
        location.protocol +
        "//" +
        location.host +
        `/saapi/unity/report/exportRoom?` +
        param;
      window.open(sendUrl);
    },

    // 下载
    downloadItem() {
      let url =
        "https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E6%A5%BC%E6%A0%8B%E5%8D%95%E5%85%83%E6%88%BF%E5%B1%8B%E4%B8%9A%E4%B8%BB%E6%A8%A1%E6%9D%BF.xlsx";
      window.open(url);
    },

    // 上传
    uploadItem(file) {
      if (utils.isNull(this.listQuery.communityId)) {
        this.$message.warning("请选择小区");
        return false;
      }
      let name = file.name.split(".");
      let suffix = name[name.length - 1];

      if (suffix !== "xls" && suffix !== "xlsx") {
        this.$message({
          type: "warning",
          message: "只能上传xls/xlsx文件",
        });
        return false;
      }

      let loading = this.$loading({
        lock: true,
        text: "导入中",
        background: "rgba(0, 0, 0, 0.7)",
      });

      let postParam = {
        file,
        communityId: this.listQuery.communityId,
      };
      let communityItem = this.communityList.filter((item) => {
        return item.id == this.listQuery.communityId;
      });
      if (communityItem.length > 0) {
        postParam.communityName = communityItem[0]["name"];
      }

      importAll(postParam).then((res) => {
        loading.close();
        if (res.data.code == 200) {
          this.$message.success("导入成功");
          this.getList();
        } else {
          this.$message({
            type: "warning",
            message: res.data.msg,
          });
        }
      });

      return false;
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.getList();
    },

    communityChange() {
      let communityId;
      if (this.dlgShow) {
        communityId = this.dlgData.communityId;
        this.dlgData.floorId = "";
        this.dlgData.unitId = "";
        this.dlgData.roomTypeId = "";
        this.houseTypeList=[]
      } else {
        communityId = this.listQuery.communityId;
        this.listQuery.floorId = "";
        this.listQuery.unitId = "";
        this.dlgData.roomTypeId = "";
        this.houseTypeList=[]
      }
      console.log(communityId,'communityId');
      this.getBuildingList(communityId);
      this.getRoomType(communityId);
    },

    floorChange() {
      let floorId;
      if (this.dlgShow) {
        floorId = this.dlgData.floorId;
        this.dlgData.unitId = "";
      } else {
        floorId = this.listQuery.floorId;
        this.listQuery.unitId = "";
      }
      this.getUnitList(floorId);
    },

    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
      };
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data;
        }
      });
    },

    // 获取楼栋列表
    getBuildingList(id) {
      if (utils.isNull(id)) {
        return;
      }
      cofloorCommunity(id).then((res) => {
        if (res.data.code == 200) {
          if (this.dlgShow) {
            this.buildingListDlg = res.data.data;
          } else {
            this.buildingList = res.data.data;
          }
        }
      });
    },

    // 获取单元列表
    getUnitList(id) {
      if (utils.isNull(id)) {
        return;
      }
      buildingunitFloor(id).then((res) => {
        if (res.data.code == 200) {
          if (this.dlgShow) {
            this.unitListDlg = res.data.data;
          } else {
            this.unitList = res.data.data;
          }
        }
      });
    },
    // 获取房屋类型列表
    getRoomType(communityId) {
      if(communityId){
        let postParam = {
          page: 1,
          limit: 200,
          communityId:communityId
        };
        roomTypePage(postParam).then((res) => {
          if (res.data.code == 200) {
            this.roomTypeList = res.data.data;
            console.log( this.roomTypeList,"roomTypeList");
            if (this.dlgShow==true) {
              this.houseTypeList = res.data.data;
            } else {
              this.houseTypeList = res.data.data;
            }
          }
        });
        }
    },
    roomTypeChange(id) {
      this.dlgData.roomTypeName = this.houseTypeList.find(
        (item) => item.id == id
      ).name;
    },

    // 获取数据
    getList() {
      this.count++;
      this.listLoading = true;
      buildingroomPage(this.listQuery).then((res) => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data));
          this.total = res.data.page ? res.data.page.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 绑定成员
    bindMember(id) {
      if (utils.isNull(id)) {
        return;
      }
      let roomMembers = [
        {
          memberId: id,
          roomId: this.selectRow.id,
          roomName: this.selectRow.roomFullName,
          communityId: this.selectRow.communityId,
          type: 1,
        },
      ];
      let postParam = {
        roomMembers,
      };
      buildingmemberBinding(postParam).then((res) => {
        if (res.data.code == 200) {
          this.$message.success(res.data.msg);
          this.selectRow = {};
          this.getList();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 绑定
    bindItem(data) {
      this.selectRow = JSON.parse(JSON.stringify(data));
      let memberId = this.selectRow.memberId;
      this.$store.commit("communityMan/memberDlg/SET_MEMBERID", memberId);
      this.$store.commit("communityMan/memberDlg/SET_DLGTYPE", "");
      this.$store.commit("communityMan/memberDlg/SET_DLGSHOW", true);
    },

    // 解绑
    unbindItem(data) {
      this.$confirm("确定解除绑定", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        buildingmemberUnbundling(data.id, data.memberId).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.getList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },

    // 显示弹窗
    addItem() {
      this.dlgType = "ADD";
      this.dlgShow = true;
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.houseTypeList=[]
      this.dlgData.communityId = this.listQuery.communityId;
      this.dlgData.floorId = this.listQuery.floorId;
      this.dlgData.unitId = this.listQuery.unitId;
      this.getBuildingList(this.dlgData.communityId);
      this.getUnitList(this.dlgData.floorId);
      this.getRoomType(this.dlgData.communityId);
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },

    // 提交接口
    subAjax() {
      let postParam = JSON.parse(JSON.stringify(this.dlgData));
      postParam.projectId = this.userInfo.projectId;
      postParam.communityName = utils.getNameById(
        postParam.communityId,
        this.communityList
      );
      postParam.floorName = utils.getNameById(
        postParam.floorId,
        this.buildingListDlg
      );
      postParam.unitName = utils.getNameById(
        postParam.unitId,
        this.unitListDlg,
        "id",
        "unitName"
      );
      postParam.apartmentName = utils.getNameById(
        postParam.apartment,
        this.apartmentList
      );
      postParam.rommTypeName = utils.getNameById(
        postParam.roomType,
        this.typeList
      );
      postParam.sectionName = utils.getNameById(
        postParam.section,
        this.sectionList
      );
      postParam.typeName = utils.getNameById(postParam.type, this.categoryList);
      postParam.roomFullName = `${postParam.floorName}#${postParam.unitName}-${postParam.layer}${postParam.roomNum}`;
      this.dlgLoading = true;
      buildingroomAddOrUpdate(postParam).then((res) => {
        this.dlgLoading = false;
        if (res.data.code == 200) {
          this.getList();
          this.dlgShow = false;
          this.$message.success(res.data.msg);
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 弹窗提交
    subDlg() {
      this.$refs["dlgForm"].validate((valid) => {
        if (valid) {
          if (this.dlgType == "EDIT") {
            if (this.dlgData.builtUpArea == this.dlgDataOld.builtUpArea) {
              this.subAjax();
            } else {
              const h = this.$createElement;
              this.$msgbox({
                title: "房屋信息改动",
                message: h("div", { class: "confirmTip" }, [
                  h(
                    "p",
                    { class: "fwarning" },
                    "原面积:" + this.dlgDataOld.builtUpArea + "m²"
                  ),
                  h(
                    "p",
                    { class: "fsuccess" },
                    "更改后面积:" + this.dlgData.builtUpArea + "m²"
                  ),
                  h("p", null, "修改面积后，新产生计费会有变化，是否继续?"),
                ]),
                showCancelButton: true,
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                beforeClose: (action, instance, done) => {
                  if (action === "confirm") {
                    this.subAjax();
                    done();
                  } else {
                    done();
                  }
                },
              });
            }
          } else {
            this.subAjax();
          }
        }
      });
    },

    // 编辑
    editItem(data) {
      if(data.roomTypeId&&data.roomTypeName){
        data.roomTypeId=Number(data.roomTypeId)
      }else{
        data.roomTypeId=""
        data.roomTypeName=''
      }
      this.dlgData = JSON.parse(JSON.stringify(data));
      this.dlgDataOld = JSON.parse(JSON.stringify(data));
      this.dlgType = "EDIT";
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
        this.getBuildingList(this.dlgData.communityId);
        this.getRoomType(this.dlgData.communityId);
      this.getUnitList(this.dlgData.floorId);
    },
    // 启用停用
    delItem(data, flag) {
      let title = "确认删除?";
      if (flag == 0) {
        title = "确认启用?";
      } else if (flag == 2) {
        title = "确认停用?";
      }
      this.$confirm(title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        buildingroomDisable(data.id, flag).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.getList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>


