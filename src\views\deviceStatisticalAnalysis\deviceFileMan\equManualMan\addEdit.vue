<template>
  <el-dialog
    width="500px"
    :title="dlgType == 'add' ? '添加' : dlgType == 'edit' ? '编辑' : '详情'"
    :visible.sync="dialogVisible"
    append-to-body
    @close="onClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      label-width="80px"
      :disabled="dlgType == 'info'"
    >
      <el-form-item label="设备类型" prop="equType">
        <el-select
          ref="equTypeRef"
          v-model="formData.equType"
          placeholder="请选择设备类型"
          clearable
          :style="{ width: '100%' }"
        >
          <el-option
            v-for="(item, index) in equipmentTypeOptions"
            :key="index"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="文件名" prop="fileName">
        <el-input
          v-model="formData.fileName"
          placeholder="请输入文件名"
          clearable
          :style="{ width: '100%' }"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="附件" prop="fileUrl">
        <qiniuUpload
          :fileList0="formData.fileUrl"
          ref="qiniuUploadRef"
          :limit="5"
          :maxSize="50"
          @successBack="successBack"
        />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取 消</el-button>
      <el-button
        @click="handelConfirm"
        v-show="dlgType !== 'info'"
        type="success"
        icon="el-icon-check"
        :loading="btnLoading"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import * as utils from "@/utils";
import qiniuUpload from "@/views/greenMan/components/qiniuUpload";
import { postAction, getAction, putAction } from "@/api";
import { getDataDict } from "@/utils";

export default {
  components: {
    qiniuUpload,
  },
  props: {},
  data() {
    return {
      dlgType: "add",
      dialogVisible: false,
      btnLoading: false,
      equipmentTypeOptions: [],
      formData: {
        equType: undefined,
        fileName: undefined,
        fileUrl: [],
      },
      rules: {
        equType: [
          {
            required: true,
            message: "请选择设备类型",
            trigger: "change",
          },
        ],
        fileName: [
          {
            required: true,
            message: "请输入文件名",
            trigger: "blur",
          },
        ],
        fileUrl: [
          {
            required: true,
            message: "请上传附件",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    utils.getDataDictERP(this, "equipmentType", 'equipmentTypeOptions');
  },
  methods: {
    init(type, row) {
      this.dlgType = type;
      this.dialogVisible = true;
      if (type == "add") {
      } else {
        this.getInfo(row);
      }
    },
    getInfo(row) {
      getAction(`/green/equ/equipment-manage-manual/get?id=${row.id}`).then(
        (res) => {
          console.log(res.data);
          let { code, data } = res.data;
          if (code === "200") {
            data.fileUrl = JSON.parse(data.fileUrl);
            console.log(data.fileUrl);
            this.formData = data ? JSON.parse(JSON.stringify(data)) : [];
            console.log(data);
          } else {
            this.$message.error(res.data.msg);
          }
        }
      );
    },
    successBack(fileList) {
      this.formData.fileUrl = fileList;
    },
    onClose() {
      this.dialogVisible = false;
      this.$refs["elForm"].resetFields();
    },
    close() {
      this.dialogVisible = false;
      this.$refs["elForm"].resetFields();
    },
    handelConfirm() {
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          let postData = JSON.parse(JSON.stringify(this.formData));
          let fileList = [];
          for (let item of postData.fileUrl) {
            let obj = {
              name: item.name,
              url: item.url,
            };
            fileList.push(obj);
          }
          postData.fileUrl = JSON.stringify(fileList);
          postData.equTypeStr = this.$refs.equTypeRef.selected.label;
          console.log(postData, "postData");
          delete postData.createTime;
          delete postData.createUserName;
          if (this.dlgType == "edit") {
            putAction(
              `/green/equ/equipment-manage-manual/update`,
              postData
            ).then((res) => {
              this.btnLoading = false;
              if (res.data.code === "200") {
                this.$message({
                  type: "success", // success, warning, info, error
                  message: "编辑成功！",
                });
                this.dialogVisible = false;
                this.$refs.elForm.resetFields();
                this.$parent.searchFunc();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          } else {
            if (postData.id) {
              delete postData.id;
            }
            postAction(
              `/green/equ/equipment-manage-manual/create`,
              postData
            ).then((res) => {
              this.btnLoading = false;
              if (res.data.code === "200") {
                this.$message({
                  type: "success", // success, warning, info, error
                  message: "添加成功！",
                });
                this.dialogVisible = false;
                this.$refs.elForm.resetFields();

                this.$parent.searchFunc();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          }
        } else {
          return;
        }
      });
    },
  },
};
</script>

<style></style>
