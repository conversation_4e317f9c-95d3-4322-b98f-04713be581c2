<template>
  <div>
    <el-dialog
      title="选择员工"
      v-if="deviceType == 'PC'"
      :close-on-click-modal="false"
      :visible.sync="dlgState"
      append-to-body
      width="1000px"
      top="30px"
    >
      <div class="gzb-box">
        <div class="gzb-top">
          <el-input
            @keyup.enter.native="searchFunc"
            class="m-shaixuan-input fl"
            placeholder="关键字"
            v-model="listQuery.label"
            style="width: 200px"
          >
            <i
              @click="resetSearchItem(['label'])"
              slot="suffix"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
          <el-button
            icon="el-icon-search"
            type="primary"
            size="mini"
            class="search-right-btn fl"
            @click="searchFunc"
            >搜索</el-button
          >
          <div class="clear"></div>
        </div>

        <!-- class='m-small-table' -->
        <el-table
          v-loading="listLoading"
          style="height: calc(100vh - 400px); overflow: auto"
          class="m-small-table mt10"
          ref="tableRef"
          :data="list"
          border
          fit
          highlight-current-row
          @row-click="tableRowClick"
        >
          <el-table-column label="" align="center" width="60">
            <template slot-scope="scope">
              <el-radio
                v-model="selectList[0].id"
                :label="scope.row.id"
                style="width: 16px"
                ><span></span
              ></el-radio>
            </template>
          </el-table-column>
          <el-table-column label="#" align="center" width="60">
            <template slot-scope="scope">
              {{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}
            </template>
          </el-table-column>

          <el-table-column
            label="员工姓名"
            prop="label"
            align="center"
            width="100"
          ></el-table-column>
          <el-table-column
            label="工号"
            prop="workNum"
            align="center"
            width="100"
          ></el-table-column>

          <el-table-column
            label="所属部门"
            prop="departmentName"
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            label="手机号"
            prop="account"
            align="center"
            width="140"
          ></el-table-column>
        </el-table>

        <pagination
          class="mt10"
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
        <div class="clear"></div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
        <el-button type="success" @click="dlgSubFunc" icon="el-icon-check">
          <span>确定</span>
        </el-button>
      </div>
    </el-dialog>

    <popup
      v-else
      v-model="dlgState"
      :style="popupStyle"
      position="left"
      lazy-render
      safe-area-inset-bottom
    >
      <nav-bar
        title="请选择人员"
        left-text="返回"
        right-text="确定"
        left-arrow
        @click-left="closeDlg"
        @click-right="dlgSubFunc"
      />
      <div class="mobile-picker pre">
        <div style="padding: 15px">
          <!-- <el-input
          v-model="search"
          clearable
          prefix-icon="el-icon-search"
          placeholder="搜索人员，支持拼音、姓名"
        ></el-input> -->
          <el-input
            v-model="listQuery.label"
            @input="searchFunc"
            clearable
            prefix-icon="el-icon-search"
            placeholder="关键字"
            size="small"
          ></el-input>
        </div>

        <list
          v-model="listLoading"
          @load="getAppList"
          :finished="this.list.length >= this.total"
          finished-text="没有更多了"
          error-text="请求失败，点击重新加载"
        >
          <cell class="m-org-item" v-for="org in list" :key="org.id">
            <template #title>
              <div @click="selectChange(org)">
                <checkbox v-model="org.selected">
                  <avatar :name="org.label" :src="org.avatar"></avatar>
                </checkbox>
              </div>
            </template>
            <!-- <template #right-icon v-if="org.type === 'dept'">
              <div
                @click.stop="nextNode(org)"
                :class="{
                  'm-org-item-next': true,
                  'm-org-item-next-disabled': org.selected
                }"
              >
                <i class="iconfont icon-map-site"></i>
                <span> 下级</span>
              </div>
            </template> -->
          </cell>
        </list>
      </div>
    </popup>
  </div>
</template>
<script>
// 组件
import Pagination from "@/components/Pagination"; // 分页

// 工具
// import { phoneReg } from '@/utils/regUtil'
import * as utils from "@/utils";
import { postAction, getAction } from "@/api";

// 接口
import Bmtree from "@/components/Dialog/Bmtree";

// 移动端
import {
  Popup,
  List,
  Cell,
  NavBar,
  Radio,
  Checkbox,
  Dialog,
  Toast
} from "vant";
import Avatar from "@/components/workFlow/common/Avatar";

let listQueryEmpty = {
  label: "",
  page: 1,
  size: 20,

  branchType: "0",
  flag: "0",

  departmentId: "",
  departmentName: ""
};
export default {
  components: {
    Pagination,

    Popup,
    List,
    Cell,
    NavBar,
    Radio,
    Checkbox,
    Dialog,
    Toast,
    Avatar
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {},
    dlgState0: {
      type: Boolean,
      default: false
    },
    dlgData0: {},

    deviceType: {
      type: String,
      default: "PC"
    }
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val;
    },
    dlgState(val) {
      if (val) {
        console.log("val", val);
        setTimeout(() => {
          this.selectList = [this.dlgQuery];

          if (this.deviceType == "PC") {
            this.searchFunc();
          } else {
            this.listQuery.page = 0;
            this.getAppList();
          }
        }, 50);
      } else {
        this.total = 0;
        this.$emit("closeDlg");
      }
    }
  },
  data() {
    return {
      dlgTitle: "",
      dlgType1: "",
      dlgType2: "",

      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        name: [{ required: true, message: "必填字段", trigger: "blur" }],
        tableId: [{ required: true, message: "必填字段", trigger: "change" }]
      },
      dlgSubLoading: false, // 提交loading

      // 列表
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      ////
      selectList: "",

      // 移动端
      multiple: false,
      checkAll: false,
      // listLoadFinished: false, // list 加载
      popupStyle: {
        height: "100%",
        width: "100%",
        background: "#f7f7f9"
      }
    };
  },
  created() {},
  methods: {
    // ---- << 移动端
    selectChange(node) {
      console.log("===点击了吗", node);
      if (node.selected) {
        this.checkAll = false;
        for (let i = 0; i < this.selectList.length; i++) {
          if (this.selectList[i].id === node.id) {
            this.selectList.splice(i, 1);
            break;
          }
        }
        node.selected = false;
      } else {
        node.selected = true;
        let nodes = this.list;
        if (!this.multiple) {
          nodes.forEach(nd => {
            if (node.id !== nd.id) {
              nd.selected = false;
            }
          });
        }

        if (!this.multiple) {
          this.selectList = [node];
        } else {
          this.selectList.push(node);
        }
      }

      this.list = JSON.parse(JSON.stringify(this.list));
    },
    noSelected(index) {
      let nodes = this.list;
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === this.selectList[index].id) {
          nodes[i].selected = false;
          this.checkAll = false;
          break;
        }
      }
      // nodes = this.searchUsers;

      this.selectList.splice(index, 1);
    },
    handleCheckAllChange() {
      this.list.forEach(node => {
        if (this.checkAll) {
          if (!node.selected) {
            node.selected = true;
            this.selectList.push(node);
          }
        } else {
          node.selected = false;
          for (let i = 0; i < this.selectList.length; i++) {
            if (this.selectList[i].id === node.id) {
              this.selectList.splice(i, 1);
              break;
            }
          }
        }
      });
      this.list = JSON.parse(JSON.stringify(this.list));
    },
    clearSelected() {
      if (this.pcMode) {
        this.$confirm("您确定要清空已选中的项?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.checkAll = false;
          this.recover();
        });
      } else {
        Dialog.confirm({
          title: "提示",
          message: "您确定要清空已选项吗？"
        }).then(() => {
          this.checkAll = false;
          this.recover();
        });
      }
    },
    recover() {
      this.selectList = [];
      this.list.forEach(nd => (nd.selected = false));
    },
    // 获取列表
    getAppList() {
      this.listQuery.page++;
      if (this.listQuery.page == 1) {
        this.list = [];
      }

      let sendObj = { ...this.listQuery };
      sendObj.labelNum = sendObj.label.trim();
      delete sendObj.label;

      this.listLoading = true;
      postAction("/sys/findUserByLabelAndNum/v2", sendObj).then(res0 => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          if (utils.isNull(res.list)) {
            this.list = [];
            this.total = 0;
          } else {
            let list0 = res.list || [];

            if (this.selectList.length > 0) {
              for (let item of list0) {
                let isHas = this.selectList.some(row => row.id == item.id);
                if (isHas) {
                  item.selected = true;
                } else {
                  item.selected = false;
                }
              }
            } else {
              for (let item of list0) {
                item.selected = false;
              }
            }
            this.list = [...this.list, ...list0];
            this.total = res.data.total;
          }
        } else {
          this.total = 0;
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // ---- >> 移动端
    ////////////////
    tableRowClick(row, column, event) {
      console.log("row", row);
      this.selectList = [row];
    },

    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    getList() {
      this.list = [];
      this.listLoading = true;
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      sendObj.labelNum = sendObj.label;
      delete sendObj.label;

      postAction("/sys/findUserByLabelAndNum/v2", sendObj).then(res0 => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          this.list = res.list;
          this.total = res.data.total;

          this.$nextTick(() => {
            this.$refs.tableRef.doLayout();
          });
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.getList();
    },
    sortChange(data) {
      let type = data.column.sortBy;
      let order = data.order;
      if (order == null) {
        type = "";
        order = "";
      } else {
        if (order == "descending") {
          order = "desc";
        } else {
          order = "asc";
        }
      }
      this.listQuery.sortParam = type;
      this.listQuery.sortOrder = order;
      this.getList();
    },

    dlgSubFunc() {
      // console.log("selectRow", this.selectRow);
      this.$emit("dlgUserSubFunc", this.selectList[0]);
      this.closeDlg();
    },
    subAjaxBack(res0) {
      let res = res0.data;
      this.dlgSubLoading = false;
      if (res.code == 200) {
        this.$message.success(res.msg);
        this.dlgState = false;
        this.$emit("getList");
        this.$emit("closeDlg");
      } else {
        this.$message({
          type: "warning",
          message: res.msg
        });
      }
    },

    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.$emit("closeDlg");
    },

    upList1() {
      this.$emit("getList");
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import "~@/assets/workFlow/theme.scss";
// === 移动端
.mobile-picker {
  /deep/ .el-input {
    border-radius: 10px;
    i {
      font-size: 1.2rem;
    }
    .el-input__inner {
      background: #ecedef;
      border: none;
      border-radius: 5px;
      font-size: 1rem;
    }
  }
  .m-org-item-tab {
    margin-bottom: 10px;
    color: $theme-primary;
  }
  .m-org-item {
    color: #303133;
    font-size: 1.1rem !important;
    padding: 15px 15px !important;
    border-bottom: 1px solid $theme-aside-bgc;
    .m-org-item-next {
      cursor: pointer;
      color: $theme-primary;
      font-size: 1.1rem;
    }
    .m-org-item-next-disabled {
      cursor: not-allowed;
      color: #c8c8c8;
    }
    i {
      font-size: 1.1rem;
    }
    .to-top {
      cursor: pointer;
      color: $theme-primary;
    }
  }
}

::-webkit-scrollbar {
  float: right;
  width: 4px;
  height: 4px;
  background-color: white;
}

::-webkit-scrollbar-thumb {
  border-radius: 16px;
  background-color: #efefef;
}
</style>
