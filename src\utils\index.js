/**
 * Created by ji<PERSON><PERSON><PERSON> on 16/11/18.
 */

// 判断是否为 null
// if (!tmp && typeof(tmp)!="undefined" && tmp!=0){

import * as regs from "./regs";

import store from "../store";
import request from "@/utils/request";
import { return2Num } from "@/utils/calendarData";

import {
  findDdItme,
  findDdItmeByChars,
  findDdItmeOther,
  findDdItmeMap,
  findUntreatedNum,
  findDdItmeERP,
  getGreenMaopao
} from "@/api/dataDic";
import { bubbling } from "@/api/processMan";
const Qs = require("querystring");

export let return2Num2 = num => {
  if (num < 10) {
    num = "0" + num;
  }
  return num;
};
// 设置未读气泡
export function setBubble() {
  let userRoles = JSON.parse(decodeURI(window.localStorage.userRoles));
  if (userRoles.includes("kaoqinguanli")) {
    findUntreatedNum().then(res => {
      if (res.data.code == 200) {
        // 人脸审核 异常打卡 考勤报表 考勤管理
        if (res.data.data.faceUrlNum > 0) {
          $(".count-tip.faceAudit")
            .html(res.data.data.faceUrlNum)
            .show();
        } else {
          $(".count-tip.faceAudit")
            .html(res.data.data.faceUrlNum)
            .hide();
        }
        if (res.data.data.errerPunchNum > 0) {
          $(".count-tip.abnormalClock")
            .html(res.data.data.errerPunchNum)
            .show();
          $(".count-tip.attendanceReport")
            .html(res.data.data.errerPunchNum)
            .show();
        } else {
          $(".count-tip.abnormalClock")
            .html(res.data.data.errerPunchNum)
            .hide();
          $(".count-tip.attendanceReport")
            .html(res.data.data.errerPunchNum)
            .hide();
        }
        if (res.data.data.countNum > 0) {
          $(".count-tip.attendanceMan")
            .html(res.data.data.countNum)
            .show();
        } else {
          $(".count-tip.attendanceMan")
            .html(res.data.data.countNum)
            .hide();
        }
      }
    });
  } 


  // green_renwuzhixing_web  xingzhengOA
  if (userRoles.indexOf("xingzhengOA") >= 0) {
    let userInfo = JSON.parse(window.localStorage.userInfo)
    getGreenMaopao({userId: userInfo.id}).then(res => {
      if (res.data) {
        let todo = res.data.data || 0;
        if (todo > 0) {
          $("i.count-tip.taskExecute")
            .text(todo > 99 ? "99+" : todo)
            .show();

          $("i.count-tip.maintainMan")
            .text(todo > 99 ? "99+" : todo)
            .show();

          $("i.count-tip.greenMan")
            .text(todo > 99 ? "99+" : todo)
            .show();
        } else {
          $("i.count-tip.taskExecute").hide();
          $("i.count-tip.maintainMan").hide();
          $("i.count-tip.greenMan").hide();
        }
      }
    });
  }


  // } else {
  //   // bubbling().then((res) => {
  //   //   console.log(res);
  //   //   if (res.data.code == 200) {
  //   //     let data = res.data.data ? res.data.data : {}||0;
  //   //     // // 待办
  //   //     // let todoCount = data.todo || 0; // 总待办
  //   // let todoParam = {
  //   //   data,
  //   // };
  //   // window.localStorage.todoParam = JSON.stringify(todoParam);
  //   // // 待办
  //   // if (data > 0) {
  //   //   $("i.count-tip.daibanMatter")
  //   //     .text(data > 99 ? "99+" : data)
  //   //     .show();
  //   // } else {
  //   //   $("i.count-tip.daibanMatter").hide();
  //   // }
  //   //   }
  //   // });
  // }
}

// obj转成qs
export function object2qs(obj) {
  return Qs.stringify(obj);
}

/**
 * @获取屏幕dpi
 * */
export function getDpi() {
  var arrDPI = [];
  if (window.screen.deviceXDPI) {
    arrDPI[0] = window.screen.deviceXDPI;
    arrDPI[1] = window.screen.deviceYDPI;
  } else {
    var tmpNode = document.createElement("DIV");
    tmpNode.style.cssText =
      "width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden";
    document.body.appendChild(tmpNode);
    arrDPI[0] = parseInt(tmpNode.offsetWidth);
    arrDPI[1] = parseInt(tmpNode.offsetHeight);
    tmpNode.parentNode.removeChild(tmpNode);
  }
  console.log(arrDPI);
  return arrDPI;
}

/**
 * 获取屏幕dpi宽度
 * */
export function getDpiWidth(width) {
  var screen_dpi = getDpi();
  var dpi_width = (width / 25.4) * screen_dpi[0];
  return parseInt(dpi_width);
}

/**
 * 获取屏幕dpi高度
 * */
export function getDpiHeight(height) {
  var screen_dpi = getDpi();
  var dpi_height = (height / 25.4) * screen_dpi[1];
  return parseInt(dpi_height);
}

// 根据Id 获取 list 中的name
export function getNameById(id, list, idKey = "id", nameKey = "name") {
  let item = list.filter(item => {
    return item[idKey] == id;
  });
  if (item.length > 0) {
    return item[0][nameKey];
  }
  return "";
}
// 根据Id 获取 单元list 中的单元name
export function getNameById2(id, list, idKey = "id", nameKey = "unitName") {
  let item = list.filter(item => {
    return item[idKey] == id;
  });
  if (item.length > 0) {
    return item[0][nameKey];
  }
  return "";
}

export function addDate(date, days) {
  var d = new Date(date);
  d.setDate(d.getDate() + days);
  var m = d.getMonth() + 1;
  return (
    d.getFullYear() +
    "-" +
    (m >= 10 ? m : "0" + m) +
    "-" +
    (d.getDate() >= 10 ? d.getDate() : "0" + d.getDate())
  );
}

// 获取本月第一天到今天
export function returnToMonth() {
  let newDate = new Date();
  let year = newDate.getFullYear();
  let month = return2Num(newDate.getMonth() + 1);
  let today = return2Num(newDate.getDate());
  let times = [`${year}-${month}-01`, `${year}-${month}-${today}`];
  return times;
}

// 图片转对象
export function imgUrl2Obj(url) {
  let name = url.split("/");
  name = name[name.length - 1];
  let type = name.split(".")[1];
  if (type != "wav") type = "image";
  let obj = {
    type,
    url,
    name
  };
  return obj;
}

// 获取前/后月份
export function getDiffMonth(date, diff) {
  date = isNull(date) ? new Date() : new Date(date);
  diff = isNull(diff) ? 0 : diff;
  let month = date.getMonth() + diff;
  if (month < 0) {
    let n = parseInt(-month / 12);
    month += n * 12;
    date.setFullYear(date.getFullYear() - n);
  }
  date = new Date(date.setMonth(month));
  return getDateStr(date, "yyyy-MM");
}

// 获取当前时间 2019-01-01
export function getCurTimeYMD() {
  let today = new Date();
  let year = today.getFullYear();
  let month = return2Num(today.getMonth() + 1);
  let day = return2Num(today.getDate());
  let hour = return2Num(today.getHours());
  let minute = return2Num(today.getMinutes());
  let second = return2Num(today.getSeconds());
  return `${year}-${month}-${day}`;
}

// 计算日期差
export function diffDate(endDate, startDate) {
  if (isNull(endDate) || isNull(startDate)) {
    return 0;
  }
  return (
    (new Date(endDate).getTime() - new Date(startDate).getTime()) /
    (24 * 60 * 60 * 1000)
  );
}

// 计算月份差
export function diffMonth(endDate, startDate) {
  if (isNull(endDate) || isNull(startDate)) {
    return 0;
  }
  if (endDate === startDate) {
    return 1;
  }
  let endYear = endDate.substr(0, 4);
  let startYear = startDate.substr(0, 4);
  let endMonth = endDate.substr(5, 2);
  let startMonth = startDate.substr(5, 2);
  return (endYear - startYear) * 12 + (endMonth - startMonth);
}

// 获取日期字符串
export function getDateStr(date, format) {
  let year = date.getFullYear();
  let month = return2Num(date.getMonth() + 1);
  let day = return2Num(date.getDate());

  let hour = return2Num(date.getHours());
  let minutes = return2Num(date.getMinutes());
  let seconds = return2Num(date.getSeconds());
  if (format == "yyyy-MM") {
    return `${year}-${month}`;
  }
  return `${year}-${month}-${day} ${hour}:${minutes}:${seconds}`;
}

export function getToday() {
  let today = new Date();
  let year = today.getFullYear();
  let month = return2Num(today.getMonth() + 1);
  let day = return2Num(today.getDate());
  return `${year}-${month}-${day}`;
}

export function getWeek(date) {
  let week = isNull(date) ? new Date().getDay() : new Date(date).getDay();
  let weekList = ["日", "一", "二", "三", "四", "五", "六"];
  return weekList[week];
}

// 获取当前时间
export function getTotime(type) {
  let today = new Date();
  let year = today.getFullYear();
  let month = return2Num(today.getMonth() + 1);
  let day = return2Num(today.getDate());

  let hour = return2Num(today.getHours());
  let minutes = return2Num(today.getMinutes());
  let seconds = return2Num(today.getSeconds());

  let reutrnStr = "";
  if (type == "second") {
    reutrnStr = `${year}-${month}-${day} ${hour}:${minutes}`;
  } else {
    reutrnStr = `${year}-${month}-${day} ${hour}:${minutes}:${seconds}`;
  }
  return reutrnStr;
}

// 获取当前时间 2019-01-01 00:00:00
export function getCurTime() {
  let today = new Date();
  let year = today.getFullYear();
  let month = return2Num(today.getMonth() + 1);
  let day = return2Num(today.getDate());
  let hour = return2Num(today.getHours());
  let minute = return2Num(today.getMinutes());
  let second = return2Num(today.getSeconds());
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

// 阿里图片，ios 横向的，转成正常的
export function imgToNormal(imgUrl) {
  if (!imgUrl.includes("?x-oss-process=image")) {
    return imgUrl + "?x-oss-process=image/resize,w_300/auto-orient,1";
  } else {
    return imgUrl;
  }
}
// 发送数据，表单形式
export function requestForm(url, method, data) {
  return request({
    url: url,
    method,
    headers: {
      "Content-Type": "multipart/form-data"
    },

    transformRequest: [
      function(data) {
        let ret = "";
        for (let it in data) {
          ret +=
            encodeURIComponent(it) + "=" + encodeURIComponent(data[it]) + "&";
        }
        ret = ret.substr(0, ret.length - 1);
        return ret;
      }
    ],
    data
  });
}

// 上传excel 发送数据
export function requestExcel(url, data) {
  let formData = new FormData();
  for (let key in data) {
    let value = data[key];
    formData.append(key, value);
  }

  return request({
    url,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data"
    },
    data: formData
  });
}

export function num2week(num) {
  let week = "";
  switch (num) {
    case "0":
      week = "周日";
      break;
    case "1":
      week = "周一";
      break;
    case "2":
      week = "周二";
      break;
    case "3":
      week = "周三";
      break;
    case "4":
      week = "周四";
      break;
    case "5":
      week = "周五";
      break;
    case "6":
      week = "周六";
      break;
  }
  return week;
}

export function getDataDictMap(scope, groupChar, key, projectId) {
  findDdItmeMap(projectId, groupChar).then(res => {
    if (res.data.code == 200) {
      let list = res.data.list;
      let newList = [];
      for (let item of list) {
        newList.push({
          id: item.item_value,
          name: item.item_text
        });
      }
      scope[key] = newList.reverse();
    }
  });
}

// 获取数据字典数据
export function getDataDict(scope, groupChar, key) {
  let postParam = {
    groupChar,
    page: 1,
    size: 500
  };
  findDdItme(postParam).then(res => {
    if (res.data.code == 200) {
      let list = res.data.list;

      let newList = [];
      for (let item of list) {
        newList.push({
          id: item.itemValue,
          name: item.itemText
        });
      }
      scope[key] = newList.reverse();
    }
  });
}
export function getDataDictERP(scope, groupChar, key) {
  let postParam = {
    groupChar,
    page: 1,
    size: 500
  };
  findDdItmeERP(postParam).then(res => {
    if (res.data.code == 200) {
      let list = res.data.list;

      let newList = [];
      for (let item of list) {
        newList.push({
          id: item.itemValue,
          name: item.itemText
        });
      }
      scope[key] = newList.reverse();
    }
  });
}

// 获取数据字典数据(异步)
export function getDataDict2(groupChar) {
  return new Promise((resolve, reject) => {
    let postParam = {
      groupChar,
      page: 1,
      size: 500
    };
    findDdItme(postParam).then(res => {
      if (res.data.code == 200) {
        let list = res.data.list;

        let newList = [];
        for (let item of list) {
          newList.push({
            id: item.itemValue,
            name: item.itemText
          });
        }
        resolve(newList.reverse());
      } else {
        reject();
      }
    });
  });
}
export function getDataDictOther(scope, groupChar, key) {
  let postParam = {
    groupChar,
    page: 1,
    size: 500
  };
  findDdItmeOther(postParam).then(res => {
    if (res.data.code == 200) {
      let list = res.data.list;

      let newList = [];
      for (let item of list) {
        newList.push({
          id: item.itemValue,
          name: item.itemText
        });
      }
      scope[key] = newList.reverse();
    }
  });
}

// 获取数据字典集合
export function getDataDictByChars(scope, groupChars, key) {
  let postParam = {
    groupChars
  };
  findDdItmeByChars(postParam).then(res => {
    if (res.data.code == 200) {
      scope[key] = res.data.data;
    }
  });
}

// 获取数据字典集合
export function getDbItems(groupChars, projectId = 104) {
  console.log(222);
  return new Promise((resolve, reject) => {
    console.log(333);
    let keyArr = groupChars.split(",");
    let sendObj = {
      groupChars,
      projectId // 正式环境
      // projectId: 85,  // 测试环境
    };
    console.log(444);
    findDdItmeByChars(sendObj).then(res0 => {
      let res = res0.data;
      console.log(555, res);
      if (res.code == 200) {
        let obj = res.data;
        let returnList = [];
        for (let item of keyArr) {
          let resList0 = obj[item].reverse();
          let resList = [];
          for (let item1 of resList0) {
            let resList0ItemObj = {
              id: item1.itemValue + "",
              name: item1.itemText
            };
            resList.push(resList0ItemObj);
          }
          returnList.push(resList);
        }
        console.log("666", returnList);
        resolve(returnList);
      } else {
        reject();
      }
    });
  });
}
// ERP 数据字典
export function getDbItemsERP(scope, keyList) {
  for (let item of keyList) {
    getDataDictERP(scope, item.dbKey, item.pageSelectKey)
  }
}

// 从数据字典中 根据 ID 返回 文字
export function arrId2Name(arr, id) {
  let hasVal = false;
  for (let item of arr) {
    if (item.id === id) {
      hasVal = true;
      return item.name;
    }
  }

  // 判断 是否为空值
  if (!hasVal) {
    return "";
  }
}
// 从数据字典中 根据 名字 返回 ID
export function arrName2Id(arr, name) {
  let hasVal = false;
  for (let item of arr) {
    if (item.name === name) {
      hasVal = true;
      return item.id;
    }
  }

  // 判断 是否为空值
  if (!hasVal) {
    return "";
  }
}
export function arrId2Row(arr, id) {
  let hasVal = false;
  for (let item of arr) {
    if (parseInt(item.id) === parseInt(id)) {
      hasVal = true;
      return item;
    }
  }
  // 判断 是否为空值
  if (!hasVal) {
    return "";
  }
}
// 从数据字典中 根据 userType 返回 文字
export function arrUserType2Name(arr, userType) {
  let hasVal = false;
  for (let item of arr) {
    if (item.itemValue === userType) {
      hasVal = true;
      return item.itemText;
    }
  }
}

// 从数组中 根据 ID 返回 文字, 数组，id值，id对应字段，name对应字段
export function arrId2Name2(arr, id, idText, nameText) {
  let hasVal = false;
  for (let item of arr) {
    if (item[idText] === id) {
      hasVal = true;
      return item[nameText];
    }
  }
  // 判断 是否为空值
  if (!hasVal) {
    return "";
  }
}
// 关闭弹窗  作用于，数据对象str，表单str
export function closeDialog(commitKey, scope, formStr) {
  scope.$nextTick(() => {
    scope.$refs[formStr].resetFields();
  });
  store.commit(commitKey, false);
}

// 显示弹窗
export function showDialog(commitKey, type, jsonStr) {
  store.commit(commitKey, true);
  store.commit("SET_PROCESSDIALOGTYPE", "");
  store.commit("SET_PROCESSDIALOGISZHIHUI", false);
  store.commit("SET_PROCESSDIALOGTABLEKEY", "");
  store.commit("SET_PROCESSDIALOGJSONSTR", "");
  setTimeout(() => {
    store.commit("SET_PROCESSDIALOGTYPE", type);
    store.commit("SET_PROCESSDIALOGTABLEKEY", commitKey);
    store.commit("SET_PROCESSDIALOGJSONSTR", jsonStr);
  }, 50);
}

// 计算时间差，并保留小数点后一位
export function dateReduce(dateArr) {
  let start = new Date(dateArr[0]);
  let end = new Date(dateArr[1]);
  let timeReduce = end - start;
  timeReduce = timeReduce / 24 / 60 / 60 / 1000;
  timeReduce = Math.round(timeReduce * 10) / 10;
  return timeReduce;
}

// 计算小时数
export function reduce2Xs(dateArr) {
  let start = new Date(dateArr[0]);
  let end = new Date(dateArr[1]);
  let timeReduce = end - start;
  timeReduce = parseInt(timeReduce / 60 / 60 / 1000);
  return timeReduce;
}

// 修改 store 中的状态，先清空，再赋值，解决组件值不存在问题
export function myCommit(commitKey, commitVal) {
  store.commit(commitKey, "");
  setTimeout(() => {
    store.commit(commitKey, commitVal);
  }, 100);
}

// 返回当前月份呢
export function backMonthNow() {
  let dateObj = new Date();
  let month = dateObj.getFullYear() + "-" + return2Num(dateObj.getMonth() + 1);
  return month;
}
// 】】 自己的

export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (("" + time).length === 10) time = parseInt(time) * 1000;
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

export function formatTime(time, option) {
  time = +time * 1000;
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

export function getQueryObject(url) {
  url = url == null ? window.location.href : url;
  const search = url.substring(url.lastIndexOf("?") + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

/**
 *get getByteLen
 * @param {Sting} val input value
 * @returns {number} output value
 */
export function getByteLen(val) {
  let len = 0;
  for (let i = 0; i < val.length; i++) {
    if (val[i].match(/[^\x00-\xff]/gi) != null) {
      len += 1;
    } else {
      len += 0.5;
    }
  }
  return Math.floor(len);
}

export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

export function param(json) {
  if (!json) return "";
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
    })
  ).join("&");
}

export function param2Obj(url) {
  const search = url.split("?")[1];
  if (!search) {
    return {};
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"') +
      '"}'
  );
}

export function html2Text(val) {
  const div = document.createElement("div");
  div.innerHTML = val;
  return div.textContent || div.innerText;
}

export function objectMerge(target, source) {
  /* Merges two  objects,
     giving the last one precedence */

  if (typeof target !== "object") {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property];
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      target[property] = sourceProperty;
    }
  });
  return target;
}

export function toggleClass(element, className) {
  if (!element || !className) {
    return;
  }
  let classString = element.className;
  const nameIndex = classString.indexOf(className);
  if (nameIndex === -1) {
    classString += "" + className;
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length);
  }
  element.className = classString;
}

// 比较时间
export function compareTime(startTime, endTime) {
  let start = 0;
  let end = 0;
  if (!isNull(startTime)) {
    start =
      parseInt(startTime.split(":")[0]) * 60 +
      parseInt(startTime.split(":")[1]);
  }
  if (!isNull(endTime)) {
    end =
      parseInt(endTime.split(":")[0]) * 60 + parseInt(endTime.split(":")[1]);
  }
  return start >= end;
}

// 秒转时间
export function second2Time(second) {
  let time = Number(second);
  let perDay = 24 * 60 * 60;
  time = time % perDay;
  let h =
    time / 3600 < 10 ? "0" + parseInt(time / 3600) : parseInt(time / 3600);
  if (h != "00") {
    time = time - h * 3600;
  }
  let m = time / 60 < 10 ? "0" + parseInt(time / 60) : parseInt(time / 60);
  return {
    timeStr: h + ":" + m,
    days: parseInt(second / perDay) + 1
  };
}
// 秒数相加减
export function calcSecond(second, minute) {
  return second + (minute || 0) * 60;
}

// 时间转秒
export function time2Second(time) {
  if (isNull(time)) {
    return 0;
  }
  let hour = parseInt(time.split(":")[0]);
  let minute = parseInt(time.split(":")[1]);
  return hour * 60 * 60 + minute * 60;
}

export const pickerOptions = [
  {
    text: "今天",
    onClick(picker) {
      const end = new Date();
      const start = new Date(new Date().toDateString());
      end.setTime(start.getTime());
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "最近一周",
    onClick(picker) {
      const end = new Date(new Date().toDateString());
      const start = new Date();
      start.setTime(end.getTime() - 3600 * 1000 * 24 * 7);
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "最近一个月",
    onClick(picker) {
      const end = new Date(new Date().toDateString());
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "最近三个月",
    onClick(picker) {
      const end = new Date(new Date().toDateString());
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      picker.$emit("pick", [start, end]);
    }
  }
];

export function getTime(type) {
  if (type === "start") {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    return new Date(new Date().toDateString());
  }
}

export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function(...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "shallowClone");
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

export function uniqueArr(arr) {
  return Array.from(new Set(arr));
}

export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

export function formatDate1(date) {
  // var date = new Date(optDate)
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();

  if (parseInt(month) < 10) {
    month = "0" + month;
  }
  if (parseInt(day) < 10) {
    day = "0" + day;
  }

  var newDate = `${year}-${month}-${day}`;
  return newDate;
}

// http url修改
export function objToParam(obj) {
  var params = "?";
  for (let item in obj) {
    var key = item;
    var val = obj[item];
    params += `${key}=${val}&`;
  }
  params = params.substr(0, params.length - 1);
  return params;
}
// 判断是否是数字
// precision-小数点位数
export function strToNum(str, precision = 2) {
  if (!isNull(str)) {
    let number = parseFloat(str);
    if (Object.is(number, NaN)) {
      number = "";
    } else {
      number = num2Round(number, precision);
    }
    return number;
  } else {
    return "";
  }
}

// http url修改 /a/b/c
export function arrToParam(arr) {
  var params = "";
  for (let item of arr) {
    params += `/${item}`;
  }
  return params;
}

// bpmn 转 activiti
// 去掉xml中的闭合标签，并自闭合
export function bpmnProcess(xmlStr, strArr) {
  let mXml = xmlStr;
  // let mXml = xmlStr.replace(/"/g, 'shuangyinhao')

  for (let item of strArr) {
    let itemArr = mXml.split("<" + item);

    for (let i = 0; i < itemArr.length; i++) {
      if (i !== 0) {
        let arr2 = itemArr[i].split(">");
        // 自闭合
        arr2[0] += "/";
        itemArr[i] = arr2.join(">");
        // 去掉闭合标签
        let arr3 = itemArr[i].split("</" + item + ">");
        let arr4 = arr3[0].split(">");
        for (let y = 0; y < arr4.length; y++) {
          if (y !== 0) {
            arr4[y] = "";
          }
        }
        arr4[0] += ">";
        arr3[0] = arr4.join("");
        itemArr[i] = arr3.join("");
      }
    }
    mXml = itemArr.join("<" + item);
  }
  return mXml;
}
// 去掉xml标签中内容
export function bpmnClearText(xmlStr, strArr) {
  let mXml = xmlStr;
  // let mXml = xmlStr.replace(/"/g, 'shuangyinhao')
  for (let item of strArr) {
    let itemArr = mXml.split("<" + item);
    for (let i = 0; i < itemArr.length; i++) {
      if (i !== 0) {
        let arr2 = itemArr[i].split("</" + item + ">");
        arr2[0] = "";
        itemArr[i] = arr2.join("");
      }
    }
    mXml = itemArr.join("");
  }
  return mXml;
}

// 判断obj是否为空
export function isEmptyObject(obj) {
  return JSON.stringify(obj) === "{}";
}

// 判断是否为空
export function isNull(data) {
  return data === "" || data === undefined || data === null;
}

// 判断是否是json
export function isJSON(str) {
  if (typeof str == "string") {
    try {
      let obj = JSON.parse(str);
      if (typeof obj == "object" && obj) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}

// 获取月份最大天数
export function getMaxDate(yearMonth) {
  let year = parseInt(yearMonth.split("-")[0]);
  let month = parseInt(yearMonth.split("-")[1]);
  if ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0) {
    if (month == 2) {
      return 29;
    }
  }
  let maxDateList = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  return maxDateList[month - 1];
}
// 获取月份最大天数
export function getLxMaxDate(month) {
  let maxDateList = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  return maxDateList[month - 1];
}
// 获取最近一个月
export function getLastMonth() {
  var end = new Date();
  var year = end.getFullYear();
  var month = end.getMonth() + 1; //0-11表示1-12月
  var day = end.getDate();
  var dateObj = {};
  dateObj.end = year + "-" + return2Num(month) + "-" + return2Num(day);
  var endMonthDay = new Date(year, month, 0).getDate(); //当前月的总天数
  if (month - 1 <= 0) {
    //如果是1月，年数往前推一年<br>
    dateObj.start = year - 1 + "-" + 12 + "-" + return2Num(day);
  } else {
    var startMonthDay = new Date(year, parseInt(month) - 1, 0).getDate();
    if (startMonthDay < day) {
      //1个月前所在月的总天数小于现在的天日期
      if (day < endMonthDay) {
        //当前天日期小于当前月总天数
        dateObj.start =
          year +
          "-" +
          return2Num(month - 1) +
          "-" +
          return2Num(startMonthDay - (endMonthDay - day));
      } else {
        dateObj.start =
          year + "-" + return2Num(month - 1) + "-" + return2Num(startMonthDay);
      }
    } else {
      dateObj.start =
        year + "-" + return2Num(month - 1) + "-" + return2Num(day);
    }
  }
  // console.log(JSON.stringify(dateObj))
  return dateObj;
}

// 判断全屏
export function isFullscreen() {
  return (
    document.fullscreenElement ||
    document.msFullscreenElement ||
    document.mozFullScreenElement ||
    document.webkitFullscreenElement ||
    false
  );
}

//
export function dealBigMoney(money) {
  //汉字的数字
  var cnNums = new Array(
    "零",
    "壹",
    "贰",
    "叁",
    "肆",
    "伍",
    "陆",
    "柒",
    "捌",
    "玖"
  );
  //基本单位
  var cnIntRadice = new Array("", "拾", "佰", "仟");
  //对应整数部分扩展单位
  var cnIntUnits = new Array("", "万", "亿", "兆");
  //对应小数部分单位
  var cnDecUnits = new Array("角", "分", "毫", "厘");
  //整数金额时后面跟的字符
  var cnInteger = "整";
  //整型完以后的单位
  var cnIntLast = "元";
  //最大处理的数字
  var maxNum = 999999999999999.9999;
  //金额整数部分
  var integerNum;
  //金额小数部分
  var decimalNum;
  //输出的中文金额字符串
  var chineseStr = "";
  //分离金额后用的数组，预定义
  var parts;
  if (money == "") {
    return "";
  }
  money = parseFloat(money);
  if (money >= maxNum) {
    //超出最大处理数字
    return "";
  }
  if (money == 0) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger;
    return chineseStr;
  }
  //转换为字符串
  money = money.toString();
  if (money.indexOf(".") == -1) {
    integerNum = money;
    decimalNum = "";
  } else {
    parts = money.split(".");
    integerNum = parts[0];
    decimalNum = parts[1].substr(0, 4);
  }
  //获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
    var zeroCount = 0;
    var IntLen = integerNum.length;
    for (var i = 0; i < IntLen; i++) {
      var n = integerNum.substr(i, 1);
      var p = IntLen - i - 1;
      var q = p / 4;
      var m = p % 4;
      if (n == "0") {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0];
        }
        //归零
        zeroCount = 0;
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
      }
      if (m == 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q];
      }
    }
    chineseStr += cnIntLast;
  }
  //小数部分
  if (decimalNum != "") {
    var decLen = decimalNum.length;
    for (var i = 0; i < decLen; i++) {
      var n = decimalNum.substr(i, 1);
      if (n != "0") {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }
  if (chineseStr == "") {
    chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum == "") {
    chineseStr += cnInteger;
  }
  return chineseStr;
}

//////////
export function num2Round(num, digit = 2) {
  if (!isNull(num)) {
    num = parseFloat(num);

    if (isNaN(num)) {
      return "";
    }

    let digitNum = 1;
    for (let i = 0; i < digit; i++) {
      digitNum += "0";
    }
    digitNum = parseInt(digitNum);

    let newNum = Math.round(num * digitNum) / digitNum;
    return newNum;
  } else {
    return "";
  }
}

// 是否是数字
export function isNumber(val) {
  let numReg = /^-?\d+$|^-?\d+\.\d+$/;
  return numReg.test(val);
}

export function formatCurrency(money) {
  //汉字的数字
  var cnNums = new Array(
    "零",
    "壹",
    "贰",
    "叁",
    "肆",
    "伍",
    "陆",
    "柒",
    "捌",
    "玖"
  );
  //基本单位
  var cnIntRadice = new Array("", "拾", "佰", "仟");
  //对应整数部分扩展单位
  var cnIntUnits = new Array("", "万", "亿", "兆");
  //对应小数部分单位
  var cnDecUnits = new Array("角", "分", "毫", "厘");
  //整数金额时后面跟的字符
  var cnInteger = "整";
  //整型完以后的单位
  var cnIntLast = "元";
  //最大处理的数字
  var maxNum = 999999999999999.9999;
  //金额整数部分
  var integerNum;
  //金额小数部分
  var decimalNum;
  //输出的中文金额字符串
  var chineseStr = "";
  //分离金额后用的数组，预定义
  var parts;
  if (money == "") {
    return "";
  }
  money = parseFloat(money);
  if (money >= maxNum) {
    //超出最大处理数字
    return "";
  }
  if (money == 0) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger;
    return chineseStr;
  }
  //转换为字符串
  money = money.toString();
  if (money.indexOf(".") == -1) {
    integerNum = money;
    decimalNum = "";
  } else {
    parts = money.split(".");
    integerNum = parts[0];
    decimalNum = parts[1].substr(0, 4);
  }
  //获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
    var zeroCount = 0;
    var IntLen = integerNum.length;
    for (var i = 0; i < IntLen; i++) {
      var n = integerNum.substr(i, 1);
      var p = IntLen - i - 1;
      var q = p / 4;
      var m = p % 4;
      if (n == "0") {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0];
        }
        //归零
        zeroCount = 0;
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
      }
      if (m == 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q];
      }
    }
    chineseStr += cnIntLast;
  }
  //小数部分
  if (decimalNum != "") {
    var decLen = decimalNum.length;
    for (var i = 0; i < decLen; i++) {
      var n = decimalNum.substr(i, 1);
      if (n != "0") {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }
  if (chineseStr == "") {
    chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum == "") {
    chineseStr += cnInteger;
  }
  return chineseStr;
}
//////////////////////// << 其他工具方法 ////////////////////////
// 身份证号 获取 性别 年龄
export let getInfoByIdNumber = idNumber => {
  if (!regs.sfzReg.test(idNumber)) {
    return "";
  }

  // 出生日期
  let year = idNumber.substr(6, 4);
  let month = idNumber.substr(10, 2);
  let day = idNumber.substr(12, 2);

  // 周岁
  let nowDate = new Date();
  let birthDate = new Date(year, month - 1, day);
  let charDate = nowDate - birthDate;

  let idNumberAge = charDate / 1000 / 60 / 60 / 24 / 365;
  idNumberAge = parseInt(idNumberAge);
  let sex = parseInt(idNumber.substr(16, 1));
  if (sex % 2 == 1) {
    sex = "男";
  } else {
    sex = "女";
  }
  let info = {
    sex,
    age: idNumberAge
  };
  return info;
};
//////////////////////// >> 其他工具方法 ////////////////////////
// 身份证验证
//---- << 身份证号验证
var vcity = {
  11: "北京",
  12: "天津",
  13: "河北",
  14: "山西",
  15: "内蒙古",
  21: "辽宁",
  22: "吉林",
  23: "黑龙江",
  31: "上海",
  32: "江苏",
  33: "浙江",
  34: "安徽",
  35: "福建",
  36: "江西",
  37: "山东",
  41: "河南",
  42: "湖北",
  43: "湖南",
  44: "广东",
  45: "广西",
  46: "海南",
  50: "重庆",
  51: "四川",
  52: "贵州",
  53: "云南",
  54: "西藏",
  61: "陕西",
  62: "甘肃",
  63: "青海",
  64: "宁夏",
  65: "新疆",
  71: "台湾",
  81: "香港",
  82: "澳门",
  91: "国外"
};
export let sfzReg2 = function(idNumber) {
  //是否为空
  if (idNumber === "") {
    return "证件号码不能为空！";
  }
  //校验长度，类型
  if (isCardNo(idNumber) === false) {
    return "您输入的证件号码不正确，请重新输入";
  }
  // 检查省份
  if (checkProvince(idNumber) === false) {
    return "您输入的证件号码不正确,请重新输入";
  }
  // 校验生日
  if (checkBirthday(idNumber) === false) {
    return "您输入的证件号码生日不正确,请重新输入";
  }
  //检验位的检测
  if (checkParity(idNumber) === false) {
    return "您的证件号码校验位不正确,请重新输入";
  }
  return "";
};
//检查号码是否符合规范，包括长度，类型
let isCardNo = function(card) {
  //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
  var reg = /(^\d{15}$)|(^\d{17}(\d|X)$)/;
  if (reg.test(card) === false) {
    return false;
  }

  return true;
};

//取身份证前两位,校验省份
let checkProvince = function(card) {
  var province = card.substr(0, 2);
  if (vcity[province] == undefined) {
    return false;
  }
  return true;
};

//检查生日是否正确
let checkBirthday = function(card) {
  var len = card.length;
  //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
  if (len == "15") {
    var re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
    var arr_data = card.match(re_fifteen);
    var year = arr_data[2];
    var month = arr_data[3];
    var day = arr_data[4];
    var birthday = new Date("19" + year + "/" + month + "/" + day);
    return verifyBirthday("19" + year, month, day, birthday);
  }
  //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
  if (len == "18") {
    var re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/;
    var arr_data = card.match(re_eighteen);
    var year = arr_data[2];
    var month = arr_data[3];
    var day = arr_data[4];
    var birthday = new Date(year + "/" + month + "/" + day);
    return verifyBirthday(year, month, day, birthday);
  }
  return false;
};

//校验日期
let verifyBirthday = function(year, month, day, birthday) {
  var now = new Date();
  var now_year = now.getFullYear();
  //年月日是否合理
  if (
    birthday.getFullYear() == year &&
    birthday.getMonth() + 1 == month &&
    birthday.getDate() == day
  ) {
    //判断年份的范围（3岁到100岁之间)
    var time = now_year - year;
    if (time >= 3 && time <= 100) {
      return true;
    }
    return false;
  }
  return false;
};

//校验位的检测
let checkParity = function(card) {
  //15位转18位
  card = changeFivteenToEighteen(card);
  var len = card.length;
  if (len == "18") {
    var arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
    var arrCh = new Array(
      "1",
      "0",
      "X",
      "9",
      "8",
      "7",
      "6",
      "5",
      "4",
      "3",
      "2"
    );
    var cardTemp = 0,
      i,
      valnum;
    for (i = 0; i < 17; i++) {
      cardTemp += card.substr(i, 1) * arrInt[i];
    }
    valnum = arrCh[cardTemp % 11];
    if (valnum == card.substr(17, 1)) {
      return true;
    }
    return false;
  }
  return false;
};

//15位转18位身份证号
let changeFivteenToEighteen = function(card) {
  if (card.length == "15") {
    var arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
    var arrCh = new Array(
      "1",
      "0",
      "X",
      "9",
      "8",
      "7",
      "6",
      "5",
      "4",
      "3",
      "2"
    );
    var cardTemp = 0,
      i;
    card = card.substr(0, 6) + "19" + card.substr(6, card.length - 6);
    for (i = 0; i < 17; i++) {
      cardTemp += card.substr(i, 1) * arrInt[i];
    }
    card += arrCh[cardTemp % 11];
    return card;
  }
  return card;
};
let debounceTimer = null;
export function debounceFunc(duration = 500) {
  return new Promise((resolve, reject) => {
    if (!isNull(debounceTimer)) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = setTimeout(() => {
      debounceTimer = null;
      resolve();
    }, duration);
  });
}

/**
 * 获取当前月份的默认日期范围
 *
 * @returns 返回当前月份的第一天和最后一天，格式为字符串数组，例如 ['2023-10-01', '2023-10-31']
 */
export function getDefaultDateRange() {
  const currentMonth = new Date();
  
  // 获取当前月份的第一天
  const startOfMonth = new Date(currentMonth);
  startOfMonth.setDate(1); // 将日期设置为当前月的第一天
  
  // 获取当前月份的最后一天
  const endOfMonth = new Date(currentMonth);
  endOfMonth.setMonth(currentMonth.getMonth() + 1, 0); // 设置为下一个月的第一天，然后减去一天得到当前月的最后一天

  // 使用 toISOString() 方法并截取日期部分
  const startDate = startOfMonth.toISOString().split('T')[0];
  const endDate = endOfMonth.toISOString().split('T')[0];

  return [startDate, endDate];
}