<template>
  <div>
    <!-- mode DESIGN PC MOBILE -->
    <div v-if="mode === 'DESIGN'">
      <el-button disabled icon="el-icon-user" type="primary" size="mini" round
        >选择岗位</el-button
      >
      <span class="placeholder"> {{ placeholder }}</span>
    </div>
    <div v-else-if="mode === 'PC' && !readonly" style="max-width: 350px">
      <el-button
        icon="el-icon-user"
        type="primary"
        size="mini"
        round
        @click="showPostMulDlg"
        >选择岗位</el-button
      >
      <span class="placeholder"> {{ placeholder }}</span>
      <selectPostMulDlg
        v-if="multiple"
        :deviceType="mode"
        :dlgState0="dlgPostMulState"
        :dlgData0="dlgPostMulData"
        :dlgType="dlgPostMulType"
        :dlgQuery="dlgPostMulQuery"
        :selectList0="dlgPostMulSelectList"
        @closeDlg="closePostMulDlg"
        @backFunc="dlgPostMulBackFunc"
      />

      <selectPostDlg
        v-else
        :deviceType="mode"
        :dlgState0="dlgPostState"
        :dlgData0="dlgPostData"
        :dlgType="dlgPostType"
        :dlgQuery="dlgPostQuery"
        @closeDlg="closePostDlg"
        @backFunc="dlgPostBackFunc"
      />
      <!-- <org-picker
        type="user"
        :multiple="multiple"
        ref="orgPicker"
        :selected="_value"
        @ok="selected"
      /> -->

      <div style="margin-top: 5px">
        <el-tag
          size="mini"
          style="margin: 5px"
          closable
          v-for="(dept, i) in _value"
          :key="i"
          @close="delDept(i)"
          >{{ dept.name }}</el-tag
        >
      </div>
    </div>
    <div v-else-if="mode === 'MOBILE' && !readonly">
      <!-- $refs.orgPicker.show() -->
      <field
        readonly
        clearable
        @clear="_value = []"
        right-icon="arrow"
        clickable
        v-model="deptDesc"
        :placeholder="placeholder"
        @click="showPostMulDlg"
      ></field>

      <selectPostMulDlg
        v-if="multiple"
        :deviceType="mode"
        :dlgState0="dlgPostMulState"
        :dlgData0="dlgPostMulData"
        :dlgType="dlgPostMulType"
        :dlgQuery="dlgPostMulQuery"
        :selectList0="dlgPostMulSelectList"
        @closeDlg="closePostMulDlg"
        @backFunc="dlgPostMulBackFunc"
      />

      <selectPostDlg
        v-else
        :deviceType="mode"
        :dlgState0="dlgPostState"
        :dlgData0="dlgPostData"
        :dlgType="dlgPostType"
        :dlgQuery="dlgPostQuery"
        @closeDlg="closePostDlg"
        @backFunc="dlgPostBackFunc"
      />
      <!-- <org-picker
        :pc-mode="false"
        type="user"
        :multiple="multiple"
        ref="orgPicker"
        :selected="_value"
        @ok="selected"
      /> -->
    </div>
    <div v-else class="preview">
      <el-tag
        size="mini"
        style="margin: 5px"
        v-for="(user, index) in _value"
        :key="index"
        >{{ user.name }}</el-tag
      >

      <!-- <avatar
        :size="35"
        :name="user.name"
        showY
        :src="user.avatar"
        v-for="(user, index) in _value"
        :key="index"
      /> -->
    </div>
  </div>
</template>

<script>
import Avatar from "@/components/workFlow/common/Avatar";
import { Field } from "vant";
import componentMinxins from "../ComponentMinxins";
import OrgPicker from "@/components/workFlow/common/OrgPicker";
import selectPostMulDlg from "@/components/DialogWflow/selectPostMulDlg";
import selectPostDlg from "@/components/DialogWflow/selectPostDlg";
export default {
  mixins: [componentMinxins],
  name: "PostPicker",
  components: {
    Field,
    // OrgPicker, //
    selectPostMulDlg,
    selectPostDlg,
    Avatar
  },
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      }
    },
    placeholder: {
      type: String,
      default: "请选择岗位"
    },
    multiple: {
      type: Boolean,
      default: false
    },
    expansion: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  computed: {
    deptDesc: {
      get() {
        if (this._value && this._value.length > 1) {
          return `${this._value[0].name}${this._value[1].name}..等${
            this._value.length
          }人`;
        } else if (this._value && this._value.length > 0) {
          return this._value[0].name;
        } else {
          return null;
        }
      },
      set(val) {}
    }
  },
  data() {
    return {
      showOrgSelect: false,

      dlgPostMulQuery: {},
      dlgPostMulState: false,
      dlgPostMulType: "", // 弹框状态add, edit
      dlgPostMulData: {},
      dlgPostMulSelectList: "",

      dlgPostQuery: {},
      dlgPostState: false,
      dlgPostType: "", // 弹框状态add, edit
      dlgPostData: {},
      dlgPostSelectList: ""
    };
  },
  methods: {
    showPostDlg() {
      let list = JSON.parse(JSON.stringify(this._value));
      list.forEach(item => {
        item.label = item.name;
      });

      this.dlgPostQuery = {
        postStatus: "" //岗位状态 0未绑定员工 1 已绑定员工
      };

      this.dlgPostType = "edit";
      this.dlgPostState = true;
    },
    // 关闭弹窗
    closePostDlg() {
      this.dlgPostState = false;
    },
    dlgPostBackFunc(list0) {
      console.log("弹窗发挥", list0);
      let select = [];
      if (list0[0].label) {
        for (let item of list0) {
          let obj = {
            type: "post",
            id: item.id,
            name: item.label,

            isLeader: null,
            avatar: "",
            sex: null,
            selected: null
          };

          select.push(obj);
        }
      }

      this.showOrgSelect = false;
      this._value = select;
    },

    ////////
    showPostMulDlg() {
      if (!this.multiple) {
        this.showPostDlg();
        return false;
      }

      let list = JSON.parse(JSON.stringify(this._value));
      list.forEach(item => {
        item.label = item.name;
      });

      this.dlgPostMulSelectList = list.length
        ? JSON.parse(JSON.stringify(list))
        : "";

      this.dlgPostMulQuery = {
        postStatus: "" //岗位状态 0未绑定员工 1 已绑定员工
      };

      this.dlgPostMulType = "edit";
      this.dlgPostMulState = true;
    },
    // 关闭弹窗
    closePostMulDlg() {
      this.dlgPostMulState = false;
    },
    dlgPostMulBackFunc(list0) {
      console.log("弹窗发挥", list0);
      let select = [];
      for (let item of list0) {
        let obj = {
          type: "post",
          id: item.id,
          name: item.label,

          isLeader: null,
          avatar: "",
          sex: null,
          selected: null
        };

        select.push(obj);
      }

      this.showOrgSelect = false;
      this._value = select;
    },

    ////////
    selected(values) {
      console.log("===values", values);
      this.showOrgSelect = false;
      this._value = values;
    },
    delDept(i) {
      this._value.splice(i, 1);
    }
  }
};
</script>

<style scoped>
.placeholder {
  margin-left: 10px;
  color: #adabab;
  font-size: smaller;
}
.preview {
  display: flex;
  justify-content: left;
  /deep/ .avatar {
    margin: 0 5px;
  }
}
</style>
