import request from '@/utils/request'

// 考勤日报
export function attendanceDaily (data) {
  return request({
    url: '/workade/kaoqinribao',
    method: 'post',
    data
  })
}

// 考勤月报
export function attendanceMonth (data) {
  return request({
    url: '/workade/kaoqinyuebao',
    method: 'post',
    data
  })
}

// 考勤汇总
export function attendanceSummary (data) {
  return request({
    url: '/workade/kaoqinhuizongbiao',
    method: 'post',
    data
  })
}

// 计算考勤月报
export function calculateMonthlyReport (month) {
  return request({
    url: `/workade/calculateMonthlyReport/${month}`,
    method: 'get'
  })
}

// 考勤月报
export function attendanceMonthRecord (data) {
  return request({
    url: '/workade/punchRecord/kaoqinjilu',
    method: 'post',
    data
  })
}
// 打卡记录表
export function checkInRecord (data) {
  return request({
    url: '/workade/dakajilubiao',
    method: 'post',
    data
  })
}