<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="时间筛选：">
          <el-date-picker
            :clearable="false"
            v-model="listQuery.rangeDate"
            type="daterange"
            range-separator="~"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="打卡类型：">
          <el-select v-model="listQuery.punchType" clearable placeholder="请选择打卡类型">
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择员工：">
          <el-input v-model="listQuery.userName" @focus="showUserDlg" placeholder="请选择员工" readonly>
            <i @click='resetSearchItem(["userId", "userName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="getList"
          >搜索</el-button
        >
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="打卡人">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="地点">
          <template slot-scope="scope">
            <span>{{ scope.row.punchLocation }}</span>
          </template>
        </el-table-column>

        <el-table-column label="时间">
          <template slot-scope="scope">
            <span>{{ scope.row.punchTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="打卡类型">
          <template slot-scope="scope">
            <span v-if="scope.row.punchType == '1'">上班打卡</span>
            <span v-else-if="scope.row.punchType == '2'">中间打卡</span>
            <span v-else>下班打卡</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-view" @click="showAddFunc(scope.row)" plain
            >查看详情</el-button
          >
        </template>
      </el-table-column>
        <!-- <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')">详情</el-button>
            <el-button type="primary" size="mini" icon="el-icon-check" plain @click="sendItem(scope.row)">回单</el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>

    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>
    <userDlg />

     <!-- << 打卡详情 -->
     <el-dialog
      top="30px"
      title="打卡详情"
      :close-on-click-modal="false"
      width="650px"
      append-to-body
      :visible.sync="dkState"
    >
      <el-form ref="dkForm" :model="dkData" label-position="right" label-width="100px" style="width: 570px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="员工姓名">
              <!-- <el-input v-model="wckqdData.name" placeholder="请输入岗位名称"/> -->
              <el-input v-model="dkData.userName" :disabled="true" />
            </el-form-item>
            <p class="m-item-box-btips" style="margin-left: 100px">
              <span>部门：{{ dkData.branchName }}</span>
              <!-- <span>岗位：{{ dkData.postName }}</span> -->
            </p>
          </el-col>
        </el-row>

        <el-form-item label="打卡时间">
          <el-input v-model="dkData.punchTime" :disabled="true" />
        </el-form-item>

        <el-form-item label="打卡地址">
          <el-input :title="dkData.punchLocation" v-model="dkData.punchLocation" :disabled="true" />
        </el-form-item>

        <el-form-item label="外出事由">
          <el-input
            :autosize="{ minRows: 2, maxRows: 2 }"
            v-model="dkData.remark"
            type="textarea"
            resize="none"
            :disabled="true"
          />
        </el-form-item>

        <!-- 左侧 打卡图片，右侧位置信息 -->

        <el-row style="width: 560px; margin: 0 auto">
          <el-col :span="11">
            <p style="text-align: center">打卡照片</p>
            <el-image class="wckqd-img" :preview-src-list="[dkData.faceUrl]" :src="dkData.faceUrl" alt=""></el-image>

            <!-- imgToNormal -->
            <!-- <el-image class='wckqd-img'
              :preview-src-list="['https://wlines.oss-cn-beijing.aliyuncs.com/hr/punch/2020-2/20200203073931_43.jpg?x-oss-process=image/resize,w_300/auto-orient,1']"
              src="https://wlines.oss-cn-beijing.aliyuncs.com/hr/punch/2020-2/20200203073931_43.jpg?x-oss-process=image/resize,w_300/auto-orient,1" alt=""></el-image> -->
          </el-col>
          <el-col :span="13">
            <p style="text-align: center">打卡地点</p>
            <div id="wckqmap1"></div>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dkState = false" icon="el-icon-back">返回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from "js-cookie";
import { mapGetters } from "vuex";
import {
  communityPage,
  cofloorCommunity,
  buildingunitFloor,
  buildingroomPage,
} from "@/api/communityMan";

import { outPunchPage } from "@/api/punchMan/report.js";
import userDlg from '@/components/Dialog/communityMan/userDlg'
import * as utils from "@/utils";
import Pagination from "@/components/Pagination";
import { uploadImg } from "@/utils/uploadImg";
import WorkSpaceBase from "@/components/WorkSpace/WorkSpaceBase";

let dlgDataEmpty = {
  id: 0,
  communityId: "",
  communityName: "",
  danyuan: "",
  danyuanId: "",
  fangwu: "",
  fangwuId: "",
  loudong: "",
  loudongId: "",
  priceScope: "",
  repairName: "",
  repairObjName: "",
  repairObjType: "",
  repairType: "",
  repairTypeText: "",
  tel: "",
  type: "",
  userName: "",
  userId: "",
  remark: "",
  payMoney: "",
  list: [],
};

export default {
  name: "repairTodo",
  extends: WorkSpaceBase,
  components: {
    Pagination,
    userDlg
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 1000 * 60 * 60 * 24;
        },
      },
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
        punchType: "",
        rangeDate: "",
        projectId: "",
        userId: "",
        userName:"",
      },
      options:[
        {id:1,label:'上班打卡'},
        {id:2,label:'中间打卡'},
        {id:3,label:'下班打卡'}
      ],

      dkState:false,
      dkData:{}
    };
  },

  computed: {
     ...mapGetters('communityMan/userDlg', {
      userId: 'userId',
      userName: 'userName',
    }),
  },
  watch: {
    userId (val) {
      this.listQuery.userId = val
    },
    userName (val) {
      this.listQuery.userName = val
    },
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    this.listQuery.rangeDate = utils.returnToMonth()
    this.getList();
  },

  methods: {
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
    },
    // 获取数据
    getList() {
      this.count++;
      this.listLoading = true;
      this.listQuery.startDate = utils.isNull(this.listQuery.rangeDate)
        ? ""
        : this.listQuery.rangeDate[0];
      this.listQuery.endDate = utils.isNull(this.listQuery.rangeDate)
        ? ""
        : this.listQuery.rangeDate[1];
      this.listQuery.projectId = this.userInfo.projectId;
      outPunchPage(this.listQuery).then((res) => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data));
          this.total = res.data.page.total;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 显示人员dlg
    showUserDlg () {
      let userId = this.listQuery.userId
      let userName = this.listQuery.userName
    //   let userInfo = this.dlgData.userInfo
      this.$store.commit('communityMan/userDlg/SET_USERID', userId)
      this.$store.commit('communityMan/userDlg/SET_USERNAME', userName)
    //   this.$store.commit('communityMan/userDlg/SET_USERINFO', userInfo)
      this.$store.commit('communityMan/userDlg/SET_DLGSHOW', true)
    },
    showAddFunc(row){
      console.log(row,"row");
      this.dkState=true
      this.dkData=row
      setTimeout(() => {
          // let punchLongitude = "126.64259223090278,45.76110921223958"
          let punchLongitude = row.punchLongitude
          // 设置地图
          var map = new AMap.Map('wckqmap1', {
            resizeEnable: true,
            zoom: 14,
            center: punchLongitude.split(','),
          })
          // 创建两个点标记
          var m1 = new AMap.Marker({
            position: punchLongitude.split(','),
          })
          map.add(m1)
      }, 200)
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-image {
  width: 150px;
  height: 150px;
  margin-right: 10px;
  margin-bottom: 10px;
}
.wckqd-img {
  cursor: pointer;
  display: block;
  width: 200px;
  height: 220px;
  margin: 0 auto;
}
#wckqmap1 {
  width: 100%;
  height: 220px;
}
</style>


