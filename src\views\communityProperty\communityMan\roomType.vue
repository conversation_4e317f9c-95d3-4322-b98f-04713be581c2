<template>
  <!-- 房产类型 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区" prop="communityId">
          <el-select
            style="width: 260px"
            v-model="listQuery.communityId"
            filterable
            clearable
            placeholder="请选择小区"
            @change="communityChange"
          >
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native="getList" placeholder="请输入房产类型" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="addItem">新增房产类型</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="房产类型">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="小区名称" width="250">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="商户号">
          <template slot-scope="scope">
            <span>{{ scope.row.mid }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否提供发票" width="110" :formatter="formatState"> </el-table-column>
        <!-- <el-table-column label="税号">
          <template slot-scope="scope">
            <span>{{ scope.row.dutyParagraph }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="企业名称">
          <template slot-scope="scope">
            <span>{{ scope.row.enterpriseName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal="false" :title="dlgTitle" :visible.sync="dlgShow" width="600px" append-to-body>
      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="房产类型" prop="name">
          <el-input v-model="dlgData.name" placeholder="请输入房产类型" />
        </el-form-item>
        <el-form-item label="所在小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商户号" prop="mid">
          <el-input v-model="dlgData.mid" placeholder="请输入商户号" />
        </el-form-item>
        <el-form-item label="" prop="invoiceCheck">
          <el-checkbox v-model="invoiceCheck" @change="isInvoice">是否提供发票</el-checkbox>
        </el-form-item>
        <el-form-item label="" prop="vatCheck" v-if="invoiceCheck">
          <el-checkbox v-model="vatCheck">是否提供增值税发票</el-checkbox>
        </el-form-item>
        <el-form-item
          :rules="
            vatCheck == true || invoiceCheck == true
              ? [{ required: true, message: '请输入企业名称', trigger: 'blur' }]
              : [{ required: false }]
          "
          label="企业名称"
          prop="enterpriseName"
          v-if="invoiceCheck"
        >
          <el-input v-model="dlgData.enterpriseName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item
          :rules="
            vatCheck == true || invoiceCheck == true
              ? [{ required: true, message: '请输入终端号', trigger: 'blur' }]
              : [{ required: false }]
          "
          label="终端号"
          prop="terminalNo"
          v-if="invoiceCheck"
        >
          <el-input v-model="dlgData.terminalNo" placeholder="请输入终端号" />
        </el-form-item>
        <p v-if="invoiceCheck" style="margin-left: 30px">默认商品配置</p>
        <el-form-item
          :rules="
            vatCheck == true || invoiceCheck == true
              ? [{ required: true, message: '请输入商品名称', trigger: 'blur' }]
              : [{ required: false }]
          "
          label="商品名称"
          prop="productName"
          v-if="invoiceCheck"
        >
          <el-input v-model="dlgData.productName" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item
          :rules="
            vatCheck == true || invoiceCheck == true
              ? [{ required: true, message: '请输入税务编号', trigger: 'blur' }]
              : [{ required: false }]
          "
          label="税务编号"
          prop="taxNo"
          v-if="invoiceCheck"
        >
          <el-input v-model="dlgData.taxNo" placeholder="请输入税务编号" />
        </el-form-item>
        <el-form-item
          :rules="
            vatCheck == true || invoiceCheck == true
              ? [{ required: true, message: '请输入商品单价', type: number, trigger: 'blur' }]
              : [{ required: false }]
          "
          label="商品单价"
          prop="productPrice"
          v-if="invoiceCheck"
        >
          <el-input-number
            :precision="2"
            :step="1"
            :min="0.01"
            :controls="false"
            placeholder="请输入商品单价"
            v-model="dlgData.productPrice"
          ></el-input-number>
          元
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-close">取消</el-button>
        <el-button type="success" :loading="dlgLoading" @click="subAjax" icon="el-icon-check">提交 </el-button>
      </div>
    </el-dialog>
    <memberDlg />
  </div>
</template>

<script>
import { saveOrU, roomTypePage, communityPage, roomTypeDel, importAll } from '@/api/communityMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import memberDlg from '@/components/Dialog/communityMan/memberDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  projectId: '',
  id: '',
  communityName: '',
  mid: '',
  //   remark: "",
  name: '',
  communityId: '',
  isProvideInvoice: '', //发票
  isVatInvoice: '', //增值税发票
  enterpriseName: '', //企业名称
  terminalNo: '', //终端号
  productName: '', //商品名称
  productPrice: undefined, //商品单价
  taxNo: '', //税务编号
}

export default {
  name: 'roomInfo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    memberDlg,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题
      invoiceCheck: false, //发票
      vatCheck: false, //增值税发票

      rules: {
        name: [{ required: true, message: '请输入房产类型', trigger: 'change' }],
        communityId: [{ required: true, message: '请选择小区', trigger: 'change' }],
        mid: [{ required: true, message: '请输入商户号', trigger: 'change' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgDataOld: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
      },
      communityList: [],
      userInfo: {},
      selectRow: {},
    }
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.dlgData.projectId = this.userInfo.projectId
    if (this.$route.query.communityId) {
      this.listQuery.communityId = parseInt(this.$route.query.communityId)
      this.addItem()
    }
    this.getList()
    this.getCommunityList()
  },

  methods: {
    formatState(row, column) {
      switch (row.isProvideInvoice) {
        case '0':
          return '是'
        default:
          return '否'
      }
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },

    communityChange() {
      let communityId
      if (this.dlgShow) {
        communityId = this.dlgData.communityId
        this.dlgData.floorId = ''
        this.dlgData.unitId = ''
      } else {
        communityId = this.listQuery.communityId
        this.listQuery.floorId = ''
        this.listQuery.unitId = ''
      }
      this.getList(communityId)
    },

    // 获取小区列表
    getCommunityList() {
      console.log('getCommunityList')
      let postParam = {
        page: 1,
        limit: 200,
      }
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取数据
    getList() {
      if (utils.isNull(this.listQuery.communityId)) {
        this.$message.warning('请选择所在小区')
        return
      }
      this.list = []
      this.listOld = []
      roomTypePage(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.data
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    isInvoice() {
      if (this.invoiceCheck == false) {
        this.dlgData.enterpriseName = ''
        this.dlgData.terminalNo = '' //终端号
        this.dlgData.productName = '' //商品名称
        this.dlgData.productPrice = '' //商品单价
        this.dlgData.taxNo = '' //税务编号
        this.vatCheck = false
      }
    },
    // 新增
    addItem() {
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.invoiceCheck = false
      this.vatCheck = false

      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
      this.dlgTitle = '新增'
    },

    // 提交接口
    subAjax() {
      if (this.invoiceCheck == false) {
        this.dlgData.isProvideInvoice = 1
        this.dlgData.isVatInvoice = 1
        // this.dlgData.dutyParagraph = "";
        this.dlgData.enterpriseName = ''
        this.dlgData.terminalNo = '' //终端号
        this.dlgData.productName = '' //商品名称
        this.dlgData.productPrice = '' //商品单价
        this.dlgData.taxNo = '' //税务编号
      } else {
        this.dlgData.isProvideInvoice = 0
      }
      if (this.vatCheck == false) {
        this.dlgData.isVatInvoice = 1
      } else {
        this.dlgData.isVatInvoice = 0
      }
      console.log(this.dlgData, 'this.dlgData')
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          postParam.projectId = this.userInfo.projectId
          saveOrU(postParam).then((res) => {
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$nextTick(() => {
                this.$refs['dlgForm'].clearValidate()
              })
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem(data) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgDataOld = JSON.parse(JSON.stringify(data))
      this.dlgType = 'EDIT'
      this.dlgShow = true
      if (data.isVatInvoice == 0) {
        this.vatCheck = true
      } else {
        this.vatCheck = false
      }
      if (data.isProvideInvoice == 0) {
        this.invoiceCheck = true
      } else {
        this.invoiceCheck = false
        // this.dlgData.dutyParagraph = "";
        this.dlgData.enterpriseName = ''
        this.dlgData.terminalNo = '' //终端号
        this.dlgData.productName = '' //商品名称
        this.dlgData.productPrice = '' //商品单价
        this.dlgData.taxNo = '' //税务编号
      }
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
      this.dlgTitle = '编辑'
    },

    // 删除
    delItem(row) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        roomTypeDel(row.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>


