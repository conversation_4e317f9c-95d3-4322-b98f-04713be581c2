<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="'选择员工'"
    :visible.sync="dlgShow"
    append-to-body
  >
    <div class="filter-container">
      <el-form inline>
        <el-form-item label="关键字">
          <el-input v-model="listQuery.label" placeholder="请输入员工姓名">
            <i
              slot="suffix"
              @click="resetSearchItem(['label'])"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-form-item label="职务">
          <el-select
            v-model="listQuery.postJob"
            filterable
            clearable
            placeholder="请选择职务"
          >
            <el-option
              v-for="item in postJobList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="getList"
        >
          搜索
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <!-- 多选 -->
      <el-table
        @select="tableSelectChange"
        @select-all="tableSelectAll"
        ref="multipleTable"
        :key="diaKey"
        v-if="diaMulState"
        class="m-small-table"
        :data="list"
        max-height="347px"
        border
        fit
        highlight-current-row
      >
        <el-table-column align="center" type="selection" width="55">
        </el-table-column>
        <!-- <el-table-column label="#" width="60">
          <template slot-scope="scope">
            <el-radio v-model="selectUserId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column> -->
        <el-table-column label="员工姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属部门">
          <template slot-scope="scope">
            <span>{{ scope.row.departmentName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="职务">
          <template slot-scope="scope">
            <span>{{ scope.row.userJob }}</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号">
          <template slot-scope="scope">
            <span>{{ scope.row.account }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 单选 -->
      <el-table
        v-if="!diaMulState"
        class="m-small-table"
        :data="list"
        @row-click="rowClick"
        border
        fit
        highlight-current-row
        max-height="347px"
      >
        <el-table-column label="#" width="60">
          <template slot-scope="scope">
            <el-radio v-model="selectUserId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="员工姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属部门">
          <template slot-scope="scope">
            <span>{{ scope.row.departmentName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="职务">
          <template slot-scope="scope">
            <span>{{ scope.row.userJob }}</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号">
          <template slot-scope="scope">
            <span>{{ scope.row.account }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.size"
        @pagination="getList"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back"> 取 消 </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import Cookie from "js-cookie";

import Pagination from "@/components/Pagination";
import * as utils from "@/utils";
import { findUserByLabelAndNumByGroup } from "@/api/staffMan";

export default {
  components: {
    Pagination,
  },
  props: {
    diaPostData: {
      type: Array,
      default: ()=>[],
    },
  },
  data() {
    return {
      list: [],

      listQuery: {
        size: 10,
        label: "",
        page: 1,
        postJob: "",
      },

      total: 0,

      selectUserId: "",

      selectUserName: "",

      selectUserInfo: {},
      postJobList: [],
      diaKey: 0,
      diaPostList: [],
    };
  },

  computed: {
    dlgShow: {
      get: function () {
        let state = this.$store.state.communityMan.userDlg.dlgShow;
        if (state === true) {
          setTimeout(() => {
          // 选中数据
          console.log(this.diaPostData,"this.diaPostData");
          if (this.diaPostData) {
            this.diaPostList = this.diaPostData;
          }else{
            this.diaPostList=[]
          }
          this.diaKey++
          }, 50)
        }
        return this.$store.state.communityMan.userDlg.dlgShow;
      },
      set: function (val) {
        this.$store.commit("communityMan/userDlg/SET_DLGSHOW", val);
      },
    },
    diaMulState: {
      get: function () {
        return this.$store.state.communityMan.userDlg.diaMulState;
      },
      set: function (val) {
        this.$store.commit("communityMan/userDlg/SET_DIAPOST_MUL", val);
      },
    },

    userId: {
      get: function () {
        return this.$store.state.communityMan.userDlg.userId;
      },
      set: function (val) {
        this.$store.commit("communityMan/userDlg/SET_USERID", val);
      },
    },

    userName: {
      get: function () {
        return this.$store.state.communityMan.userDlg.userName;
      },
      set: function (val) {
        this.$store.commit("communityMan/userDlg/SET_USERNAME", val);
      },
    },

    userInfo: {
      get: function () {
        return this.$store.state.communityMan.userDlg.userInfo;
      },
      set: function (val) {
        this.$store.commit("communityMan/userDlg/SET_USERINFO", val);
      },
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.userId)) {
          this.selectUserId = "";
          this.selectUserName = "";
          this.selectUserInfo = {};
        }
        this.getList();
      }
    },

    userId(val) {
      this.selectUserId = val;
    },
    diaMulState(val) {
      this.diaMulState = val;
    },

    userName(val) {
      this.selectUserName = val;
    },

    userInfo(val) {
      this.selectUserInfo = val;
    },
  },

  created() {
    utils.getDataDict(this, "postJob", "postJobList");
  },

  methods: {
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
    },

    rowClick(row, column, event) {
      this.selectUserId = row["id"];
      this.selectUserName = row["label"];
      this.selectUserInfo = JSON.parse(JSON.stringify(row));
      // let diaPostList = [row];
      // this.selectId = row.id;
      // this.diaPostList = JSON.parse(JSON.stringify(diaPostList));
    },
    // << 表格 复选事件
    tableSelectChange(selection, row) {
      // 判断是否存在，存在则删除，不存在则增加
      let diaPostList = JSON.parse(JSON.stringify(this.diaPostList));
      let isHas = false;
      let index = "";
      for (let i = 0; i < this.diaPostList.length; i++) {
        if (row.id == this.diaPostList[i].id) {
          index = i;
          isHas = true;
          break;
        }
      }

      if (isHas) {
        diaPostList.splice(index, 1);
      } else {
        diaPostList.push(row);
      }
      console.log("====diaPostList", diaPostList);
      this.diaPostList = JSON.parse(JSON.stringify(diaPostList));
    },

    getList() {
      this.list = [];
      findUserByLabelAndNumByGroup(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data ? res.data.data : [];
          this.total = res.data.page ? res.data.page.total : 0;
          if (this.diaMulState) {
            setTimeout(() => {
              for (let item of this.list) {
                let isHas = this.diaPostList.some((item2) => {
                  return item.id == item2.id;
                });
                console.log("isHas", isHas);
                if (isHas) {
                  this.$refs.multipleTable.toggleRowSelection(item, true);
                }
              }
            }, 50);
          }
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    subDlg() {
      if (!this.diaMulState) {
        this.userId = this.selectUserId;
        this.userName = this.selectUserName;
        this.userInfo = this.selectUserInfo;
        this.$store.commit("communityMan/userDlg/SET_USERID", this.userId);
        this.$store.commit("communityMan/userDlg/SET_USERNAME", this.userName);
        this.$store.commit("communityMan/userDlg/SET_USERINFO", this.userInfo);
        // this.closeDlg();
      } else {
        this.$emit("mulData", JSON.parse(JSON.stringify(this.diaPostList)));
        // this.$store.commit('communityMan/userDlg/SET_DIAPOST_GET', JSON.stringify(this.diaPostList))
      }
      // return
      this.closeDlg();
    },

    closeDlg() {
      this.$store.commit("communityMan/userDlg/SET_DIAPOST_MUL", false);
      this.$store.commit("communityMan/userDlg/SET_DLGSHOW", false);
    },

    // 全选
    tableSelectAll(selection) {
      // 全选-selection.length!=0
      let diaPostList = JSON.parse(JSON.stringify(this.diaPostList));
      if (selection.length != 0) {
        let selectIndexArr = [];
        // 全选
        for (let i = 0; i < selection.length; i++) {
          let isHas = diaPostList.some((item) => {
            return selection[i].id == item.id;
          });
          !isHas && selectIndexArr.push(i);
        }
        for (let index of selectIndexArr) {
          diaPostList.push(selection[index]);
        }
        this.diaPostList = JSON.parse(JSON.stringify(diaPostList));
      } else {
        // 取消全选
        let removeIndexArr = [];

        for (let i = 0; i < diaPostList.length; i++) {
          let isHas = this.list.some((item) => {
            return diaPostList[i].id == item.id;
          });
          isHas && removeIndexArr.push(i);
        }
        removeIndexArr.sort(function (a, b) {
          return a - b;
        });
        removeIndexArr = removeIndexArr.reverse();
        for (let index of removeIndexArr) {
          diaPostList.splice(index, 1);
        }
        this.diaPostList = JSON.parse(JSON.stringify(diaPostList));
      }
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>