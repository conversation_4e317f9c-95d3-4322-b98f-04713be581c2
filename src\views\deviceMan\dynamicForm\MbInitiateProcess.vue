<template>
  <div v-loading="loading">
    <div>
      <div style="padding-bottom: 5rem">
      <!-- <div @click="test2" style="line-height: 50px;border: 2px solid red">关闭</div> -->
        <Field
          required
          readonly
          clickable
          label="选择项目"
          :value="routerQuery.projectName"
          placeholder="选择项目"
          @click="projectClick"
          :disabled="disabledForm || hasProjectName"
        />
        <Field
          required
          readonly
          clickable
          label="选择模板"
          :value="routerQuery.deviceName"
          placeholder="选择模板"
          @click="deviceNameClick"
          :disabled="disabledForm || hasProjectName"
        />
        <div class="mt10"></div>
        <form-render
          v-if="!loading"
          v-show="routerQuery.deviceId"
          mode="MOBILE"
          class="process-form"
          ref="form"
          :forms="forms"
          v-model="formData"
          :disabledForm="disabledForm"
        />
      </div>
      <div style="height: 60px;"></div>
      <v-button
        v-if="!disabledForm"
        class="submit-btn"
        round
        type="info"
        @click="submit"
        :disabled="submitDisabled"
        >确定</v-button
      >
    </div>

    <!-- <Popup v-model="projectPickerState" position="bottom">
      <Picker
        show-toolbar
        title="选择项目"
        value-key="name"
        :columns="projectList"
        @cancel="projectPickerState = false"
        @confirm="projectConfirm"
      />
    </Popup> -->
    <Popup v-model="modelPickerState" position="bottom">
      <Picker
        show-toolbar
        title="选择模板"
        value-key="name"
        :columns="modelList"
        @cancel="modelPickerState = false"
        @confirm="modelConfirm"
      />
    </Popup>
  </div>
</template>

<script>
import { getAction, postAction, putAction } from "@/api";
import {
  Radio,
  RadioGroup,
  NavBar,
  Tabs,
  Tab,
  Button,
  Toast,
  //====
  Picker, //
  Popup,
  Field
} from "vant";
import * as utils from "@/utils";

import FormRender from "@/views/administrationOA/common/form/FormRender";

const VButton = Button;
export default {
  // props: {
  //   disabledForm: {
  //     type: Boolean,
  //     default: false // add info
  //   }
  // },
  components: {
    // FormDesignRender,
    FormRender,
    VButton,

    Radio,
    RadioGroup,
    NavBar,
    Tabs,
    Tab,
    // ===

    Picker,
    Popup,
    Field
  },
  data() {
    return {
      submitDisabled: false,
      active: 0,
      loading: false,
      formData: {},
      userDepts: [],
      userDeptId: "",
      processUsers: {},
      form: {
        formId: "",
        formName: "",
        logo: {},
        formItems: [],
        process: {},
        frequency: "",
        remark: ""
      },
      userInfo: "",

      disabledForm: false,
      instanceId: "",

      selfSelectMap: {},

      ///////////////

      // projectList: [],
      modelList: [],
      routerQuery: {},
      // projectId: "",
      // tempId: '',
      // deviceid: "",
      // rowId: "",
      // formType: "",

      formData0: [],


      hasProjectName: false,
      // 弹窗
      projectPickerState: false, // 弹窗
      modelPickerState: false // 弹窗
    };
  },
  mounted() {
    document.title="模板运行记录"
    // projectId
    // deviceid
    // rowId
    // formType   add edit info
    //////////
    let routerQuery = (this.routerQuery = JSON.parse(
      JSON.stringify(this.$route.query)
    ));
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.hasProjectName = true
    routerQuery.projectId = this.userInfo.projectId
    routerQuery.projectName = this.userInfo.projectName

    console.log('8888888888')
    console.log('==this.userInfo', this.userInfo)
    // if (routerQuery.projectId) {
    //   this.hasProjectName = true
    // } else {
    //   this.hasProjectName = false
    // }

    // this.userInfo = JSON.parse(window.localStorage.ERPUserInfo);
    // this.projectList = this.userInfo.projects;

    // this.routerQuery.projectName = utils.arrId2Name(
    //   this.projectList,
    //   this.routerQuery.projectId
    // );
    this.getModelSelect();

    this.disabledForm = routerQuery.formType == "info" ? true : false;
  },
  computed: {
    forms() {
      return this.$store.state.design.formItems;
    }
    // process() {
    //   return this.$store.state.design.process;
    // }
  },
  methods: {
    test2(){
      if (window.localStorage.h5Env == "xcx") {
        wx.miniProgram.navigateBack();
      } else {
        this.$router.go(-1);
      }
    },

    projectClick() {
      if (this.disabledForm || this.hasProjectName) return false;
      this.projectPickerState = true;
    },
    deviceNameClick() {
      if (this.disabledForm || this.hasProjectName) return false;
      this.modelPickerState = true;
    },
    projectConfirm(value) {
      // this.value = value;
      this.routerQuery.projectId = value.id;
      this.routerQuery.projectName = value.name;
      this.projectPickerState = false;
      this.getModelSelect();
    },
    async getModelSelect() {
      let sendObj = {
        projectId: this.routerQuery.projectId,
        pageNo: 1,
        pageSize: 1000,
      };
      try {
        let res0 = await getAction(`/sa/green/equ/models/page`, sendObj);
        let res = res0.data;

        console.log("+++this.routerQuery.deviceId", this.routerQuery.deviceId);
        if (res && res.code == 200) {
          console.log("===res.data.list", res.data.list);
          this.modelList = res.data.list;
          let someItem = res.data.list.filter(item => {
            return item.id + "" === this.routerQuery.deviceId + "";
          });
          console.log("===someItem", someItem);
          if (someItem.length) {
            someItem = someItem[0];
            this.routerQuery.deviceName = someItem.name;
            if (this.routerQuery.rowId) {
              this.loadFormInfo("info");
            } else {
              this.loadFormInfo("create");
            }
          } else {
            this.routerQuery.deviceId = "";
            this.routerQuery.deviceName = "";
          }
        } else {
          this.$message.warning(res.msg);
        }
      } catch (err) {
        console.log("====err", err);
        this.$message.error(err.msg);
      }
    },
    modelConfirm(value) {
      // this.value = value;
      // deviceid deviceName equModel
      this.routerQuery.deviceId = value.id;
      this.routerQuery.deviceName = value.name;

      this.modelPickerState = false;
      this.loadFormInfo("create");
    },

    loadFormInfo(type) {
      this.loading = true;
      getAction("/sa/green/equ/models/get?id=" + this.routerQuery.deviceId)
        .then(rsp0 => {
          let rsp = rsp0.data;
          this.loading = false;
          if (utils.isNull(rsp.data)) {
            Toast.fail("未绑定模版");
            this.form = { formItems: [] };
            //构建表单及校验规则
            this.$store.state.design = this.form;
            return false;
          }
          console.log("7777", rsp.data);
          let form = rsp.data;
          this.formData0 = JSON.parse(form.formItems);

          form.formItems = JSON.parse(form.formItems);
          this.form = form;
          //构建表单及校验规则
          this.$store.state.design = form;

          console.log("===设置头部", "发起-" + form.name);
          // "发起-"
          document.title = form.name;

          if (type == "info" && this.routerQuery.rowId) {
            this.loadFormData();
          } else {
            this.formData = {};
          }
        })
        .catch(err => {
          console.log("error", err);
          this.loading = false;
          Toast.fail("获取流程模型失败");
        });
    },
    loadFormData() {
      getAction("/sa/green/equ/operation-record/get?id=" + this.routerQuery.rowId)
        .then(rsp0 => {
          let rsp = rsp0.data;
          let formData0 = JSON.parse(rsp.data.dataInfoJson);
          console.log("++++表单接口返回数据", formData0);
          let formData = {};
          for (let item of formData0) {
            formData[item.id] = item.value;
          }
          this.formData = formData;
          (this.formData0 = formData0),
            console.log("++++构造后的表单数据", this.formData);
        })
        .catch(e => {
          Toast.fail("回显数据失败");
        });
    },
    submit() {
      // if (!this.$isNotEmpty(this.form.processDefId)) {
      //   Toast.fail("流程未发布");
      //   return;
      // }
      // let errMsg = this.$refs.rightRef.validateFunc();
      // console.log("===errMsg", errMsg);
      // if (errMsg) {
      //   Toast.fail(errMsg);
      //   return false;
      // }
      // if (!this.routerQuery.projectId) {
      //   Toast.fail("请选择项目");
      //   return;
      // }
      if (!this.routerQuery.deviceId) {
        Toast.fail("请选择模板");
        return;
      }
      this.submitDisabled = true
      this.validate(valid => {
        if (valid) {
          // //Toast.success('校验成功')
          // let startParams = {
          //   deptId: this.userDeptId,
          //   formData: this.formData,
          //   processUsers: this.processUsers
          // };
          console.log("====formData", this.formData);
          console.log("====formData0", this.formData0);
          this.formData0.map(item => {
            item.value = this.formData[item.id];
          });

          // 修改
          // put  /green/equ/operation-record/update
          let sendObj = {
            // id: this.routerQuery.rowId,
            projectId: this.routerQuery.projectId,
            projectName: this.routerQuery.projectName,
            modelId: this.routerQuery.deviceId,
            modelName: this.routerQuery.deviceName,
            dataInfoJson: JSON.stringify(this.formData0)
          };
          let sendUrl = "";
          let sendFunc = "";

          console.log(
            "=====this.routerQuery.formType",
            this.routerQuery.formType
          );
          if (this.routerQuery.formType == "add") {
            sendUrl = "/sa/green/equ/operation-record/create";
            sendFunc = postAction;
          } else {
            sendObj.id = this.routerQuery.rowId;
            sendUrl = "/sa/green/equ/operation-record/update";
            sendFunc = putAction;
          }
          console.log("+++发送数据", sendObj);
          // return false;
          sendFunc(sendUrl, sendObj)
            .then(rsp => {
              Toast.success("提交成功");
              setTimeout(() => {
                if (window.localStorage.h5Env == "xcx") {
                  wx.miniProgram.navigateBack();
                } else {
                  this.$router.go(-1);
                }
              }, 1000);
              // this.$router.push("/administrationOA/mySubmit?type=app");
            })
            .catch(err => {
              Toast.fail("提交失败");
            });
        } else {
          Toast.fail("请完善表单");
          this.submitDisabled = false

        }
      });
    },
    back() {
      this.$router.push("/workspace/forms");
    },
    validate(call) {
      this.$refs.form.validate(call);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/workFlow/theme.scss";

body {
  font-size: 12px;
}

.process-form {
  /deep/ .el-form-item__label {
    padding: 0 0;
  }
}

/deep/ .van-tabs {
  .van-tabs__wrap {
    border-bottom: 1px solid $theme-aside-bgc;
  }
  .van-tabs__line {
    background-color: $theme-primary;
  }
  .van-tab--active .van-tab__text {
    color: $theme-primary;
  }
}

.submit-btn {
  position: fixed;
  bottom: 2rem;
  width: 95%;
  left: calc((100% - 95%) / 2);
}

/*/deep/ .van-nav-bar--fixed{
  position: fixed !important;
}

/deep/ .van-nav-bar__content{
  background: $theme-primary;
  i, .van-nav-bar__text, .van-nav-bar__title{
    color: white;
  }
}*/

.m-form-item {
  position: relative;
  padding: 10px 10px 20px 10px;
  background: white;
  //margin-top: 5px;
  margin-bottom: 10px;
  & > div:first-child {
    margin-bottom: 8px;
    font-size: 1.2rem;
    color: #545456;
  }
  & > div:last-child {
    //padding: 0 5px;
  }
  .title-required {
    color: $theme-danger;
  }
  .valid-error {
    color: $theme-danger;
    position: absolute;
    font-size: 0.85rem;
  }
}
</style>
