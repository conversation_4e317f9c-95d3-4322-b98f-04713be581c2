
const usersDialog = {
  state: {
    usersOrBranState: false,
    usersOrBranArr: [], // 组件返回页面值
    usersOrBranArr2: [],  // 给组件初始值

    usersOrBranType: '1',  // 单选框选中类型 0 岗位, 1 员工

    usersOrBranTypeDis: false,  // 是否可改变单选框状态
    usersOrBrancanSelectBm: true,  // 是否允许部门单机时间

    usersOrBranRoleState: false,  // 是否根据权限，读取登录人的下属员工或岗位

    // usersOrBranSync: {},  // 动态查岗位/员工条件
    usersOrBranSqType: '',  // findOrgBranchPostByCondition 空岗。。状态

    // 岗位条件
    // let sendObj = {
      // page: 1,
      // size: 500,
      // label: '',
      // postJob: '',  // 岗位职务值
      // branchId: '',
      // ptype: '',  // 岗位状态：0-启用，1-停用
      // postStatus: '',  // 	岗位状态 0空岗/1兼岗/2本岗
      // postIds: ''
    // }


    // this.$store.commit('SET_USERSORBRANTYPE', '1')  // 0 岗位，1 员工
    // this.$store.commit('SET_USERSORBRANTYPEDIS', false)  // 是否可改变单选框状态
    // this.$store.commit('SET_USERSORBRANCANSELECTBM', true)  // 是否允许部门 双击事件
    // this.$store.commit('SET_USERSORBRANROLESTATE', false)  // 是否根据权限

    // this.$store.commit('SET_USERSORBRANSTATE', true)
  },

  mutations: {
    SET_USERSORBRANSTATE: (state, val) => {
      state.usersOrBranState = val
    },
    SET_USERSORBRANARR: (state, val) => {
      state.usersOrBranArr = JSON.parse(JSON.stringify(val))
    },
    SET_USERSORBRANARR2: (state, val) => {
      state.usersOrBranArr2 = JSON.parse(JSON.stringify(val))
    },
    SET_USERSORBRANTYPE: (state, val) => {
      state.usersOrBranType = val
    },

    SET_USERSORBRANTYPEDIS: (state, val) => {
      state.usersOrBranTypeDis = val
    },
    SET_USERSORBRANCANSELECTBM: (state, val) => {
      state.usersOrBrancanSelectBm = val
    },
    SET_USERSORBRANROLESTATE: (state, val) => {
      state.usersOrBranRoleState = val
    },
    SET_USERSORBRANSQTYPE: (state, val) => {
      state.usersOrBranSqType = val
    },
    // SET_USERSORBRANSYNC: (state, val) => {
    //   state.usersOrBranSync = val
    // }

  },

  actions: {
    
  }
}

export default usersDialog
