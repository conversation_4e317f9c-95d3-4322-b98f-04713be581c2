<template>
  <!-- 优惠券设置 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区:">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在楼栋:">
          <el-select v-model="listQuery.floorId" filterable clearable placeholder="请选择楼栋" @change="floorChange" style="width: 140px">
            <el-option v-for="item in buildingList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在单元:">
          <el-select v-model="listQuery.unitId" filterable clearable placeholder="请选择单元" style="width: 140px">
            <el-option v-for="item in unitList" :key="item.id" :label="item.unitName" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="关键字:">
          <el-input @keyup.enter.native="getList" placeholder="房屋名称" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>

        <el-button icon="el-icon-search" type="success" size="mini" @click="searchFunc" class="mb10">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="showDlg('add')" class="mb10">添加</el-button>
        <el-button @click="downLoadFunc" icon="el-icon-download" size="mini" type="primary" plain class="mb10">导出</el-button>
      </el-form>
    </div>
    <el-table class="m-small-table" height="calc(100vh - 328px)" v-loading="listLoading" :data="list" border fit highlight-current-row>
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (listQuery.page - 1) * listQuery.limit + scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column label="小区名称" min-width="160" prop="communityName"></el-table-column>
      <el-table-column label="房屋" prop="roomNum"></el-table-column>
      <el-table-column label="楼层" prop="layer" width="80" align="center"></el-table-column>
      <el-table-column label="承租人姓名" prop="userName" width="120" align="center"></el-table-column>
      <el-table-column label="电话" prop="phone" width="110"></el-table-column>
      <el-table-column label="编号" prop="contractNo" width="180"></el-table-column>
      <el-table-column label="签章日期" prop="signDate" width="110" align="center"></el-table-column>
      <el-table-column label="合同日期" width="170px" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.startDate }} - {{ scope.row.endDate }}</div>
        </template>
      </el-table-column>

      <el-table-column label="租金单价" prop="price" width="100" align="center"></el-table-column>
      <el-table-column label="租金面积" prop="insideArea" width="100" align="center"></el-table-column>
      <el-table-column label="应收租金" prop="yszjMoney" width="100" align="center"></el-table-column>

      <el-table-column label="操作" width="180" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="showDlg('edit', scope.row)">编辑</el-button>

          <el-popover placement="bottom" width="200" trigger="hover">
            <div v-for="(item, index) of printList" :key="item.id" class="table-dialog-p" @click="printTrClick(scope.row, item.id)">
              {{ item.name }}
            </div>
            <el-button slot="reference" type="primary" size="mini" icon="el-icon-printer" plain>打印</el-button>
          </el-popover>

          <!-- <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <!-- 弹窗 新增/编辑 -->
    <addDlg
      :dlgState0="dlgState"
      :dlgData0="dlgData"
      :dlgType="dlgType"
      :dlgQuery="dlgQuery"
      @closeDlg="closeDlg"
      @getList="getList"
      :communityList="communityList"
    />
    <!-- <infoDlg
      :dlgState0="dlgInfoState"
      :dlgData0="dlgInfoData"
      :dlgType="dlgInfoType"
      :dlgQuery="dlgInfoQuery"
      @closeDlg="closeInfoDlg"
      @getList="getList"
    /> -->
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'

import * as utils from '@/utils'
import { postAction, getAction } from '@/api'

import { communityPage, cofloorCommunity, buildingunitFloor } from '@/api/communityMan'

import { uploadImg } from '@/utils/uploadImg'
import Pagination from '@/components/Pagination'
import addDlg from './addDlg'

// import infoDlg from './infoDlg'

let listQueryEmpty = {
  label: '', //	模糊查询	body	false	string
  page: 1,
  limit: 20,

  communityId: '',
  floorId: '',
  unitId: '',
}

export default {
  components: {
    Pagination,
    addDlg,
  },
  data() {
    return {
      // 弹窗数据
      dlgQuery: {},
      dlgState: false,
      dlgType: '', // 弹框状态add, edit
      dlgData: {},
      // 详情弹窗
      dlgInfoQuery: {},
      dlgInfoState: false,
      dlgInfoType: '', // 弹框状态add, edit
      dlgInfoData: {},

      /////

      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        label: '',
        page: 1,
        limit: 20,

        loginType: '', // 查询类型  1 web查询 2公众号用户查询
        type: '', //  请选择就诊类型：1门诊就诊 2疫苗接种 3健康证
        state: '', //  0有效  1 已打印  2 超时  全部''
      },

      // 下拉框
      communityList: [],
      buildingList: [],
      unitList: [],

      printList: [
        { id: '0', name: '首页（编号）' },
        { id: '1', name: '第1页（出租人，承租人）' },
        { id: '4,5', name: '第4,5页（合同有效期，签章日期）' },
        { id: '6', name: '第6页（承租人信息，房屋信息）' },
        { id: '8,9', name: '第8,9页（租金单价，用途，面积，应收租金）' },
      ],
    }
  },

  created() {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.searchFunc()
  },

  methods: {
    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
      }
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },
    // 获取楼栋列表
    getBuildingList(id) {
      if (utils.isNull(id)) {
        return
      }
      cofloorCommunity(id).then((res) => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data
          // if (this.dlgShow) {
          //   this.buildingListDlg = res.data.data
          // } else {

          // }
        }
      })
    },
    // 获取单元列表
    getUnitList(id) {
      if (utils.isNull(id)) {
        return
      }
      buildingunitFloor(id).then((res) => {
        if (res.data.code == 200) {
          this.unitList = res.data.data

          // if (this.dlgShow) {
          //   this.unitListDlg = res.data.data
          // } else {
          //   this.unitList = res.data.data
          // }
        }
      })
    },

    communityChange() {
      let communityId = this.listQuery.communityId
      this.listQuery.floorId = ''
      this.listQuery.unitId = ''

      this.listQuery = JSON.parse(JSON.stringify(this.listQuery))
      this.getBuildingList(communityId)
      // this.getRoomType(communityId)
    },
    floorChange() {
      let floorId = this.listQuery.floorId
      this.listQuery.unitId = ''
      this.listQuery = JSON.parse(JSON.stringify(this.listQuery))
      this.getUnitList(floorId)
    },

    // 获取房屋类型列表
    // getRoomType(communityId) {
    //   if (communityId) {
    //     let postParam = {
    //       page: 1,
    //       limit: 200,
    //       communityId: communityId,
    //     }
    //     roomTypePage(postParam).then((res) => {
    //       if (res.data.code == 200) {
    //         if (this.dlgShow == true) {
    //           this.houseTypeList = res.data.data
    //         } else {
    //           this.houseTypeList = res.data.data
    //         }
    //       }
    //     })
    //   }
    // },
    // 导出
    downLoadFunc() {
      // ---- 调接口下载
      let sendObj = JSON.parse(JSON.stringify(this.listQuery))
      delete sendObj.page
      delete sendObj.limit
      postAction('/unity/roomContract/export', sendObj).then((res0) => {
        window.open(res0.data.msg)
      })
    },
    ///////////////

    // << --- 弹窗 ---
    // -- 表单弹窗
    showDlg(type, row) {
      if (type == 'add') {
        this.dlgQuery.communityId = this.listQuery.communityId
        this.dlgQuery = { id: 0, communityId: this.listQuery.communityId }
      } else {
        this.dlgQuery = row
      }

      this.dlgType = type
      this.dlgState = true
    },
    // 关闭弹窗
    closeDlg() {
      this.dlgState = false
    },

    // 详情弹窗
    printTrClick(row, itemId) {
      let tableRow = JSON.parse(JSON.stringify(row))
      console.log(tableRow,"tableRow");
      // 4,5
      tableRow.startDate2 = tableRow.startDate.replace(/-/g, '   ')
      tableRow.endDate2 = tableRow.endDate.replace(/-/g, '   ')
      tableRow.signDate2 = tableRow.signDate.replace(/-/g, '   ')

      window.sessionStorage.printData = JSON.stringify(tableRow)
      let newWin = this.$router.resolve({ path: '/tenancyContractPrint', query: { id: itemId } })
      window.open(newWin.href, '_blank')
    },
    // -- 表单弹窗
    showInfoDlg(row) {
      this.dlgInfoQuery = row
      this.dlgInfoType = 'info'
      this.dlgInfoState = true
    },
    // 关闭弹窗
    closeInfoDlg() {
      this.dlgInfoState = false
    },
    // >> --- 弹窗 ---
    //////
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // << 列表
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    // 获取数据
    getList() {
      // if (!this.listQuery.communityId) {
      //   this.$message.warning('请选择小区')
      //   return false
      // }
      // if (!this.listQuery.floorId) {
      //   this.$message.warning('请选择小区')
      //   return false
      // }

      let sendObj = JSON.parse(JSON.stringify(this.listQuery))
      sendObj.projectId = this.userInfo.projectId

      this.listLoading = true
      postAction('/unity/roomContract/page', sendObj).then((res0) => {
        this.listLoading = false
        let res = res0.data
        if (res.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data))

          for (let item of list) {
            item.yszjMoney = utils.num2Round(parseFloat(item.price) * parseFloat(item.insideArea), 2)
          }

          this.list = list
          this.total = res.page.total ? res.page.total : 0
          // this.formatList()
        } else {
          this.list = []
          this.total = 0
          this.$message.error(res.msg)
        }
      })
    },
    // >> 列表
    delItem(data, flag) {
      let title = '确认删除?'

      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        getAction(`/aaa/bbb/${data.id}`).then((res0) => {
          let res = res0.data
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}

// 打印
.table-dialog-p {
  line-height: 36px;
  text-align: center;
  height: 36px;
  background: #fff;
  color: #333;
  cursor: pointer;
  margin-left: -12px;
  margin-right: -12px;
  padding-left: 8px;
  font-size: 12px;
  // 可选的
  &.can:hover {
    background-color: #ecf5ff;
    color: #67c23a;
  }

  // 选中的
  &.selected {
    background-color: #ecf5ff;
    color: #67c23a;
  }
  // &.selected:hover {
  //   background-color: #67C23A;
  //   color: #fff
  // }

  // 不可选的
  &.disabled {
    cursor: default;
    color: #ccc;
  }
  // &.disabled:hover {
  //   cursor: default;
  //   color: #888;
  //   background-color: #fff;
  // }

  &.mt3 {
    margin-top: -9px;
  }
  &.mb3 {
    margin-bottom: -9px;
  }
}
</style>


