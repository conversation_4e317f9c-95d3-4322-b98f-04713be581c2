!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e["vue-orgchart"]={})}(this,function(e){"use strict";function t(e,t){return e===t||e!=e&&t!=t}function n(e,n){for(var r=e.length;r--;)if(t(e[r][0],n))return r;return-1}function r(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function i(e){return null==e?void 0===e?$:Y:W&&W in Object(e)?function(e){var t=U.call(e,J),n=e[J];try{e[J]=void 0;var r=!0}catch(e){}var i=V.call(e);return r&&(t?e[J]=n:delete e[J]),i}(e):function(e){return X.call(e)}(e)}function o(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function s(e){if(!o(e))return!1;var t=i(e);return t==Z||t==K||t==G||t==Q}function a(e){if(!o(e)||function(e){return!!te&&te in e}(e))return!1;return(s(e)?le:re).test(function(e){if(null!=e){try{return ne.call(e)}catch(e){}try{return e+""}catch(e){}}return""}(e))}function l(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return a(n)?n:void 0}function c(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function d(e,t){var n=e.__data__;return function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}(t)?n["string"==typeof t?"string":"hash"]:n.map}function u(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function h(e){var t=this.__data__=new r(e);this.size=t.size}function f(e,t,n){"__proto__"==t&&ge?ge(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function p(e,n,r){(void 0===r||t(e[n],r))&&(void 0!==r||n in e)||f(e,n,r)}function v(e,t){var n=t?function(e){var t=new e.constructor(e.byteLength);return new Se(t).set(new Se(e)),t}(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function g(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Ne)}function y(e){return null!=e&&"object"==typeof e}function m(e){return y(e)&&i(e)==we}function b(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Oe}function _(e){return null!=e&&b(e.length)&&!s(e)}function E(e,n,r){var i=e[n];Ze.call(e,n)&&t(i,r)&&(void 0!==r||n in e)||f(e,n,r)}function S(e,t){return!!(t=null==t?Ke:t)&&("number"==typeof e||Qe.test(e))&&e>-1&&e%1==0&&e<t}function A(e,t){var n=Te(e),r=!n&&ke(e),i=!n&&!r&&ze(e),o=!n&&!r&&!i&&Ge(e),s=n||r||i||o,a=s?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],l=a.length;for(var c in e)!t&&!et.call(e,c)||s&&("length"==c||i&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||S(c,l))||a.push(c);return a}function L(e){if(!o(e))return function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}(e);var t=g(e),n=[];for(var r in e)("constructor"!=r||!t&&tt.call(e,r))&&n.push(r);return n}function C(e){return _(e)?A(e,!0):L(e)}function N(e){return function(e,t,n,r){var i=!n;n||(n={});for(var o=-1,s=t.length;++o<s;){var a=t[o],l=r?r(n[a],e[a],a,n,e):void 0;void 0===l&&(l=e[a]),i?f(n,a,l):E(n,a,l)}return n}(e,C(e))}function w(e,t,n,r,a,l,c){var d=e[n],u=t[n],h=c.get(u);if(h)p(e,n,h);else{var f=l?l(d,u,n+"",e,t,c):void 0,m=void 0===f;if(m){var b=Te(u),E=!b&&ze(u),S=!b&&!E&&Ge(u);f=u,b||E||S?Te(d)?f=d:!function(e){return y(e)&&_(e)}(d)?E?(m=!1,f=function(e,t){if(t)return e.slice();var n=e.length,r=Ee?Ee(n):new e.constructor(n);return e.copy(r),r}(u,!0)):S?(m=!1,f=v(u,!0)):f=[]:f=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}(d):function(e){if(!y(e)||i(e)!=He)return!1;var t=Ce(e);if(null===t)return!0;var n=Fe.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Ie.call(n)==Ue}(u)||ke(u)?(f=d,ke(d)?f=N(d):(!o(d)||r&&s(d))&&(f=function(e){return"function"!=typeof e.constructor||g(e)?{}:Le(Ce(e))}(u))):m=!1}m&&(c.set(u,f),a(f,u,r,l,c),c.delete(u)),p(e,n,f)}}function x(e,t,n,r,i){e!==t&&ye(t,function(s,a){if(o(s))i||(i=new h),w(e,t,a,n,x,r,i);else{var l=r?r(e[a],s,a+"",e,t,i):void 0;void 0===l&&(l=s),p(e,a,l)}},C)}function j(e){return e}function q(e,t){return at(function(e,t,n){return t=nt(void 0===t?e.length-1:t,0),function(){for(var r=arguments,i=-1,o=nt(r.length-t,0),s=Array(o);++i<o;)s[i]=r[t+i];i=-1;for(var a=Array(t+1);++i<t;)a[i]=r[i];return a[t]=n(s),function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}(e,this,a)}}(e,t,j),e+"")}var k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},O=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),B=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},D=function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)},P=function(){function e(t){T(this,e),this._name="OrgChart",Promise.prototype.finally=function(e){var t=this.constructor;return this.then(function(n){return t.resolve(e()).then(function(){return n})},function(n){return t.resolve(e()).then(function(){throw n})})};var n=this,r=B({nodeTitle:"name",nodeId:"id",toggleSiblingsResp:!1,depth:999,chartClass:"",exportButton:!1,exportButtonName:"Export",exportFilename:"OrgChart",parentNodeSymbol:"",draggable:!1,direction:"t2b",pan:!1,zoom:!1,toggleCollapse:!0},t),i=r.data,o=document.createElement("div"),s=document.querySelector(r.chartContainer);if(this.options=r,delete this.options.data,this.chart=o,this.chartContainer=s,o.dataset.options=JSON.stringify(r),o.setAttribute("class","orgchart"+(""!==r.chartClass?" "+r.chartClass:"")+("t2b"!==r.direction?" "+r.direction:"")),"object"===(void 0===i?"undefined":k(i)))this.buildHierarchy(o,r.ajaxURL?i:this._attachRel(i,"00"),0);else if("string"==typeof i&&i.startsWith("#"))this.buildHierarchy(o,this._buildJsonDS(document.querySelector(i).children[0]),0);else{var a=document.createElement("i");a.setAttribute("class","fa fa-circle-o-notch fa-spin spinner"),o.appendChild(a),this._getJSON(i).then(function(e){n.buildHierarchy(o,r.ajaxURL?e:n._attachRel(e,"00"),0)}).catch(function(e){console.error("failed to fetch datasource for orgchart",e)}).finally(function(){var e=o.querySelector(".spinner");e.parentNode.removeChild(e)})}if(o.addEventListener("click",this._clickChart.bind(this)),r.exportButton&&!s.querySelector(".oc-export-btn")){var l=document.createElement("button"),c=document.createElement("a");l.setAttribute("class","oc-export-btn"+(""!==r.chartClass?" "+r.chartClass:"")),"Export"===r.exportButtonName?l.innerHTML="Export":l.innerHTML=""+r.exportButtonName,l.addEventListener("click",this._clickExportButton.bind(this)),c.setAttribute("class","oc-download-btn"+(""!==r.chartClass?" "+r.chartClass:"")),c.setAttribute("download",r.exportFilename+".png"),s.appendChild(l),s.appendChild(c)}r.pan&&(s.style.overflow="hidden",o.addEventListener("mousedown",this._onPanStart.bind(this)),o.addEventListener("touchstart",this._onPanStart.bind(this)),document.body.addEventListener("mouseup",this._onPanEnd.bind(this)),document.body.addEventListener("touchend",this._onPanEnd.bind(this))),r.zoom&&(s.addEventListener("wheel",this._onWheeling.bind(this)),s.addEventListener("touchstart",this._onTouchStart.bind(this)),document.body.addEventListener("touchmove",this._onTouchMove.bind(this)),document.body.addEventListener("touchend",this._onTouchEnd.bind(this))),s.appendChild(o)}return O(e,[{key:"_closest",value:function(e,t){return e&&(t(e)&&e!==this.chart?e:this._closest(e.parentNode,t))}},{key:"_siblings",value:function(e,t){return Array.from(e.parentNode.children).filter(function(n){return n!==e&&(!t||e.matches(t))})}},{key:"_prevAll",value:function(e,t){for(var n=[],r=e.previousElementSibling;r;)t&&!r.matches(t)||n.push(r),r=r.previousElementSibling;return n}},{key:"_nextAll",value:function(e,t){for(var n=[],r=e.nextElementSibling;r;)t&&!r.matches(t)||n.push(r),r=r.nextElementSibling;return n}},{key:"_isVisible",value:function(e){return null!==e.offsetParent}},{key:"_addClass",value:function(e,t){e.forEach(function(e){t.indexOf(" ")>0?t.split(" ").forEach(function(t){return e.classList.add(t)}):e.classList.add(t)})}},{key:"_removeClass",value:function(e,t){e.forEach(function(e){t.indexOf(" ")>0?t.split(" ").forEach(function(t){return e.classList.remove(t)}):e.classList.remove(t)})}},{key:"_css",value:function(e,t,n){e.forEach(function(e){e.style[t]=n})}},{key:"_removeAttr",value:function(e,t){e.forEach(function(e){e.removeAttribute(t)})}},{key:"_one",value:function(e,t,n,r){e&&e.addEventListener(t,function i(o){try{n.call(r,o)}finally{e.removeEventListener(t,i)}})}},{key:"_getDescElements",value:function(e,t){var n=[];return e.forEach(function(e){return n.push.apply(n,D(e.querySelectorAll(t)))}),n}},{key:"_getJSON",value:function(e){return new Promise(function(t,n){var r=new XMLHttpRequest;r.open("GET",e),r.onreadystatechange=function(){4===this.readyState&&(200===this.status?t(JSON.parse(this.response)):n(new Error(this.statusText)))},r.responseType="json",r.setRequestHeader("Content-Type","application/json"),r.send()})}},{key:"_buildJsonDS",value:function(e){var t=this,n={name:e.firstChild.textContent.trim(),relationship:("LI"===e.parentNode.parentNode.nodeName?"1":"0")+(e.parentNode.children.length>1?1:0)+(e.children.length?1:0)};return e.id&&(n.id=e.id),e.querySelector("ul")&&Array.from(e.querySelector("ul").children).forEach(function(e){n.children||(n.children=[]),n.children.push(t._buildJsonDS(e))}),n}},{key:"_attachRel",value:function(e,t){if(e.relationship=t+(e.children&&e.children.length>0?1:0),e.children){var n=!0,r=!1,i=void 0;try{for(var o,s=e.children[Symbol.iterator]();!(n=(o=s.next()).done);n=!0){var a=o.value;this._attachRel(a,"1"+(e.children.length>1?1:0))}}catch(e){r=!0,i=e}finally{try{!n&&s.return&&s.return()}finally{if(r)throw i}}}return e}},{key:"_repaint",value:function(e){e&&(e.style.offsetWidth=e.offsetWidth)}},{key:"_isInAction",value:function(e){return e.querySelector(":scope > .edge").className.indexOf("fa-")>-1}},{key:"_getNodeState",value:function(e,t){var n=this,r=void 0,i={exist:!1,visible:!1};return"parent"===t?((r=this._closest(e,function(e){return e.classList&&e.classList.contains("nodes")}))&&(i.exist=!0),i.exist&&this._isVisible(r.parentNode.children[0])&&(i.visible=!0)):"children"===t?((r=this._closest(e,function(e){return"TR"===e.nodeName}).nextElementSibling)&&(i.exist=!0),i.exist&&this._isVisible(r)&&(i.visible=!0)):"siblings"===t&&((r=this._siblings(this._closest(e,function(e){return"TABLE"===e.nodeName}).parentNode)).length&&(i.exist=!0),i.exist&&r.some(function(e){return n._isVisible(e)})&&(i.visible=!0)),i}},{key:"getRelatedNodes",value:function(e,t){return"parent"===t?this._closest(e,function(e){return e.classList.contains("nodes")}).parentNode.children[0].querySelector(".node"):"children"===t?Array.from(this._closest(e,function(e){return"TABLE"===e.nodeName}).lastChild.children).map(function(e){return e.querySelector(".node")}):"siblings"===t?this._siblings(this._closest(e,function(e){return"TABLE"===e.nodeName}).parentNode).map(function(e){return e.querySelector(".node")}):[]}},{key:"_switchHorizontalArrow",value:function(e){var t=this.options,n=e.querySelector(".leftEdge"),r=e.querySelector(".rightEdge"),i=this._closest(e,function(e){return"TABLE"===e.nodeName}).parentNode;if(t.toggleSiblingsResp&&(void 0===t.ajaxURL||this._closest(e,function(e){return e.classList.contains(".nodes")}).dataset.siblingsLoaded)){var o=i.previousElementSibling,s=i.nextElementSibling;o&&(o.classList.contains("hidden")?(n.classList.add("fa-chevron-left"),n.classList.remove("fa-chevron-right")):(n.classList.add("fa-chevron-right"),n.classList.remove("fa-chevron-left"))),s&&(s.classList.contains("hidden")?(r.classList.add("fa-chevron-right"),r.classList.remove("fa-chevron-left")):(r.classList.add("fa-chevron-left"),r.classList.remove("fa-chevron-right")))}else{var a=this._siblings(i),l=!!a.length&&!a.some(function(e){return e.classList.contains("hidden")});n.classList.toggle("fa-chevron-right",l),n.classList.toggle("fa-chevron-left",!l),r.classList.toggle("fa-chevron-left",l),r.classList.toggle("fa-chevron-right",!l)}}},{key:"_hoverNode",value:function(e){var t=e.target,n=!1,r=t.querySelector(":scope > .topEdge"),i=t.querySelector(":scope > .bottomEdge"),o=t.querySelector(":scope > .leftEdge");"mouseenter"===e.type?(r&&(n=this._getNodeState(t,"parent").visible,r.classList.toggle("fa-chevron-up",!n),r.classList.toggle("fa-chevron-down",n)),i&&(n=this._getNodeState(t,"children").visible,i.classList.toggle("fa-chevron-down",!n),i.classList.toggle("fa-chevron-up",n)),o&&this._switchHorizontalArrow(t)):Array.from(t.querySelectorAll(":scope > .edge")).forEach(function(e){e.classList.remove("fa-chevron-up","fa-chevron-down","fa-chevron-right","fa-chevron-left")})}},{key:"_clickNode",value:function(e){var t=e.currentTarget,n=this.chart.querySelector(".focused");n&&n.classList.remove("focused"),t.classList.add("focused")}},{key:"_buildParentNode",value:function(e,t,n){var r=this,i=document.createElement("table");t.relationship=t.relationship||"001",this._createNode(t,0).then(function(e){var t=r.chart;e.classList.remove("slide-up"),e.classList.add("slide-down");var o=document.createElement("tr"),s=document.createElement("tr"),a=document.createElement("tr"),l=document.createElement("tr");o.setAttribute("class","hidden"),o.innerHTML='<td colspan="2"></td>',i.appendChild(o),s.setAttribute("class","lines hidden"),s.innerHTML='<td colspan="2"><div class="downLine"></div></td>',i.appendChild(s),a.setAttribute("class","lines hidden"),a.innerHTML='<td class="rightLine">&nbsp;</td><td class="leftLine">&nbsp;</td>',i.appendChild(a),l.setAttribute("class","nodes"),l.innerHTML='<td colspan="2"></td>',i.appendChild(l),i.querySelector("td").appendChild(e),t.insertBefore(i,t.children[0]),i.children[3].children[0].appendChild(t.lastChild),n()}).catch(function(e){console.error("Failed to create parent node",e)})}},{key:"_switchVerticalArrow",value:function(e){e.classList.toggle("fa-chevron-up"),e.classList.toggle("fa-chevron-down")}},{key:"showParent",value:function(e){var t=this._prevAll(this._closest(e,function(e){return e.classList.contains("nodes")}));this._removeClass(t,"hidden"),this._addClass(Array(t[0].children).slice(1,-1),"hidden");var n=t[2].querySelector(".node");this._one(n,"transitionend",function(){n.classList.remove("slide"),this._isInAction(e)&&this._switchVerticalArrow(e.querySelector(":scope > .topEdge"))},this),this._repaint(n),n.classList.add("slide"),n.classList.remove("slide-down")}},{key:"showSiblings",value:function(e,t){var n=this,r=[],i=this._closest(e,function(e){return"TABLE"===e.nodeName}).parentNode;r=t?"left"===t?this._prevAll(i):this._nextAll(i):this._siblings(i),this._removeClass(r,"hidden");var o=this._prevAll(this._closest(e,function(e){return e.classList.contains("nodes")}));if(i=Array.from(o[0].querySelectorAll(":scope > .hidden")),t?this._removeClass(i.slice(0,2*r.length),"hidden"):this._removeClass(i,"hidden"),!this._getNodeState(e,"parent").visible){this._removeClass(o,"hidden");var s=o[2].querySelector(".node");this._one(s,"transitionend",function(e){e.target.classList.remove("slide")},this),this._repaint(s),s.classList.add("slide"),s.classList.remove("slide-down")}r.forEach(function(e){Array.from(e.querySelectorAll(".node")).forEach(function(e){n._isVisible(e)&&(e.classList.add("slide"),e.classList.remove("slide-left","slide-right"))})}),this._one(r[0].querySelector(".slide"),"transitionend",function(){var t=this;r.forEach(function(e){t._removeClass(Array.from(e.querySelectorAll(".slide")),"slide")}),this._isInAction(e)&&(this._switchHorizontalArrow(e),e.querySelector(".topEdge").classList.remove("fa-chevron-up"),e.querySelector(".topEdge").classList.add("fa-chevron-down"))},this)}},{key:"hideSiblings",value:function(e,t){var n=this,r=this._closest(e,function(e){return"TABLE"===e.nodeName}).parentNode;if(this._siblings(r).forEach(function(e){e.querySelector(".spinner")&&(n.chart.dataset.inAjax=!1)}),!t||t&&"left"===t){this._prevAll(r).forEach(function(e){Array.from(e.querySelectorAll(".node")).forEach(function(e){n._isVisible(e)&&e.classList.add("slide","slide-right")})})}if(!t||t&&"left"!==t){this._nextAll(r).forEach(function(e){Array.from(e.querySelectorAll(".node")).forEach(function(e){n._isVisible(e)&&e.classList.add("slide","slide-left")})})}var i=[];this._siblings(r).forEach(function(e){Array.prototype.push.apply(i,Array.from(e.querySelectorAll(".slide")))});var o=[],s=!0,a=!1,l=void 0;try{for(var c,d=i[Symbol.iterator]();!(s=(c=d.next()).done);s=!0){var u=c.value,h=this._closest(u,function(e){return e.classList.contains("nodes")}).previousElementSibling;o.push(h),o.push(h.previousElementSibling)}}catch(e){a=!0,l=e}finally{try{!s&&d.return&&d.return()}finally{if(a)throw l}}(o=[].concat(D(new Set(o)))).forEach(function(e){e.style.visibility="hidden"}),this._one(i[0],"transitionend",function(n){var s=this;o.forEach(function(e){e.removeAttribute("style")});var a=[];a=t?"left"===t?this._prevAll(r,":not(.hidden)"):this._nextAll(r,":not(.hidden)"):this._siblings(r);var l=Array.from(this._closest(r,function(e){return e.classList.contains("nodes")}).previousElementSibling.querySelectorAll(":scope > :not(.hidden)")).slice(1,t?2*a.length+1:-1);this._addClass(l,"hidden"),this._removeClass(i,"slide"),a.forEach(function(e){Array.from(e.querySelectorAll(".node")).slice(1).forEach(function(e){s._isVisible(e)&&(e.classList.remove("slide-left","slide-right"),e.classList.add("slide-up"))})}),a.forEach(function(e){s._addClass(Array.from(e.querySelectorAll(".lines")),"hidden"),s._addClass(Array.from(e.querySelectorAll(".nodes")),"hidden"),s._addClass(Array.from(e.querySelectorAll(".verticalNodes")),"hidden")}),this._addClass(a,"hidden"),this._isInAction(e)&&this._switchHorizontalArrow(e)},this)}},{key:"hideParent",value:function(e){var t=Array.from(this._closest(e,function(e){return e.classList.contains("nodes")}).parentNode.children).slice(0,3);t[0].querySelector(".spinner")&&(this.chart.dataset.inAjax=!1),this._getNodeState(e,"siblings").visible&&this.hideSiblings(e);var n=t.slice(1);this._css(n,"visibility","hidden");var r=t[0].querySelector(".node"),i=this._getNodeState(r,"parent").visible;r&&this._isVisible(r)&&(r.classList.add("slide","slide-down"),this._one(r,"transitionend",function(){r.classList.remove("slide"),this._removeAttr(n,"style"),this._addClass(t,"hidden")},this)),r&&i&&this.hideParent(r)}},{key:"addParent",value:function(e,t){var n=this;this._buildParentNode(e,t,function(){if(!e.querySelector(":scope > .topEdge")){var t=document.createElement("i");t.setAttribute("class","edge verticalEdge topEdge fa"),e.appendChild(t)}n.showParent(e)})}},{key:"_startLoading",value:function(e,t){var n=this.options,r=this.chart;if(void 0!==r.dataset.inAjax&&"true"===r.dataset.inAjax)return!1;e.classList.add("hidden");var i=document.createElement("i");i.setAttribute("class","fa fa-circle-o-notch fa-spin spinner"),t.appendChild(i),this._addClass(Array.from(t.querySelectorAll(":scope > *:not(.spinner)")),"hazy"),r.dataset.inAjax=!0;var o=this.chartContainer.querySelector(".oc-export-btn"+(""!==n.chartClass?"."+n.chartClass:""));return o&&(o.disabled=!0),!0}},{key:"_endLoading",value:function(e,t){var n=this.options;e.classList.remove("hidden"),t.querySelector(":scope > .spinner").remove(),this._removeClass(Array.from(t.querySelectorAll(":scope > .hazy")),"hazy"),this.chart.dataset.inAjax=!1;var r=this.chartContainer.querySelector(".oc-export-btn"+(""!==n.chartClass?"."+n.chartClass:""));r&&(r.disabled=!1)}},{key:"_clickTopEdge",value:function(e){e.stopPropagation();var t=this,n=e.target,r=n.parentNode,i=this._getNodeState(r,"parent"),o=this.options;if(i.exist){var s=this._closest(r,function(e){return e.classList.contains("nodes")}).parentNode.firstChild.querySelector(".node");if(s.classList.contains("slide"))return;i.visible?(this.hideParent(r),this._one(s,"transitionend",function(){this._isInAction(r)&&(this._switchVerticalArrow(n),this._switchHorizontalArrow(r))},this)):this.showParent(r)}else{var a=n.parentNode.id;this._startLoading(n,r)&&this._getJSON("function"==typeof o.ajaxURL.parent?o.ajaxURL.parent(r.dataset.source):o.ajaxURL.parent+a).then(function(e){"true"===t.chart.dataset.inAjax&&Object.keys(e).length&&t.addParent(r,e)}).catch(function(e){console.error("Failed to get parent node data.",e)}).finally(function(){t._endLoading(n,r)})}}},{key:"hideChildren",value:function(e){var t=this,n=this._nextAll(e.parentNode.parentNode),r=n[n.length-1],i=[];r.querySelector(".spinner")&&(this.chart.dataset.inAjax=!1);var o=Array.from(r.querySelectorAll(".node")).filter(function(e){return t._isVisible(e)}),s=r.classList.contains("verticalNodes");s||(o.forEach(function(e){Array.prototype.push.apply(i,t._prevAll(t._closest(e,function(e){return e.classList.contains("nodes")}),".lines"))}),i=[].concat(D(new Set(i))),this._css(i,"visibility","hidden")),this._one(o[0],"transitionend",function(a){this._removeClass(o,"slide"),s?t._addClass(n,"hidden"):(i.forEach(function(e){e.removeAttribute("style"),e.classList.add("hidden"),e.parentNode.lastChild.classList.add("hidden")}),this._addClass(Array.from(r.querySelectorAll(".verticalNodes")),"hidden")),this._isInAction(e)&&this._switchVerticalArrow(e.querySelector(".bottomEdge"))},this),this._addClass(o,"slide slide-up")}},{key:"showChildren",value:function(e){var t=this,n=this,r=this._nextAll(e.parentNode.parentNode),i=[];this._removeClass(r,"hidden"),r.some(function(e){return e.classList.contains("verticalNodes")})?r.forEach(function(e){Array.prototype.push.apply(i,Array.from(e.querySelectorAll(".node")).filter(function(e){return n._isVisible(e)}))}):Array.from(r[2].children).forEach(function(e){Array.prototype.push.apply(i,Array.from(e.querySelector("tr").querySelectorAll(".node")).filter(function(e){return n._isVisible(e)}))}),this._repaint(i[0]),this._one(i[0],"transitionend",function(n){t._removeClass(i,"slide"),t._isInAction(e)&&t._switchVerticalArrow(e.querySelector(".bottomEdge"))},this),this._addClass(i,"slide"),this._removeClass(i,"slide-up")}},{key:"_buildChildNode",value:function(e,t,n){var r=t.children||t.siblings;e.querySelector("td").setAttribute("colSpan",2*r.length),this.buildHierarchy(e,{children:r},0,n)}},{key:"addChildren",value:function(e,t){var n=this,r=this.options,i=0;this.chart.dataset.inEdit="addChildren",this._buildChildNode.call(this,this._closest(e,function(e){return"TABLE"===e.nodeName}),t,function(){if(++i===t.children.length){if(!e.querySelector(".bottomEdge")){var o=document.createElement("i");o.setAttribute("class","edge verticalEdge bottomEdge fa"),e.appendChild(o)}if(!e.querySelector(".symbol")){var s=document.createElement("i");s.setAttribute("class","fa "+r.parentNodeSymbol+" symbol"),e.querySelector(":scope > .title").appendChild(s)}n.showChildren(e),n.chart.dataset.inEdit=""}})}},{key:"_clickBottomEdge",value:function(e){var t=this;e.stopPropagation();var n=this,r=this.options,i=e.target,o=i.parentNode,s=this._getNodeState(o,"children");if(s.exist){var a=this._closest(o,function(e){return"TR"===e.nodeName}).parentNode.lastChild;if(Array.from(a.querySelectorAll(".node")).some(function(e){return t._isVisible(e)&&e.classList.contains("slide")}))return;s.visible?this.hideChildren(o):this.showChildren(o)}else{var l=i.parentNode.id;this._startLoading(i,o)&&this._getJSON("function"==typeof r.ajaxURL.children?r.ajaxURL.children(o.dataset.source):r.ajaxURL.children+l).then(function(e){"true"===n.chart.dataset.inAjax&&e.children.length&&n.addChildren(o,e)}).catch(function(e){console.error("Failed to get children nodes data",e)}).finally(function(){n._endLoading(i,o)})}}},{key:"_complementLine",value:function(e,t,n){var r=e.parentNode.parentNode.children;r[0].children[0].setAttribute("colspan",2*t),r[1].children[0].setAttribute("colspan",2*t);for(var i=0;i<n;i++){var o=document.createElement("td"),s=document.createElement("td");o.setAttribute("class","rightLine topLine"),o.innerHTML="&nbsp;",r[2].insertBefore(o,r[2].children[1]),s.setAttribute("class","leftLine topLine"),s.innerHTML="&nbsp;",r[2].insertBefore(s,r[2].children[1])}}},{key:"_buildSiblingNode",value:function(e,t,n){var r=this,i=this,o=t.siblings?t.siblings.length:t.children.length,s="TD"===e.parentNode.nodeName?this._closest(e,function(e){return"TR"===e.nodeName}).children.length:1,a=s+o,l=a>1?Math.floor(a/2-1):0;if("TD"===e.parentNode.nodeName){var c=this._prevAll(e.parentNode.parentNode);c[0].remove(),c[1].remove();var d=0;i._buildChildNode.call(i,i._closest(e.parentNode,function(e){return"TABLE"===e.nodeName}),t,function(){if(++d===o){var t=Array.from(i._closest(e.parentNode,function(e){return"TABLE"===e.nodeName}).lastChild.children);if(s>1){var r=e.parentNode.parentNode;Array.from(r.children).forEach(function(e){t[0].parentNode.insertBefore(e,t[0])}),r.remove(),i._complementLine(t[0],a,s),i._addClass(t,"hidden"),t.forEach(function(e){i._addClass(e.querySelectorAll(".node"),"slide-left")})}else{var c=e.parentNode.parentNode;t[l].parentNode.insertBefore(e.parentNode,t[l+1]),c.remove(),i._complementLine(t[l],a,1),i._addClass(t,"hidden"),i._addClass(i._getDescElements(t.slice(0,l+1),".node"),"slide-right"),i._addClass(i._getDescElements(t.slice(l+1),".node"),"slide-left")}n()}})}else{var u=0;i.buildHierarchy.call(i,i.chart,t,0,function(){if(++u===a){var t=e.nextElementSibling.children[3].children[l],o=document.createElement("td");o.setAttribute("colspan",2),o.appendChild(e),t.parentNode.insertBefore(o,t.nextElementSibling),i._complementLine(t,a,1);var s=i._closest(e,function(e){return e.classList&&e.classList.contains("nodes")}).parentNode.children[0];s.classList.add("hidden"),i._addClass(Array.from(s.querySelectorAll(".node")),"slide-down");var c=r._siblings(e.parentNode);i._addClass(c,"hidden"),i._addClass(i._getDescElements(c.slice(0,l),".node"),"slide-right"),i._addClass(i._getDescElements(c.slice(l),".node"),"slide-left"),n()}})}}},{key:"addSiblings",value:function(e,t){var n=this;this.chart.dataset.inEdit="addSiblings",this._buildSiblingNode.call(this,this._closest(e,function(e){return"TABLE"===e.nodeName}),t,function(){if(n._closest(e,function(e){return e.classList&&e.classList.contains("nodes")}).dataset.siblingsLoaded=!0,!e.querySelector(".leftEdge")){var t=document.createElement("i"),r=document.createElement("i");t.setAttribute("class","edge horizontalEdge rightEdge fa"),e.appendChild(t),r.setAttribute("class","edge horizontalEdge leftEdge fa"),e.appendChild(r)}n.showSiblings(e),n.chart.dataset.inEdit=""})}},{key:"removeNodes",value:function(e){var t=this._closest(e,function(e){return"TABLE"===e.nodeName}).parentNode,n=this._siblings(t.parentNode);"TD"===t.nodeName?this._getNodeState(e,"siblings").exist?(n[2].querySelector(".topLine").nextElementSibling.remove(),n[2].querySelector(".topLine").remove(),n[0].children[0].setAttribute("colspan",n[2].children.length),n[1].children[0].setAttribute("colspan",n[2].children.length),t.remove()):(n[0].children[0].removeAttribute("colspan"),n[0].querySelector(".bottomEdge").remove(),this._siblings(n[0]).forEach(function(e){return e.remove()})):Array.from(t.parentNode.children).forEach(function(e){return e.remove()})}},{key:"_clickHorizontalEdge",value:function(e){var t=this;e.stopPropagation();var n=this,r=this.options,i=e.target,o=i.parentNode,s=this._getNodeState(o,"siblings");if(s.exist){var a=this._closest(o,function(e){return"TABLE"===e.nodeName}).parentNode;if(this._siblings(a).some(function(e){var n=e.querySelector(".node");return t._isVisible(n)&&n.classList.contains("slide")}))return;if(r.toggleSiblingsResp){var l=this._closest(o,function(e){return"TABLE"===e.nodeName}).parentNode.previousElementSibling,c=this._closest(o,function(e){return"TABLE"===e.nodeName}).parentNode.nextElementSibling;i.classList.contains("leftEdge")?l&&l.classList.contains("hidden")?this.showSiblings(o,"left"):this.hideSiblings(o,"left"):c&&c.classList.contains("hidden")?this.showSiblings(o,"right"):this.hideSiblings(o,"right")}else s.visible?this.hideSiblings(o):this.showSiblings(o)}else{var d=i.parentNode.id,u=this._getNodeState(o,"parent").exist?"function"==typeof r.ajaxURL.siblings?r.ajaxURL.siblings(JSON.parse(o.dataset.source)):r.ajaxURL.siblings+d:"function"==typeof r.ajaxURL.families?r.ajaxURL.families(JSON.parse(o.dataset.source)):r.ajaxURL.families+d;this._startLoading(i,o)&&this._getJSON(u).then(function(e){"true"===n.chart.dataset.inAjax&&(e.siblings||e.children)&&n.addSiblings(o,e)}).catch(function(e){console.error("Failed to get sibling nodes data",e)}).finally(function(){n._endLoading(i,o)})}}},{key:"_clickToggleButton",value:function(e){var t=this,n=e.target,r=n.parentNode.nextElementSibling,i=Array.from(r.querySelectorAll(".node")),o=Array.from(r.children).map(function(e){return e.querySelector(".node")});o.some(function(e){return e.classList.contains("slide")})||(n.classList.toggle("fa-plus-square"),n.classList.toggle("fa-minus-square"),i[0].classList.contains("slide-up")?(r.classList.remove("hidden"),this._repaint(o[0]),this._addClass(o,"slide"),this._removeClass(o,"slide-up"),this._one(o[0],"transitionend",function(){t._removeClass(o,"slide")})):(this._addClass(i,"slide slide-up"),this._one(i[0],"transitionend",function(){t._removeClass(i,"slide"),i.forEach(function(e){t._closest(e,function(e){return"UL"===e.nodeName}).classList.add("hidden")})}),i.forEach(function(e){var n=Array.from(e.querySelectorAll(".toggleBtn"));t._removeClass(n,"fa-minus-square"),t._addClass(n,"fa-plus-square")})))}},{key:"_dispatchClickEvent",value:function(e){var t=e.target.classList;t.contains("topEdge")?this._clickTopEdge(e):t.contains("rightEdge")||t.contains("leftEdge")?this._clickHorizontalEdge(e):t.contains("bottomEdge")?this._clickBottomEdge(e):t.contains("toggleBtn")?this._clickToggleButton(e):this._clickNode(e)}},{key:"_onDragStart",value:function(e){var t=e.target,n=this.options,r=/firefox/.test(window.navigator.userAgent.toLowerCase());if(r&&e.dataTransfer.setData("text/html","hack for firefox"),this.chart.style.transform){var i=void 0,o=void 0;document.querySelector(".ghost-node")?o=(i=this.chart.querySelector(":scope > .ghost-node")).children[0]:((i=document.createElementNS("http://www.w3.org/2000/svg","svg")).classList.add("ghost-node"),o=document.createElementNS("http://www.w3.org/2000/svg","rect"),i.appendChild(o),this.chart.appendChild(i));var s=this.chart.style.transform.split(","),a=Math.abs(window.parseFloat("t2b"===n.direction||"b2t"===n.direction?s[0].slice(s[0].indexOf("(")+1):s[1]));i.setAttribute("width",t.offsetWidth),i.setAttribute("height",t.offsetHeight),o.setAttribute("x",5*a),o.setAttribute("y",5*a),o.setAttribute("width",120*a),o.setAttribute("height",40*a),o.setAttribute("rx",4*a),o.setAttribute("ry",4*a),o.setAttribute("stroke-width",1*a);var l=e.offsetX*a,c=e.offsetY*a;if("l2r"===n.direction?(l=e.offsetY*a,c=e.offsetX*a):"r2l"===n.direction?(l=t.offsetWidth-e.offsetY*a,c=e.offsetX*a):"b2t"===n.direction&&(l=t.offsetWidth-e.offsetX*a,c=t.offsetHeight-e.offsetY*a),r){var d=document.createElement("img");d.src="data:image/svg+xml;utf8,"+(new XMLSerializer).serializeToString(i),e.dataTransfer.setDragImage(d,l,c),o.setAttribute("fill","rgb(255, 255, 255)"),o.setAttribute("stroke","rgb(191, 0, 0)")}else e.dataTransfer.setDragImage(i,l,c)}var u=e.target,h=null;null!==this._closest(u,function(e){return e.classList&&e.classList.contains("nodes")})&&(h=this._closest(u,function(e){return e.classList&&e.classList.contains("nodes")}).parentNode.children[0].querySelector(".node"));var f=Array.from(this._closest(u,function(e){return"TABLE"===e.nodeName}).querySelectorAll(".node"));this.dragged=u,Array.from(this.chart.querySelectorAll(".node")).forEach(function(e){f.includes(e)||(n.dropCriteria?n.dropCriteria(u,h,e)&&e.classList.add("allowedDrop"):e.classList.add("allowedDrop"))})}},{key:"_onDragOver",value:function(e){e.preventDefault();e.currentTarget.classList.contains("allowedDrop")||(e.dataTransfer.dropEffect="none")}},{key:"_onDragEnd",value:function(e){Array.from(this.chart.querySelectorAll(".allowedDrop")).forEach(function(e){e.classList.remove("allowedDrop")})}},{key:"_onDrop",value:function(e){var t=e.currentTarget,n=this.chart,r=this.dragged,i=this._closest(r,function(e){return e.classList&&e.classList.contains("nodes")}).parentNode.children[0].children[0];if(this._removeClass(Array.from(n.querySelectorAll(".allowedDrop")),"allowedDrop"),t.parentNode.parentNode.nextElementSibling){var o=window.parseInt(t.parentNode.colSpan)+2;if(t.parentNode.setAttribute("colspan",o),t.parentNode.parentNode.nextElementSibling.children[0].setAttribute("colspan",o),!r.querySelector(".horizontalEdge")){var s=document.createElement("i"),a=document.createElement("i");s.setAttribute("class","edge horizontalEdge rightEdge fa"),r.appendChild(s),a.setAttribute("class","edge horizontalEdge leftEdge fa"),r.appendChild(a)}var l=t.parentNode.parentNode.nextElementSibling.nextElementSibling,c=document.createElement("td"),d=document.createElement("td");c.setAttribute("class","leftLine topLine"),c.innerHTML="&nbsp;",l.insertBefore(c,l.children[1]),d.setAttribute("class","rightLine topLine"),d.innerHTML="&nbsp;",l.insertBefore(d,l.children[2]),l.nextElementSibling.appendChild(this._closest(r,function(e){return"TABLE"===e.nodeName}).parentNode);var u=this._siblings(this._closest(r,function(e){return"TABLE"===e.nodeName}).parentNode).map(function(e){return e.querySelector(".node")});if(1===u.length){var h=document.createElement("i"),f=document.createElement("i");h.setAttribute("class","edge horizontalEdge rightEdge fa"),u[0].appendChild(h),f.setAttribute("class","edge horizontalEdge leftEdge fa"),u[0].appendChild(f)}}else{var p=document.createElement("i");p.setAttribute("class","edge verticalEdge bottomEdge fa"),t.appendChild(p),t.parentNode.setAttribute("colspan",2);var v=this._closest(t,function(e){return"TABLE"===e.nodeName}),g=document.createElement("tr"),y=document.createElement("tr"),m=document.createElement("tr");g.setAttribute("class","lines"),g.innerHTML='<td colspan="2"><div class="downLine"></div></td>',v.appendChild(g),y.setAttribute("class","lines"),y.innerHTML='<td class="rightLine">&nbsp;</td><td class="leftLine">&nbsp;</td>',v.appendChild(y),m.setAttribute("class","nodes"),v.appendChild(m),Array.from(r.querySelectorAll(".horizontalEdge")).forEach(function(e){r.removeChild(e)});var b=this._closest(r,function(e){return"TABLE"===e.nodeName}).parentNode;m.appendChild(b)}var _=window.parseInt(i.colSpan);if(_>2){i.setAttribute("colspan",_-2),i.parentNode.nextElementSibling.children[0].setAttribute("colspan",_-2);var E=i.parentNode.nextElementSibling.nextElementSibling;E.children[1].remove(),E.children[1].remove();var S=Array.from(i.parentNode.parentNode.children[3].children).map(function(e){return e.querySelector(".node")});1===S.length&&(S[0].querySelector(".leftEdge").remove(),S[0].querySelector(".rightEdge").remove())}else i.removeAttribute("colspan"),i.querySelector(".node").removeChild(i.querySelector(".bottomEdge")),Array.from(i.parentNode.parentNode.children).slice(1).forEach(function(e){return e.remove()});var A=new CustomEvent("nodedropped.orgchart",{detail:{draggedNode:r,dragZone:i.children[0],dropZone:t}});n.dispatchEvent(A)}},{key:"_createNode",value:function(e,t){var n=this,r=this.options;return new Promise(function(i,o){if(e.children){var s=!0,a=!1,l=void 0;try{for(var c,d=e.children[Symbol.iterator]();!(s=(c=d.next()).done);s=!0){c.value.parentId=e.id}}catch(e){a=!0,l=e}finally{try{!s&&d.return&&d.return()}finally{if(a)throw l}}}var u=document.createElement("div");delete e.children,u.dataset.source=JSON.stringify(e),e[r.nodeId]&&(u.id=e[r.nodeId]);var h=n.chart.dataset.inEdit,f=void 0;f=h?"addChildren"===h?" slide-up":"":t>=r.depth?" slide-up":"",u.setAttribute("class","node "+(e.className||"")+f),r.draggable&&u.setAttribute("draggable",!0),e.parentId&&u.setAttribute("data-parent",e.parentId),u.innerHTML='\n        <div class="title">'+e[r.nodeTitle]+"</div>\n        "+(r.nodeContent?'<div class="content">'+e[r.nodeContent]+"</div>":"")+"\n      ";var p=e.relationship||"";if(r.verticalDepth&&t+2>r.verticalDepth){if(t+1>=r.verticalDepth&&Number(p.substr(2,1))){var v=document.createElement("i"),g=t+1>=r.depth?"plus":"minus";v.setAttribute("class","toggleBtn fa fa-"+g+"-square"),u.appendChild(v)}}else{if(Number(p.substr(0,1))){var y=document.createElement("i");y.setAttribute("class","edge verticalEdge topEdge fa"),u.appendChild(y)}if(Number(p.substr(1,1))){var m=document.createElement("i"),b=document.createElement("i");m.setAttribute("class","edge horizontalEdge rightEdge fa"),u.appendChild(m),b.setAttribute("class","edge horizontalEdge leftEdge fa"),u.appendChild(b)}if(Number(p.substr(2,1))){var _=document.createElement("i"),E=document.createElement("i"),S=u.querySelector(":scope > .title");_.setAttribute("class","edge verticalEdge bottomEdge fa"),u.appendChild(_),E.setAttribute("class","fa "+r.parentNodeSymbol+" symbol"),S.insertBefore(E,S.children[0])}}r.toggleCollapse&&(u.addEventListener("mouseenter",n._hoverNode.bind(n)),u.addEventListener("mouseleave",n._hoverNode.bind(n)),u.addEventListener("click",n._dispatchClickEvent.bind(n))),r.draggable&&(u.addEventListener("dragstart",n._onDragStart.bind(n)),u.addEventListener("dragover",n._onDragOver.bind(n)),u.addEventListener("dragend",n._onDragEnd.bind(n)),u.addEventListener("drop",n._onDrop.bind(n))),r.createNode&&r.createNode(u,e),i(u)})}},{key:"buildHierarchy",value:function(e,t,n,r){var i=this,o=this.options,s=void 0,a=t.children,l=o.verticalDepth&&n+1>=o.verticalDepth;if(Object.keys(t).length>1&&(s=l?e:document.createElement("table"),l||e.appendChild(s),this._createNode(t,n).then(function(e){if(l)s.insertBefore(e,s.firstChild);else{var t=document.createElement("tr");t.innerHTML="\n            <td "+(a?'colspan="'+2*a.length+'"':"")+">\n            </td>\n          ",t.children[0].appendChild(e),s.insertBefore(t,s.children[0]?s.children[0]:null)}r&&r()}).catch(function(e){console.error("Failed to creat node",e)})),a&&0!==a.length){1===Object.keys(t).length&&(s=e);var c=void 0,d=o.verticalDepth&&n+2>=o.verticalDepth,u=i.chart.dataset.inEdit;if(c=u?"addSiblings"===u?"":" hidden":n+1>=o.depth?" hidden":"",!d){var h=document.createElement("tr");h.setAttribute("class","lines"+c),h.innerHTML='\n          <td colspan="'+2*a.length+'">\n            <div class="downLine"></div>\n          </td>\n        ',s.appendChild(h)}var f=document.createElement("tr");f.setAttribute("class","lines"+c),f.innerHTML='\n        <td class="rightLine">&nbsp;</td>\n        '+a.slice(1).map(function(){return'\n          <td class="leftLine topLine">&nbsp;</td>\n          <td class="rightLine topLine">&nbsp;</td>\n          '}).join("")+'\n        <td class="leftLine">&nbsp;</td>\n      ';var p=void 0;if(d)if(p=document.createElement("ul"),c&&p.classList.add(c.trim()),n+2===o.verticalDepth){var v=document.createElement("tr");v.setAttribute("class","verticalNodes"+c),v.innerHTML="<td></td>",v.firstChild.appendChild(p),s.appendChild(v)}else s.appendChild(p);else(p=document.createElement("tr")).setAttribute("class","nodes"+c),s.appendChild(f),s.appendChild(p);a.forEach(function(e){var t=void 0;d?t=document.createElement("li"):(t=document.createElement("td")).setAttribute("colspan",2),p.appendChild(t),i.buildHierarchy(t,e,n+1,r)})}}},{key:"_clickChart",value:function(e){!this._closest(e.target,function(e){return e.classList&&e.classList.contains("node")})&&this.chart.querySelector(".node.focused")&&this.chart.querySelector(".node.focused").classList.remove("focused")}},{key:"_clickExportButton",value:function(){var e=this.options,t=this.chartContainer,n=t.querySelector(":scope > .mask"),r=t.querySelector(".orgchart:not(.hidden)"),i="l2r"===e.direction||"r2l"===e.direction;n?n.classList.remove("hidden"):((n=document.createElement("div")).setAttribute("class","mask"),n.innerHTML='<i class="fa fa-circle-o-notch fa-spin spinner"></i>',t.appendChild(n)),t.classList.add("canvasContainer"),window.html2canvas(r,{width:i?r.clientHeight:r.clientWidth,height:i?r.clientWidth:r.clientHeight,onclone:function(e){var t=e.querySelector(".canvasContainer");t.style.overflow="visible",t.querySelector(".orgchart:not(.hidden)").transform=""}}).then(function(e){var n=t.querySelector(".oc-download-btn");t.querySelector(".mask").classList.add("hidden"),n.setAttribute("href",e.toDataURL()),n.click()}).catch(function(e){console.error("Failed to export the curent orgchart!",e)}).finally(function(){t.classList.remove("canvasContainer")})}},{key:"_loopChart",value:function(e){var t=this,n={id:e.querySelector(".node").id};return e.children[3]&&Array.from(e.children[3].children).forEach(function(e){n.children||(n.children=[]),n.children.push(t._loopChart(e.firstChild))}),n}},{key:"_loopChartDataset",value:function(e){var t=this,n=JSON.parse(e.querySelector(".node").dataset.source);return e.children[3]&&Array.from(e.children[3].children).forEach(function(e){n.children||(n.children=[]),n.children.push(t._loopChartDataset(e.firstChild))}),n}},{key:"getChartJSON",value:function(){return this.chart.querySelector(".node").id?this._loopChartDataset(this.chart.querySelector("table")):"Error: Nodes of orghcart to be exported must have id attribute!"}},{key:"getHierarchy",value:function(){return this.chart.querySelector(".node").id?this._loopChart(this.chart.querySelector("table")):"Error: Nodes of orghcart to be exported must have id attribute!"}},{key:"_onPanStart",value:function(e){var t=e.currentTarget;if(this._closest(e.target,function(e){return e.classList&&e.classList.contains("node")})||e.touches&&e.touches.length>1)t.dataset.panning=!1;else{t.style.cursor="move",t.dataset.panning=!0;var n=0,r=0,i=window.getComputedStyle(t).transform;if("none"!==i){var o=i.split(",");i.includes("3d")?(n=Number.parseInt(o[12],10),r=Number.parseInt(o[13],10)):(n=Number.parseInt(o[4],10),r=Number.parseInt(o[5],10))}var s=0,a=0;if(e.targetTouches){if(1===e.targetTouches.length)s=e.targetTouches[0].pageX-n,a=e.targetTouches[0].pageY-r;else if(e.targetTouches.length>1)return}else s=e.pageX-n,a=e.pageY-r;t.dataset.panStart=JSON.stringify({startX:s,startY:a}),t.addEventListener("mousemove",this._onPanning.bind(this)),t.addEventListener("touchmove",this._onPanning.bind(this))}}},{key:"_onPanning",value:function(e){var t=e.currentTarget;if("false"!==t.dataset.panning){var n=0,r=0,i=JSON.parse(t.dataset.panStart),o=i.startX,s=i.startY;if(e.targetTouches){if(1===e.targetTouches.length)n=e.targetTouches[0].pageX-o,r=e.targetTouches[0].pageY-s;else if(e.targetTouches.length>1)return}else n=e.pageX-o,r=e.pageY-s;var a=window.getComputedStyle(t).transform;if("none"===a)a.includes("3d")?t.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, "+n+", "+r+", 0, 1)":t.style.transform="matrix(1, 0, 0, 1, "+n+", "+r+")";else{var l=a.split(",");a.includes("3d")?(l[12]=n,l[13]=r):(l[4]=n,l[5]=r+")"),t.style.transform=l.join(",")}}}},{key:"_onPanEnd",value:function(e){var t=this.chart;"true"===t.dataset.panning&&(t.dataset.panning=!1,t.style.cursor="default",document.body.removeEventListener("mousemove",this._onPanning),document.body.removeEventListener("touchmove",this._onPanning))}},{key:"_setChartScale",value:function(e,t){var n=window.getComputedStyle(e).transform;if("none"===n)e.style.transform="scale("+t+","+t+")";else{var r=n.split(",");n.includes("3d")?e.style.transform=n+" scale3d("+t+","+t+", 1)":(r[0]="matrix("+t,r[3]=t,e.style.transform=n+" scale("+t+","+t+")")}e.dataset.scale=t}},{key:"_onWheeling",value:function(e){e.preventDefault();var t=e.deltaY>0?.8:1.2;this._setChartScale(this.chart,t)}},{key:"_getPinchDist",value:function(e){return Math.sqrt((e.touches[0].clientX-e.touches[1].clientX)*(e.touches[0].clientX-e.touches[1].clientX)+(e.touches[0].clientY-e.touches[1].clientY)*(e.touches[0].clientY-e.touches[1].clientY))}},{key:"_onTouchStart",value:function(e){var t=this.chart;if(e.touches&&2===e.touches.length){var n=this._getPinchDist(e);t.dataset.pinching=!0,t.dataset.pinchDistStart=n}}},{key:"_onTouchMove",value:function(e){var t=this.chart;if(t.dataset.pinching){var n=this._getPinchDist(e);t.dataset.pinchDistEnd=n}}},{key:"_onTouchEnd",value:function(e){var t=this.chart;if(t.dataset.pinching){t.dataset.pinching=!1;var n=t.dataset.pinchDistEnd-t.dataset.pinchDistStart;n>0?this._setChartScale(t,1):n<0&&this._setChartScale(t,-1)}}},{key:"name",get:function(){return this._name}}]),e}(),z=Array.prototype.splice;r.prototype.clear=function(){this.__data__=[],this.size=0},r.prototype.delete=function(e){var t=this.__data__,r=n(t,e);return!(r<0||(r==t.length-1?t.pop():z.call(t,r,1),--this.size,0))},r.prototype.get=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]},r.prototype.has=function(e){return n(this.__data__,e)>-1},r.prototype.set=function(e,t){var r=this.__data__,i=n(r,e);return i<0?(++this.size,r.push([e,t])):r[i][1]=t,this};var H="object"==typeof global&&global&&global.Object===Object&&global,R="object"==typeof self&&self&&self.Object===Object&&self,M=H||R||Function("return this")(),I=M.Symbol,F=Object.prototype,U=F.hasOwnProperty,V=F.toString,J=I?I.toStringTag:void 0,X=Object.prototype.toString,Y="[object Null]",$="[object Undefined]",W=I?I.toStringTag:void 0,G="[object AsyncFunction]",Z="[object Function]",K="[object GeneratorFunction]",Q="[object Proxy]",ee=M["__core-js_shared__"],te=function(){var e=/[^.]+$/.exec(ee&&ee.keys&&ee.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),ne=Function.prototype.toString,re=/^\[object .+?Constructor\]$/,ie=Function.prototype,oe=Object.prototype,se=ie.toString,ae=oe.hasOwnProperty,le=RegExp("^"+se.call(ae).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ce=l(M,"Map"),de=l(Object,"create"),ue="__lodash_hash_undefined__",he=Object.prototype.hasOwnProperty,fe=Object.prototype.hasOwnProperty,pe="__lodash_hash_undefined__";c.prototype.clear=function(){this.__data__=de?de(null):{},this.size=0},c.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},c.prototype.get=function(e){var t=this.__data__;if(de){var n=t[e];return n===ue?void 0:n}return he.call(t,e)?t[e]:void 0},c.prototype.has=function(e){var t=this.__data__;return de?void 0!==t[e]:fe.call(t,e)},c.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=de&&void 0===t?pe:t,this},u.prototype.clear=function(){this.size=0,this.__data__={hash:new c,map:new(ce||r),string:new c}},u.prototype.delete=function(e){var t=d(this,e).delete(e);return this.size-=t?1:0,t},u.prototype.get=function(e){return d(this,e).get(e)},u.prototype.has=function(e){return d(this,e).has(e)},u.prototype.set=function(e,t){var n=d(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};var ve=200;h.prototype.clear=function(){this.__data__=new r,this.size=0},h.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},h.prototype.get=function(e){return this.__data__.get(e)},h.prototype.has=function(e){return this.__data__.has(e)},h.prototype.set=function(e,t){var n=this.__data__;if(n instanceof r){var i=n.__data__;if(!ce||i.length<ve-1)return i.push([e,t]),this.size=++n.size,this;n=this.__data__=new u(i)}return n.set(e,t),this.size=n.size,this};var ge=function(){try{var e=l(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),ye=function(e){return function(t,n,r){for(var i=-1,o=Object(t),s=r(t),a=s.length;a--;){var l=s[e?a:++i];if(!1===n(o[l],l,o))break}return t}}(),me="object"==typeof e&&e&&!e.nodeType&&e,be=me&&"object"==typeof module&&module&&!module.nodeType&&module,_e=be&&be.exports===me?M.Buffer:void 0,Ee=_e?_e.allocUnsafe:void 0,Se=M.Uint8Array,Ae=Object.create,Le=function(){function e(){}return function(t){if(!o(t))return{};if(Ae)return Ae(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}(),Ce=function(e,t){return function(n){return e(t(n))}}(Object.getPrototypeOf,Object),Ne=Object.prototype,we="[object Arguments]",xe=Object.prototype,je=xe.hasOwnProperty,qe=xe.propertyIsEnumerable,ke=m(function(){return arguments}())?m:function(e){return y(e)&&je.call(e,"callee")&&!qe.call(e,"callee")},Te=Array.isArray,Oe=9007199254740991,Be="object"==typeof e&&e&&!e.nodeType&&e,De=Be&&"object"==typeof module&&module&&!module.nodeType&&module,Pe=De&&De.exports===Be?M.Buffer:void 0,ze=(Pe?Pe.isBuffer:void 0)||function(){return!1},He="[object Object]",Re=Function.prototype,Me=Object.prototype,Ie=Re.toString,Fe=Me.hasOwnProperty,Ue=Ie.call(Object),Ve={};Ve["[object Float32Array]"]=Ve["[object Float64Array]"]=Ve["[object Int8Array]"]=Ve["[object Int16Array]"]=Ve["[object Int32Array]"]=Ve["[object Uint8Array]"]=Ve["[object Uint8ClampedArray]"]=Ve["[object Uint16Array]"]=Ve["[object Uint32Array]"]=!0,Ve["[object Arguments]"]=Ve["[object Array]"]=Ve["[object ArrayBuffer]"]=Ve["[object Boolean]"]=Ve["[object DataView]"]=Ve["[object Date]"]=Ve["[object Error]"]=Ve["[object Function]"]=Ve["[object Map]"]=Ve["[object Number]"]=Ve["[object Object]"]=Ve["[object RegExp]"]=Ve["[object Set]"]=Ve["[object String]"]=Ve["[object WeakMap]"]=!1;var Je="object"==typeof e&&e&&!e.nodeType&&e,Xe=Je&&"object"==typeof module&&module&&!module.nodeType&&module,Ye=Xe&&Xe.exports===Je&&H.process,$e=function(){try{return Ye&&Ye.binding&&Ye.binding("util")}catch(e){}}(),We=$e&&$e.isTypedArray,Ge=We?function(e){return function(t){return e(t)}}(We):function(e){return y(e)&&b(e.length)&&!!Ve[i(e)]},Ze=Object.prototype.hasOwnProperty,Ke=9007199254740991,Qe=/^(?:0|[1-9]\d*)$/,et=Object.prototype.hasOwnProperty,tt=Object.prototype.hasOwnProperty,nt=Math.max,rt=ge?function(e,t){return ge(e,"toString",{configurable:!0,enumerable:!1,value:function(e){return function(){return e}}(t),writable:!0})}:j,it=800,ot=16,st=Date.now,at=function(e){var t=0,n=0;return function(){var r=st(),i=ot-(r-n);if(n=r,i>0){if(++t>=it)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(rt),lt=function(e){return q(function(n,r){var i=-1,s=r.length,a=s>1?r[s-1]:void 0,l=s>2?r[2]:void 0;for(a=e.length>3&&"function"==typeof a?(s--,a):void 0,l&&function(e,n,r){if(!o(r))return!1;var i=typeof n;return!!("number"==i?_(r)&&S(n,r.length):"string"==i&&n in r)&&t(r[n],e)}(r[0],r[1],l)&&(a=s<3?void 0:a,s=1),n=Object(n);++i<s;){var c=r[i];c&&e(n,c,i,a)}return n})}(function(e,t,n){x(e,t,n)}),ct=function(e,t){return lt(e,t)},dt={render:function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"vo-basic",attrs:{id:"chart-container"}})},staticRenderFns:[],name:"orgchart",props:{data:{type:Object,default:function(){return{}}},pan:{type:Boolean,default:!1},zoom:{type:Boolean,default:!1},direction:{type:String,default:"t2b"},verticalDepth:{type:Number},toggleSiblingsResp:{type:Boolean,default:!1},ajaxURL:{type:Object},depth:{type:Number,default:999},nodeTitle:{type:String,default:"name"},parentNodeSymbol:{type:String,default:""},nodeContent:{type:String},nodeId:{type:String,default:"id"},createNode:{type:Function},exportButton:{type:Boolean,default:!1},exportButtonName:{type:String,default:"Export"},exportFilename:{type:String},chartClass:{type:String,default:""},draggable:{type:Boolean,default:!1},dropCriteria:{type:Function},toggleCollapse:{type:Boolean,default:!0}},data:function(){return{newData:null,orgchart:null,defaultOptions:{chartContainer:"#chart-container"}}},mounted:function(){null===this.newData&&this.initOrgChart()},methods:{initOrgChart:function(){var e=ct(this.defaultOptions,this.$props);this.orgchart=new P(e)}},watch:{data:function(e){var t=this;this.newData=e;new Promise(function(t){e&&t()}).then(function(){var e=ct(t.defaultOptions,t.$props);t.orgchart=new P(e)})}}},ut=function e(t,n){return t&&(n(t)&&t!==document.querySelector(".orgchart")?t:e(t.parentNode,n))},ht=function(e,t,n,r){r?document.querySelector(r).addEventListener(t,function(t){(t.target.classList&&t.target.classList.contains(e.slice(1))||ut(t.target,function(t){return t.classList&&t.classList.contains(e.slice(1))}))&&n(t)}):document.querySelectorAll(e).forEach(function(e){e.addEventListener(t,n)})},ft=function(e){var t=ut(e.target,function(e){return e.classList&&e.classList.contains("node")}),n=document.getElementById("selected-node");n.value=t.querySelector(".title").textContent,n.dataset.node=t.id},pt=function(e){ut(e.target,function(e){return e.classList&&e.classList.contains("node")})||(document.getElementById("selected-node").textContent="")},vt={render:function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"vo-edit",attrs:{id:"chart-container"}})},staticRenderFns:[],name:"VoEdit",props:{data:{type:Object},pan:{type:Boolean,default:!1},zoom:{type:Boolean,default:!1},direction:{type:String,default:"t2b"},verticalDepth:{type:Number},toggleSiblingsResp:{type:Boolean,default:!1},ajaxURL:{type:Object},depth:{type:Number,default:999},nodeTitle:{type:String,default:"name"},parentNodeSymbol:{type:String,default:""},nodeContent:{type:String},nodeId:{type:String,default:"id"},createNode:{type:Function},exportButton:{type:Boolean,default:!1},exportButtonName:{type:String,default:"Export"},exportFilename:{type:String},chartClass:{type:String,default:""},draggable:{type:Boolean,default:!1},dropCriteria:{type:Function},toggleCollapse:{type:Boolean,default:!0}},data:function(){return{newData:null,orgchart:null,defaultOptions:{chartContainer:"#chart-container",createNode:function(e,t){e.id=1e3*(new Date).getTime()+Math.floor(1001*Math.random())}}}},mounted:function(){null===this.newData&&this.initOrgChart(),this.$nextTick(function(){ht(".node","click",ft,"#chart-container"),ht(".orgchart","click",pt,"#chart-container")})},methods:{initOrgChart:function(){var e=ct(this.defaultOptions,this.$props);this.orgchart=new P(e)}},watch:{data:function(e){var t=this;this.newData=e;new Promise(function(t){e&&t()}).then(function(){var e=ct(t.defaultOptions,t.$props);t.orgchart=new P(e)})}}};"undefined"!=typeof window&&window.Vue&&(window.Vue.component("vo-basic",dt),window.Vue.component("vo-edit",vt)),e.VoBasic=dt,e.VoEdit=vt,Object.defineProperty(e,"__esModule",{value:!0})});
