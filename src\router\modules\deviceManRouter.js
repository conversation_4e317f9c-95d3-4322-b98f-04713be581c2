/** 设备分支 **/
import Layout from "@/views/layout/Layout";

const examRouter = {
  path: "/deviceMan",
  component: Layout,
  redirect: "/examMan/course/list",
  name: "deviceMan",
  meta: {
    title: "设备管理",
    icon: "deviceMan",
    roles: ["equ_shebeiguanli_jyt"],
  },

  children: [
    {
      path: "deviceFileMan",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "deviceFileMan",
      meta: {
        title: "设备档案管理",
        roles: ["equ_shebeidanganguanli_jyt"],
      },
      children: [
        {
          path: "basicledgerMain",
          component: () =>
            import("@/views/deviceMan/deviceFileMan/basicledgerMain/index"),
          name: "basicledgerMain",
          meta: {
            title: "设备信息维护",
            roles: ["equ_jichutaizhangweihu_jyt"],
          },
          children: [],
        },
        {
          path: "equManualMan",
          component: () =>
            import("@/views/deviceMan/deviceFileMan/equManualMan/index"),
          name: "equManualMan",
          meta: {
            title: "操作手册管理",
            roles: ["equ_caozuoshouceguanli_jyt"],
          },
          children: [],
        },
        {
          path: "deviceOperRecords",
          component: () =>
            import("@/views/deviceMan/deviceFileMan/deviceOperRecords/index"),
          name: "deviceOperRecords",
          meta: {
            title: "设备运行记录",
            roles: ["equ_shebeiyunxingjilu_jyt"],
          },
          children: [],
        },
        {
          path: "runningRecordTempMan",
          component: () =>
            import(
              "@/views/deviceMan/deviceFileMan/runningRecordTempMan/index"
            ),
          name: "runningRecordTempMan",
          meta: {
            title: "运行模板管理",
            roles: ["equ_yunxingjilumobanguanli_jyt"],
          },
          children: [],
        },
        {
          path: "MainItemsList",
          component: () =>
            import("@/views/deviceMan/deviceFileMan/MainItemsList/index"),
          name: "MainItemsList",
          meta: {
            title: "检修事项维护",
            roles: ["equ_jianxiushixiangweihu_jyt"],
          },
          children: [],
        },
        {
          path: "mainSubjectCategory",
          component: () =>
            import("@/views/deviceMan/deviceFileMan/mainSubjectCategory/index"),
          name: "mainSubjectCategory",
          meta: {
            title: "维修科目维护",
            roles: ["equ_weixiukemuleibieweihu_jyt"],
          },
          children: [],
        },
        {
          path: "mainEquPosition",
          component: () =>
            import("@/views/deviceMan/deviceFileMan/mainEquPosition/index"),
          name: "mainEquPosition",
          meta: {
            title: "设备地址维护",
            roles: ["equ_shebeidizhiweihu_jyt"],
          },
          children: [],
        },
        {
          path: "formProcessDesign",
          component: () =>
            import(
              "@/views/deviceMan/deviceFileMan/runningRecordTempMan/dyFormDesign/index"
            ),
          name: "formProcessDesign",
          meta: {
            title: "运行记录模板设置",
            roles: ["equ_yunxingjilumobanguanli_jyt"],
          },
          hidden: true,
          children: [],
        },
      ],
    },
    {
      path: "deviceMainRepair",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "deviceMainRepair",
      meta: {
        title: "设备保养检修",
        roles: ["equ_shebeibaoyangjianxiu_jyt"],
      },
      children: [
        {
          path: "deviceMain",
          component: () => import("@/views/heatMan/dataCollect/index"),
          name: "deviceMain",
          meta: {
            title: "设备保养",
            roles: ["equ_shebeibaoyang_jyt"],
          },
          children: [
            {
              path: "mainPlan",
              component: () =>
                import(
                  "@/views/deviceMan/deviceMainRepair/deviceMain/mainPlan/index"
                ),
              name: "mainPlan",
              meta: {
                title: "保养计划",
                roles: ["equ_baoyangjihua_jyt"],
              },
              children: [],
            },
            {
              path: "mainPlanExe",
              component: () =>
                import(
                  "@/views/deviceMan/deviceMainRepair/deviceMain/mainPlanExe/index"
                ),
              name: "mainPlanExe",
              meta: {
                title: "保养计划执行",
                roles: ["equ_baoyangjihuazhixing_jyt"],
              },
              children: [],
            },
            {
              path: "mainPlanExeMan",
              component: () =>
                import(
                  "@/views/deviceMan/deviceMainRepair/deviceMain/mainPlanExeMan/index"
                ),
              name: "mainPlanExeMan",
              meta: {
                title: "保养执行记录",
                roles: ["equ_baoyangzhixingjilu_jyt"],
              },
              children: [],
            },
          ],
        },
        {
          path: "deviceRepair",
          component: () => import("@/views/heatMan/dataCollect/index"),
          name: "deviceRepair",
          meta: {
            title: "设备检修",
            roles: ["equ_shebeijianxiu_jyt"],
          },
          children: [
            {
              path: "mainPlan",
              component: () =>
                import(
                  "@/views/deviceMan/deviceMainRepair/deviceRepair/mainPlan/index"
                ),
              name: "mainPlan",
              meta: {
                title: "检修计划",
                roles: ["equ_jianxiujihua_jyt"],
              },
              children: [],
            },
            {
              path: "mainPlanExe",
              component: () =>
                import(
                  "@/views/deviceMan/deviceMainRepair/deviceRepair/mainPlanExe/index"
                ),
              name: "mainPlanExe",
              meta: {
                title: "检修计划执行",
                roles: ["equ_jianxiujihuazhixing_jyt"],
              },
              children: [],
            },
            {
              path: "mainPlanExeMan",
              component: () =>
                import(
                  "@/views/deviceMan/deviceMainRepair/deviceRepair/mainPlanExeMan/index"
                ),
              name: "mainPlanExeMan",
              meta: {
                title: "检修执行记录",
                roles: ["equ_jianxiuzhixingjilu_jyt"],
              },
              children: [],
            },
          ],
        },
        {
          path: "deviceMaintainableMan",
          component: () => import("@/views/heatMan/dataCollect/index"),
          name: "deviceMaintainableMan",
          meta: {
            title: "设备维修",
            roles: ["equ_shebeiweixiuguanli_jyt"],
          },
          children: [
            {
              path: "equPlan",
              component: () =>
                import(
                  "@/views/deviceMan/deviceMainRepair/equMain/equPlan/index"
                ),
              name: "equPlan",
              meta: {
                title: "设备维修",
                roles: ["equ_shebeiweixiu_jyt"],
              },
              children: [],
            },
            {
              path: "equMan",
              component: () =>
                import(
                  "@/views/deviceMan/deviceMainRepair/equMain/equMan/index"
                ),
              name: "equMan",
              meta: {
                title: "设备维修记录",
                roles: ["equ_shebeiweixiujilu_jyt"],
              },
              children: [],
            },
          ],
        },
      ],
    },
    {
      path: "reportMan",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "reportMan",
      meta: {
        title: "报表管理",
        roles: ["equ_baobiaoguanli_jyt"],
      },
      children: [
        {
          path: "deviceMainRepair",
          component: () =>
            import("@/views/deviceMan/reportMan/deviceMainRepair/index"),
          name: "deviceMainRepair",
          meta: {
            title: "设备保养、检修信息",
            roles: ["equ_shebeibaoyangjianxiuxinxi_jyt"],
          },
          children: [],
        },

        {
          path: "equOperRecords",
          component: () => import("@/views/heatMan/dataCollect/index"),
          name: "equOperRecords",
          meta: {
            title: "设备运行记录统计",
            roles: ["equ_shebeiyunxingjilutongji_jyt"],
          },
          children: [
            {
              path: "deviceOperRecords",
              component: () =>
                import("@/views/deviceMan/reportMan/deviceOperRecords/index"),
              name: "deviceOperRecords",
              meta: {
                title: "设备运行信息",
                roles: ["equ_shebeiyunxingxinxitongji_jyt"],
              },
              children: [],
            },
            {
              path: "month",
              component: () =>
                import("@/views/deviceMan/reportMan/equOperRecords/month.vue"),
              name: "month",
              meta: {
                title: "月度统计",
                roles: ["equ_shebeiyuedutongji_jyt"],
              },
              children: [],
            },
          ],
        },
      ],
    },
    {
      path: "technicalDataMan",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "technicalDataMan",
      meta: {
        title: "技术资料管理",
        roles: ["equ_jishuziliao_jyt"],
      },
      children: [
        {
          path: "createTechnicalData",
          component: () =>
            import(
              "@/views/deviceMan/technicalDataMan/createTechnicalData.vue"
            ),
          name: "createTechnicalData",
          meta: {
            title: "技术资料创建",
            roles: ["equ_jishuziliaochuangjian_jyt"],
          },
          children: [],
        },
        {
          path: "viewTechnicalData",
          component: () =>
            import("@/views/deviceMan/technicalDataMan/viewTechnicalData.vue"),
          name: "viewTechnicalData",
          meta: {
            title: "技术资料查看",
            roles: ["equ_jishuziliaochakan_jyt"],
          },
          children: [],
        },
        {
          path: "manageTechnicalData",
          component: () =>
            import(
              "@/views/deviceMan/technicalDataMan/manageTechnicalData.vue"
            ),
          name: "manageTechnicalData",
          meta: {
            title: "技术资料管理",
            roles: ["equ_jishuziliaoguanli_jyt"],
          },
          children: [],
        },
      ],
    },
  ],
};

export default examRouter;
