<template>
  <!-- 參數設置  類型設置 -->
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick" class='tab-container'>
      <el-tab-pane v-for='(item, index) of navListSync' :key='index' :label="item.roleText" :name="index + ''">
        <components v-if='activeName == index' :is="testTemplate"></components>
      </el-tab-pane>
    </el-tabs>
    <router-view />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

//页面组件
import accompanyConfig from './accompanyConfig'   //参数设置
import typeConfig from './typeConfig'             //类型设置

export default {
  components: { accompanyConfig, typeConfig },

  data() {
    return {
      // 动态导航列表
      navList: [
        { roleId: 'peizhenpeijianshezhi', roleText: '参数设置', template: typeConfig },
        { roleId: 'peizhenpeijianshezhi', roleText: '系统设置', template: accompanyConfig },
      ],
      navListSync: [],

      activeName: '0',
      testTemplate: '',
    }
  },

  created() {
    // 当前用户的权限
    let userRoles = JSON.parse(decodeURI(window.localStorage.userRoles))

    for (let navItem of this.navList) {
      for (let item of userRoles) {
        if (item == navItem.roleId) {
          this.navListSync.push(navItem)
        }
      }
    }
  },

  mounted() {
    // 缓存tab index顺序
    let tabNavIndex = 0
    if (window.localStorage.tabNavIndex) {
      tabNavIndex = parseInt(window.localStorage.tabNavIndex)
    }
    this.activeName = tabNavIndex + ''
    if (this.navListSync.length > 0) {
      this.testTemplate = this.navListSync[tabNavIndex].template
    }
  },
  beforeDestroy() {
    window.localStorage.tabNavIndex = 0
  },

  methods: {
    handleClick(tab, event) {
      this.testTemplate = this.navListSync[tab.index].template
      window.localStorage.tabNavIndex = tab.index
    }
  },
}

</script>

<style rel="stylesheet/scss" lang="scss">
</style>