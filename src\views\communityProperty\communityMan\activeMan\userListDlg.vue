<template>
  <!-- 弹窗 新增/编辑 -->
  <!-- :title="dlgType === 'add' ? '添 加' : '修 改'" -->
  <el-dialog
    :title="`${dlgQuery.name}-活动参与详情`"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="1400px"
    top="30px"
  >
    <div class="gzb-box">
      <div class="gzb-top">
        <el-input
          @keyup.enter.native="searchFunc"
          class="m-shaixuan-input fl"
          placeholder="关键字"
          v-model="listQuery.text"
          style="width: 200px"
        >
          <i @click="resetSearchItem(['text'])" slot="suffix" class="el-input__icon el-icon-error"></i>
        </el-input>

        <el-select class="fl ml10" style="width: 130px" v-model="listQuery.status" clearable placeholder="请选择">
          <el-option label="未领取商品" value="3"> </el-option>
          <el-option label="已领取商品" value="4"> </el-option>
        </el-select>

        <el-button icon="el-icon-search" type="primary" size="mini" class="search-right-btn fl" @click="getList"
          >搜索</el-button
        >
        <el-button class="search-right-btn fl" @click="downLoadFunc" icon="el-icon-download" size="mini"
          >导出</el-button
        >

        <div class="clear"></div>
      </div>
      <!-- class='m-small-table' -->
      <el-table
        v-loading="listLoading"
        height="calc(100vh - 300px)"
        class="m-small-table mt10"
        ref="tableRef"
        :data="list"
        border
        fit
        highlight-current-row
        @sort-change="sortChange"
      >
        <el-table-column label="#" align="center" fixed="left" width="60">
          <template slot-scope="scope">
            {{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column label="业主姓名" prop="name" width="80" align="center"></el-table-column>
        <el-table-column label="业主电话" prop="phone" width="140" align="center"></el-table-column>
        <el-table-column label="身份证" prop="idCard" width="150" align="center"></el-table-column>
        <el-table-column label="住址" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.floorName }}{{ scope.row.unitName }}{{ scope.row.roomName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="领取商品" prop="goodsName"></el-table-column>
        <el-table-column label="领取时间" prop="receiveTime" width="140" align="center"></el-table-column>
        <el-table-column label="状态" width="100px" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '3'" type="warning">未领取</el-tag>
            <el-tag v-else type="success">已领取</el-tag>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        class="mt10"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.size"
        @pagination="getList"
      />
      <div class="clear"></div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
import Pagination from '@/components/Pagination' // 分页

// 工具
// import { phoneReg } from '@/utils/regUtil'
import * as utils from '@/utils'
import { postAction, getAction } from '@/api'

// 接口
import Bmtree from '@/components/Dialog/Bmtree'

let listQueryEmpty = {
  text: '', //	模糊查询
  page: 1,
  size: 20,
  status: '',
}
export default {
  components: {
    Pagination,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        console.log('val', val)
        setTimeout(() => {
          this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
          this.searchFunc()
        }, 50)
      } else {
        this.total = 0
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      // 弹窗
      dlgTitle: '',
      dlgType1: '',
      dlgType2: '',

      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        tableId: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      dlgSubLoading: false, // 提交loading
      // 其他数据 下拉框
      // 3:已参加活动未领取商品 4 已参加活动已领取商品
      stateSelect: [
        { id: '3', name: '未领取' },
        { id: '4', name: '已领取' },
      ], // 关联表单

      // 列表
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
    }
  },
  created() {},
  methods: {
    downLoadFunc() {
      let sendObj = {
        label: this.listQuery.text, //	模糊查找	body	false	string
        activityId: this.dlgQuery.id, //	活动id	body	false	string
        communityId: this.dlgQuery.communityId, //	小区id	body	false	string
        status: this.listQuery.status, //	3:已参加活动未领取商品 4 已参加活动已领取商品
      }
      postAction('/unity/activity/activityOwnerUserExport', sendObj).then((res0) => {
        window.open(res0.data.data.url)
      })
    },
    // -- << 列表
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.list = []
      this.listLoading = true

      let sendObj = {
        label: this.listQuery.text, //	模糊查找	body	false	string
        page: this.listQuery.page, //		body	false	int32
        limit: this.listQuery.size, //		body	false	int32
        activityId: this.dlgQuery.id, //	活动id	body	false	string
        communityId: this.dlgQuery.communityId, //	小区id	body	false	string

        status: this.listQuery.status, //	3:已参加活动未领取商品 4 已参加活动已领取商品

        // activityStatus:'',//	0:进行中 1:已结束	body	false	string
        // openId:'',//	openId	body	false	string
      }

      postAction(`/unity/activity/activityOwnerUserPage`, sendObj).then((res0) => {
        let res = res0.data
        this.listLoading = false
        if (res.code == 200) {
          this.list = res.data
          this.total = res.page.total

          this.$nextTick(() => {
            this.$refs.tableRef.doLayout()
          })
        } else {
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },

    // 搜索框 清空单个条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },
    // 排序
    sortChange(data) {
      let type = data.column.sortBy
      let order = data.order
      if (order == null) {
        type = ''
        order = ''
      } else {
        if (order == 'descending') {
          order = 'desc'
        } else {
          order = 'asc'
        }
      }
      this.listQuery.sortParam = type
      this.listQuery.sortOrder = order
      this.getList()
    },
    // -- >> 列表

    closeDlg() {
      this.dlgLoading = false
      this.dlgSubLoading = false
      // this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')
    },

    // 更新外层列表
    upList1() {
      this.$emit('getList')
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.gzb-box {
  padding: 0 20px;
  .gzb-top,
  .gzb-bottom {
    // margin-bottom: 10px;
    p {
      margin: 0px;
      display: inline-block;
      margin-right: 40px;
    }
  }
  .gzb-bottom {
    margin-top: 10px;
    p {
      margin-right: 20px;
    }
  }
}
</style>