<template>
  <!--病床管理-->
  <div class="app-container">
    <div class="filter-container">
      <el-form ref="searchForm" class='clearfix' label-width="90px" @submit.native.prevent>
        <div class='fr'>
          <el-input @focus="showBranchDlg()" v-model="listQuery.branchName" readonly placeholder="请选择部门"></el-input>
          <el-input @focus="showWardDlg()" v-model="listQuery.wardName" readonly placeholder="请选择病房"></el-input>
          <el-input v-model="listQuery.str" placeholder='请输入床号'>
            <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">
            搜索
          </el-button>
          <el-button icon='el-icon-refresh' type="primary" size='mini' @click="resetItem">
            重置
          </el-button>
          <el-button icon='el-icon-plus' type="primary" size='mini' @click="addItem">
            新增
          </el-button>
        </div>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="床号">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="病房名称">
          <template slot-scope="scope">
            <span>{{ scope.row.wardName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="科室">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="180">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="editItem(scope.row, scope.$index)" plain>
              编辑
            </el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="delItem(scope.row, scope.$index)" plain>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="dlgTitle" :visible.sync="dlgShow">
      <el-form ref="dlgForm" :rules="dlgRules" :model="dlgData" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属科室" prop="'branchName'">
              <el-input @focus="showBranchDlg()" v-model="dlgData.branchName" readonly placeholder="请选择所属科室"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病房名称" prop="wardName">
              <el-input @focus="showWardDlg()" v-model="dlgData.wardName" readonly placeholder="请选择病房名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="床号" prop="name">
              <el-input v-model="dlgData.name" placeholder="请填写床号"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-back" @click="dlgShow = false">取 消</el-button>
        <el-button icon="el-icon-check" :disabled="!subEnable" type="success" @click="subDlg()">保 存</el-button>
      </div>
    </el-dialog>

    <wardDlg />
    <branchDlg />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import wardDlg from '@/components/Dialog/clothingMan/wardDlg'
import branchDlg from '@/components/Dialog/platformMan/branchDlg'

import {
  findWardDynamic
} from '@/api/medicalMatchManSystem/clothingMan/wardMan'
import {
  saveOrUSickBed,
  updateSickBed,
  findSickBedDynamic
} from '@/api/medicalMatchManSystem/clothingMan/sickBedMan'


export default {
  components: {
    Pagination,
    branchDlg,
    wardDlg
  },
  data () {
    return {
      dlgRules: {
        name: [
          { required: true, message: '床号必填', trigger: 'blur' },
          { min: 1, max: 18, message: '床号最多18个字符', trigger: 'blur' }
        ],
        wardName: [{ required: true, message: '病房名称必填', trigger: 'change' }],
        branchName: [{ required: true, message: '所属科室必填', trigger: 'change' }]
      },

      // 主表格相关
      list: [],
      listQuery: {
        page: 1,
        size: 20,
        str: '',
        branchId: '',
        branchName: '',
        wardId: '',
        wardName: ''
      },
      total: 0,
      listLoading: false,
      subEnable: true,
      dlgShow: false,
      dlgTitle: '',
      dlgType: '',
      dlgData: {
        name: '',
        branchId: '',
        branchName: '',
        wardId: '',
        wardName: ''
      },
    }
  },

  computed: {
    ...mapGetters('platformMan/branchDlg', {
      branchId: 'branchId',
      branchName: 'branchName'
    }),
    ...mapGetters('clothingMan/wardDlg', {
      wardId: 'wardId',
      wardName: 'wardName'
    }),
  },

  watch: {
    branchId (val) {
      if (this.dlgShow) {
        if (this.dlgData.branchId != val) {
          this.dlgData.wardId = ""
        }
        this.dlgData.branchId = val
      }
      else {
        this.listQuery.branchId = val
      }
    },
    branchName (val) {
      if (this.dlgShow) {
        if (this.dlgData.branchName != val) {
          this.dlgData.wardName = ""
        }
        this.dlgData.branchName = val
      }
      else {
        this.listQuery.branchName = val
      }
    },
    wardId (val) {
      this.dlgShow ? this.dlgData.wardId = val : this.listQuery.wardId = val
    },
    wardName (val) {
      this.dlgShow ? this.dlgData.wardName = val : this.listQuery.wardName = val
    }
  },

  created () {
    this.getList()
  },

  methods: {
    // 显示部门树
    showBranchDlg () {
      let branchId = this.dlgShow ? this.dlgData.branchId : this.listQuery.branchId
      let branchName = this.dlgShow ? this.dlgData.branchName : this.listQuery.branchName

      this.$store.commit('platformMan/branchDlg/SET_BRANCHID', branchId)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', branchName)
      this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', true)
    },

    // 显示病房列表
    showWardDlg () {
      if (this.dlgShow && utils.isNull(this.dlgData.branchId)) {
        this.$message.error("请先选择科室")
        return
      }
      let branchId = this.dlgShow ? this.dlgData.branchId : this.listQuery.branchId
      let wardId = this.dlgShow ? this.dlgData.wardId : this.listQuery.wardId
      let wardName = this.dlgShow ? this.dlgData.wardName : this.listQuery.wardName

      this.$store.commit('clothingMan/wardDlg/SET_BRANCHID', branchId)
      this.$store.commit('clothingMan/wardDlg/SET_WARDID', wardId)
      this.$store.commit('clothingMan/wardDlg/SET_WARDNAME', wardName)
      this.$store.commit('clothingMan/wardDlg/SET_DLGSHOW', true)
    },

    // 主功能相关
    getList () {
      this.list = []
      this.listLoading = true
      findSickBedDynamic(this.listQuery).then(res => {
        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    resetStr () {
      this.listQuery.str = ''
      this.getList()
    },

    resetItem () {
      this.listQuery.str = ''
      this.listQuery.branchId = ''
      this.listQuery.branchName = ''
      this.listQuery.wardId = ''
      this.listQuery.wardName = ''
      this.getList()
    },

    searchItem () {
      this.getList()
    },

    addItem () {
      this.dlgData = {
        name: '',
        branchId: this.listQuery.branchId,
        branchName: this.listQuery.branchName,
        wardId: '',
        wardName: ''
      }
      this.dlgShow = true
      this.dlgType = 'add'
      this.dlgTitle = '新增病房信息'
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    editItem (data, idx) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgShow = true
      this.dlgType = 'edit'
      this.dlgTitle = '编辑病房信息'
    },

    delItem (data, idx) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let postParam = {
            id: data.id
          }
          updateSickBed(postParam).then(res => {
            let code = res.data.code
            let msg = res.data.msg
            if (code === '200') {
              this.$message({
                type: 'success',
                message: msg
              })
              this.getList()
            } else {
              this.$message.error(msg)
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    subDlg () {
      this.$refs['dlgForm'].validate(valid => {
        if (valid) {
          this.subEnable = false
          let postParam = this.dlgData
          if (this.dlgType === 'add') {
            postParam['id'] = '0'
          } else {
            postParam['id'] = this.dlgData.id
          }
          saveOrUSickBed(postParam).then(res => {
            this.subEnable = true
            let code = res.data.code
            let msg = res.data.msg
            if (code == 200) {
              this.$message({
                type: 'success',
                message: msg
              })
              this.dlgShow = false
              this.getList()
            } else {
              this.$message.error(msg)
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>