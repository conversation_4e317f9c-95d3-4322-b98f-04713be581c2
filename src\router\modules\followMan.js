/** 随访管理 **/

import Layout from "@/views/layout/Layout";

const followManRouter = {
  path: "/followMan",
  component: Layout,
  name: "follow<PERSON>an",
  meta: {
    title: "随访管理",
    icon: "sfgl",
    roles: ["suif<PERSON><PERSON>n<PERSON>"]
  },
  children: [
    {
      path: "basicInformation",
      component: () => import("@/views/followupMan/basicInformation/index"),
      meta: {
        title: "基础信息维护",
        roles: ["jichuxinxiweihu"]
      },
      children: [
        {
          path: "patientInformation",
          component: () =>
            import("@/views/followupMan/basicInformation/patientInformation"),
          name: "就诊信息",
          meta: {
            title: "就诊信息",
            roles: ["huanzhexinxi"]
          }
        },
        {
          path: "diseaseInformation",
          component: () =>
            import("@/views/followupMan/basicInformation/diseaseInformation"),
          name: "病种信息",
          meta: {
            title: "病种信息",
            roles: ["bingzhongxinxi"]
          }
        },
        {
          path: "illnessInformation",
          component: () =>
            import("@/views/followupMan/basicInformation/illnessInformation"),
          name: "疾病信息",
          meta: {
            title: "疾病信息",
            roles: ["jibingxinxi"]
          }
        },
        {
          path: "drugInformation",
          component: () =>
            import("@/views/followupMan/basicInformation/drugInformation"),
          name: "药品信息",
          meta: {
            title: "药品信息",
            roles: ["yaopinxinxi"]
          }
        }
      ]
    },
    {
      path: "healthPromotion",
      component: () => import("@/views/followupMan/healthPromotion/index"),
      meta: {
        title: "健康宣传维护",
        roles: ["jiankangxuanjiaoweihu"]
      },
      children: [
        {
          path: "healthPromotion",
          component: () =>
            import("@/views/followupMan/healthPromotion/healthPromotion"),
          name: "健康宣传维护",
          meta: {
            title: "健康宣传维护",
            roles: ["jkjiankangxuanjiaoweihu"]
          },
          children: []
        }
      ]
    },
    {
      path: "statistical",
      component: () => import("@/views/followupMan/statistical/index"),
      meta: {
        title: "统计查询",
        roles: ["tongjichaxun"]
      },
      children: [
        {
          path: "satisfactionQuery",
          component: () =>
            import("@/views/followupMan/statistical/satisfactionQuery"),
          name: "满意度调查统计",
          meta: {
            title: "满意度调查统计",
            roles: ["manyidudiaochatongji"]
          },
          children: []
        },
        {
          path: "followupWork",
          component: () =>
            import("@/views/followupMan/statistical/followupWork"),
          name: "随访工作统计",
          meta: {
            title: "随访工作统计",
            roles: ["suifanggongzuotongji"]
          },
          children: []
        },
        {
          path: "followupRecord",
          component: () =>
            import("@/views/followupMan/statistical/followupRecord"),
          name: "随访记录统计",
          meta: {
            title: "随访记录统计",
            roles: ["suifangjilutongji"]
          },
          children: []
        }
      ]
    },
    {
      path: "followupCenter",
      component: () => import("@/views/followupMan/followupCenter/index"),
      meta: {
        title: "中心随访",
        roles: ["zhongxinsuifang"]
      },
      children: [
        {
          path: "surveyFileSetting",
          component: () =>
            import("@/views/followupMan/followupCenter/surveyFileSetting"),
          name: "调查问卷设置",
          meta: {
            title: "调查问卷设置",
            roles: ["diaochawenjuanshezhi"]
          },
          children: []
        },
        {
          path: "satisfactionSurvey",
          component: () =>
            import("@/views/followupMan/followupCenter/satisfactionSurvey"),
          name: "满意度调查",
          meta: {
            title: "满意度调查",
            roles: ["manyidudiaocha"]
          },
          children: []
        },
        {
          path: "followupMan",
          component: () =>
            import("@/views/followupMan/followupCenter/followupManagement"),
          name: "中心随访",
          meta: {
            title: "中心随访",
            roles: ["zxzhongxinsuifang"]
          },
          children: []
        },
        {
          path: "appointReminder",
          component: () =>
            import("@/views/followupMan/followupCenter/appointReminder"),
          name: "复诊提醒",
          meta: {
            title: "复诊提醒",
            roles: ["fuzhentixing"]
          },
          children: []
        }
      ]
    },
    {
      path: "followupDept",
      component: () => import("@/views/followupMan/followupDept/index"),
      meta: {
        title: "科室随访",
        roles: ["keshisuifang"]
      },
      children: [
        {
          path: "surveyFileSetting",
          component: () =>
            import("@/views/followupMan/followupDept/surveyFileSetting"),
          name: "调查问卷设置",
          meta: {
            title: "调查问卷设置",
            roles: ["ksdiaochawenjuanshezhi"]
          },
          children: []
        },
        {
          path: "followupContentTmpl",
          component: () =>
            import("@/views/followupMan/followupDept/followupContentTmpl"),
          name: "随访内容模板",
          meta: {
            title: "随访内容模板",
            roles: ["kssuifangneirongmoban"]
          },
          children: []
        },
        {
          path: "followupConclusionTmpl",
          component: () =>
            import("@/views/followupMan/followupDept/followupConclusionTmpl"),
          name: "随访结论模板",
          meta: {
            title: "随访结论模板",
            roles: ["kssuifangjielunmoban"]
          },
          children: []
        },
        {
          path: "doctorAdvicemaintain",
          component: () =>
            import("@/views/followupMan/followupDept/doctorAdvicemaintain"),
          name: "医嘱维护",
          meta: {
            title: "医嘱维护",
            roles: ["ksyizhuweihu"]
          },
          children: []
        },
        {
          path: "followupPlanSetting",
          component: () =>
            import("@/views/followupMan/followupDept/followupPlanSetting"),
          name: "随访计划设置",
          meta: {
            title: "随访计划设置",
            roles: ["kssuifangjihuashezhi"]
          },
          children: []
        },
        {
          path: "followupMan",
          component: () =>
            import("@/views/followupMan/followupDept/followupManagement"),
          name: "随访管理",
          meta: {
            title: "随访管理",
            roles: ["kssuifangguanli"]
          },
          children: []
        }
      ]
    },
    {
      path: "dynamicForm",
      component: () => import("@/views/followupMan/dynamicForm/index"),
      meta: {
        title: "动态表单维护",
        roles: ["jiankangxuanjiaoweihu"]
      },
      children: [
        {
          path: "dynamicForm",
          component: () =>
            import("@/views/followupMan/dynamicForm/dynamicForm"),
          name: "动态表单维护",
          meta: {
            title: "动态表单维护",
            roles: ["jkjiankangxuanjiaoweihu"]
          },
          children: []
        }
      ]
    },
    {
      path: "tagMaintain",
      component: () => import("@/views/followupMan/tagMaintain/index"),
      meta: {
        title: "标签维护",
        roles: ["jiankangxuanjiaoweihu"]
      },
      children: [
        {
          path: "tagMaintain",
          component: () =>
            import("@/views/followupMan/tagMaintain/tagMaintain"),
          name: "标签维护",
          meta: {
            title: "标签维护",
            roles: ["jkjiankangxuanjiaoweihu"]
          },
          children: []
        },
        {
          path: "tagTypeMaintain",
          component: () =>
            import("@/views/followupMan/tagMaintain/tagTypeMaintain"),
          name: "标签类别维护",
          meta: {
            title: "标签类别维护",
            roles: ["jkjiankangxuanjiaoweihu"]
          },
          children: []
        }
      ]
    }
  ]
};

export default followManRouter;
