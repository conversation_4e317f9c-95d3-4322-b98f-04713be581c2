import request from '@/utils/request'

/*
*用户管理相关
*/

// 查询运送配置列表 
export function findTraConfigLike(data) {
	return request({
		url: `/sys/findTraConfigLike`,
		method: 'post',
		data
	})
}
// 新增或者更新运送配置
export function saveOrUTraConfig(data) {
	return request({
		url: `/sys/saveOrUTraConfig`,
		method: 'post',
		data
	})
}

// 删除角色
export function delTraConfig(data) {
	return request({
		url: `/sys/delTraConfig`,
		method: 'post',
		data
	})
}

// 查询配置关联部门
export function findBranchByConfidId(data) {
	return request({
		url: `/sys/findBranchByConfidId`,
		method: 'post',
		data
	})
}

// 查询运送类型配置列表
export function findTaskTypeConfigByFlag(data) {
	return request({
		url: `/tra/findTaskTypeConfigByFlag`,
		method: 'post',
		data
	})
}

// 修改运送类型配置
export function updateTaskTypeConfig(data) {
	return request({
		url: `/tra/updateTaskTypeConfig`,
		method: 'post',
		data
	})
}




