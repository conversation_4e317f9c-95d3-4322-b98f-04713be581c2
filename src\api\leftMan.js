// 电梯管理相关接口
import request from "@/utils/request";

// << 电梯配置管理
//查询电梯管理列表
export function getLiftPage(data) {
  return request({
    url: "/iot/pageLift",
    method: "post",
    data
  });
}
//新增电梯管理
export function createLift(data) {
  return request({
    url: "/iot/addLift",
    method: "post",
    data
  });
}
//修改电梯管理
export function updateLift(data) {
  return request({
    url: "/iot/updateLift",
    method: "post",
    data
  });
}
//查询电梯管理详情
export function getLift(id) {
  return request({
    url: `/iot/getLiftById/${id}`,
    method: "get"
  });
}
//删除电梯管理
export function deleteLift(id) {
  return request({
    url: `/iot/deleteLift/${id}`,
    method: "get"
  });
}


// << 摄像头档案管理
//查询摄像头档案列表
export function getCameraArchivesPage(data) {
  return request({
    url: "/iot/pageCameraArchive",
    method: "post",
    data
  });
}
//新增摄像头档案
export function createCameraArchives(data) {
  return request({
    url: "/iot/addCameraArchive",
    method: "post",
    data
  });
}
//查询摄像头档案详情
export function getCameraArchives(id) {
  return request({
    url: `/iot/getCameraArchive/${id}`,
    method: "get"
  });
}
//删除摄像头档案
export function deleteCameraArchives(id) {
  return request({
    url: `/iot/deleteCameraArchive/${id}`,
    method: "get"
  });
}


// << 摄像头管理
//查询摄像头列表
export function pageFireCamera(data) {
  return request({
    url: "/iot/pageFireCamera",
    method: "post",
    data
  });
}
//新增摄像头
export function addFireCamera(data) {
  return request({
    url: "/iot/addFireCamera",
    method: "post",
    data
  });
}
//重置密码
export function resetPasswordFireCamera(data) {
  return request({
    url: '/iot/resetPasswordFireCamera',
    method: 'post',
    data
  })
}
//校验密码
export function checkPassword(data) {
  return request({
    url: '/iot/checkPassword',
    method: 'post',
    data
  })
}
// 删除摄像头
export function deleteFireCamera(id) {
  return request({
    url: `/iot/deleteFireCamera/${id}`,
    method: 'get'
  })
}
// 本地录像
export function recordhistories(data) {
  return request({
    url: `/iot/recordhistories`,
    method: 'post',
    data
  })
}

// << 报警类型管理
//查询报警类型列表
export function getAlartTypePage(data) {
  return request({
    url: "/iot/pageAlartType",
    method: "post",
    data
  });
}
//新增报警类型
export function createAlartType(data) {
  return request({
    url: "/iot/addAlartType",
    method: "post",
    data
  });
}
//修改报警类型
export function updateAlartType(data) {
  return request({
    url: "/iot/updateAlartType",
    method: "post",
    data
  });
}
//查询报警类型详情
export function getAlartType(id) {
  return request({
    url: `/iot/getAlartType/${id}`,
    method: "get"
  });
}
//删除报警类型
export function deleteAlartType(id) {
  return request({
    url: `/iot/deleteAlartType/${id}`,
    method: "get"
  });
}

// << 报警规则
//查询报警规则列表
export function getSendMsgConfigPage(data) {
  return request({
    url: "/iot/sendMsgConfig/page",
    method: "post",
    data
  });
}
//新增报警规则
export function createSendMsgConfig(data) {
  return request({
    url: "/iot/sendMsgConfig/addOrUpdate",
    method: "post",
    data
  });
}
//查询报警类型详情
export function getSendMsgConfig(id) {
  return request({
    url: `/iot/sendMsgConfig/get/${id}`,
    method: "get"
  });
}
//删除报警类型
export function deleteSendMsgConfig(id) {
  return request({
    url: `/iot/sendMsgConfig/del/${id}`,
    method: "get"
  });
}

// << 报警配置
//查询报警配置列表
export function pageAlartTypeConfig(data) {
  return request({
    url: "/iot/pageAlartTypeConfig",
    method: "post",
    data
  });
}
//新增报警配置
export function addAlartTypeConfig(data) {
  return request({
    url: "/iot/addAlartTypeConfig",
    method: "post",
    data
  });
}
//删除报警配置
export function deleteAlartTypeCofig(id) {
  return request({
    url: `/iot/deleteAlartTypeCofig/${id}`,
    method: "get"
  });
}
//查询报警配置详情
export function getAlartTypeConfig(id) {
  return request({
    url: `/iot/getAlartTypeConfig/${id}`,
    method: "get"
  });
}

// << 报警推送记录
//电话报警记录
export function phoneSendLog(data) {
  return request({
    url: "/iot/sendMsgConfig/phonePage",
    method: "post",
    data
  });
}
export function sendMsgConfigExport(data) {
  return request({
    url: "/iot/sendMsgConfig/export",
    method: "post",
    data
  });
}
//短信报警记录
export function smsSendLog(data) {
  return request({
    url: "/iot/sendMsgConfig/smsPage",
    method: "post",
    data
  });
}
// << 报警分析
export function alarmStatistic(data) {
  return request({
    url: `/iot/log/alarmStatistic`,
    method: "post",
    data
  });
}
// << 报警管理
export function alarmPage(data) {
  return request({
    url: `/iot/log/alarmPage`,
    method: "post",
    data
  });
}
//详情
export function alarmInfo(id) {
  return request({
    url: `/iot/log/alarmInfo/${id}`,
    method: "get"
  });
}
//处理
export function alarmHandle(data) {
  return request({
    url: `/iot/log/alarmHandle`,
    method: "post",
    data
  });
}
// << 实时监控
//电梯监控数量
export function liftCameraCount(id) {
  return request({
    url: `/iot/log/liftCameraCount/${id}`,
    method: "get"
  });
}
//电梯摄像头运行警报日志
export function liftCameraAlarms(id) {
  return request({
    url: `/iot/log/liftCameraAlarms/${id}`,
    method: "get"
  });
}
//电梯运行日志
export function liftRunningLogs(id) {
  return request({
    url: `/iot/log/liftRunningLogs/${id}`,
    method: "get"
  });
}
//获取报事点位树
export function findAreaTree(projectId) {
  return request({
    url: `/report/area/findAreaTree/${projectId}`,
    method: "get"
  });
}
//绑定节点提交
export function liftBindAddressPoint(data) {
  return request({
    url: `/iot/liftBindAddressPoint`,
    method: 'post',
    data
  });
}
//摄像头绑定节点提交
export function cameraNodeBindPoint(data) {
  return request({
    url: `/iot/cameraNodeBindPoint `,
    method: 'post',
    data
  });
}

//节点查询
export function queryliftNodeBindPoint(id) {
  return request({
    url: `/iot/equip-node/queryliftNodeBindPoint?equipNodeId=${id}`,
    method: 'get',
  });
}