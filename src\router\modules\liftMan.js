/** 基础运营管理系统 **/

import Layout from "@/views/layout/Layout";

const liftManRouter = {
  path: "/liftMan",
  component: Layout,
  name: "liftMan",
  meta: {
    title: "电梯监控",
    icon: "dtjk",
    roles: ["diantijiankong"]
  },
  children: [
    // {
    //   path: "test",
    //   component: () => import("@/views/leftMan/test"),
    //   name: "大屏测试",
    //   meta: {
    //     title: "大屏测试",
    //     roles: ["shishijiankong"]
    //   }
    // },

    {
      path: "realTimeMonitoringScreen",
      component: () => import("@/views/leftMan/realTimeMonitoringScreen"),
      name: "实时监控大屏",
      meta: {
        title: "实时监控大屏",
        roles: ["shishijiankong"]
      }
    },
    
    {
      path: "realTimeMonitoring",
      component: () => import("@/views/leftMan/realTimeMonitoring"),
      name: "实时监控",
      meta: {
        title: "实时监控",
        roles: ["shishijiankong"]
      }
    },
    {
      path: "realTimeMonitoring3D",
      component: () => import("@/views/leftMan/realTimeMonitoring3D"),
      name: "实时监控3D",
      meta: {
        title: "实时监控3D",
        roles: ["shishijiankong3d"]
      }
    },
    {
      path: "alarmManagement",
      component: () => import("@/views/leftMan/alarmManagement"),
      name: "报警管理",
      meta: {
        title: "报警管理",
        // roles: ["baojingguanli"]
      }
    },
    {
      path: "alarmAnalysis",
      component: () => import("@/views/leftMan/alarmAnalysis"),
      name: "报警分析",
      meta: {
        title: "报警分析",
        roles: ["baojingfenxi"]
      }
    },
    {
      path: "sendlog",
      component: () => import("@/views/leftMan/sendlog/index"),
      meta: {
        title: "报警推送记录",
          roles: ["baojingtuisongjilu"]
      },
      children: [
        {
          path: "phoneSendLog",
          component: () => import("@/views/leftMan/sendlog/phoneSendLog"),
          name: "电话报警记录",
          meta: {
            title: "电话报警记录",
              roles: ["dianhuabaojingjilu"]
          },
          children: []
        },
        {
          path: "smsSendLog",
          component: () => import("@/views/leftMan/sendlog/smsSendLog"),
          name: "短信报警记录",
          meta: {
            title: "短信报警记录",
              roles: ["duanxinbaojingjilu"]
          },
          children: []
        }
      ]
    },
    {
      path: "config",
      component: () => import("@/views/leftMan/config/index"),
      meta: {
        title: "电梯配置管理",
          // roles: ["diantipeizhiguanli"]
      },
      children: [
        {
          path: "lift",
          component: () => import("@/views/leftMan/config/lift"),
          name: "电梯管理",
          meta: {
            title: "电梯管理",
              // roles: ["diantiguanli"]
          },
          children: []
        },
        {
          path: "cameraArchives",
          component: () => import("@/views/leftMan/config/cameraArchives"),
          name: "摄像头档案",
          meta: {
            title: "摄像头档案",
              roles: ["shexiangtoudangan"]
          },
          children: []
        },
        {
          path: "cameraMan",
          component: () => import("@/views/leftMan/config/cameraMan"),
          name: "摄像头管理",
          meta: {
            title: "摄像头管理",
              roles: ["shexiangtouguanli"]
          },
          children: []
        },
        {
          path: "alartType",
          component: () => import("@/views/leftMan/config/alartType"),
          name: "电梯报警类型",
          meta: {
            title: "电梯报警类型",
              roles: ["diantibaojingleixing"]
          },
          children: []
        },
        {
          path: "sendMsgConfig",
          component: () => import("@/views/leftMan/config/sendMsgConfig"),
          name: "电梯报警规则",
          meta: {
            title: "电梯报警规则",
              roles: ["diantibaojingguize"]
          },
          children: []
        },
        {
          path: "alartTypeConfig",
          component: () => import("@/views/leftMan/config/alartTypeConfig"),
          name: "电梯报警配置",
          meta: {
            title: "电梯报警配置",
              roles: ["diantibaojingpeizhi"]
          },
          children: []
        }
      ]
    }
  ]
};

export default liftManRouter;
