<template>
  <!-- 弹窗 岗位 -->
  <el-dialog
    :title="bmsType == 'baoShiKeMu' ? '选择报事科目' : '选择部门'"
    :close-on-click-modal="false"
    :append-to-body="true"
    :visible.sync="bmsState"
    width="900px"
    top="30px"
    icon-class="el-icon-info"
  >
    <div class="dialog-bms-bar">
      <!-- 部门 -->
      <div class="dialog-bms-left">
        <el-input placeholder="输入关键字进行过滤" style="margin-bottom: 10px" v-model="bmsText"> </el-input>
        <div class="bms-tree">
          <el-tree
            :expand-on-click-node="false"
            :data="bmsData"
            ref="bmsDom"
            :default-expanded-keys="defaultOpenList"
            :filter-node-method="bmsFilter"
            :props="defaultProps"
            node-key="id"
            @node-click="bmsClick"
          >
            <div class="custom-tree-node" slot-scope="{ data }">
              <span v-if="bmsType == 'baoShiKeMu'" :class="data.selected ? 'fsuccess' : ''">{{ data.name }}</span>
              <span v-else :class="data.selected ? 'fsuccess' : ''">{{ data.label }}</span>

              <span v-if="bmsType == 'baoShiKeMu' && data.type == 0" class="fdanger">(分类)</span>
              <span v-if="bmsType == 'baoShiKeMu' && data.type == 1" class="fsuccess">(科目)</span>
              <span v-if="bmsType == 'baoShiKeMu' && data.status == 0" class="fsuccess">(启用)</span>
              <span v-if="bmsType == 'baoShiKeMu' && data.status == 1" class="fdanger">(停用)</span>
            </div>
          </el-tree>
        </div>
      </div>
      <div class="dialog-bms-right">
        <div v-if="bmsType == 'baoShiKeMu'">
          <el-button
            v-for="(item, index) of bmsArr2"
            :key="index"
            @click="rightRemove(item, index)"
            class="bms-a"
            type="success"
            size="mini"
            plain
            >{{ item.name }}</el-button
          >
        </div>
        <div v-else>
          <el-button
            v-for="(item, index) of bmsArr2"
            :key="index"
            @click="rightRemove(item, index)"
            class="bms-a"
            type="success"
            size="mini"
            plain
            >{{ item.label }}</el-button
          >
        </div>
      </div>
      <!-- 部门多选框 -->
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog" icon="el-icon-back">取消</el-button>
      <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import { findTreePermission } from '@/api/dataDic'

import { findSubjectTree } from '@/api/reportMan'

export default {
  data() {
    return {
      isShow: false,
      bmsType: '',
      bmsOtherQuery: '',
      // 部门树
      bmsData: [],
      bmsSelect: {},
      bmsText: '', // 部门左侧筛选

      // 右侧选中的样式
      bmsIds: [],
      bmsNames: [],
      bmsArr2: [],
      defaultOpenList: [],
      // 树过滤
      defaultProps: {
        children: 'children',
        label: 'label',
      },

      diaLoading: false,
      rightSelectId: '', // 右侧选中的id
    }
  },
  computed: {
    bmsState: {
      get: function () {
        let state = this.$store.getters.bmsState
        if (state === false) {
          this.bmsArr2 = []
        }
        if (state) {
          setTimeout(() => {
            let bmsType = this.$store.getters.bmsType
            if (this.$store.getters.bmsArr == '') {
              this.bmsArr2 = []
            } else {
              this.bmsArr2 = JSON.parse(this.$store.getters.bmsArr)
            }
            this.bmsOtherQuery = this.$store.getters.bmsOtherQuery ? JSON.parse(this.$store.getters.bmsOtherQuery) : {}
            if (bmsType == 'baoShiKeMu') {
              this.getBskmTree()
            } else {
              this.findTreePermission()
            }
            this.bmsType = bmsType
          }, 200)
        }

        return state
      },
      set: function (newVal) {
        this.$store.commit('SET_BMSSTATE', newVal)
      },
    },
    // bmsArr: {
    //   get: function() {
    //     let bmsArr = this.$store.getters.bmsArr

    //     return bmsArr
    //   },
    //   set: function(newVal) {
    //   }
    // },
  },
  watch: {
    bmsText(val) {
      console.log(val)
      this.$refs.bmsDom.filter(val)
    },
    // bmsArr(val) {
    //   this.bmsArr2 = JSON.parse(val)
    // }
  },
  created() {},
  methods: {
    // 右侧关联删除
    rightRemove(row, index) {
      this.bmsArr2.splice(index, 1)

      this.rightSelectId = row.id
      this.selectAllNode(this.bmsData)
    },
    // 【【 左侧相关
    // 获取科目树
    getBskmTree() {
      let diaLoading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      console.log(this.bmsOtherQuery.projectId)

      // return false

      findSubjectTree(this.bmsOtherQuery.projectId).then((res) => {
        diaLoading.close()
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code == 200) {
          let listStr = JSON.stringify(res.data.data)
          let listStrNew = listStr.replace(/"id"/g, '"selected":false,"id"')
          this.bmsData = JSON.parse(listStrNew)
          if (this.bmsData.length > 0) {
            this.defaultOpenList = [this.bmsData[0]['id']]
          }
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 获取部门
    findTreePermission() {
      let diaLoading = this.$loading({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      findTreePermission().then((res) => {
        diaLoading.close()
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code == 200) {
          let listStr = JSON.stringify(res.data.list)
          let listStrNew = listStr.replace(/"id"/g, '"selected":false,"id"')
          this.bmsData = JSON.parse(listStrNew)
          if (this.bmsData.length > 0) {
            this.defaultOpenList = [this.bmsData[0]['id']]
          }
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 递归全选当前下的节点
    selectAllNode(childrenArr, type) {
      for (let item of childrenArr) {
        if (item.id == this.rightSelectId) {
          item.selected = false
          this.bmsData = JSON.parse(JSON.stringify(this.bmsData))
          console.log('找到了')
          return false
        }

        if (item.children) {
          this.selectAllNode(item.children, type)
        }
      }
    },
    // 节点左键点击事件
    bmsClick(data, node, mNode) {
      // 报事科目验证
      if (this.bmsType == 'baoShiKeMu') {
        if (data.type == 0) {
          this.$message({
            type: 'warning',
            message: '无法选择分类节点',
          })
          return false
        }
        if (data.status == 1) {
          this.$message({
            type: 'warning',
            message: '无法选择停用节点',
          })
          return false
        }
      }

      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      let mData = JSON.parse(JSON.stringify(data))
      mData.children = []

      let isHas = this.bmsArr2.every((item) => {
        return item.id !== mData.id
      })

      if (isHas) {
        data.selected = true
        this.bmsArr2.push(mData)
      } else {
        let bmsArr2 = JSON.parse(JSON.stringify(this.bmsArr2))
        data.selected = false
        for (let i = 0; i < bmsArr2.length; i++) {
          if (bmsArr2[i].id == data.id) {
            bmsArr2.splice(i, 1)
            break
          }
        }
        this.bmsArr2 = JSON.parse(JSON.stringify(bmsArr2))

        // this.$message({
        //   type: 'warning',
        //   message: '所选部门已在列表中，请勿重复添加。'
        // })
      }
    },
    // 筛选部门
    bmsFilter(value, data) {
      if (!value) return true
      if (this.bmsType == 'baoShiKeMu') {
        return data.name.indexOf(value) !== -1
      } else {
        return data.label.indexOf(value) !== -1
      }
    },
    // 】】 左侧相关

    // 【【 右侧相关
    // 右侧删除部门方法
    delBmFunc(id, relationName) {
      let nArr = this.bmsArr2.filter((item) => {
        return item.id !== id
      })
      this.bmsArr2 = JSON.parse(JSON.stringify(nArr))
    },
    // 】】 右侧相关

    // 【【 其他
    // 选择部门提交
    bumenOkFunc() {
      this.$store.commit('SET_BMSARR', JSON.stringify(this.bmsArr2))
      this.$store.commit('SET_BMS_TYPE', '')
      this.$store.commit('SET_BMS_OTHERQUERY', '')

      setTimeout(() => {
        this.$store.commit('SET_BMSARR', '')
      }, 200)

      this.closeDialog()
    },
    // 关闭弹窗
    closeDialog() {
      this.$store.commit('SET_BMSSTATE', false)
    },
    // 】】 其他

    // 筛选岗位
    // filterBmrightListOld
    // filterGangwei(val) {
    //   this.filterBmrightList = JSON.parse(JSON.stringify(this.filterBmrightListOld))
    //   this.filterBmrightList = this.filterBmrightList.filter(item => {
    //     return item.label.indexOf(val) >= 0
    //   })
    // },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dialog-bms-bar {
  display: flex;
  justify-content: space-between;
}
.dialog-bms-left,
.dialog-bms-right {
  width: 49%;
}
.bms-tree {
  height: 400px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
}
.dialog-bms-right {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 10px;
  .bms-a {
    margin-bottom: 10px;
    margin-left: 0px;
    margin-right: 6px;
  }
  i {
    display: inline-block;
    margin-left: 3px;
  }
}
</style>