<template>
  <!-- 小区管理 -》 业务受理 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form
        inline
        style="text-align: center"
        :model="listQuery"
        @submit.native.prevent
      >
        <el-form-item>
          <el-select v-model="listQuery.pageType" placeholder="房屋">
            <el-option
              v-for="item in pageTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="listQuery.roomName"
            placeholder="请选择房屋"
            @focus="showRoomDlg"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item v-if="curActive<4">
        <el-select class="fl ml10" clearable @change="searchFunc" v-model="listQuery.status" style="width: 160px" placeholder="缴费状态">
            <el-option label="已缴费" value="1"></el-option>
            <el-option label="未缴费" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="curActive<4">
          <el-select
             @change="searchFunc"
            v-model="listQuery.feeType"
            filterable
            clearable
            placeholder="请选择费用类型"
          >
            <el-option v-for="item in costTypeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchFunc"
          >查询</el-button
        >
        <el-button icon="el-icon-more-outline" type="primary" size="mini" @click="batchPayment" v-if="curActive < 4">缴费</el-button>
      </el-form>
    </div>
    <el-form>
      <el-row>
        <el-col :span="4">
          <el-image
            fit="cover"
            :src="listQuery.ownerInfo.photo"
            :preview-src-list="[listQuery.ownerInfo.photo]"
          >
            <div class="el-image__error" slot="error">暂无照片</div>
          </el-image>
        </el-col>
        <el-col :span="20">
          <el-col :span="6">
            <el-form-item label="业主编号：">
              {{ listQuery.ownerInfo.id }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="业主名称：">
              {{ listQuery.ownerInfo.name }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系电话：">
              {{ listQuery.ownerInfo.phone }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="身份证：">
              {{ listQuery.ownerInfo.idCard }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="入住日期：">
              {{ listQuery.ownerInfo.bindTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="性别：">
              {{ listQuery.ownerInfo.sex }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业主备注：">
              {{ listQuery.ownerInfo.remark }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所在小区：">
              {{ listQuery.roomInfo.communityName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="房屋编号：">
              {{ listQuery.roomInfo.roomFullName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="房屋面积：">
              {{ listQuery.roomInfo.builtUpArea }}
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="算费系数：">
              {{listQuery.roomInfo.feeCoefficient}}
            </el-form-item>
          </el-col> -->
          <el-col :span="6">
            <el-form-item label="户型：">
              {{ listQuery.roomInfo.sectionName }}
              {{ listQuery.roomInfo.apartmentName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="房屋状态：">
              <el-tag type="success" v-if="listQuery.roomInfo.state == 1"
                >已入住</el-tag
              >
              <el-tag type="warning" v-if="listQuery.roomInfo.state == 0"
                >未入住</el-tag
              >
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="房屋ID：">
              {{ listQuery.roomInfo.id }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="房屋备注：" label-width="90px">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="listQuery.roomInfo.remark"
                readonly
                resize="none"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-col>
      </el-row>
    </el-form>
    <el-tabs v-model="curActive" type="card" @tab-click="handleClick">
      <el-tab-pane label="房屋费用" name="1"></el-tab-pane>
      <el-tab-pane label="车位费用" name="2"></el-tab-pane>
      <el-tab-pane label="车库费用" name="3"></el-tab-pane>
      <el-tab-pane label="家庭成员" name="4"></el-tab-pane>
      <el-tab-pane label="报修单" name="5"></el-tab-pane>
      <el-tab-pane label="投诉单" name="6"></el-tab-pane>
    </el-tabs>
    <template v-if="curActive < 4">
      <div class="table-container">
        <el-table
          class="m-small-table"
          height="100%"
          v-loading="listLoading"
          :data="payList"
          border
          fit
          highlight-current-row
          ref="multipleTable"
          :key="tableKey" :row-key="getRowKeys" @row-click="tableRowClick" @selection-change="selectionChange"
        >
        <el-table-column :selectable="checkInit" align="center" :reserve-selection="true" type="selection" width="55"
          > </el-table-column>
          <el-table-column label="费用类型">
            <template slot-scope="scope">
              <span>{{ scope.row.feeName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用项目">
            <template slot-scope="scope">
              <span>{{ scope.row.configName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用标识">
            <template slot-scope="scope">
              <span>{{ scope.row.feeFlagName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="应收金额">
            <template slot-scope="scope">
              <span>{{ scope.row.receivableAmount }}</span>
            </template>
          </el-table-column>

          <el-table-column label="建账时间">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime }}</span>
            </template>
          </el-table-column>

          <el-table-column label="计费开始时间">
            <template slot-scope="scope">
              <span>{{ scope.row.startDate }}</span>
            </template>
          </el-table-column>

          <el-table-column label="计费结束时间">
            <template slot-scope="scope">
              <span>{{ scope.row.endDate }}</span>
            </template>
          </el-table-column>

          <el-table-column label="说明" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <template v-if="scope.row.remark">
                <div v-for="(item, index) in scope.row.remark.split(',')">
                  {{ item }}
                </div>
              </template>
            </template>
          </el-table-column>

          <el-table-column label="状态" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status == 0" type="danger">未缴费</el-tag>
              <el-tag v-if="scope.row.status == 1" type="success"
                >已缴费</el-tag
              >
            </template>
          </el-table-column>

          <el-table-column label="缴费时间" prop="payTime" width="160" align="center"></el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="320"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <!-- <el-button
                v-if="scope.row.status == 0"
                type="primary"
                size="mini"
                icon="el-icon-edit"
                plain
                @click="editItem(scope.row, 'EDIT')"
                >缴费</el-button
              > -->
              <el-button
                type="success"
                size="mini"
                icon="el-icon-view"
                plain
                @click="editItem(scope.row, 'VIEW')"
                >缴费历史</el-button
              >
              <el-button
                v-if="scope.row.status == 0"
                type="danger"
                size="mini"
                icon="el-icon-back"
                plain
                @click="delItem(scope.row)"
                >取消费用</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="page-container">
        <pagination
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          @pagination="getPayList"
        />
      </div>

      <el-dialog
        :close-on-click-modal="false"
        :title="dlgType == 'EDIT' ? '缴费' : '缴费历史'"
        :visible.sync="dlgShow"
        width="1200px"
        append-to-body
      >
        <el-form
          ref="dlgForm"
          :disabled="dlgType == 'VIEW'"
          :rules="rules"
          :model="dlgData"
          label-position="right"
          label-width="135px"
        >
          <el-divider v-if="dlgType == 'VIEW'">费用信息</el-divider>
          <el-row>
            <el-col :span="8">
              <el-form-item label="费用ID：">{{ dlgData.id }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="费用标识：">{{
                dlgData.feeFlagName
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="费用类型：">{{
                dlgData.feeName
              }}</el-form-item>
            </el-col>
          </el-row>
          <el-col :span="8">
            <el-form-item label="付费对象：">{{
              dlgData.payName
            }}</el-form-item>
          </el-col>
          <el-row>
            <el-col :span="8">
              <el-form-item label="费用项：">{{
                dlgData.configName
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="费用状态：">{{
                dlgData.status == 0 ? "未缴费" : "已缴费"
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="缴费时间：">{{
                dlgData.payTime
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="建账时间：">{{
                dlgData.createTime
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计费开始时间：">{{
                dlgData.startDate
              }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            
            <el-col :span="8">
              <el-form-item label="计费结束时间：">{{
                dlgData.endDate
              }}</el-form-item>
            </el-col>
            
          </el-row>
          <el-form-item label="说明：">{{ dlgData.remark }}</el-form-item>
          <el-divider v-if="dlgType == 'VIEW'">缴费历史</el-divider>
          <el-table
            class="m-small-table"
            :data="dlgData.list"
            border
            fit
            ref="dlgTableRef"
            highlight-current-row
          >
            <el-table-column
            :selectable="dTDisFn"
              label="#"
              align="center"
              type="selection"
              width="50"
            >
            </el-table-column>

            <el-table-column label="费用类型">
              <template slot-scope="scope">
                <span>{{ scope.row.feeName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用项目">
              <template slot-scope="scope">
                <span>{{ scope.row.configName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用标识">
              <template slot-scope="scope">
                <span>{{ scope.row.feeFlagName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用摘要" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <span
                  >{{ scope.row.startDate }} ~ {{ scope.row.endDate }}
                  {{ scope.row.feeName }}
                </span>
              </template>
            </el-table-column>

            <el-table-column label="应收金额">
              <template slot-scope="scope">
                <span>{{ scope.row.receivableAmount }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用金额">
              <template slot-scope="scope">
                <span>{{ scope.row.amount.toFixed(2) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="优惠金额">
              <template slot-scope="scope">
                <span>{{ scope.row.preferentialAmount }}</span>
              </template>
            </el-table-column>

            <el-table-column label="滞纳金">
              <template slot-scope="scope">
                <span>{{ scope.row.zhinajin }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-form-item
            class="mt10"
            label="收费方式："
            v-if="dlgType == 'EDIT'"
          >
            <el-select
              style="width: 200px"
              v-model="dlgData.offlinePayWay"
              filterable
              clearable
              placeholder="收费方式"
            >
              <el-option
                v-for="item in payWayList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="缴费金额：" v-if="dlgType == 'EDIT'">
            <span class="fdanger font24">{{ totalMoney.toFixed(2) }}元</span>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dlgShow = false" icon="el-icon-back"
            >取消</el-button
          >
          <el-button
            v-if="dlgType !== 'VIEW'"
            type="success"
            :loading="dlgLoading"
            @click="subDlg"
            icon="el-icon-check"
          >
            <span>缴费</span>
          </el-button>
        </div>
      </el-dialog>

    <el-dialog :close-on-click-modal="false" title="缴费" @close="closeBatchPaymentDlg" :visible.sync="batchPaymentDlg"
      width="1200px" append-to-body>
      <el-form ref="batchPaymentForm" :rules="batchPaymentRules" :model="batchPaymentData" label-position="right"
        label-width="135px">
        <el-table ref="batchPayTableRef" v-loading="batchPaymentLoading" key="id" class="m-small-table"
          :data="batchPaymentData.list" border fit highlight-current-row>
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="付费对象" width="160">
            <template slot-scope="scope">
              <span v-if="scope.row.payType == '1'">{{ scope.row.roomName + '(房屋)' }}</span>
              <span v-else-if="scope.row.payType == '2'">{{ scope.row.parkingName + '(车位)' }}</span>
              <span v-else>{{ scope.row.garageName + '(车库)' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用类型">
            <template slot-scope="scope">
              <span>{{ scope.row.feeName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用项目">
            <template slot-scope="scope">
              <span>{{ scope.row.configName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用标识" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.feeFlagName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用摘要">
            <template slot-scope="scope">
              <span>{{ scope.row.startDate }} ~ {{ scope.row.endDate }} {{ scope.row.feeName }} </span>
            </template>
          </el-table-column>

          <el-table-column label="应收金额" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ num2Round(scope.row.receivableAmount) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="实缴金额" align="center" width="110">
            <template slot-scope="scope">
              <span>{{
                (batchPaymentMoney > oldBatchPaymentMoney ? num2Round(scope.row.receivableAmount + scope.row.changeAmount
                  +
                  scope.row.zhinajin - scope.row.preferentialAmount) : num2Round(scope.row.receivableAmount -
                    scope.row.changeAmount + scope.row.zhinajin - scope.row.preferentialAmount)).toFixed(2)||0
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="优惠申请" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ num2Round(scope.row.preferentialAmount) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="滞纳金" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ scope.row.zhinajin }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="60" align="center">
            <template slot-scope="scope">
              <el-button @click="delSelectRow(scope.$index)" type="danger" size="mini" icon="el-icon-delete"
                plain></el-button>
            </template>
          </el-table-column>

        </el-table>
        <el-form-item class="mt10" label="收费方式：" prop="offlinePayWay">
          <!-- <div v-if="batchPaymentData.offlinePayWay == 0">线上支付</div> -->
          <el-select style="width: 200px" v-model="batchPaymentData.offlinePayWay" filterable clearable
            placeholder="收费方式">
            <el-option v-for="item in payWayList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="优惠券：">
          <el-tag type="primary">{{ batchPaymentData.couponMoney }} 元</el-tag>
        </el-form-item>

        <el-form-item label="缴费金额：">
          <div>
            <el-input-number @change="batchPaymentMoneyChange" v-model="batchPaymentMoney" :max="bigBatchPaymentMoney"
              :min="smBatchPaymentMoney" :precision="2" :step="0.01"></el-input-number>元
            <!-- <span class="fdanger">{{ num2Round(batchPaymentMoney - batchPaymentData.couponMoney) }} </span> -->
            (实缴金额:{{ sjMoney }} - 优惠券:{{ batchPaymentData.couponMoney }})
          </div>
          <!-- <span class="fdanger font24">{{ totalMoney.toFixed(2) }}元</span> -->
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchPaymentDlg = false" icon="el-icon-back">取消</el-button>

        <el-button type="success" @click="subBatchPaymentDlg" icon="el-icon-check">缴费</el-button>
      </div>
    </el-dialog>
    </template>
    <template v-if="curActive == 4">
      <div class="table-container">
        <el-table
          class="m-small-table"
          height="100%"
          v-loading="listLoading"
          :data="memberList"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="姓名">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="性别">
            <template slot-scope="scope">
              <span>{{ scope.row.sex }}</span>
            </template>
          </el-table-column>

          <el-table-column label="年龄">
            <template slot-scope="scope">
              <span>{{ scope.row.age }}</span>
            </template>
          </el-table-column>

          <el-table-column label="身份证">
            <template slot-scope="scope">
              <span>{{ scope.row.idCard }}</span>
            </template>
          </el-table-column>

          <el-table-column label="联系方式">
            <template slot-scope="scope">
              <span>{{ scope.row.phone }}</span>
            </template>
          </el-table-column>

          <el-table-column label="微信">
            <template slot-scope="scope">
              <span>{{ scope.row.wchat }}</span>
            </template>
          </el-table-column>

          <el-table-column label="QQ">
            <template slot-scope="scope">
              <span>{{ scope.row.qq }}</span>
            </template>
          </el-table-column>

          <el-table-column label="身份">
            <template slot-scope="scope">
              <span v-if="scope.row.type == 1">业主</span>
              <span v-if="scope.row.type == 2">成员</span>
              <span v-if="scope.row.type == 3">租户</span>
            </template>
          </el-table-column>

          <el-table-column label="备注" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span>{{ scope.row.remark }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="120"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-edit"
                plain
                @click="showMiDlg('edit', scope.row)"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-container">
        <pagination
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          @pagination="getMemberList"
        />
      </div>
    </template>
    <template v-if="curActive == 5">
      <div class="table-container">
        <el-table
          class="m-small-table"
          height="100%"
          v-loading="listLoading"
          :data="repairList"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="报修人">
            <template slot-scope="scope">
              <span>{{ scope.row.repairName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="联系方式">
            <template slot-scope="scope">
              <span>{{ scope.row.tel }}</span>
            </template>
          </el-table-column>

          <el-table-column label="预约时间">
            <template slot-scope="scope">
              <span>{{ scope.row.appointmentTime }}</span>
            </template>
          </el-table-column>

          <el-table-column label="报修类型">
            <template slot-scope="scope">
              <span>{{ scope.row.repairTypeText }}</span>
            </template>
          </el-table-column>

          <el-table-column label="维修费用(元)">
            <template slot-scope="scope">
              <span>{{ scope.row.priceScope }}</span>
            </template>
          </el-table-column>

          <el-table-column label="维修内容" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span>{{ scope.row.context }}</span>
            </template>
          </el-table-column>

          <el-table-column label="位置">
            <template slot-scope="scope">
              <div>小区:{{ scope.row.communityName }}</div>
              <div>房屋:{{ scope.row.fangwu }}</div>
            </template>
          </el-table-column>

          <el-table-column label="状态">
            <template slot-scope="scope">
              <span>{{ scope.row.stateName }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="page-container">
        <pagination
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          @pagination="getRepairList"
        />
      </div>
    </template>
    <template v-if="curActive == 6">
      <div class="table-container">
        <el-table
          class="m-small-table"
          height="100%"
          v-loading="listLoading"
          :data="complaintList"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="投诉类型">
            <template slot-scope="scope">
              <span>{{ scope.row.valuateTypeText }}</span>
            </template>
          </el-table-column>

          <el-table-column label="房屋">
            <template slot-scope="scope">
              <div>小区:{{ scope.row.communityName }}</div>
              <div>房屋:{{ scope.row.fangwu }}</div>
            </template>
          </el-table-column>

          <el-table-column label="投诉人">
            <template slot-scope="scope">
              <span>{{ scope.row.valuateUserName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="投诉人电话">
            <template slot-scope="scope">
              <span>{{ scope.row.valuatePhone }}</span>
            </template>
          </el-table-column>

          <el-table-column label="投诉状态" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status == 0" type="danger">未处理</el-tag>
              <el-tag v-else type="success">已处理</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="处理人">
            <template slot-scope="scope">
              <span>{{ scope.row.genzongName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="处理人电话">
            <template slot-scope="scope">
              <span>{{ scope.row.genzongPhone }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="page-container">
        <pagination
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          @pagination="getComplaintList"
        />
      </div>
    </template>
    <roomDlg />
    <memberInfoDlg
      :dlgState0="dlgMiState"
      :dlgData0="dlgMiData"
      :dlgType="dlgMiType"
      :dlgQuery="dlgMiQuery"
      @closeDlg="closeMiDlg"
      @getList="getList"
      :roomInfo="roomInfo"
    />
  </div>
</template>

<script>
import Cookie from "js-cookie";
import { mapGetters } from "vuex";
import { buildingmemberRoom } from "@/api/ownerMan";
import {
  findPayBillSumPageYewushouli,
  payOweAmount,
  payOweAmountV,
  delPayFeeBillSumById,
  getPayBillPage,
} from "@/api/costMan";
import { yewushouliBaoXiuPage } from "@/api/repairMan";
import { membercomplaintPage } from "@/api/complaintMan";
import * as utils from "@/utils";
import Pagination from "@/components/Pagination";
import { uploadImg } from "@/utils/uploadImg";
import roomDlg from "@/components/Dialog/communityMan/roomDlg";
import WorkSpaceBase from "@/components/WorkSpace/WorkSpaceBase";
import { postAction, getAction } from '@/api'
import memberInfoDlg from "@/views/communityProperty/ownerMan/memberInfoDlg";

let dlgDataEmpty = {
  list: [],
  offlinePayWay: "",
};
let batchPaymentDataEmpty = {
  list: [],
  offlinePayWay: '',
  couponMoney: 0,
  couponId: ""
}

export default {
  name: "businessHandle",
  extends: WorkSpaceBase,
  components: {
    Pagination,
    roomDlg,
    memberInfoDlg,
  },
  data() {
    return {
      curActive: "1",
      payList: [], // 缴费列表
      selectList: [], // 选择缴费列表
      totalMoney: 0,
      memberList: [], // 成员列表
      repairList: [], // 报修列表
      complaintList: [], // 投诉列表

      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: "", // ADD\EDIT
      dlgTitle: "", // 标题

      rules: {
        roomId: [{ required: true, message: "必填字段", trigger: "change" }],
        sex: [{ required: true, message: "必填字段", trigger: "blur" }],
        name: [{ required: true, message: "必填字段", trigger: "change" }],
        phone: [
          { required: true, message: "必填字段", trigger: "blur" },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: "手机号码格式有误！",
            trigger: "blur",
          },
        ],
        idCard: [
          { required: true, message: "必填字段", trigger: "blur" },
          {
            pattern:
              /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
            message: "证件号码格式有误！",
            trigger: "blur",
          },
        ],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
        pageType: "1",
        roomId: "",
        roomName: "",
        roomInfo: {},
        ownerInfo: {},
        status:"",
      },

      userInfo: {},
      pageTypeList: [
        {
          id: "1",
          name: "房屋",
        },
      ],
      feeFlagList: [
        {
          id: "1",
          name: "周期性费用",
        },
        {
          id: "2",
          name: "一次性费用",
        },
      ],
      stateList: [
        {
          id: "0",
          name: "未派单",
        },
        {
          id: "1",
          name: "已接单",
        },
        {
          id: "2",
          name: "待评价",
        },
        {
          id: "3",
          name: "待回访",
        },
        {
          id: "4",
          name: "待评价待回访",
        },
        {
          id: "5",
          name: "待评价已回访",
        },
        {
          id: "6",
          name: "已评价待回访",
        },
        {
          id: "7",
          name: "已结束",
        },
      ],
      typeList: [
        {
          id: 1,
          name: "投诉",
        },
        {
          id: 2,
          name: "建议",
        },
      ],
      payWayList: [
        {
          id: "1",
          name: "微信",
        },
        {
          id: "2",
          name: "支付宝",
        },
        {
          id: "3",
          name: "现金",
        },
      ],

      // 会员信息弹窗
      dlgMiQuery: {},
      dlgMiState: false,
      dlgMiType: "", // 弹框状态add, edit
      dlgMiData: {},
      costTypeList:[],//费用类型字典

      //多选缴费
      batchPaymentData: JSON.parse(JSON.stringify(batchPaymentDataEmpty)),
      batchPaymentDlg: false,
      batchPaymentRules: {
        offlinePayWay: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      diaPostList: [],//多选数据
      tableKey: 0,
      batchPaymentMoney: 0,
      bigBatchPaymentMoney: 0,
      smBatchPaymentMoney: 0,
      oldBatchPaymentMoney: 0,//未修改前金额
      selectBatchPaymentList: [],
      sjMoney: 0,
      batchPaymentLoading: false
    };
  },

  computed: {
    ...mapGetters("communityMan/roomDlg", {
      roomId: "roomId",
      roomName: "roomName",
      roomInfo: "roomInfo",
    }),
  },

  watch: {
    roomId(val) {
      if (val) {
        this.listQuery.roomId = val;
        this.getList();
      }
    },

    roomName(val) {
      this.listQuery.roomName = val;
    },

    roomInfo(val) {
      console.log(val);
      this.listQuery.roomInfo = JSON.parse(JSON.stringify(val));
    },
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    utils.getDataDict(this, "costType", "costTypeList");
  },

  methods: {
    num2Round(num, digit = 2) {
      return utils.num2Round(num, digit)
    },
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    // << --- 弹窗 ---
    // -- 表单弹窗
    showMiDlg(type, row) {
      if (utils.isNull(this.roomInfo.id)) {
        this.$message.warning("请选择房屋");
        return;
      }

      if (type == "add") {
        this.dlgMiQuery = { id: 0 };
      } else {
        this.dlgMiQuery = row;
      }
      this.dlgMiType = type;
      this.dlgMiState = true;
    },
    // 关闭弹窗
    closeMiDlg() {
      this.dlgMiState = false;
    },
    ////
    handleClick(tab, event) {
      if (this.curActive < 4) {
        // this.getPayList();
        this.getMemberList();
      } else if (this.curActive == 4) {
        this.getMemberList();
      } else if (this.curActive == 5) {
        this.getRepairList();
      } else if (this.curActive == 6) {
        this.getComplaintList();
      }
    },

    getList() {
      if (utils.isNull(this.listQuery.roomId)) {
        this.$message.warning("请选择房屋");
        return;
      }
      // this.getPayList()
      // this.getMemberList()
      // this.getRepairList()
      // this.getComplaintList()
      if (this.curActive < 4) {
        this.getMemberList();
        // this.getPayList();
      } else if (this.curActive == 4) {
        this.getMemberList();
      } else if (this.curActive == 5) {
        this.getRepairList();
        this.getMemberList();
      } else if (this.curActive == 6) {
        this.getComplaintList();
        this.getMemberList();
      }
    },

    selectionChange(val) {
      console.log('=====val', val)
      this.batchPaymentData.list = []
      this.selectBatchPaymentList = []
      this.batchPaymentData.list = JSON.parse(JSON.stringify(val))
      this.selectBatchPaymentList = JSON.parse(JSON.stringify(val))
    },

    calcMoney() {
      if (this.batchPaymentDlg == true) {
        let money = 0
        for (let i of this.batchPaymentData.list) {
          money += i.receivableAmount + i.zhinajin - i.preferentialAmount
        }
        this.sjMoney = utils.num2Round(money)
        if (this.batchPaymentData.couponMoney > 0) {
          money = utils.num2Round(money - this.batchPaymentData.couponMoney)
        }
        console.log(this.batchPaymentData.list, "this.batchPaymentData.list");
        this.batchPaymentMoney = utils.num2Round(money)
        this.bigBatchPaymentMoney = 0
        this.smBatchPaymentMoney = 0
        this.oldBatchPaymentMoney = 0 //未修改前金额
        this.bigBatchPaymentMoney = utils.num2Round(this.batchPaymentMoney + 1)
        if(utils.num2Round(this.batchPaymentMoney - 1)>0.01){
          this.smBatchPaymentMoney=utils.num2Round(this.batchPaymentMoney - 1)
        }else{
          this.smBatchPaymentMoney=0
        }
        this.oldBatchPaymentMoney = JSON.parse(JSON.stringify(this.batchPaymentMoney))
      } else {
        let money = 0
        for (let i of this.selectList) {
          money += i.receivableAmount + i.zhinajin - i.preferentialAmount
        }
        this.totalMoney = utils.num2Round(money)
      }
    },

    // 获取支付列表
    getPayList() {
      if (utils.isNull(this.listQuery.roomId)) {
        return;
      }
      if (this.curActive < 4) {
        let postParam = {
          page: this.listQuery.page,
          limit: this.listQuery.limit,
          payType: this.curActive,
          status:this.listQuery.status,
          feeType:this.listQuery.feeType,
        };
        if (this.curActive == 1) {
          postParam.roomId = this.listQuery.roomId;
        } else if (this.curActive == 2) {
          console.log(this.listQuery.ownerInfo);
          postParam.parkingIds = this.listQuery.ownerInfo.parkings;
        } else if (this.curActive == 3) {
          postParam.garageIds = this.listQuery.ownerInfo.garages;
        }
        this.listLoading = true;
        // this.total = 0;
        findPayBillSumPageYewushouli(postParam).then((res) => {
          this.listLoading = false;
          if (res.data.code == 200) {
            this.total = res.data.page ? res.data.page.total : 0;
            this.payList = res.data.data
              ? JSON.parse(JSON.stringify(res.data.data))
              : [];
            for (let i of this.payList) {
              i.feeName = utils.getNameById(i.feeType, this.costTypeList);
              i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList);
            }
            this.$refs.multipleTable.clearSelection();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      }
    },

    // 编辑
    editItem(data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgData = Object.assign(
        this.dlgData,
        JSON.parse(JSON.stringify(data))
      );
      this.dlgData.offlinePayWay=""
      if (this.curActive == 1) {
        this.dlgData.payName = data.roomName + "(房屋)";
      } else if (this.curActive == 2) {
        this.dlgData.payName = data.parkingName + "(车位)";
      } else if (this.curActive == 3) {
        this.dlgData.payName = data.garageName + "(车库)";
      }
      this.dlgType = type;
      if(type=="EDIT"){
        if (data.isPay == '1') {
          this.$message.warning('存在支付中缴费请勿重复提交')
          return
        }
      }
      this.dlgShow = true;
      let postParam = {
        page: 1,
        limit: 99,
        status: type == "EDIT" ? "0" : "1",
        billSumId: data.id,
      };
      getPayBillPage(postParam).then((res) => {
        if (res.data.code == 200) {
          for (let i of res.data.data) {
            i.feeName = utils.getNameById(i.feeType, this.costTypeList);
            i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList);
            i.amount = i.receivableAmount + i.zhinajin - i.preferentialAmount;
          }
          this.dlgData.list = JSON.parse(JSON.stringify(res.data.data));
          this.selectList = JSON.parse(JSON.stringify(res.data.data))
          this.$nextTick(() => {
            this.calcMoney() // 计算金额

            if (this.dlgType !== 'VIEW') {
              this.getYhqList(JSON.parse(JSON.stringify(res.data.data))) // 获取优惠券
            }
          })
        }
      });
    },
    // 获取优惠券接口
    getYhqList() {
      let list = this.dlgData.list
      if (!list.length) return false
      let ids = []
      let amount = 0
      for (let item of list) {
        amount += item.receivableAmount
      }

      amount = utils.num2Round(amount)

      getAction(`/unity/coupon/matchCoupon/${this.dlgData.id}/${amount}`).then((res0) => {
        let res = res0.data
        if (res.code == 200) {
          console.log('res', res)
          // for (let i = 0; i < res.data.length; i++) {
          //   list[i].yhq = res.data[i].yhq
          // }
          // this.dlgData.list = JSON.parse(JSON.stringify(list))

          console.log('res.data.couponMoney', res.data.couponMoney)

          this.dlgData.couponId = res.data.couponId // 优惠券
          this.dlgData.couponMoney = res.data.couponMoney

          this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
        } else {
          this.$message.warning(res.msg)
        }

        setTimeout(() => {
          for (let item of this.dlgData.list) {
            this.$refs.dlgTableRef.toggleRowSelection(item, true)
          }
        }, 200)
      })
    },

    // 删除
    delItem(data) {
      this.$confirm("确定取消费用?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delPayFeeBillSumById(data.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.getPayList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    // 弹窗表格禁选
    dTDisFn(row, index) {
      console.log('row', row)
      console.log('index', index)

      return false
    },

    // 弹窗提交
    subDlg() {
      if (this.selectList.length == 0) {
        this.$message.warning("请选择缴费记录");
        return;
      }
      this.$refs["dlgForm"].validate((valid) => {
        if (valid) {
          this.$confirm("确定缴费?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            let postParam = {};
            let billIds = [];
            for (let i of this.selectList) {
              billIds.push(i.id);
            }
            let obj={}
            obj.billId=this.selectList[0].id
            obj.billSumId=this.selectList[0].billSumId
            obj.configId=this.selectList[0].configId
            obj.receivableAmount=this.selectList[0].receivableAmount
            obj.receivedAmount=this.selectList[0].receivableAmount
            let billList = []
            billList.push(obj)
            // postParam.billIds = billIds.join(",");
            postParam.couponId = this.dlgData.couponId
            postParam.couponMoney = this.dlgData.couponMoney
            postParam.feeBillList=billList
            postParam.communityId = this.selectList[0]["communityId"];
            postParam.offlinePayWay = this.dlgData.offlinePayWay;
            postParam.openId=""
            postParam.payWay= 3
            postParam.payType = this.curActive;
            postParam.roomId = this.selectList[0]["roomId"];
            postParam.roomName = this.selectList[0]["roomName"];
            postParam.roomTypeId = this.selectList[0]["roomTypeId"];
          
            this.dlgLoading = true;
            payOweAmountV(postParam).then((res) => {
              this.dlgLoading = false;
              if (res.data.code == 200) {
                this.getPayList();
                this.calcMoney();
                this.dlgShow = false;
                this.$message.success(res.data.msg);
              } else {
                this.$message.error(res.data.msg);
              }
            });
          });
        }
      });
    },

    // 获取数据
    getMemberList() {
      if (utils.isNull(this.listQuery.roomId)) {
        return;
      }
      this.listLoading = true;
      this.total = 0;
      buildingmemberRoom(this.listQuery.roomId).then((res) => {
        this.listLoading = false;
        if (res.data.code == 200) {
          console.log('22222')
          this.memberList = JSON.parse(JSON.stringify(res.data.data));

          this.total = this.memberList.length;
          if (res.data.data.length > 0) {
            for (let i of res.data.data) {
              if (i.type == 1) {
                this.listQuery.ownerInfo = JSON.parse(JSON.stringify(i));
                break;
              }
            }
          } else {
            this.listQuery.ownerInfo = {};
          }
          if (this.curActive < 4) {
            this.getPayList();
          }
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    getRowKeys(row) {
      return row.id
    },
    tableRowClick(row, column, event) {
      // if (column.label == '操作') return false

      // this.$refs.multipleTable.toggleRowSelection(row)
    },
    // 获取报修列表
    getRepairList() {
      if (utils.isNull(this.listQuery.roomId)) {
        return;
      }
      let postParam = {
        page: this.listQuery.page,
        limit: this.listQuery.limit,
        roomId: this.listQuery.roomId,
      };
      this.listLoading = true;
      this.total = 0;
      yewushouliBaoXiuPage(postParam).then((res) => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.repairList = JSON.parse(JSON.stringify(res.data.data));
          this.total = res.data.page ? res.data.page.total : 0;
          for (let i of this.repairList) {
            i.stateName = utils.getNameById(i.state, this.stateList);
          }
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 获取投诉列表
    getComplaintList() {
      if (utils.isNull(this.listQuery.roomId)) {
        return;
      }
      let postParam = {
        page: this.listQuery.page,
        limit: this.listQuery.limit,
        roomId: this.listQuery.roomId,
      };
      this.listLoading = true;
      this.total = 0;
      membercomplaintPage(postParam).then((res) => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.complaintList = JSON.parse(JSON.stringify(res.data.data));
          this.total = res.data.page ? res.data.page.total : 0;
          for (let i of this.complaintList) {
            i.valuateTypeText = utils.getNameById(i.valuateType, this.typeList);
          }
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    //批量缴费弹框
    batchPayment() {
      console.log(this.batchPaymentData,"this.batchPaymentData");
      if (this.batchPaymentData.list.length <= 0) {
        this.$message({
          type: 'warning',
          message: '请选择缴费项',
        })
        return false
      }
      for (let i of this.batchPaymentData.list) {
        i.changeAmount = 0
        if (i.status != 0) {
          this.$message({
            type: 'warning',
            message: '请统一选择未缴费项',
          })
          return false
        }
        if (i.receivableAmount <= 0) {
          this.$message({
            type: 'warning',
            message: '应收金额为0请重新选择',
          })
          return false
        }
      }
      this.getSelectYhqList()
      this.getSelectInfo()
      this.batchPaymentDlg = true
    },
    closeBatchPaymentDlg() {
      this.batchPaymentData.list = JSON.parse(JSON.stringify(this.selectBatchPaymentList))
    },
    checkInit(row, index) {
      if (row.isPay == '1'||row.status=='1') {
        return false
      } else {
        return true
      }
    },
    delSelectRow(idx) {
      this.batchPaymentData.list.splice(idx, 1)
      for (let item of this.payList) {
        let isHas = this.batchPaymentData.list.some((item2) => {
          return item2.id == item.id
        })
        if (isHas) {
          this.$refs.multipleTable.toggleRowSelection(item, true)
        } else {
          this.$refs.multipleTable.toggleRowSelection(item, false)
        }
      }
      let money = 0
      for (let i of this.batchPaymentData.list) {
        money += i.receivableAmount + i.zhinajin - i.preferentialAmount
      }
      this.batchPaymentMoney = utils.num2Round(money)
      this.bigBatchPaymentMoney = 0
      this.smBatchPaymentMoney = 0
      this.oldBatchPaymentMoney = 0 //未修改前金额
      this.bigBatchPaymentMoney = utils.num2Round(this.batchPaymentMoney + 1)
      if(utils.num2Round(this.batchPaymentMoney - 1)>0.01){
          this.smBatchPaymentMoney=utils.num2Round(this.batchPaymentMoney - 1)
        }else{
          this.smBatchPaymentMoney=0
        }
      this.oldBatchPaymentMoney = JSON.parse(JSON.stringify(this.batchPaymentMoney))
      if (this.selectBatchPaymentList.length > 0) {
        this.getSelectYhqList()
        this.getSelectInfo()
        this.batchPaymentMoneyChange()
      } else {
        this.sjMoney = 0
      }
      this.$forceUpdate()
    },
    batchPaymentMoneyChange() {
      //分配金额
      let list = this.batchPaymentData.list
      // value 数值
      // amount 分配数量
      let value = this.batchPaymentMoney < this.oldBatchPaymentMoney ? utils.num2Round(this.oldBatchPaymentMoney - this.batchPaymentMoney) : utils.num2Round(this.batchPaymentMoney - this.oldBatchPaymentMoney)
      let amount = this.batchPaymentData.list.length
      let first = utils.num2Round(utils.num2Round(Math.floor(parseInt((value / amount) * 100) / 100 * Math.pow(10, 4))) / Math.pow(10, 4))
      first = parseInt(first * 100) / 100
      let last = (value - first * (amount - 1)).toFixed(4)
      for (let i = 0; i <= list.length; i++) {
        if (i < list.length - 1) {
          list[i].changeAmount = 0
          list[i].changeAmount = utils.num2Round(first)
        } else {
          list[list.length - 1].changeAmount = 0
          list[list.length - 1].changeAmount = utils.num2Round(last)
        }
      }
      if(this.batchPaymentMoney==undefined){
        for (let i of list) {
          i.changeAmount=0
        }
      }
      let money = 0
        for (let i of list) {
          this.batchPaymentMoney > this.oldBatchPaymentMoney ? money += i.receivableAmount + i.changeAmount + i.zhinajin - i.preferentialAmount : money += i.receivableAmount - i.changeAmount + i.zhinajin - i.preferentialAmount
        }
      console.log(this.batchPaymentMoney,"batchPaymentMoney");
      
      this.sjMoney = utils.num2Round(money)
    },
      //多选查缴费单信息
    getSelectInfo() {
      let idList = []
      for (let index = 0; index < this.batchPaymentData.list.length; index++) {
        idList.push(this.batchPaymentData.list[index].id)

      }
      let postParam = {
        page: 1,
        limit: 99,
        status: '0',
        // status: '1',
        billSumIds: idList.join(','),
      }
      this.batchPaymentLoading = true
      getPayBillPage(postParam).then((res) => {
        this.batchPaymentLoading = false
        if (res.data.code == 200) {
          for (let i of res.data.data) {
            i.feeName = utils.getNameById(i.feeType, this.costTypeList)
            i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList)
            i.amount = i.receivableAmount + i.zhinajin - i.preferentialAmount
          }
          this.batchPaymentData.list = JSON.parse(JSON.stringify(res.data.data))
          for (let i of this.batchPaymentData.list) {
            i.changeAmount = 0
          }
          // this.selectBatchPaymentList = JSON.parse(JSON.stringify(res.data.data))
          this.$nextTick(() => {
            this.calcMoney() // 计算金额
            // if (this.dlgType !== 'VIEW') {
            //   this.getYhqList(JSON.parse(JSON.stringify(res.data.data))) // 获取优惠券
            // } else {
            //   setTimeout(() => {
            //     for (let item of this.dlgData.list) {
            //       this.$refs.dlgTableRef.toggleRowSelection(item, true)
            //     }
            //   }, 200)
            //   //  this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
            // }
          })
        }
      })
    },
    // 多选获取优惠券接口
    getSelectYhqList() {
      let list = this.selectBatchPaymentList
      if (!list.length) return false
      let ids = []
      let amount = 0
      for (let item of list) {
        amount += item.receivableAmount
      }
      amount = utils.num2Round(amount)
      for (let index = 0; index < list.length; index++) {
        ids.push(list[index].id)
      }

      getAction(`/unity/coupon/matchCoupon/${ids.join(',')}/${amount}`).then((res0) => {
        let res = res0.data
        if (res.code == 200) {
          console.log('res', res)
          // for (let i = 0; i < res.data.length; i++) {
          //   list[i].yhq = res.data[i].yhq
          // }
          // this.dlgData.list = JSON.parse(JSON.stringify(list))

          if (res.data != null) {
            this.batchPaymentData.couponId = res.data.couponId // 优惠券
            this.batchPaymentData.couponMoney = res.data.couponMoney
          } else {
            this.batchPaymentData.couponId = ''
            this.batchPaymentData.couponMoney = 0
          }

          this.batchPaymentData = JSON.parse(JSON.stringify(this.batchPaymentData))
        } else {
          this.$message.warning(res.msg)
        }

        setTimeout(() => {
          for (let item of this.dlgData.list) {
            this.$refs.dlgTableRef.toggleRowSelection(item, true)
          }
        }, 200)
      })
    },
    // 选择房屋
    showRoomDlg() {
      this.$store.commit(
        "communityMan/roomDlg/SET_ROOMID",
        this.listQuery.roomId
      );
      this.$store.commit(
        "communityMan/roomDlg/SET_ROOMNAME",
        this.listQuery.roomName
      );
      this.$store.commit(
        "communityMan/roomDlg/SET_ROOMINFO",
        this.listQuery.roomInfo
      );
      this.$store.commit("communityMan/roomDlg/SET_DLGSHOW", true);
    },
    subBatchPaymentDlg() {
      if (this.batchPaymentData.list.length == 0) {
        this.$message({
          type: 'warning',
          message: '请选择缴费记录',
        })
        return false
      }
      this.$refs['batchPaymentForm'].validate((valid) => {
        if (valid) {
          if(this.batchPaymentMoney==undefined){
            this.$message({
              type: 'warning',
              message: '请输入缴费金额',
            })
            return false
          }
          this.$confirm('确定缴费?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            let newList = this.batchPaymentData.list
            let billList = []
            for (let index = 0; index < newList.length; index++) {
              let obj = {}
              obj.billSumId = newList[index].billSumId
              obj.billId = newList[index].id
              obj.receivedAmount = this.batchPaymentMoney > this.oldBatchPaymentMoney ? utils.num2Round(newList[index].receivableAmount + newList[index].changeAmount) : utils.num2Round(newList[index].receivableAmount -
                newList[index].changeAmount).toFixed(2)
              obj.receivableAmount = newList[index].receivableAmount
              obj.configId = newList[index].configId
              billList.push(obj)
            }
            let sendObj = {
              communityId: this.batchPaymentData.list[0]['communityId'],
              openId: "",
              payType: this.curActive,
              payWay: 3,
              offlinePayWay: this.batchPaymentData.offlinePayWay,
              roomId: this.batchPaymentData.list[0]['roomId'],
              roomName: this.batchPaymentData.list[0]['roomName'],
              roomTypeId: this.batchPaymentData.list[0]['roomTypeId'],
              couponId: this.batchPaymentData.couponId,
              couponMoney: this.batchPaymentData.couponMoney,
              feeBillList: billList
            }
            if (sendObj.payType == "2") {
              sendObj.roomId = this.batchPaymentData.list[0].parkingId;
              sendObj.roomName = this.batchPaymentData.list[0].parkingName;
            }
            if (sendObj.payType == "3") {
              sendObj.roomId = this.batchPaymentData.list[0].garageId;
              sendObj.roomName = this.batchPaymentData.list[0].garageName;
            }
            if (sendObj.roomTypeId == 0) {
              this.$message({
                type: 'warning',
                message: '未绑定房产类型,该缴费单无效',
              })
              return false
            }
            // console.log(sendObj,"sendObj");
            // return
            payOweAmountV(sendObj).then((res) => {
              console.log(res);
              if (res.data.code == 200) {
                this.batchPaymentDlg = false
                this.getList()
                this.$refs.multipleTable.clearSelection();
                this.$message.success(res.data.msg)
              } else {
                this.$message.error(res.data.msg)
              }
            })

          })
        }
      })
    }
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-image {
  width: 150px;
  height: 200px;
}
.table-container {
  height: calc(100% - 360px);
}
</style>


