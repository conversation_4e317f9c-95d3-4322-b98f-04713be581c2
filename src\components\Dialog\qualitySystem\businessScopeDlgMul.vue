<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="'选择区域/点位'"
    :visible.sync="dlgShow"
    width="900px"
    append-to-body
  >
    <div class="clear-container clearfix">
      <div class="tree-container">
        <span class="fbold">待选</span>
        <el-tree
          ref="treeDom"
          show-checkbox
          highlight-current
          node-key="id"
          :props="defaultProps"
          @check="treeCheck"
          :check-strictly="true"
          :default-expanded-keys="defaultOpenList"
          :data="list"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse"
          :expand-on-click-node="false"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span :title="data.name">{{ data.name }}</span>
          </span>
        </el-tree>
      </div>
      <div class="table-container">
        <span class="fbold cBlue">已选</span>
        <el-table
          ref="tableBar"
          class="m-small-table"
          :data="selectTableList"
          border
          fit
          highlight-current-row
        >
          <el-table-column type="index" label="序号" align="center" width="50">
          </el-table-column>
          <el-table-column label="区域/点位" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.regionName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-delete"
                type="danger"
                size="mini"
                plain
                @click="delItem(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="clear"></div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-close" @click="closeDlg"> 取 消 </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";

import * as utils from "@/utils";

import { loadWorkRegionTree } from "@/api/qualitySystem/projectWorkSetup.js";

export default {
  components: {},
  data() {
    return {
      listQuery: {
        projectId: "",
        projectName: "",
        work: "",
        page: 1,
        size: 20
      },

      list: [],

      selectKeys: [],

      selectList: [],

      selectTableList: [],

      defaultOpenList: [], // 默认展开

      defaultProps: {
        children: "children",
        label: "name"
      }
    };
  },

  computed: {
    dlgShow: {
      get: function() {
        return this.$store.state.qualitySystem.businessScopeDlgMul.dlgShow;
      },
      set: function(val) {
        this.$store.commit(
          "qualitySystem/businessScopeDlgMul/SET_DLGSHOW",
          val
        );
      }
    },

    businessScopeList: {
      get: function() {
        return this.$store.state.qualitySystem.businessScopeDlgMul.list;
      },
      set: function(val) {
        this.$store.commit("qualitySystem/businessScopeDlgMul/SET_LIST", val);
      }
    },

    workType: {
      get: function() {
        return this.$store.state.qualitySystem.businessScopeDlgMul.workType;
      },
      set: function(val) {
        this.$store.commit(
          "qualitySystem/businessScopeDlgMul/SET_WORKTYPE",
          val
        );
      }
    },

    projectId: {
      get: function() {
        return this.$store.state.qualitySystem.businessScopeDlgMul.projectId;
      },
      set: function(val) {
        this.$store.commit(
          "qualitySystem/businessScopeDlgMul/SET_PROJECTID",
          val
        );
      }
    },

    projectName: {
      get: function() {
        return this.$store.state.qualitySystem.businessScopeDlgMul.projectName;
      },
      set: function(val) {
        this.$store.commit(
          "qualitySystem/businessScopeDlgMul/SET_PROJECTNAME",
          val
        );
      }
    }
  },

  watch: {
    dlgShow(val) {
      if (val) {
        this.getList();
      }
    },
    businessScopeList(val) {
      let list = JSON.parse(JSON.stringify(val));
      for (let item of list) {
        item.id = item.regionId;
      }
      this.selectTableList = JSON.parse(JSON.stringify(list));
      this.selectList = JSON.parse(JSON.stringify(list));
    }
  },

  created() {},

  methods: {
    // 树节点展开
    handleNodeExpand(data) {
      // 保存当前展开的节点
      let flag = false;
      this.defaultOpenList.some(item => {
        if (item === data.id) {
          // 判断当前节点是否存在， 存在不做处理
          flag = true;
          return true;
        }
      });
      if (!flag) {
        // 不存在则存到数组里
        this.defaultOpenList.push(data.id);
      }
    },

    // 树节点关闭
    handleNodeCollapse(data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i;
        }
      });
    },

    // 删除项
    delItem(idx) {
      this.selectTableList.splice(idx, 1);
      this.setCheckedKeys();
    },

    // 递归全选当前下的节点
    selectAllNode(childrenList, type) {
      for (let item of childrenList) {
        // 全选，全部取消
        if (type == "select") {
          if (!this.selectKeys.includes(item.id)) {
            this.selectKeys.push(item.id);
            this.selectList.push(item);
          }
        } else {
          if (this.selectKeys.includes(item.id)) {
            let mIndex = this.selectKeys.indexOf(item.id);
            this.selectKeys.splice(mIndex, 1);
            this.selectList.splice(mIndex, 1);
          }
        }
        if (item.children) {
          this.selectAllNode(item.children, type);
        }
      }
    },

    treeCheck(checkedNodes, checkedKeys) {
      if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
        this.selectKeys = checkedKeys.checkedKeys;
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes));
        // select-全选；remove-取消全选
        this.selectAllNode(checkedNodes.children, "select");
      } else {
        this.selectKeys = checkedKeys.checkedKeys;
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes));
        this.selectAllNode(checkedNodes.children, "remove");
      }
      this.$refs.treeDom.setCheckedKeys(this.selectKeys);
      let checkNodes = this.$refs.treeDom.getCheckedNodes();
      // 没有的添加
      let selIdList = this.selectTableList.map(item => item.id);
      for (let i of checkNodes) {
        if (i.type == 1 && !selIdList.includes(i.id)) {
          this.selectTableList.push(i);
        }
      }
      // 列表有的删除
      let idList = checkNodes.map(item => item.id);
      for (let i = this.selectTableList.length - 1; i >= 0; i--) {
        if (!idList.includes(this.selectTableList[i]["id"])) {
          this.selectTableList.splice(i, 1);
        }
      }
      this.formatTableList();
    },

    // 格式化 表格数据
    formatTableList() {
      let selectTableList = JSON.parse(JSON.stringify(this.selectTableList));
      for (let i of selectTableList) {
        i.regionId = i.id;
        i.regionName = i.regionNameList.join("-");
        i.beginTime = "";
        i.endTime = "";
        i.duration = "";
      }
      this.selectTableList = JSON.parse(JSON.stringify(selectTableList));
      this.$forceUpdate();
    },

    setCheckedKeys() {
      this.$nextTick(() => {
        let currentKey = [];
        for (let i of this.selectTableList) {
          let checkId = parseInt(i.regionId);
          currentKey.push(checkId);
        }
        this.selectKeys = currentKey;
        this.$refs.treeDom.setCheckedKeys(currentKey);
      });
    },

    // 递归
    recursiveList(list) {
      let fn = (list, name) => {
        for (let i of list) {
          if (utils.isNull(i.regionNameList)) {
            i.regionNameList = [];
          }
          if (name) {
            for (let j of name) {
              i.regionNameList.push(j);
            }
          }
          i.regionNameList.push(i.name);
          if (i.children && i.children.length > 0) {
            fn(i.children, i.regionNameList);
          }
        }
      };
      fn(list);
    },

    // 获取树列表
    getList() {
      this.list = [];
      this.listQuery.projectId = this.projectId;
      this.listQuery.projectName = this.projectName;
      this.listQuery.work = this.workType;
      loadWorkRegionTree(this.listQuery).then(res => {
        if (res.data.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data.data));
          this.recursiveList(list);
          this.list = list;
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = [list[0].id];
          }
          this.setCheckedKeys();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    subDlg() {
      this.selectList = JSON.parse(JSON.stringify(this.selectTableList));
      this.$store.commit(
        "qualitySystem/businessScopeDlgMul/SET_LIST",
        this.selectList
      );
      this.closeDlg();
    },

    closeDlg() {
      this.$store.commit(
        "qualitySystem/businessScopeDlgMul/SET_DLGSHOW",
        false
      );
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 660px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

.clear-container {
  height: 100%;
  .tree-container {
    float: left;
    width: 180px;
    height: 100%;
    .el-tree {
      background: #f2f2f2;
      width: 100%;
      height: calc(100% - 26px);
      margin-top: 10px;
      overflow: auto;
      padding-top: 10px;
    }
    /deep/ .el-tree > .el-tree-node {
      display: inline-block;
      min-width: 100%;
    }
  }

  .table-container {
    float: right;
    width: calc(100% - 200px);
    height: 100%;
    .el-table {
      margin-top: 10px;
    }
  }
}
</style>
