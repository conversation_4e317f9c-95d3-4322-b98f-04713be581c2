<template>
  <div>
    <el-dialog
      title="设备台账"
      :visible.sync="dialogVisible"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="header">
        <div style="width: 200px; height: 200px">
          <el-image :src="formData.picUrl[0]" alt="" class="imageStyle">
            <div slot="error">
              <i class="el-icon-picture-outline"></i>
              暂无图片
            </div>
          </el-image>
        </div>
        <div
          style="margin-left: 10px; height: 200px; overflow: auto"
          class="content1"
        >
          <el-descriptions title="设备参数" :column="1" style="width: 200px">
            <el-descriptions-item
              :label="item.name"
              v-for="(item, index) in listData"
              :key="index"
            >
              <span>{{ item.value }}</span></el-descriptions-item
            >
          </el-descriptions>
        </div>
        <div style="height: 200px" class="content">
          <el-descriptions
            :column="3"
            border
            :label-style="{ width: '120px' }"
            :content-style="{ width: '120px' }"
          >
            <el-descriptions-item label="设备名称"
              ><span :title="formData.equName">{{
                formData.equName
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备编码"
              ><span :title="formData.equCode">{{
                formData.equCode
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备类型"
              ><span :title="formData.equTypeStr">{{
                formData.equTypeStr
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备责任人"
              ><span :title="formData.responsiblePersonName">{{
                formData.responsiblePersonName
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备安装时间"
              ><span :title="formData.installTime">{{
                formData.installTime
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备型号"
              ><span :title="formData.equModel">{{
                formData.equModel
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备所在位置"
              ><span :title="formData.equPosition">{{
                formData.equPosition
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="参考使用年限"
              ><span :title="`${formData.useAge}年`">{{
                formData.useAge
              }}年</span></el-descriptions-item
            >
            <el-descriptions-item label="设备控制区域"
              ><span :title="formData.controlArea">{{
                formData.controlArea
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="生产厂家"
              ><span :title="formData.manufacturer">{{
                formData.manufacturer
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备检测周期"
              ><span :title="formData.checkCycleStr">{{
                formData.checkCycleStr
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备使用状态"
              ><span :title="formData.statusStr">{{
                formData.statusStr
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备出厂时间"
              ><span :title="formData.leaveTime">{{
                formData.leaveTime
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="设备安装图纸">
              <el-image
                v-for="(item, index) in formData.drawingUrl"
                :key="index"
                style="width: 30px; height: 30px; margin: 0 3px"
                :src="item"
                :preview-src-list="formData.drawingUrl"
              >
                <div slot="error">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <h4 style="font-weight: 700; font-size: 15px">设备运行记录</h4>
      <div class="bottom">
        <el-tabs type="card" @tab-click="handleClick" v-model="activeName">
          <el-tab-pane label="保养记录" name="maintain">
            <el-table
              :data="maintainTableData"
              height="435px"
              ref="tableBar"
              class="m-small-table"
              :key="tableKey"
              border
              fit
              highlight-current-row
              style="width: 100%; height: auto"
            >
            <el-table-column label="设备编码" align="center" prop="equCode" width="auto">
            </el-table-column>
              <el-table-column
                prop="maintenanceName"
                label="计划名称"
                width="auto"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="equName"
                label="设备名称"
                width="auto"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="equModel"
                label="设备型号"
                width="auto"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="equPosition"
                label="设备位置"
                width="auto"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                label="执行情况"
                align="center"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.invokeStatusStr }}</span>
                  <!-- <span v-if="scope.row.invokeStatus == '1'"
                    >（{{ scope.row.invokeStatusInfo }}）</span
                  > -->
                </template>
              </el-table-column>
              <el-table-column
                prop="invokeUserName"
                label="执行人"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              /><el-table-column
                label="备注"
                align="center"
                prop="info"
                show-overflow-tooltip
              />
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                  <el-button
                    @click="showDlg('info', scope.row)"
                    icon="el-icon-document"
                    size="mini"
                    type="success"
                    title="详情"
                    plain
                    >详情</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination
              class="mt10"
              :total="maintainPage.total"
              :page.sync="maintainPage.pageNo"
              :limit.sync="maintainPage.pageSize"
              @pagination="getMaintainTableData()"
            />
          </el-tab-pane>

          <el-tab-pane label="检修记录" name="overhaul">
            <el-table
              :data="overhaulTableData"
              height="435px"
              ref="tableBar"
              class="m-small-table"
              :key="tableKey"
              border
              fit
              highlight-current-row
              style="width: 100%; height: auto"
            >
            <el-table-column label="设备编码" align="center" prop="equCode" width="auto">
            </el-table-column>
              <el-table-column
                prop="checkName"
                label="计划名称"
                width="auto"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="equName"
                label="设备名称"
                width="auto"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="equModel"
                label="设备型号"
                width="auto"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="equPosition"
                label="设备位置"
                width="auto"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="checkCycleStr"
                label="设备保养周期"
                width="auto"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                label="执行情况"
                align="center"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.invokeStatusStr }}</span>
                  <!-- <span v-if="scope.row.invokeStatus == '1'"
                    >（{{ scope.row.invokeStatusInfo }}）</span
                  > -->
                </template>
              </el-table-column>
              <el-table-column
                prop="invokeUserName"
                label="检修人员"
                width="auto"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                  <el-button
                    @click="showOverhaulDlg('info', scope.row)"
                    icon="el-icon-document"
                    size="mini"
                    type="success"
                    title="详情"
                    plain
                    >详情</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination
              class="mt10"
              :total="overhaulPage.total"
              :page.sync="overhaulPage.pageNo"
              :limit.sync="overhaulPage.pageSize"
              @pagination="getOverhaulTableData()"
            />
          </el-tab-pane>

          <el-tab-pane label="维修记录" name="service">
            <el-table
              :data="serviceTableData"
              height="435px"
              ref="tableBar"
              class="m-small-table"
              :key="tableKey"
              border
              fit
              highlight-current-row
              style="width: 100%; height: auto"
            >
            <el-table-column label="设备编码" align="center" prop="equCode" width="auto">
            </el-table-column>
              <el-table-column
                prop="equName"
                label="设备名称"
                width="auto"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="content"
                label="内容描述"
                width="auto"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="statusStr"
                label="维修状态"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="finishTime"
                label="完成时间"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="repairPersonName"
                label="维修人员"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                  <el-button
                    @click="showServiceDlg(scope.row)"
                    icon="el-icon-document"
                    size="mini"
                    type="success"
                    title="详情"
                    plain
                    >详情</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination
              class="mt10"
              :total="servicePage.total"
              :page.sync="servicePage.pageNo"
              :limit.sync="servicePage.pageSize"
              @pagination="getServiceTableData()"
            />
          </el-tab-pane>

          <el-tab-pane label="设备日志" name="deviceLogs">
            <el-table
              :data="deviceLogsTableData"
              height="435px"
              ref="tableBar"
              class="m-small-table"
              :key="tableKey"
              border
              fit
              highlight-current-row
              style="width: 100%; height: auto"
            >
              <el-table-column
                prop="createTime"
                label="时间"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="statusStr"
                label="备注"
                width="auto"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="createUserName"
                label="记录人员"
                width="auto"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination
              class="mt10"
              :total="deviceLogsPage.total"
              :page.sync="deviceLogsPage.pageNo"
              :limit.sync="deviceLogsPage.pageSize"
              @pagination="getDeviceLogsTableData()"
            />
          </el-tab-pane>

          <el-tab-pane label="设备操作手册" name="deviceMan">
            <div class="search-item">
              <div class="search-item-label lh28">筛选条件：</div>
              <el-input
                v-model="deviceManSearchForm.fileName"
                placeholder="关键字"
                clearable
                class="fl"
                style="width: 160px"
              ></el-input>
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="getDeviceManTableData()"
                class="fl ml10"
                >查询</el-button
              >
              <!-- <el-button
                type="success"
                @click="showAdddeviceMan('add')"
                icon="el-icon-plus"
                class="fl ml10"
                >添加</el-button
              > -->
            </div>
            <el-table
              :data="deviceManTableData"
              height="397px"
              ref="tableBar"
              class="m-small-table"
              :key="tableKey"
              border
              fit
              highlight-current-row
              style="width: 100%; height: auto"
            >
              <el-table-column label="#" align="center" width="60">
                <template slot-scope="scope">
                  {{
                    (deviceManPage.pageNo - 1) * deviceManPage.pageSize +
                    scope.$index +
                    1
                  }}
                </template>
              </el-table-column>
              <el-table-column
                prop="equTypeStr"
                label="设备类型"
                width="auto"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="fileName"
                label="文件名"
                width="100"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="name"
                label="文档文件"
                width="auto"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column label="操作" width="280" align="center">
                <template slot-scope="scope">
                  <el-button
                    @click="showAdddeviceMan('info', scope.row)"
                    icon="el-icon-document"
                    size="mini"
                    type="primary"
                    title="详情"
                    plain
                    >详情</el-button
                  >
                  <!-- <el-button
                    type="primary"
                    size="mini"
                    @click="showAdddeviceMan('edit', scope.row)"
                    plain
                    icon="el-icon-edit"
                    >编辑</el-button
                  >
                  <el-button
                    type="danger"
                    size="mini"
                    @click="deviceManDelFunc(scope.row)"
                    plain
                    icon="el-icon-delete"
                    >删除</el-button
                  > -->
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination
              class="mt10"
              :total="deviceManPage.total"
              :page.sync="deviceManPage.pageNo"
              :limit.sync="deviceManPage.pageSize"
              @pagination="getDeviceManTableData()"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
    <adddeviceMan ref="adddeviceMan"></adddeviceMan>
    <addMaintainDlg
      ref="addMaintainDlg"
      :dlgData0="dlgMaintainData"
      dlgType="info"
    />
    <addServiceDlg ref="addServiceDlg" dlgType="info"></addServiceDlg>
    <addOverhaulDlg
      ref="addOverhaulDlg"
      :dlgData0="dlgOverhaulData"
      dlgType="info"
    />
  </div>
</template>

<script>
import QRCode from "qrcode";
import { postAction, getAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination"; // 分页
import adddeviceMan from "../equManualMan/addEdit";
import addMaintainDlg from "../../deviceMainRepair/deviceMain/mainPlanExe/addDlg";
import addOverhaulDlg from "../../deviceMainRepair/deviceRepair/mainPlanExe/addDlg";
import addServiceDlg from "@/views/deviceMan/deviceMainRepair/equMain/equPlan/addEdit.vue";
export default {
  components: {
    Pagination,
    adddeviceMan,
    addMaintainDlg,
    addServiceDlg,
    addOverhaulDlg,
  },
  data() {
    return {
      dlgMaintainData: {},
      dlgOverhaulData: {},
      activeName: "maintain",
      dialogVisible: false,
      listData: [],
      formData: [],
      maintainTableData: [],
      maintainPage: {
        pageNo: 1,
        pageSize: 20,
        total: 0,
      },
      overhaulTableData: [],
      overhaulPage: {
        pageNo: 1,
        pageSize: 20,
        total: 0,
      },
      serviceTableData: [],
      servicePage: {
        pageNo: 1,
        pageSize: 20,
        total: 0,
      },
      deviceLogsTableData: [],
      deviceLogsPage: {
        pageNo: 1,
        pageSize: 20,
        total: 0,
      },
      deviceManTableData: [],
      deviceManPage: {
        pageNo: 1,
        pageSize: 20,
        total: 0,
      },
      deviceManSearchForm: {
        fileName: "",
      },
      equData: undefined,
    };
  },
  mounted() {},
  methods: {
    // onClose() {
    //   this.deviceManSearchForm.fileName = "";
    //   this.maintainTableData = [];
    //   this.maintainPage = {
    //     pageNo: 1,
    //     pageSize: 20,
    //     total: 0,
    //   };
    //   this.overhaulTableData = [];
    //   this.overhaulPage = {
    //     pageNo: 1,
    //     pageSize: 20,
    //     total: 0,
    //   };
    //   this.serviceTableData = [];
    //   this.servicePage = {
    //     pageNo: 1,
    //     pageSize: 20,
    //     total: 0,
    //   };
    //   this.deviceLogsTableData = [];
    //   this.deviceLogsPage = {
    //     pageNo: 1,
    //     pageSize: 20,
    //     total: 0,
    //   };
    //   this.deviceManTableData = [];
    //   this.deviceManPage = {
    //     pageNo: 1,
    //     pageSize: 20,
    //     total: 0,
    //   };
    // },
    showServiceDlg(row) {
      let addEdit = this.$refs.addServiceDlg;
      addEdit.title = "详情";
      getAction(`sa/green/equ/repair-manage/get?id=${row.id}`).then((res) => {
        let { code, data } = res.data;
        if (code == 200) {
          console.log(data, "data");
          // addEdit.projectChange(data.projectId);
          addEdit.formData.equId = data ? data.equId : "";
          addEdit.formData.equName = data ? data.equName : "";
          addEdit.formData.projectName = data ? data.projectName : "";
          addEdit.formData.id = data ? data.id : "";
          addEdit.formData.projectId = data.projectId ? data.projectId : "";

          if (data.fileUrl) {
            addEdit.formData.fileUrl = JSON.parse(data.fileUrl);
          } else {
            addEdit.formData.fileUrl = [];
          }

          addEdit.formData.content = data ? data.content : "";
          addEdit.formData.status = data ? data.status : "";
          addEdit.formData.repairPersonName = data ? data.repairPersonName : "";
          addEdit.formData.repairPersonId = data ? data.repairPersonId : "";
          addEdit.formData.finishTime = data ? data.finishTime : "";

          let accessoryJson = JSON.parse(data.accessoryJson);
          let typeJson = JSON.parse(data.typeJson);
          typeJson.map((item) => {
            item.options = [
              {
                id: item.contentId,
                workDescribe: item.content,
              },
            ];
          });

          addEdit.typeTableData = typeJson;
          addEdit.accessoryTableData = accessoryJson;
        } else {
          this.$message.error(res.data.msg);
        }
      });
      addEdit.dialogVisible = true;
    },
    showDlg(type, row) {
      this.dlgMaintainData = row;
      this.$refs.addMaintainDlg.dlgState = true;
    },
    showOverhaulDlg(type, row) {
      this.dlgOverhaulData = row;
      this.$refs.addOverhaulDlg.dlgState = true;
    },
    //检修记录
    getOverhaulTableData() {
      let { pageNo, pageSize } = this.overhaulPage;
      getAction(
        `sa/green/equ/check-invoke/page?equId=${this.equData.id}&pageNo=${pageNo}&pageSize=${pageSize}&invokeStatus=1`
      ).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          this.overhaulTableData = data.list ? data.list : [];
          this.overhaulPage.total = data.total ? data.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    //维修记录
    getServiceTableData() {
      let { pageNo, pageSize } = this.servicePage;
      getAction(
        `sa/green/equ/repair-manage/page?equId=${this.equData.id}&pageNo=${pageNo}&pageSize=${pageSize}`
      ).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          this.serviceTableData = data.list ? data.list : [];
          this.servicePage.total = data.total ? data.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    //保养记录
    getMaintainTableData() {
      let { pageNo, pageSize } = this.maintainPage;
      getAction(
        `sa/green/equ/maintenance-invoke/page?equId=${this.equData.id}&pageNo=${pageNo}&pageSize=${pageSize}&invokeStatus=1`
      ).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          this.maintainTableData = data.list ? data.list : [];
          this.maintainPage.total = data.total ? data.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    //设备日志
    getDeviceLogsTableData() {
      let { pageNo, pageSize } = this.deviceLogsPage;
      getAction(
        `sa/green/equ/equipment-manage-log/page?equId=${this.equData.id}&pageNo=${pageNo}&pageSize=${pageSize}`
      ).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          this.deviceLogsTableData = data.list ? data.list : [];
          this.deviceLogsPage.total = data.total ? data.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    //设备管理手册
    getDeviceManTableData() {
      let { pageNo, pageSize } = this.deviceManPage;
      getAction(
        `sa/green/equ/equipment-manage-manual/page?fileName=${this.deviceManSearchForm.fileName}&equType=${this.equData.equType}&pageNo=${pageNo}&pageSize=${pageSize}`
      ).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          console.log(data.list);
          for (let i = 0; i < data.list.length; i++) {
            let name = [];
            data.list[i].fileUrl = JSON.parse(data.list[i].fileUrl);
            data.list[i].fileUrl.map((item) => {
              name.push(item.name);
            });
            data.list[i].name = name.toString();
          }

          console.log(data.list, "data.list");
          this.deviceManTableData = data.list ? data.list : [];
          this.deviceManPage.total = data.total ? data.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    showAdddeviceMan(type, row) {
      this.$refs.adddeviceMan.init(type, row);
    },
    deviceManDelFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAction(
          `sa/green/equ/equipment-manage-manual/delete?id=${row.id}`
        ).then((res) => {
          if (res.data.code === "200") {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.getDeviceManTableData();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    handleClick(tab, event) {
      if (this.activeName == "maintain") {
        this.getMaintainTableData();
      } else if (this.activeName == "overhaul") {
        this.getOverhaulTableData();
      } else if (this.activeName == "service") {
        this.getServiceTableData();
      } else if (this.activeName == "deviceLogs") {
        this.getDeviceLogsTableData();
      } else if (this.activeName == "deviceMan") {
        this.getDeviceManTableData();
      }
    },
    init(row) {
      this.dialogVisible = true;
      this.activeName = "maintain";
      this.maintainPage.pageNo = 1;
      this.maintainPage.pageSize = 20;
      this.equData = row;
      console.log(row, "rowwwww");
      this.getMaintainTableData();
      getAction(`sa/green/equipment-manage/get?id=${row.id}`).then((res) => {
        console.log(res.data);
        let { code, data } = res.data;
        if (code === "200") {
          //pestIds返回来的是数字类型数组，转换成字符串类型
          if (data.picUrl) {
            data.picUrl = data.picUrl.split(",");
          } else {
            data.picUrl = [];
          }
          if (data.drawingUrl) {
            data.drawingUrl = data.drawingUrl.split(",");
          } else {
            data.drawingUrl = [];
          }
          this.listData = data.list;
          delete data.list;
          this.formData = data ? JSON.parse(JSON.stringify(data)) : [];
          console.log(this.formData, "this.formData");

          // for (let i = 0; i < this.form.pestIds.length; i++) {
          //   console.log(this.form.pestIds[i]);
          //   this.form.pestIds[i] = this.form.pestIds[i].toString();
          // }
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.imageStyle {
  width: 200px;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .header {
  height: 180px;
  display: flex;
  align-items: center;
  .el-descriptions-item__cell {
    font-size: 14px;
    color: black;
    line-height: 30px;
  }
  .content {
    .el-descriptions-item__content {
      max-width: 120px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .content1 {
    .el-descriptions-item__content {
      max-width: 128px;
      //超出一行省略号
      white-space: nowrap; //禁止换行
      // overflow: hidden;
      text-overflow: ellipsis; //...
    }
  }
}

.bottom {
  height: 550px;
}
</style>