// 小区dlg组件

const communityDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    list: [],

  },

  getters: {
    dlgShow: state => state.dlgShow,

    list: state => state.list,

  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_LIST: (state, val) => {
      state.list = val
    },

  },

  actions: {

  }
}

export default communityDlgMul
