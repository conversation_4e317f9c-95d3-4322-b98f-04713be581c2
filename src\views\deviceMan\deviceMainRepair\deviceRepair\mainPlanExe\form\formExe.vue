<template>
  <div>
    <el-form-item label="检修事项" prop="checkItem">
      <el-button
        @click="addItem('bynr')"
        :disabled="dlgType !== 'add'"
        icon="el-icon-plus"
        type="primary"
        plain
        >添加</el-button
      >
      <el-table
        class="mt10"
        :data="dlgData.checkItem"
        fit
        border
        highlight-current-row
      >
        <el-table-column label="#" type="index" align="center" width="60">
        </el-table-column>
        <el-table-column label="检修项">
          <template slot-scope="scope">
            <div v-if="dlgType != 'add'">{{ scope.row.name }}</div>
            <el-select
              v-else
              v-model="scope.row.value"
              placeholder="请选择检修事项"
              clearable
              filterable
              @change="typeChange(scope.$index, scope.row.value)"
            >
              <el-option
                v-for="(item, index) in maintenanceItemsOptions"
                :key="index"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="检修内容及要求">
          <template slot-scope="scope">
            <div v-if="dlgType != 'add'">{{ scope.row.content }}</div>
            <el-select
              v-else
              ref="selectJianxiu"
              allow-create
              default-first-option
              v-model="scope.row.contentId"
              placeholder="请选择检修内容"
              clearable
              filterable
              :title="findLabel()"
            >
              <el-option
                v-for="(item, index) in scope.row.options
                  ? scope.row.options
                  : []"
                :key="index"
                :label="item.workDescribe"
                :value="item.id"
                :title="item.workDescribe"
                style="width: 200px"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          v-if="dlgType == 'add'"
          label="操作"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              @click="delItem('bynr', scope.$index)"
              icon="el-icon-delete"
              size="mini"
              type="danger"
              title="删除"
              plain
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
    <el-form-item label="执行情况" prop="invokeStatus">
      <!-- 0待执行 1已执行 -->
      <!-- invokeStatus
             invokeStatusStr -->
      <el-radio-group
        v-model="dlgData.invokeStatus"
        :disabled="dlgType != 'add' && dlgData.invokeStatus == '1'"
        @change="radioChange"
      >
        <el-radio
          v-for="item of axztSelect"
          :key="item.id"
          :label="item.id"
          >{{ item.name }}</el-radio
        >
      </el-radio-group>
    </el-form-item>

    <!-- <el-form-item
      v-if="dlgData.invokeStatus == '1'"
      label="执行情况备注"
      prop="invokeStatusInfo"
    >
      <el-input
        :autosize="{ minRows: 4, maxRows: 5 }"
        v-model="dlgData.invokeStatusInfo"
        type="textarea"
        placeholder="请输入"
        style="width: 100%"
        @input="changeInput($event)"
      />
    </el-form-item> -->

    <el-form-item
      label="更换配件"
      prop="accessoryJson"
      v-if="dlgData.invokeStatus == 1"
    >
      <el-button
        @click="addItem('ghpj')"
        icon="el-icon-plus"
        type="primary"
        plain
        >添加</el-button
      >
      <el-table
        class="mt10"
        :data="dlgData.accessoryJson"
        fit
        border
        highlight-current-row
      >
        <el-table-column label="#" type="index" align="center" width="60">
        </el-table-column>
        <el-table-column label="选项">
          <template slot-scope="scope">
            <div v-if="dlgType == 'info'">{{ scope.row.option }}</div>

            <el-select
              v-else
              v-model="scope.row.optionId"
              filterable
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item of optionSelect"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="单位" width="130">
          <template slot-scope="scope">
            <div v-if="dlgType == 'info'">{{ scope.row.unit }}</div>
            <el-input v-else v-model="scope.row.unit" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column label="数量" width="140">
          <template slot-scope="scope">
            <div v-if="dlgType == 'info'">{{ scope.row.num }}</div>

            <el-input-number
              v-else
              v-model="scope.row.num"
              :min="0"
              placeholder="请输入"
              controls-position="right"
              style="width: 110px"
            />
          </template>
        </el-table-column>

        <el-table-column
          v-if="dlgType != 'info'"
          label="操作"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              @click="delItem('ghpj', scope.$index)"
              icon="el-icon-delete"
              size="mini"
              type="danger"
              title="删除"
              plain
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>

    <el-form-item
      label="执行人"
      prop="invokeUserName"
      v-if="dlgData.invokeStatus == 1"
    >
      <!-- invokeUserId   invokeUserName -->
      <el-input
        v-model="dlgData.invokeUserName"
        :title="dlgData.invokeUserName"
        @focus="showUserMulDlg"
        placeholder="请选择"
      />
    </el-form-item>

    <el-form-item label="备注" prop="info">
      <el-input
        :autosize="{ minRows: 4, maxRows: 5 }"
        v-model="dlgData.info"
        type="textarea"
        placeholder="请输入"
        style="width: 100%"
      />
    </el-form-item>

    <el-form-item label="执行图片" prop="fileUrl" v-if="dlgType !== 'info'">
      <FileUpload
        :fileList0="this.value.fileUrl"
        :onlyImage="true"
        ref="qiniuUploadRef"
        :limit="5"
        :maxSize="5"
        @successBack="successBack"
        :dlgType="dlgType"
      />
    </el-form-item>
    <el-form-item label="执行图片" prop="fileUrl" v-if="dlgType === 'info'">
      <div v-if="this.value && this.value.fileUrl.length === 0">无</div>
      <el-image
        v-else
        v-for="(item, index) in this.value.fileUrl"
        :key="index"
        style="width: 100px; height: 100px; margin-right: 10px"
        :src="item.url"
        :preview-src-list="fileSrcList"
      >
      </el-image>
    </el-form-item>
    <el-form-item label="计划图片" prop="picUrl" v-if="dlgType === 'add'">
      <FileUpload
        :fileList0="this.value.picUrl"
        :onlyImage="true"
        ref="qiniuUploadRef1"
        :limit="5"
        :maxSize="5"
        @successBack="successBack1"
        :dlgType="dlgType == 'add' ? 'add' : 'info'"
      />
    </el-form-item>
    <el-form-item label="计划图片" prop="picUrl" v-if="dlgType !== 'add'">
      <div v-if="this.value && this.value.picUrl.length === 0">无</div>
      <el-image
        v-else
        v-for="(item, index) in this.value.picUrl"
        :key="index"
        style="width: 100px; height: 100px; margin-right: 10px"
        :src="item.url"
        :preview-src-list="picSrcList"
      >
      </el-image>
    </el-form-item>
     <!-- 多选员工 -->
     <selectUserMulDlg
      ref="userMulDlgRef"
      :dlgType="dlgUserMulType"
      :dlgQuery="dlgUserMulQuery"
      :selectList0="dlgUserMulSelectList"
      @backFunc="dlgUserMulBackFunc"
      :noRule="noRule"
      :isOnJob="true"
    />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getDataDict } from "@/utils";
import { postAction, getAction, putAction } from "@/api";
import FileUpload from "@/views/deviceMan/components/fileUpload";
import selectUserMulDlg from "@/components/DialogWflow/selectUserMulDlg";

export default {
  props: {
    value: {
      type: Object,
      default: {},
    },
    axztSelect: {
      type: Array,
      default: [],
    },
    maintenanceItemsOptions: {
      type: Array,
      default: [],
    },
    optionSelect: {
      type: Array,
      default: [],
    },
    dlgType: {
      type: String,
      default: "add",
    },
    isZhixing: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    FileUpload,
    selectUserMulDlg,
  },
  data() {
    return {
      deviceSelect: [],
      dlgUserMulQuery: {},
      dlgUserMulType: "", // 弹框状态add, edit
      dlgUserMulSelectList: "",
      noRule: false,
    };
  },
  computed: {
    dlgData: {
      get: function () {
        return this.value;
      },
      set: function (val) {
        this.$emit("input", val);
      },
    },
    fileSrcList() {
      let fileUrlList = [];
      if (this.value.fileUrl.length !== 0) {
        this.value.fileUrl.forEach((item) => {
          fileUrlList.push(item.url);
        });
      }
      return fileUrlList;
    },
    picSrcList() {
      let picUrlList = [];
      if (this.value.picUrl.length !== 0) {
        this.value.picUrl.forEach((item) => {
          picUrlList.push(item.url);
        });
      }
      return picUrlList;
    },
  },
  watch: {
  },
  mounted() {},
  methods: {
    dlgUserMulBackFunc(list0) {
      console.log("弹窗返回", list0);
      let personIds = [];
      let personNames = [];

      list0.forEach((item) => {
        personIds.push(item.id);
        personNames.push(item.label);
      });

      this.dlgData.invokeUserId  = personIds.join(",");
      this.dlgData.invokeUserName = personNames.join(",");
    },
    showUserMulDlg() {
      if (
        this.dlgData.invokeUserId  &&
        this.dlgData.invokeUserName
      ) {
        let idList = this.dlgData.invokeUserId .split(",");
        let nameList = this.dlgData.invokeUserName.split(",");

        // 检查idList和nameList长度是否一致
        if (idList.length !== nameList.length) {
          console.error("ID列表和名称列表长度不一致");
          return;
        }

        let list = idList.map((id, index) => ({
          id: id,
          label: nameList[index],
        }));

        this.dlgUserMulSelectList = JSON.stringify(list);
      } else {
        this.dlgUserMulSelectList = "";
      }

      this.dlgUserMulQuery = {};
      this.dlgUserMulType = "edit";
      this.$refs.userMulDlgRef.show();
    },
    // changeInput(){
    //   this.$forceUpdate()
    // },
    findLabel() {
      if (this.$refs.selectJianxiu) {
        return this.$refs.selectJianxiu.selected.label;
      }
    },
    typeChange(index, val) {
      this.dlgData.checkItem[index].contentId = "";
      if (val) {
        this.getRepairSubjectOptions(index, val);
      } else {
        this.dlgData.checkItem[index].options = [];
      }
    },
    getRepairSubjectOptions(index, val) {
      getAction(
        `sa/green/equ/check-item/page?type=${val}&pageNo=1&pageSize=1000`
      ).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          this.dlgData.checkItem[index].options = data.list;
          this.dlgData.checkItem = JSON.parse(
            JSON.stringify(this.dlgData.checkItem)
          );
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    radioChange(val) {
      if (val == 0) {
        // this.dlgData.invokeStatusInfo = undefined;
        this.dlgData.accessoryJson = [
          { option: "", optionStr: "", unit: "", num: undefined },
        ];
        this.dlgData.invokeUserName = null;
      }
    },
    successBack(fileList) {
      this.dlgData.fileUrl = fileList;
    },
    successBack1(fileList) {
      this.dlgData.picUrl = fileList;
    },
    addItem(type) {
      if (type == "bynr") {
        this.dlgData.checkItem.push({
          contentId: "",
          value: "",
        });
      }
      if (type == "ghpj") {
        this.dlgData.accessoryJson.push({
          option: "",
          optionStr: "",
          unit: "",
          num: undefined,
        });
      }

      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    },

    delItem(type, index) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        if (type == "bynr") {
          this.dlgData.checkItem.splice(index, 1);
        }
        if (type == "ghpj") {
          this.dlgData.accessoryJson.splice(index, 1);
        }

        this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
