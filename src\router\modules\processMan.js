/** 业务协同 **/

import Layout from "@/views/layout/Layout";

const processMan = {
  path: "/processMan",
  component: Layout,
  name: "业务协同",
  meta: {
    title: "业务协同",
    icon: "ywxt",
    roles: ["yewuxietong"]
  },
  children: [
    {
      path: "tibaoMatter",
      component: () => import("@/views/processMan/tibaoMatter"),
      name: "提报事项",
      meta: {
        title: "提报事项",
        roles: ["tibaoshixiang"]
      },
      children: []
    },
    {
      path: "daibanMatter",
      component: () => import("@/views/processMan/daibanMatter"),
      name: "待办事项",
      meta: {
        title: "待办事项",
        roles: ["daibanshixiang"]
      },
      children: []
    },
    {
      path: "daifaMatter",
      component: () => import("@/views/processMan/daifaMatter"),
      name: "待发事项",
      meta: {
        title: "待发事项",
        roles: ["daifashixiang"]
      },
      children: []
    },
    {
      path: "yifaMatter",
      component: () => import("@/views/processMan/yifaMatter"),
      name: "已发事项",
      meta: {
        title: "已发事项",
        roles: ["yifashixiang"]
      },
      children: []
    },
    {
      path: "yibanMatter",
      component: () => import("@/views/processMan/yibanMatter"),
      name: "已办事项",
      meta: {
        title: "已办事项",
        roles: ["yibanshixiang"]
      },
      children: []
    },
    //

    {
      path: "processList",
      component: () =>
        import("@/views/processMan/processMan/processList/index"),
      name: "流程管理",
      meta: {
        title: "流程管理",
        roles: ["liuchengguanli"]
      }
    },
    {
      path: "processSet",
      component: () => import("@/views/processMan/processMan/processSet"),
      name: "流程设置",
      hidden: true,
      meta: {
        title: "流程设置",
        roles: ["liuchengguanli"],
        activeMenu: "/processMan/processList"
      }
    },
    {
      path: "bpmnViewer",
      component: () => import("@/views/processMan/processMan/bpmnViewer"),
      name: "查看流程图",
      hidden: true,

      meta: {
        title: "查看流程图",
        activeMenu: "/processMan/processList"
      }
    }

    // {
    //   path: 'liuchengmoxing',
    //   component: () => import('@/views/processMan/createProcessModel'),
    //   name: '创建流程模型',
    //   meta: {
    //     title: '创建流程模型',
    //     // roles: ['paibanguanli']
    //   }
    // },
  ]
};

export default processMan;
