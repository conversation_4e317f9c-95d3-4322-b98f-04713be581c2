// 角色dlg组件

const roleDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    dlgType: '',

    roleId: '',

    roleName: '',

  },

  getters: {
    dlgShow: state => state.dlgShow,

    dlgType: state => state.dlgType,

    roleId: state => state.roleId,

    roleName: state => state.roleName,

  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val
    },

    SET_ROLEID: (state, val) => {
      state.roleId = val
    },

    SET_ROLENAME: (state, val) => {
      state.roleName = val
    },

  },

  actions: {

  }
}

export default roleDlg
