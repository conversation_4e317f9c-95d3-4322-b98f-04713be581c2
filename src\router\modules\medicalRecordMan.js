import Layout from "@/views/layout/Layout";

const medicalRecordRouter = {
  path: "/medicalRecordMan",
  component: Layout,
  name: "medicalRecordMan",
  meta: {
    title: "病历办理",
    icon: "blbl",
    roles: ["suifangguanli"]
  },
  children: [
    {
      path: "businessHan",
      component: () => import("@/views/medicalRecord/businessHan/index"),
      meta: {
        title: "业务办理",
        roles: ["suifangguanli"]
      },
      children: [
        {
          path: "postAudit",
          component: () =>
            import("@/views/medicalRecord/businessHan/postAudit"),
          name: "邮寄审核",
          meta: {
            title: "邮寄审核",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "medicalRecordMailing",
          component: () =>
            import("@/views/medicalRecord/businessHan/medicalRecordMailing"),
          name: "病历邮寄",
          meta: {
            title: "病历邮寄",
            roles: ["suifangguanli"]
          },
          children: []
        },

        {
          path: "appointReceiveHandle",
          component: () =>
            import("@/views/medicalRecord/businessHan/appointReceiveHandle"),
          name: "预约领取办理",
          meta: {
            title: "预约领取办理",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "appointReceiveConfirm",
          component: () =>
            import("@/views/medicalRecord/businessHan/appointReceiveConfirm"),
          name: "预约领取确认",
          meta: {
            title: "预约领取确认",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "appointReceiveQuery",
          component: () =>
            import("@/views/medicalRecord/businessHan/appointReceiveQuery"),
          name: "预约领取查询",
          meta: {
            title: "预约领取查询",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "goodsManagement",
          component: () =>
            import("@/views/medicalRecord/businessHan/goodsManagement"),
          name: "货位管理",
          meta: {
            title: "货位管理",
            roles: ["suifangguanli"]
          },
          children: []
        }
      ]
    },
    {
      path: "businessMan",
      component: () => import("@/views/medicalRecord/businessMan/index"),
      meta: {
        title: "业务管理",
        roles: ["suifangguanli"]
      },
      children: [
        {
          path: "invalidPaperManagement",
          component: () =>
            import("@/views/medicalRecord/businessMan/invalidPaperManagement"),
          name: "作废纸张管理",
          meta: {
            title: "作废纸张管理",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "printerManagement",
          component: () =>
            import("@/views/medicalRecord/businessMan/printerManagement"),
          name: "打印机盘点",
          meta: {
            title: "打印机盘点",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "dailyCheck",
          component: () =>
            import("@/views/medicalRecord/businessMan/dailyCheck"),
          name: "日结盘点",
          meta: {
            title: "日结盘点",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "dailyDetail",
          component: () =>
            import("@/views/medicalRecord/businessMan/dailyDetail"),
          name: "日结明细",
          meta: {
            title: "日结明细",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "dailySummary",
          component: () =>
            import("@/views/medicalRecord/businessMan/dailySummary"),
          name: "日结汇总",
          meta: {
            title: "日结汇总",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "monthQuery",
          component: () =>
            import("@/views/medicalRecord/businessMan/monthQuery"),
          name: "月结查询",
          meta: {
            title: "月结查询",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "EMSQuery",
          component: () => import("@/views/medicalRecord/businessMan/EMSQuery"),
          name: "EMS月结",
          meta: {
            title: "EMS月结",
            roles: ["suifangguanli"]
          },
          children: []
        }
      ]
    },
    {
      path: "mailHan",
      component: () => import("@/views/medicalRecord/mailHan/index"),
      meta: {
        title: "邮寄办理",
        roles: ["suifangguanli"]
      },
      children: [
        {
          path: "mailReceive",
          component: () => import("@/views/medicalRecord/mailHan/mailReceive"),
          name: "邮寄接收",
          meta: {
            title: "邮寄接收",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "mailExport",
          component: () => import("@/views/medicalRecord/mailHan/mailExport"),
          name: "邮寄导出",
          meta: {
            title: "邮寄导出",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "waitMailQuery",
          component: () =>
            import("@/views/medicalRecord/mailHan/waitMailQuery"),
          name: "待邮查询",
          meta: {
            title: "待邮查询",
            roles: ["suifangguanli"]
          },
          children: []
        }
      ]
    },
    // 客服中心
    {
      path: "customerServiceCenter",
      component: () =>
        import("@/views/medicalRecord/customerServiceCenter/index"),
      meta: {
        title: "客服中心",
        roles: ["suifangguanli"]
      },
      children: [
        {
          path: "orderQuery",
          component: () =>
            import("@/views/medicalRecord/customerServiceCenter/orderQuery"),
          name: "订单查询",
          meta: {
            title: "订单查询",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "mailInformationQuery",
          component: () =>
            import(
              "@/views/medicalRecord/customerServiceCenter/mailInformationQuery"
            ),
          name: "邮寄信息",
          meta: {
            title: "邮寄信息",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "medicalRecordStateQuery",
          component: () =>
            import(
              "@/views/medicalRecord/customerServiceCenter/medicalRecordStateQuery"
            ),
          name: "病案状态",
          meta: {
            title: "病案状态",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "settlementRefundQuery",
          component: () =>
            import(
              "@/views/medicalRecord/customerServiceCenter/settlementRefundQuery"
            ),
          name: "结退查询",
          meta: {
            title: "结退查询",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "exceptionHandling",
          component: () =>
            import(
              "@/views/medicalRecord/customerServiceCenter/exceptionHandling"
            ),
          name: "异常处理",
          meta: {
            title: "异常处理",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "businessAudit",
          component: () =>
            import("@/views/medicalRecord/customerServiceCenter/businessAudit"),
          name: "业务审核",
          meta: {
            title: "业务审核",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "settlementPayment",
          component: () =>
            import(
              "@/views/medicalRecord/customerServiceCenter/settlementPayment"
            ),
          name: "结算补缴",
          meta: {
            title: "结算补缴",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "settlementRefund",
          component: () =>
            import(
              "@/views/medicalRecord/customerServiceCenter/settlementRefund"
            ),
          name: "结算退款",
          meta: {
            title: "结算退款",
            roles: ["suifangguanli"]
          },
          children: []
        }
      ]
    },
    {
      path: "financialMan",
      component: () => import("@/views/medicalRecord/financialMan/index"),
      meta: {
        title: "财务管理",
        roles: ["suifangguanli"]
      },
      children: [
        {
          path: "EMSQuery",
          component: () =>
            import("@/views/medicalRecord/financialMan/EMSQuery"),
          name: "EMS查询",
          meta: {
            title: "EMS查询",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "dailySummary",
          component: () =>
            import("@/views/medicalRecord/financialMan/dailySummary"),
          name: "日结汇总",
          meta: {
            title: "日结汇总",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "dailyDetail",
          component: () =>
            import("@/views/medicalRecord/financialMan/dailyDetail"),
          name: "日结明细",
          meta: {
            title: "日结明细",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "refundQuery",
          component: () =>
            import("@/views/medicalRecord/financialMan/refundQuery"),
          name: "已退查询",
          meta: {
            title: "已退查询",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "jhPaymentQuery",
          component: () =>
            import("@/views/medicalRecord/financialMan/jhPaymentQuery"),
          name: "聚合支付查询",
          meta: {
            title: "聚合支付查询",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "jhReconciliationQuery",
          component: () =>
            import("@/views/medicalRecord/financialMan/jhReconciliationQuery"),
          name: "聚合对账查询",
          meta: {
            title: "聚合对账查询",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "wxPaymentQuery",
          component: () =>
            import("@/views/medicalRecord/financialMan/wxPaymentQuery"),
          name: "微信支付查询",
          meta: {
            title: "微信支付查询",
            roles: ["suifangguanli"]
          },
          children: []
        },
        {
          path: "payRecorder",
          component: () =>
            import("@/views/medicalRecord/financialMan/payRecorder"),
          name: "对账查询",
          meta: {
            title: "对账查询",
            roles: ["suifangguanli"]
          },
          children: []
        }
      ]
    }
  ]
};

export default medicalRecordRouter;
