<template>
  <el-dialog :close-on-click-modal="false" :title="'选择工作项'" :before-close="closeDlg" :visible.sync="dlgShow" width="1200px">
    <div class="clear-container clearfix">
      <div class="tree-container">
        <span class="fbold">待选</span>
        <el-tree ref="workItemTree" show-checkbox highlight-current node-key="id" :props="defaultProps" @check="treeCheck" :check-strictly="true" :default-expanded-keys="defaultOpenList" :data="list" :filter-node-method="filterNode" @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" :expand-on-click-node="false">
        </el-tree>
      </div>
      <div class="table-container">
      <div>
        <span class="fbold cBlue">已选</span>
        <el-table ref="tableBar" class="m-small-table" :data="selectTableList" border fit highlight-current-row>
          <el-table-column type="index" label="序号" align="center" width="50"> </el-table-column>
          <el-table-column label="工作项" align="center" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span>{{ scope.row.rulesName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" align="center" width="180">
            <template slot-scope="scope">
              <span>第{{ scope.row.beginDays || '--' }}日</span>
              <el-time-picker :disabled="beginTime != ''" @change="calcBeginTime" v-if="scope.$index == 0" format="HH:mm" value-format="HH:mm" v-model="scope.row.beginTime" placeholder="开始时间" style="width:120px;">
              </el-time-picker>
              <span v-else>{{ scope.row.beginTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束时间" align="center" width="180">
            <template slot-scope="scope">
              <span>第{{ scope.row.endDays || '--' }}日</span>
              <span>{{ scope.row.endTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时长(分钟)" align="center" width="100">
            <template slot-scope="scope">
              <el-input-number @change="calcBeginTime" style="width: 100%" :precision="0" :step="1" :min="1" :controls="false" v-model="scope.row.duration" placeholder="整数"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="规程内容" align="center" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.rulesSettingNum }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="260" align="center">
            <template slot-scope="scope">
              <el-button :disabled="scope.$index == 0" icon="el-icon-arrow-up" type="primary" size="mini" plain @click="upItem(scope.$index)">
                上移
              </el-button>
              <el-button :disabled="scope.$index == selectTableList.length - 1" icon="el-icon-arrow-down" type="primary" size="mini" plain @click="downItem(scope.$index)">
                下移
              </el-button>
              <el-button icon="el-icon-delete" type="danger" size="mini" plain @click="delItem(scope.$index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-close" @click="closeDlg"> 取 消 </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'

import { loadWorkItemsTree, getByWorkIds } from '@/api/qualitySystem/workInstruction.js'

export default {
  components: {},
  data () {
    return {
      filterBranch: '',

      list: [],

      selectKeys: [],

      selectTableList: [],

      defaultOpenList: [], // 默认展开

      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.qualitySystem.workItemTimeDlgMul.dlgShow
      },
      set: function (val) {
        this.$store.commit('qualitySystem/workItemTimeDlgMul/SET_DLGSHOW', val)
      },
    },

    workType: {
      get: function () {
        return this.$store.state.qualitySystem.workItemTimeDlgMul.workType
      },
      set: function (val) {
        this.$store.commit('qualitySystem/workItemTimeDlgMul/SET_WORKTYPE', val)
      },
    },

    workName: {
      get: function () {
        return this.$store.state.qualitySystem.workItemTimeDlgMul.workName
      },
      set: function (val) {
        this.$store.commit('qualitySystem/workItemTimeDlgMul/SET_WORKNAME', val)
      },
    },

    dlgType: {
      get: function () {
        return this.$store.state.qualitySystem.workItemTimeDlgMul.dlgType
      },
      set: function (val) {
        this.$store.commit('qualitySystem/workItemTimeDlgMul/SET_DLGTYPE', val)
      },
    },

    workItemList: {
      get: function () {
        return this.$store.state.qualitySystem.workItemTimeDlgMul.list
      },
      set: function (val) {
        this.$store.commit('qualitySystem/workItemTimeDlgMul/SET_LIST', val)
      },
    },

    beginTime: {
      get: function () {
        return this.$store.state.qualitySystem.workItemTimeDlgMul.beginTime
      },
      set: function (val) {
        this.$store.commit('qualitySystem/workItemTimeDlgMul/SET_BEGINTIME', val)
      },
    },
  },

  watch: {
    filterBranch (val) {
      this.$refs.workItemTree.filter(val)
    },

    dlgShow (val) {
      if (val) {
        this.selectTableList = JSON.parse(JSON.stringify(this.workItemList))
        // 设置第一项开始时间
        if (this.beginTime && this.selectTableList.length > 0) {
          this.selectTableList[0]['beginTimeSeconds'] = this.beginTime
          this.calcBeginTime()
        }
        this.getList()
      }
    },

    beginTime (val) {
    },

    workItemList (val) { },
  },

  created () { },

  methods: {
    // 树节点展开
    handleNodeExpand (data) {
      // 保存当前展开的节点
      let flag = false
      this.defaultOpenList.some((item) => {
        if (item === data.id) {
          // 判断当前节点是否存在， 存在不做处理
          flag = true
          return true
        }
      })
      if (!flag) {
        // 不存在则存到数组里
        this.defaultOpenList.push(data.id)
      }
    },

    // 树节点关闭
    handleNodeCollapse (data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i
        }
      })
    },

    // 递归全选当前下的节点
    selectAllNode (childrenList, type) {
      for (let item of childrenList) {
        // 全选，全部取消
        if (type == 'select') {
          if (!this.selectKeys.includes(item.id)) {
            if (item.state == 0) {
              this.selectKeys.push(item.id)
              this.selectList.push(item)
            }
          }
        } else {
          if (this.selectKeys.includes(item.id)) {
            let mIndex = this.selectKeys.indexOf(item.id)

            this.selectKeys.splice(mIndex, 1)
            this.selectList.splice(mIndex, 1)
          }
        }
        if (item.children) {
          this.selectAllNode(item.children, type)
        }
      }
    },

    // 树节点点击
    treeCheck (checkedNodes, checkedKeys) {
      if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        // select-全选；remove-取消全选
        this.selectAllNode(checkedNodes.children, 'select')
      } else {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        this.selectAllNode(checkedNodes.children, 'remove')
      }
      this.$refs.workItemTree.setCheckedKeys(this.selectKeys)

      let checkNodes = this.$refs.workItemTree.getCheckedNodes()
      if (checkNodes.length > 0) {
        this.getCheckedWork()
      } else {
        this.selectTableList = []
      }
    },

    filterNode (value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },

    setCheckedKeys () {
      this.$nextTick(() => {
        let currentKey = []
        for (let i in this.selectTableList) {
          currentKey.push(parseInt(this.selectTableList[i].workItemId))
        }
        this.selectKeys = currentKey
        this.$refs.workItemTree.setCheckedKeys(currentKey)
      })
    },

    // 删除项
    delItem (idx) {
      this.selectTableList.splice(idx, 1)
      this.setCheckedKeys()
      this.calcBeginTime()
    },

    // 计算开始时间
    calcBeginTime () {
      this.$nextTick(() => {
        for (let i = 0; i < this.selectTableList.length; i++) {
          let item = this.selectTableList[i]
          if (i == 0) {
            if (utils.isNull(this.beginTime)) {
              item['beginTimeSeconds'] = utils.time2Second(item['beginTime'])
            } else {
              item['beginTimeSeconds'] = item['beginTimeSeconds'] ? item['beginTimeSeconds'] : utils.time2Second(item['beginTime'])
            }
            item['endTimeSeconds'] = utils.calcSecond(item['beginTimeSeconds'], item['duration'])
            let beginTime = utils.second2Time(item['beginTimeSeconds'])
            let endTime = utils.second2Time(item['endTimeSeconds'])
            item['beginTime'] = beginTime.timeStr
            item['beginDays'] = beginTime.days
            item['endTime'] = endTime.timeStr
            item['endDays'] = endTime.days
          } else {
            let prevItem = this.selectTableList[i - 1]
            let beginTimeSeconds = prevItem['endTimeSeconds']
            item['beginTimeSeconds'] = beginTimeSeconds
            item['endTimeSeconds'] = utils.calcSecond(item['beginTimeSeconds'], item['duration'])
            let beginTime = utils.second2Time(item['beginTimeSeconds'])
            let endTime = utils.second2Time(item['endTimeSeconds'])
            item['beginTime'] = beginTime.timeStr
            item['beginDays'] = beginTime.days
            item['endTime'] = endTime.timeStr
            item['endDays'] = endTime.days
          }
          this.selectTableList.splice(i, 1, item)
        }
        this.$forceUpdate()
      })
    },

    // 上移，下移
    onMove (index, dir) {
      let moveComm = (curIndex, nextIndex) => {
        let arr = JSON.parse(JSON.stringify(this.selectTableList))
        arr[curIndex] = arr.splice(nextIndex, 1, arr[curIndex])[0]
        return arr
      }

      if (dir === 1 && index === 0) {
        this.$message.warning('已在顶部！')
      } else if (dir === 0 && index === this.selectTableList.length - 1) {
        this.$message.warning('已在底部！')
      } else {
        let nextIndex = dir === 1 ? index - 1 : index + 1
        if (dir === 1 && index === 1) {
          // 休息项不能移动到顶部
          if (this.selectTableList[index].type == 1) {
            this.$message.warning('休息项不能移动到第一项')
            return
          }
        }
        this.selectTableList = moveComm(index, nextIndex)
        this.calcBeginTime()
        this.$forceUpdate()
      }
    },

    // 上移
    upItem (idx) {
      this.onMove(idx, 1)
    },

    // 下移
    downItem (idx) {
      this.onMove(idx, 0)
    },

    // 批量获取工作项内容
    getCheckedWork () {
      let workIdList = []
      let workNameList = []
      for (let i of this.selectList) {
        if (i.type == 1) {
          workIdList.push(i.id)
          workNameList.push(i.name)
        }
      }
      if (workIdList.length == 0) {
        return
      }

      let postParam = {
        workIds: workIdList.join(','),
        workNames: workNameList.join(','),
        workName: this.workName,
        work: this.workType,
      }
      getByWorkIds(postParam).then((res) => {
        if (res.data.code == 200) {
          if (this.selectList.length == 0) {
            return
          }
          let list = res.data.data
          // 没有的添加
          let selIdList = this.selectTableList.map((item) => item.workItemId)
          for (let i of list) {
            if (!selIdList.includes(i.workItemId)) {
              this.selectTableList.push(i)
            }
          }
          // 列表有的删除
          let idList = list.map((item) => item.workItemId)
          for (let i = this.selectTableList.length - 1; i >= 0; i--) {
            if (!idList.includes(this.selectTableList[i]['workItemId'])) {
              this.selectTableList.splice(i, 1)
            }
          }
          // 设置第一项开始时间
          if (this.beginTime && this.selectTableList.length > 0) {
            this.selectTableList[0]['beginTimeSeconds'] = this.beginTime
            this.calcBeginTime()
          }
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 递归
    recursiveList (list) {
      let fn = (list) => {
        for (let i of list) {
          i.disabled = i.state == 1
          if (i.children.length > 0) {
            fn(i.children)
          }
        }
      }
      fn(list)
    },

    // 获取树列表
    getList () {
      this.list = []
      let postParam = {
        name: this.workName,
        value: this.workType,
      }
      loadWorkItemsTree(postParam).then((res) => {
        if (res.data.code == 200) {
          this.recursiveList(res.data.data)
          this.list = JSON.parse(JSON.stringify(res.data.data))
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = [res.data.data[0].id]
          }
          this.setCheckedKeys()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    validateDlg () {
      for (let i = 0; i < this.selectTableList.length; i++) {
        if (utils.isNull(this.selectTableList[i].beginTime) || utils.isNull(this.selectTableList[i].endTime)) {
          this.$message.warning(`请填写第${i + 1}项工作项${this.selectTableList[i].rulesName}的开始结束时间`)
          return false
        }
        if (utils.isNull(this.selectTableList[i].duration)) {
          this.$message.warning(`请填写第${i + 1}项工作项${this.selectTableList[i].rulesName}的工作时长`)
          return false
        }
      }
      return true
    },

    subDlg () {
      if (this.selectTableList.length == 0) {
        this.$message.warning('请选择工作项')
        return
      }
      if (!this.validateDlg()) {
        return
      }
      this.$store.commit('qualitySystem/workItemTimeDlgMul/SET_LIST', JSON.parse(JSON.stringify(this.selectTableList)))
      this.closeDlg()
    },

    closeDlg () {
      this.$store.commit('qualitySystem/workItemTimeDlgMul/SET_BEGINTIME', '')
      this.$store.commit('qualitySystem/workItemTimeDlgMul/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 660px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

.clear-container {
  height: 100%;
  .tree-container {
    float: left;
    width: 180px;
    height: 100%;
    .el-tree {
      background: #f2f2f2;
      height: calc(100% - 26px);
      margin-top: 10px;
      overflow: auto;
      padding-top: 10px;
    }

    /deep/ .el-tree > .el-tree-node {
      display: inline-block;
      min-width: 100%;
    }
  }

  .table-container {
    float: right;
    width: calc(100% - 200px);
    height: 100%;
    .el-table {
      margin-top: 10px;
    }
  }
}
</style>