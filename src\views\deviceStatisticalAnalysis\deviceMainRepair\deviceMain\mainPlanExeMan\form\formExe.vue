<template>
  <div>
    <el-form-item label="保养内容" prop="maintenanceItem">
      <!-- <el-button
        @click="addItem('bynr')"
        :disabled="dlgType !== 'add'"
        icon="el-icon-plus"
        type="primary"
        plain
        >添加</el-button
      > -->
      <el-table
        class="mt10"
        :data="dlgData.maintenanceItem"
        fit
        border
        highlight-current-row
      >
        <el-table-column label="#" type="index" align="center" width="60">
        </el-table-column>
        <el-table-column label="保养项" width="130">
          <template slot-scope="scope">
            <div v-if="dlgType != 'add'">{{ scope.row.name }}</div>
            <el-input v-else v-model="scope.row.name" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column label="保养内容及要求">
          <template slot-scope="scope">
            <div v-if="dlgType != 'add'">{{ scope.row.value }}</div>
            <el-input v-else v-model="scope.row.value" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column
          v-if="dlgType == 'add'"
          label="操作"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              @click="delItem('bynr', scope.$index)"
              icon="el-icon-delete"
              size="mini"
              type="danger"
              title="删除"
              plain
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
    <el-form-item label="执行情况" prop="invokeStatus">
      <!-- 0待执行 1已执行 -->
      <!-- invokeStatus
             invokeStatusStr -->
      <el-radio-group v-model="dlgData.invokeStatus" :disabled="dlgType!='add' && dlgData.invokeStatus == '1'" @change="radioChange">
        <el-radio v-for="item of axztSelect" :key="item.id" :label="item.id+''">{{
          item.name
        }}</el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- <el-form-item
      v-if="dlgData.invokeStatus == '1'"
      label="执行情况备注"
      prop="invokeStatusInfo"
    >
      <el-input
        :autosize="{ minRows: 4, maxRows: 5 }"
        v-model="dlgData.invokeStatusInfo"
        type="textarea"
        placeholder="请输入"
        style="width: 100%"
        @input="changeInput($event)"
      />
    </el-form-item> -->

    <el-form-item label="更换配件" prop="accessoryJson" v-if="dlgData.invokeStatus == '1'">
      <!-- <el-button
        @click="addItem('ghpj')"
        icon="el-icon-plus"
        type="primary"
        plain
        >添加</el-button
      > -->
      <el-table
        class="mt10"
        :data="dlgData.accessoryJson"
        fit
        border
        highlight-current-row
      >
        <el-table-column label="#" type="index" align="center" width="60">
        </el-table-column>
        <el-table-column label="选项">
          <template slot-scope="scope">
            <div v-if="dlgType == 'info'">{{ scope.row.option }}</div>

            <el-select
              v-else
              v-model="scope.row.optionId"
              filterable
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item of optionSelect"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="单位" width="130">
          <template slot-scope="scope">
            <div v-if="dlgType == 'info'">{{ scope.row.unit }}</div>
            <el-input v-else v-model="scope.row.unit" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column label="数量" width="140">
          <template slot-scope="scope">
            <div v-if="dlgType == 'info'">{{ scope.row.num }}</div>

            <el-input-number
              v-else
              v-model="scope.row.num"
              :min="0"
              placeholder="请输入"
              controls-position="right"
              style="width: 110px"
            />
          </template>
        </el-table-column>

        <el-table-column
          v-if="dlgType != 'info'"
          label="操作"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              @click="delItem('ghpj', scope.$index)"
              icon="el-icon-delete"
              size="mini"
              type="danger"
              title="删除"
              plain
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>

    <el-form-item label="执行人" prop="invokeUserName" v-if="dlgData.invokeStatus == '1'">
      <!-- invokeUserId   invokeUserName -->
      <el-input
        v-model="dlgData.invokeUserName"
        :title="dlgData.invokeUserName"
        @focus="showUserTree"
        placeholder="请选择"
      />
    </el-form-item>

    <el-form-item label="备注" prop="info">
      <el-input
        :autosize="{ minRows: 4, maxRows: 5 }"
        v-model="dlgData.info"
        type="textarea"
        placeholder="请输入"
        style="width: 100%"
      />
    </el-form-item>

    <el-form-item label="执行图片" prop="fileUrl">
      <div v-if="dlgData.fileUrl.length === 0">无</div>
      <el-image
        v-else
        v-for="(item, index) in dlgData.fileUrl"
        :key="index"
        style="width: 100px; height: 100px; margin-right: 10px"
        :src="item"
        :preview-src-list="dlgData.fileUrl"
      >
      </el-image>
    </el-form-item>
    <el-form-item label="计划图片" prop="picUrl">
      <div v-if="dlgData.picUrl.length === 0">无</div>
      <el-image
        v-else
        v-for="(item, index) in dlgData.picUrl"
        :key="index"
        style="width: 100px; height: 100px; margin-right: 10px"
        :src="item"
        :preview-src-list="dlgData.picUrl"
      >
      </el-image>
    </el-form-item>
    <Usertree :isRole="false" dlgType2="deviceUser"/>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { postAction, getAction, putAction } from "@/api";
import qiniuUpload from "@/views/greenMan/components/qiniuUpload";
import Usertree from "@/components/Dialog/Usertree"; // 员工弹窗
export default {
  props: {
    value: {
      type: Object,
      default: {}
    },
    axztSelect: {
      type: Array,
      default: []
    },
    optionSelect: {
      type: Array,
      default: []
    },
    dlgType: {
      type: String,
      default: "add"
    },
    isZhixing: {
      type: Boolean,
      default: false
    },
  },
  components: {
    qiniuUpload,
    Usertree
  },
  data() {
    return {
      deviceSelect: []
    };
  },
  computed: {
    dlgData: {
      get: function() {
        return this.value;
      },
      set: function(val) {
        this.$emit("input", val);
      }
    },
    ...mapGetters([
      // 员工
      "userTreeGet"
    ])
  },
  watch:{
    userTreeGet(val) {
      console.log("===返回的啥", val);
      if (val == "empty") {
        return false;
      }

      let list = JSON.parse(val);
      let idArr = [];
      let nameArr = [];
      for (let item of list) {
        idArr.push(item.id);
        nameArr.push(item.label);
      }
      this.dlgData.invokeUserId = idArr.join(",");
      this.dlgData.invokeUserName = nameArr.join(",");

      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    }
  },

  methods: {
    // changeInput(){
    //   this.$forceUpdate()
    // },
    radioChange(val){
      if(val === '0' && this.dlgType == 'add'){
        // this.dlgData.invokeStatusInfo = ""
        this.dlgData.accessoryJson = [{ option: "", optionStr: "", unit: "", num: undefined }]
        this.dlgData.invokeUserName = null
      }
    },
    successBack(fileList) {
      this.dlgData.fileUrl = fileList;
    },
    successBack1(fileList) {
      this.dlgData.picUrl = fileList;
    },
    showUserTree() {
      this.$store.commit("SET_USERTREE_ISS", true);
      this.$store.commit("SET_USERTREE_DIATYPE", "");
      this.$store.commit("SET_USERTREESQTYPE", "");
      this.$store.commit("SET_USERTREESTATE", true);
    },
    addItem(type) {
      if (type == "bynr") {
        this.dlgData.maintenanceItem.push({
          name: "",
          value: ""
        });
      }
      if (type == "ghpj") {
        this.dlgData.accessoryJson.push({
          option: "",
          optionStr: "",
          unit: "",
          num: undefined
        });
      }

      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    },

    delItem(type, index) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        if (type == "bynr") {
          this.dlgData.maintenanceItem.splice(index, 1);
        }
        if (type == "ghpj") {
          this.dlgData.accessoryJson.splice(index, 1);
        }

        this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
