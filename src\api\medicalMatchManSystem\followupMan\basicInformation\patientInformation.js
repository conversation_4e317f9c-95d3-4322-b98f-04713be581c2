import request from '@/utils/request'
import { requestExcel } from '@/utils'

/*
*就诊信息
*/

// 动态查询就诊信息 分页 
export function findSickUserDynamic(data) 
{
	return request({
		url: `/follow/findSickUserDynamic`,
		method: 'post',
		data
	})
}

// 诊
export function updateSickUser(data)
{
	return request({
		url: `/follow/updateSickUser`,
		method: 'post',
		data
	})
}

// 新增/修改就诊信息接口
export function saveOrUSickUser(data)
{
	return request({
		url: `/follow/saveOrUSickUser`,
		method: 'post',
		data
	})
}

// 导入
export function importExcelSickUser(data)
{
	return requestExcel('/follow/importExcelSickUser', data)
}




