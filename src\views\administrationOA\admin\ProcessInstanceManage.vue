<template>
  <div class="mazhenguo app-container">
    <div class="filter-container">
      <el-form inline :model="params" @submit.native.prevent>
        <el-form-item label="发起人:">
          <el-input
            @focus="showUserDlg"
            v-model="params.startUserName"
            placeholder="请选择"
            readonly
          >
            <i
              slot="suffix"
              @click="resetSearchItem(['startUserId', 'startUserName'])"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>

        <el-form-item label="单据类型:">
          <el-cascader
            style="width: 200px"
            v-model="params.formId"
            :options="formTypeList"
            :props="{
              expandTrigger: 'hover',
              children: 'items',
              value: 'id',
              label: 'name'
            }"
            @change="searchFunc"
            collapse-tags
            clearable
            filterable
          ></el-cascader>
        </el-form-item>
        <el-form-item label="发起时间:">
          <el-date-picker
            style="width: 230px"
            v-model="params.dateRange"
            @change="searchFunc"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchFunc"
          >搜索</el-button
        >

        <el-button @click="exportFunc" icon="el-icon-download" size="mini"
          >导出</el-button
        >
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      height="calc(100vh - 308px)"
      :data="dataList"
      :header-cell-style="{ background: '#e8e8e8' }"
      style="width: 100%; "
      @row-click="showProcess"
    >
      <el-table-column
        prop="processDefName"
        label="审批类型"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="staterUser"
        show-overflow-tooltip
        label="发起人"
        min-width="100px"
      >
        <template slot-scope="scope">
          {{ scope.row.staterUser ? scope.row.staterUser.name : "" }}
          <!-- <avatar
            :size="35"
            :name="scope.row.staterUser.name"
            :src="scope.row.staterUser.avatar"
          /> -->
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="startTime"
        label="提交时间"
        min-width="120px"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="finishTime"
        label="结束时间"
        min-width="120px"
      ></el-table-column>
      <el-table-column show-overflow-tooltip prop="taskName" label="当前节点">
      </el-table-column>
      <el-table-column prop="status" label="审批状态">
        <template slot-scope="scope">
          <el-tag
            type="primary"
            size="medium"
            v-if="scope.row.status === 'RUNNING'"
            >进行中</el-tag
          >
          <el-tag
            type="danger"
            size="medium"
            v-else-if="scope.row.result === 'refuse-end'"
            >审批驳回</el-tag
          >
          <el-tag
            type="info"
            size="medium"
            v-else-if="scope.row.result === 'cancel-end'"
            >已撤销</el-tag
          >
          <el-tag type="success" size="medium" v-else>审批通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="duration"
        label="已耗时"
        min-width="120px"
      >
        <template slot-scope="scope">
          {{ getDuration(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="90" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click.stop="delInstance(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right" class="mt10">
      <!-- hide-on-single-page -->
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="params.pageSize"
        :current-page.sync="params.pageNo"
        @current-change="getSubmittedList"
      ></el-pagination>
    </div>
    <el-drawer
      :size="isMobile ? '100%' : '500px'"
      direction="rtl"
      title="审批详情"
      :visible.sync="processVisible"
    >
      <instance-preview
        v-if="processVisible"
        :node-id="selectInstance.nodeId"
        :instance-id="selectInstance.instanceId"
        @handler-after="handlerAfter"
      ></instance-preview>
    </el-drawer>
    <selectUserDlg
      :dlgState0="dlgUserState"
      :dlgData0="dlgUserData"
      :dlgType="dlgUserType"
      :dlgQuery="dlgUserQuery"
      @closeDlg="closeUserDlg"
      @dlgUserSubFunc="dlgUserSubFunc"
    />
  </div>
</template>

<script>
import taskApi from "@/api/workFlow/processTask";
import { delProcessInst } from "@/api/workFlow/process";
import moment from "moment";
import InstancePreview from "../workspace/approval/ProcessInstancePreview";
import { getGroupModelsByUser } from "@/api/workFlow/modelGroup";
import selectUserDlg from "@/components/Dialog2/selectUserDlg";
import * as utils from "@/utils";

// http://*************:11002/saapi/ws/wflow/process/submittedList?pageSize=10&pageNo=1&code=
let paramsEmpty = {
  pageSize: 20,
  pageNo: 1,
  code: "",

  // label: "",
  startUserId: "",
  startUserName: "",
  formId: "", // 单据类型
  dateRange: []
  // beginDate
  // endDate
};

export default {
  name: "ProcessInstancePreview",
  components: { InstancePreview, selectUserDlg },
  data() {
    return {
      total: 0,
      params: {
        pageSize: 10,
        pageNo: 1,
        finished: null,
        code: ""
      },
      selectInstance: "",
      loading: false,
      processVisible: false,
      formList: [],
      dataList: [],
      formTypeList: [],
      dataList: [],
      dlgUserQuery: {},
      dlgUserState: false,
      dlgUserType: "", // 弹框状态add, edit
      dlgUserData: {}
    };
  },
  mounted() {
    this.params = JSON.parse(JSON.stringify(paramsEmpty));

    this.getFormTypeList();
    this.searchFunc();
  },
  computed: {
    isMobile() {
      return window.screen.width < 450;
    }
  },
  methods: {
    showUserDlg() {
      this.dlgUserQuery = "";
      this.dlgUserState = true;
    },
    // 关闭弹窗
    closeUserDlg() {
      this.dlgUserState = false;
    },
    // 选择员工返回
    dlgUserSubFunc(data) {
      console.log("车辆返回", data);
      if (utils.isNull(data)) return false;
      this.params.startUserId = data.id;
      this.params.startUserName = data.label;
      this.searchFunc();
    },
    getFormTypeList() {
      getGroupModelsByUser()
        .then(rsp => {
          this.loading = false;
          let list = rsp.data;
          for (let item of list) {
            if (item.items && item.items.length > 0) {
              for (let item2 of item.items) {
                item2.id = item2.formId;
                item2.name = item2.formName;
              }
            }
          }

          console.log("===list", list);
          this.formTypeList = list;
        })
        .catch(err => {
          this.loading = false;
          this.$err(err, "获取分组异常");
          this.recentlyUsed.length = 0;
        });
    },
    searchFunc() {
      this.params.pageNo = 1;
      this.getSubmittedList();
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.params[item] = "";
      }

      this.searchFunc();
    },

    getSubmittedList() {
      let sendObj = JSON.parse(JSON.stringify(this.params));
      if (sendObj.dateRange && sendObj.dateRange.length > 0) {
        sendObj.beginDate = sendObj.dateRange[0];
        sendObj.endDate = sendObj.dateRange[1];
      } else {
        sendObj.beginDate = "";
        sendObj.endDate = "";
      }
      delete sendObj.dateRange;

      if (sendObj.formId && sendObj.formId.length > 0) {
        sendObj.formId = sendObj.formId[1];
      } else {
        sendObj.formId = "";
      }
      sendObj.startUser = sendObj.startUserId;

      delete sendObj.startUserId;
      delete sendObj.startUserName;

      this.loading = true;
      taskApi
        .getSubmittedList(sendObj)
        .then(rsp => {
          this.loading = false;
          this.total = rsp.data.total;
          this.dataList = rsp.data.records;
        })
        .catch(e => {
          this.loading = false;
        });
    },
    exportFunc() {
      let sendObj = JSON.parse(JSON.stringify(this.params));
      if (sendObj.dateRange && sendObj.dateRange.length > 0) {
        sendObj.beginDate = sendObj.dateRange[0];
        sendObj.endDate = sendObj.dateRange[1];
      } else {
        sendObj.beginDate = "";
        sendObj.endDate = "";
      }
      delete sendObj.dateRange;

      if (sendObj.formId && sendObj.formId.length > 0) {
        sendObj.formId = sendObj.formId[1];
      } else {
        sendObj.formId = "";
      }
      sendObj.startUser = sendObj.startUserId;

      delete sendObj.startUserId;
      delete sendObj.startUserName;

      let loading = this.$loading({
        lock: true,
        text: "导出中...",
        background: "rgba(0, 0, 0, 0.7)"
      });

      taskApi
        .exportSubmittedList(sendObj)
        .then(rsp => {
          loading.close();
          let url = rsp.data;
          console.log("===导出url", url);
          url = url.replace(/\s/g, "");
          console.log("===url", url);
          window.open(url);
        })
        .catch(e => {
          loading.close();
        });
    },
    showProcess(row) {
      this.processVisible = true;
      this.selectInstance = row;
    },
    delInstance(row) {
      this.$confirm(
        "删除后将失去该流程实例所有数据且无法恢复，是否继续 ?",
        "警告",
        {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(() => {
        delProcessInst(row.instanceId)
          .then(rsp => {
            this.getSubmittedList();
            this.$ok(rsp, "删除流程实例成功");
          })
          .catch(err => {
            this.$err(err, "删除流程实例失败");
          });
      });
    },
    getDuration(row) {
      let end = this.$isNotEmpty(row.finishTime)
        ? row.finishTime
        : moment().format("YYYY-MM-DD HH:mm:ss");
      return this.$timeCoverStr(row.startTime, end);
    },
    handlerAfter() {
      this.processVisible = false;
      this.getSubmittedList();
    }
  }
  // watch: {
  //   params: {
  //     deep: true,
  //     handler() {
  //       this.getSubmittedList();
  //     }
  //   }
  // }
};
</script>

<style scoped></style>
