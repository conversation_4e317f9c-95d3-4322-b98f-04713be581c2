// 选择业务范围dlg组件

const businessScopeDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    list: [],

    workItem: {},

    workType: '',

    projectId: '',

    projectName: '',

    beginTime: ''

  },

  getters: {
    dlgShow: state => state.dlgShow,

    list: state => state.list,

    workItem: state => state.workItem,

    workType: state => state.workType,

    projectId: state => state.projectId,

    projectName: state => state.projectName,

    beginTime: state => state.beginTime

  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_LIST: (state, val) => {
      state.list = val
    },

    SET_WORKITEM: (state, val) => {
      state.workItem = val
    },

    SET_WORKTYPE: (state, val) => {
      state.workType = val
    },

    SET_PROJECTID: (state, val) => {
      state.projectId = val
    },

    SET_PROJECTNAME: (state, val) => {
      state.projectName = val
    },

    SET_BEGINTIME: (state, val) => {
      state.beginTime = val
    }

  },

  actions: {

  }
}

export default businessScopeDlgMul
