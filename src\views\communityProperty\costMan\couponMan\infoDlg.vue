<template>
  <!-- 
    表单少： 已用数量
    表格少字段 使用房屋，使用日期 -->
  <!-- 弹窗 新增/编辑 -->
  <el-dialog
    class="mazhenguo"
    title="优惠券详情"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="900px"
    top="30px"
  >
    <el-form
      ref="dlgDataForm"
      :rules="dlgRules"
      :model="dlgData"
      label-position="right"
      label-width="110px"
      style="width: 860px"
      size="mini"
      @submit.native.prevent
    >
      <!-- <el-row>
        <el-col :span="8"> </el-col>
        <el-col :span="8"> </el-col>
        <el-col :span="8"> </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="8">
          <el-form-item label="优惠券名称:">
            {{ dlgData.name }}
          </el-form-item>
        </el-col>
        <el-col :span="8"
          ><el-form-item label="优惠券数量:">
            {{ dlgData.total }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="已用数量:">
            {{ dlgData.useNum }}
          </el-form-item></el-col
        >
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="优惠金额:">
            {{ dlgData.discount }}
          </el-form-item>
        </el-col>
        <el-col :span="8"
          ><el-form-item label="所属小区:">
            {{ dlgData.communityName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8"
          ><el-form-item label="截止日期:">
            {{ dlgData.endTime }}
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="收费项目:">
            {{ dlgData.configName }}
          </el-form-item>
        </el-col>
      </el-row>

      <el-table class="" style="width: 100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="#" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="使用房屋">
          <template slot-scope="scope">
            <span v-if="scope.row.roomName">{{ scope.row.roomName }}</span>
            <span v-else-if="scope.row.parkingName">{{ scope.row.parkingName }}</span>
            <span v-else-if="scope.row.garageName">{{ scope.row.garageName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="业主姓名" width="90" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.ownerName == '' || scope.row.ownerName == 'null' ? '' : scope.row.ownerName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="缴费方式">
          <template slot-scope="scope">
            <!-- payWay  1:小程序支付 2:扫码支付 3:现场支付 -->
            <span v-if="scope.row.payWay == '1'">小程序支付</span>
            <span v-else-if="scope.row.payWay == '2'">扫码支付</span>
            <span v-else-if="scope.row.payWay == '3'">现场支付</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="应收金额" prop="receivableAmount" width="100" align="center"> </el-table-column>
        <el-table-column label="实收金额" prop="receivedAmount" width="100" align="center"> </el-table-column>
        <el-table-column label="使用时间" prop="payTime" width="140" align="center"> </el-table-column>
      </el-table>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
import Tinymce from '@/components/Tinymce' // 富文本组件
// 工具
// import { phoneReg } from '@/utils/regUtil'
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
// 接口
import { isNull, getDbItems, arrId2Name, arrIds2Names, batchGetNameById } from '@/utils'
import { postAction, getAction } from '@/api'

let dlgDataEmpty = {
  // 测试数据
}
export default {
  components: {
    Tinymce,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          let dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
          if (this.dlgType != 'add') {
            dlgData = this.dlgQuery
          }
          this.dlgData = JSON.parse(JSON.stringify(dlgData))

          this.getList()
          // this.$nextTick(() => {
          //   this.$refs['dlgDataForm'].clearValidate()
          // })
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      // 弹窗
      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        type: [{ required: true, message: '必填字段', trigger: 'change' }],
        remark: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      dlgSubLoading: false, // 提交loading

      // 表格数据
      listLoading: false,
      list: [],
    }
  },
  created() {},
  methods: {
    isNull(val) {
      return isNull(val)
    },
    // 获取数据
    getList() {
      this.listLoading = true
      // let sendObj = {
      //   aaaa: this.dlgData.aaaa,
      // }
      getAction(`/unity/coupon/info/${this.dlgData.id}`).then((res0) => {
        this.listLoading = false
        let res = res0.data
        if (res.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.feeBillSums))
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /////

    closeDlg() {
      // this.dlgLoading = false
      // this.dlgSubLoading = false
      // this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')

      // this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      // this.$nextTick(() => {
      //   this.$refs['dlgDataForm'].clearValidate()
      // })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>