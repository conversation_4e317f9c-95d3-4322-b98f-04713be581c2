<template>
    <!-- 环境安全 -->
    <div class="app-container">
      <div class="filter-container">
        <el-form inline @submit.native.prevent>
          <el-form-item label="筛选条件:">
            <el-select
              style="width: 180px"
              class="fl ml10"
              @change="getList"
              v-model="listQuery.equipType"
              clearable
              placeholder="请选择分类"
            >
              <el-option
                v-for="item of equipTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="listQuery.equipRoomIds"
              clearable
              multiple
              collapse-tags
              placeholder="请选择设备间"
              style="width: 210px"
            >
              <el-option
                v-for="item of sbjSelect"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            @click="searchFunc"
            >搜索</el-button
          >
          <!-- <el-button class="search-right-btn" @click="downLoadFunc" icon="el-icon-download" size="mini"
              >导出</el-button
            > -->
        </el-form>
      </div>
  
      <div class="table-container">
        <el-table
          class="m-small-table el-table"
          height="100%"
          v-loading="listLoading"
          :data="list"
          border
          fit
          highlight-current-row
          :row-class-name="tableRowClassName"
        >
          <el-table-column label="序号" width="60" align="center">
            <template slot-scope="scope">
              <span>{{
                (listQuery.page - 1) * listQuery.limit + scope.$index + 1
              }}</span>
            </template>
          </el-table-column>
  
          <el-table-column label="报警对象">
            <template slot-scope="scope">
              <!-- <span
                v-if="scope.row.type == 0"
                class="m-a"
                @click="showBjDia(scope.row)"
                >{{ scope.row.nodeName }}</span
              > -->
              <span>{{ scope.row.nodeName }}</span>
            </template>
          </el-table-column>
  
          <el-table-column label="报警类型">
            <template slot-scope="scope">
              <span>{{ scope.row.alertName }}</span>
            </template>
          </el-table-column>
  
          <el-table-column label="区域" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.area }}</span>
            </template>
          </el-table-column>
          <el-table-column label="设备间名称">
            <template slot-scope="scope">
              <span>{{ scope.row.equipRoomName }}</span>
            </template>
          </el-table-column>
  
          <el-table-column label="设备名称">
            <template slot-scope="scope">
              <span>{{ scope.row.equipName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="分类" align="center" prop="equipTypeStr" />
          <el-table-column label="报警时间" align="center" prop="createTime" />
          <el-table-column label="报警内容" prop="content" />
        </el-table>
      </div>
  
      <div class="page-container">
        <pagination
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          @pagination="getList"
        />
      </div>
  
      <!-- << 弹窗 报警信息 -->
      <el-dialog
        title="历史记录"
        :close-on-click-modal="false"
        :append-to-body="true"
        :visible.sync="diaBjState"
        width="1200px"
        top="30px"
        icon-class="el-icon-info"
      >
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="列表" name="liebiao">
            <el-date-picker
              style="width: 300px"
              class="fl"
              @change="getBjList"
              v-model="diaBjQuery.dateRange"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
            <el-button
              icon="el-icon-search"
              type="success"
              size="mini"
              class="search-right-btn fl"
              @click="getBjList"
              >搜索</el-button
            >
  
            <div class="clear"></div>
  
            <el-table
              ref="tableBar"
              class="m-small-table mt10"
              v-loading="listLoading"
              :key="diaBjTableKey"
              :data="diaBjList"
              border
              fit
              highlight-current-row
              style="width: 100%"
              max-height="500px"
            >
              <el-table-column label="#" type="index" align="center" width="70">
                <template slot-scope="scope">
                  <span>{{
                    (diaBjQuery.page - 1) * diaBjQuery.limit + scope.$index + 1
                  }}</span>
                </template>
              </el-table-column>
  
              <el-table-column
                v-for="(item, index) of diaBjTHList"
                :key="index"
                :label="item.label"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row[item.key] }}</span>
                </template>
              </el-table-column>
            </el-table>
  
            <!-- 分页 -->
            <pagination
              class="mt10"
              v-show="diaBjTotal > 0"
              :total="diaBjTotal"
              :page.sync="diaBjQuery.page"
              :limit.sync="diaBjQuery.limit"
              @pagination="getBjList"
            />
          </el-tab-pane>
          <el-tab-pane label="趋势" name="qushi">
            <el-date-picker
              class="fl ml10"
              style="width: 350px"
              @change="getZxt"
              v-model="zxtQuery.dateRange"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              start-placeholder="开始日期"
              end-placeholder="截止日期"
              size="mini"
            >
            </el-date-picker>
            <el-select
              @change="handleSelectChange"
              class="fl ml10"
              style="width: 200px"
              v-model="zxtQuery.disRespVos"
              multiple
              collapse-tags
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.type"
                :label="item.name"
                :value="`${item.type},${item.name}`"
              >
              </el-option>
            </el-select>
            <el-button
              icon="el-icon-search"
              type="success"
              size="mini"
              class="search-right-btn fl"
              @click="getZxt"
              >搜索</el-button
            >
            <div class="clear"></div>
  
            <div
              v-if="showChart2"
              id="echart-bar2"
              style="height: 500px; margin-top: 16px"
            ></div>
          </el-tab-pane>
        </el-tabs>
        <div class="clear"></div>
  
        <div slot="footer" class="dialog-footer">
          <el-button @click="diaBjState = false" icon="el-icon-back"
            >取消</el-button
          >
        </div>
      </el-dialog>
      <Usertree />
    </div>
  </template>
  
  <script>
  import { mapGetters } from "vuex";
  import Cookie from "js-cookie";
  
  import { regionTree } from "@/api/basicManSystem/comprehenOperateMonitor.js"; //查区域
  import Usertree from "@/components/Dialog/Usertree";
  import Pagination from "@/components/Pagination"; // 分页
  import * as utils from "@/utils";
  import {
    arrId2Name, // 根据id 获取name
    isNull,
    getDataDictOther // 数据字典
  } from "@/utils";
  import * as echarts from "echarts";
  import moment, { localeData } from "moment"; //导入文件
  import { getAction, postAction } from "../../api";
  // 接口
  import {
    findAbnormalAlarmPage, // 分页
    handleAlarm, // 处理
    ignoreAlarm // 忽略
  } from "@/api/mzgApi";
  
  import {
    findEquioPage, // 设备间
    protocolLoran
  } from "@/api/safetyMonitoringApi";
  import { QNRTPlayer } from "qn-rtplayer-web";
  let player = new QNRTPlayer();
  let dlgDataEmpty = {
    id: "",
    handlerId: "",
    handler: "",
    handlerRemark: "",
    confirmationTime: "",
    status: "",
    handlerHours: "",
    handlerResultStr: "",
    handlerResult: ""
  };
  
  // 报警信息弹窗
  let diaBjQueryEmpty = {
    id: "",
    label: "",
    page: 1,
    limit: 10,
    dateRange: []
  };
  //折线图
  let zxtQueryEmpty = {
    disRespVos: [],
    nodeId: "",
    startTime: "",
    endTime: "",
    dateRange: []
  };
  export default {
    components: {
      Pagination,
      Usertree
    },
    watch: {
      // 用户tree
      userTreeUserId(nVal) {
        if (this.dlgShow) {
          this.dlgData.handlerId = nVal;
  
          //console.log(this.dlgData.chargeLeaderId + "22222222222222");
          // this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
        }
      },
      userTreeUserName(nVal) {
        if (this.dlgShow) {
          this.dlgData.handler = nVal;
          // this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
        }
      }
    },
    computed: {
      ...mapGetters([
        // 部门
        "bmTreeBranchId",
        "bmTreeBranchName"
      ]),
      // 部门选人相关
      userTreeState: {
        get: function() {
          let userTreeState = this.$store.getters.userTreeState;
          return userTreeState;
        },
        set: function(newVal) {
          // this.$store.commit('SET_USERTREESTATE', newVal)
        }
      },
      userTreeUserId: {
        get: function() {
          let userTreeUserId = this.$store.getters.userTreeUserId;
          return userTreeUserId;
        },
        set: function(newVal) {
          // this.$store.commit('SET_USERTREEUSERID', newVal)
        }
      },
      userTreeUserName: {
        get: function() {
          let userTreeUserName = this.$store.getters.userTreeUserName;
          return userTreeUserName;
        },
        set: function(newVal) {
          // this.$store.commit('SET_USERTREEUSERNAME', newVal)
        }
      }
    },
    data() {
      return {
        isDis: false,
        sbjSelect: [], // 设备间
        dlgRules: {
          handlerRemark: [
            { required: true, message: "必填字段", trigger: "change" }
          ],
          handler: [{ required: true, message: "必填字段", trigger: "change" }],
          confirmationTime: [
            { required: true, message: "必填字段", trigger: "change" }
          ],
          handlerResultStr: [
            { required: true, message: "必填字段", trigger: "change" }
          ],
          status: [{ required: true, message: "必填字段", trigger: "change" }],
          handlerHours: [
            { required: true, message: "必填字段", trigger: "change" }
          ]
        },
        equipTypeList: [], //分类
        list: [],
        listQuery: {
          page: 1,
          limit: 20,
          equipRoomIds: "",
          equipType:''
        },
        areaList: [], //区域
  
        srcList: [], //图片
  
        total: 0,
        listLoading: false,
  
        dlgType: "",
        dlgLoading: false,
        dlgShow: false,
        dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
        // << 弹窗-报警信息
        diaBjTableKey: 0,
        diaBjState: false,
        diaBjTHList: [],
        diaBjList: [],
        diaBjTotal: 0,
        diaBjQuery: JSON.parse(JSON.stringify(diaBjQueryEmpty)),
        // >> 弹窗-报警信息
        // << 弹窗 设置计划
        activeName: "liebiao",
        zxtQuery: JSON.parse(JSON.stringify(zxtQueryEmpty)),
        options: [],
        bjDiaRow: {},
        showChart2: false,
        echartRoom2: null,
        zxtSelect: [],
  
        equipmentDrawing: "",
        equipmentDrawingList: [],
        dropDiaXYList: [], // 坐标
        imgHeight: "",
        imgWidth: "",
        iconScale1: 1,
        dropDiaList: [
          { id: 1, name: "用电" },
          { id: 2, name: "环境参数" },
          { id: 23, name: "环境参数23" }
        ], // 传感器列表
        sbImgH: "",
        giveAlarmRow: {}, //报警详情/报警信息
        alarmDetailData: {
          pojo: {}
        },
        userInfo: {},
        //查看大图dlg
        gjSelect: []
      };
    },
    created() {
      this.userInfo = JSON.parse(window.localStorage.userInfo);
      if (this.$route.query.today) {
        this.listQuery.dateRange = [
          this.$route.query.today,
          this.$route.query.today
        ];
      }
      getDataDictOther(this, "iot_equip_type", "equipTypeList"); // 业务分类
      getDataDictOther(this, "gaojingchulijieguo", "gjSelect"); // 报警处理结果
      this.getSbjSelect(); // 获取设备间
      this.getList();
      this.getAreaList();
    },
    methods: {
      //区域
      getAreaList() {
        this.areaList = [];
        let sendObj = {
          page: 1,
          size: 9999
        };
        regionTree(sendObj).then(res => {
          if (res.data.code == "200") {
            this.areaList = this.getTreeData(res.data.data);
          } else {
            this.$message({
              type: "warning",
              message: res.data.msg
            });
          }
        });
      },
      getTreeData(data) {
        for (var i = 0; i < data.length; i++) {
          if (data[i].children.length < 1) {
            data[i].children = undefined;
          } else {
            this.getTreeData(data[i].children);
          }
        }
        return data;
      },
      areaChange(selectedValues) {
        if (selectedValues) {
          if (this.dlgState) {
            this.dlgData.area = this.$refs[
              "refSubCat0"
            ].getCheckedNodes()[0].label; //获取选中name
          } else {
            this.listQuery.equipRoomId = "";
            this.listQuery.equipRoomName = "";
            this.listQuery.area = this.$refs[
              "refSubCat"
            ].getCheckedNodes()[0].label; //获取选中name
          }
        } else {
          this.dlgData.equipRoomId = "";
          this.dlgData.equipRoomName = "";
        }
        this.getSbjSelect();
      },
      //导出
      downLoadFunc() {
        this.listQuery.beginDate = this.listQuery.dateRange
          ? this.listQuery.dateRange[0]
          : "";
        this.listQuery.endDate = this.listQuery.dateRange
          ? this.listQuery.dateRange[1]
          : "";
        let sendObj = JSON.parse(JSON.stringify(this.listQuery));
        delete sendObj.dateRange;
        if (sendObj.equipRoomIds.length > 0) {
          sendObj.equipRoomIds = sendObj.equipRoomIds.join(",");
        } else {
          sendObj.equipRoomIds = "";
        }
        let loading = this.$loading({
          lock: true,
          text: "导出中...",
          background: "rgba(0, 0, 0, 0.7)"
        });
        postAction("/iot/abnormalAlarmExport", sendObj).then(res0 => {
          loading.close();
          window.open(res0.data.data.url);
        });
      },
      radioChange() {
        this.dlgData.handlerId = "";
        this.dlgData.handler = "";
        this.dlgData.handlerRemark = "";
        this.dlgData.confirmationTime = "";
        this.dlgData.handlerHours = "";
        this.dlgData.handlerResultStr = "";
        this.dlgData.handlerResult = "";
        this.$nextTick(() => {
          this.$refs["dlgForm"].clearValidate();
        });
      },
      // 选人弹窗
      showUserTree() {
        this.$store.commit("SET_USERTREESQTYPE", "");
        this.$store.commit("SET_USERTREESTATE", true);
      },
      gjSelectProductType(data) {
        let { value, label } = data;
        this.dlgData.handlerResultStr = label;
        this.dlgData.handlerResult = value;
      },
      getEquipmentDrawing(id) {
        this.equipmentDrawing = "";
        this.equipmentDrawingList = [];
        getAction(`/iot/findListByEquipId/${id}`).then(res1 => {
          let res = res1.data;
          if (res.code == 200) {
            let dropDiaList = res.list;
            if (res.data.imgUrl != null && res.data.imgUrl != "") {
              this.equipmentDrawing = res.data.imgUrl;
              this.equipmentDrawingList = res.data.imgUrl.split(",");
  
              // 坐标 - 并将坐标渲染到画布上
              if (res.data.coordinate == null || res.data.coordinate == "") {
                this.dropDiaXYList = [];
              } else {
                let arr = [];
                let coordinate = JSON.parse(res.data.coordinate);
                let obj = coordinate.find(obj => {
                  return obj.id == this.giveAlarmRow.nodeId;
                });
                if (obj != undefined) {
                  arr.push(obj);
                } else {
                  arr = [];
                }
                // console.log(obj,"obj");
                let dropDiaXYList = (this.dropDiaXYList = arr);
                // this.dropDiaXYList = JSON.parse(JSON.stringify(dropDiaXYList))
              }
              console.log(this.dropDiaXYList, "this.dropDiaXYList");
            } else {
              this.equipmentDrawing = "";
              this.equipmentDrawingList = [];
            }
            let img = new Image();
            img.src = this.equipmentDrawing;
            img.onload = async () => {
              this.imgWidth = 400 / img.width;
              this.imgHeight = 202 / img.height;
              if (img.width / img.height > 400 / 202) {
                this.imgWidth = 400;
                this.imgHeight = (img.height * 400) / img.width;
              } else {
                this.imgWidth = (img.width * 202) / img.height;
                this.imgHeight = 202;
              }
              this.iconScale1 = this.imgWidth / 980;
            };
  
            this.dropDiaList = JSON.parse(JSON.stringify(dropDiaList));
            // 图片高度
            setTimeout(() => {
              this.sbImgH = $(".drop-right-img").height() + 4;
            }, 500);
          } else {
            this.$message({
              type: "warning",
              message: res.msg
            });
          }
          console.log(res, "res");
        });
      },
      //tab切换
      handleClick(tab, event) {
        this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty));
        this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
        this.diaBjQuery.id = this.bjDiaRow.nodeId;
        this.zxtSelect = [];
        console.log(tab, event);
        if (tab.name == "liebiao") {
          this.getBjList();
        } else {
          const now = moment();
          const start = moment(now).subtract(3, "hours");
          const end = moment().format("YYYY-MM-DD HH:mm");
          let startTime = moment(start).format("YYYY-MM-DD HH:mm");
          // let endTime = moment(end).format('YYYY-MM-DD HH:mm');
          this.zxtQuery.dateRange = [startTime, end];
          this.getDxList();
          this.getZxt();
        }
      },
      getDxList() {
        getAction(`/iot/trend/nodeDataDis/${this.bjDiaRow.nodeId}`).then(res1 => {
          let res = res1.data;
          if (res.code == 200) {
            this.options = res.data;
          } else {
            this.$message.error(res.msg);
          }
        });
      },
      handleSelectChange() {
        this.zxtSelect = [];
        this.zxtQuery.disRespVos.forEach(element => {
          console.log(element, "element");
          let [type, name] = element.split(",");
          this.zxtSelect.push({ type, name });
        });
      },
      getZxt() {
        this.showChart2 = false;
        if (
          isNull(this.zxtQuery.dateRange) ||
          this.zxtQuery.dateRange.length <= 0
        ) {
          this.$message.warning("请先选择起止时间");
          return false;
        }
        let sendObj = JSON.parse(JSON.stringify(this.zxtQuery));
        // 日期范围
        sendObj.startTime = "";
        sendObj.endTime = "";
        if (
          !isNull(this.zxtQuery.dateRange) &&
          this.zxtQuery.dateRange.length > 0
        ) {
          sendObj.startTime = this.zxtQuery.dateRange[0];
          sendObj.endTime = this.zxtQuery.dateRange[1];
        }
        sendObj.disRespVos = this.zxtSelect;
        sendObj.nodeId = this.bjDiaRow.nodeId;
        let loading = this.$loading({
          lock: true,
          text: "加载中...",
          background: "rgba(0, 0, 0, 0.7)"
        });
        postAction("/iot/trend/nodeDataTrend", sendObj).then(res1 => {
          loading.close();
          let res = res1.data;
          if (res.code == 200) {
            if (!utils.isNull(res.data) && res.data.list.length > 0) {
              this.showChart2 = true;
              // this.list = res.data;
              setTimeout(() => {
                this.setEchartBar2(res.data.list, res.data.times);
                //   this.createRoom(res.data.list)
              }, 100);
            }
          }
        });
      },
      //创建折线图
      setEchartBar2(arr, dataMap) {
        console.log(arr, "arr");
        if (this.showChart2 == false) {
          // if (!utils.isNull(arr)) {
          //   this.echartRoom2.clear();
          // }
          return;
        }
        // << 本月1号到当天
        let xList = [];
        let xList0 = [];
        // let dateObj = new Date();
        // console.log("dateObj.getDate()", dateObj.getDate());
        // console.log(this.getEveryDayDateByBetweenDate(this.listQuery.dateRange[0],this.listQuery.dateRange[1]),'时间间隔');
        // let dayNum = parseInt(dateObj.getDate());
        // let month = utils.return2Num2(dateObj.getMonth() + 1);
        // let year = dateObj.getFullYear();
        // for (let i = 0; i < dayNum; i++) {
        //   let key = `${year}-${month}-${utils.return2Num2(i + 1)}`;
        //   xList.push(key);
        //   xList0.push(`${year}-${month}-${utils.return2Num2(i + 1)}`);
        // }
  
        // 拼接数据
        let data = [];
        let listData = [];
        for (let index = 0; index < dataMap.length; index++) {
          let obj = {
            yearMonthDate: dataMap[index],
            count: 0,
            type: ""
          };
          listData.push(obj);
        }
        for (let i = 0; i < arr.length; i++) {
          let itemLine = arr[i];
          let lineObj = {
            name: itemLine.name,
            type: "line",
            stack: "",
            data: []
          };
          let map = itemLine.list;
          // console.log('111111111map', map)
          // console.log('111111111listData', listData)
          for (let key = 0; key < map.length; key++) {
            for (let k = 0; k < listData.length; k++) {
              if (map[key].time == listData[k].yearMonthDate) {
                lineObj.data.push(map[key].value);
                // listData[k].value = map[key].value;
                // listData[k].type = map[key].type;
              }
            }
          }
          data.push(lineObj);
          // let arrData = [];
          // console.log("==listData", listData);
          // for (let o = 0; o < listData.length; o++) {
          //   arrData.push(listData[o].value);
          // }
          // console.log(arrData, "arrData");
          // lineObj.data = arrData;
          // data.push(lineObj);
        }
        console.log(data, "data--------------");
        // xList0.push(map[key].yearMonthDate)
        xList0 = dataMap;
        // 绘制图标
        var myChart = echarts.init(document.getElementById("echart-bar2"));
        var option = {
          title: {
            text: ""
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross"
            }
          },
  
          legend: {
            left: 10
          },
          grid: {
            left: "2%",
            right: "2%",
            bottom: "2%",
            containLabel: true
          },
          xAxis: {
            type: "category",
            boundaryGap: false, // true-刻度中间 false-刻度线上
            data: xList0
          },
          yAxis: {
            type: "value"
            // name: '单位（吨）',
            // nameTextStyle: {
            //   color: '#aaa',
            //   nameLocation: 'start',
            // },
          },
          series: data
          // series: [[1,2,3],[12,22,32],[13,23,33]]
        };
        myChart.clear();
        myChart.setOption(option);
        myChart.on("click", param => {
          console.log("param", param);
          // // componentIndex
          // // dataIndex
          // let msg = `${this.echartLineData[param.componentIndex].name}：${
          //   this.echartLineData[param.componentIndex].data[param.dataIndex]
          // }`
          // alert(msg)
        });
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },
      showBjDia(row) {
        // 调用接口
        (this.activeName = "liebiao"),
          (this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty)));
        this.diaBjQuery.id = row.nodeId;
        this.bjDiaRow = row;
        this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
        this.getBjList();
      },
      // 获取列表
      getBjList() {
        let beginTime = "";
        let endTime = "";
        if (
          this.diaBjQuery.dateRange != null &&
          this.diaBjQuery.dateRange.length != 0
        ) {
          beginTime = this.diaBjQuery.dateRange[0];
          endTime = this.diaBjQuery.dateRange[1];
        }
        let sendObj = {
          page: this.diaBjQuery.page,
          limit: this.diaBjQuery.limit,
          id: this.diaBjQuery.id,
          beginTime,
          endTime
        };
  
        let loading = this.$loading({
          lock: true,
          text: "加载中...",
          background: "rgba(0, 0, 0, 0.7)"
        });
        protocolLoran(sendObj).then(res1 => {
          loading.close();
          let res = res1.data;
          if (res.code == "200") {
            if (res.data == null) {
              this.diaBjTotal = 0;
              this.$message({
                type: "warning",
                message: "暂无报警信息"
              });
              return false;
            }
            // 表头
            let diaBjTHList = [];
            for (let key in res.data.field) {
              let label = res.data.field[key];
              let obj = {
                key,
                label
              };
              diaBjTHList.push(obj);
            }
            this.diaBjTHList = diaBjTHList;
  
            // 表格数据
            this.diaBjTotal = res.data.total;
  
            this.diaBjList = res.list;
            this.diaBjState = true;
            this.diaBjTableKey++;
          } else {
            this.$message({
              type: "warning",
              message: res.msg
            });
          }
        });
      },
      // 高亮
      tableRowClassName({ row, rowIndex }) {
        if (row.alarmLevel == "3") {
          return "color-warn1";
        } else if (row.alarmLevel == "2") {
          return "color-warn2";
        } else if (row.alarmLevel == "1") {
          return "color-warn3";
        }
        return "";
      },
      // << 获取设备间
      getSbjSelect() {
        let sendObj = {
          label: "",
          page: 1,
          limit: 9999,
          isEquipRoom: 0, // 0设备间 1设备 不传查所有
          areaId: this.listQuery.areaId,
          equipType: this.listQuery.equipType
        };
       
        findEquioPage(sendObj).then(res1 => {
          this.listLoading = false;
          let res = res1.data;
          if (res.code == "200") {
            this.sbjSelect = JSON.parse(JSON.stringify(res.list));
          } else {
            this.$message({
              type: "warning",
              message: res.msg
            });
          }
        });
      },
      // >> 获取设备间
      getList() {
        this.list = [];
        this.listLoading = true;
        this.listQuery.beginDate = this.listQuery.dateRange
          ? this.listQuery.dateRange[0]
          : "";
        this.listQuery.endDate = this.listQuery.dateRange
          ? this.listQuery.dateRange[1]
          : "";
  
        let sendObj = JSON.parse(JSON.stringify(this.listQuery));
        delete sendObj.dateRange;
        if (sendObj.equipRoomIds.length > 0) {
          sendObj.equipRoomIds = sendObj.equipRoomIds.join(",");
        } else {
          sendObj.equipRoomIds = "";
        }
        // sendObj.equipRoomName = utils.arrId2Name(this.sbjSelect, sendObj.equipRoomIds);
        // delete sendObj.equipRoomIds;
  
        postAction('/iot/abnormal-alarm-offline/page',sendObj).then(res => {
          this.listLoading = false;
          if (res.data.code == 200) {
            this.total = res.data.data.total;
            this.list = JSON.parse(JSON.stringify(res.data.data.records));
          } else {
            this.$message.warning(res.data.msg);
          }
        });
      },
  
      clearQuery(arr) {
        for (let item of arr) {
          this.listQuery[item] = "";
        }
        this.searchFunc();
      },
  
      searchFunc() {
        this.getList();
      },
  
      getInfo1(id) {
        getAction(`/iot/findEquipNodeById/${id}`).then(res => {
          if (res.data.code == 200) {
            this.alarmDetailData = res.data.data;
            console.log(this.alarmDetailData, "this.alarmDetailData");
            this.getEquipmentDrawing(this.alarmDetailData.pojo.equipRoomId);
            this.dlgShow = true;
            this.$nextTick(() => {
              this.$refs["dlgForm"].clearValidate();
            });
          } else {
            this.$message({
              type: "warning",
              message: res.data.msg
            });
          }
        });
      },
   
    }
  };
  </script>
  <style rel="stylesheet/scss" lang="scss">
  .el-table {
    .color-warn1 {
      color: red;
    }
    .color-warn2 {
      color: orange;
    }
    .color-warn3 {
      color: blue;
    }
  }
  
  .card2_1 {
    /deep/.el-card__body {
      height: 270px;
      overflow-y: auto;
    }
  }
  
  .card2_2 {
    /deep/.el-card__body {
      height: 580px;
      overflow-y: auto;
    }
  }
  .drop-right-img {
    // height: 100%;
    width: 400px;
    height: 202px;
    -webkit-user-drag: none;
  }
  .oneBtn {
    position: absolute;
    right: 20px;
    top: 22px;
  }
  
  .giveAlarmDlgCol {
    border: 1px solid #eff6ff;
    height: 260px;
    padding: 15px;
    background-color: #f8faff;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
  
  .towBtn {
    position: absolute;
    right: 20px;
    top: 0;
  }
  
  .alarmMessageListBox {
    display: flex;
    justify-content: space-between;
    font-weight: bolder;
    background-color: #fff7f6;
    margin-bottom: 10px;
    padding: 6px;
  }
  
  .deviceViewBox {
    // font-weight: bolder;
    // margin-bottom: 10px;
    padding: 10px;
    border-bottom: 1px solid #f1efef;
    // position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .card1 {
    /deep/.el-card__body {
      height: calc(100% - 53px);
      overflow-y: auto;
    }
  }
  
  .echart {
    height: 200px;
  }
  
  ////////////////
  .list-yuan-btn {
    height: 28px;
    line-height: 28px;
  
    padding: 0 10px;
    border-radius: 14px;
    margin-right: 10px;
  }
  
  .bg-red {
    background: #ff0900 !important;
    color: white !important;
    box-shadow: 0 4px 10px #ffc5c3 !important;
  }
  
  .bg-yellow {
    background: #fef6e2;
    color: #e5ab35;
  }
  
  .bg-blue {
    background: #ecf5ff;
    color: #5eadfe;
  }
  
  .bg-green {
    background: #e2f9ee;
    color: #69dfb0;
  }
  
  //
  .item-bg-red {
    background: linear-gradient(to right, #fff2f1, #fff, #fff, #fff, #fff);
  }
  
  //
  .ml16 {
    margin-right: 16px;
  }
  .drop-left-p {
    position: absolute;
    z-index: 1;
  }
  .icon_cgq_right {
    width: 20px;
  }
  .icon_cgq_right.success {
    animation-name: animation_success; /* 执行动画名称 */
    background-color: #e1f3d8; /* 背景颜色 */
  }
  .icon_cgq_right.danger {
    animation-name: animation_danger; /* 执行动画名称 */
    background-color: #fde2e2; /* 背景颜色 */
  }
  .icon_cgq_right.warning {
    animation-name: animation_warning; /* 执行动画名称 */
    background-color: #faecd8; /* 背景颜色 */
  }
  // #dqhz .el-card__body {
  //   padding: 0 !important;
  // }
  /deep/.el-card__body {
    padding: 0;
    // padding-left: 20px;
  }
  </style>
  