<template>
  <!-- 弹窗 新增/编辑 -->
  <el-dialog
    class="mazhenguo"
    :title="dlgType === 'add' ? '添 加' : '修改/部署流程'"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="500px"
    top="30px"
  >
    <el-form
      ref="dlgDataForm"
      :rules="dlgRules"
      :model="dlgData"
      label-position="right"
      label-width="90px"
      style="width: 440px"
      size="mini"
      @submit.native.prevent
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="dlgData.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item v-if="dlgType == 'add'" label="关联表单" prop="tableKey">
        <el-input v-model="dlgData.tableKey" placeholder="请输入" />
      </el-form-item>
      <!-- <el-form-item label="选择小区" prop="aaaa">
        <el-select v-model="dlgData.aaaa" clearable placeholder="请选择" @change="xqSelectChange">
          <el-option v-for="item of xqSelect" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </el-form-item> -->

      <!-- <el-form-item label="描述" prop="aaaa">
        <el-input
          :autosize="{ minRows: 3, maxRows: 4 }"
          v-model="dlgData.aaaa"
          type="textarea"
          placeholder="请输入"
          style="width: 100%"
        />
      </el-form-item> -->
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
import Tinymce from '@/components/Tinymce' // 富文本组件
// 工具
// import { phoneReg } from '@/utils/regUtil'
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
// 接口
import * as utils from '@/utils'
import { postAction, getAction, formAction } from '@/api'

let dlgDataEmpty = {
  name: '',
  tableKey: '',
}
export default {
  components: {
    Tinymce,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          let dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
          if (this.dlgType != 'add') {
            console.log('---dlgQuery', this.dlgQuery)
            dlgData = {
              name: this.dlgQuery.sourceName,
              tableKey: this.dlgQuery.tableKey,
            }
          }
          this.dlgData = JSON.parse(JSON.stringify(dlgData))
          this.$nextTick(() => {
            this.$refs['dlgDataForm'].clearValidate()
          })
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      // 弹窗
      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        tableKey: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },
      dlgSubLoading: false, // 提交loading

      // 下拉框
      xqSelect: [], // 小区
      sxxmSelect: [], // 收费项目
    }
  },
  created() {
    // this.getDataDict()
  },
  methods: {
    /////

    // 数据字典

    // 弹窗提交 ------
    dlgSubFunc() {
      this.$refs['dlgDataForm'].validate((valid) => {
        if (valid) {
          this.dlgSubLoading = true
          if (this.dlgType == 'add') {
            let sendObj = JSON.parse(JSON.stringify(this.dlgData))
            postAction('/act/table/add', sendObj).then((res0) => {
              let res = res0.data
              this.dlgSubLoading = false
              if (res.code == 200) {
                this.$message.success(res.msg)
                this.dlgState = false
                this.$emit('getList')
                this.$emit('closeDlg')
              } else {
                this.$message({
                  type: 'warning',
                  message: res.msg,
                })
              }
            })
          } else {
            let sendObj = {
              bpmn: this.dlgQuery.bpmnXML,
              sourceName: this.dlgData.name,
              tableId: this.dlgQuery.id,
            }
            console.log('sendObj', sendObj)
            // return false
            formAction('/act/process/add', sendObj).then((res0) => {
              let res = res0.data
              this.dlgSubLoading = false
              if (res.code == 200) {
                this.$message.success('操作成功')
                this.dlgState = false
                this.$emit('getList')
                this.$emit('closeDlg')

                // 跳转
                setTimeout(() => {
                  this.$router.go(-1)
                }, 200)
              } else {
                this.$message({
                  type: 'warning',
                  message: res.msg,
                })
              }
            })
          }
        }
      })
    },

    closeDlg() {
      this.dlgLoading = false
      this.dlgSubLoading = false
      this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')

      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.$nextTick(() => {
        this.$refs['dlgDataForm'].clearValidate()
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>