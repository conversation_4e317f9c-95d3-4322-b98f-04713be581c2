<template>
  <div class="">
    <!-- 供应链 弹窗 多选商品 -->
    <el-dialog
      :close-on-click-modal="false"
      :title="`选择${pageStr}`"
      :append-to-body="true"
      :visible.sync="gylGoodsState"
      width="900px"
      top="30px"
      icon-class="el-icon-info"
    >
      <div class="dialog-bms-bar">
        <!-- 部门 -->
        <div class="dialog-bms-left">
          <el-input placeholder="输入关键字进行过滤" style="margin-bottom: 10px" v-model="bmsText"> </el-input>
          <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
          <div class="bms-tree">
            <el-tree
              :expand-on-click-node="false"
              :data="bmsData"
              ref="bmsDom"
              default-expand-all
              :filter-node-method="bmsFilter"
              :props="defaultProps"
              @node-click="bmsClick"
            >
            </el-tree>
          </div>
        </div>
        <div class="dialog-bms-right">
          <div>
            <el-popover class="fr popDom" placement="bottom-end" width="800" @show="showPopover" trigger="click">
              <el-table ref="returnListRef" style="height: 400px; overflow: auto" :data="returnList">
                <el-table-column type="index" width="50" align="center"></el-table-column>

                <el-table-column property="name" :label="tableCols[0].str"></el-table-column>
                <el-table-column property="code" width="160" :label="tableCols[1].str"></el-table-column>
                <!-- <el-table-column property="typeName" label="分类"></el-table-column> -->
                <el-table-column property="" label="操作" width="100" align="center">
                  <template slot-scope="scope">
                    <el-button @click="popRemoveRow(scope.row)" type="danger" size="mini" icon="el-icon-delete" plain></el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-button class="fr search-right-btn" type="success" slot="reference" icon="el-icon-arrow-down">查看已选</el-button>
            </el-popover>

            <el-button class="fr search-right-btn" icon="el-icon-search" type="success" size="mini" @click="getList">搜索</el-button>
            <el-input
              @change="getList"
              class="m-shaixuan-input fr"
              :placeholder="`${pageStr}名称`"
              style="width: 200px"
              v-model="listQuery.label"
            >
              <i @click="resetSearchItem(['label'])" slot="suffix" class="el-input__icon el-icon-error"></i>
            </el-input>
            <div class="clear"></div>
          </div>

          <!-- 表格 -->
          <el-table
            ref="multipleTable"
            class="m-small-table mt10"
            v-loading="listLoading"
            :key="10210811"
            :data="list"
            border
            fit
            highlight-current-row
            style="width: 100%"
            tooltip-effect="dark"
            height="346px"
            @select="tableSelectChange"
            @select-all="tableSelectAll"
            :default-sort="{ prop: 'aaa', order: 'descending' }"
          >
            <el-table-column align="center" type="selection" width="55"> </el-table-column>

            <el-table-column label="名称">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="编号">
              <template slot-scope="scope">
                <span>{{ scope.row.code }}</span>
              </template>
            </el-table-column>

            <!-- <el-table-column :label="tableCols[0].str">
              <template slot-scope="scope">
                <span>{{ scope.row.mName }}</span>
              </template>
            </el-table-column>

            <el-table-column  :label="tableCols[1].str">
              <template slot-scope="scope">
                <span>{{ scope.row.mId }}</span>
              </template>
            </el-table-column> -->
          </el-table>

          <!-- 分页 -->
          <!-- :pagerCount='4' -->
          <pagination
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.limit"
            layout="total,prev, pager, next"
            @pagination="getList"
            :pager-count="5"
          />
          <div class="clear"></div>
        </div>
        <!-- 部门多选框 -->
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon="el-icon-back">取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import { findOrgBranchAll } from '@/api/dataDic'

import { postAction, getAction } from '@/api'

import {
  cacfsTree, // 获取 商品树
  scfsTree, // 获取 供应商树
  caesPage, // 查询商品列表
  supplierPage, // 查询供应商列表  limit，name，page，upCode
} from '@/api/supplyChainApi'

import Pagination from '@/components/Pagination' // Secondary package based on el-pagination

let listQueryEmpty = {
  label: '', // 模糊
  page: 1,
  limit: 20,

  code: '', // 编码
  name: '', //name
}

export default {
  props: {
    dlgQuery: {},
  },
  components: { Pagination },
  data() {
    return {
      // 部门树
      bmsData: [],
      bmsType: '', // sp-商品，gys-供应商
      bmsSelect: {},
      bmsText: '', // 部门左侧筛选
      selectNode: '',

      // 右侧表格
      // {id, bm, typeId, name}  bm-编码
      list: [],
      listLoading: false,
      total: 0,
      tableSelectList: '', // 复选
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      returnList: [], // 返回的列表集合
      // 树过滤
      defaultProps: {
        children: 'children',
        label: 'name',
      },

      pageStr: '', // 商品、供应商
      // 右侧表格显示内容
      // tableCols: [],  // 表格提示内容
      tableCols: [
        { str: '商品名称', key: 'mName' },
        { str: '商品编号', key: 'mId' },
      ],
    }
  },
  computed: {
    ...mapGetters(['gylGoodsArrSet']),

    gylGoodsState: {
      get: function () {
        let state = this.$store.getters.gylGoodsState

        if (state === false) {
          this.returnList = []
          this.list = []
        }
        setTimeout(() => {
          this.bmsType = this.$store.getters.gylGoodsType
          if (this.bmsType == 'sp') {
            this.tableCols = [
              { str: '商品名称', key: 'mName' },
              { str: '商品编号', key: 'mId' },
            ]
            this.pageStr = '商品'
          } else if (this.bmsType == 'gys') {
            this.tableCols = [
              { str: '供应商名称', key: 'mName' },
              { str: '供应商编号', key: 'mId' },
            ]
            this.pageStr = '供应商'
          }
          // pageStr
          // this.$store.commit('SET_GYLGOODSTYPE', 'sp')
          this.getTree()
          this.getList()

          this.listQuery.code = ''
          this.listQuery.name = ''
          $('.tree-on').removeClass('tree-on')
        }, 50)
        return state
      },
      set: function (newVal) {
        this.$store.commit('SET_GYLGOODSSTATE', newVal)
      },
    },
  },
  watch: {
    bmsText(val) {
      this.$refs.bmsDom.filter(val)
    },
    gylGoodsArrSet(val) {
      this.returnList = JSON.parse(JSON.stringify(val))
    },
  },
  created() {},
  methods: {
    // [[ 新的
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },
    // 获取商品分类 树
    getTree() {
      if (this.bmsType == 'sp') {
        let userInfo = JSON.parse(window.localStorage.userInfo)

        getAction(`/schain/commodity/class/tree/${userInfo.projectId}`).then((res) => {
          let code = res.data.code
          let data = res.data.data
          let msg = res.data.msg

          if (code === '200') {
            let listStr = JSON.stringify(res.data.data)

            this.bmsData = JSON.parse(listStr)
          } else {
            this.$message.error(msg)
          }
        })
      } else if (this.bmsType == 'gys') {
        scfsTree().then((res) => {
          let code = res.data.code
          let data = res.data.data
          let msg = res.data.msg

          if (code === '200') {
            let listStr = JSON.stringify(res.data.list)
            listStr = listStr.replace(/supplierCode/g, 'mId')
            listStr = listStr.replace(/supplierName/g, 'mName')

            this.bmsData = JSON.parse(listStr)
          } else {
            this.$message.error(msg)
          }
        })
      }
    },
    // 节点左键点击事件
    bmsClick(data, node, mNode) {
      console.log('-data', data)
      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      // 获取分类下商品
      this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))

      this.selectNode = data
      this.listQuery.code = data.code + '' === '0' ? '' : data.code
      this.listQuery.name = data.name
      this.getList()
    },
    // 获取分类下商品
    getList() {
      console.log('发送数据', this.listQuery)
      this.listLoading = true
      if (this.bmsType == 'sp') {
        let sendObj = this.listQuery
        if (this.dlgQuery) {
          sendObj = { ...sendObj, ...this.dlgQuery }
        }

        postAction('/schain/commodity/page', sendObj).then((res1) => {
          this.listLoading = false
          let res = res1.data

          if (res.code === '200') {
            this.total = res.page.total

            // let listStr = JSON.stringify(res.list)
            // listStr = listStr.replace(/commodityCode/g, 'mId')
            // listStr = listStr.replace(/commodityName/g, 'mName')
            let list = JSON.parse(JSON.stringify(res.data))
            let selectList = []

            for (let i = 0; i < list.length; i++) {
              let item = list[i]
              item.mIndex = (this.listQuery.page - 1) * this.listQuery.size + i + 1

              // 添加选中项
              for (let item2 of this.returnList) {
                if (item2.id == item.id) {
                  selectList.push(item)
                }
              }
            }
            this.list = JSON.parse(JSON.stringify(list))

            // 设置选中项
            setTimeout(() => {
              for (let item of this.list) {
                for (let item2 of selectList) {
                  if (item.id == item2.id) {
                    this.$refs.multipleTable.toggleRowSelection(item, true)
                  }
                }
              }
            }, 50)
          } else {
            this.$message({
              type: 'error', // success, warning, info, error
              message: res.msg,
            })
          }
        })
      } else if (this.bmsType == 'gys') {
        let sendObj = {
          // commodityName: this.listQuery.label,
          page: this.listQuery.page,
          limit: this.listQuery.size,
          name: this.listQuery.mName,
          upCode: this.listQuery.mId,
        }
        supplierPage(sendObj).then((res1) => {
          this.listLoading = false
          let res = res1.data

          if (res.code === '200') {
            this.total = res.data.total

            // let listStr = JSON.stringify(res.list)
            // listStr = listStr.replace(/commodityCode/g, 'mId')
            // listStr = listStr.replace(/commodityName/g, 'mName')
            let list = JSON.parse(JSON.stringify(res.list))
            let selectList = []

            for (let i = 0; i < list.length; i++) {
              let item = list[i]
              item.mIndex = (this.listQuery.page - 1) * this.listQuery.size + i + 1

              item.mId = item.supplierArchivesCode
              item.mName = item.supplierName

              // 添加选中项
              for (let item2 of this.returnList) {
                if (item2.mId == item.mId) {
                  selectList.push(item)
                }
              }
            }
            this.list = JSON.parse(JSON.stringify(list))

            // 设置选中项
            setTimeout(() => {
              for (let item of this.list) {
                for (let item2 of selectList) {
                  if (item.mId == item2.mId) {
                    this.$refs.multipleTable.toggleRowSelection(item, true)
                  }
                }
              }
            }, 50)
          } else {
            this.$message({
              type: 'error', // success, warning, info, error
              message: res.msg,
            })
          }
        })
      }
    },
    // 表格复选框
    tableSelectChange(arr, row) {
      let checkedState = false // true-勾选状态，false-取下选择状态
      for (let item of arr) {
        if (item.id == row.id) {
          checkedState = true
          break
        }
      }
      // 判断是否是勾选状态
      if (checkedState) {
        this.returnList.push(row)
        console.log('勾选', this.returnList)
      } else {
        let returnList = this.returnList.filter((item) => {
          return item.id != row.id
        })
        this.returnList = JSON.parse(JSON.stringify(returnList))
        console.log('取消选择', returnList)
      }
    },
    // 表格 全选
    tableSelectAll(arr) {
      let len = arr.length
      console.log(arr.length)
      // 长度为0 取消全选，将list中所有数据，从returnList中移除
      // 长度不为0，全选，将list中所有数据，追加到 returnList中

      let list = JSON.parse(JSON.stringify(this.list))
      let returnList = JSON.parse(JSON.stringify(this.returnList))
      if (len == 0) {
        let newList = []
        for (let item of returnList) {
          let hasId = list.some((item2) => {
            return item2.id == item.id
          })
          if (!hasId) {
            newList.push(item)
          }
        }
        returnList = JSON.parse(JSON.stringify(newList))
      } else {
        for (let item of list) {
          let hasId = returnList.some((item2) => {
            return item2.id == item.id
          })
          if (!hasId) {
            returnList.push(item)
          }
        }
      }

      console.log('完美的选中数据', returnList)
      this.returnList = returnList
    },

    // 已选框
    showPopover() {
      for (let item of this.returnList) {
        this.$refs.returnListRef.toggleRowSelection(item, true)
      }
    },
    // 弹窗 复选框改变事件
    // 删除库
    popRemoveRow(row) {
      let returnList = this.returnList.filter((item) => {
        return item.id != row.id
      })
      this.returnList = JSON.parse(JSON.stringify(returnList))

      // 选择商品弹窗，复选框同步改变
      for (let item of this.list) {
        let isHas = this.returnList.some((item2) => {
          return item2.id == item.id
        })
        if (isHas) {
          this.$refs.multipleTable.toggleRowSelection(item, true)
        } else {
          this.$refs.multipleTable.toggleRowSelection(item, false)
        }
      }
    },
    // 表格多选重置
    tableCheckFunc() {
      let list = this.list
      for (let i = 0; i < list.length; i++) {
        let item = list[i]

        let isSelect = this.returnList.some((item2) => {
          item2.id == item.id
        })

        if (isSelect) {
          this.$refs.multipleTable.toggleRowSelection(item, true)
        }
      }
      this.list = JSON.parse(JSON.stringify(list))
    },
    // 选择部门提交
    bumenOkFunc() {
      console.log('返回数据', this.returnList)
      this.$emit('backFunc', this.returnList)
      setTimeout(() => {
        this.closeDialog()
      }, 50)
    },
    // ]] 新的
    ////////////////
    // 【【 左侧相关
    // 获取部门
    findOrgBranchAll() {
      findOrgBranchAll().then((res) => {
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code === '200') {
          this.bmsData = JSON.parse(JSON.stringify(res.data.list))
        } else {
          this.$message.error(msg)
        }
      })
    },

    // 筛选部门
    bmsFilter(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 】】 左侧相关

    // 【【 右侧相关
    // 右侧删除部门方法
    delBmFunc(relationId, relationName) {
      let nArr = this.bmsArr2.filter((item) => {
        return item.relationId !== relationId
      })
      this.bmsArr2 = JSON.parse(JSON.stringify(nArr))
    },
    // 】】 右侧相关

    // 【【 其他

    // 关闭弹窗
    closeDialog() {
      this.$store.commit('SET_GYLGOODSSTATE', false)
    },
    // 】】 其他

    // 筛选岗位
    // filterBmrightListOld
    // filterGangwei(val) {
    //   this.filterBmrightList = JSON.parse(JSON.stringify(this.filterBmrightListOld))
    //   this.filterBmrightList = this.filterBmrightList.filter(item => {
    //     return item.label.indexOf(val) >= 0
    //   })
    // },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dialog-bms-bar {
  display: flex;
  justify-content: space-between;
}
.dialog-bms-left {
  width: 360px;
  margin-right: 10px;
}
// .dialog-bms-left,.dialog-bms-right {
//   width: 49%;
// }
.bms-tree {
  height: 400px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
}
.dialog-bms-right {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 10px;
  flex: 1;
  .bms-a {
    margin-bottom: 10px;
    margin-left: 0px;
    margin-right: 6px;
  }
  i {
    display: inline-block;
    margin-left: 3px;
  }
}

// .popDom.el-table .cell {
//   height: 23px;
// }
</style>