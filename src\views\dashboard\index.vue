<template>
  <component :is="currentRole" />
</template>

<script>
import { mapGetters } from 'vuex'
import adminDashboard from './admin'
import * as utils from '@/utils'

export default {
  name: 'Dashboard',
  components: { adminDashboard },
  data() {
    return {
      currentRole: 'adminDashboard'
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ])
  },
  created() {

  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dashboard-container {
  height: 100%;
}
</style>