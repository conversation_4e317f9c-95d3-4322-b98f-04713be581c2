<template>
  <el-dialog :close-on-click-modal="false" :title="'选择坐席'" :visible.sync="dlgShow">
    <div class="filter-container">
      <el-form ref="searchForm" :inline="true" class="clearfix" label-width="90px" @submit.native.prevent>
        <el-form-item>
          <el-input
            @keyup.enter.native="getList"
            v-model="listQuery.label"
            placeholder="请输入用户全名/手机号/岗位/姓名"
          >
            <i slot="suffix" @click="resetLabel" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" limit="mini" @click="searchItem"> 搜索 </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class="m-small-table" :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectSeatId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>

        <el-table-column label="用户全名">
          <template slot-scope="scope">
            <span>{{ scope.row.displayName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号">
          <template slot-scope="scope">
            <span>{{ scope.row.phone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="绑定岗位">
          <template slot-scope="scope">
            <span>{{ scope.row.postName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="在岗人员">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back"> 取 消 </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import { getCsSeatPage } from '@/api/customerCenter/callSetup.js'

export default {
  components: {
    Pagination,
  },
  data() {
    return {
      list: [],

      listQuery: {
        page: 1,
        limit: 10,
        label: '',
      },

      total: 0,

      selectSeatId: '',

      selectSeatName: '',
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.customerCenter.seatDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('customerCenter/seatDlg/SET_DLGSHOW', val)
      },
    },

    seatId: {
      get: function () {
        return this.$store.state.customerCenter.seatDlg.seatId
      },
      set: function (val) {
        this.$store.commit('customerCenter/seatDlg/SET_SEATID', val)
      },
    },

    seatName: {
      get: function () {
        return this.$store.state.customerCenter.seatDlg.seatName
      },
      set: function (val) {
        this.$store.commit('customerCenter/seatDlg/SET_SEATNAME', val)
      },
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.seatId)) {
          this.selectSeatId = ''
          this.selectSeatName = ''
        } else {
          this.selectSeatId = this.seatId
          this.selectSeatName = this.seatName
        }
        this.getList()
      }
    },

    seatId: {
      handler(val) {
        this.selectSeatId = val
      },
      immediate: true,
      deep: true,
    },

    seatName: {
      handler(val) {
        this.selectSeatName = val
      },
      immediate: true,
      deep: true,
    },
  },

  methods: {
    resetLabel() {
      this.listQuery.label = ''
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    rowClick(row, column, event) {
      this.selectSeatId = row['id']
      this.selectSeatName = row['displayName']
      this.selectSeatRow = row
    },

    getList() {
      this.list = []
      getCsSeatPage(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = utils.isNull(res.data.data) ? [] : res.data.data
          this.total = utils.isNull(res.data.page) ? 0 : res.data.page.total
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    subDlg() {
      this.$store.commit('customerCenter/seatDlg/SET_SEATID', '')
      this.$store.commit('customerCenter/seatDlg/SET_SEATNAME', '')
      this.$store.commit('customerCenter/seatDlg/SET_SEATINFO', '')

      setTimeout(() => {
        this.$store.commit('customerCenter/seatDlg/SET_SEATID', this.selectSeatId)
        this.$store.commit('customerCenter/seatDlg/SET_SEATNAME', this.selectSeatName)
        this.$store.commit('customerCenter/seatDlg/SET_SEATINFO', JSON.stringify(this.selectSeatRow))
        this.closeDlg()
      }, 100)
    },

    closeDlg() {
      this.$store.commit('customerCenter/seatDlg/SET_DLGSHOW', false)
    },
  },
}
</script>