<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' 
      :title="bpmnType=='add'?'新建流程':'编辑流程'" 
      top='30px'
      
      :visible.sync="bpmnState" 
      width='900px'
      :fullscreen='true'
      append-to-body>
      <div class="bpmn-body">
        <div class="canvas" ref="canvas"></div>
        <div id="js-properties-panel" class="panel"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="success" @click="okFunc" icon="el-icon-check">确定</el-button>

        <div style="display: none">
          <a class="bpmn-btn fr mr10" ref="saveSvg" href="javascript:" title="导出 SVG">导出 SVG</a>
          <a class="bpmn-btn fr mr10" ref="saveDiagram" href="javascript:" title="导出 BPMN">导出 BPMN</a>
        </div>
        
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// 数据接口
import { findOrgBranchAll, findSysUserAll } from '@/api/dataDic'
// 页面组件
import Pagination from '@/components/Pagination'

// bpmn相关依赖
import BpmnModeler from 'bpmn-js/lib/Modeler'
import propertiesPanelModule from 'bpmn-js-properties-panel'
import propertiesProviderModule from 'bpmn-js-properties-panel/lib/provider/camunda'
import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda'
import customTranslate from '@/utils/bpmnChineseTranslate/index.js'

export default {
  components: { Pagination },
  data() {
    return {
      // bpmn建模器
      bpmnModeler: null,
      container: null,
      canvas: null,
      xmlStr: null,
      processName: '',

      bpmnXml: ''
    }
  },
  computed: {
    ...mapGetters([
      // 部门树
      'bpmnIndex',
      'bpmnType',
      'bpmnXmlSet'
    ]),
    bpmnState: {
      get: function() {
        let state = this.$store.getters.bpmnState
        if (state === true) {
          // let emptyStr = '<div class="canvas" ref="canvas"></div><div id="js-properties-panel" class="panel"></div>'
          // $('.bpmn-body').html(emptyStr)
          setTimeout(() => {
            if (this.bpmnIndex == 1) {
              this.setBpmn()
            } else {
              this.createNewDiagram()
            }
            
          }, 50)
        }
        return state
      },
      set: function(newVal) {
        this.$store.commit('SET_BPMNSTATE', newVal)
      }
    },

  },
  watch: {
  },
  created() {
  },
  mounted() {
    
  },
  methods: {
    // 【【 bpmn 相关
    // 初始化
    setBpmn() {
      // 设置大小
      let boxW = $(window).width() - 40
      let boxH = $(window).height() - 126
      $('.bpmn-body').width(boxW)
      $('.bpmn-body').height(boxH)
      setTimeout(() => {
        $('.panel').height(boxH)
      }, 200)
      
      // 获取到属性ref为“content”的dom节点
      this.container = this.$refs.content
      // 获取到属性ref为“canvas”的dom节点
      const canvas = this.$refs.canvas

      // 建模，官方文档这里讲的很详细
      this.bpmnModeler = new BpmnModeler({
        container: canvas,
        // 添加控制板
        propertiesPanel: {
          parent: '#js-properties-panel'
        },
        additionalModules: [
          // 左边工具栏以及节点
          propertiesProviderModule,
          // 右边的工具栏
          propertiesPanelModule
        ],
        additionalModules: [
          propertiesProviderModule,
          propertiesPanelModule,
          customTranslate // !国际化
        ],
        moddleExtensions: {
          camunda: camundaModdleDescriptor
        }
      })

      // 下载画图
      let _this = this
      // 获取a标签dom节点
      const downloadLink = this.$refs.saveDiagram
      const downloadSvgLink = this.$refs.saveSvg
      // 给图绑定事件，当图有发生改变就会触发这个事件
      this.bpmnModeler.on('commandStack.changed', function () {
        _this.saveSVG(function (err, svg) {
          _this.setEncoded(downloadSvgLink, 'diagram.svg', err ? null : svg)
        })

        _this.saveDiagram(function (err, xml) {
          _this.bpmnXml = xml
          _this.setEncoded(downloadLink, 'diagram.bpmn', err ? null : xml)
        })
      })

      this.createNewDiagram()

    },
    // 画图
    createNewDiagram () {
      // const bpmnXmlStr = ''
      let bpmnXmlStr = '<?xml version="1.0" encoding="UTF-8"?>\n' +
        '<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">\n' +
        '  <bpmn2:process id="Process_1" isExecutable="false">\n' +
        '    <bpmn2:startEvent id="StartEvent_1"/>\n' +
        '  </bpmn2:process>\n' +
        '  <bpmndi:BPMNDiagram id="BPMNDiagram_1">\n' +
        '    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">\n' +
        '      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">\n' +
        '        <dc:Bounds height="36.0" width="36.0" x="412.0" y="240.0"/>\n' +
        '      </bpmndi:BPMNShape>\n' +
        '    </bpmndi:BPMNPlane>\n' +
        '  </bpmndi:BPMNDiagram>\n' +
        '</bpmn2:definitions>'
      // 将字符串转换成图显示出来
      this.bpmnModeler.importXML(bpmnXmlStr, function (err) {
        if (err) {
          console.error(err)
        }
      })
      
    },
    // 下载为SVG格式,done是个函数，调用的时候传入的
    saveSVG (done) {
      // 把传入的done再传给bpmn原型的saveSVG函数调用
      this.bpmnModeler.saveSVG(done)
    },
    // 下载为SVG格式,done是个函数，调用的时候传入的
    saveDiagram (done) {
      // 把传入的done再传给bpmn原型的saveXML函数调用
      this.bpmnModeler.saveXML({ format: true }, function (err, xml) {
        done(err, xml)
      })
    },
    // 当图发生改变的时候会调用这个函数，这个data就是图的xml
    setEncoded (link, name, data) {
      // 把xml转换为URI，下载要用到的
      const encodedData = encodeURIComponent(data)
      // 获取到图的xml，保存就是把这个xml提交给后台
      this.xmlStr = data
      // 下载图的具体操作,改变a的属性，className令a标签可点击，href令能下载，download是下载的文件的名字
      if (data) {
        link.className = 'active'
        link.href = 'data:application/bpmn20-xml;charset=UTF-8,' + encodedData
        link.download = name
      }
    },

    // 】】 bpmn 相关

    // 选择部门提交
    okFunc() {
      let sendObj = {
        xmlStr: this.bpmnXml
      }
      console.log('发送数据', JSON.stringify(sendObj))
      // if (this.selectedId === '') {
      //   this.$message({
      //     type: 'warning',
      //     message: '请选择员工'
      //   })
      // } else {
      //   let selectedObj = this.list.filter(item => {
      //     return item.id === this.selectedId
      //   })

      //   this.$store.commit('SET_USERTREEUSERID', '')
      //   this.$store.commit('SET_USERTREEUSERNAME', '')
      //   setTimeout(() => {
      //     this.$store.commit('SET_USERTREEUSERID', this.selectedId)
      //     this.$store.commit('SET_USERTREEUSERNAME', selectedObj[0].label)
      //     this.closeDialog()
      //   }, 50)
      // }
    },
    // 关闭弹窗 
    closeDialog() {
      this.bpmnState = false
      // this.$store.commit('SET_BPMNSTATE', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
/*左边工具栏以及编辑节点的样式*/
  @import 'bpmn-js/dist/assets/diagram-js.css';
  @import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
  @import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css';
  @import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';
  /*右边工具栏样式*/
  @import 'bpmn-js-properties-panel/dist/assets/bpmn-js-properties-panel.css';
  .containers{
    position: absolute;
    background-color: #ffffff;
    width: 100%;
    height: 90%;
  }
  .canvas{
    width: 100%;
    height: 100%;
  }
  .panel{
    position: absolute;
    right: 0;
    top: 54px;
    width: 300px;
    overflow: auto;
  }
  .bjs-powered-by {
    display: none;
  }
  .buttons{
    position: absolute;
    left: 20px;
    bottom: 20px;
    &>li{
      display:inline-block;margin: 5px;
      &>a{
        color: #999;
        background: #eee;
        cursor: not-allowed;
        padding: 8px;
        border: 1px solid #ccc;
        &.active{
          color: #333;
          background: #fff;
          cursor: pointer;
        }
      }
    }
  }

  .bpmn-btn {
    display: inline-block;
    padding: 0px 10px;
    line-height: 22px;
    height: 22px;
    font-size: 12px;
    background-color: #409EFF;
    border-color: #409EFF;
    color: #FFF;
    border-radius: 3px;
    
  }
  .bpmn-btn:active {
    background: #3a8ee6;
    border-color: #3a8ee6;
    color: #FFF;
  }
  .bpmn-btn:focus,.bpmn-btn:hover {
    background: #66b1ff;
    border-color: #66b1ff;
    color: #FFF;
  }
  .mr10 {
    margin-left: 10px;
  }
</style>