import request from '@/utils/request'
import { requestExcel } from '@/utils'

// 导入社保
export function payMonthSocialSecurityImports(data) {
  return requestExcel('/ade/payMonthSocialSecurityImports', data)
}

// 获取社保导入记录
export function findPayMonthSocialSecurityDynamic(data) {
  return request({
    url: `/ade/findPayMonthSocialSecurityDynamic`,
    method: 'post',
    data
  })
}

// 查询社保记录
export function findMonthSocialSecurityDynamicCX(data) {
  return request({
    url: `/ade/findMonthSocialSecurityDynamicCX`,
    method: 'post',
    data
  })
}

// 查询社保详细  ecurityFamily-社保户，yearMonth-年月，xianzhongleixing-险种类型（1养老/2医疗/3失业/4生育/5工伤），label,page,size,sort
export function findMonthSocialSecurityDynamicXX(data) {
  return request({
    url: `/ade/findMonthSocialSecurityDynamicXX`,
    method: 'post',
    data
  })
}
// 查询社保汇总统计
export function findPaySocialSecurityDetailSummarySum(data) {
  return request({
    url: `/ade/findPaySocialSecurityDetailSummarySum`,
    method: 'post',
    data
  })
}
