// 设备管理
import request from "@/utils/request";
import { objToParam } from "@/utils/index";

//设备保养统计
export function equMaintenanceStatistic(data) {
  let paramStr = objToParam(data);
  return request({
    url: `sa/green/equipment-manage/maintenance-statistic${paramStr}`,
    method: "get",
  });
}

//设备保养统计导出
export function equMaintenanceStatisticExport(data) {
  let paramStr = objToParam(data);
  return request({
    url: `sa/green/equipment-manage/maintenance-statistic/export${paramStr}`,
    method: "get",
  });
}

//设备保养统计
export function equInvokeStatistic(data) {
  let paramStr = objToParam(data);
  return request({
    url: `sa/green/equipment-manage/invoke-statistic${paramStr}`,
    method: "get",
  });
}

//设备保养统计导出
export function equInvokeStatisticExport(data) {
  let paramStr = objToParam(data);
  return request({
    url: `sa/green/equipment-manage/invoke-statistic/export${paramStr}`,
    method: "get",
  });
}

// 单据导出列表
export function pageGreenExportDownloadPage(data) {
  return request({
    url: `sa/green/exportDownload/page`,
    method: "post",
    data,
  });
}

// 删除单据
export function delGreenExportDownload(id) {
  return request({
    url: `sa/green/exportDownload/del/${id}`,
    method: "get",
  });
}

//设备维保统计
export function equInvokeMaintainStatistic(data) {
  let paramStr = objToParam(data);
  return request({
    url: `sa/green/equipment-manage/invoke-maintain-statistic${paramStr}`,
    method: "get",
  });
}

//保养执行记录导出
export function maintenanceInvokeExport(data) {
  let paramStr = objToParam(data);
  return request({
    url: `sa/green/equ/maintenance-invoke/export${paramStr}`,
    method: "get",
  });
}

//检修执行记录导出
export function checkInvokeExport(data) {
  let paramStr = objToParam(data);
  return request({
    url: `sa/green/equ/check-invoke/export${paramStr}`,
    method: "get",
  });
}
