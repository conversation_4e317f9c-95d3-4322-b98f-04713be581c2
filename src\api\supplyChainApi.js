/**
 * 供应链 API
 * by 马振国
 */
import request from '@/utils/request'
////////// [[ 档案管理
// [[ 商品档案分类
// tab1 [[ 获取档案分类
// 获取档案分类 树
export function cacfsTree() {
  return request({
    url: `/chain/commodity/archives/class/tree`,
    method: 'get'
  })
}

// 新增 档案分类
export function cacfsAdd(data) {
  return request({
    url: `/chain/commodity/archives/class/add`,
    method: 'post',
    data
  })
}

// 编辑 档案分类
export function cacfsUpdate(data) {
  return request({
    url: `/chain/commodity/archives/class/update`,
    method: 'post',
    data
  })
}
// 删除档案分类
export function cacfsDel(id) {
  return request({
    url: `/chain/commodity/archives/class/del/${id}`,
    method: 'get'
  })
}

// 上移，下移
export function cacfsUpdown(data) {
  return request({
    url: `/chain/commodity/archives/class/updown`,
    method: 'post',
    data
  })
}
// tab1 ]] 获取档案分类 树

// tab2 [[ 商品档案
// 获取档案列表
export function caesPage(data) {
  return request({
    url: `/chain/caes/page`,
    method: 'post',
    data
  })
}

// 获取流水号  upNum
export function caesNum(data) {
  return request({
    url: `/chain/caes/num`,
    method: 'post',
    data
  })
}

// 新增 档案分类
export function caesAdd(data) {
  return request({
    url: `/chain/caes/add`,
    method: 'post',
    data
  })
}

// 编辑 档案分类
export function caesUpdate(data) {
  return request({
    url: `/chain/caes/update`,
    method: 'post',
    data
  })
}

// 删除
export function caesDel(ids) {
  return request({
    url: `/chain/caes/info/del/${ids}`,
    method: 'get'
  })
}

// 批量，单个 启停  state:0启用，1停用
export function caesQt(ids, state) {
  return request({
    url: `/chain/caes/info/state/${ids}/${state}`,
    method: 'get'
  })
}

// 获取单据详情
export function caesInfo(id) {
  return request({
    url: `/chain/caes/info/${id}`,
    method: 'get'
  })
}
// 商品详情（多条） commodityCodes
export function getGoodsInfo(data) {
  return request({
    url: `/chain/caes/info/ids`,
    method: 'post',
    data
  })
}

// tab2 ]] 商品档案

// tab3 [[ 资产分类
// 获取树
export function acfsTree() {
  return request({
    url: `/chain/asset/class/tree`,
    method: 'get'
  })
}

// 新增 分类
export function acfsAdd(data) {
  return request({
    url: `/chain/asset/class/add`,
    method: 'post',
    data
  })
}

// 编辑 分类
export function acfsUpdate(data) {
  return request({
    url: `/chain/asset/class/update`,
    method: 'post',
    data
  })
}

// 删除
export function acfsDel(id) {
  return request({
    url: `/chain/asset/class/del/${id}`,
    method: 'get'
  })
}
// 上移，下移
export function acfsUpdown(data) {
  return request({
    url: `/chain/asset/class/updown`,
    method: 'post',
    data
  })
}

// 自动生成资产编号
export function acfsNum(data) {
  return request({
    url: `/chain/asset/class/num`,
    method: 'post',
    data
  })
}

// 查详情
export function acfsInfo(num) {
  return request({
    url: `/chain/asset/class/info/${num}`,
    method: 'get'
  })
}
// tab3 ]] 资产分类

////////// ]] 档案管理

////////// [[ 供应商管理
// tab1 供应商分类
// 获取树
export function scfsTree() {
  return request({
    url: `/chain/supplier/class/tree`,
    method: 'get'
  })
}
// 新增节点
export function scfsAdd(data) {
  return request({
    url: `/chain/supplier/class/add`,
    method: 'post',
    data
  })
}
// 修改节点
export function scfsUpdate(data) {
  return request({
    url: `/chain/supplier/class/update`,
    method: 'post',
    data
  })
}
// 删除节点
export function scfsDel(supplierCode) {
  return request({
    url: `/chain/supplier/class/del/${supplierCode}`,
    method: 'get'
  })
}
// 详情
export function scfsInfo(supplierCode) {
  return request({
    url: `/chain/supplier/class/info/${supplierCode}`,
    method: 'get'
  })
}
// 上下移动
export function scfsUpdown(data) {
  return request({
    url: `/chain/supplier/class/updown`,
    method: 'post',
    data
  })
}

// tab2 供应商档案
// 供应商列表  page,limit,upCode, name
export function supplierPage(data) {
  return request({
    url: `/chain/supplier/page`,
    method: 'post',
    data
  })
}
// 新增商品
export function supplierAdd(data) {
  return request({
    url: `/chain/supplier/add`,
    method: 'post',
    data
  })
}
// 启停用  code,startAndStop(0-启用，1-停用)
export function supplierState(data) {
  return request({
    url: `/chain/supplier/state`,
    method: 'post',
    data
  })
}

// 删除
export function supplierDel(id) {
  return request({
    url: `/chain/supplier/del/${id}`,
    method: 'get'
  })
}
export function supplierNum(data) {
  return request({
    url: `/chain/supplier/num`,
    method: 'post',
    data
  })
}

////////// ]] 供应商管理

////////// [[ 业务控制
//// tab1 库房管理
// 库房树
export function storageRoomTree() {
  return request({
    url: `/chain/storage/room/tree`,
    method: 'get'
  })
}

// 新增
export function storageRoomAdd(data) {
  return request({
    url: `/chain/storage/room/add`,
    method: 'post',
    data
  })
}

// 修改
export function storageRoomUpdate(data) {
  return request({
    url: `/chain/storage/room/update`,
    method: 'post',
    data
  })
}

// 删除
export function storageRoomDel(code) {
  return request({
    url: `/chain/storage/room/del/${code}`,
    method: 'get'
  })
}

// 详情
export function storageRoomInfo(code) {
  return request({
    url: `/chain/storage/room/info/${code}`,
    method: 'get'
  })
}

// 上下移动
export function storageRoomUpdown(data) {
  return request({
    url: `/chain/storage/room/updown`,
    method: 'post',
    data
  })
}

///// tab 2 部门业务控制
// 添加
export function bbssAdd(data) {
  return request({
    url: `/chain/bbss/add`,
    method: 'post',
    data
  })
}

// 详情
export function bbssInfo(id) {
  return request({
    url: `/chain/bbss/info/${id}`,
    method: 'get'
  })
}

///// tab 3 部门常用商品
// 新增
export function branchCommodityAdd(data) {
  return request({
    url: `/chain/branch/commodity/add`,
    method: 'post',
    data
  })
}
// 删除
export function branchCommodityDel(data) {
  return request({
    url: `/chain/branch/commodity/del`,
    method: 'post',
    data
  })
}
///// tab 4 商品供应商
// 新增
export function supplierCommodityAdd(data) {
  return request({
    url: `/chain/supplier/commodity/add`,
    method: 'post',
    data
  })
}

// 获取详情
export function supplierCommodityInfo(data) {
  return request({
    url: `/chain/supplier/commodity/info`,
    method: 'post',
    data
  })
}
////////// ]] 业务控制

///////////////////////////// 【【 用料管理 /////////////////////////////
////////// 【【 物资需求 //////////
// 分页
export function summaryOrderPage(data) {
  return request({
    url: `/chain/summary/order/page`,
    method: 'post',
    data
  })
}
// 获取详情 id
export function summaryOrderInfo(id) {
  return request({
    url: `/chain/summary/order/${id}`,
    method: 'get'
  })
}

// 新增需求单
export function summaryOrderCreate(data) {
  return request({
    url: `/chain/summary/order/create`,
    method: 'post',
    data
  })
}

// 修改需求单
export function summaryOrderUpdate(data) {
  return request({
    url: `/chain/summary/order/update`,
    method: 'post',
    data
  })
}
//// 汇总显示 接口
export function summarySheetGenerateInfo(data) {
  return request({
    url: `/chain/summary/sheet/generate/info`,
    method: 'post',
    data
  })
}
// 汇总提交
export function summarySheetCreate(data) {
  return request({
    url: `/chain/summary/sheet/create`,
    method: 'post',
    data
  })
}
// 查看汇总详情
export function summarySheet(id) {
  return request({
    url: `/chain/summary/sheet/${id}`,
    method: 'get'
  })
}
// 如果不是基础数据，点击查看商品
export function summarySheetGoods(data) {
  return request({
    url: `/chain/summary/sheet/goods`,
    method: 'post',
    data
  })
}


////////// 】】 物资需求 //////////
///////////////////////////// 】】 用料管理 /////////////////////////////