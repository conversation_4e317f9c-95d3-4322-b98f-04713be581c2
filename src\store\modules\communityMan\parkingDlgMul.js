// 车位dlg组件

const parkingDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    memberId: "",

    list: []
  },

  getters: {
    dlgShow: state => state.dlgShow,

    memberId: state => state.memberId,

    list: state => state.list
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_MEMBERID: (state, val) => {
      state.memberId = val;
    },

    SET_LIST: (state, val) => {
      state.list = val;
    }
  },

  actions: {}
};

export default parkingDlgMul;
