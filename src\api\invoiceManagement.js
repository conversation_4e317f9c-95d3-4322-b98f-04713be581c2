import request from '@/utils/request'

// 申请开票列表
export function findPayBillSumPage(data) {
    return request({
      url: `/unity/payfeebill/findPayBillSumPage`,
      method: 'post',
      data
    })
  }
// 申请开票提交
export function payInvoiceSave(data) {
    return request({
      url: `/unity/payInvoice/save`,
      method: 'post',
      data
    })
  }
// 详情
export function getInfo(billSumId) {
    return request({
      url: `/unity/payInvoice/getInfo/${billSumId}`,
      method: 'get',
    })
  }
// 查询最近一次抬头
export function getTitle(userId) {
    return request({
      url: `/unity/payInvoice/getTitle/${userId}`,
      method: 'get',
    })
  }
// 申请开票提交
export function payInvoicePage(data) {
  return request({
    url: `unity/payInvoice/page`,
    method: 'post',
    data
  })
}
// 待开开票
export function audit(data) {
  return request({
    url: `unity/payInvoice/audit`,
    method: 'post',
    data
  })
}
// 领取
export function payInvoiceReceive(data) {
  return request({
    url: `unity/payInvoice/receive`,
    method: 'post',
    data
  })
}
// 作废
export function payInvoiceCancel(data) {
  return request({
    url: `unity/payInvoice/cancel`,
    method: 'post',
    data
  })
}