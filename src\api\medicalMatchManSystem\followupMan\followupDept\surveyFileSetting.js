import request from '@/utils/request'

/*
 *科室调查问卷
 */

// 分页查询 
export function findQuestionnaireDynamic(data) {
  return request({
    url: `/follow/findQuestionnaireDynamic`,
    method: 'post',
    data
  })
}

// 删除
export function updateQuestionnaire(data) {
  return request({
    url: `/follow/updateQuestionnaire`,
    method: 'post',
    data
  })
}

// 新增/修改调查问卷接口
export function saveOrUQuestionnaire(data) {
  return request({
    url: `/follow/saveOrUQuestionnaire`,
    method: 'post',
    data
  })
}

// 根据调查问卷id查询内容
export function findQuestionByQuestionnaireId(data) {
  return request({
    url: `/follow/findQuestionByQuestionnaireId`,
    method: 'post',
    data
  })
}

// 根据用户id 调查问卷id查询内容
export function findQuestionByQuestionnaireIdAndUserId(data) {
  return request({
    url: `/follow/findQuestionByQuestionnaireIdAndUserId`,
    method: 'post',
    data
  })
}

// 调查问卷发布
export function releaseQuestionnaire(data) {
  return request({
    url: `/follow/releaseQuestionnaire`,
    method: 'post',
    data
  })
}

// 查找用户
export function findPatientDynamic(data) {
  return request({
    url: `/follow/findPatientDynamic`,
    method: 'post',
    data
  })
}

// 保存用户填写问卷内容
export function saveChooseUser(data) {
  return request({
    url: `/follow/saveChooseUser`,
    method: 'post',
    data
  })
}

// 调查问卷填写详情统计
export function findQuestionWriteInfo(data) {
  return request({
    url: `/follow/findQuestionWriteInfo`,
    method: 'post',
    data
  })
}

// 调查问卷填空项详细信息
export function findQuestionChooseInfo(data) {
  return request({
    url: `/follow/findQuestionChooseInfo`,
    method: 'post',
    data
  })
}
