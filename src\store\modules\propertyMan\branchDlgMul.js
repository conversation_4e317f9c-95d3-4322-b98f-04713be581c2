// 多选部门dlg组件

const branchDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    dlgType: '',

    branchIds: '',

    branchNames: '',

    projectId: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    dlgType: state => state.dlgType,

    branchIds: state => state.branchIds,

    branchNames: state => state.branchNames,

    projectId: state => state.projectId,
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val
    },

    SET_BRANCHIDS: (state, val) => {
      state.branchIds = val
    },

    SET_BRANCHNAMES: (state, val) => {
      state.branchNames = val
    },

    SET_PROJECTID: (state, val) => {
      state.projectId = val
    }
  },

  actions: {

  }
}

export default branchDlgMul
