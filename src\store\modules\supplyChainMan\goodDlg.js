// 商品dlg组件

const goodDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    dlgQuery: {},

    goodId: '',

    goodName: '',

    goodType: '',

    goodInfo: {},

    branchShow: false
  },

  getters: {
    dlgShow: state => state.dlgShow,

    dlgQuery: state => state.dlgQuery,

    goodId: state => state.goodId,

    goodName: state => state.goodName,

    goodType: state => state.goodType,

    goodInfo: state => state.goodInfo,

    branchShow: state => state.branchShow
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_DLGQUERY: (state, val) => {
      state.dlgQuery = val
    },

    SET_GOODID: (state, val) => {
      state.goodId = val
    },

    SET_GOODNAME: (state, val) => {
      state.goodName = val
    },

    SET_GOODTYPE: (state, val) => {
      state.goodType = val
    },

    SET_GOODINFO: (state, val) => {
      state.goodInfo = val
    },

    SET_BRANCHSHOW: (state, val) => {
      state.branchShow = val
    },
  },

  actions: {

  }
}

export default goodDlg
