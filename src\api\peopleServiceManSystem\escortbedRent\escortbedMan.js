import request from '@/utils/request'

/*
* 陪护床管理
*/

// 动态查询陪护床信息 
export function page (data) {
  return request({
    url: `/u/usapi/gxc/page`,
    method: 'post',
    data
  })
}

// 新增修改陪护床信息
export function saveOrUpdate (data) {
  return request({
    url: `/u/usapi/gxc/saveOrUpdate`,
    method: 'post',
    data
  })
}


// 删除
export function del (id) {
  return request({
    url: `/u/usapi/gxc/del/${id}`,
    method: 'get'
  })
}

// 查询陪护床使用详情
export function findUseInfoByRfid (data) {
  return request({
    url: `/u/usapi/ph/findUseInfoByRfid`,
    method: 'post',
    data
  })
}