<template>
  <!-- 规则配置 -->
  <div class="app-container zhangkexin" style="margin-bottom: 32px; padding-bottom: 10px">
    <el-form size="small" ref="searchForm" class="" :model="listQuery" label-width="90px" @submit.native.prevent>
      <el-form-item label="筛选条件：">
        <el-input @keyup.enter.native="searchFunc" class="m-shaixuan-input fl" placeholder="请输入" v-model="listQuery.label"
          style="width: 200px" size="small">
          <i @click="resetSearchItem(['label'])" slot="suffix" class="el-input__icon el-icon-error"></i>
        </el-input>
        <!-- <el-date-picker
          class="fl ml10"
          style="width: 250px"
          @change="searchFunc"
          v-model="listQuery.dateRange"
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          size="small"
        >
        </el-date-picker> -->

        <el-button icon="el-icon-search" type="primary" size="small" class="fl ml10" @click="searchFunc">搜索</el-button>
        <el-button icon="el-icon-plus" type="success" size="small" class="fl ml10" @click="showDlg">新增</el-button>
      </el-form-item>
      <div class="clear"></div>
    </el-form>
    <el-table height="calc(100vh - 320px)" ref="tableBar" class="m-small-table" :data="list" border fit
      highlight-current-row style="width: 100%; height: auto">
      <el-table-column label="序号" align="center" width="60">
        <template slot-scope="scope">
          {{ (listQuery.page - 1) * listQuery.limit + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="规则名称">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人">
        <template slot-scope="scope">
          <span>{{ scope.row.createUserName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="230" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-edit" @click="showRowDlg(scope.row, 'edit')"
            plain>编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="delFun(scope.row)" plain>删除</el-button>
          <el-button type="info" size="mini" icon="el-icon-more" @click="showRowDlg(scope.row, 'info')"
            plain>详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :title="title" :close-on-click-modal="false" :visible.sync="dlgState" width="800px" append-to-body>
      <el-form :disabled="isDis" ref="dlgForm" :rules="dlgRules" :model="dlgData" label-position="right"
        label-width="95px" size="mini" @submit.native.prevent>
        <el-form-item label="规则名称" prop="name">
          <el-input style="width: 70%" v-model="dlgData.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-row>
          <el-col :span="9">
            <el-form-item label="第一接收人" prop="userNamea">
              <el-input style="width: 150px;" v-model="dlgData.userNamea"  :title="dlgData.userNamea" @focus="showUserMulDlg('0')"
                placeholder="请选择第一接收人" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="推送方式" prop="msgTypea">
              <el-checkbox-group v-model="dlgData.msgTypea">
                <!-- <el-checkbox label="1">系统消息(web/小程序)</el-checkbox> -->
                <el-checkbox label="2">短信</el-checkbox>
                <el-checkbox label="3">电话</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4">
            <el-form-item label="">
              <el-checkbox :true-label="1" :false-label="0" v-model="dlgData.isOpenUserb">开启第二接收人,</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="19">
            <el-form-item label="">
              当第一接收人未处理报警超时<el-input-number :disabled="dlgData.isOpenUserb == 0 ? true : false" v-model="dlgData.sendMinb"
                :precision="0" :min="0" label=""></el-input-number>分钟,通知第二接收人
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="第二接收人">
              <el-input :disabled="dlgData.isOpenUserb == 0 ? true : false" :title="dlgData.userNameb" style="width: 150px;"
                v-model="dlgData.userNameb" @focus="showUserMulDlg('1')" placeholder="请选择第二接收人" readonly></el-input>
            </el-form-item></el-col>
          <el-col :span="14">
            <el-form-item label="推送方式">
              <el-checkbox-group v-model="dlgData.msgTypeb" :disabled="dlgData.isOpenUserb == 0 ? true : false">
                <!-- <el-checkbox label="1">系统消息(web/小程序)</el-checkbox> -->
                <el-checkbox label="2">短信</el-checkbox>
                <el-checkbox label="3">电话</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4">
            <el-form-item label="">
              <el-checkbox :true-label="1" :false-label="0" :disabled="dlgData.isOpenUserb == 0 ? true : false"
                v-model="dlgData.isOpenUserc">开启第三接收人,</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="19">
            <el-form-item label="">
              当第二接收人未处理报警超时<el-input-number :disabled="dlgData.isOpenUserc == 1 && dlgData.isOpenUserb == 1
                ? false
                : true
                " v-model="dlgData.sendMinc" :precision="0" :min="0" label=""></el-input-number>分钟,通知第三接收人
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="第三接收人">
              <el-input :disabled="dlgData.isOpenUserc == 1 && dlgData.isOpenUserb == 1
                ? false
                : true
                " style="width: 150px;" v-model="dlgData.userNamec" :title="dlgData.userNamec" @focus="showUserMulDlg('2')" placeholder="请选择第三接收人"
                readonly></el-input> </el-form-item></el-col>
          <el-col :span="14">
            <el-form-item label="推送方式">
              <el-checkbox-group v-model="dlgData.msgTypec" :disabled="dlgData.isOpenUserc == 1 && dlgData.isOpenUserb == 1
                ? false
                : true
                ">
                <!-- <el-checkbox label="1">系统消息(web/小程序)</el-checkbox> -->
                <el-checkbox label="2">短信</el-checkbox>
                <el-checkbox label="3">电话</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgState = false" icon="el-icon-close">关闭</el-button>
        <el-button v-if="!isDis" type="success" @click="subFunc" icon="el-icon-check">
          <span>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <!-- 多选员工 -->
    <selectUserMulDlg ref="userMulDlgRef" :dlgType="dlgUserMulType" :dlgQuery="dlgUserMulQuery"
      :selectList0="dlgUserMulSelectList" @backFunc="dlgUserMulBackFunc" :noRule="noRule"/>
    <Usertree />
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import Usertree from "@/components/Dialog/Usertree";
import Pagination from "@/components/Pagination"; // Secondary package based on el-pagination
import { postAction, getAction } from "@/api";
import jsCookie from "js-cookie";
import selectUserMulDlg from "@/components/DialogWflow/selectUserMulDlg";
import {
  isNull,
  getDbItems,
  objToParam,
  arrId2Name,
  arrIds2Names,
  getPreMonth
} from "@/utils";
let dlgDataEmpty = {
  // 测试数据
  id: "",
  name: "",
  userNamea: "", //第一接收人
  userIda: "",
  msgTypea: [],
  userNameb: "",
  userIdb: "",
  isOpenUserb: 0,
  sendMinb: undefined,
  msgTypeb: [],
  userNamec: "",
  userIdc: "",
  isOpenUserc: 0,
  sendMinc: undefined,
  msgTypec: []
};
export default {
  components: {
    Pagination,
    Usertree,
    selectUserMulDlg
  },
  data() {
    return {
      total: 0,
      // 弹窗
      title: "",
      dlgState: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        name: [{ required: true, message: "必填字段", trigger: "change" }],
        userNamea: [{ required: true, message: "必填字段", trigger: "change" }],
        msgTypea: [{ required: true, message: "必填字段", trigger: "change" }]
      },

      list: [],

      listQuery: {
        // dateRange: [],
        label: "",
        page: 1,
        limit: 20
        // beginDate: "",
        // endDate: ""
      },
      userType: "",

      isDis: false,
      dlgUserMulQuery: {},
      dlgUserMulType: "", // 弹框状态add, edit
      dlgUserMulSelectList: "",
      noRule:false,
      userA: [],
      userB: [],
      userC: [],
      nowUserSelect: null
    };
  },
  computed: {
    ...mapGetters([
      // 部门
      "bmTreeBranchId",
      "bmTreeBranchName"
    ]),
   
    // 部门选人相关
    userTreeState: {
      get: function () {
        let userTreeState = this.$store.getters.userTreeState;
        return userTreeState;
      },
      set: function (newVal) {
        // this.$store.commit('SET_USERTREESTATE', newVal)
      }
    },
    userTreeUserId: {
      get: function () {
        let userTreeUserId = this.$store.getters.userTreeUserId;
        return userTreeUserId;
      },
      set: function (newVal) {
        // this.$store.commit('SET_USERTREEUSERID', newVal)
      }
    },
    userTreeUserName: {
      get: function () {
        let userTreeUserName = this.$store.getters.userTreeUserName;
        return userTreeUserName;
      },
      set: function (newVal) {
        // this.$store.commit('SET_USERTREEUSERNAME', newVal)
      }
    }
  },
  watch: {
    // 用户tree
    userTreeUserId(nVal) {
      if (this.dlgState) {
        if (this.userType === "0") {
          this.dlgData.userIda = nVal;
        } else if (this.userType === "1") {
          this.dlgData.userIdb = nVal;
        } else if (this.userType === "2") {
          this.dlgData.userIdc = nVal;
        }
        //console.log(this.dlgData.chargeLeaderId + "22222222222222");
        // this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      }
      //   else {
      //     if (this.userType === '0') {
      //       this.dlgData.branchLeaderId = nVal
      //     } else {
      //       this.dlgData.chargeLeaderId = nVal
      //     }
      //     this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
      //   }
    },
    userTreeUserName(nVal) {
      if (this.dlgState) {
        if (this.userType === "0") {
          this.dlgData.userNamea = nVal;
        } else if (this.userType === "1") {
          this.dlgData.userNameb = nVal;
        } else if (this.userType === "2") {
          this.dlgData.userNamec = nVal;
        }
        // this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      }
      //   else {
      //     if (this.userType === '0') {
      //       this.dlgData.branchLeader = nVal
      //     } else {
      //       this.dlgData.chargeLeader = nVal
      //     }
      //     this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
      //   }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    showUserMulDlg(key) {
      this.noRule=true
      this.userType = key;
      let list = []
      if (this.userType === "0") {
        let array1=[]
        let array2=[]
        if (this.dlgData.userIda.split(',')!='') {
          array1=this.dlgData.userIda.split(',')
          array2=this.dlgData.userNamea.split(',')
        }
        let newArray = array1.map((item, index) => {return {id: item, label: array2[index]}})
        list = newArray
      } else if (this.userType === "1") {
        let array1=[]
        let array2=[]
        if (this.dlgData.userIdb.split(',')!='') {
          array1=this.dlgData.userIdb.split(',')
          array2=this.dlgData.userNameb.split(',')
        }
        let newArray = array1.map((item, index) => {return {id: item, label: array2[index]}})
        list = newArray
      } else if (this.userType === "2") {
        let array1=[]
        let array2=[]
        if (this.dlgData.userIdc.split(',')!='') {
          array1=this.dlgData.userIdc.split(',')
          array2=this.dlgData.userNamec.split(',')
        }

        let newArray = array1.map((item, index) => {return {id: item, label: array2[index]}})
        list = newArray
      }
      // list.forEach(item => {
      //   item.label = item.name;
      // });
      this.dlgUserMulSelectList = list.length ? JSON.stringify(list) : "";
      this.dlgUserMulQuery = {};
      this.dlgUserMulType = "edit";
      this.$refs.userMulDlgRef.show();

    },
    dlgUserMulBackFunc(list0) {
      console.log("弹窗发挥", list0);
      let list =JSON.parse(JSON.stringify(list0))
      let idArr = []
      let nameArr = []
      for (let index = 0; index < list.length; index++) {
        idArr.push(list[index].id)
        nameArr.push(list[index].label)
      }
      if (this.userType === "0") {
        this.dlgData.userNamea = nameArr.join(',')
        this.dlgData.userIda = idArr.join(',')
      } else if (this.userType === "1") {
        this.dlgData.userNameb = nameArr.join(',')
        this.dlgData.userIdb = idArr.join(',')
      } else if (this.userType === "2") {
        this.dlgData.userNamec = nameArr.join(',')
        this.dlgData.userIdc = idArr.join(',')
      }

    },
    // 清空搜索条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },
    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    // 选人弹窗
    // showUserMulDlg(type) {
    //   this.userType = type;
    //   this.$store.commit("SET_USERTREESQTYPE", "");
    //   this.$store.commit("SET_USERTREESTATE", true);
    // },
    getList() {
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      // 日期范围
      // sendObj.beginDate = "";
      // sendObj.endDate = "";
      // if (
      //   !isNull(this.listQuery.dateRange) &&
      //   this.listQuery.dateRange.length > 0
      // ) {
      //   sendObj.beginDate = this.listQuery.dateRange[0];
      //   sendObj.endDate = this.listQuery.dateRange[1];
      // }
      postAction("/iot/sendMsgConfig/page", sendObj).then(res => {
        if (res.data.code == 200) {
          this.list = res.data.data;
          this.total = res.data.page.total;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    showDlg() {
      this.title = "新增规则配置";
      this.isDis = false;
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgState = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },
    //编辑/详情
    showRowDlg(row, type) {
      if (type == "edit") {
        this.title = "编辑规则配置";
        this.isDis = false;
      }
      if (type == "info") {
        this.title = "规则配置详情";
        this.isDis = true;
      }
      this.dlgData = JSON.parse(JSON.stringify(row));
      console.log(this.dlgData,"this.dlgData");
      if (this.dlgData.msgTypea != "") {
        this.dlgData.msgTypea = this.dlgData.msgTypea.split(",");
      } else {
        this.dlgData.msgTypea = [];
      }
      if (this.dlgData.msgTypeb != "") {
        this.dlgData.msgTypeb = this.dlgData.msgTypeb.split(",");
      } else {
        this.dlgData.msgTypeb = [];
      }
      if (this.dlgData.msgTypec != "") {
        this.dlgData.msgTypec = this.dlgData.msgTypec.split(",");
      } else {
        this.dlgData.msgTypec = [];
      }
      if (this.dlgData.sendMinb == null) {
        this.dlgData.sendMinb = undefined;
      }
      if (this.dlgData.sendMinc == null) {
        this.dlgData.sendMinc = undefined;
      }
      this.dlgState = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },
    delFun(row) {
      this.$confirm("是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        getAction(`/iot/sendMsgConfig/del/${row.id}`).then(res => {
          if (res.data.code == 200) {
            this.getList();
            this.$message.success(res.data.msg);
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    subFunc() {
      this.$refs["dlgForm"].validate(valid => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));
          if (
            (sendObj.isOpenUserb == 1 && !sendObj.sendMinb) ||
            (sendObj.isOpenUserb == 1 && sendObj.sendMinb == "")
          ) {
            this.$message({
              type: "warning",
              message: "请填写通知第二接收人时间间隔"
            });
            return false;
          }
          if (sendObj.isOpenUserb == 1 && sendObj.userIdb == "") {
            this.$message({
              type: "warning",
              message: "请选择第二接收人"
            });
            return false;
          }
          if (
            (sendObj.isOpenUserb == 1 && sendObj.msgTypeb.length <= 0) ||
            (sendObj.isOpenUserb == 1 && sendObj.msgTypeb == "")
          ) {
            this.$message({
              type: "warning",
              message: "请选择第二接收人推送方式"
            });
            return false;
          }
          if (
            (sendObj.isOpenUserc == 1 &&
              sendObj.isOpenUserb == 1 &&
              !sendObj.sendMinc) ||
            (sendObj.isOpenUserc == 1 &&
              sendObj.isOpenUserb == 1 &&
              sendObj.sendMinc == "")
          ) {
            this.$message({
              type: "warning",
              message: "请填写通知第三接收人时间间隔"
            });
            return false;
          }
          if (
            sendObj.isOpenUserc == 1 &&
            sendObj.isOpenUserb == 1 &&
            sendObj.userIdc == ""
          ) {
            this.$message({
              type: "warning",
              message: "请选择第三接收人"
            });
            return false;
          }
          if (
            (sendObj.isOpenUserc == 1 &&
              sendObj.isOpenUserb == 1 &&
              sendObj.msgTypec.length <= 0) ||
            (sendObj.isOpenUserc == 1 &&
              sendObj.isOpenUserb == 1 &&
              sendObj.msgTypec == "")
          ) {
            this.$message({
              type: "warning",
              message: "请选择第三接收人推送方式"
            });
            return false;
          }
          if (typeof sendObj.msgTypea == "string") {
            sendObj.msgTypea = sendObj.msgTypea;
          } else {
            sendObj.msgTypea = sendObj.msgTypea.join(",");
          }
          if (typeof sendObj.msgTypeb == "string") {
            sendObj.msgTypeb = sendObj.msgTypeb;
          } else {
            sendObj.msgTypeb = sendObj.msgTypeb.join(",");
          }
          if (typeof sendObj.msgTypec == "string") {
            sendObj.msgTypec = sendObj.msgTypec;
          } else {
            sendObj.msgTypec = sendObj.msgTypec.join(",");
          }
          if (sendObj.isOpenUserb == 0) {
            sendObj.userNameb = "";
            sendObj.userIdb = "";
            sendObj.msgTypeb = "";
            sendObj.sendMinb = "0";
            sendObj.userNamec = "";
            sendObj.userIdc = "";
            sendObj.msgTypec = "";
            sendObj.sendMinc = "";
            sendObj.isOpenUserc = 0;
          }
          if (sendObj.isOpenUserc == 0) {
            sendObj.userNamec = "";
            sendObj.userIdc = "";
            sendObj.msgTypec = "";
            sendObj.sendMinc = "0";
          }
          // console.log(sendObj, "sendObj");
          // return;
          postAction("/iot/sendMsgConfig/addOrUpdate", sendObj).then(res => {
            if (res.data.code == 200) {
              this.$message.success(res.data.msg);
              this.dlgState = false;
              this.getList();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
