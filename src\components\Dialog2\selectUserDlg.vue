<template>
  <el-dialog
    title="选择员工"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="1000px"
    top="30px"
  >
    <div class="gzb-box">
      <div class="gzb-top">
        <el-input
          @keyup.enter.native="searchFunc"
          class="m-shaixuan-input fl"
          placeholder="关键字"
          v-model="listQuery.label"
          style="width: 200px"
        >
          <i
            @click="resetSearchItem(['label'])"
            slot="suffix"
            class="el-input__icon el-icon-error"
          ></i>
        </el-input>
        <el-button
          icon="el-icon-search"
          type="primary"
          size="mini"
          class="search-right-btn fl"
          @click="searchFunc"
          >搜索</el-button
        >
        <div class="clear"></div>
      </div>

      <!-- class='m-small-table' -->
      <el-table
        v-loading="listLoading"
        height="500"
        class="m-small-table mt10"
        ref="tableRef"
        :data="list"
        border
        fit
        highlight-current-row
        @row-click="tableRowClick"
      >
        <el-table-column label="" align="center" width="60">
          <template slot-scope="scope">
            <el-radio
              v-model="selectRow.id"
              :label="scope.row.id"
              style="width: 16px"
              ><span></span
            ></el-radio>
          </template>
        </el-table-column>
        <el-table-column label="#" align="center" width="60">
          <template slot-scope="scope">
            {{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column
          label="员工姓名"
          prop="label"
          align="center"
          width="100"
        ></el-table-column>
        <el-table-column
          label="工号"
          prop="workNum"
          align="center"
          width="100"
        ></el-table-column>

        <el-table-column
          label="所属部门"
          prop="departmentName"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          label="手机号"
          prop="account"
          align="center"
          width="140"
        ></el-table-column>
      </el-table>

      <pagination
        class="mt10"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.size"
        @pagination="getList"
      />
      <div class="clear"></div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span>确定</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
import Pagination from "@/components/Pagination"; // 分页

// 工具
// import { phoneReg } from '@/utils/regUtil'
import * as utils from "@/utils";
import { postAction, getAction } from "@/api";

// 接口
import Bmtree from "@/components/Dialog/Bmtree";

let listQueryEmpty = {
  label: "",
  page: 1,
  size: 20,

  branchType: "0",
  flag: "0",

  departmentId: "",
  departmentName: ""
};
export default {
  components: {
    Pagination
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {},
    dlgState0: {
      type: Boolean,
      default: false
    },
    dlgData0: {}
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val;
    },
    dlgState(val) {
      if (val) {
        console.log("val", val);
        setTimeout(() => {
          this.selectRow = this.dlgQuery;
          this.searchFunc();
        }, 50);
      } else {
        this.total = 0;
        this.$emit("closeDlg");
      }
    }
  },
  data() {
    return {
      dlgTitle: "",
      dlgType1: "",
      dlgType2: "",

      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        name: [{ required: true, message: "必填字段", trigger: "blur" }],
        tableId: [{ required: true, message: "必填字段", trigger: "change" }]
      },
      dlgSubLoading: false, // 提交loading

      // 列表
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      ////
      selectRow: ""
    };
  },
  created() {},
  methods: {
    tableRowClick(row, column, event) {
      console.log("row", row);
      this.selectRow = row;
    },

    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    getList() {
      this.list = [];
      this.listLoading = true;
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      sendObj.labelNum = sendObj.label;
      delete sendObj.label;

      postAction("/sys/findUserByLabelAndNum/v2", sendObj).then(res0 => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          this.list = res.list;
          this.total = res.data.total;

          this.$nextTick(() => {
            this.$refs.tableRef.doLayout();
          });
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.getList();
    },
    sortChange(data) {
      let type = data.column.sortBy;
      let order = data.order;
      if (order == null) {
        type = "";
        order = "";
      } else {
        if (order == "descending") {
          order = "desc";
        } else {
          order = "asc";
        }
      }
      this.listQuery.sortParam = type;
      this.listQuery.sortOrder = order;
      this.getList();
    },

    dlgSubFunc() {
      console.log("selectRow", this.selectRow);
      this.$emit("dlgUserSubFunc", this.selectRow);
      this.closeDlg();
    },
    subAjaxBack(res0) {
      let res = res0.data;
      this.dlgSubLoading = false;
      if (res.code == 200) {
        this.$message.success(res.msg);
        this.dlgState = false;
        this.$emit("getList");
        this.$emit("closeDlg");
      } else {
        this.$message({
          type: "warning",
          message: res.msg
        });
      }
    },

    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.$emit("closeDlg");
    },

    upList1() {
      this.$emit("getList");
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.gzb-box {
  padding: 0 0px;
  .gzb-top,
  .gzb-bottom {
    // margin-bottom: 10px;
    p {
      margin: 0px;
      display: inline-block;
      margin-right: 40px;
    }
  }
  .gzb-bottom {
    margin-top: 10px;
    p {
      margin-right: 20px;
    }
  }
}
</style>
