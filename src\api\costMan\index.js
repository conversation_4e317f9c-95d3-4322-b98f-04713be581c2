import request from "@/utils/request";

// 折扣设置分页
export function feeDiscountPage(data) {
  return request({
    url: `/unity/payfeeconfig/feeDiscountPage`,
    method: "post",
    data
  });
}

// 折扣设置新增
export function addOrUpdateFeeDiscount(data) {
  return request({
    url: `/unity/payfeeconfig/addOrUpdateFeeDiscount`,
    method: "post",
    data
  });
}

// 折扣设置删除
export function delFeeDiscountById(id, flag) {
  return request({
    url: `/unity/payfeeconfig/delFeeDiscountById/${id}/${flag}`,
    method: "get"
  });
}

// 计费设置分页
export function payFeeConfigPage(data) {
  return request({
    url: `/unity/payfeeconfig/payFeeConfigPage`,
    method: "post",
    data
  });
}

// 新增/修改计费项设置
export function addOrUpdatePayFeeConfig(data) {
  return request({
    url: `/unity/payfeeconfig/addOrUpdatePayFeeConfig`,
    method: "post",
    data
  });
}

// 计费设置修改状态
export function delPayFeeConfigById(id, flag) {
  return request({
    url: `/unity/payfeeconfig/delPayFeeConfigById/${id}/${flag}`,
    method: "get"
  });
}

// 计费设置查询关联折扣信息
export function findInfosById(id) {
  return request({
    url: `/unity/payfeeconfig/findInfosById/${id}`,
    method: "get"
  });
}

// 新增/计费项折扣设置
export function addOrUpdate(data) {
  return request({
    url: `/unity/payfeeconfig/addOrUpdate`,
    method: "post",
    data
  });
}

// 删除计费设置查询关联折扣信息
export function delPayFeeConfigDiscountById(configId, discountId) {
  return request({
    url: `/unity/payfeeconfig/delPayFeeConfigDiscountById/${configId}/${discountId}`,
    method: "get"
  });
}

// 批量创建 缴费
export function addPayFeeBills(data) {
  return request({
    url: `/unity/payfeebill/addPayFeeBills`,
    method: "post",
    data
  });
}

// 分页查询费用记录详情分页
export function payfeebillPage(data) {
  return request({
    url: `/unity/payfeebill/page`,
    method: "post",
    data
  });
}

// 分页查询费用记录
export function findPayBillSumPage(data) {
  return request({
    url: `/unity/payfeebill/findPayBillSumPage`,
    method: "post",
    data
  });
}

// 分页查询费用记录 优惠申请
export function findPayBillSumPageYouhuishenqing(data) {
  return request({
    url: `/unity/payfeebill/findPayBillSumPageYouhuishenqing`,
    method: "post",
    data
  });
}

// 缴费
// 小程序 /unity/order/payOweAmount
// web /unity/payfeebill/payOweAmount
export function payOweAmount(data) {
  return request({
    url: `/unity/order/payOweAmount`,
    method: "post",
    data
  });
}
//多选缴费
export function payOweAmountV(data) {
  return request({
    url: `/unity/order/payOweAmount/v2`,
    method: "post",
    data
  });
}
export function payfeebillPayOweAmount(data) {
  return request({
    url: `/unity/payfeebill/payOweAmount`,
    method: "post",
    data
  });
}

// 取消费用
export function delPayFeeBillSumById(id) {
  return request({
    url: `/unity/payfeebill/delPayFeeBillSumById/${id}`,
    method: "get"
  });
}

// 根据总费单id集合查询详情
export function findAllPayBillInfosBySumIds(id) {
  return request({
    url: `/unity/payfeebill/findAllPayBillInfosBySumIds/${id}`,
    method: "get"
  });
}

// 根据总费单id集合查询详情 分页
export function getPayBillPage(data) {
  return request({
    url: `/unity/payfeebill/getPayBillPage`,
    method: "post",
    data
  });
}

// 分页查询优惠提交记录
export function findBillDiscountApplyPage(data) {
  return request({
    url: `/unity/billdiscountapply/findBillDiscountApplyPage`,
    method: "post",
    data
  });
}

// 新增优惠审核
export function addOrUpdateDiscountApplyConfig(data) {
  return request({
    url: `/unity/billdiscountapply/addOrUpdatePayFeeConfig`,
    method: "post",
    data
  });
}

// 审核优惠记录
export function auditApplyInfo(data) {
  return request({
    url: `/unity/billdiscountapply/auditApplyInfo`,
    method: "post",
    data
  });
}

// 分页查询费用记录详情分页
export function orderPage(data) {
  return request({
    url: `/unity/order/page`,
    method: "post",
    data
  });
}

// 审核交易订单
export function auditPayOrder(data) {
  return request({
    url: `/unity/order/auditPayOrder`,
    method: "post",
    data
  });
}

// 业务受理 缴费列表
export function findPayBillSumPageYewushouli(data) {
  return request({
    url: `/unity/payfeebill/findPayBillSumPageYewushouli`,
    method: "post",
    data
  });
}

// 计算公式分页
export function formulaPage(data) {
  return request({
    url: `/unity/formula/page`,
    method: "post",
    data
  });
}

// 计算公式新增
export function formulaAddOrUpdate(data) {
  return request({
    url: `/unity/formula/addOrUpdate`,
    method: "post",
    data
  });
}

// 计算公式删除
export function formulaDel(id) {
  return request({
    url: `/unity/formula/del/${id}`,
    method: "get"
  });
}

// 物资需求单查询
export function demandPage(data) {
  return request({
    url: `/schain/demand/page`,
    method: "post",
    data
  });
}
// 多选物资需求单 查询商品分组列表
export function listGroupByCommodity(data) {
  return request({
    url: `/schain/demand/listGroupByCommodity`,
    method: "post",
    data
  });
}
// 供应商管理分页
export function supplierPage(data) {
  return request({
    url: `/schain/supplier/page`,
    method: "post",
    data
  });
}
// 新增供应商
export function supplierAdd(data) {
  return request({
    url: `/schain/supplier/add`,
    method: "post",
    data
  });
}
//删除供应商
export function supplierDel(id) {
  return request({
    url: `/schain/supplier/del/${id}`,
    method: "get",
  });
}
// 收据查询分页
export function payBillReportPrint(data) {
  return request({
    url: `/unity/payBillReportPrint/page`,
    method: "post",
    data
  });
}
//收据查询详情
export function payBillReportPrintGetInfo(batchNum) {
  return request({
    url: `/unity/payBillReportPrint/getInfo/${batchNum}`,
    method: "get",
  });
}