<template>
  <!--消毒点位设置-->
  <div class="app-container">
    <div class="filter-container clearfix">
      <el-form inline @submit.native.prevent>
        <el-form-item label="">
          <el-input @focus="showKeshiDlg()" v-model="listQuery.departmentName" readonly placeholder="请选择科室">
            <i @click="resetSearchItem(['departmentId', 'departmentName'])" slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item label="选择类型:">
          <el-select v-model="listQuery.pointTypeId" filterable clearable placeholder="请选择类型">
            <el-option v-for="item in typeDlgList" :key="item.id" :label="item.showName" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字:">
          <el-input style="width: 220px" @keyup.enter.native="getList" placeholder="请输入点位名称" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>

        <el-button icon="el-icon-search" type="success" size="mini" @click="searchItem">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="addItem">添加</el-button>
        <el-button icon='el-icon-download' type="primary" size='mini' @click='xzPrint'>下载二维码</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class="m-small-table" height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="部门科室">
          <template slot-scope="scope">
            <span>{{ scope.row.departmentName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="点位名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="点位类型" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.pointTypeNames }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column label="二维码" width="80" align="center">
        <template slot-scope="scope">
          <el-image style="width: 40px; height: 40px" :preview-src-list="[scope.row.imgUrl]" :src="scope.row.imgUrl" alt=""></el-image>
        </template>
      </el-table-column> -->

        <el-table-column label="操作" align="center" width="320" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button plain icon="el-icon-picture" type="primary" @click="qrcodeItem(scope.row, scope.$index)">二维码</el-button>
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="infoItem(scope.row)">详情</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal="false" title="消毒点位维护" :visible.sync="dlgShow" width="600px" append-to-body>
      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="部门科室" prop="departmentName">
          <el-input
            :disabled="this.dlgType == 'INFO'"
            @focus="showKeshiDlg"
            v-model="dlgData.departmentName"
            readonly
            placeholder="请选择科室"
          ></el-input>
        </el-form-item>
        <el-form-item label="点位名称" prop="name">
          <el-input :disabled="this.dlgType == 'INFO'" v-model="dlgData.name" placeholder="请输入名称" />
        </el-form-item>
        <el-button type="primary" size="mini" @click="checkPoint" v-if="this.dlgType != 'INFO'" style="margin: 0 0 20px 25px"
          >选择点位类型</el-button
        >
        <el-table
          class="m-small-table"
          style="width: 535px; margin-left: 25px"
          max-height="200px"
          v-loading="listLoading"
          :data="dlgData.relationTypes"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

          <el-table-column label="显示名称">
            <template slot-scope="scope">
              <span>{{ scope.row.showName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="320" class-name="small-padding fixed-width" v-if="this.dlgType != 'INFO'">
            <template slot-scope="scope">
              <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delI(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-form-item label="备注" style="margin-top: 20px">
          <el-input
            :disabled="this.dlgType == 'INFO'"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button type="success" :loading="dlgLoading" @click="subDlg" icon="el-icon-check" v-if="this.dlgType != 'INFO'">
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" title="选择点位类型" :visible.sync="typeDlgShow" width="1000" append-to-body>
      <el-form inline @submit.native.prevent>
        <el-form-item label="筛选条件:">
          <el-input @keyup.enter.native="searchFunc" placeholder="请输入关键字" v-model="listTypeQuery.label" clearable> </el-input>
        </el-form-item>

        <el-button icon="el-icon-search" type="success" @click="searchFunc">搜索</el-button>
      </el-form>

      <el-table
        ref="multipleTable"
        class="m-small-table mazhenguo"
        height="430px"
        v-loading="dlgListLoading"
        :data="typeDlgList"
        border
        fit
        :row-key="getRowKeys"
        @selection-change="selectionChange"
        highlight-current-row
        :key="tableKey"
      >
        <el-table-column label="#" align="center" type="selection" :reserve-selection="true" width="50"> </el-table-column>
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="类型名称" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.typeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="显示名称" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.showName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="消毒频率" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.frequency }}</span>
          </template>
        </el-table-column>

        <el-table-column label="消毒时长/h" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.duration }}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column label="表头备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.title }}</span>
          </template>
        </el-table-column>

        <el-table-column label="表尾备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.tail }}</span>
          </template>
        </el-table-column> -->
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="typeDlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button type="success" :loading="dlgLoading" @click="subTypeDlg" icon="el-icon-check">
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
      <div class="page-container">
        <pagination :total="typeTotal" :page.sync="listTypeQuery.page" :limit.sync="listTypeQuery.limit" @pagination="searchFunc" />
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" title="二维码详情" :visible.sync="dialogVisible">
      <div class="qrcode-dom" id="qr-box">
        <canvas id="QRCode" class="qrcode-canvas"></canvas>
        <div>{{ dialogData.qrCode }}</div>
        <div>{{ dialogData.name }}</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" icon="el-icon-back">关闭</el-button>
        <el-button type="success" @click="qrPrint" icon="el-icon-download">导出</el-button>
      </div>
    </el-dialog>

    <selectBmDlg
      :dlgState0="dlgKeshiState"
      :dlgType="dlgKeshiType"
      :dlgQuery="dlgKeshiQuery"
      :dlgSelectData="dlgKeshiSelectData"
      :isRole="false"
      title="科室"
      @closeDlg="closeKeshiDlg"
      @backFunc="dlgKeshibackFunc"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import { medicalWasteConst } from '@/utils/const'
import selectBmDlg from '@/components/Dialog2/selectBmDlg'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import QRCode from 'qrcode' //引入生成二维码插件
import { html2canvasToImage } from '@/utils/domToImage'
import { disPointTypePage, disPointPage, disPointAdd, disPointDel, disPointInfo,exportQrImages} from '@/api/disinfectMan/disinfectConfig'

let dlgDataEmpty = {
  id: '',
  departmentId: '',
  departmentName: '',
  name: '',
  remark: '',
  relationTypes: [],
  pointTypeNames: [],
  projectId: '',
  projectName: '',
}
export default {
  components: {
    Pagination,
    selectBmDlg,
    ElImageViewer,
  },
  data() {
    return {
      // 科室弹窗
      dlgKeshiQuery: {},
      dlgKeshiState: false,
      dlgKeshiType: '', // 弹框状态add, edit
      dlgKeshiSelectData: { id: '', label: '' },

      list: [],
      dlgShow: false, // 新增
      dlgType: '',
      listQuery: {
        page: 1,
        limit: 20,
        pointTypeId: '',
        departmentId: '',
        departmentName: '',
        label: '',
      },
      listTypeQuery: {
        page: 1,
        limit: 20,
        label: '',
      },
      total: 0,
      typeTotal: 0,
      listLoading: false,
      totalItem: {},
      fields: [],
      stateList: medicalWasteConst.stateList,
      batchList: medicalWasteConst.batchList,
      userInfo: {},
      branchType: '0',
      dlgLoading: false,
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      rules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        departmentName: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      dlgListQuery: {
        page: 1,
        limit: 20,
        userId: Cookie.get('userId'),
      },
      dlgListLoading: false,
      typeDlgShow: false,
      typeList: [],
      typeDlgList: [],
      selectList: [],
      tableKey: 0,

      dialogData: {},
      qrcodeMsg: '', //生成二维码信息
      dialogVisible: false,
    }
  },

  computed: {},

  watch: {
    // 通过监听获取数据
    qrcodeMsg(val) {
      console.log(val)
      this.$nextTick(() => {
        // 获取页面的canvas
        let qrcode = document.getElementById('QRCode')
        // 将获取到的数据（val）画到qrcode（canvas）上
        QRCode.toCanvas(
          qrcode,
          val,
          {
            scale: 24,
          },
          function (error) {
            console.log(error)
          }
        )
      })
    },
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.getList()
    this.getTypeList()
  },

  methods: {
    searchFunc(){
      this.getTypeList()
      let idListSel = this.selectList.map((item) => item.id)
      if (idListSel.length == 0) {
        this.$nextTick(() => {
          this.$refs.multipleTable.clearSelection()
        })
      } else {
        this.$nextTick(() => {
          this.typeDlgList.forEach((item) => {
            this.$refs.multipleTable.toggleRowSelection(item, idListSel.includes(item.id))
          })
        })
      }
    },
    // 获取点位类型
    getTypeList() {
      this.dlgListLoading = true
      disPointTypePage(this.listTypeQuery).then((res) => {
        this.dlgListLoading = false
        if (res.data.code == 200) {
          this.typeList = JSON.parse(JSON.stringify(res.data.data))
          this.typeDlgList = JSON.parse(JSON.stringify(res.data.data))
          this.typeTotal = res.data.page.total
          // if (this.dlgShow) {
          //   this.toggleRowSelection()
          // }
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    //导出二维码
    qrPrint() {
      let params = {
        node: document.getElementById('qr-box'),
        pngName: this.qrTitle,
        shouldNotDownload: false,
        responseResultMethod: (response) => {
          console.log(response)
          // loading.close()
          if (response.resultCode != 200) {
            _this.$message.error('导出失败！请使用80以上版本的谷歌浏览器')
          }
        },
      }
      html2canvasToImage(params)
    },
    xzPrint(){
      let sendObj =JSON.parse(JSON.stringify(this.listQuery))
      let label=sendObj.label?'label='+sendObj.label+"&":""
      let departmentId=sendObj.departmentId?'departmentId='+sendObj.departmentId+'&':""
      console.log(departmentId,'departmentId');
      let pointlypeId=sendObj.pointlypeId?'pointlypeId='+sendObj.pointlypeId+"&":""
      let projectId='projectId='+this.userInfo.projectId
      let listQuery=""
      listQuery=label+departmentId+pointlypeId+projectId
      let sendUrl = location.protocol + '//' + location.host + '/saapi/cloth/dis-point/exportQrImages?' + label+departmentId+pointlypeId+projectId
      console.log('---sendUrl', sendUrl)
      window.open(sendUrl)
    },

    showKeshiDlg() {
      if (this.listQuery.departmentName) {
        this.dlgKeshiSelectData = {
          id: this.listQuery.departmentId,
          label: this.listQuery.departmentName,
        }
      } else {
        this.dlgKeshiSelectData = { id: '', label: '' }
      }
      this.dlgKeshiState = true
    },
    dlgKeshibackFunc(data) {
      if (this.dlgShow) {
        this.dlgData.departmentName = data.label
        this.dlgData.departmentId = data.id
      } else {
        this.listQuery.departmentId = data.id
        this.listQuery.departmentName = data.label
      }
      // this.searchFunc()
    },
    closeKeshiDlg() {
      this.dlgKeshiState = false
    },

    // 显示弹窗
    addItem() {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 搜索框 清空单个条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    getList() {
      disPointPage(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    //点位类型弹框
    checkPoint() {
      this.listTypeQuery.page = 1
      this.getTypeList()
      this.typeDlgShow = true
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout()
      })
      this.toggleRowSelection()
      // this.$forceUpdate();
    },
    getRowKeys(row) {
      return row.id
    },
    selectionChange(val) {
      this.selectList = JSON.parse(JSON.stringify(val))
    },
    //多选提交
    subTypeDlg() {
      this.dlgData.relationTypes = this.selectList
      this.typeDlgShow = false
    },
    // 设置选中行
    toggleRowSelection() {
      let idListSel = this.dlgData.relationTypes.map((item) => item.id)
      if (idListSel.length == 0) {
        this.$nextTick(() => {
          this.$refs.multipleTable.clearSelection()
        })
      } else {
        this.$nextTick(() => {
          this.typeDlgList.forEach((item) => {
            this.$refs.multipleTable.toggleRowSelection(item, idListSel.includes(item.id))
          })
        })
      }
    },
    //删除点位
    delI(idx) {
      this.dlgData.relationTypes.splice(idx, 1)
      this.toggleRowSelection()
      // let idList = this.dlgData.relationTypes.map((item) => item.id);
      // this.$nextTick(() => {
      //    for (let item of this.typeDlgList) {
      //      this.$refs.multipleTable.toggleRowSelection(
      //        item,
      //        idList.includes(item.id)
      //      );
      //    }
      //  })
      // this.$forceUpdate();
    },
    subDlg() {
      this.$refs.dlgForm.validate((valid) => {
        if (valid) {
          if (this.dlgData.relationTypes.length <= 0) {
            this.$message.warning('请选择点位类型')
            return
          }
          let sendObj = JSON.parse(JSON.stringify(this.dlgData))
          sendObj.pointTypeNames = []
          // if(!Array.isArray(sendObj.pointTypeNames)){
          //   sendObj.pointTypeNames=sendObj.pointTypeNames.split(',')
          // }
          for (let i of sendObj.relationTypes) {
            i.pointTypeId = i.id
            i.pointTypeName = i.showName
            delete i.attributes
            delete i.createBy
            delete i.createTime
            delete i.duration
            delete i.frequency
            delete i.frequencyValue
            delete i.modes
            delete i.projectId
            delete i.projectName
            delete i.showName
            delete i.tail
            delete i.title
            delete i.typeName
            delete i.updateBy
            delete i.updateTime
            sendObj.pointTypeNames.push(i.pointTypeName)
          }
          delete sendObj.updateBy
          delete sendObj.updateTime
          delete sendObj.createBy
          delete sendObj.createTime
          sendObj.pointTypeNames = sendObj.pointTypeNames.join(',')
          sendObj.projectId = this.userInfo.projectId
          sendObj.projectName = this.userInfo.projectName
          disPointAdd(sendObj).then((res) => {
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },
    //二维码
    qrcodeItem(data, idx) {
      this.dialogVisible = true
      this.qrcodeMsg = data.qrCode
      // console.log('location', location.href)
      // if (location.href.indexOf('jyt.wlines.cn')>=0) {
      //   this.qrcodeMsg = 'https://user.jyt.wlines.cn/jyt/?id=' + data.qrCode
      // } else {
      //   this.qrcodeMsg = 'https://user.jyt.wlines.cn/jyt/?id=' + data.qrCode
      // }
      this.dialogData = data
    },
    // 编辑
    editItem(data) {
      this.getInfo(data.id)
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },
    //获取详情
    getInfo(id) {
      disPointInfo(id).then((res) => {
        let data = JSON.parse(JSON.stringify(res.data.data))
        for (let i of data.relationTypes) {
          i.id = i.pointTypeId
          i.showName = i.pointTypeName
        }
        this.dlgData = data
      })
    },
    // 详情
    infoItem(data) {
      this.getInfo(data.id)
      this.dlgType = 'INFO'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },
    delItem(data, flag) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        disPointDel(data.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-image {
  display: block;
  width: 32px;
  height: 32px;
  margin: 0 auto;

  /deep/ .el-image__error {
    font-size: 12px;
  }
}

/deep/ .el-image-viewer__img {
  background: #ffffff;
}

.el-table--border /deep/ td.border-right,
.el-table--border /deep/ th.border-right {
  border-right: 2px solid #cccccc !important;
}
.qrcode-dom {
  text-align: center;
  padding-bottom: 30px;
  .qrcode-canvas {
    width: 200px !important;
    height: 200px !important;
  }
}
</style>