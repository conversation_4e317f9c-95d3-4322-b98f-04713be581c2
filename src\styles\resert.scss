.m-main {
  padding: 20px;
}
.clear {
  clear: both;
  height: 0px;
  overflow: hidden;
}
.fixed-width .el-button--mini {
  width: auto;
}
.elli, .text-cut {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// elementui 表头错位问题
body .el-table th.gutter {
  display: table-cell !important;
}

.fbold {
  font-weight: bold;
}

// 【【 带值的
.lh28 {
  line-height: 28px !important;
}
.lh32  {
  line-height: 32px;
}
.ml10 {
  margin-left: 10px;
}
.ml20 {
  margin-left: 20px;
}
.ml30 {
  margin-left: 30px;
}
.mr10 {
  margin-right: 10px;
}
.mr20 {
  margin-right: 20px;
}

.mt10 {
  margin-top: 10px;
}
.mt20 {
  margin-top: 20px;
}
.mt30 {
  margin-top: 30px;
}
.mb10 {
  margin-bottom: 10px;
}
.mb30 {
  margin-bottom: 30px;
}
.c999 {
  color: #999;
}
.c666 {
  color: #666;
}
.c333 {
  color: #333;
}

.fs12 {
  font-size: 12px;
}
.fs14 {
  font-size: 14px;
}
.fs16 {
  font-size: 16px;
}
.fs18 {
  font-size: 18px;
}
.fs20 {
  font-size: 20px;
}
.m-a {
  text-decoration: underline;
  color: #409eff;
  cursor: pointer;
}

// 默认绿色
.cBlue {
  color: #42b983;
}
.bgBlue {
  background: #42b983;
}

// 默认黄色
.cYellow {
  color: #f8ac59;
}
.bgBlue {
  background: #f8ac59;
}

// 】】带值的

// 【【 其他通用
.fl {
  float: left;
}
.fr {
  float: right;
}

.dflex {
  display: flex;
  justify-content: space-between;
}
.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flexc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flexct {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.flexlc{
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.flexlt{
  display: flex;
  justify-content: flex-start;
  align-items:flex-start;
}
.flexcc{
  display: flex;
  justify-content:center;
  align-items:center;
}
.flex-sub {
  flex: 1;
}
.flex-v {
  display: flex;
  align-items: center;
}
.dtable {
  display: table;
}
.dtable-cell {
  display: table-cell;
}
.flex1 {
  flex: 1;
}
.flex-auto {
  flex: auto;
}
.align-center {
  align-items: center;
}
.justify-center {
  justify-content: center;

}
.pre {
  position: relative;
}
.pab {
  position: absolute;
}

.fdanger {
  color: #f56c6c;
}
.fwarning {
  color: #e6a23c;
}
.fsuccess {
  color: #67c23a;
}
.fblur {
  color: #409eff;
}
.finfo {
  color: #909399;
}
.el-loading-spinner .el-loading-text {
  color: #19aa8d;
}
.font-bold {
  font-weight: bold;
}
.textrow {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// loading
.el-loading-spinner .path {
  stroke: #19aa8d;
}

.el-radio {
  margin-right: 10px !important;
}

.el-radio:last-child {
  margin-right: 0 !important;
}

.el-checkbox {
  margin-right: 10px !important;
}
// 】】 其他通用
.el-tabs__item:hover {
  color: #19aa8d !important;
}
.el-tabs__item.is-active {
  color: #19aa8d !important;
}
.el-tabs__active-bar {
  background-color: #19aa8d !important;
}

// 蓝色版本
.blue .el-tabs__item:hover {
  color: #1989fa !important;
}
.blue .el-tabs__item.is-active {
  color: #1989fa !important;
}
.blue .el-tabs__active-bar {
  background-color: #1989fa !important;
}
.blue .el-radio__input.is-checked .el-radio__inner {
  border-color: #1989fa;
  background: #1989fa;
}

.blue .el-radio.is-checked .el-radio__label {
  color: #1989fa
}

// 提示框 背景框
.m-tips {
  font-size: 14px;
  color: #666;
  border: 1px solid #dcdfe6;
  line-height: 36px;
  padding: 0 16px;
  border-radius: 3px;
  box-shadow: 0 2px 6px 0 rgba(114, 124, 245, 0.5);
  margin-bottom: 20px;
  span {
    display: inline-block;
    margin-right: 30px;
  }
}
.m-desc {
  font-size: 12px;
  float: right;
  color: #e6a23c;
}

// 复选框 的树
.el-tree-node__label {
  margin-left: 5px;
}

// a标签，返回
.aBacklink {
  display: inline-block;
  line-height: 28px;
  height: 28px;
  color: #909399;
  &:hover {
    color: #67c23a;
  }
}
.m-neirong {
  max-height: 500px;
  overflow: auto;
}

// 表格固定列 横向滚动条拖拽无效
// .el-table .el-table__fixed {
//   height:auto !important;
//   bottom:16px;
// }


// 字体
.text-bold {
  font-weight: bold;
}
.text-bolder {
  font-weight: bolder;
}
.text-black {
  color: #333333;
}
.text-red {
  color: #e54d42 !important;
}
.text-blue {
  color: #409EFF !important;
}
.text-center {
text-align: center;
}
.text-right {
  text-align: right;
}

.m-small-table {
  // 复选
  .el-checkbox {
    margin-right: 0px !important;
  }
  
}


// 新版搜索样式
.search-item {
  float: left;
  margin-bottom: 10px;
  margin-right: 10px;
  .search-item-label {
    float: left;
    line-height: 32px;
    font-size: 14px;
  }
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}
// 日期范围 至
.el-date-editor .el-range-separator {
  width: auto;
}
