<template>
  <div>
    <div v-if="mode === 'DESIGN'">
      <el-button disabled icon="el-icon-user" type="primary" size="mini" round
        >选择人员</el-button
      >
      <span class="placeholder"> {{ placeholder }}</span>
    </div>
    <div v-else-if="mode === 'PC' && !readonly" style="max-width: 350px">
      <!-- showUserMulDlg -->
      <!-- $refs.orgPicker.show() -->
      <el-button
        icon="el-icon-user"
        type="primary"
        size="mini"
        round
        @click="showUserMulDlg"
        >选择人员</el-button
      >

      <span class="placeholder"> {{ placeholder }}</span>
      <div style="margin-top: 5px">
        <el-tag
          size="mini"
          style="margin: 5px"
          closable
          v-for="(dept, i) in _value"
          :key="i"
          @close="delDept(i)"
          >{{ dept.name }}</el-tag
        >
      </div>

      <selectUserMulDlg
        v-if="multiple"
        :deviceType="mode"
        ref="userMulDlgRef"
        :dlgType="dlgUserMulType"
        :dlgQuery="dlgUserMulQuery"
        :selectList0="dlgUserMulSelectList"
        @backFunc="dlgUserMulBackFunc"
      />

      <selectUserDlg
        v-else
        :deviceType="mode"
        :dlgState0="dlgUserState"
        :dlgData0="dlgUserData"
        :dlgType="dlgUserType"
        :dlgQuery="dlgUserQuery"
        @closeDlg="closeUserDlg"
        @dlgUserSubFunc="dlgUserSubFunc"
      />

      <!-- <org-picker
        type="user"
        :multiple="multiple"
        ref="orgPicker"
        :selected="_value"
        @ok="selected"
      /> -->
    </div>
    <div v-else-if="mode === 'MOBILE' && !readonly">
      <!-- $refs.orgPicker.show() showUserMulDlg -->
      <field
        readonly
        clearable
        @clear="_value = []"
        right-icon="arrow"
        clickable
        v-model="deptDesc"
        :placeholder="placeholder"
        @click="showUserMulDlg"
      ></field>
      <selectUserMulDlg
        v-if="multiple"
        :deviceType="mode"
        ref="userMulDlgRef"
        :dlgType="dlgUserMulType"
        :dlgQuery="dlgUserMulQuery"
        :selectList0="dlgUserMulSelectList"
        @backFunc="dlgUserMulBackFunc"
      />

      <selectUserDlg
        v-else
        :deviceType="mode"
        :dlgState0="dlgUserState"
        :dlgData0="dlgUserData"
        :dlgType="dlgUserType"
        :dlgQuery="dlgUserQuery"
        @closeDlg="closeUserDlg"
        @dlgUserSubFunc="dlgUserSubFunc"
      />

      <!-- <org-picker
        :pc-mode="false"
        type="user"
        :multiple="multiple"
        ref="orgPicker"
        :selected="_value"
        @ok="selected"
      /> -->
    </div>
    <div v-else class="preview">
      <!-- <el-tag
        size="mini"
        style="margin: 5px"
        v-for="(user, index) in _value"
        :key="index"
        >{{ user.name }}</el-tag
      > -->

      <!-- {{ user.name }} -->
      <avatar
        :size="35"
        :name="user.name"
        showY
        src="https://erp.wlines.cn/static/image/user_avatar.png"
        v-for="(user, index) in _value"
        :key="index"
      />
    </div>
  </div>
</template>

<script>
import * as utils from "@/utils/index";
import Avatar from "@/components/workFlow/common/Avatar";
import { Field } from "vant";
import componentMinxins from "../ComponentMinxins";
import OrgPicker from "@/components/workFlow/common/OrgPicker";
import selectUserMulDlg from "@/components/DialogWflow/selectUserMulDlg";
import selectUserDlg from "@/components/DialogWflow/selectUserDlg";

export default {
  mixins: [componentMinxins],
  name: "DeptPicker",
  components: { Field, OrgPicker, selectUserMulDlg, selectUserDlg, Avatar },
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      }
    },
    placeholder: {
      type: String,
      default: "请选择人员"
    },
    multiple: {
      type: Boolean,
      default: false
    },
    expansion: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  computed: {
    deptDesc: {
      get() {
        if (this._value && this._value.length > 1) {
          return `${this._value[0].name}${this._value[1].name}..等${
            this._value.length
          }人`;
        } else if (this._value && this._value.length > 0) {
          return this._value[0].name;
        } else {
          return null;
        }
      },
      set(val) {}
    }
  },
  data() {
    return {
      showOrgSelect: false,

      dlgUserMulQuery: {},
      dlgUserMulType: "", // 弹框状态add, edit
      dlgUserMulSelectList: "",

      //
      dlgUserQuery: {},
      dlgUserState: false,
      dlgUserType: "", // 弹框状态add, edit
      dlgUserData: {}
    };
  },
  methods: {
    selected(values) {
      console.log("====values", values);
      this.showOrgSelect = false;
      this._value = values;
    },
    delDept(i) {
      this._value.splice(i, 1);
    },

    //
    showUserMulDlg() {
      if (!this.multiple) {
        this.showUserDlg();
        return false;
      }

      if (this._value) {
        let list = JSON.parse(JSON.stringify(this._value));
        list.forEach(item => {
          item.label = item.name;
        });

        this.dlgUserMulSelectList = JSON.stringify(list);
      } else {
        this.dlgUserMulSelectList = "";
      }

      this.dlgUserMulQuery = {};

      this.dlgUserMulType = "edit";
      this.$refs.userMulDlgRef.show();
    },

    dlgUserMulBackFunc(list0) {
      console.log("弹窗发挥", list0);
      let select = [];
      for (let item of list0) {
        let obj = {
          type: "user",
          id: item.id,
          name: item.label,

          isLeader: null,
          avatar: "",
          sex: null,
          selected: null
        };

        select.push(obj);
      }

      this.showOrgSelect = false;
      this._value = select;
    },
    //////

    showUserDlg() {
      this.dlgUserQuery = "";
      this.dlgUserState = true;
    },
    // 关闭弹窗
    closeUserDlg() {
      this.dlgUserState = false;
    },
    // 选择员工返回
    dlgUserSubFunc(data) {
      console.log("车辆返回", data);
      if (utils.isNull(data)) return false;
      // this.dlgData.userId =data.id
      // this.dlgData.userName =data.label

      let list0 = [data];

      let select = [];
      for (let item of list0) {
        let obj = {
          type: "user",
          id: item.id,
          name: item.label,

          isLeader: null,
          avatar: "",
          sex: null,
          selected: null
        };

        select.push(obj);
      }

      this._value = select;
    }
  }
};
</script>

<style scoped>
.placeholder {
  margin-left: 10px;
  color: #adabab;
  font-size: smaller;
}
.preview {
  display: flex;
  justify-content: left;
  /deep/ .avatar {
    margin: 0 5px;
  }
}
</style>
