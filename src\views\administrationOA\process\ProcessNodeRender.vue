<template>
  <div class="process-node-render">
    <div>
      <div style="font-size: 16px">{{ task.title }}</div>
      <span style="color:#a8adaf;" v-if="task.users">{{ desc }}</span>
    </div>

    <div
      v-if="task.users && task.users.length > 2 && !task.enableEdit"
      style="display:flex;"
    >
      <avatar
        :closeable="task.enableEdit"
        @close="delUser(0)"
        :size="46"
        showY
        :src="task.users[0].avatar"
        :type="task.users[0].type"
        :name="task.users[0].name"
        key="user_0"
      ></avatar>
      <avatar
        :closeable="task.enableEdit"
        @close="delUser(1)"
        :size="46"
        showY
        :src="task.users[1].avatar"
        :type="task.users[1].type"
        :name="task.users[1].name"
        key="user_1"
      ></avatar>
      <div style="line-height: 62px">等共{{ task.users.length }}人</div>
    </div>
    <div v-else style="display:flex;">
      <avatar
        :closeable="user.enableEdit"
        @close="delUser(i)"
        :size="46"
        showY
        :src="user.avatar"
        :type="user.type"
        :name="user.name"
        v-for="(user, i) in task.users"
        :key="'user_' + i"
      ></avatar>
      <span
        :class="
          `add-user ${isValidate && task.users.length === 0 ? 'red' : ''}`
        "
        v-if="task.enableEdit && (task.multiple || task.users.length === 0)"
        @click="$emit('addUser', task)"
      >
        <i class="el-icon-plus" style="width: 46px; height: 46px"></i>
        <div>添加</div>
      </span>
    </div>
  </div>
</template>

<script>
import Avatar from "@/components/workFlow/common/Avatar";

export default {
  name: "ProcessNodeRender",
  props: {
    desc: {
      type: String,
      required: ""
    },
    task: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    },

    isValidate: {
      type: Boolean,
      default: false
    }
  },
  components: { Avatar },
  data() {
    return {};
  },

  methods: {
    delUser(i) {
      this.task.users.splice(i, 1);
      this.$emit("delUser", this.task.id, i);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/workFlow/theme.scss";
.add-user {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 56px;
  i {
    padding: 13px;
    font-size: 1.1rem;
    border-radius: 50%;
    border: 1px dashed #8c8c8c;
    cursor: pointer;

    &:hover {
      color: $theme-primary;
      border: 1px dashed $theme-primary;
    }
  }

  &.red {
    i {
      border: 1px dashed red;
      cursor: pointer;
      &:hover {
        color: red;
        border: 1px dashed red;
      }
    }
  }
}
</style>
