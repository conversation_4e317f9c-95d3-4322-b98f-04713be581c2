<template>
  <!-- 弹窗 新增/编辑 -->
  <el-dialog
    class="mazhenguo"
    title="新增/编辑成员信息"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="600px"
    top="30px"
  >
    <el-form
      ref="dlgDataForm"
      :rules="dlgRules"
      :model="dlgData"
      label-position="right"
      label-width="90px"
      style="width: 550px"
      size="mini"
      @submit.native.prevent
    >
      <el-form-item label="手机" prop="phone">
        <el-input v-model="dlgData.phone" placeholder="请填写手机号码" @input="getOwnerInfo"></el-input>
      </el-form-item>

      <el-form-item label="姓名" prop="name">
        <el-input v-model="dlgData.name" placeholder="请填写姓名"></el-input>
      </el-form-item>

      <el-row>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="dlgData.sex">
              <el-radio :label="'男'">男</el-radio>
              <el-radio :label="'女'">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年龄">
            <el-input-number v-model="dlgData.age" :controls="false" :min="0" :precision="0" :step="1"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="身份证" prop="idCard">
        <el-input v-model="dlgData.idCard" placeholder="请填写身份证号码"></el-input>
      </el-form-item>

      <el-form-item label="类型" prop="type">
        <el-radio-group v-model="dlgData.type">
          <!-- <el-radio label="1">业主</el-radio> -->
          <el-radio label="2">成员</el-radio>
          <el-radio label="3">租户</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="与业主关系">
        <el-select v-model="dlgData.relationship" placeholder="与业主关系">
          <el-option v-for="item in relationshipList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="微信">
        <el-input v-model="dlgData.wchat" placeholder="请填写微信"></el-input>
      </el-form-item>

      <el-form-item label="QQ">
        <el-input v-model="dlgData.qq" placeholder="请填写QQ"></el-input>
      </el-form-item>

      <el-form-item label="照片">
        <el-upload class="avatar-uploader" action="" :show-file-list="false" :before-upload="beforeUpload">
          <el-image v-if="dlgData.photo" class="upload-img" :src="dlgData.photo" alt=""></el-image>
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          <i v-if="dlgData.photo" @click.stop="delUploadImg()" class="el-icon-error avatar_icon"></i>
        </el-upload>
      </el-form-item>

      <el-form-item label="备注">
        <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" v-model="dlgData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
import Tinymce from '@/components/Tinymce' // 富文本组件
// 工具
// import { phoneReg } from '@/utils/regUtil'
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
// 接口
import * as utils from '@/utils'
import { postAction, getAction } from '@/api'
import { communityPage } from '@/api/communityMan'
import { payFeeConfigPage } from '@/api/costMan'
import { buildingmemberAddOrUpdate, buildingmemberPhone } from '@/api/ownerMan'

let dlgDataEmpty = {
  id: '',
  age: '',
  name: '',
  phone: '',
  photo: '',
  remark: '',
  idCard: '',
  sex: '男',
  wchat: '',
  qq: '',
  roomId: '',
  relationship: '',
  relationshipName: '',

  type: '2', // 2-业主 成员 租户
}
export default {
  components: {
    Tinymce,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
    roomInfo: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          let dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
          if (this.dlgType != 'add') {
            dlgData = this.dlgQuery
            // dlgData.rollId = this.dlgQuery.rollId
            // dlgData.userId = this.dlgQuery.userId
            // dlgData.name = this.dlgQuery.staffName
            // dlgData.branchName = this.dlgQuery.staffName
            // 净领额
          }
          this.dlgData = JSON.parse(JSON.stringify(dlgData))
          this.$nextTick(() => {
            this.$refs['dlgDataForm'].clearValidate()
          })
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      userInfo: {},

      // 弹窗
      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        roomId: [{ required: true, message: '必填字段', trigger: 'change' }],
        sex: [{ required: true, message: '必填字段', trigger: 'blur' }],
        name: [{ required: true, message: '必填字段', trigger: 'change' }],
        phone: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: '手机号码格式有误！',
            trigger: 'blur',
          },
        ],
        idCard: [
          {
            pattern:
              /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
            message: '证件号码格式有误！',
            trigger: 'blur',
          },
        ],
      },
      dlgSubLoading: false, // 提交loading

      // 下拉框
      relationshipList: [],
    }
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    utils.getDataDict(this, 'relationship', 'relationshipList')
  },
  methods: {
    // 获取业主信息
    getOwnerInfo() {
      if (this.dlgData.phone.length == 11 && this.dlgType == 'add') {
        let phone = this.dlgData.phone
        buildingmemberPhone(this.dlgData.phone).then((res) => {
          this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
          this.dlgData.phone = phone
          if (res.data.code == 200) {
            this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(res.data.data)))
          }
        })
      }
    },
    // 弹窗提交 ------
    dlgSubFunc() {
      this.$refs['dlgDataForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.relationshipName = utils.getNameById(postParam.relationship, this.relationshipList)
          postParam.rooms = [this.roomInfo]
          // postParam.type = 2
          delete postParam.garages

          // this.dlgSubLoading = true
          buildingmemberAddOrUpdate(postParam).then((res0) => {
            let res = res0.data
            this.dlgSubLoading = false
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.dlgState = false
              this.$emit('getList')
              this.$emit('closeDlg')
              this.$emit('upList1')
            } else {
              this.$message({
                type: 'warning',
                message: res.msg,
              })
            }
          })
        }
      })
    },

    closeDlg() {
      this.dlgLoading = false
      this.dlgSubLoading = false
      this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')

      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.$nextTick(() => {
        this.$refs['dlgDataForm'].clearValidate()
      })
    },

    // 上传对话框图片
    beforeUpload(file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/memberInfo_').then((res) => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg() {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>