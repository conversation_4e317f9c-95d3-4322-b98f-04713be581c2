<template>
  <div class="">
    <!-- 考勤管理 弹窗 -->
    <el-dialog :close-on-click-modal='false' 
      :title="title" 
      
      width='800px'
      top='30px'
      append-to-body
      :visible.sync="faceState">
       <!-- :rules="" :model="" -->
      <el-form ref="addForm" label-position="right" label-width="90px" style="width: 660px; margin-left:30px;">
        <!-- {{ faceId }}
        {{ facePunshId }} -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名">
              <el-input v-model="faceLabel" :disabled="true"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位">
              <el-input v-model="facePLabel" :disabled="true"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="工号">
              <el-input v-model="faceWorkNum" :disabled="true"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门">
              <el-input v-model="faceBLabel" :disabled="true"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if='faceType !== 2 && faceType !== "hmd" && faceType !== "wgdk" && faceType !== "dkjl"'>
          <el-col :span="12">
            <el-form-item label="录入原因">
              <el-input v-model="faceInputCauseType" :disabled="true"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" v-if='faceType !== "hmd" || faceType !== "wgdk"'>
              <el-input v-model="faceIdNumber" :disabled="true"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if='faceType === 2 || faceType === "hmd" || faceType === "wgdk" || faceType === "dkjl"'>
          <el-col :span="12">
            <el-form-item label="打卡时间">
              <el-input v-model="faceInputCauseType" :disabled="true"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" v-if='faceType !== "hmd" && faceType !== "wgdk" && faceType !== "dkjl"'>
              <!-- faceChouchaState 0 正常，1 非活体打卡嫌疑， 2 重点监管 -->
              <span v-if='faceChouchaState === 0' class='fsuccess'>正常</span>
              <span v-if='faceChouchaState === 1' class='fwarning'>违规打卡</span>
              <span v-if='faceChouchaState === 2' class='fdanger'>重点监管</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row class="img-box">
          <el-col :span="12">
            <div class="img-box-item">
              <p class="diaface-p" v-if='faceType === 2 || faceType === "hmd" || faceType === "wgdk" || faceType === "dkjl"'>录入照片</p>
              <p class="diaface-p" v-else>打卡录入照片</p>
              <img :src="faceImg1New" alt="" @click="showBigImg(faceImg1New)">
            </div>
          </el-col>
          <el-col :span="11" :offset="1">
            <div class="img-box-item">
              <p class="diaface-p" v-if='faceType === 2 || faceType === "hmd" || faceType === "wgdk" || faceType === "dkjl"'>打卡照片</p>
              <p class="diaface-p" v-else>身份证头像面照片</p>
              <!-- <img :src="addData.face_img_url" alt="" @click="showBigImg(addData.face_img_url)"> -->
              <img :src="faceImg2New" alt="" @click="showBigImg(faceImg2New)">
            </div>
          </el-col>
        </el-row>
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- 人脸录入审核 -->
        <div v-if='faceType === 0'>
          <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
          <!-- <el-button type="danger" @click="authFunc('2')">审核不通过</el-button> -->
          <!-- <el-button type="primary" @click="authFunc('1')">审核通过</el-button> -->

          <el-button v-if='!faceIsDesc' type='danger' :loading='formSubLoading1' @click="authFunc('2')">
            <span v-if='formSubLoading1 === true'>提交中</span>
            <span v-else>审核不通过</span>
          </el-button>
          <el-button v-if='!faceIsDesc' type='primary' :loading='formSubLoading2' @click="authFunc('1')">
            <span v-if='formSubLoading2 === true'>提交中</span>
            <span v-else>审核通过</span>
          </el-button>
        </div>
        <!-- 人脸监管 -->
        <div v-if='faceType === 1'>
          <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
          <el-button v-if='!faceIsDesc' type='danger' :loading='formSubLoading1' @click="authFunc('3')">
            <span v-if='formSubLoading1 === true'>提交中</span>
            <span v-else>监管驳回</span>
          </el-button>
        </div>
        <!-- 考勤抽查 -->
        <div v-if='faceType === 2'>
          <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
          <!-- faceChouchaState 0 正常，1 非活体打卡嫌疑， 2 重点监管 -->
          <!-- <el-button v-if='faceChouchaState === 1' type="danger" @click="btnAuthFunc(0)">排除嫌疑</el-button>
          <el-button v-if='faceChouchaState !== 2' type="primary" @click="btnAuthFunc(1)">重点监管</el-button>
          <el-button v-if='faceChouchaState === 2' type="primary" @click="btnAuthFunc(2)">取消监管</el-button> -->
          <el-button v-if='faceChouchaState !== 1 && !faceIsDesc' type='danger' :loading='formSubLoading1' @click="btnWgYesFunc(1)">
            <span v-if='formSubLoading1 === true'>提交中</span>
            <span v-else>违规打卡</span>
          </el-button>
          <el-button v-if='faceChouchaState === 1 && !faceIsDesc' type='primary' :loading='formSubLoading1' @click="btnWgYesFunc(0)">
            <span v-if='formSubLoading1 === true'>提交中</span>
            <span v-else>排除违规嫌疑</span>
          </el-button>
          <el-button v-if='faceChouchaState !== 2 && !faceIsDesc && faceIsZdjg != 1' type='danger' :loading='formSubLoading2' @click="btnAuthFunc(1)">
            <span v-if='formSubLoading2 === true'>提交中</span>
            <span v-else>重点监管</span>
          </el-button>
          <el-button v-if='faceIsZdjg == 1' type='primary' :loading='formSubLoading3' @click="btnAuthFunc(2)">
            <span v-if='formSubLoading3 === true'>提交中</span>
            <span v-else>取消监管</span>
          </el-button>
        </div>

        <!-- 黑名单 -->
        <div v-if='faceType === "hmd"'>
          <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
          <!-- faceChouchaState 0 正常，1 非活体打卡嫌疑， 2 重点监管 -->

          <el-button v-if='!faceIsDesc' type='primary' :loading='formSubLoading1' @click="btnAuthFunc(2)">
            <span v-if='formSubLoading3 === true'>提交中</span>
            <span v-else>取消监管</span>
          </el-button>
        </div>

        <!-- 违规打卡 -->
        <div v-if='faceType === "wgdk"'>
          <el-button type='primary' :loading='formSubLoading1' @click="btnWgYesFunc(0)">
            <span v-if='formSubLoading1 === true'>提交中</span>
            <span v-else>排除违规嫌疑</span>
          </el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>
        
        
      </div>
      <Imgenlarge />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import adminDashboard from './admin'
// import editorDashboard from './editor'
import { updateFaceEntryStatus, updatePunchRecordById, updatePunchExcByPunchId } from '@/api/attendanceMan' // 审核 , , 违规打卡状态
import Imgenlarge from '@/components/Dialog/Imgenlarge'

export default {
  name: 'Dashboard',
  components: { Imgenlarge },
  data() {
    return {
      title: '',
      formSubLoading1: false,
      formSubLoading2: false,
      formSubLoading3: false,
      
      // 解决图片旋转问题
      // ?x-oss-process=image/resize,w_300/auto-orient,1
      faceImg1New: '',
      faceImg2New: ''
    }
  },
  computed: {
    ...mapGetters([
      'faceUserId',
      'faceIsDesc',
      'faceChouchaState',
      'faceId',
      'facePunshId',
      'faceLabel',
      'facePLabel',
      'faceWorkNum',
      'faceBLabel',
      'faceInputCauseType',
      'faceIdNumber',
      'faceImg1',
      'faceImg2', 

      'faceIsZdjg'

    ]),
    
    // 审核弹窗
    faceType() {
      let faceType = this.$store.getters.faceType
      switch(faceType) {
        case 0:
          this.title = '人脸录入审核'
          break;
        case 1:
          this.title = '人脸录入监管'
          break;
        case 2:
          this.title = '考勤抽查'
          break;
        case 'hmd':
          this.title = '被加入黑名单当次打卡'
          break;
        case 'wgdk':
          this.title = '违规打卡详情'
          break;
        case 'dkjl':
          this.title = '打卡记录'
          break;
      }
      return faceType
    },
    faceState: {
      get: function() {
        let state = this.$store.getters.faceState
        if (!state) {
          this.$store.commit('SET_FACEISZDJG', '')
        }
        
        return state
      },
      // set: function(newVal) {
      //   this.$store.commit('SET_FACESTATE', newVal)
      // }
      set: function(newVal) {
        console.log('faceState.set', newVal)
        if (newVal == false) {
          this.$store.commit('SET_FACEIMG1', 'empty')
          this.$store.commit('SET_FACEIMG2', 'empty')
        }
        this.$store.commit('SET_FACESTATE', newVal)
      }
    },

    // 大图
    imgEnlargeUrl: {
      get: function() {
        return this.$store.getters.imgEnlargeUrl
      },
      set: function(newVal) {
        this.$store.commit('SET_ENLARGEURL', newVal)
        
      }
    },
    imgEnlargeState: {
      get: function() {
        return this.$store.getters.imgEnlargeState
      },
      set: function(newVal) {
        this.$store.commit('SET_ENLARGESTATE', newVal)
      }
    },

  },
  watch: {
    faceChouchaState(val) {
      console.log(val)
    },
    faceImg1(val) {
      console.log("faceImg1__"+val);
      if (val&&!val.includes('?x-oss-process=image')) {
        console.log("faceImg1");
        this.faceImg1New = val + '?x-oss-process=image/resize,w_300/auto-orient,1'
      }
    },
    faceImg2(val) {
      console.log("faceImg2__"+val);
      if (val&&!val.includes('?x-oss-process=image')) {
        console.log("faceImg2");
        this.faceImg2New = val + '?x-oss-process=image/resize,w_300/auto-orient,1'
      }
    },

  },
  created() {

  },
  
  methods: {
    // 【【 0人脸录入审核 审核方法 0 审核通过，1 审核不通过
    authFunc(status) {
      if (status === '1') {
        this.formSubLoading2 = true
      } else if (status === '2') {
        this.formSubLoading1 = true
      } else if (status === '3') {
        this.formSubLoading1 = true
      }
      updateFaceEntryStatus(this.faceId, status).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        
        if (code === '200') {
          let msg1 = ''
          if (status === '1') {
            msg1 = '提交【审核通过】成功'
            this.formSubLoading2 = false
          } else if (status === '2') {
            msg1 = '提交【审核不通过】成功'
            this.formSubLoading1 = false
          } else if (status === '3') {
            msg1 = '提交【监管驳回】成功'
            this.formSubLoading1 = false
          }
          this.$message.success(msg1)

          this.$store.commit('SET_FACECANREFRESH', false)
          setTimeout(() => {
            this.$store.commit('SET_FACECANREFRESH', true)
            this.closeDialog()
          }, 50)
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 】】 0人脸录入审核  审核方法
    // 审核打卡 
    btnAuthFunc(btnType) {
      let msg1 = ''
      let apId = ''
      let punchExc = ''
      let apbStatus = ''
      if (btnType === 0) {
        msg1 = '提交【排除嫌疑】成功'
        punchExc = 0
        apbStatus = ''
        apId = this.faceId
        this.formSubLoading1 = true
      } else if (btnType === 1) {
        apId = this.faceId
        msg1 = '提交【重点监管】成功'
        punchExc = ''
        apbStatus = 0
        this.formSubLoading2 = true
      } else {
        // apId = this.facePunshId
        apId = this.faceId
        msg1 = '提交【取消监管】成功'
        punchExc = ''
        apbStatus = 1
        this.formSubLoading3 = true
      }
    
      let sendObj = {
        userId: this.faceUserId,
        apId,
        punchExc,
        apbStatus,
      }
      
      updatePunchRecordById(sendObj).then(res => {
        if (btnType === 0) {
          this.formSubLoading1 = false
        } else if (btnType === 1) {
          this.formSubLoading2 = false
        } else {
          this.formSubLoading3 = false
        }
        let code = res.data.code
        let msg = res.data.msg
        if (code === '200') {
          this.$message.success(msg1)
          this.$store.commit('SET_FACECANREFRESH', false)
          setTimeout(() => {
            this.$store.commit('SET_FACECANREFRESH', true)
            this.closeDialog()
          }, 50)
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 排除违规打卡嫌疑
    btnWgYesFunc(puchExc) {
      this.formSubLoading1 = true
      let sendObj = {
        puchExc,
        punchId: this.faceId
      }
      
      updatePunchExcByPunchId(sendObj).then(res => {
        this.formSubLoading1 = false
        let code = res.data.code
        let msg = res.data.msg
        if (code === '200') {
          this.$message.success(msg)
          this.$store.commit('SET_FACECANREFRESH', false)
          setTimeout(() => {
            this.$store.commit('SET_FACECANREFRESH', true)
            this.closeDialog()
          }, 50)
        } else {
          this.$message.error(msg)
        }
      })
      
    },
    // 图片方法
    showBigImg(url) {
      this.imgEnlargeUrl = url
      this.imgEnlargeState = true

      this.$store.commit('SET_ENLARGEISPROCESS', false)
    },
    // 提交
    bumenOkFunc() {
      this.faceState = false
    },

    // 关闭弹窗 
    closeDialog() {
      this.faceState = false
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">

.dialog-footer-span {
  font-size: 14px;
  color: #666;
  display: inline-block;
  padding-right: 10px;
}

// 审核弹窗图片
.m-item-box {
  display: flex;
  justify-content: space-between;
}
.m-item-box .el-form-item {
  width: 300px;
}
.img-box {
  margin-left: 20px;

  p {
    text-align: center;
  }
  img {
    cursor: pointer;
    width: 300px;
    height: 300px;
    border: 1px solid #dcdfe6
  }
}
.img-box-item {
  width: 300px;
}
.diaface-p {
  margin-bottom: 20px;
}
</style>