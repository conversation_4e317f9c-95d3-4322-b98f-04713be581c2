
const bmTree = {
  state: {
    bmTreeState: false,
    bmTreeIsRole: false,  // 是否根据权限
    bmTreeBranchId: '',
    bmTreeBranchName: ''
  },

  mutations: {
    SET_BMTREESTATE: (state, bmTreeState) => {
      state.bmTreeState = bmTreeState
    },
    SET_BMTREEISROLE: (state, val) => {
      state.bmTreeIsRole = val
    },
    SET_BMTREEBRANCHID: (state, bmTreeBranchId) => {
      state.bmTreeBranchId = bmTreeBranchId
    },
    SET_BMTREEBRANCHNAME: (state, bmTreeBranchName) => {
      state.bmTreeBranchName = bmTreeBranchName
    }
  },

  actions: {
    
  }
}

export default bmTree
