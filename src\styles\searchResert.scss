// 搜索框 样式重置
.el-input__icon {
  height: auto;

  &:hover {
    color: #000;
    cursor: pointer;
  }
}

// 筛选框
.el-input__prefix,
.el-input__suffix {
  height: auto;
}

.filter-container {
  width: 100%;
  min-height: 50px;
  overflow: hidden;

  .fr > .el-input,
  .fr > .el-select {
    width: 200px;
    margin-left: 10px;
  }

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }

  .el-date-editor {
    width: 240px;
  }

  .m-shaixuan-bar {
    position: relative;

    .m-shaixuan-input {
      width: 400px;
    }

    .m-shaixuan-ab {
      position: absolute;
      top: 34px;
      left: 0px;
      z-index: 99;
      box-shadow: 0 2px 6px 0 rgba(114, 124, 245, 0.5);

      padding-right: 40px;
      padding-top: 10px;

      border: 1px solid #dcdfe6;
      background: #fff;
      border-radius: 4px;

      input[type="number"] {
        padding-right: 0px;
      }

      .m-shaixuan-title {
        color: #19aa8d;
        margin: 0;
        padding: 0px 0px 4px 6px;
        margin-bottom: 10px;
        margin-left: 20px;
        border-bottom: 1px solid #19aa8d;
      }

      // label 加粗
      .el-form-item__label {
        font-weight: normal;
      }

      .el-form-item {
        flex: 1;
      }

      .m-shaixuan-btngroup {
        float: right;
        margin-bottom: 20px;
        margin-left: 20px;
      }

      // 关闭图标
      .m-shaixuan-close {
        cursor: pointer;
        position: absolute;
        right: 10px;
        top: 5px;
        color: #dcdfe6;

        &:hover {
          color: red;
        }
      }
    }
  }
}

.filter-container button.el-button--mini {
  height: 28px;
}

.operate-container {
  height: 50px;
}

.form-container {
  height: calc(100% - 50px);
  overflow-y: auto;
}

.table-container {
  height: calc(100% - 100px);
  overflow-y: auto;
  overflow-x: hidden;
  .el-table {
    height: 100%;
  }
}

.page-container {
  height: 50px;
  padding: 8px 0;
}

.search-right-btn {
  vertical-align: top;
  margin-left: 10px;
  height: 28px;
}

.search-batch-bar {
  vertical-align: top;
  margin-left: 10px;
  height: 28px;
}

// 选择日期范围 样式
.el-range-editor--mini .el-range-separator {
  width: 40px;
}

.el-upload-list__item {
  background: #fdf6e3;
}

@media screen and (max-width: 1366px) {
  .table-container {
    height: calc(100% - 125px);
  }
}

@media screen and (max-width: 1280px) {
  .table-container {
    height: calc(100% - 150px);
  }
}

