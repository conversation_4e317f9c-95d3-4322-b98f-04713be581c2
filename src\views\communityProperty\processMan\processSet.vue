<template>
  <!-- 画流程图页面 -->
  <div class="app-container" style="padding: 0">
    <!-- <div style="padding-left: 20px; padding-top: 10px; font-size: 20px">爱丽丝的房间阿里山附件</div> -->
    <!-- <el-button @click="saveFunc">保存111</el-button> -->
    <div class="page-title">{{ processData.name }}</div>
    <div style="height: calc(100% - 27px)">
      <div class="content with-diagram" id="js-drop-zone">
        <div class="message error">
          <div class="note">
            <p>无法显示bpms2.0</p>
            <div class="details">
              <span>错误详细信息</span>
              <pre></pre>
            </div>
          </div>
        </div>
        <div class="canvas" id="js-canvas"></div>
        <div class="properties-panel-parent" id="js-properties-panel"></div>
      </div>
    </div>

    <ul class="buttons">
      <li class="item download">
        <a class="btn" @click.stop="saveFunc">部署</a>
      </li>

      <!-- <li class="item download">
        <a class="btn" @click.stop="bsFunc">部署</a>
      </li> -->

      <!-- <li class="item upload">
        <form id="form1" name="myForm" onsubmit="return false" method="post" enctype="multipart/form-data" title="上传文件">
          <input type="file" name="uploadFile" id="uploadFile" accept=".bpmn" style="display: none" />
          <label class="label" for="uploadFile">导入</label>
        </form>
      </li>
      <li class="item download">
        <a class="btn" href id="downloadBpmn">导出</a>
      </li>
      <li class="item submit">
        <a class="btn" id="js-download-diagram"> 部署 </a>
      </li> -->
    </ul>
    <div class="sy-alert sy-alert-model animated" sy-enter="zoomIn" sy-leave="zoomOut" sy-type="confirm" sy-mask="true" id="alert">
      <div class="sy-title">部署流程</div>
      <div class="sy-content">
        确认是否部署该流程
        <!--    <div class="form">-->
        <!--      <p class="input-item"><input id="deploymentName" type="text" placeholder="请输入流程名称"></p>-->
        <!--    </div>-->
      </div>
      <div class="sy-btn">
        <button id="sure">确定</button>
        <button class="cancel">取消</button>
      </div>
    </div>
    <div class="sy-mask cancel"></div>

    <!-- 组件 -->
    <selectUserDlg
      :dlgState0="dlgUserState"
      :dlgData0="dlgUserData"
      :dlgType="dlgUserType"
      :dlgQuery="dlgUserQuery"
      @closeDlg="closeUserDlg"
      @dlgUserSubFunc="dlgUserSubFunc"
    />
    <!-- <addDlg :dlgState0="dlgState" :dlgData0="dlgData" :dlgType="dlgType" :dlgQuery="dlgQuery" @closeDlg="closeDlg" /> -->
  </div>
</template>

<script>
import * as utils from '@/utils'
import { postAction, getAction, formAction } from '@/api'

// bpmn
import $ from 'jquery'
import BpmnModeler from 'bpmn-js/lib/Modeler'
//import propertiesPanelModule from 'static/bpmnjs/resources/properties-panel';
import propertiesPanelModule from 'bpmn-js-properties-panel'
import propertiesProviderModule from '/static/bpmnjs/resources/properties-panel/provider/activiti'
import activitiModdleDescriptor from '/static/bpmnjs/resources/activiti.json'
import customTranslate from '/static/bpmnjs/resources/customTranslate/customTranslate'
import customControlsModule from '/static/bpmnjs/resources/customControls'
import tools from '/static/bpmnjs/resources/tools'

// import diagramXML from '/static/bpmnjs/resources/newDiagram.bpmn'

import '/static/bpmnjs/css/diagram-js.css'
import '/static/bpmnjs/vendor/bpmn-font/css/bpmn-embedded.css'
import '/static/bpmnjs/css/app.css'

// 组件
import selectUserDlg from '@/components/Dialog2/selectUserDlg'
// import addDlg from './processList/addDlg'

export default {
  components: {
    selectUserDlg,
    // addDlg,
  },
  data() {
    return {
      processData: '',

      bpmnModeler: '',
      diagramXML0: '', // 原始数据
      diagramXMLEmpty:
        '<?xml version="1.0" encoding="UTF-8"?>' +
        '<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd" id="sample-diagram" targetNamespace="http://activiti.org/bpmn">' +
        '<bpmn2:process id="Process_1" isExecutable="true">' +
        '<bpmn2:extensionElements>' +
        '<activiti:executionListener class="com.wlines.simpleaccess.component.listener.CustomProcessListener" event="start" />' +
        '<activiti:executionListener class="com.wlines.simpleaccess.component.listener.CustomProcessListener" event="end" />' +
        '</bpmn2:extensionElements>' +
        '<bpmn2:startEvent id="StartEvent_1"/>' +
        '</bpmn2:process>' +
        '<bpmndi:BPMNDiagram id="BPMNDiagram_1">' +
        '<bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">' +
        '<bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">' +
        '<dc:Bounds height="36.0" width="36.0" x="412.0" y="240.0"/>' +
        '</bpmndi:BPMNShape>' +
        '</bpmndi:BPMNPlane>' +
        '</bpmndi:BPMNDiagram>' +
        '</bpmn2:definitions>',

      // -- 选择员工弹窗
      dlgUserQuery: {},
      dlgUserState: false,
      dlgUserType: '', // 弹框状态add, edit
      dlgUserData: {},

      // 弹窗数据
      dlgQuery: {},
      dlgState: false,
      dlgType: '', // 弹框状态add, edit
      dlgData: {},
    }
  },

  created() {
    // this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    // this.searchFunc()
  },
  mounted() {
    this.processData = JSON.parse(window.sessionStorage.storageProcessData)
    if (!utils.isNull(this.processData.deploymentId)) {
      setTimeout(() => {
        this.getBpmnXml()
      }, 500)
    } else {
      setTimeout(() => {
        console.log('111this.diagramXMLEmpty', this.diagramXMLEmpty)
        let dateObj = new Date()
        let timeStr = dateObj.getTime()
        this.diagramXMLEmpty = this.diagramXMLEmpty.replace(/=\"Process_1\"/g, `="P${timeStr}"`)
        this.bpmnInitFunc(this.diagramXMLEmpty)
      }, 500)
    }
    // 自定义点击事件
    $(document).on('click', '.bn-selectUser', () => {
      this.showUserDlg()
    })
  },

  methods: {
    getBpmnXml() {
      let sendObj = {
        deploymentId: this.processData.deploymentId,
        resourceName: this.processData.sourceName,
      }
      let sendObjStr = utils.objToParam(sendObj)
      getAction(`/act/process/getXml` + sendObjStr).then((res0) => {
        this.diagramXML0 = res0.data
        let bpmnXML = res0.data
        if (bpmnXML.indexOf('event="start"') < 0) {
          let ppStr = bpmnXML.match(/\<bpmn2:process(.*)+\>/)
          console.log('ppStr[0]', ppStr[0])
          bpmnXML = bpmnXML.replace(
            ppStr[0],
            ppStr[0] +
              '<bpmn2:extensionElements>' +
              '<activiti:executionListener class="com.wlines.simpleaccess.component.listener.CustomProcessListener" event="start" />' +
              '<activiti:executionListener class="com.wlines.simpleaccess.component.listener.CustomProcessListener" event="end" />' +
              '</bpmn2:extensionElements>'
          )
        }
        this.bpmnInitFunc(bpmnXML)
      })
    },
    // -- 编辑弹窗
    showDlg(bpmnXML) {
      this.dlgQuery = { ...this.processData, bpmnXML }
      this.dlgType = 'edit'
      this.dlgState = true
    },
    // 关闭弹窗
    closeDlg() {
      this.dlgState = false
    },
    // -- 选择员工弹窗
    showUserDlg() {
      this.dlgUserQuery = ''
      this.dlgUserState = true
    },
    // 关闭弹窗
    closeUserDlg() {
      this.dlgUserState = false
    },
    // 选择员工返回
    dlgUserSubFunc(selectRow) {
      console.log('车辆返回', selectRow)
      if (utils.isNull(selectRow)) return false
      let labelStr = `${selectRow.label}*u*${selectRow.id}`
      let bnMap = {
        name: selectRow.label,
        assignee: selectRow.id,
      }
      tools.updateBnName(this.bpmnModeler, bnMap)
    },

    // 获取详情

    // 新增
    saveFunc() {
      tools.getBpmnXml(this.bpmnModeler).then((xml) => {
        let bpmnXML = xml
        this.$confirm('确认部署?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          let sendObj = {
            bpmn: bpmnXML,
            sourceName: this.processData.name,
            tableId: this.processData.id,
          }
          console.log('sendObj', sendObj)
          // return false
          formAction('/act/process/add', sendObj).then((res0) => {
            let res = res0.data
            this.dlgSubLoading = false
            if (res.code == 200) {
              this.$message.success('操作成功')
              this.dlgState = false
              this.$emit('getList')
              this.$emit('closeDlg')
              setTimeout(() => {
                this.$router.go(-1)
              }, 200)
            } else {
              this.$message({
                type: 'warning',
                message: res.msg,
              })
            }
          })
        })
      })
    },

    bpmnInitFunc(diagramXML) {
      this.diagramXML0 = diagramXML

      const proHost = window.location.protocol + '//' + window.location.host
      const href = window.location.href.split('bpmnjs')[0]
      const key = href.split(window.location.host)[1]
      const publicurl = proHost + key

      var customTranslateModule = {
        translate: ['value', customTranslate],
      }
      var container = $('#js-drop-zone')
      var canvas = $('#js-canvas')
      this.bpmnModeler = new BpmnModeler({
        container: canvas,
        propertiesPanel: {
          parent: '#js-properties-panel',
        },
        additionalModules: [
          propertiesPanelModule, // 左边工具栏及节点
          propertiesProviderModule, // 自定义属性模型
          customControlsModule,
          customTranslateModule,
        ],
        // additionalModules: [propertiesPanelModule, customControlsModule, customTranslateModule],
        moddleExtensions: {
          activiti: activitiModdleDescriptor,
        },
      })
      container.removeClass('with-diagram')
      if (!window.FileList || !window.FileReader) {
        window.alert('请使用谷歌、火狐、IE10+浏览器')
      } else {
        tools.registerFileDrop(container, tools.createDiagram(diagramXML, this.bpmnModeler, container))
      }
      var param = tools.getUrlParam(window.location.href)
      $('.item').show()
      // if (param.type === 'addBpmn') {
      tools.createDiagram(diagramXML, this.bpmnModeler, container)

      // 点击新增
      $('#js-download-diagram').on('click', function () {
        tools.syopen('alert')
      })

      // 点击取消
      $('.cancel').on('click', function () {
        tools.syhide('alert')
      })
      // 点击确定
      $('#sure').on('click', function () {
        // const text=$("#deploymentName").val()
        tools.saveBpmn(this.bpmnModeler)
      })

      // 点击下载
      $('#downloadBpmn').on('click', function () {
        tools.downLoad(this.bpmnModeler)
      })
      // 点击上传
      $('#uploadFile').on('change', function () {
        tools.upload(this.bpmnModeler, container)
      })
    },
  },
  beforeDestroy() {
    this.bpmnModeler.destroy()
  },
}
</script>
<!-- scoped -->
<style rel="stylesheet/scss" lang="scss" scoped>
.item {
  display: none;
  cursor: pointer;
}
.bjs-powered-by {
  display: none;
}
.buttons > li {
  /* display: inline-block; */
  margin-right: 10px;
  height: 26px;
  line-height: 26px;
  float: left;
}
.buttons > li > a.btn {
  background: #00bcd4;
  border: none;
  outline: none;
  padding: 0px 20px;
  color: #fff;
  display: inline-block;
  opacity: 1;
  height: 34px;
  font-size: 14px;
  line-height: 34px;
}
.label {
  background: #00bcd4;
  border: none;
  outline: none;
  padding: 0px 10px;
  color: #fff;
  display: inline-block;
  cursor: pointer;
  opacity: 1;
  height: 26px;
  font-size: 14px;
  line-height: 26px;
}
.sy-mask {
  width: 100%;
  height: 100%;
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  left: 0;
  top: 0;
  z-index: 1000;
  display: none;
}
.sy-alert {
  position: fixed;
  display: none;
  background: #fff;
  border-radius: 5px;
  overflow: hidden;
  width: 300px;
  max-width: 90%;
  max-height: 80%;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 9999;
}
.sy-alert.animated {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
}
.sy-alert .sy-title {
  height: 45px;
  color: #333;
  line-height: 45px;
  font-size: 15px;
  border-bottom: 1px solid #eee;
  padding: 0 12px;
}
.sy-alert .sy-content {
  padding: 20px;
  text-align: center;
  font-size: 14px;
  line-height: 24px;
  color: #666;
  overflow-y: auto;
}
.sy-alert .sy-btn {
  height: 50%;
  border-top: 1px solid #eee;
  overflow: hidden;
}
.sy-alert .sy-btn button {
  float: left;
  border: 0;
  color: #333;
  cursor: pointer;
  background: #fff;
  width: 50%;
  line-height: 45px;
  font-size: 15px;
  text-align: center;
}
.sy-alert .sy-btn button:nth-child(1) {
  color: #888;
  border-right: 1px solid #eee;
}
.sy-alert.sy-alert-alert .sy-btn button {
  float: none;
  width: 100%;
}
.sy-alert.sy-alert-tips {
  text-align: center;
  width: 150px;
  background: rgba(0, 0, 0, 0.7);
}
.sy-alert.sy-alert-tips .sy-content {
  padding: 8px;
  color: #fff;
  font-size: 14px;
}
.sy-alert.sy-alert-model .sy-content {
  text-align: left;
}
.sy-alert.sy-alert-model .sy-content .form .input-item {
  margin-bottom: 12px;
  position: relative;
}
.sy-alert.sy-alert-model .sy-content .form .input-item input {
  display: block;
  position: relative;
  width: 100%;
  border: 1px solid #eee;
  padding: 10px;
}
.sy-alert.sy-alert-model .sy-content .form .input-item .getcode {
  border: 0;
  top: 0;
  right: 0;
  position: absolute;
  background: 0;
  line-height: 37px;
  color: #f60;
  width: 100px;
  text-align: center;
}

/////
.page-title {
  padding-left: 20px;
  padding-top: 14px;
  margin-bottom: -8px;
  font-size: 18px;
  font-weight: bold;
}
</style>


