<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区">
          <el-select
            v-model="listQuery.communityId"
            filterable
            clearable
            placeholder="请选择小区"
            @change="communityChange"
          >
            <el-option
              v-for="item in communityList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input
            @keyup.enter.native="getList"
            placeholder="请输入车位名称"
            v-model="listQuery.label"
          >
            <i
              slot="suffix"
              @click="resetSearchItem(['label'])"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="getList"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="addItem"
          >新增车位</el-button
        >
        <el-button
          icon="el-icon-download"
          size="mini"
          type="primary"
          @click="exportExcel"
        >
          Excel导出
        </el-button>
        <el-upload class="upload-wrap" action="" :before-upload="uploadItem">
          <el-button icon="el-icon-upload" size="mini" type="primary">
            Excel导入
          </el-button>
        </el-upload>
        <el-button
          icon="el-icon-download"
          type="primary"
          size="mini"
          @click="downloadItem()"
        >
          模板下载
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车位编号">
          <template slot-scope="scope">
            <span>{{ scope.row.numStr }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车位类型">
          <template slot-scope="scope">
            <span>{{ scope.row.typeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="区域">
          <template slot-scope="scope">
            <span>{{ scope.row.area }}</span>
          </template>
        </el-table-column>
        <el-table-column label="房产类型">
          <template slot-scope="scope">
            <span>{{ scope.row.roomTypeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="建筑面积">
          <template slot-scope="scope">
            <span>{{ scope.row.measureArea }}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column label="算费系数">
          <template slot-scope="scope">
            <span>{{ scope.row.parkingCoefficient }}</span>
          </template>
        </el-table-column> -->

        <el-table-column label="使用人">
          <template slot-scope="scope">
            <span>{{
              scope.row.parkingUsage ? scope.row.parkingUsage.usages : ""
            }}</span>
          </template>
        </el-table-column>

        <el-table-column label="使用人电话">
          <template slot-scope="scope">
            <span>{{
              scope.row.parkingUsage ? scope.row.parkingUsage.usagePhone : ""
            }}</span>
          </template>
        </el-table-column>

        <el-table-column label="到期时间">
          <template slot-scope="scope">
            <span>{{
              scope.row.parkingUsage ? scope.row.parkingUsage.endTime : ""
            }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车位状态" align="center">
          <template slot-scope="scope">
            <span class="fsuccess" v-if="scope.row.state == 1">已售</span>
            <span class="fwarning" v-else-if="scope.row.state == 2">出租</span>
            <span class="fblur" v-else-if="scope.row.state == 3">自用</span>
            <span class="finfo" v-else>未售</span>
          </template>
        </el-table-column>

        <el-table-column label="业主">
          <template slot-scope="scope">
            <span>{{ scope.row.ownerName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="电话">
          <template slot-scope="scope">
            <span>{{ scope.row.ownerPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="320"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.state == 1"
              type="warning"
              size="mini"
              icon="el-icon-close"
              plain
              @click="unbindItem(scope.row)"
              >解绑</el-button
            >
            <el-button
              v-else
              type="success"
              size="mini"
              icon="el-icon-check"
              plain
              @click="bindItem(scope.row)"
              >绑定</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              plain
              @click="editItem(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-check"
              plain
              @click="rentItem(scope.row)"
              >出租</el-button
            >
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              plain
              @click="delItem(scope.row, 1)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>

    <el-dialog
      :close-on-click-modal="false"
      :title="(dlgType === 'RENT' ? '出租' : '新增/编辑') + '车位信息'"
      :visible.sync="dlgShow"
      width="600px"
      top="30px"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :rules="rules"
        :model="dlgData"
        label-position="right"
        label-width="100px"
      >
        <template v-if="dlgType !== 'RENT'">
          <el-form-item label="所在小区" prop="communityId">
            <el-select
              v-model="dlgData.communityId"
              filterable
              clearable
              placeholder="请选择小区"
              @change="communityChange"
            >
              <el-option
                v-for="item in communityList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="类型" prop="type">
            <el-select
              v-model="dlgData.type"
              filterable
              clearable
              placeholder="请选择类型"
            >
              <el-option
                v-for="item in typeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="区域" prop="area">
            <el-input v-model="dlgData.area" placeholder="请输入区域" />
          </el-form-item>
          <el-form-item label="房产类型" prop="roomTypeId">
            <el-select
              @change="roomTypeChange"
              v-model="dlgData.roomTypeId"
              filterable
              clearable
              placeholder="请选择类别"
            >
              <el-option
                v-for="item in houseTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="车位编号" prop="numStr">
            <el-input v-model="dlgData.numStr" placeholder="请输入编号" />
          </el-form-item>

          <el-form-item label="建筑面积">
            <el-input-number
              v-model="dlgData.measureArea"
              :controls="false"
              :min="0"
              :precision="2"
              :step="1"
            ></el-input-number>
          </el-form-item>
          <!-- 
        <el-form-item label="算费系数">
          <el-input-number v-model="dlgData.parkingCoefficient" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
        </el-form-item> -->

          <el-form-item label="备注">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 6 }"
              v-model="dlgData.remark"
              placeholder="请输入备注"
            />
          </el-form-item>

          <el-form-item label="车位状态" prop="state">
            <el-radio-group v-model="dlgData.state">
              <el-radio :disabled="dlgType === 'EDIT'" label="2">出租</el-radio>
              <el-radio label="3">自用</el-radio>
              <el-radio label="0">未售</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
        <template v-if="dlgData.state == 2 || dlgData.state == 3">
          <template v-if="dlgData.state == 2">
            <el-form-item label="租用日期" prop="rangeDate">
              <el-date-picker
                :disabled="dlgType === 'EDIT' && dlgData.state == 2"
                v-model="dlgData.rangeDate"
                type="daterange"
                range-separator="~"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </template>
          <el-form-item label="使用人" prop="usages">
            <el-input
              :disabled="dlgType === 'EDIT' && dlgData.state == 2"
              v-model="dlgData.usages"
              placeholder="请输入使用人"
            />
          </el-form-item>

          <el-form-item label="使用人电话" prop="usagePhone">
            <el-input
              :disabled="dlgType === 'EDIT' && dlgData.state == 2"
              v-model="dlgData.usagePhone"
              placeholder="请输入使用人电话"
            />
          </el-form-item>

          <el-divider content-position="left">车辆信息</el-divider>
          <div
            class="clearfix"
            v-if="!(dlgType === 'EDIT' && dlgData.state == 2)"
          >
            <el-button
              type="primary"
              size="mini"
              class="fr"
              icon="el-icon-plus"
              plain
              @click="addCar()"
              >添加车辆</el-button
            >
          </div>
          <el-table
            class="m-small-table mt10"
            :data="dlgData.memberCars"
            border
            fit
            highlight-current-row
          >
            <el-table-column
              label="序号"
              type="index"
              align="center"
              width="60"
            >
            </el-table-column>

            <el-table-column label="车辆型号">
              <template slot-scope="scope">
                <el-form-item
                  label-width="0"
                  :prop="'memberCars.' + scope.$index + '.carModel'"
                  :rules="[
                    {
                      required: true,
                      message: '车辆型号必填',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    :disabled="dlgType === 'EDIT' && dlgData.state == 2"
                    v-model="scope.row.carModel"
                    placeholder="请填写车辆型号"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column label="车牌号">
              <template slot-scope="scope">
                <el-form-item
                  label-width="0"
                  :prop="'memberCars.' + scope.$index + '.carNum'"
                  :rules="[
                    { required: true, message: '车牌号必填', trigger: 'blur' },
                  ]"
                >
                  <el-input
                    :disabled="dlgType === 'EDIT' && dlgData.state == 2"
                    v-model="scope.row.carNum"
                    placeholder="请填写车牌号"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              align="center"
              width="80"
              v-if="!(dlgType === 'EDIT' && dlgData.state == 2)"
            >
              <template slot-scope="scope">
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-close"
                  plain
                  @click="delCar(scope.$index)"
                  >解绑</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button
          type="success"
          :loading="dlgLoading"
          @click="subDlg"
          icon="el-icon-check"
        >
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <memberDlg />
  </div>
</template>

<script>
import Cookie from "js-cookie";
import { mapGetters } from "vuex";
import {
  communityPage,
  coparkingPage,
  coparkingLease,
  coparkingAddOrUpdate,
  coparkingDisable,
  coparkingBinding,
  coparkingUnbinding,
  importCoParkin,
  roomTypePage,
} from "@/api/communityMan";
import * as utils from "@/utils";
import * as constConfig from "@/configs/const.js";
import Pagination from "@/components/Pagination";
import { uploadImg } from "@/utils/uploadImg";
import memberDlg from "@/components/Dialog/communityMan/memberDlg";
import WorkSpaceBase from "@/components/WorkSpace/WorkSpaceBase";

let dlgDataEmpty = {
  id: "",
  communityId: "",
  communityName: "",
  type: "",
  typeName: "",
  area: "",
  parkingCoefficient: "",
  measureArea: "",
  numStr: "",
  remark: "",
  state: "0",
  rangeDate: "",
  usages: "",
  usagePhone: "",
  memberCars: [],
  roomTypeId: "",
};

export default {
  name: "parkingInfo",
  extends: WorkSpaceBase,
  components: {
    Pagination,
    memberDlg,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: "", // ADD\EDIT
      dlgTitle: "", // 标题

      rules: {
        numStr: [{ required: true, message: "必填字段", trigger: "blur" }],
        communityId: [
          { required: true, message: "必填字段", trigger: "change" },
        ],
        roomTypeId: [
          { required: true, message: "请选择房产类型", trigger: "change" },
        ],
        rangeDate: [{ required: true, message: "必填字段", trigger: "change" }],
        usages: [{ required: true, message: "必填字段", trigger: "blur" }],
        usagePhone: [
          { required: true, message: "必填字段", trigger: "blur" },
          {
            pattern: constConfig.PHONE_REG,
            message: "电话格式有误！",
            trigger: "blur",
          },
        ],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      houseTypeList:[],//房屋类型
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
        communityId: "",
      },
      communityList: [],
      typeList: [],
      userInfo: {},
      selectRow: {},
    };
  },
  computed: {
    ...mapGetters("communityMan/memberDlg", {
      memberId: "memberId",
    }),
  },
  watch: {
    memberId(val) {
      if (val && val != this.selectRow.ownerId) {
        this.bindMember(val);
      }
    },
  },

  created() {
    this.getCommunityList();
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    utils.getDataDict(this, "parkingType", "typeList");
  },

  methods: {
    
    roomTypeChange(id) {
      this.dlgData.roomTypeName = this.houseTypeList.find(
        (item) => item.id == id
      ).name;
    },
    // 导出
    exportExcel() {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery));
      exportParam.userId = this.userInfo.id;
      exportParam.projectId = this.userInfo.projectId;
      let param = Object.keys(exportParam)
        .map(function (key) {
          return (
            encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key])
          );
        })
        .join("&");

      let sendUrl =
        location.protocol +
        "//" +
        location.host +
        `/saapi/unity/report/exportCoParkin?` +
        param;
      window.open(sendUrl);
    },

    // 下载
    downloadItem() {
      let url =
        "https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E5%AF%BC%E5%85%A5%E8%BD%A6%E4%BD%8D%E6%A8%A1%E6%9D%BF.xlsx";
      window.open(url);
    },

    // 上传
    uploadItem(file) {
      let name = file.name.split(".");
      let suffix = name[name.length - 1];

      if (suffix !== "xls" && suffix !== "xlsx") {
        this.$message({
          type: "warning",
          message: "只能上传xls/xlsx文件",
        });
        return false;
      }

      let loading = this.$loading({
        lock: true,
        text: "导入中",
        background: "rgba(0, 0, 0, 0.7)",
      });

      let postParam = {
        file,
      };

      importCoParkin(postParam).then((res) => {
        loading.close();
        if (res.data.code == 200) {
          this.$message.success("导入成功");
          this.getList();
        } else {
          this.$message({
            type: "warning",
            message: res.data.msg,
          });
        }
      });

      return false;
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
    },
    communityChange() {
      let communityId;
      if (this.dlgShow) {
        communityId = this.dlgData.communityId;
        this.dlgData.floorId = "";
        this.dlgData.unitId = "";
        this.dlgData.roomTypeId = "";
      } else {
        communityId = this.listQuery.communityId;
        this.listQuery.floorId = "";
        this.listQuery.unitId = "";
        this.dlgData.roomTypeId = "";
      }
      this.getRoomType(communityId);
    },
    // 获取房屋类型列表
    getRoomType(communityId) {
      if(communityId){
        let postParam = {
          page: 1,
          limit: 200,
          communityId:communityId
        };
        roomTypePage(postParam).then((res) => {
          if (res.data.code == 200) {
            if (this.dlgShow==true) {
              this.houseTypeList = res.data.data;
            } else {
              this.houseTypeList = res.data.data;
            }
          }
        });
        }
    },

    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
      };
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data;
        }
      });
    },

    // 获取数据
    getList() {
      this.count++;
      this.listLoading = true;
      coparkingPage(this.listQuery).then((res) => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data));
          this.total = res.data.page ? res.data.page.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 添加车辆
    addCar() {
      this.dlgData.memberCars.push({
        carModel: "",
        carNum: "",
      });
    },

    // 删除车辆
    delCar(idx) {
      this.dlgData.memberCars.splice(idx, 1);
    },

    // 绑定成员
    bindMember(id) {
      if (utils.isNull(id)) {
        return;
      }
      coparkingBinding(this.selectRow.id, id).then((res) => {
        if (res.data.code == 200) {
          this.$message.success(res.data.msg);
          this.selectRow = {};
          this.getList();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 绑定
    bindItem(data) {
      this.selectRow = JSON.parse(JSON.stringify(data));
      let memberId = this.selectRow.ownerId;
      this.$store.commit("communityMan/memberDlg/SET_MEMBERID", memberId);
      this.$store.commit("communityMan/memberDlg/SET_DLGTYPE", "VIEW");
      this.$store.commit("communityMan/memberDlg/SET_DLGSHOW", true);
    },

    // 解绑
    unbindItem(data) {
      this.$confirm("确定解除绑定", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        coparkingUnbinding(data.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.getList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },

    // 显示弹窗
    addItem() {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgData.communityId = this.listQuery.communityId;
      this.dlgType = "ADD";
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },

    // 弹窗提交
    subDlg() {
      this.$refs["dlgForm"].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData));
          postParam.projectId = this.userInfo.projectId;
          postParam.communityName = utils.getNameById(
            postParam.communityId,
            this.communityList
          );
          postParam.typeName = utils.getNameById(postParam.type, this.typeList);
          if (postParam.state == 2 || postParam.state == 3) {
            postParam.parkingUsage = {
              usages: postParam.usages,
              usagePhone: postParam.usagePhone,
            };
            if (postParam.state == 2) {
              postParam.parkingUsage.startTime = postParam.rangeDate[0];
              postParam.parkingUsage.endTime = postParam.rangeDate[1];
            }
            delete postParam.rangeDate;
            delete postParam.usages;
            delete postParam.usagePhone;
          } else {
            delete postParam.memberCars;
          }
          let methods = {
            RENT: coparkingLease,
            ADD: coparkingAddOrUpdate,
            EDIT: coparkingAddOrUpdate,
          };
          this.dlgLoading = true;
          methods[this.dlgType](postParam).then((res) => {
            this.dlgLoading = false;
            if (res.data.code == 200) {
              this.getList();
              this.dlgShow = false;
              this.$message.success(res.data.msg);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },

    // 编辑
    editItem(data) {
      if(data.roomTypeId&&data.roomTypeName){
        data.roomTypeId=Number(data.roomTypeId)
      }else{
        data.roomTypeId=""
        data.roomTypeName=''
      }
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgData = Object.assign(
        this.dlgData,
        JSON.parse(JSON.stringify(data))
      );
      if (this.dlgData.parkingUsage) {
        this.dlgData.usages = this.dlgData.parkingUsage.usages;
        this.dlgData.usagePhone = this.dlgData.parkingUsage.usagePhone;
        if (
          !utils.isNull(this.dlgData.parkingUsage.startTime) &&
          !utils.isNull(this.dlgData.parkingUsage.endTime)
        ) {
          this.dlgData.rangeDate = [
            this.dlgData.parkingUsage.startTime,
            this.dlgData.parkingUsage.endTime,
          ];
        }
      }
      this.dlgData.memberCars = this.dlgData.memberCars
        ? this.dlgData.memberCars
        : [];
      this.dlgType = "EDIT";
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
      this.getRoomType(this.dlgData.communityId);
    },

    // 出租
    rentItem(data) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgData = Object.assign(
        this.dlgData,
        JSON.parse(JSON.stringify(data))
      );
      this.dlgData.memberCars = [];
      this.dlgData.state = "2";
      this.dlgType = "RENT";
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },

    // 启用停用
    delItem(data, flag) {
      let title = "确认删除?";
      if (flag == 0) {
        title = "确认启用?";
      } else if (flag == 2) {
        title = "确认停用?";
      }
      this.$confirm(title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        coparkingDisable(data.id, flag).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.getList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


