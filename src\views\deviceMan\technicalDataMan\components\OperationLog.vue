<template>
  <div>
    <!-- 操作日志对话框 -->
    <el-dialog
      title="操作日志"
      :visible.sync="dialogVisible"
      width="1000px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-table
        :data="logData"
        height="calc(100vh - 400px)"
        ref="tableBar"
        class="m-small-table"
        v-loading="loading"
        :key="tableKey"
        border
        fit
        highlight-current-row
        style="width: 100%; height: auto"
      >
        <el-table-column label="#" align="center" width="50" fixed="left">
          <template slot-scope="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column
          label="操作人"
          prop="updaterName"
          width="auto"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.updaterName || "-" }}
          </template>
        </el-table-column>

        <el-table-column
          label="操作时间"
          prop="updateTime"
          width="150"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.updateTime || "-" }}
          </template>
        </el-table-column>

        <el-table-column
          label="名称"
          prop="name"
          width="auto"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.name || "-" }}
          </template>
        </el-table-column>

        <el-table-column
          label="图纸类型"
          prop="typeStr"
          width="auto"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.typeStr || "-" }}
          </template>
        </el-table-column>

        <el-table-column label="共享图片" width="150" align="center">
          <template slot-scope="scope">
            <div v-if="getImageList(scope.row.imgUrl).length > 0" class="image-actions">
              <el-button
                type="text"
                size="mini"
                @click="previewImages(getImageList(scope.row.imgUrl))"
              >
                查看({{ getImageList(scope.row.imgUrl).length }})
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-dropdown @command="downloadImg" trigger="click">
                <el-button type="text" size="mini">
                  下载<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="image in getImageList(scope.row.imgUrl)"
                    :key="image.url"
                    :command="image"
                  >
                    {{ image.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="原始文件" width="auto" align="center">
          <template slot-scope="scope">
            <div v-if="getFileList(scope.row.fileUrl).length > 0">
              <el-dropdown @command="downloadFile">
                <el-button type="text" size="mini">
                  下载({{ getFileList(scope.row.fileUrl).length }})<i
                    class="el-icon-arrow-down el-icon--right"
                  ></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="file in getFileList(scope.row.fileUrl)"
                    :key="file.url"
                    :command="file"
                  >
                    {{ file.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="附件" width="auto" align="center">
          <template slot-scope="scope">
            <div v-if="getFileList(scope.row.annexUrl).length > 0">
              <el-dropdown @command="downloadFile">
                <el-button type="text" size="mini">
                  下载({{ getFileList(scope.row.annexUrl).length }})<i
                    class="el-icon-arrow-down el-icon--right"
                  ></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="file in getFileList(scope.row.annexUrl)"
                    :key="file.url"
                    :command="file"
                  >
                    {{ file.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          label="备注"
          prop="remark"
          width="auto"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.remark || "-" }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        class="mt10"
        :total="total"
        :page.sync="currentPage"
        :limit.sync="pageSize"
        @pagination="loadLogData"
      />
      <div class="clear"></div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="imagePreviewVisible"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="image-preview-container">
        <el-carousel
          v-if="previewImageList.length > 1"
          :autoplay="false"
          indicator-position="outside"
          height="500px"
        >
          <el-carousel-item v-for="(image, index) in previewImageList" :key="index">
            <div class="carousel-image-item">
              <div class="image-name">{{ image.name }}</div>
              <div class="image-wrapper">
                <el-image
                  :src="image.url"
                  fit="contain"
                  style="width: 100%; height: 450px;"
                  :preview-src-list="[image.url]"
                />
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>

        <!-- 单张图片时直接显示 -->
        <div v-else-if="previewImageList.length === 1" class="single-image-container">
          <div class="image-name">{{ previewImageList[0].name }}</div>
          <div class="image-wrapper">
            <el-image
              :src="previewImageList[0].url"
              fit="contain"
              style="width: 100%; max-height: 500px;"
              :preview-src-list="[previewImageList[0].url]"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOldList } from "@/api/technicalData";

export default {
  name: "OperationLog",

  data() {
    return {
      dialogVisible: false,
      imagePreviewVisible: false,
      loading: false,
      logData: [],
      previewImageList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      currentRecordId: null,
      tableKey: 0,
    };
  },

  methods: {
    /**
     * 显示操作日志对话框
     */
    async showDialog(recordId) {
      this.currentRecordId = recordId;
      this.dialogVisible = true;
      this.currentPage = 1;
      await this.loadLogData();
    },

    /**
     * 加载日志数据
     */
    async loadLogData() {
      if (!this.currentRecordId) return;

      this.loading = true;
      try {
        const params = {
          id: this.currentRecordId,
          page: this.currentPage,
          size: this.pageSize,
        };

        const { data } = await getOldList(params);
        console.log(data, "操作日志数据");

        if (data.code === "200") {
          this.logData = data.data.list || [];
          this.total = data.data.total || 0;
        } else {
          this.$message.error(data.msg || "获取日志失败");
          this.logData = [];
          this.total = 0;
        }
      } catch (error) {
        console.error("获取日志失败:", error);
        this.$message.error("获取日志失败");
        this.logData = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 解析文件列表
     */
    getFileList(fileUrlStr) {
      if (!fileUrlStr) return [];
      try {
        const fileList = JSON.parse(fileUrlStr);
        return Array.isArray(fileList) ? fileList : [];
      } catch (error) {
        console.error("解析文件列表失败:", error);
        return [];
      }
    },

    /**
     * 解析图片列表
     */
    getImageList(imgUrlStr) {
      console.log("原始图片数据:", imgUrlStr);
      const result = this.getFileList(imgUrlStr);
      console.log("解析后的图片列表:", result);
      return result;
    },

    /**
     * 预览图片
     */
    previewImages(images) {
      console.log("预览图片数据:", images);
      if (!images || images.length === 0) {
        this.$message.warning("没有可预览的图片");
        return;
      }
      this.previewImageList = images;
      this.imagePreviewVisible = true;
    },

    /**
     * 关闭对话框
     */
    handleClose() {
      this.dialogVisible = false;
      this.imagePreviewVisible = false;
      this.logData = [];
      this.previewImageList = [];
      this.currentRecordId = null;
      this.currentPage = 1;
      this.total = 0;
    },

    /**
     * 下载文件
     */
    downloadFile(file) {
      if (!file || !file.url) return;

      const link = document.createElement("a");
      link.href = file.url;
      link.download = file.name || "";
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    /**
     * 下载文件
     */
    downloadImg(file) {
      console.log("下载文件:", file);

      if (!file || !file.url) return;
      // 使用 window.open 实现文件下载
      // window.open(file.url, "_blank");
      this.downloadByBlob(file.url, file.name);
    },

    // 下载图片
    downloadByBlob(url, name) {
      let image = new Image();
      image.setAttribute("crossOrigin", "anonymous");
      image.src = url;
      image.onload = () => {
        let canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        let ctx = canvas.getContext("2d");
        ctx.drawImage(image, 0, 0, image.width, image.height);
        canvas.toBlob((blob) => {
          let url = URL.createObjectURL(blob);
          this.download(url, name);
          // 用完释放URL对象
          URL.revokeObjectURL(url);
        });
      };
    },

    download(href, name) {
      let eleLink = document.createElement("a");
      eleLink.download = name;
      eleLink.href = href;
      eleLink.click();
      eleLink.remove();
    },
  },
};
</script>

<style lang="scss" scoped>
.image-preview-container {
  .carousel-image-item {
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;

    .image-name {
      font-weight: bold;
      margin-bottom: 10px;
      color: #333;
      font-size: 14px;
      padding: 0 20px;
    }

    .image-wrapper {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 20px;
    }
  }

  .single-image-container {
    text-align: center;

    .image-name {
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }

    .image-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }
  }
}

// 轮播图样式调整
::v-deep .el-carousel {
  .el-carousel__container {
    height: 500px;
  }

  .el-carousel__item {
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .el-carousel__indicators {
    .el-carousel__indicator {
      .el-carousel__button {
        background-color: #ddd;

        &.is-active {
          background-color: #409eff;
        }
      }
    }
  }
}

::v-deep .el-dropdown-menu__item {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 表格样式与主页面保持一致
::v-deep .m-small-table {
  .el-table__header-wrapper {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 500;
    }
  }
}

.mt10 {
  margin-top: 10px;
}

.clear {
  clear: both;
}

// 图片操作按钮样式
.image-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;

  .el-button {
    margin: 0;
    padding: 0 4px;
  }

  .el-divider--vertical {
    margin: 0 4px;
    height: 12px;
  }
}
</style>
