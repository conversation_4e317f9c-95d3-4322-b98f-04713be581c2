<template>
  <!-- 考勤异常单 选人 -->
  <el-dialog
    :close-on-click-modal="false"
    :title="'选择员工'"
    :visible.sync="dlgShow"
    @close="closeDlg"
    top="30px"
    append-to-body
    width="1100px"
  >
    <div class="filter-container">
      <el-form inline>
        <el-form-item>
          <el-input @keyup.enter.native="getList" v-model="listQuery.labelNum" placeholder="员工姓名\岗位名称">
            <i slot="suffix" @click="resetSearchItem(['labelNum'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.branchName" @focus="showBranchDlg" placeholder="选择部门" readonly>
            <i
              @click="resetSearchItem(['branchId', 'branchName'])"
              slot="suffix"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList"> 搜索 </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class="m-small-table" height="100%" :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectUserId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>

        <el-table-column label="员工姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属部门">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="工号">
          <template slot-scope="scope">
            <span>{{ scope.row.userCode }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所在岗位">
          <template slot-scope="scope">
            <span>{{ scope.row.postName }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back"> 取 消 </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check"> 确 定 </el-button>
    </div>
    <branchDlg />
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import branchDlg from '@/components/Dialog/platformMan/branchDlg'
import {
  findUserByLabelAndNum,
  findUserByLabelAndNumAndFrom, // 不按权限
  userPage,
} from '@/api/staffMan'

export default {
  props: {
    isRole: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    Pagination,
    branchDlg,
  },
  data() {
    return {
      list: [],

      listQuery: {
        page: 1,
        size: 10,
        labelNum: '',
        branchId: '',
        branchName: '',
      },

      total: 0,

      selectUserId: '',

      selectUserName: '',

      selectUserInfo: {},
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.financialSystem.userDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('financialSystem/userDlg/SET_DLGSHOW', val)
      },
    },

    dlgType: {
      get: function () {
        return this.$store.state.financialSystem.userDlg.dlgType
      },
      set: function (val) {
        this.$store.commit('financialSystem/userDlg/SET_DLGTYPE', val)
      },
    },

    userId: {
      get: function () {
        return this.$store.state.financialSystem.userDlg.userId
      },
      set: function (val) {
        this.$store.commit('financialSystem/userDlg/SET_USERID', val)
      },
    },

    userName: {
      get: function () {
        return this.$store.state.financialSystem.userDlg.userName
      },
      set: function (val) {
        this.$store.commit('financialSystem/userDlg/SET_USERNAME', val)
      },
    },

    userInfo: {
      get: function () {
        return this.$store.state.financialSystem.userDlg.userInfo
      },
      set: function (val) {
        this.$store.commit('financialSystem/userDlg/SET_USERINFO', val)
      },
    },

    dlgQuery: {
      get: function () {
        return this.$store.state.financialSystem.userDlg.dlgQuery
      },
      set: function (val) {
        this.$store.commit('financialSystem/userDlg/SET_DLGQUERY', val)
      },
    },

    ...mapGetters('platformMan/branchDlg', {
      branchId: 'branchId',
      branchName: 'branchName',
    }),
  },

  watch: {
    dlgShow(val) {
      if (val) {
        this.selectUserId = utils.isNull(this.userId) ? '' : this.userId
        this.selectUserName = utils.isNull(this.userName) ? '' : this.userName
        this.selectUserInfo = utils.isEmptyObject(this.userInfo) ? {} : JSON.parse(JSON.stringify(this.userInfo))
        this.listQuery.branchId = ''
        this.listQuery.branchName = ''
        this.getList()
      }
    },

    branchId(val) {
      if (this.dlgShow) {
        this.listQuery.branchId = val
      }
    },

    branchName(val) {
      if (this.dlgShow) {
        this.listQuery.branchName = val
      }
    },

    userId(val) {
      this.selectUserId = val
    },

    userName(val) {
      this.selectUserName = val
    },

    userInfo(val) {
      this.selectUserInfo = JSON.parse(JSON.stringify(val))
    },
  },

  methods: {
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },

    // 显示部门对话框
    showBranchDlg() {
      let branchId = this.listQuery.branchId
      let branchName = this.listQuery.branchName
      this.$store.commit('platformMan/branchDlg/SET_DLGTYPE', 'PERMISSION')
      this.$store.commit('platformMan/branchDlg/SET_BRANCHID', branchId)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', branchName)
      this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', true)
    },

    rowClick(row, column, event) {
      this.selectUserId = row['id']
      this.selectUserName = row['label']
      this.selectUserInfo = JSON.parse(JSON.stringify(row))
    },

    getList() {
      this.list = []
      this.listQuery = Object.assign(this.listQuery, this.dlgQuery)
      let method = this.dlgType === 'PERMISSION' ? userPage : findUserByLabelAndNum
      method(this.listQuery).then((res) => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let data = res.data.data
          if (utils.isNull(data)) {
            return
          }
          this.list = res.data.list
          this.total = res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      this.userId = this.selectUserId
      this.userName = this.selectUserName
      this.userInfo = this.selectUserInfo
      this.$store.commit('financialSystem/userDlg/SET_USERINFO', this.userInfo)
      this.$store.commit('financialSystem/userDlg/SET_USERNAME', this.userName)
      this.$store.commit('financialSystem/userDlg/SET_USERID', this.userId)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('financialSystem/userDlg/SET_DLGTYPE', '')
      this.$store.commit('financialSystem/userDlg/SET_DLGQUERY', {})
      this.$store.commit('financialSystem/userDlg/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px !important;
  .el-dialog__body {
    height: calc(100% - 110px);
  }
}
</style>