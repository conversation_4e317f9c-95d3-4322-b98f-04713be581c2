import request from '@/utils/request'

// 部门树维护
export function findBranchTreeByProject (id) {
  return request({
    url: `/sys/department/findBranchTreeByProject?projectId=${id}`,
    method: 'get'
  })
}

// 添加部门信息
export function saveBranchInfo (data) {
  return request({
    url: '/sys/department/saveBranchInfo',
    method: 'post',
    data
  })
}

// 删除部门
export function removeBranchInfo (id) {
  return request({
    url: '/sys/department/removeBranchInfo?id=' + id,
    method: 'get'
  })
}


// 部门上移下移
export function upDown (up, down) {
  return request({
    url: `/sys/department/upDown/${up}/${down}`,
    method: 'get'
  })
}
