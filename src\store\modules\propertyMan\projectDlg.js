// 项目dlg组件

const projectDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    projectId: '',

    projectName: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    projectId: state => state.projectId,

    projectName: state => state.projectName
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_PROJECTID: (state, val) => {
      state.projectId = val
    },

    SET_PROJECTNAME: (state, val) => {
      state.projectName = val
    }
  },

  actions: {

  }
}

export default projectDlg
