/** 绿化分支 **/
import Layout from "@/views/layout/Layout";
// component: () => import("@/views/heatMan/dataCollect/index"),
const examRouter = {
  path: "/deviceStatisticalAnalysis",
  component: Layout,
  redirect: "/examMan/course/list",
  name: "deviceStatisticalAnalysis",
  meta: {
    title: "设备统计分析",
    icon: "deviceMan",
    roles: ["equ_shebeiguanli"],
  },

  children: [
    {
      path: "deviceFileMan",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "deviceFileMan",
      meta: {
        title: "设备档案管理",
        roles: ["equ_shebeidanganguanli"],
      },
      children: [
        {
          path: "basicledgerMain",
          component: () =>
            import("@/views/deviceStatisticalAnalysis/deviceFileMan/basicledgerMain/index"),
          name: "basicledgerMain",
          meta: {
            title: "设备信息维护",
            roles: ["equ_jichutaizhangweihu"],
          },
          children: [],
        },
        {
          path: "equManualMan",
          component: () =>
            import("@/views/deviceStatisticalAnalysis/deviceFileMan/equManualMan/index"),
          name: "equManualMan",
          meta: {
            title: "操作手册管理",
            roles: ["equ_caozuoshouceguanli"],
          },
          children: [],
        },
        {
          path: "deviceOperRecords",
          component: () =>
            import("@/views/deviceStatisticalAnalysis/deviceFileMan/deviceOperRecords/index"),
          name: "deviceOperRecords",
          meta: {
            title: "设备运行记录",
            roles: ["equ_shebeiyunxingjilu"],
          },
          children: [],
        },

        // {
        //   path: "runningRecordTempMan",
        //   component: () =>
        //     import("@/views/deviceStatisticalAnalysis/deviceFileMan/runningRecordTempMan/index"),
        //   name: "runningRecordTempMan",
        //   meta: {
        //     title: "运行模板管理",
        //     roles: ["equ_yunxingjilumobanguanli"]
        //   },
        //   children: []
        // },
        // {
        //   path: "MainItemsList",
        //   component: () =>
        //     import("@/views/deviceStatisticalAnalysis/deviceFileMan/MainItemsList/index"),
        //   name: "MainItemsList",
        //   meta: {
        //     title: "检修事项维护",
        //     roles: ["equ_jianxiushixiangweihu"]
        //   },
        //   children: []
        // },
        // {
        //   path: "mainSubjectCategory",
        //   component: () =>
        //     import("@/views/deviceStatisticalAnalysis/deviceFileMan/mainSubjectCategory/index"),
        //   name: "mainSubjectCategory",
        //   meta: {
        //     title: "维修科目维护",
        //     roles: ["equ_weixiukemuleibieweihu"]
        //   },
        //   children: []
        // },

        // {
        //   path: "formProcessDesign",
        //   component: () =>
        //     import("@/views/deviceStatisticalAnalysis/deviceFileMan/runningRecordTempMan/dyFormDesign/index"),
        //   name: "formProcessDesign",
        //   meta: {
        //     title: "运行记录模板设置",
        //     roles: ["equ_yunxingjilumobanguanli"]
        //   },
        //   hidden: true,
        //   children: []
        // }
      ],
    },

    {
      path: "deviceMainRepair",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "deviceMainRepair",
      meta: {
        title: "设备保养检修",
        roles: ["equ_shebeibaoyangjianxiu"],
      },
      children: [
        {
          path: "deviceMain",
          component: () => import("@/views/heatMan/dataCollect/index"),
          name: "deviceMain",
          meta: {
            title: "设备保养",
            roles: ["equ_shebeibaoyang"],
          },
          children: [
            {
              path: "mainPlan",
              component: () =>
                import(
                  "@/views/deviceStatisticalAnalysis/deviceMainRepair/deviceMain/mainPlan/index"
                ),
              name: "mainPlan",
              meta: {
                title: "保养计划",
                roles: ["equ_baoyangjihua"],
              },
              children: [],
            },
            {
              path: "mainPlanExe",
              component: () =>
                import(
                  "@/views/deviceStatisticalAnalysis/deviceMainRepair/deviceMain/mainPlanExe/index"
                ),
              name: "mainPlanExe",
              meta: {
                title: "保养计划执行",
                roles: ["equ_baoyangjihuazhixing"],
              },
              children: [],
            },
            {
              path: "mainPlanExeMan",
              component: () =>
                import(
                  "@/views/deviceStatisticalAnalysis/deviceMainRepair/deviceMain/mainPlanExeMan/index"
                ),
              name: "mainPlanExeMan",
              meta: {
                title: "保养执行记录",
                roles: ["equ_baoyangzhixingjilu"],
              },
              children: [],
            },
          ],
        },
        {
          path: "deviceRepair",
          component: () => import("@/views/heatMan/dataCollect/index"),
          name: "deviceRepair",
          meta: {
            title: "设备检修",
            roles: ["equ_shebeijianxiu"],
          },
          children: [
            {
              path: "mainPlan",
              component: () =>
                import(
                  "@/views/deviceStatisticalAnalysis/deviceMainRepair/deviceRepair/mainPlan/index"
                ),
              name: "mainPlan",
              meta: {
                title: "检修计划",
                roles: ["equ_jianxiujihua"],
              },
              children: [],
            },
            {
              path: "mainPlanExe",
              component: () =>
                import(
                  "@/views/deviceStatisticalAnalysis/deviceMainRepair/deviceRepair/mainPlanExe/index"
                ),
              name: "mainPlanExe",
              meta: {
                title: "检修计划执行",
                roles: ["equ_jianxiujihuazhixing"],
              },
              children: [],
            },
            {
              path: "mainPlanExeMan",
              component: () =>
                import(
                  "@/views/deviceStatisticalAnalysis/deviceMainRepair/deviceRepair/mainPlanExeMan/index"
                ),
              name: "mainPlanExeMan",
              meta: {
                title: "检修执行记录",
                roles: ["equ_jianxiuzhixingjilu"],
              },
              children: [],
            },
          ],
        },
        {
          path: "deviceMaintainableMan",
          component: () => import("@/views/heatMan/dataCollect/index"),
          name: "deviceMaintainableMan",
          meta: {
            title: "设备维修",
            roles: ["equ_shebeiweixiuguanli"],
          },
          children: [
            {
              path: "equPlan",
              component: () =>
                import(
                  "@/views/deviceStatisticalAnalysis/deviceMainRepair/equMain/equPlan/index"
                ),
              name: "equPlan",
              meta: {
                title: "设备维修",
                roles: ["equ_shebeiweixiu"],
              },
              children: [],
            },
            {
              path: "equMan",
              component: () =>
                import(
                  "@/views/deviceStatisticalAnalysis/deviceMainRepair/equMain/equMan/index"
                ),
              name: "equMan",
              meta: {
                title: "设备维修记录",
                roles: ["equ_shebeiweixiujilu"],
              },
              children: [],
            },
          ],
        },
      ],
    },

    {
      path: "reportMan",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "reportMan",
      meta: {
        title: "报表管理",
        roles: ["equ_baobiaoguanli"],
      },
      children: [
        {
          path: "deviceMainRepair",
          component: () =>
            import("@/views/deviceStatisticalAnalysis/reportMan/deviceMainRepair/index"),
          name: "reportManDeviceMainRepair",
          meta: {
            title: "设备保养、检修信息",
            roles: ["equ_shebeibaoyangjianxiuxinxi"],
          },
          children: [],
        },

        {
          path: "equOperRecords",
          component: () => import("@/views/heatMan/dataCollect/index"),
          name: "equOperRecords",
          meta: {
            title: "设备运行记录统计",
            roles: ["equ_shebeiyunxingjilutongji"],
          },
          children: [
            {
              path: "deviceOperRecords",
              component: () =>
                import("@/views/deviceStatisticalAnalysis/reportMan/deviceOperRecords/index"),
              name: "equOperRecordsDeviceOperRecords",
              meta: {
                title: "设备运行信息",
                roles: ["equ_shebeiyunxingxinxitongji"],
              },
              children: [],
            },
            {
              path: "month",
              component: () =>
                import("@/views/deviceStatisticalAnalysis/reportMan/equOperRecords/month.vue"),
              name: "equOperRecordsMonth",
              meta: {
                title: "月度统计",
                roles: ["equ_shebeiyuedutongji"],
              },
              children: [],
            },
          ],
        },
      ],
    },

    // {
    //   path: "mbInitiateProcess",
    //   component: () =>
    //     import("@/views/deviceMan/dynamicForm/MbInitiateProcess"),
    //   name: "创建记录",
    //   meta: {
    //     title: "创建记录"
    //   },
    //   hidden: true,
    //   children: []
    // }

    //
  ],
};

export default examRouter;
