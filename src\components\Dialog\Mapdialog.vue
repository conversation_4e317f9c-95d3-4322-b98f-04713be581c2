<template>
  <div class="" id='map-dialog'>
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' title="设置考勤范围" :visible.sync="mapDialogState" width='1200px' top='40px' append-to-body>

      <div class="mapDialog-bar">
        <div class="mapDialog-search">
          <el-autocomplete class="inline-input" v-model="mapStr" :fetch-suggestions="querySearch" @select="mapSelectFunc" placeholder="请输入地址进行搜索"></el-autocomplete>
        </div>
        <div id="container1"></div>
        <div id="panel"></div>

        <!-- 多边形 -->
        <div class='info'>
          <span class='close-desc'><i class='el-icon-warning fwarning'></i> 操作说明：点击鼠标右键完成绘制</span>
          <el-button v-show='!hzState' type='primary' @click='drawPolygon' size='mini' plain>绘制多边形</el-button>
          <el-button v-show='hzState' id="close" type='danger' size='mini' plain>清除</el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <!-- <span class='dialog-footer-span' v-show='mJWD'>当前考勤经纬度：{{ mJWD }}</span> -->
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="primary" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findAdeHolidayAll } from '@/api/attendanceGroupMan'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination
import * as utils from '@/utils'
import { setTimeout } from 'timers'

export default {
  components: {
    Pagination
  },
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      mMap: '',

      loadingState: true,
      mapStr: '',
      placeSearch: '',

      poiboxIndex: '',  // 点击的第几个
      poiboxArr: [],  //详情数据列表

      mapObj: '',
      mJWD: '',

      mouseTool: '',
      hzState: true
    }
  },
  computed: {
    ...mapGetters([
      'role'
    ]),
    mapDialogIsFirst: {
      get: function () {
        return this.$store.getters.mapDialogIsFirst
      },
      set: function (newVal) {
        this.$store.commit('SET_MAPDIALOGISFIRST', newVal)
      }
    },
    mapDialogState: {
      get: function () {
        if (this.mapDialogIsFirst) {
          setTimeout(() => {
            this.setMap()
          }, 50)
        }
        return this.$store.getters.mapDialogState
      },
      set: function (newVal) {
        this.$store.commit('SET_MAPDIALOGSTATE', newVal)
      }
    },

    mapDialogStr: {
      get: function () {
        return this.$store.getters.mapDialogStr
      },
      set: function (newVal) {
        this.$store.commit('SET_MAPDIALOGSTR', newVal)
      }
    },
    mapDialogJWD: {
      get: function () {
        this.mJWD = this.$store.getters.mapDialogJWD
        return this.$store.getters.mapDialogJWD
      },
      set: function (newVal) {
        this.mapDialogIsFirst = false
        // this.$store.commit('SET_MAPDIALOGJWD', newVal)
      }
    },
  },
  watch: {
  },
  created() {
    let that = this
    $(document).on('click', '.poibox', function () {
      let mIndex = $(this).index()
      let obj = that.poiboxArr[mIndex]

      // that.mapDialogLng = obj.location.lng
      // that.mapDialogLat = obj.location.lat
      that.mapDialogStr = obj.name
    })
  },
  mounted() {
    this.$nextTick(() => {
      $('.map-selected').click()
    })
  },
  methods: {
    drawPolygon() {
      this.hzState = true
      // this.mouseTool.polygon({
      //   strokeColor: "#FF33FF", 
      //   // strokeOpacity: 1,
      //   strokeWeight: 6,
      //   strokeOpacity: 0.2,
      //   fillColor: '#1791fc',
      //   fillOpacity: 0.4,
      //   // 线样式还支持 'dashed'
      //   strokeStyle: "solid",
      //   // strokeStyle是dashed时有效
      //   // strokeDasharray: [30,10],
      // })


      this.mouseTool.polygon({
        strokeColor: "#FF33FF",
        strokeWeight: 6,
        strokeOpacity: 0.2,
        fillColor: '#1791fc',
        fillOpacity: 0.4,
        // 线样式还支持 'dashed'
        strokeStyle: "solid",
        // strokeStyle是dashed时有效
        // strokeDasharray: [30,10],
      })

    },
    // 【【 高德地图
    setMap() {
      let map = ''
      let polyline = ''

      if (this.mapDialogJWD) {
        let mJWD = this.mapDialogJWD
        let mPoint = mJWD.split(';')
        let nJwd = []
        let cenPointX = 0
        let cenPointY = 0
        for (let item of mPoint) {
          let jd = parseFloat(item.split(',')[0])
          let wd = parseFloat(item.split(',')[1])

          let pointArr = []
          pointArr.push(jd)
          pointArr.push(wd)
          nJwd.push(JSON.parse(JSON.stringify(pointArr)))

          // 计算中心点
          cenPointX += jd
          cenPointY += wd
        }

        // 中心点
        cenPointX = cenPointX / mPoint.length
        cenPointY = cenPointY / mPoint.length

        // 修改经纬度
        map = new AMap.Map('container1', {
          resizeEnable: true, //是否监控地图容器尺寸变化
          zoom: 15, //初始化地图层级
          center: [cenPointX, cenPointY], //初始化地图中心点
          // center: [nJwd[0][0] + 0.002, nJwd[0][1] - 0.0005] //初始化地图中心点
        })
        polyline = new AMap.Polygon({
          path: nJwd,          //设置线覆盖物路径
          fillColor: '#a0cff6',   // 填充颜色
          strokeColor: "#b3aff8", //线颜色
          strokeWeight: 4,        //线宽
          strokeStyle: "solid",   //线样式
        });
        map.add(polyline);
      } else {
        // 第一次设置考勤定位
        map = new AMap.Map('container1', {
          resizeEnable: true, //是否监控地图容器尺寸变化
          zoom: 11, //初始化地图层级
          // center: [this.mapDialogLng, this.mapDialogLat] //初始化地图中心点
        })
      }

      this.mapObj = map
      // 绘制多边形
      let _this = this
      AMap.plugin('AMap.MouseTool', () => {
        _this.mouseTool = new AMap.MouseTool(map);
        var overlays = [];

        _this.drawPolygon()

        _this.mouseTool.on('draw', e => {
          console.log(e)
          // event.obj 为绘制出来的覆盖物对象
          // log.info('覆盖物对象绘制完成')
          let mStr = ''
          for (let item of e.obj.w.path) {
            let lng = item.lng;
            let lat = item.lat
            let str = lng + ',' + lat + ';'
            mStr += str
          }
          mStr = mStr.substr(0, mStr.length - 1)

          _this.mJWD = mStr
        })
      })

      var that = this
      $(document).on('click', '#close', () => {
        this.hzState = false
        map.remove(polyline)
        that.mJWD = ''
        this.mouseTool.close(true)//关闭，并清除覆盖物
        // for(var i=0;i<radios.length;i+=1){
        //     radios[i].checked = false;
        // }
        // setTimeout(() => {
        //   draw('polygon')
        // }, 50)

      })

      // 根据关键字搜索
      AMap.service(["AMap.PlaceSearch"], () => {
        //构造地点查询类
        this.placeSearch = new AMap.PlaceSearch({
          pageSize: 5, // 单页显示结果条数
          pageIndex: 1, // 页码
          city: "010", // 兴趣点城市
          citylimit: false,  //是否强制限制在设置的城市内搜索
          map: map, // 展现结果的地图实例
          panel: "panel", // 结果列表将在此容器中进行展示。
          autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
          extensions: 'base' //返回基本地址信息
        });
        //关键字查询
        // placeSearch.search('北京大学');
      });


    },
    // 自动输入
    autoInput(cb) {
      var keywords = this.mapStr
      AMap.plugin('AMap.Autocomplete', () => {
        // 实例化Autocomplete
        var autoOptions = {
          city: '全国'
        }
        var autoComplete = new AMap.Autocomplete(autoOptions);
        autoComplete.search(keywords, (status, result) => {
          console.log(result.tips)
          if (!result.tips) {
            return false
          }
          let arr = result.tips
          for (let item of arr) {
            item.value = item.name
          }
          cb(arr)
          // 搜索成功时，result即是对应的匹配数据
          // var node = new PrettyJSON.view.Node({
          //     el: document.querySelector("#input-info"),
          //     data: result
          // });
        })
      })
    },

    // 控件值改变时触发
    querySearch(queryString, cb) {
      if (queryString === '') {
        cb([])
      } else {
        this.autoInput(cb)
      }
    },

    // 输入框值变化方法
    mapSelectFunc(obj) {
      console.log(obj)
      this.placeSearch.search(obj.name, (status, result) => {
        if (!utils.isEmptyObject(result)) {
          this.poiboxArr = result.poiList.pois
        }
      })
    },
    // 选点
    showInfoClick(e) {
      // Lng 经度， lat 维度
      var text = e.lnglat.getLng() + ',' + e.lnglat.getLat()
    },
    // 】】 高德地图

    // 选择部门提交
    bumenOkFunc() {
      this.$store.commit('SET_MAPDIALOGJWD', this.mJWD)
      this.closeDialog()
      // alert(this.mJWD)
      // this.$store.commit('SET_MAPDIALOGJWD', '')
      // setTimeout(() => {
      //   this.$store.commit('SET_MAPDIALOGJWD', this.mJWD)
      //   this.closeDialog()
      // }, 50)
    },
    // 关闭弹窗 
    closeDialog() {
      this.mapObj.destroy()
      this.mapDialogState = false
      this.$store.commit('SET_MAPDIALOGSTATE', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-dialog__body {
  padding: 10px 20px !important;
}
.mapDialog-search {
  margin-bottom: 10px;
}
#container1 {
  width: 1160px;
  height: 600px;
}
#panel {
  position: absolute;
  background-color: white;
  max-height: 90%;
  overflow-y: auto;
  top: 120px;
  right: 26px;
  width: 280px;
}
.dialog-footer-span {
  color: #666;
  font-size: 14px;
  margin-right: 20px;
}

// 矩形选框样式
.info {
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
  position: absolute;
  top: 643px;
  background-color: white;
  width: auto;
  min-width: 22rem;
  border-width: 0;
  left: 30px;
  right: auto;
  box-shadow: 0 2px 6px 0 rgba(114, 124, 245, 0.5);
  z-index: 9999;
}
.input-item input[type='button'] {
  padding: 0 10px;
  margin-right: 10px;
}

// 高德地图
.input-card {
  position: absolute;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border-radius: 0.25rem;
  width: 300px !important;
  border-width: 0;
  border-radius: 0.4rem;
  box-shadow: 0 2px 6px 0 rgba(114, 124, 245, 0.5);
  bottom: 156px;
  right: auto;
  left: 30px;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 0.75rem 1.25rem;
}
.close-desc {
  display: inline-block;
  line-height: 28px;
}
#close {
  // float: right;
  // display: inline-block;
  // font-weight: 400;
  // text-align: center;
  // white-space: nowrap;
  // vertical-align: middle;
  // -webkit-user-select: none;
  // -moz-user-select: none;
  // -ms-user-select: none;
  // user-select: none;
  // border: 1px solid transparent;
  // transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  // background-color: transparent;
  // background-image: none;
  // color: #25A5F7;
  // border-color: #25A5F7;
  // padding: .25rem .5rem;
  // line-height: 1.5;
  // border-radius: 1rem;
  // -webkit-appearance: button;
  // cursor: pointer;
}
.input-text {
  line-height: 1;
  margin-right: 16px;
  color: #999;
}
.input-item {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 40px;
}
</style>