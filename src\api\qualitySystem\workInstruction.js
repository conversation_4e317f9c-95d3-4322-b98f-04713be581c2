import request from '@/utils/request'
import {
  requestExcel
} from '@/utils'

/*
 *工作项配置
 */

// 加载工作项树 
export function loadWorkItemsTree(data) {
  return request({
    url: `/report/work/loadWorkItemsTree`,
    method: 'post',
    data
  })
}

// 新增修改工作项
export function addOrUpdateInfo(data) {
  return request({
    url: `/report/work/addOrUpdateInfo`,
    method: 'post',
    data
  })
}

// 删除工作项
export function workItemRemove(id) {
  return request({
    url: `/report/work/remove/${id}`,
    method: 'get'
  })
}


// 按工作项查询作业规程
export function getByWorkId(id) {
  return request({
    url: `/report/workRules/getByWorkId/${id}`,
    method: 'get'
  })
}

// 按工作向批量查询 作业规程
export function getByWorkIds(data) {
  return request({
    url: `/report/workRules/getByWorkIds`,
    method: 'post',
    data
  })
}


// 按工作项新增作业规程
export function insertWorkRules(data) {
  return request({
    url: `/report/workRules/insertWorkRules`,
    method: 'post',
    data
  })
}


// 分页查询作业流程模板
export function wpmPage(data) {
  return request({
    url: `/report/wpm/page`,
    method: 'post',
    data
  })
}

// 新增修改作业流程模板
export function addOrUpdate(data) {
  return request({
    url: `/report/wpm/addOrUpdate`,
    method: 'post',
    data
  })
}

// 根据id获取流程模板信息
export function getInfoById(id) {
  return request({
    url: `/report/wpm/getInfoById/${id}`,
    method: 'get'
  })
}

// 删除作业流程模板
export function wpmRemove(id) {
  return request({
    url: `/report/wpm/remove/${id}`,
    method: 'get'
  })
}

// 导入
export function importRules(data) {
  return requestExcel('/report/workRules/importRules', data)
}
