/**
 * Created by mzg on 19/06/01
 * Desc 日历 返回的当月的天数集合
 */

export let return2Num = num => {
  if (num < 10) {
    num = '0' + num
  }

  return num
}

// 返回日历 天数集合（包含上月及下月）
export let returnCalendarByMonth = (month, tMonthData) => {
  // 获取单月 的天数集合
  let tyear = parseInt(month.split('-')[0])
  let tmonth = parseInt(month.split('-')[1])


  // 下一月
  let nyear = ''
  let nmonth = ''

  // 上一月
  let pyear = ''
  let pmonth = ''

  // 判断当前月
  if (tmonth == '12') {
    nyear = tyear + 1
    nmonth = 1

    pyear = tyear
    pmonth = tmonth - 1
  } else if(tmonth == '1')  {
    pyear = tyear - 1
    pmonth = 12

    nyear = tyear
    nmonth = tmonth + 1
  } else {
    pyear = tyear
    pmonth = tmonth - 1

    nyear = tyear
    nmonth = tmonth + 1
  }
 
  // 小于 10 的 前边加 '0'
  tmonth = return2Num(tmonth)
  nmonth = return2Num(nmonth)
  pmonth = return2Num(pmonth)

  let startDay = new Date(`${month}-01`)
  let endDay = new Date(`${nyear}-${nmonth}-01`)

  let daysNum = (endDay - startDay) / 1000 / 60 / 60 / 24;  // 当月天数

  // 获取上月的最后一天
  let pDate = new Date(startDay.setDate(0))
  let plastDay = parseInt(pDate.getDate())
  let plastWeek = parseInt(pDate.getDay())

  // 拼接 月份天数
  // 部分上月
  let pre = []
  if (plastWeek != 0) {
    for (let i = 0;i<plastWeek;i++) {
      let item = {

        date: `${pyear}-${pmonth}-${plastDay-i}`,
        workType: '',
        state: 0,
        label: plastDay-i,
        type: 'pre',
        bgClass: 'white'
      }
      pre.push(item)
    }
  }

  pre = pre.reverse()

  // 拼接本月
  let tday = new Date()
  tday = month + '-' + return2Num(tday.getDate())
  console.log(tday)
  let mData = []

  if (tMonthData != null)
  {
    for (let item of tMonthData) {
      let bgClass = ''
  
      if (tday == item.date) {
        bgClass = 'green'
      } else {
        bgClass = 'white'
      }
  
      let tobj = {
        date: item.date,
        state: item.state,
        workType: item.workType,
  
        label: item.date.substr(8, 2),
        type: 'on',
        bgClass
      }
      mData.push(tobj)
    }
  }
  console.log(mData)
  // let jintianDate = new Date()
  // let jintian = jintianDate.getFullYear() + '-' + return2Num(jintianDate.getMonth() + 1) + "-" +return2Num(jintianDate.getDate())
  // console.log(jintian)
  // for (let i = 0;i<daysNum;i++) {
  //   let itemDay = return2Num(i+1)
  //   let itemDate = new Date(`${month}-${itemDay}`)

  //   let item = {
  //     date: `${month}-${itemDay}`,
  //     label: i+1,
  //     week: itemDate.getDay(),
  //     type: 'on'
  //   }

    
  //   if (jintian == `${month}-${itemDay}`) {
  //     item.type = 'on thisDay'
  //   }
    
  //   mData.push(item)
  // }

  // 部分下月
  let lastWeek = parseInt(endDay.getDay())
  let nData = []
  if (lastWeek != 1) {
    let len = 7-lastWeek
    if (len == 7) {
      len = 0
    }
    for (let i = 0;i<=len;i++) {
      let item = {
        date: `${nyear}-${nmonth}-${return2Num(i + 1)}`,
        workType: '',
        state: 0,
        label: i + 1,
        type: 'next',
        bgClass: 'white'
      }
      nData.push(item)
    }
  }

  return [...pre, ...mData, ...nData]


  // console.log('上月',pre.reverse())
  // console.log('本月', mData)
  // console.log('次月', nData)
}

// 返回 周 大写
export let returnWeekUpCase = week => {
  let weelUpCase = ''
  switch(week) {
    case 1:
      weelUpCase = "一"
      break;
    case 2:
      weelUpCase = "二"
      break
    case 3:
      weelUpCase = "三"
      break
    case 4:
      weelUpCase = "四"
      break
    case 5:
      weelUpCase = "五"
      break
    case 6:
      weelUpCase = "六"
      break
    case 0:
      weelUpCase = "日"
      break
  }
  return weelUpCase
}