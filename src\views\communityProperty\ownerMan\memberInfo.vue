<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList">查询</el-button>
        <el-button icon="el-icon-check" type="primary" size="mini" @click="showRoomDlg">选择房屋</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="showMiDlg('add', {})">新增成员</el-button>
      </el-form>
    </div>
    <el-form>
      <el-divider content-position="left">房屋信息</el-divider>
      <el-row>
        <el-col :span="8">
          <el-form-item label="小区名称：">
            {{ roomInfo.communityName }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="楼栋：">
            {{ roomInfo.floorName }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单元：">
            {{ roomInfo.unitName }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="房屋名称：">
            {{ roomInfo.roomFullName }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业主姓名：">
            {{ roomInfo.memberName }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系电话：">
            {{ roomInfo.memberPhone }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="性别">
          <template slot-scope="scope">
            <span>{{ scope.row.sex }}</span>
          </template>
        </el-table-column>

        <el-table-column label="年龄">
          <template slot-scope="scope">
            <span>{{ scope.row.age }}</span>
          </template>
        </el-table-column>

        <el-table-column label="与业主关系">
          <template slot-scope="scope">
            <span>{{ scope.row.relationshipName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="身份证">
          <template slot-scope="scope">
            <span>{{ scope.row.idCard }}</span>
          </template>
        </el-table-column>

        <el-table-column label="联系方式">
          <template slot-scope="scope">
            <span>{{ scope.row.phone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="微信">
          <template slot-scope="scope">
            <span>{{ scope.row.wchat }}</span>
          </template>
        </el-table-column>

        <el-table-column label="QQ">
          <template slot-scope="scope">
            <span>{{ scope.row.qq }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="showMiDlg('edit', scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" :disabled="scope.row.type == 1" plain @click="delItem(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <memberInfoDlg
      :dlgState0="dlgMiState"
      :dlgData0="dlgMiData"
      :dlgType="dlgMiType"
      :dlgQuery="dlgMiQuery"
      @closeDlg="closeMiDlg"
      @getList="getList"
      :roomInfo="roomInfo"
    />

    <roomDlg />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { buildingmemberRoom, buildingmemberAddOrUpdate, buildingmemberMember, buildingmemberPhone } from '@/api/ownerMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import roomDlg from '@/components/Dialog/communityMan/roomDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

import memberInfoDlg from './memberInfoDlg'

let dlgDataEmpty = {
  id: '',
  age: '',
  name: '',
  phone: '',
  photo: '',
  remark: '',
  idCard: '',
  sex: '男',
  wchat: '',
  qq: '',
  roomId: '',
  relationship: '',
  relationshipName: '',
}

export default {
  name: 'memberInfo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    roomDlg,
    memberInfoDlg,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        roomId: [{ required: true, message: '必填字段', trigger: 'change' }],
        sex: [{ required: true, message: '必填字段', trigger: 'blur' }],
        name: [{ required: true, message: '必填字段', trigger: 'change' }],
        phone: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: '手机号码格式有误！',
            trigger: 'blur',
          },
        ],
        idCard: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern:
              /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
            message: '证件号码格式有误！',
            trigger: 'blur',
          },
        ],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
      },
      userInfo: {},

      relationshipList: [],

      // 会员信息弹窗
      dlgMiQuery: {},
      dlgMiState: false,
      dlgMiType: '', // 弹框状态add, edit
      dlgMiData: {},
    }
  },

  computed: {
    ...mapGetters('communityMan/roomDlg', {
      roomId: 'roomId',
      roomName: 'roomName',
      roomInfo: 'roomInfo',
    }),
  },

  watch: {
    roomId(val) {
      if (val) {
        this.getList()
      }
    },

    roomName(val) {},

    roomInfo(val) {},
  },

  created() {
    this.$store.commit('communityMan/roomDlg/SET_ROOMID', '')
    this.$store.commit('communityMan/roomDlg/SET_ROOMNAME', '')
    this.$store.commit('communityMan/roomDlg/SET_ROOMINFO', {})
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    utils.getDataDict(this, 'relationship', 'relationshipList')
  },

  methods: {
    // << --- 弹窗 ---
    // -- 表单弹窗
    showMiDlg(type, row) {
      if (utils.isNull(this.roomInfo.id)) {
        this.$message.warning('请选择房屋')
        return
      }

      if (type == 'add') {
        this.dlgMiQuery = { id: 0 }
      } else {
        this.dlgMiQuery = row
      }
      this.dlgMiType = type
      this.dlgMiState = true
    },
    // 关闭弹窗
    closeMiDlg() {
      this.dlgMiState = false
    },

    /////////
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取数据
    getList() {
      if (utils.isNull(this.roomInfo.id)) {
        this.$message.warning('请选择房屋')
        return
      }
      this.count++
      this.listLoading = true
      buildingmemberRoom(this.roomId).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 选择业主
    showRoomDlg() {
      let roomInfo = JSON.parse(JSON.stringify(this.roomInfo))
      this.$store.commit('communityMan/roomDlg/SET_ROOMID', this.roomId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMNAME', this.roomName)
      this.$store.commit('communityMan/roomDlg/SET_ROOMINFO', this.roomInfo)
      this.$store.commit('communityMan/roomDlg/SET_DLGSHOW', true)
    },

    // 获取业主信息
    getOwnerInfo() {
      if (this.dlgData.phone.length == 11) {
        let phone = this.dlgData.phone
        buildingmemberPhone(this.dlgData.phone).then((res) => {
          this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
          this.dlgData.phone = phone
          if (res.data.code == 200) {
            this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(res.data.data)))
          }
        })
      }
    },

    // 显示弹窗
    addItem() {
      if (utils.isNull(this.roomInfo.id)) {
        this.$message.warning('请选择房屋')
        return
      }
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg() {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.relationshipName = utils.getNameById(postParam.relationship, this.relationshipList)
          postParam.rooms = [this.roomInfo]
          postParam.type = 2
          this.dlgLoading = true
          buildingmemberAddOrUpdate(postParam).then((res) => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem(data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgType = type
      this.dlgShow = true
    },

    // 启用停用
    delItem(data) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        buildingmemberMember(data.id, this.roomId).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload(file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/memberInfo_').then((res) => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg() {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


