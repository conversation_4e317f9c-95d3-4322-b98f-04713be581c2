import request from '@/utils/request'

// 访客分页查询
export function visitorrecordPage(data){
  return request({
    url: `/unity/visitorrecord/page`,
    method: 'post',
    data
  })
}

// 新增/修改访客业务
export function visitorrecordAddOrUpdate(data) {
  return request({
    url: `/unity/visitorrecord/addOrUpdate`,
    method: 'post',
    data
  })
}

// 删除访客业务
export function visitorrecordDisable(id, flag) {
  return request({
    url: `/unity/visitorrecord/disable/${id}/${flag}`,
    method: 'get'
  })
}

// 查询访客详情
export function visitorrecordInfo(id) {
  return request({
    url: `/unity/visitorrecord/info/${id}`,
    method: 'get'
  })
}
