<template>
  <!--订单指派-->
  <div class="app-container">
    <!-- 搜素按钮组 -->
    <div class="filter-container">
      <el-form ref="searchForm" class='n-search' label-width="90px" @submit.native.prevent>
        <div class="n-search-bar">
          <div class='n-search-item n-search-item-r fr'>
            <el-input v-model="listQuery.str" placeholder='请输入就诊医院\患者姓名\性别\联系电话'>
              <i @click="resetStr" slot="suffix" class="el-input__icon el-icon-error"></i>
            </el-input>
            <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">搜索</el-button>
          </div>
          <div class="clear"></div>
        </div>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="任务内容">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="就诊医院">
          <template slot-scope="scope">
            <span>{{ scope.row.projectName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="患者姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.patientName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="性别/年龄">
          <template slot-scope="scope">
            <span>{{ scope.row.sexAge }}</span>
          </template>
        </el-table-column>

        <el-table-column label="联系电话">
          <template slot-scope="scope">
            <span>{{ scope.row.patientPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="紧急联系方式">
          <template slot-scope="scope">
            <span>{{ scope.row.emergencyPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="陪护时间">
          <template slot-scope="scope">
            <span>{{ scope.row.times }}</span>
          </template>
        </el-table-column>

        <el-table-column label="详细情况说明">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="评价">
          <template slot-scope="scope">
            <span>{{ scope.row.evaluation }}</span>
          </template>
        </el-table-column>

        <el-table-column label="患者总结">
          <template slot-scope="scope">
            <span>{{ scope.row.conclusion }}</span>
          </template>
        </el-table-column>

        <el-table-column label="陪护人">
          <template slot-scope="scope">
            <span>
              <el-button type="primary" size='mini' icon="el-icon-right" @click="assignOrder(scope.row)">指派</el-button>
            </span>
          </template>
        </el-table-column>

      </el-table>
    </div>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>
    <el-dialog :close-on-click-modal='false' title="指派订单" :visible.sync="dialogVisible">
      <el-table class='m-small-table' :data="userList" border fit highlight-current-row @current-change="handleCurrentChange">
        <el-table-column label="选中" width="50">
          <template slot-scope="scope">
            <el-radio class="radio" v-model="selectedUserId" :label="scope.row.userId">{{ scope.$index + 1}}</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="星级">
          <template slot-scope="scope">
            <span>{{ scope.row.star }}</span>
          </template>
        </el-table-column>

        <el-table-column label="陪护人">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="性别">
          <template slot-scope="scope">
            <span>{{ scope.row.sex }}</span>
          </template>
        </el-table-column>

        <el-table-column label="本月服务小时数">
          <template slot-scope="scope">
            <span>{{ scope.row.hours }}</span>
          </template>
        </el-table-column>

        <el-table-column label="电话">
          <template slot-scope="scope">
            <span>{{ scope.row.userPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态">
          <template slot-scope="scope">
            <span>{{ scope.row.stateText }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination :total="userTotal" :page.sync="userListQuery.page" :limit.sync="userListQuery.size" @pagination="getUserList" />

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitDialog()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination' //分页
import * as util from "@/utils";
import { findPzTaskDynamic, findPzTaskUserDynamic, pzTaskAssign } from "@/api/medicalAuxiliaryManSystem/accompany/accompany"

export default {
  components: {
    Pagination
  },
  data () {
    return {
      dialogVisible: false,
      userList: [],
      list: [],
      listQuery: {
        status: "0",
        str: "",
        page: 1,
        size: 20,
      },
      total: 0,
      userListQuery: {
        page: 1,
        size: 20,
      },
      userTotal: 0,
      listLoading: false,
      selectedUserId: "",
      selectedTaskId: "",
    }
  },
  created () {
    this.getList()
    this.getUserList()
  },
  methods: {
    getList () {
      this.list = []
      this.listLoading = true;
      findPzTaskDynamic(this.listQuery).then(res => {
        this.listLoading = false;
        let code = res.data.code;
        let msg = res.data.msg;
        if (code === "200") {
          let data = res.data.data;
          if (util.isNull(data)) {
            this.$message({
              type: "warning",
              message: msg
            });
            return false
          }
          this.total = data.total
          this.list = res.data.list;
        }
        else {
          this.$message.error(msg);
        }
      });
    },
    getUserList () {
      this.userList = []
      findPzTaskUserDynamic(this.userListQuery).then(res => {
        let code = res.data.code;
        let msg = res.data.msg;
        if (code === "200") {
          let data = res.data.data;
          if (util.isNull(data)) {
            this.$message({
              type: "warning",
              message: msg
            });
            return false
          }
          this.userTotal = data.total
          this.userList = res.data.list;
        }
        else {
          this.$message.error(msg);
        }
      });
    },
    handleCurrentChange (data) {
      this.selectedUserId = data.userId
    },
    searchItem () {
      this.getList()
    },
    resetStr () {
      this.listQuery.str = ''
      this.getList()
    },
    assignOrder (data) {
      this.dialogVisible = true
      this.selectedTaskId = data.id
    },
    submitDialog () {
      if (util.isNull(this.selectedUserId)) {
        this.$message.error('请选择陪护人')
        return
      }
      let postParam = {
        userId: this.selectedUserId,
        pzTaskId: this.selectedTaskId
      }
      pzTaskAssign(postParam).then(res => {
        let code = res.data.code;
        let msg = res.data.msg;
        if (code === "200") {
          this.$message.success("指派成功")
          this.getList()
          this.getUserList()
        }
        else {
          this.$message.error(msg);
        }
      });
      this.dialogVisible = false;
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>