import request from '@/utils/request'

/*
*陪诊陪检
*/

// 动态查询陪诊订单
export function findUserOrderDynamic(data) 
{
	return request({
		url: `/u/usapi/order/findUserOrderDynamic`,
		method: 'post',
		data
	})
}

// 跟踪评价反馈
export function updateOrderValuateGenZong(data)
{
	return request({
		url: `/u/usapi/task/updateOrderValuateGenZong`,
		method: 'post',
		data
	})
}

// 动态查询反馈意见
export function findOrderValuateByDynamic(data)
{
	return request({
		url: `/u/usapi/task/findOrderValuateByDynamic`,
		method: 'post',
		data
	})
}





