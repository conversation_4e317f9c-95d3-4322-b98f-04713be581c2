
const userTree = {
  state: {
    // 考勤设置
    kqzTabIndex: 0,  // 考勤设置 tab index
    kqzTypeId: '',  // 考勤组分类ID

    // 用户权限
    yhqxTabIndex: 0,  // 用户权限 tab index
    yhqxTypeId: '',  // 用户分类 Id
  },

  mutations: {
    // 考勤设置
    SET_KQZTABINDEX: (state, val) => {
      state.kqzTabIndex = val
    },
    SET_KQZTYPEID: (state, val) => {
      state.kqzTypeId = val
    },

    // 用户分类
    SET_YHQXTABINDEX: (state, val) => {
      state.yhqxTabIndex = val
    },
    SET_YHQXTYPEID: (state, val) => {
      state.yhqxTypeId = val
    },
  },

  actions: {
    
  }
}

export default userTree
