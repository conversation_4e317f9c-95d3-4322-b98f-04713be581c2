<template>
  <div class="m-dash-bar calendar-bar">
    <p class="m-dash-bar-title">日历</p>
    <!-- 年月箭头选择 -->
    <div class="m-calendar">
      <p style="color: #666; margin-bottom: 22px; text-align: center;">{{calendarMonth}}</p>
      <!-- <el-row class="m-calendar-t">
        <el-col :span='6'>
          <i @click="calendarArrowClick(1)" class='el-icon-d-arrow-left fl' title='前一年' style="margin-left: 4%"></i>
          <i @click="calendarArrowClick(2)" class='el-icon-arrow-left fl' title='前一月'></i>
        </el-col>
        <el-col :span='12'>
          <el-date-picker
            @change='calendarChange'
            class="calendar-date"
            v-model="calendarMonth"
            :clearable='false'
            type="month"
            format="yyyy-MM"
            value-format="yyyy-MM"
            placeholder="选择月份">
          </el-date-picker>
        </el-col>
        <el-col :span='6'>
          <i @click="calendarArrowClick(3)" class='el-icon-d-arrow-right fr' title='后一年' style="margin-right: 4%"></i>
          <i @click="calendarArrowClick(4)" class='el-icon-arrow-right fr' title='后一月'></i>
        </el-col>
      </el-row> -->
      <!-- 日历表格 -->
      <div class="m-calendar-c">
        <!-- 头 -->
        <div class="m-calendar-c-head dflex">
          <div v-for="item of 7" :key="item" class="m-calendar-item flex-auto">
            {{item == '7' ? '日' : returnWeekUpCase(item)}}
          </div>
        </div>
        <!--  -->
        <!-- ${today} == ${calendarData[(item1-1) * 7 + item - 1].date} ? 'thisDay': '' -->
        <div v-for='item1 of (calendarData.length/7)' :key="item1" class="dflex">
          <div @click='calendarClick(calendarData[(item1-1) * 7 + item - 1].date)' v-for="item of 7" :key="item" :class="`m-calendar-item flex-auto item1 ${calendarData[(item1-1) * 7 + item - 1].type} ${calendarData[(item1-1) * 7 + item - 1].bgClass}`" :title='calendarData[(item1-1) * 7 + item - 1].date'>
            {{calendarData[(item1-1) * 7 + item - 1].label}}

            <!-- 班/休 -->
            <div class="m-calendar-item-type">
              <span :class="calendarData[(item1-1) * 7 + item - 1].workType == '休'? 'fwarning': ''">{{calendarData[(item1-1) * 7 + item - 1].workType}}</span>
            </div>

            <!-- 是否有待办事项，有并未完成显示黄点，有并且已完成-显示绿点 -->
            <div v-if='calendarData[(item1-1) * 7 + item - 1].state == 1' class="m-calendar-item-point yellow"></div>
            <div v-if='calendarData[(item1-1) * 7 + item - 1].state == 2' class="m-calendar-item-point green"></div>
          </div>
        </div>

      </div>
    </div>
    
    <!-- 鼠标悬停时，显示的事项列表 -->

    <div v-show='dbsxState' class="calendar-bar-ab">
      <p class="m-dash-bar-title" style="margin-top: 16px;">
        已发事项 - {{selectedDay}}
        <i @click="closeDbsx" title='关闭' class="el-icon-close fr m-dash-bar-title-close"></i>
      </p>

      <div v-loading='tzListLoading' class="calendar-bar-ab-con">
        <div @click="pageTo('/processMan/yifaMatter')" v-for='(item, index) of tzList' :key="index" :title='item.title' class="tzgg-item dflex">
          <div class="tzgg-item-index">{{index + 1}}</div>
          <div class="tzgg-item-title flex1 elli">{{item.processName}}</div>

          <div v-if='item.currentActType == "已结束"' class="tzgg-item-state fsuccess">{{item.currentActType}}</div>
          <div v-else class="tzgg-item-state fdanger">{{item.currentActType}}</div>
          
          <div class="tzgg-item-time">{{item.applyTime}}</div>
          <div class="tzgg-item-more"><i class="el-icon-arrow-right"></i></div>
        </div>

        <div v-show='tzListEmptyState' class="tzgg-empty">
          <img class="tzgg-empty-img" src="/static/image/list_empty.jpg" alt="">
          <p class="tzgg-empty-p">当前日期暂无已发事项 ~</p>
        </div>
        <div v-show="tzList.length" @click="pageTo('/processMan/yifaMatter')" class="tzgg-more" title='查看更多'>查看更多</div>
      </div>

    </div>

  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'

import { returnCalendarByMonth, returnWeekUpCase, return2Num } from '@/utils/calendarData.js'

import { 
  punchAttendancesByMonth,  // 日历接口
} from '@/api/dashboard'

import { 
  alreadyissued,  // 已发事项
} from '@/api/processMan'

export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      userId: '',  // 用户 userId
      // 日历
      calendarMonth: '',
      calendarData: [],  // 日历数据
      calendarLoading: false,  // 日历加载loading状态

      today: '',  // 今天
      selectedDay: '',  // 选中的天

      // 待办事项列表
      tzList: [
        // { id: 1, title: '万线测试祁鑫发来一条审批任务:取消兼岗单', time: '2019-06-24' },
      ],
      tzListLoading: false,  // loading
      tzListEmptyState: true,  // 显示 空 的图标

      // 待办事项的 显示状态
      dbsxState: false,

      rlLoading: false,  // 日历loading
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ])
  },
  created() {
    let tDay = new Date()
    this.calendarMonth = `${tDay.getFullYear()}-${return2Num(tDay.getMonth() + 1)}`
    this.getRili(this.calendarMonth)
    this.today = `${tDay.getFullYear()}-${return2Num(tDay.getMonth() + 1)}-${return2Num(tDay.getDate())}`

    this.userId = Cookie.get('userId')
  },
  methods: {
    // 选中日期，显示弹窗中 的 待办列表
    getDaiban(date) {
      this.tzListEmptyState = false
      this.tzListLoading = true
      this.tzList = []
      let sendObj = {
        page: 1,
        size: 10,
        userId: this.userId,
        beginTime: date,  // 开始事件
        endTime: date,  // 结束之间
        processId: "",  // 单据类型
        processName: "",  // 单据类型
      }
      alreadyissued(sendObj).then(res => {
        this.tzListLoading = false
        // tzList
        if (res.data.code == 200) {
          if (res.data.list) {
            this.tzList = res.data.list
          } else {
            this.tzListEmptyState = true
          }
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })

    },
    // 获取日历
    getRili(calendarMonth) {
      this.rlLoading = true
      let sendObj = {
        yearMonth: calendarMonth
      }
      punchAttendancesByMonth(sendObj).then(res => {
        this.rlLoading = false
        if (res.data.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data.list))
          this.changeCalendarMonth(calendarMonth, list)
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })
    },
    // 跳转页面
    pageTo(url) {
      this.$router.push({path: url})
    },
    ////// 日历相关
    // 返回 周 大写
    returnWeekUpCase(week) {
      return returnWeekUpCase(week)
    },
    changeCalendarMonth(month, monthData) {
      if (monthData != null)
      {
        this.calendarData = returnCalendarByMonth(month, monthData)
      }
    },
    // 箭头改变月份 1-前一年，2-前一月，3-后一年；4-后一月
    calendarArrowClick(type) {
      console.log('111', this.calendarMonth)
      let year = parseInt(this.calendarMonth.split('-')[0])
      let month = parseInt(this.calendarMonth.split('-')[1])
      if (type == 1) {
        this.calendarMonth = `${year - 1}-${return2Num(month)}`
      } else if(type == 2) {
        if (month == 1) {
          this.calendarMonth = `${year - 1}-12`
        }  else {
          this.calendarMonth = `${year}-${return2Num(month - 1)}`
        }
      } else if(type == 3) {
        this.calendarMonth = `${year + 1}-${return2Num(month)}`
        
      } else if(type == 4) {
        if (month == 12) {
          this.calendarMonth = `${year + 1}-01`
        }  else {
          this.calendarMonth = `${year}-${return2Num(month + 1)}`
        }
      }

      this.getRili(this.calendarMonth)
    },
    // 点击日期 显示待办事项
    calendarClick(date) {
      // 选中日期
      console.log('123', this.calendarData)
      for (let item of this.calendarData) {
        if (date == item.date) {
          item.bgClass = 'green'
        } else {
          item.bgClass = 'white'
        }
      }
      // 右侧 弹窗列表数据
      this.selectedDay = date
      this.dbsxState = true
      this.getDaiban(date)
    },
    // 关闭待办事项
    closeDbsx() {
      this.dbsxState = false
    },
    // 选择月份
    calendarChange(val) {
      this.calendarData = returnCalendarByMonth(val)
    },
    
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  @import 'src/styles/dashboard.scss';
  // 日历
  .calendar-bar {
    position: relative;
  }
  .m-calendar-t {
    // width: 400px;
  }
  .m-calendar-t i {
    height: 40px;
    line-height: 40px;
    cursor: pointer;
  }
  .m-calendar-c {
    // width: 400px;
    height: 304px;
  }

  .m-calendar-item {
    position: relative;
    width: 50px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 12px;
    
    margin: 4px 4px 0;
  }
  .m-calendar-item.item1 {
    cursor: pointer;
    border-radius: 3px;
    height: 40px;
    line-height: 40px;
  }

  .m-calendar-item.item1:hover,.m-calendar-item.item1.thisDay {
    background: #f3f3f3;
  }
  .m-calendar-item.item1.white {
    background: #fff;
  }
  .m-calendar-item.item1.green {
    background: #42d2b9;
    color: #FFF;
    .m-calendar-item-type {
      color: #FFF;
      .fwarning {
        color: #FFF;
      }
    }
  }
  .m-calendar-item.pre,.m-calendar-item.next {
    color: #999;
  }

  .calendar-date {
    width: 100%;display:block;margin: 6px auto 0;
  }

  .m-calendar-c-head {
    background: #f2f2f2;
    color: #19AA8D
  }

  // 班/休
  .m-calendar-item-type {
    position: absolute;
    right: 15%;
    top: 0px;
    color: #67C23A;
  }
  // 圆点
  .m-calendar-item-point {
    position: absolute;
    bottom: 4px;
    left: 50%;
    margin-left: -2px;

    width: 4px;
    height: 4px;
    border-radius: 2px;
  }
  .m-calendar-item-point.yellow {
    background: #E6A23C
  }
  .m-calendar-item-point.green {
    background: #67C23A
  }

  // 待办事项的 绝对定位 框
  .calendar-bar-ab {
    box-sizing: content-box;
    position: absolute;
    z-index: 9;
    left: 100%;
    top: 0px;

    padding-left: 16px;
    width: 100%;
    height: 100%;
    background: #fff;
    border-left: 1px dashed #f5f5f5;
    border-radius: 0 3px 3px 0;
  } 
  .calendar-bar-ab-con {
    position: relative;
    padding-right: 16px;
    margin-top: 4px;
    height: 310px;
  }


  // 通知公告
  .tzgg-item {
    border-bottom: 1px dashed #f5f5f5;
    line-height: 30px;
    color: #888;
    font-size: 12px;
    cursor: pointer;
  }
  .tzgg-item-index {
    width: 22px;
    text-align: left;
  }
  .tzgg-item-time {
    width: 126px;
    text-align: right;
  }
  .tzgg-item-state {
    width: 60px;
    text-align: right;
  }
  .tzgg-item-more {
    margin-left: 6px;
  }
  .tzgg-more {
    position: absolute;
    bottom: -38px;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 12px;
    line-height: 30px;
    color: #888;
    cursor: pointer;
  }
  .tzgg-more:hover {
    color: #000;
    text-decoration: underline;
  }
  .m-dash-bar-title-close {
    margin-right: 16px;
    cursor: pointer;
  }

  // 待办事项 弹窗
  .tzgg-empty {
    padding-top: 50px;

    .tzgg-empty-img {
      display: block;
      margin: 0px auto 10px
    }
    .tzgg-empty-p {
      text-align: center;
      color: #999;
      font-size: 14px

    }
  }
</style>