// 部门dlg组件

const branchDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    dlgType: "", // 调用不同接口

    dlgParam: {}, // 调用接口参数

    branchId: '',

    branchName: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    dlgType: state => state.dlgType,

    dlgParam: state => state.dlgParam,

    branchId: state => state.branchId,

    branchName: state => state.branchName
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val
    },

    SET_DLGPARAM: (state, val) => {
      state.dlgParam = val
    },

    SET_BRANCHID: (state, val) => {
      state.branchId = val
    },

    SET_BRANCHNAME: (state, val) => {
      state.branchName = val
    }
  },

  actions: {

  }
}

export default branchDlg
