<template>
  <div>
    <!-- 共享图片上传 -->
    <el-form-item label="共享图片" prop="imgUrl">
      <qiniuUpload
        :fileList0="formData.imgUrl"
        ref="qiniuUploadRef0"
        :limit="5"
        :maxSize="50"
        @successBack="handleImgUrlChange"
        :onlyImage="true"
      />
    </el-form-item>

    <!-- 原始文件上传 -->
    <el-form-item v-if="!hideFiles" label="原始文件" prop="fileUrl">
      <allFileUpload
        :fileList0="formData.fileUrl"
        ref="qiniuUploadRef1"
        :limit="5"
        :maxSize="50"
        @successBack="handleFileUrlChange"
      />
    </el-form-item>

    <!-- 附件上传（选填） -->
    <el-form-item v-if="!hideFiles" label="附件">
      <allFileUpload
        :fileList0="formData.annexUrl"
        ref="qiniuUploadRef2"
        :limit="5"
        :maxSize="50"
        @successBack="handleAnnexUrlChange"
      />
    </el-form-item>
  </div>
</template>

<script>
// import qiniuUpload from "@/views/greenMan/components/qiniuUpload";
import allFileUpload from "@/views/deviceMan/components/allFileUpload";
import qiniuUpload from "@/views/deviceMan/components/fileUpload";

export default {
  name: "FileUpload",
  components: {
    qiniuUpload,
    allFileUpload,
  },
  props: {
    formData: {
      type: Object,
      required: true,
    },
    dlgType: {
      type: String,
      default: "add",
    },
    hideFiles: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {};
  },

  methods: {
    handleImgUrlChange(fileList) {
      this.formData.imgUrl = fileList;
    },
    handleFileUrlChange(fileList) {
      this.formData.fileUrl = fileList;
    },
    handleAnnexUrlChange(fileList) {
      this.formData.annexUrl = fileList;
    },
  },
};
</script>

<style lang="scss" scoped></style>
