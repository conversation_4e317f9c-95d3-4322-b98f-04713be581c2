import request from "@/utils/request";

/*
 * 医废参数设置
 */

// 根据项目ID获取项目信息
export function queryProjectById(data) {
  return request({
    url: `/sys/queryProjectById`,
    method: "post",
    data
  });
}

// 根据项目ID修改项目信息
export function updateProjectById(data) {
  return request({
    url: `/sys/updateProjectById`,
    method: "post",
    data
  });
}

// 查询扎带根据科室
export function findMedicalWasteBandageByBranchId(data) {
  return request({
    url: `/me/findMedicalWasteBandageByBranchId`,
    method: "post",
    data
  });
}

// 查询扎带详情
export function findMedicalBandageDistriByType(data) {
  return request({
    url: `/me/findMedicalBandageDistriByType`,
    method: "post",
    data
  });
}

// 发放 盘亏
export function distributionTie(data) {
  return request({
    url: `/me/distributionTie`,
    method: "post",
    data
  });
}
// 获取医废类别
export function medicalDicListAll() {
  return request({
    url: `/me/medical/dic/listAll`,
    method: "get"
  });
}
// 按项目查 医废类别
export function medicalTypeList(projectId) {
  return request({
    url: `/me/medical/type/list/${projectId}`,
    method: "get"
  });
}
