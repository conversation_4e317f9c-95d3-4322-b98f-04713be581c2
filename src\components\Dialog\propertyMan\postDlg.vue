<template>
  <el-dialog :close-on-click-modal='false' :title="'选择岗位'" :before-close="closeDlg" :visible.sync="dlgShow">
    <div class="filter-container">
      <el-form ref="searchForm" :inline="true" class='clearfix' label-width="90px" @submit.native.prevent>
        <el-form-item>
          <el-input @keyup.enter.native='getList' v-model="listQuery.label" placeholder='请输入岗位/在岗员工'>
            <i slot="suffix" @click='resetSearchItem(["label"])' class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.branchName" @focus="showBranchDlg" placeholder="选择部门" readonly>
            <el-select style="width: 100px" v-model="listQuery.branchType" slot="prepend" placeholder="请选择">
              <el-option label="当前部门" value="0"></el-option>
              <el-option label="当前及所属部门" value="1"></el-option>
            </el-select>
            <i @click='resetSearchItem(["branchId", "branchName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" limit='mini' @click="searchItem">
          搜索
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table ref="postTable" class='m-small-table' :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="#" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectPostId" :label="scope.row.postId">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>

        <el-table-column label="岗位名称">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属部门">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="在岗员工" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="岗位类型" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.postCategoryText }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check">
        确 定
      </el-button>
    </div>
    <branchDlg />
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import branchDlg from '@/components/Dialog/platformMan/branchDlg'
import {
  findPostByDynamic,  // 获取岗位
  findPostNotByDynamic
} from '@/api/postMan'  // 查岗位

export default {
  components: {
    Pagination,
    branchDlg
  },
  data() {
    return {

      list: [],

      listQuery: {
        page: 1,
        size: 10,
        label: "",
        branchId: '',
        branchName: '',
        branchType: '0'
      },

      total: 0,

      selectPostId: '',

      selectPostInfo: ''

    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.propertyMan.postDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('propertyMan/postDlg/SET_DLGSHOW', val)
      }
    },

    dlgType: {
      get: function () {
        return this.$store.state.propertyMan.postDlg.dlgType
      },
      set: function (val) {
        this.$store.commit('propertyMan/postDlg/SET_DLGTYPE', val)
      }
    },


    postQuery: {
      get: function () {
        return this.$store.state.propertyMan.postDlg.postQuery
      },
      set: function (val) {
        this.$store.commit('propertyMan/postDlg/SET_POSTQUERY', val)
      }
    },

    postId: {
      get: function () {
        return this.$store.state.propertyMan.postDlg.postId
      },
      set: function (val) {
        this.$store.commit('propertyMan/postDlg/SET_POSTID', val)
      }
    },

    postInfo: {
      get: function () {
        return this.$store.state.propertyMan.postDlg.postInfo
      },
      set: function (val) {
        this.$store.commit('propertyMan/postDlg/SET_POSTINFO', val)
      }
    },

    ...mapGetters('platformMan/branchDlg', {
      branchId: 'branchId',
      branchName: 'branchName'
    }),

  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.postId)) {
          this.selectPostId = ""
          this.selectPostInfo = ""
        } else {
          this.selectPostId = this.postId
          this.selectPostInfo = JSON.parse(JSON.stringify(this.postInfo))
        }
        this.listQuery = Object.assign(this.listQuery, this.postQuery)
        this.getList()
      }
    },

    branchId(val) {
      this.listQuery.branchId = val
    },

    branchName(val) {
      this.listQuery.branchName = val
    },

    postId: {
      handler(val) {
        this.selectPostId = val
      },
      immediate: true,
      deep: true
    },

    postInfo: {
      handler(val) {
        this.selectPostInfo = val
      },
      immediate: true,
      deep: true
    },

  },

  methods: {

    // 显示部门对话框
    showBranchDlg() {
      let branchId = this.listQuery.branchId
      let branchName = this.listQuery.branchName
      this.$store.commit('platformMan/branchDlg/SET_BRANCHPERMISSION', true)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHID', branchId)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', branchName)
      this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', true)
    },


    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    rowClick(row, column, event) {
      this.selectPostId = row['postId']
      this.selectPostInfo = JSON.parse(JSON.stringify(row))
    },

    getList() {
      this.list = []
      let method = findPostByDynamic
      if (this.dlgType === 'ALL') {
        method = findPostNotByDynamic
      }
      method(this.listQuery).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      this.$store.commit('propertyMan/postDlg/SET_POSTID', this.selectPostId)
      this.$store.commit('propertyMan/postDlg/SET_POSTINFO', JSON.parse(JSON.stringify(this.selectPostInfo)))
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('propertyMan/postDlg/SET_POSTQUERY', {})
      this.$store.commit('propertyMan/postDlg/SET_DLGTYPE', '')
      this.$store.commit('propertyMan/postDlg/SET_DLGSHOW', false)
    }
  }
}
</script>