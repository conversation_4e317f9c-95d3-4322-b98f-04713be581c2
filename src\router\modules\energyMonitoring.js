/** 能源监控 **/

import Layout from "@/views/layout/Layout";
const energyMonitoringRouter = {
  path: "/energyMonitoring",
  component: Layout,
  name: "energyMonitoring",
  meta: {
    title: "能耗监测",
    icon: "nyjk",
    roles: ["nengyuanjiance"]
  },
  children: [
    {
      path: "detectionPlatform",
      component: () =>
        import("@/views/energyMonitoring/detectionPlatform/index"),
      name: "能耗监测大屏",
      meta: {
        title: "能耗监测大屏",
        roles: ["ecm_daping"]
      }
    },
    {
      path: "abnormalAlarmMan",
      component: () => import("@/views/equipSafeMan/abnormalAlarmMan"),
      name: "报警管理",
      meta: {
        title: "报警管理",
        roles: ["ecm_baojingguanli"]
      }
    },
    {
      path: "sensorEquip",
      component: () => import("@/views/equipSafeMan/sensorEquip"),
      name: "传感器管理",
      meta: {
        title: "传感器管理",
        roles: ["ecm_chuanganqiguanli"]
      }
    },
    {
      path: "cameraManagement",
      component: () => import("@/views/equipSafeMan/cameraManagement"),
      name: "摄像机管理",
      meta: {
        title: "摄像机管理",
        roles: ["ecm_shexiangjiguanli"]
      }
    },
    {
      path: "statisticalAnalysis",
      component: () =>
        import("@/views/electricalFireMonitoring/statisticalAnalysis/index"),
      meta: {
        title: "统计分析",
        roles: ["ecm_tongjifenxi"]
      },
      children: [
        {
          path: "warnInfoQuery",
          component: () =>
            import(
              "@/views/electricalFireMonitoring/statisticalAnalysis/warnInfoQuery"
            ),
          name: "报警信息查询",
          meta: {
            title: "报警信息查询",
            roles: ["ecm_baojingxinxichaxun"]
          },
          children: []
        },
        {
          path: "alarmStatisticalAnalysis",
          component: () =>
            import(
              "@/views/electricalFireMonitoring/statisticalAnalysis/alarmStatisticalAnalysis"
            ),
          name: "报警信息统计",
          meta: {
            title: "报警信息统计",
            roles: ["ecm_baojingxinxitongji"]
          },
          children: []
        },
        {
          path: "alarmReminderRecord",
          component: () =>
            import(
              "@/views/electricalFireMonitoring/statisticalAnalysis/alarmReminderRecord/index"
            ),
          name: "报警提醒记录",
          meta: {
            title: "报警提醒记录",
            roles: ["ecm_baojingjilutixing"]
          },
          children: []
        },
        {
          path: "energyConsumptionRanking",
          component: () =>
            import(
              "@/views/energyMonitoring/statisticalAnalysis/energyConsumptionRanking/index"
            ),
          name: "能耗排名分析",
          meta: {
            title: "能耗排名分析",
            roles: ["ecm_nenghaopaimingfenxi"]
          },
          children: []
        },
        {
          path: "energyUseStatistics",
          component: () =>
            import(
              "@/views/energyMonitoring/statisticalAnalysis/energyUseStatistics/index"
            ),
          name: "用能统计",
          meta: {
            title: "用能统计",
            roles: ["ecm_yongnengtongji"]
          },
          children: []
        },
        {
          path: "energyUseTrend",
          component: () =>
            import(
              "@/views/energyMonitoring/statisticalAnalysis/energyUseTrend/index"
            ),
          name: "用能趋势分析",
          meta: {
            title: "用能趋势分析",
            roles: ["ecm_yongnengqushifenxi"]
          },
          children: []
        }
      ]
    }
  ]
};
export default energyMonitoringRouter;
