// 多选科目dlg组件

const subjectDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    subjectIds: "",

    subjectNames: "",

    projectId: ""
  },

  getters: {
    dlgShow: state => state.dlgShow,

    subjectIds: state => state.subjectIds,

    subjectNames: state => state.subjectNames,

    projectId: state => state.projectId
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_SUBJECTIDS: (state, val) => {
      state.subjectIds = val;
    },

    SET_SUBJECTNAMES: (state, val) => {
      state.subjectNames = val;
    },

    SET_PROJECTID: (state, val) => {
      state.projectId = val;
    }
  },

  actions: {}
};

export default subjectDlgMul;
