<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <!-- <div class="search-item-label lh28">选择项目：</div>
            <el-select
              class="fl"
              style="width: 220px"
              v-model="searchForm.projectId"
              placeholder="选择项目"
              @change="projectChange"
              filterable
              clearable
            >
              <el-option
                v-for="item of projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-item"> -->
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input
              v-model="searchForm.equName"
              placeholder="关键字"
              clearable
              class="fl"
              style="width: 160px"
              @change="searchFunc"
            ></el-input>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
            <!-- <el-button type="success" @click="showDlg('add')" icon="el-icon-plus" class="fl ml10">添加</el-button> -->
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc(100vh - 290px)"
      ref="tableBar"
      class="m-small-table"
      v-loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="设备编码"
        align="center"
        prop="equCode"
        width="auto"
      >
      </el-table-column>
      <el-table-column
        prop="equName"
        label="设备名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="content"
        label="内容描述"
        width="300"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="statusStr"
        label="维修状态"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="finishTime"
        label="完成时间"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="repairPersonName"
        label="维修人员"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" width="auto" align="center">
        <template slot-scope="scope">
          <el-button
            @click="showDlg('info', scope.row)"
            icon="el-icon-document"
            size="mini"
            type="success"
            title="详情"
            plain
            >详情</el-button
          >
          <!-- <el-button type="primary" size="mini" @click="showDlg('edit', scope.row)" plain
            icon="el-icon-edit">编辑</el-button>
          <el-button type="danger" size="mini" @click="delFunc(scope.row)" plain icon="el-icon-delete">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="searchFunc()"
    />
    <div class="clear"></div>

    <addEdit ref="addEdit" :dlgType="dlgType"></addEdit>
    <!-- <infoView ref="infoView"></infoView> -->

    <el-dialog
      :title="'设备二维码-' + selectRow.equName"
      :visible.sync="dialogVisible"
      width="300px"
    >
      <div class="qrbox2">
        <div v-if="dialogVisible" ref="qrCode"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import addEdit from "./addEdit";
import { postAction, getAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination"; // 分页
import { MxDfProject } from "@/mixins/MxDfProject.js";
import QRCode from "qrcodejs2";
export default {
  components: {
    Pagination,
    addEdit,
  },
  mixins: [MxDfProject],

  data() {
    return {
      searchForm: {
        projectId: JSON.parse(window.localStorage.userInfo).projectId,
        equName: "",
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      total: 0,
      listLoading: false,
      dlgType: "add",
      projectList: [],
      dialogVisible: false,
      selectRow: "",
    };
  },
  created() {
    this.searchFunc();
  },
  mounted() {
    // this.searchFunc()
  },
  methods: {
    showQrCode(row) {
      this.selectRow = row;
      this.dialogVisible = true;
      setTimeout(() => {
        let qrUrl = row.equCode + "";
        this.qrcode = new QRCode(this.$refs.qrCode, {
          text: qrUrl,
          width: 200,
          height: 200,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H,
        });
      }, 200);
    },
    searchFunc() {
      let { equName, pageNo, pageSize, projectId } = this.searchForm;
      if (projectId == "") {
        this.$message.warning("请先选择项目");
        return;
      }

      this.listLoading = true;
      getAction(
        `/green/equ/repair-manage/page?projectId=${projectId}&equName=${equName}&pageNo=${pageNo}&pageSize=${pageSize}`
      ).then((res) => {
        this.listLoading = false;
        let { code, data } = res.data;
        if (code == 200) {
          this.tableData = data.list ? data.list : [];
          this.total = data.total ? data.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    showDlg(type, row) {
      let addEdit = this.$refs.addEdit;
      this.dlgType = type;
      if (type == "add") {
        addEdit.title = "添加";
      } else if (type == "info") {
        addEdit.title = "详情";
      } else {
        addEdit.title = "编辑";
      }
      if (type !== "add") {
        getAction(`/green/equ/repair-manage/get?id=${row.id}`).then((res) => {
          let { code, data } = res.data;
          if (code == 200) {
            console.log(data, "data");
            addEdit.getEquOptions(data.projectId);
            setTimeout(() => {
              addEdit.formData.projectName = data ? data.projectName : "";
              addEdit.formData.equModel = data ? data.equModel : "";
              addEdit.formData.equId = data ? data.equId : "";
              addEdit.formData.equName = data ? data.equName : "";
              addEdit.formData.id = data ? data.id : "";
              addEdit.formData.projectId = data.projectId ? data.projectId : "";

              if (data.fileUrl) {
                addEdit.formData.fileUrl = JSON.parse(data.fileUrl);
              } else {
                addEdit.formData.fileUrl = [];
              }

              addEdit.formData.content = data ? data.content : "";
              addEdit.formData.status = data ? data.status : "";
              addEdit.formData.repairPersonName = data
                ? data.repairPersonName
                : "";
              addEdit.formData.repairPersonId = data ? data.repairPersonId : "";
              addEdit.formData.finishTime = data ? data.finishTime : "";

              let accessoryJson = JSON.parse(data.accessoryJson);
              let typeJson = JSON.parse(data.typeJson);
              typeJson.map((item) => {
                item.options = [
                  {
                    id: item.contentId,
                    workDescribe: item.content,
                  },
                ];
              });

              addEdit.typeTableData = typeJson;
              addEdit.accessoryTableData = accessoryJson;
            }, 100);
          } else {
            this.$message.error(res.data.msg);
          }
        });
      }
      this.$refs.addEdit.dialogVisible = true;
    },
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAction(`/green/equ/repair-manage/delete?id=${row.id}`).then(
          (res) => {
            if (res.data.code === "200") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.searchFunc();
            } else {
              this.$message.error(res.data.msg);
            }
          }
        );
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.qrbox2 {
  padding: 36px 0 64px;
  width: 200px;
  height: 200px;
  margin: 0 auto;
  box-sizing: content-box;
}
</style>