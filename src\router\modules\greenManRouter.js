/** 绿化分支 **/
import Layout from "@/views/layout/Layout";

// component: () => import("@/views/heatMan/dataCollect/index"),
const examRouter = {
  path: "/greenMan",
  component: Layout,
  redirect: "/examMan/course/list",
  name: "greenMan",
  meta: {
    title: "绿化管理",
    icon: "greenMan",
    roles: ["green_lvhuaguanli_web"]
  },

  children: [
    {
      path: "plantKnowledge",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "plantKnowledge",
      meta: {
        title: "植物知识",
        roles: ["green_zhiwuzhishi_web"]
      },
      children: [
        {
          path: "diseasePestMan",
          component: () =>
            import("@/views/greenMan/plantKnowledge/diseasePestMan/index"),
          name: "diseasePestMan",
          meta: {
            title: "病虫害管理",
            roles: ["green_bingchonghaiguanli_web"]
          },
          children: []
        },

        {
          path: "seedlingTypeMan",
          component: () =>
            import("@/views/greenMan/plantKnowledge/seedlingTypeMan/index"),
          name: "seedlingTypeMan",
          meta: {
            title: "苗木种类管理",
            roles: ["green_miaomuzhongleiguanli_web"]
          },
          children: []
        }
      ]
    },

    {
      path: "maintainMan",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "maintainMan",
      meta: {
        title: "养护管理",
        roles: ["green_yanghuguanli_web"]
      },
      children: [

        {
          path: "taskDisplay",
          component: () =>
            import("@/views/greenMan/maintainMan/taskDisplay/index"),
          name: "taskDisplay",
          meta: {
            title: "任务展示",
            roles: ["green_renwuzhanshi_web"]
          },
          children: []
        },
        {
          path: "maintainHistory",
          component: () =>
            import("@/views/greenMan/maintainMan/maintainHistory/index"),
          name: "maintainHistory",
          meta: {
            title: "养护记录",
            roles: ["green_yanghujilu_web"]
          },
          children: []
        },
        {
          path: "statisticalReport",
          component: () =>
            import("@/views/greenMan/maintainMan/statisticalReport/index"),
          name: "statisticalReport",
          meta: {
            title: "统计报表",
            roles: ["green_tongjibaobiao_web"],
          },
          children: [],
        },
      ]
    },

    {
      path: "resourceMan",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "resourceMan",
      meta: {
        title: "资源管理",
        roles: ["green_ziyuanguanli_web"]
      },
      children: [

        {
          path: "greenDeviceMan",
          // component: () => import("@/views/greenMan/resourceMan/greenStaffMan/index"),
          component: () =>
            import("@/views/greenMan/resourceMan/greenDeviceMan/index"),
          name: "greenDeviceMan",
          meta: {
            title: "设备管理",
            roles: ["green_shebeianquan_web"]
          },
          children: []
        }
      ]
    }

    //
  ]
};

export default examRouter;
