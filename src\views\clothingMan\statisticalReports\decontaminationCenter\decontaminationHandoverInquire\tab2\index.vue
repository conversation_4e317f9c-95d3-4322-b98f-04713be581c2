<template>
  <div class="mazhenguo" style="padding-bottom: 10px">
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <!-- <div class="search-item">
            <div class="search-item-label">aaaa：</div>
          </div> -->
          <!-- <div class="search-item">
            <div class="search-item-label">筛选条件：</div>

            <el-select
              class="fl"
              size="small"
              v-model="listQuery.projectId"
              @change="searchFunc"
              placeholder="选择项目"
              style="width: 200px"
              filterable
            >
              <el-option v-for="(item, index) of projectsList" :key="index" :label="item.name" :value="item.id + ''"> </el-option>
            </el-select>
          </div> -->

          <div class="search-item">
            <div class="search-item-label">操作时间：</div>
            <el-date-picker
              class="fl"
              style="width: 230px"
              v-model="listQuery.dateRange"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="截止日期"
              size="small"
            >
            </el-date-picker>
            <el-button class="fl ml10" @click="searchFunc" size="small" icon="el-icon-search" type="primary">搜索</el-button>
          </div>
        </div>
      </div>
    </div>

    <el-table
      height="calc(100vh - 220px - 32px - 8px - 50px)"
      ref="tableRef"
      class="m-small-table"
      v-loading="listLoading"
      :key="tableKey"
      :data="list"
      border
      fit
      highlight-current-row
      @sort-change="sortChange"
      @select="tableSelectChange"
      @select-all="tableSelectAll"
      @row-click="tableRowClick"
    >
      <el-table-column label="#" type="index" align="center" width="60"> </el-table-column>

      <el-table-column label="档案名称" prop="archivesName"></el-table-column>
      <el-table-column label="材质" prop="clothMaterialText"></el-table-column>
      <el-table-column label="规格" prop="clothSpecificationText"></el-table-column>
      <el-table-column label="重量" prop="weight" width="120" align="center"></el-table-column>
      <el-table-column label="送洗数量" prop="totalCount" width="120" align="center"></el-table-column>

      <!-- <el-table-column label="状态" width="120px" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.status == 1" type="danger">未完成入库</el-tag>
            <el-tag v-if="scope.row.status == 2" type="success">已完成入库</el-tag>
            <el-tag v-if="scope.row.status == 99" type="warning">已取消</el-tag>
          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" width="140" align="center">
        <template slot-scope="scope">
          <el-button @click="showListDlg('info', scope.row)" icon="el-icon-document" size="mini" type="primary" title="详情" plain
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" /> -->
    <div class="clear"></div>
    <!-- 弹窗 新增/编辑 -->
    <!-- 弹窗 列表 -->
    <listDlg
      :dlgState0="dlgListState"
      :dlgData0="dlgListData"
      :dlgType="dlgListType"
      :dlgQuery="dlgListQuery"
      @closeDlg="closeListDlg"
      @getList="getList"
    />
    <!-- <branchDlgMul :isMultiple="true" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// 工具
import * as utils from '@/utils'

// 接口
import { postAction, getAction } from '@/api'

// 组件
// import Pagination from '@/components/Pagination' // 分页
import listDlg from './listDlg'
// import branchDlgMul from '@/components/Dialog/propertyMan/branchDlgMul'

let listQueryEmpty = {
  // 1-采购入库 2-中心发放 3-科室入库 4-科室发放 5-科室回收 6-科室送洗 7-洗消收取
  // 8-中心送洗 9-洗消接收 10-洗消交接 11-中心取回 12-洗消发放 13-科室洗消入库
  type: '8',
  projectId: '', // 项目
  // branchIds: '', // 科室
  // branchNames: '',

  dateRange: [],
  startDate: '',
  endDate: '',
}
export default {
  components: {
    // Pagination,
    listDlg,
    // branchDlgMul,
  },
  // props: {},
  data() {
    return {
      // 页面数据
      searchMoreState: false, // 更多筛选
      tableKey: 0,
      list: [],
      selectList: [], // 选中
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      // -- 表单弹窗
      dlgQuery: {},
      dlgState: false,
      dlgType: '', // 弹框状态add, edit
      dlgData: {},

      // -- 列表弹窗
      dlgListQuery: {},
      dlgListState: false,
      dlgListType: '', // 弹框状态add, edit
      dlgListData: {},

      userInfo: '',
      projectsList: [], // 医院列表
    }
  },
  computed: {
    ...mapGetters('propertyMan/branchDlgMul', {
      branchIds: 'branchIds',
      branchNames: 'branchNames',
    }),
  },
  watch: {
    branchIds(val) {
      this.listQuery.branchIds = val
      setTimeout(() => {
        this.listQuery = JSON.parse(JSON.stringify(this.listQuery))
      }, 100)
    },
    branchNames(val) {
      this.listQuery.branchNames = val
    },
  },
  created() {
    console.log('index-created')

    this.listQuery.dateRange = utils.returnToMonth()
    // let userInfo = (this.userInfo = JSON.parse(window.localStorage.ERPUserInfo))
    // this.projectsList = this.userProjectsList
    // if (this.dfProjectId) {
    //   this.listQuery.projectId = listQueryEmpty.projectId = this.dfProjectId + ''
    //   this.getList()
    // }

    // listQueryEmpty.rollMonth = getPreMonth()
    // this.listQuery.rollMonth = getPreMonth()
    // this.searchFunc()
    // 数据字典
    // let keyList = (this.keyMap = [
    //   // { dbKey: "sex", pageSelectKey: "sexSelect", pageKey: "sex", pageName: 'sexText' },
    //   { dbKey: 'post_name_type', pageSelectKey: 'typeSelect' }, // 婚姻状况
    // ])
    // utils.getDbItems(this, keyList)
  },
  mounted() {},
  methods: {
    showBranchDlgMul() {
      if (utils.isNull(this.listQuery.projectId)) {
        this.$message.warning('请先选择项目')
        return
      }
      this.$store.commit('propertyMan/branchDlgMul/SET_PROJECTID', this.listQuery.projectId)
      this.$store.commit('propertyMan/branchDlgMul/SET_BRANCHIDS', this.listQuery.branchIds)
      this.$store.commit('propertyMan/branchDlgMul/SET_BRANCHNAMES', this.listQuery.branchNames)
      this.$store.commit('propertyMan/branchDlgMul/SET_DLGSHOW', true)
    },
    // ------ 列表
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.searchFunc()
    },
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.list = []
      let sendObj = JSON.parse(JSON.stringify(this.listQuery))

      let userInfo = JSON.parse(window.localStorage.userInfo)
      sendObj.projectId = userInfo.projectId

      // 日期范围
      sendObj.startDate = ''
      sendObj.endDate = ''
      if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
        sendObj.startDate = sendObj.dateRange[0]
        sendObj.endDate = sendObj.dateRange[1]
      }
      delete sendObj.dateRange

      // sendObj.branchIds = sendObj.branchIds ? sendObj.branchIds.split(',') : []

      // sendObj.limit = sendObj.size
      // delete sendObj.size

      this.listLoading = true
      postAction('/cloth/coflow/archivesFlowCount', sendObj).then((res0) => {
        let res = res0.data
        this.listLoading = false
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = []
            this.total = 0
          } else {
            this.list = res.data
            // this.total = res.data.total

            this.$nextTick(() => {
              this.$refs.tableRef.doLayout()
            })
          }
        } else {
          this.total = 0
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },

    // -- 删除
    delFunc(row) {
      this.delAjax(row.id)
    },
    // 批量删除
    batchDelFunc() {
      if (!this.selectList.length) return false
      this.delAjax(sendObj)
    },
    delAjax(id) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let sendObj = {
          id,
        }
        getAction(`/aaa/bbb`, sendObj).then((res0) => {
          let res = res0.data
          if (res.code == '200') {
            this.$message({
              message: res.msg,
              type: 'success',
            })
            this.getList()
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
      })
    },
    // 排序
    sortChange(data) {
      let type = data.column.sortBy
      let order = data.order
      if (order == null) {
        type = ''
        order = ''
      } else {
        if (order == 'descending') {
          order = 'desc'
        } else {
          order = 'asc'
        }
      }
      this.listQuery.sortParam = type
      this.listQuery.sortOrder = order
      this.getList()
    },

    // << --- 弹窗 ---
    // -- 表单弹窗
    showDlg(type, row) {
      if (type == 'add') {
        this.dlgQuery = { id: 0 }
      } else {
        this.dlgQuery = row
      }
      this.dlgType = type
      this.dlgState = true
    },
    // 关闭弹窗
    closeDlg() {
      this.dlgState = false
    },
    // -- 列表
    // -- 列表弹窗
    showListDlg(type, row) {
      if (type == 'add') {
        this.dlgListQuery = { id: 0 }
      } else {
        let dlgListQuery = { ...this.listQuery, ...row }

        // 日期范围
        dlgListQuery.startDate = ''
        dlgListQuery.endDate = ''
        if (!utils.isNull(dlgListQuery.dateRange) && dlgListQuery.dateRange.length > 0) {
          dlgListQuery.startDate = dlgListQuery.dateRange[0]
          dlgListQuery.endDate = dlgListQuery.dateRange[1]
        }
        delete dlgListQuery.dateRange

        this.dlgListQuery = dlgListQuery
      }

      this.dlgListType = type
      this.dlgListState = true
    },
    // 关闭弹窗
    closeListDlg() {
      this.dlgListState = false
    },
    // >> --- 弹窗 ---

    // << 新 复选
    // @select="tableSelectChange"
    //   @select-all="tableSelectAll"
    //   @row-click="tableRowClick"
    // <el-table-column label="#" align="center" type="selection" width="55"> </el-table-column>
    // --- getList
    // 如果复选设置表格勾选
    // let list = this.list
    // this.$nextTick(() => {
    //   console.log('this.selectList', this.selectList)
    //   if (this.selectList.length > 0) {
    //     for (let item of list) {
    //       let isHas = this.selectList.some((row) => row.id == item.id)
    //       if (isHas) {
    //         this.$refs.tableRef.toggleRowSelection(item, true)
    //       } else {
    //         this.$refs.tableRef.toggleRowSelection(item, false)
    //       }
    //     }
    //   } else {
    //     this.$refs.tableRef.clearSelection()
    //   }
    // })

    // 点击复选框
    tableSelectChange(arr, row) {
      this.tableCheckBaseFunc(row)
    },
    // 点击行
    tableRowClick(row, column, event) {
      // let disabledCol = ['商品名称','商品编码','图片']
      // if (disabledCol.indexOf(column.label) >=0) return false
      this.$refs.tableRef.toggleRowSelection(row)
      this.tableCheckBaseFunc(row)
    },
    // 单行操作方法
    tableCheckBaseFunc(row) {
      let isCheck = !this.selectList.some((item) => item.id == row.id) // true-勾选状态，false-取下选择状态
      console.log('isCheck', isCheck)
      // 判断是否是勾选状态
      if (isCheck) {
        // 勾选
        this.selectList.push(row)
      } else {
        // 取消选择
        let selectList = this.selectList.filter((item) => {
          return item.id != row.id
        })
        this.selectList = JSON.parse(JSON.stringify(selectList))
      }
    },
    // 表格 全选
    tableSelectAll(arr) {
      let len = arr.length
      // console.log(arr.length)
      // 长度为0 取消全选，将list中所有数据，从selectList中移除
      // 长度不为0，全选，将list中所有数据，追加到 selectList中
      let list = JSON.parse(JSON.stringify(this.list))
      let selectList = JSON.parse(JSON.stringify(this.selectList))
      if (len == 0) {
        let newList = []
        for (let item of selectList) {
          let hasId = list.some((item2) => item2.id == item.id)
          if (!hasId) {
            newList.push(item)
          }
        }
        selectList = JSON.parse(JSON.stringify(newList))
      } else {
        for (let item of list) {
          let hasId = selectList.some((item2) => item2.id == item.id)
          if (!hasId) {
            selectList.push(item)
          }
        }
      }
      // console.log('完美的选中数据', selectList)
      this.selectList = selectList
    },
    // >> 新 复选

    // -- << 导入导出
    // 模板
    downLoadFunc() {
      let sendObj = {
        aaa: '111',
      }
      let sendUrl = location.protocol + '//' + location.host + `/api/chain/purchase/branch/export?` + utils.objToParam(sendObj)
      window.open(sendUrl)
      // window.open(
      //   'https://wlines.oss-cn-beijing.aliyuncs.com/cloud_service/downloadModel/%E8%80%81%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
      // )

      // ---- 调接口下载
      //  let sendObj = JSON.parse(JSON.stringify(this.listQuery))
      // // 日期范围
      // sendObj.startDate = ''
      // sendObj.endDate = ''
      // if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
      //   sendObj.startDate = sendObj.dateRange[0]
      //   sendObj.endDate = sendObj.dateRange[1]
      // }
      // delete sendObj.dateRange

      // sendObj.limit = sendObj.size
      // delete sendObj.size

      // postAction('/crm/refuelRecord/export', sendObj).then((res0) => {
      //   window.open(res0.data.data)
      // })

      // ---- 异步下载（带列表）
      // this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      // this.dlgShow = true
      // this.dlgType = 'EXPORT'
      // this.$nextTick(() => {
      //   this.$refs.dlgForm.clearValidate()
      // })
    },
    importExl1(file) {
      if (!this.fileValid(file)) return false
      let loading = this.$loading({
        lock: true,
        text: '导入中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      let sendObj = {
        file,
      }
      elderImport(sendObj).then((res) => {
        loading.close()
        this.importBack(res)
      })
    },
    fileValid(file) {
      var suffixArr = file.name.split('.')
      var suffix = suffixArr[suffixArr.length - 1].toLowerCase()
      if (suffix != 'xlsx' && suffix != 'xls') {
        this.$message.warning('文件格式错误')
        return false
      }
      return true
    },
    importBack(res) {
      if (res.code == 200) {
        this.$message.success(res.msg)
        this.getList()
      } else {
        this.$message({
          type: 'warning',
          message: res.msg,
        })
      }
    },
    // 接口导出
    // 导出
    outList(type, row) {
      let loading = this.$loading({
        lock: true,
        text: '导出中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      window.open(`/api/main/elder/importsf/${type}/${row.id}`)
      setTimeout(() => {
        loading.close()
      }, 2000)
    },
    // -- >> 导入导出
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>


