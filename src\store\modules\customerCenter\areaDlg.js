// 点位dlg组件

const areaDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    dlgType: "",

    curAreaId: "",

    areaId: "",

    areaName: "",
    areaName2: '',  // 带层级

    projectId: ""
  },

  getters: {
    dlgShow: state => state.dlgShow,

    dlgType: state => state.dlgType,

    curAreaId: state => state.curAreaId,

    areaId: state => state.areaId,

    areaName: state => state.areaName,
    areaName2: state => state.areaName2,

    projectId: state => state.projectId
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_CURAREAID: (state, val) => {
      state.curAreaId = val;
    },

    SET_AREAID: (state, val) => {
      state.areaId = val;
    },

    SET_AREANAME2: (state, val) => {
      state.areaName2 = val;
    },
    SET_AREANAME: (state, val) => {
      state.areaName = val;
    },

    SET_PROJECTID: (state, val) => {
      state.projectId = val;
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val;
    }
  },

  actions: {}
};

export default areaDlg;
