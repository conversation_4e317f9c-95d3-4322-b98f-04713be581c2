import request from "@/utils/request";

// 分页查询人脸录入记录
export function faceAuditPage(data) {
  return request({
    url: "/unity/rldongtaichaxun",
    method: "post",
    data
  });
}

// 审核人脸录入记录
export function updateFaceEntryStatus(id, status) {
  return request({
    url: `/unity/updateFaceEntryStatus/${id}/${status}`,
    method: "get"
  });
}

// 考勤组分页查询
export function findGroupByDynamic(data) {
  return request({
    url: "/unity/findGroupByDynamic",
    method: "post",
    data
  });
}

// 新建考勤组
export function saveAdeGroup(data) {
  return request({
    url: "/unity/saveAdeGroup",
    method: "post",
    data
  });
}

// 删除考勤组
export function delgroupById(id) {
  return request({
    url: `/unity/delgroupById/${id}`,
    method: "get"
  });
}
