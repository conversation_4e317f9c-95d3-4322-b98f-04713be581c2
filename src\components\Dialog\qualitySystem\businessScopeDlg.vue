<template>
  <el-dialog :close-on-click-modal="false" :title="'更换区域/点位'" :visible.sync="dlgShow" @close="closeDlg" append-to-body>
    <el-form>
      <el-form-item>
        <el-input placeholder="输入名称进行过滤" v-model="filterText"></el-input>
      </el-form-item>
      <el-tree ref="treeDom" highlight-current node-key="id" :props="defaultProps" :data="list" @node-click="treeNodeClick" :default-expanded-keys="defaultOpenList" :filter-node-method="filterNode" :expand-on-click-node="false">
      </el-tree>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-span" :title="selectName" v-if="selectName">当前选中：{{selectName}}</span>
      <el-button icon="el-icon-close" @click="closeDlg"> 取 消 </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg"> 确 定 </el-button>
    </div>

  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'

import { loadWorkRegionTree } from '@/api/qualitySystem/projectWorkSetup.js'

export default {
  components: {
  },

  props: {
    superDlgShow: {
      type: Boolean,
      required: true,
      default: false,
    },

    superSelectId: {
      type: String | Number,
      required: false,
      default: ''
    },

    superSelectName: {
      type: String,
      required: false,
      default: ''
    },

    superProjectId: {
      type: String | Number,
      required: false,
      default: ''
    },

    superProjectName: {
      type: String | Number,
      required: false,
      default: ''
    },

    superWorkType: {
      type: String | Number,
      required: false,
      default: ''
    },
  },

  data () {
    return {
      dlgShow: this.superDlgShow,

      listQuery: {
        projectId: '',
        projectName: '',
        work: '',
        page: 1,
        size: 20,
      },

      list: [],

      filterText: '',

      defaultOpenList: [], // 默认展开

      selectId: "",

      selectName: "",

      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },

  watch: {
    filterText (val) {
      this.$refs.treeDom.filter(val);
    },

    superDlgShow: {
      immediate: true,
      handler (val) {
        this.dlgShow = val
        if (val) {
          this.$nextTick(() => {
            this.selectId = this.superSelectId
            this.selectName = this.superSelectName
            this.getList()
          })
        }
      }
    },

  },

  created () { },

  methods: {
    // 树节点展开
    handleNodeExpand (data) {
      // 保存当前展开的节点
      let flag = false
      this.defaultOpenList.some((item) => {
        if (item === data.id) {
          // 判断当前节点是否存在， 存在不做处理
          flag = true
          return true
        }
      })
      if (!flag) {
        // 不存在则存到数组里
        this.defaultOpenList.push(data.id)
      }
    },

    // 树节点关闭
    handleNodeCollapse (data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i
        }
      })
    },


    // 重置树状态
    resetTree () {
      this.$nextTick(() => {
        if (utils.isNull(this.selectId)) {
          this.$refs.treeDom.setCurrentKey()
        } else {
          this.$refs.treeDom.setCurrentKey(this.selectId)
        }
      })
    },

    treeNodeClick (data) {
      if (data.type == 0) {
        this.resetTree()
        this.$message({
          type: "warning",
          message: "只能选择区域点位"
        })
        return
      }
      console.log()
      this.selectId = data.id
      this.selectName = data.regionNameList.join('-')
    },

    filterNode (value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    setCheckedKeys () {
      this.$nextTick(() => {
        this.$refs.treeDom.setCurrentKey(this.selectId)
      })
    },

    // 递归
    recursiveList (list) {
      let fn = (list, name) => {
        for (let i of list) {
          if (utils.isNull(i.regionNameList)) {
            i.regionNameList = []
          }
          if (name) {
            for (let j of name) {
              i.regionNameList.push(j)
            }
          }
          i.regionNameList.push(i.name)
          if (i.children && i.children.length > 0) {
            fn(i.children, i.regionNameList)
          }
        }
      }
      fn(list)
    },

    // 获取树列表
    getList () {
      this.list = []
      this.listQuery.projectId = this.superProjectId
      this.listQuery.projectName = this.superProjectName
      this.listQuery.work = this.superWorkType
      loadWorkRegionTree(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data.data))
          this.recursiveList(list)
          this.list = list
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = [list[0].id]
          }
          this.setCheckedKeys()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    subDlg () {
      let currentNode = this.$refs.treeDom.getCurrentNode()
      this.selectId = currentNode.id
      this.selectName = currentNode.regionNameList.join('-')
      let superParam = {
        selectId: this.selectId,
        selectName: this.selectName
      }
      this.$emit('superFunc', superParam)
      this.closeDlg()
    },

    closeDlg () {
      this.$emit('update:superDlgShow', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 660px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

.clear-container {
  height: 100%;
  .tree-container {
    float: left;
    width: 180px;
    height: 100%;
    .el-tree {
      background: #f2f2f2;
      width: 100%;
      height: calc(100% - 26px);
      margin-top: 10px;
      overflow: auto;
      padding-top: 10px;
    }
    /deep/ .el-tree > .el-tree-node {
      display: inline-block;
      min-width: 100%;
    }
  }

  .table-container {
    float: right;
    width: calc(100% - 200px);
    height: 100%;
    .el-table {
      margin-top: 10px;
    }
  }
}
</style>