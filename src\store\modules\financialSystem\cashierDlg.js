// 生成出纳dlg组件

const cashierDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    dlgType: '',

    amount: '', // 金额

    remark: '', // 备注

    title: '', // 标题 付款or回款

    formData: {}
  },

  getters: {
    dlgShow: state => state.dlgShow,

    dlgType: state => state.dlgType,

    amount: state => state.amount,

    remark: state => state.remark,

    title: state => state.title,

    formData: state => state.formData,
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val
    },

    SET_AMOUNT: (state, val) => {
      state.amount = val
    },

    SET_REMARK: (state, val) => {
      state.remark = val
    },

    SET_TITLE: (state, val) => {
      state.title = val
    },

    SET_FORM_DATA: (state, val) => {
      state.formData = val
    },
  },

  actions: {

  }
}

export default cashierDlg
