<template>
  <!-- 流程管理 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="关键字:">
          <el-input @keyup.enter.native="getList" placeholder="请输入关键字" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>

        <el-button icon="el-icon-search" type="success" size="mini" @click="searchFunc">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="showDlg('add')">添加</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class="m-small-table" height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="#" align="center" width="60">
          <template slot-scope="scope">
            {{ (listQuery.page - 1) * listQuery.limit + scope.$index + 1 }}
          </template>
        </el-table-column>

        <!-- <el-table-column label="ID" prop="id" sort-by="name" width="100" sortable="custom" /> -->
        <el-table-column label="ID" prop="id" width="140" />
        <el-table-column label="KEY" prop="tableKey" width="140" />
        <el-table-column label="名称" prop="name" />
        <!-- <el-table-column label="版本" prop="version" width="80" /> -->
        <el-table-column label="创建时间" prop="createTime" width="180" />
        <!-- <el-table-column label="最后修改时间" prop="lastUpdateTimeStr" width="180" /> -->

        <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="pageToEdit(scope.row)" title="编辑">编辑</el-button>

            <!-- <el-button class="ml0" type="text" size="mini" @click="delFunc(scope.row)" title="删除">删除</el-button> -->
            <!-- <el-button class="ml0" type="text" size="mini" @click="exportRow(scope.row)" title="导出">导出</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <!-- 弹窗 新增/编辑 -->
    <addDlg :dlgState0="dlgState" :dlgData0="dlgData" :dlgType="dlgType" :dlgQuery="dlgQuery" @closeDlg="closeDlg" @getList="getList" />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'

import * as utils from '@/utils'
import { postAction, getAction } from '@/api'
import { uploadImg } from '@/utils/uploadImg'
import Pagination from '@/components/Pagination'
import addDlg from './addDlg'

export default {
  components: {
    Pagination,
    addDlg,
  },
  data() {
    return {
      // 弹窗数据
      dlgQuery: {},
      dlgState: false,
      dlgType: '', // 弹框状态add, edit
      dlgData: {},
      // 详情弹窗
      dlgInfoQuery: {},
      dlgInfoState: false,
      dlgInfoType: '', // 弹框状态add, edit
      dlgInfoData: {},

      /////

      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        // label: '',
        page: 1,
        limit: 20,

        // loginType: '', // 查询类型  1 web查询 2公众号用户查询
        // type: '', //  请选择就诊类型：1门诊就诊 2疫苗接种 3健康证
        // state: '', //  0有效  1 已打印  2 超时  全部''
      },
    }
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.searchFunc()
  },

  methods: {
    // 编辑
    pageToEdit(row) {
      let storageProcessData = {
        ...row,
      }
      window.sessionStorage.storageProcessData = JSON.stringify(storageProcessData)
      this.$router.push(`/processMan/processSet`)
    },
    // 部署
    bsFunc(row) {
      this.$confirm('确认部署?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        processDeploy(row.id).then((res0) => {
          let res = res0.data
          if (res.code == 200) {
            this.$message({
              message: res.msg,
              type: 'success',
            })
            this.getList()
          } else {
            this.$message({
              type: 'warning',
              message: res.msg,
            })
          }
        })
      })
    },
    // 导出
    exportRow(row) {
      let hostIp = '*************'
      if (location.host.includes('erp')) {
        hostIp = '************'
      }
      window.open(`http://${hostIp}:8888/export/bpmn?modelId=${row.id}`)
    },

    delFunc(row) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        getAction(`/act/process/del/${row.id}`, sendObj).then((res0) => {
          let res = res0.data
          if (res.code == '200') {
            this.$message({
              message: '删除成功',
              type: 'success',
            })
            this.getList()
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
      })
    },
    ///////
    // << --- 弹窗 ---
    // -- 表单弹窗
    showDlg(type, row) {
      if (type == 'add') {
        this.dlgQuery = { id: 0 }
      } else {
        this.dlgQuery = row
      }
      this.dlgType = type
      this.dlgState = true
    },
    // 关闭弹窗
    closeDlg() {
      this.dlgState = false
    },

    // 详情弹窗
    // -- 表单弹窗
    showInfoDlg(row) {
      this.dlgInfoQuery = row
      this.dlgInfoType = 'info'
      this.dlgInfoState = true
    },
    // 关闭弹窗
    closeInfoDlg() {
      this.dlgInfoState = false
    },
    // >> --- 弹窗 ---
    //////
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // << 列表
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    // 获取数据
    getList() {
      let sendObj = JSON.parse(JSON.stringify(this.listQuery))
      // if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
      //   sendObj.startDate = sendObj.dateRange[0]
      //   sendObj.endDate = sendObj.dateRange[1]
      // }
      // delete sendObj.dateRange

      this.listLoading = true

      postAction('/act/table/page', sendObj).then((res0) => {
        this.listLoading = false
        let res = res0.data
        if (res.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data))
          this.total = res.page.total ? res.page.total : 0
          // this.formatList()
        } else {
          this.list = []
          this.total = 0
          this.$message.error(res.msg)
        }
      })
    },
    // >> 列表

    // 弹窗提交
    subDlg() {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.contentJson = JSON.stringify(postParam.contentJson)
          postParam.projectId = this.userInfo.projectId
          postParam.creator = Cookie.get('userId')
          postParam.createName = Cookie.get('userName')
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          this.dlgLoading = true

          postAction('/aaa/bbb', postParam).then((res) => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 启用停用
    delItem(data, flag) {
      let title = '确认删除?'

      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        getAction(`/aaa/bbb/${data.id}`).then((res0) => {
          let res = res0.data
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


