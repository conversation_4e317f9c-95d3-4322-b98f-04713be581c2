import request from '@/utils/request'

// 分页 
export function page (data) {
  return request({
    url: `/nbiot/point/page`,
    method: 'post',
    data
  })
}

// 新增
export function saveOrUpdate (data) {
  return request({
    url: `/nbiot/point/saveOrU`,
    method: 'post',
    data
  })
}


// 删除
export function del (id) {
  return request({
    url: `/nbiot/point/del/${id}`,
    method: 'get'
  })
}

// 历史记录
export function reportLogPage (data) {
  return request({
    url: `/nbiot/point/reportLogPage`,
    method: 'post',
    data
  })
}

// 区域温度统计
export function tempCountPage (data) {
  return request({
    url: `/nbiot/point/tempCountPage`,
    method: 'post',
    data
  })
}

// 导出
export function tempCountExport (data) {
  return request({
    url: `/nbiot/point/tempCountExport`,
    method: 'post',
    data
  })
}

// 趋势
export function tempAnalys (data) {
  return request({
    url: `/nbiot/point/tempAnalys/v2`,
    method: 'post',
    data
  })
}
