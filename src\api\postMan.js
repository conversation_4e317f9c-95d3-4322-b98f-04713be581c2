/**
 * desc 人力资源系统 - 岗位相关
 * by 马振国
 */
import request from '@/utils/request'
// 模糊查询岗位  branchId, str, page, size
export function findPostLike(data) {
  return request({
    url: `/sys/findPostLike`,
    method: 'post',
    data
  })
}

// 获取部门 下 岗位
export function findOrgBranchPost({brId, page, size}) {
  return request({
    url: `/sys/findOrgBranchPost/${brId}/${page}/${size}`,
    method: 'get'
  })
}

export function findOrgBranchPostByCondition(data) {
  return request({
    url: `/sys/findOrgBranchPostByCondition`,
    method: 'post',
    data
  })
}

// 获取岗位列表
// page, size, label 岗位名称，postJob 岗位职务值，branchId 部门id
export function findPostByDynamic(data) {
  return request({
    url: `/sys/findPostByDynamic`,
    method: 'post',
    data
  })
}
export function pagePostUser(data) {
  return request({
    url: `/sys/pagePostUser`,
    method: 'post',
    data
  })
}

// 获取所有岗位
export function findOrgPostAll({page, size}) {
  return request({
    url: `/sys/findOrgPostAll/${page}/${size}`,
    method: 'get'
  })
}

// 新增岗位
export function saveOrgPost(data) {
  return request({
    url: `/sys/saveOrgPost`,
    method: 'post',
    data
  })
}

// 修改岗位 
export function upDateOrgPost(data) {
  return request({
    url: `/sys/upDateOrgPost`,
    method: 'post',
    data
  })
}

// 删除岗位
export function delOrgPost(id) {
  return request({
    url: `/sys/delOrgPost/${id}`,
    method: 'get'
  })
}

// 员工兼岗信息
export function findConPostByUserId(userId) {
  return request({
    url: `/sys/findConPostByUserId/${userId}`,
    method: 'get'
  })
}

// 岗位调薪单，选择岗位后，列出岗位的人
export function findPostSalaryInf(postIds) {
  return request({
    url: `/sys/findPostSalaryInf/${postIds}`,
    method: 'get'
  })
}

// 获取权限下拉框
export function findSysRoleAll() {
  return request({
    url: `/sys/findSysRoleAll/1/9999`,
    method: 'get'
  })
}

// 设置权限{ postId, upBranchIds, postName, branchIds }
export function postSetDept(data) {
  return request({
    url: `/sys/postSetDept`,
    method: 'post',
    data
  })
}

// 设置考勤范围
export function findBranchPunch(data) {
  return request({
    url: `/sys/findBranchPunch`,
    method: 'post',
    data
  })
}
export function saveBranchPunch(data) {
  return request({
    url: `/sys/saveBranchPunch`,
    method: 'post',
    data
  })
}

///////// [[ 工龄工资
// 获取可以设置工龄工资的列表
export function findPostSeniorityWageList(data) {
  return request({
    url: `/sys/findPostSeniorityWageList`,
    method: 'post',
    data
  })
}

// 设置工龄工资接口  postId, postName, datas
export function addPostSeniorityWage(data) {
  return request({
    url: `/sys/addPostSeniorityWage`,
    method: 'post',
    data
  })
}

// 按岗位获取工龄工资详情
export function listPostSeniorityWage(id) {
  return request({
    url: `/sys/listPostSeniorityWage/${id}`,
    method: 'get'
  })
}
///////// ]] 工龄工资

///////// [[ 设置年假
// 获取设置年假 岗位 列表
export function findPostAnnualLeaveList(data) {
  return request({
    url: `/sys/findPostAnnualLeaveList`,
    method: 'post',
    data
  })
}

// 设置工龄工资接口  postId, postName, datas
export function adeAnnualLeavePostAdd(data) {
  return request({
    url: `/sys/adeAnnualLeavePostAdd`,
    method: 'post',
    data
  })
}

// 按岗位获取工龄工资详情
export function listAdeAnnualLeavePost(id) {
  return request({
    url: `/sys/listAdeAnnualLeavePost/${id}`,
    method: 'get'
  })
}
///////// ]] 设置年假


