/** 报事系统 **/
import Layout from "@/views/layout/Layout";

const reportManRouter = {
  path: "/reportMan",
  component: Layout,
  name: "reportMan",
  meta: {
    title: "报事管理",
    icon: "dash-kqjg",
    roles: ["baoshiguanli_web"]
  },
  alwaysShow: true,
  children: [
    {
      path: "reportConfig",
      component: () => import("@/views/reportMan/reportConfig/index"),
      meta: {
        title: "报事设置",
        roles: ["baoshishezhi_web"]
      },
      alwaysShow: true,
      children: [
        // {
        //   path: "reportArea",
        //   component: () => import("@/views/reportMan/reportConfig/reportArea"),
        //   name: "报事区域",
        //   meta: {
        //     title: "报事区域"
        //     // roles: ["aaaa"]
        //   }
        // },
        // {
        //   path: "reportSubjectModel",
        //   component: () =>
        //     import("@/views/reportMan/reportConfig/reportSubjectModel"),
        //   name: "报事科目设置",
        //   meta: {
        //     title: "报事科目设置"
        //     // roles: ["aaaa"]
        //   }
        // },
        // {
        //   path: "reportSubject",
        //   component: () =>
        //     import("@/views/reportMan/reportConfig/reportSubject"),
        //   name: "项目报事科目",
        //   meta: {
        //     title: "项目报事科目"
        //     // roles: ["aaaa"]
        //   }
        // },
        {
          path: "dispatchConfig",
          component: () =>
            import("@/views/reportMan/reportConfig/dispatchConfig"),
          name: "派工设置",
          meta: {
            title: "派工设置",
            roles: ["paigongshezhi_web"]
          }
        },
        {
          path: "workOrderStep",
          component: () =>
            import("@/views/reportMan/reportConfig/workOrderStep"),
          name: "工单步骤",
          meta: {
            title: "工单步骤",
            roles: ["gongdanbuzhou_web"]
          }
        }
      ]
    },
    {
      path: "reportManagement",
      component: () => import("@/views/reportMan/reportManagement/index"),
      name: "报事管理",
      meta: {
        title: "报事管理",
        roles: ["baoshiguanli2_web"]
      }
    },
    {
      path: "reportSupervise",
      component: () => import("@/views/reportMan/reportSupervise/index"),
      name: "报事监管",
      meta: {
        title: "报事监管",
        roles: ["baoshijianguan_web"]
      }
    },
    {
      path: "reportStatistics",
      component: () => import("@/views/reportMan/reportSupervise/index"),
      name: "报事统计",
      meta: {
        title: "报事统计",
        roles: ["baoshitongji_web"]
      }
    },
    //品质7月改
    // {
    //   path: "reportSuperviseNew",
    //   component: () => import("@/views/reportMan/reportSuperviseNew/index"),
    //   name: "报事统计",
    //   meta: {
    //     title: "报事统计",
    //     roles: ["baoshitongji_old"]
    //   }
    // },

    {
      path: "statisticalAnalysis",
      component: () => import("@/views/reportMan/statisticalAnalysis"),
      name: "统计分析",
      meta: {
        title: "统计分析",
        roles: ["baoshifenxi_web"]
      }
    },
    //品质7月改
    // {
    //   path: "statisticalAnalysisNew",
    //   component: () => import("@/views/reportMan/statisticalAnalysisNew"),
    //   name: "统计分析",
    //   meta: {
    //     title: "统计分析",
    //     roles: ["baoshifenxi_old"]
    //   }
    // }
    {
      path: "orderRecords",
      component: () => import("@/views/reportMan/orderRecords"),
      name: "接单提示记录",
      meta: {
        title: "接单提示记录",
        roles: ["jiedantishijilu_web"]
      }
    }
  ]
};

export default reportManRouter;
