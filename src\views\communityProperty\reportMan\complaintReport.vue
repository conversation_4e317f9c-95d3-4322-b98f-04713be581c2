<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>

        <el-form-item label="选择小区" prop="communityId">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="投诉类型">
          <template slot-scope="scope">
            <span>{{ scope.row.valuateTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="房屋">
          <template slot-scope="scope">
            <div>小区:{{ scope.row.communityName }}</div>
            <div>房屋:{{ scope.row.fangwu }}</div>
          </template>
        </el-table-column>

        <el-table-column label="投诉人">
          <template slot-scope="scope">
            <span>{{ scope.row.valuateUserName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="投诉人电话">
          <template slot-scope="scope">
            <span>{{ scope.row.valuatePhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="投诉状态" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 0" type="danger">未处理</el-tag>
            <el-tag v-else type="success">已处理</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="处理人">
          <template slot-scope="scope">
            <span>{{ scope.row.genzongName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="处理人电话">
          <template slot-scope="scope">
            <span>{{ scope.row.genzongPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="查看投诉" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :disabled="dlgType === 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <template v-if="dlgType === 'VIEW'">

          <el-form-item label="归属小区" prop="communityId">
            <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
              <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属楼栋">
            <el-select v-model="dlgData.loudongId" filterable clearable placeholder="请选择楼栋" @change="floorChange">
              <el-option v-for="item in buildingList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属单元">
            <el-select v-model="dlgData.danyuanId" filterable clearable placeholder="请选择单元" @change="unitChange">
              <el-option v-for="item in unitList" :key="item.id" :label="item.unitName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="归属房屋">
            <el-select v-model="dlgData.fangwuId" filterable clearable placeholder="请选择房屋">
              <el-option v-for="item in roomList" :key="item.id" :label="item.roomFullName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="投诉类型" prop="valuateType">
            <el-radio-group v-model="dlgData.valuateType">
              <el-radio v-for="item in typeList" :key="item.id" :label="item.id">{{item.name}}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="投诉人" prop="valuateUserName">
            <el-input v-model="dlgData.valuateUserName" placeholder="请填写投诉人姓名"></el-input>
          </el-form-item>

          <el-form-item label="投诉人电话" prop="valuatePhone">
            <el-input v-model="dlgData.valuatePhone" placeholder="请填写投诉人电话"></el-input>
          </el-form-item>

          <el-form-item label="投诉内容" prop="valuateContent">
            <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.valuateContent" placeholder="请输入投诉内容" />
          </el-form-item>

          <el-form-item label="处理人" prop="genzongName">
            <el-input v-model="dlgData.genzongName" placeholder="请填写处理人姓名"></el-input>
          </el-form-item>

          <el-form-item label="处理人电话" prop="genzongPhone">
            <el-input v-model="dlgData.genzongPhone" placeholder="请填写处理人电话"></el-input>
          </el-form-item>
        </template>

        <el-form-item label="处理内容" prop="valuateGenzong">
          <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.valuateGenzong" placeholder="请输入处理内容" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, cofloorCommunity, buildingunitFloor, buildingroomPage } from '@/api/communityMan'

import {
  membercomplaintPage,
  membercomplaintHandle,
} from '@/api/complaintMan'

import {
  formComplaint,
} from '@/api/reportMan'

import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: 0,
  communityId: '',
  communityName: '',
  danyuan: '',
  danyuanId: '',
  fangwu: '',
  fangwuId: '',
  loudong: '',
  loudongId: '',
  valuateContent: '',
  valuateUserName: '',
  valuatePhone: '',
  valuateType: '',
  valuateUserId: '',
  valuateUserName: '',
  valuateGenzong: '',
  type: '1',
}


export default {
  name: 'complaintReg',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {

      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - (1000 * 60 * 60 * 24);
        },
      },
      // 弹窗 类型
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        danyuanId: [{ required: true, message: '必填字段', trigger: 'change' }],
        fangwuId: [{ required: true, message: '必填字段', trigger: 'change' }],
        loudongId: [{ required: true, message: '必填字段', trigger: 'change' }],
        valuateUserName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        valuateType: [{ required: true, message: '必填字段', trigger: 'change' }],
        valuateContent: [{ required: true, message: '必填字段', trigger: 'blur' }],
        valuateGenzong: [{ required: true, message: '必填字段', trigger: 'blur' }],
        valuatePhone: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: '号码格式有误！',
            trigger: 'blur'
          }
        ],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        rangeDate: '',
        status: '1',
        valuateType: '',
        logType: '1',
        type: 1
      },
      userInfo: {},
      communityList: [],
      buildingList: [],
      unitList: [],
      roomList: [],
      typeList: [
        {
          id: 1,
          name: '投诉'
        },
        {
          id: 2,
          name: '建议'
        },
      ],
    }
  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {

    communityChange () {
      let communityId = this.dlgData.communityId
      this.dlgData.loudongId = ""
      this.dlgData.danyuanId = ""
      this.dlgData.repairType = ""
      this.getBuildingList(communityId)
      this.getRoomList()
    },

    floorChange () {
      let floorId = this.dlgData.loudongId
      this.dlgData.danyuanId = ""
      this.getUnitList(floorId)
      this.getRoomList()
    },

    unitChange () {
      this.getRoomList()
    },


    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取楼栋列表
    getBuildingList (id) {
      if (utils.isNull(id)) {
        return
      }
      cofloorCommunity(id).then(res => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data
        }
      })
    },

    // 获取单元列表
    getUnitList (id) {
      if (utils.isNull(id)) {
        return
      }
      buildingunitFloor(id).then(res => {
        if (res.data.code == 200) {
          this.unitList = res.data.data
        }
      })
    },

    // 获取房屋列表
    getRoomList () {
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.dlgData.communityId,
        floorId: this.dlgData.loudongId,
        unitId: this.dlgData.danyuanId
      }
      buildingroomPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.roomList = res.data.data
        }
      })
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    formatList () {
      for (let i of this.list) {
        i.valuateTypeText = utils.getNameById(i.valuateType, this.typeList)
      }
    },

    // 获取数据
    getList () {
      if (utils.isNull(this.listQuery.communityId)) {
        this.$message.warning("请选择小区")
        return
      }
      this.count++
      this.listLoading = true
      formComplaint(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.formatList()
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = {
            genzongId: Cookie.get('userId'),
            genzongName: Cookie.get('userName'),
            genzongPhone: this.userInfo.account,
            id: this.dlgData.id,
            valuateGenzong: this.dlgData.valuateGenzong,
          }
          this.dlgLoading = true
          membercomplaintHandle(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgData.loudongId = this.dlgData.loudongId === 0 ? '' : this.dlgData.loudongId
      this.dlgData.danyuanId = this.dlgData.danyuanId === 0 ? '' : this.dlgData.danyuanId
      this.dlgData.fangwuId = this.dlgData.fangwuId === 0 ? '' : this.dlgData.fangwuId
      this.dlgType = type
      this.dlgShow = true
      this.getBuildingList(this.dlgData.communityId)
      this.getUnitList(this.dlgData.loudongId)
      this.getRoomList()
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/ownerInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


