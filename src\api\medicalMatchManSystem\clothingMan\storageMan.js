import request from '@/utils/request'

/*
 * 库房管理
 */

// 新增/修改库房信息接口
export function saveOrUStorage(data) {
  return request({
    url: `/cloth/saveOrUStorage`,
    method: 'post',
    data
  })
}

// 修改库房信息状态（删除库房信息） * 后期需要增加验证 科室库房库存大于0时 不允许删除
export function updateStorage(data) {
  return request({
    url: `/cloth/updateStorage`,
    method: 'post',
    data
  })
}

// 动态查询库房信息 分页
export function findStorageDynamic(data) {
  return request({
    url: `/cloth/findStorageDynamic`,
    method: 'post',
    data
  })
}
