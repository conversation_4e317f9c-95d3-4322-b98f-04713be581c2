// 考勤组相关接口
import request from '@/utils/request'

// 【【 1 节假日设置
// 获取节假日列表
export function findAdeHolidayAll({page, size}) {
  return request({
    url: `/ade/findAdeHolidayAll/${page}/${size}`,
    method: 'get'
  })
}

// 新增节假日  
export function saveAdeHoliday(data) {
  return request({
    url: '/ade/saveAdeHoliday',
    method: 'post',
    data
  })
}

// 修改节假日  
export function upDateAdeHoliday(data) {
  return request({
    url: '/ade/upDateAdeHoliday',
    method: 'post',
    data
  })
}

// 删除节假日
export function deleteAdeHolidayById(id) {
  return request({
    url: `/ade/deleteAdeHolidayById/${id}`,
    method: 'get'
  })
}

// 节假日获取绑定的岗位
export function findHolidayPostList(data) {
  return request({
    url: `/ade/findHolidayPostList`,
    method: 'post',
    data
  })
}
// 节假日 绑定岗位
export function saveOrUpdateHolidayPost(data) {
  return request({
    url: `/ade/saveOrUpdateHolidayPost`,
    method: 'post',
    data
  })
}

// 】】 1 节假日设置
// 【【 2 考勤组管理
// 动态查询考勤组
export function findGroupByDynamic(data) {
  return request({
    url: `/ade/findGroupByDynamic`,
    method: 'post',
    data
  })
}
// 获取考勤组列表
export function findgroupAll({page, size}) {
  return request({
    url: `/ade/findgroupAll/${page}/${size}`,
    method: 'get'
  })
}
// 新增考勤组
export function saveAdeGroup(data) {
  return request({
    url: '/ade/saveAdeGroup',
    method: 'post',
    data
  })
}
// 修改考勤组
export function updateAdeGroup(data) {
  return request({
    url: '/ade/updateAdeGroup',
    method: 'post',
    data
  })
}
// 删除考勤组
export function delgroupById(id) {
  return request({
    url: `/ade/delgroupById/${id}`,
    method: 'get'
  })
}

// 绑定考勤组
export function updateGroupPosts(data) {
  return request({
    url: '/ade/updateGroupPosts',
    method: 'post',
    data
  })
}
// 】】 2 考勤组管理

// 【【 3考勤组分类
// 获取列表
export function findGroupTypeByDynamic(data) {
  return request({
    url: `/ade/findGroupTypeByDynamic`,
    method: 'post',
    data
  })
}
// 新增分类
export function saveGroupType(data) {
  return request({
    url: '/ade/saveGroupType',
    method: 'post',
    data
  })
}

// 更新分类
export function updateGroupTypeById(data) {
  return request({
    url: '/ade/updateGroupTypeById',
    method: 'post',
    data
  })
}

// 删除分类
export function delGroupTypeById(id) {
  return request({
    url: `/ade/delGroupTypeById/${id}`,
    method: 'get'
  })
}

// 】】 3考勤组分类

// 4 [[ 值班组 管理
// 获取列表
export function findZbzList({page, size}) {
  return request({
    url: `/ade/og/findgroupAll/${page}/${size}`,
    method: 'get'
  })
}

// 查询详情
export function findOndutyGroupInfo(id) {
  return request({
    url: `/ade/og/findOndutyGroupInfo/${id}`,
    method: 'get'
  })
}

// 新增值班组
export function saveOndutyGroup(data) {
  return request({
    url: '/ade/og/saveOndutyGroup',
    method: 'post',
    data
  })
}

// 修改
export function updateOndutyGroup(data) {
  return request({
    url: '/ade/og/updateOndutyGroup',
    method: 'post',
    data
  })
}

// 获取值班组排班数据 yM(月份)，groupId（值班组id）
export function ondutyScheduling(data) {
  return request({
    url: '/ade/ondutyScheduling/page',
    method: 'post',
    data
  })
}

// 获取值班组 下的员工  groupId
export function getUsersByGroupId(data) {
  return request({
    url: '/ade/getUsersByGroupId',
    method: 'post',
    data
  })
}

// 对 值班组，对应日期，进行排班  date,groupId,ruleId,postIds
export function buildOndutyAdeScheduling(data) {
  return request({
    url: '/ade/buildOndutyAdeScheduling',
    method: 'post',
    data
  })
}

// 删除值班组
export function delOndutyGroup(id) {
  return request({
    url: `/ade/og/delOndutyGroup/${id}`,
    method: 'get'
  })
}

// 获取用户，负责的值班组
export function findUserGroup(id) {
  return request({
    url: `/ade/og/findUserGroup`,
    method: 'post'
  })
}

// 4 ]] 值班组 管理