<template>
  <div class="basic-table">
    <el-table :data="tableData" :height="tableHeight" :row-style="{ height: `${rowHeight}px` }" border ref="tableBar"
      class="m-small-table" v-loading="listLoading" :key="tableKey" fit highlight-current-row
      style="width: 100%; height: auto">
      <el-table-column v-for="(item, index) in TableHeaderList" :key="index" :prop="item.Field"
        :label="item.FieldDisplayName" width="auto" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="item.FieldDisplayName === '检修事项'">
            <div v-if="scope.row.checkItem && scope.row.checkItem.length == 0"></div>
            <div v-else>
              <span v-for="(item1, index1) of scope.row.checkItem" :key="index1">
                <span :title="item1.name">{{ item1.name }}</span>
                <span v-if="index1 != scope.row.checkItem.length - 1">,</span>
              </span>
            </div>
          </span>

          <span v-else-if="item.FieldDisplayName === '保养事项'">
            <div v-if="scope.row.maintenanceItem && scope.row.maintenanceItem.length == 0"></div>
            <div v-else>
              <span v-for="(item2, index2) of scope.row.maintenanceItem" :key="index2">
                <span :title="item2.name">{{ item2.name }}</span>
                <span v-if="index2 != scope.row.maintenanceItem.length - 1">,</span>
              </span>
            </div>
          </span>

          <span v-else>
            <span :title="scope.row[item.Field]">{{
              scope.row[item.Field]
            }}</span>
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" width="auto" align="center">
        <template slot-scope="scope">
          <el-button
            @click="showDlg('info', scope.row)"
            icon="el-icon-document"
            size="mini"
            type="success"
            title="详情"
            plain
            >详情</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>

    <pagination v-if="hasPagination" class="mt10" :total="total" :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize" @pagination="getList" />
    <div class="clear"></div>
  </div>
</template>

<script>
export default {
  name: "BasicTable",
  props: {
    TableHeaderList: {
      type: Array,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    seriesName: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
    total: {
      type: Number,
      default: 0,
    },
    hasPagination: {
      type: Boolean,
      default: true,
    },
    tableHeight: {
      type: Number,
      default: 300,
    },
    rowHeight: {
      type: Number,
      default: 40,
    },
    searchForm: {
      type: Object,
      default() {
        return {
          pageNo: 1,
          pageSize: 10,
        };
      },
    },

  },
  data() {
    return {
      listLoading: false,
      tableKey: 1,
    };
  },
  methods: {
    getList() {
      if (this.seriesName === "所用人工") {
        this.$emit("getRenYuanTableData", this.searchForm);
      } else if (this.seriesName === "维修次数" || this.seriesName === "关联维修次数") {
        this.$emit("getWeiXiuTableData", this.searchForm);
      } else if (this.seriesName === "检修次数"  || this.seriesName === "实际检修次数") {
        this.$emit("getJianXiuTableData", this.searchForm);
      } else if (this.seriesName === "保养次数" || this.seriesName === "实际保养次数") {
        this.$emit("getBaoYangTableData", this.searchForm);
      }
    },
  },
};
</script>

<style scoped>
.basic-table {
  width: 100%;
}
</style>