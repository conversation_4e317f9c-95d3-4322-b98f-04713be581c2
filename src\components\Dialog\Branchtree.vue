<template>
  <!-- 部门 复选框 -->
  <div class="">
    <el-dialog :close-on-click-modal='false' title="选择部门" :visible.sync="branchTreeState" width='600px' top='30px' append-to-body>
      <div class="">
        <!-- <el-input
          placeholder="输入关键字进行过滤"
          style="margin-bottom: 10px;"
          v-model="filterBmLeftText">
        </el-input> -->
        <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
        <div class='m-dialog-h'>
          <el-tree :data="treeData" :check-strictly='true' @check='treeCheck' show-checkbox default-expand-all ref="tree" node-key='id' highlight-current :default-checked-keys='defaultSelectKey' :props="defaultProps">
          </el-tree>
          <!-- <el-tree 
            :data="treeData" 
            ref="treeDom"
            default-expand-all
            :filter-node-method="filterNode"
            @node-click="nodeClick">
          </el-tree> -->
        </div>

      </div>
      <div slot="footer" class="dialog-footer">
        <!-- <span class='dialog-footer-span' v-show='branchName'>当前选中：{{ branchName }}</span> -->
        <el-button @click="closeDialog" icon='el-icon-back'>返回</el-button>

        <el-button type="danger" @click="removeNode" icon="el-icon-delete">清空</el-button>
        <el-button :loading="btnLoading" type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findTreeByFrom, findOrgBranchAll } from '@/api/dataDic'
import { setTimeout } from 'timers';
// import adminDashboard from './admin'
// import editorDashboard from './editor'

export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {

      // 部门树
      treeData: [],
      selectKeys: [],  // 选中的节点id集合
      selectNames: [],  // 选中的节点 Name
      checkChildIds: [],  // 选中当前节点下的 id 集合
      childNode: {},  // 选中的当前节点

      btnLoading: false,
      isFirst: false,

      // 树过滤
      defaultProps: {
        children: 'children',
        label: 'label',
      },

      // 返回的 id + name
      backIdName: [],

      // 默认数据
      defaultSelectKey: [],

    }
  },
  computed: {
    ...mapGetters([
      'branchTreeKeysSet',
      'branchTreeIsSelectChild'
    ]),
    branchTreeState: {
      get: function () {
        let state = this.$store.getters.branchTreeState
        if (state) {

          setTimeout(() => {
            this.removeNode()
            // 是否根据权限
            let branchTreeIsRoot = this.$store.getters.branchTreeIsRoot
            if (branchTreeIsRoot) {
              findOrgBranchAll().then(res => {
                this.selectKeys = []
                let code = res.data.code
                let data = res.data.data
                let msg = res.data.msg

                if (code === '200') {
                  this.treeData = JSON.parse(JSON.stringify(res.data.list))

                  if (this.$store.getters.branchTreeKeysSet) {
                    setTimeout(() => {
                      this.selectKeys = this.$store.getters.branchTreeKeysSet.split(',')
                      this.defaultSelectKey = this.$store.getters.branchTreeKeysSet.split(',')

                      let names = this.$store.getters.branchTreeNamesSet.split(',')
                      let selectNames = []
                      for (let i = 0; i < names.length; i++) {
                        let obj = {
                          id: this.selectKeys[i],
                          label: names[i]
                        }
                        selectNames.push(obj)
                      }

                      this.selectNames = JSON.parse(JSON.stringify(selectNames))
                    }, 50)

                  }

                } else {
                  this.$message.error(msg)
                }
              })
            }
            else {
              findTreeByFrom().then(res => {

                let code = res.data.code
                let data = res.data.data
                let msg = res.data.msg
                if (code === '200') {
                  this.treeData = JSON.parse(JSON.stringify(res.data.list))
                  if (this.$store.getters.branchTreeKeysSet) {
                    setTimeout(() => {
                      this.selectKeys = this.$store.getters.branchTreeKeysSet.split(',')
                      this.defaultSelectKey = this.$store.getters.branchTreeKeysSet.split(',')

                      let names = this.$store.getters.branchTreeNamesSet.split(',')
                      let selectNames = []
                      for (let i = 0; i < names.length; i++) {
                        let obj = {
                          id: this.selectKeys[i],
                          label: names[i]
                        }
                        selectNames.push(obj)
                      }

                      this.selectNames = JSON.parse(JSON.stringify(selectNames))

                    }, 50)
                  }

                } else {
                  this.$message.error(msg)
                }
              })
            }
          }, 50)
        }

        return state
      },
      set: function (newVal) {
        this.$store.commit('SET_BRANCHTREESTATE', newVal)
      }
    },

    branchTreekeys: {
      get: function () {
        let branchTreekeys = JSON.parse(this.$store.getters.branchTreekeys)
        this.$refs.tree.setCheckedKeys(branchTreekeys)
        return branchTreekeys
      },
      set: function (newVal) {

      }
    },
    branchTreeIsRoot: {
      get: function () {
        return branchTreeIsRoot
      },
      set: function (newVal) {

      }
    }
  },
  watch: {

  },
  created() {
    // 部门
    // this.findTreeByFrom()
  },
  methods: {
    // 【【 节点相关
    // 清空选中节点
    removeNode() {
      this.selectKeys = []
      this.selectNames = []
      this.$refs.tree.setCheckedKeys([]);
    },
    // 节点选中触发事件
    treeCheck(checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys) {
      // console.log('checkedNodes', checkedNodes)
      // console.log('checkedKeys', checkedKeys)
      // console.log('halfCheckedNodes', halfCheckedNodes)
      // console.log('halfCheckedKeys', halfCheckedKeys)

      // checkedNodes 当前选中的节点数据
      // checkedKeys.checkedKeys 选中的 id 数组

      // 减少，增加
      // console.log(checkedKeys.checkedKeys.length,  this.selectKeys.length)


      if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectNames = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        // select-全选；remove-取消全选
        this.selectAllNode(checkedNodes.children, 'select')
      } else {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectNames = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        this.selectAllNode(checkedNodes.children, 'remove')
      }
      setTimeout(() => {
        this.$refs.tree.setCheckedKeys(this.selectKeys);
      }, 50)


      setTimeout(() => {
        console.log(this.selectNames)
      }, 500)

    },

    // 递归全选当前下的节点
    selectAllNode(childrenArr, type) {

      for (let item of childrenArr) {
        // 全选，全部取消
        if (type == 'select') {
          if (!this.selectKeys.includes(item.id)) {
            this.selectKeys.push(item.id)
            this.selectNames.push(item)
          }
        } else {
          if (this.selectKeys.includes(item.id)) {
            let mIndex = this.selectKeys.indexOf(item.id)

            this.selectKeys.splice(mIndex, 1)
            this.selectNames.splice(mIndex, 1)
          }
        }
        if (item.children) {
          this.selectAllNode(item.children, type)
        }
      }
    },

    // 【【 弹窗按钮
    // 提交
    bumenOkFunc() {
      // this.btnLoading = true
      this.$store.commit('SET_BRANCHTREEKEYS', 'empty')
      this.$store.commit('SET_BRANCHTREENAMES', 'empty')
      this.$store.commit('SET_BRANCHTREEHALFKEYS', '')

      setTimeout(() => {
        let names = []
        for (let item of this.selectNames) {
          names.push(item.label)
        }
        this.$store.commit('SET_BRANCHTREEKEYS', this.selectKeys.join(','))
        this.$store.commit('SET_BRANCHTREENAMES', names.join(','))
        this.$store.commit('SET_BRANCHTREEHALFKEYS', JSON.stringify(this.$refs.tree.getHalfCheckedKeys()))
        this.$refs.tree.setCheckedKeys([])

        this.closeDialog()
      }, 100)
    },

    // 关闭弹窗 
    closeDialog() {
      this.isFirst = true
      this.$store.commit('SET_BRANCHTREESTATE', false)

    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.dialog-footer-span {
  font-size: 14px;
  color: #666;
  display: inline-block;
  padding-right: 10px;
}
</style>