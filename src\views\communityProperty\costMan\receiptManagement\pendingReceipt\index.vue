<template>
    <div class="app-container">
        <div class="filter-container">
            <el-form inline :model="listQuery" @submit.native.prevent>
                <el-form-item label="类型：">
                    <el-select v-model="listQuery.payType" filterable placeholder="请选择类型" @change="payTypeChange">
                        <el-option v-for="item in payTypeList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label=islabel prop="">
                    <el-input style="width: 200px" v-model="backName" placeholder="选择房屋/车位/车库" readonly @focus="showDlg">
                    </el-input>
                </el-form-item>
                <el-form-item label="缴费日期">
                    <el-date-picker style="width: 220px" v-model="listQuery.dateRange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="截止日期">
                    </el-date-picker>
                </el-form-item>
                <el-button icon="el-icon-search" type="success" size="mini" @click="getList">查询</el-button>
                <el-button icon="el-icon-document-copy" type="success" size="mini" @click="batchFunc">批量打印</el-button>
            </el-form>
        </div>
        <div class="table-container">
            <el-table class='m-small-table' ref="multipleTable" @select="listDataChange" @select-all="tableSelectAll"
                height="100%" v-loading="listLoading" :data="list" show-overflow-tooltip border fit highlight-current-row
                :empty-text="count == 0 ? '请搜索' : '暂无数据'">
                <el-table-column type="selection" width="55"> </el-table-column>

                <el-table-column label="费用类型">
                    <template slot-scope="scope">
                        <span>{{ scope.row.feeName }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="费用项目">
                    <template slot-scope="scope">
                        <span>{{ scope.row.configName }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="费用标识">
                    <template slot-scope="scope">
                        <span>{{ scope.row.feeFlagName }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="实收金额">
                    <template slot-scope="scope">
                        <span>{{ num2Round(scope.row.receivedAmount) }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="缴费日期">
                    <template slot-scope="scope">
                        <span>{{ scope.row.payTime }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="计费开始时间">
                    <template slot-scope="scope">
                        <span>{{ scope.row.startDate }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="计费结束时间">
                    <template slot-scope="scope">
                        <span>{{ scope.row.endDate }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="说明">
                    <template slot-scope="scope">
                        <span>{{ scope.row.remark }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="操作" align="center" width="130" class-name="small-padding fixed-width">
                    <template slot-scope="scope">
                        <el-button type="primary" size="mini" icon="el-icon-printer" plain
                            @click="printingFun(scope.row)">打印收据</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="page-container">
            <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
        </div>
        <typeDlg :dlgState0="dlgState" :dlgType="dlgType" @closeDlg="closeDlg" @backFunc="backFunc" />
    </div>
</template>
<script>
import { findPayBillSumPage } from '@/api/costMan'
import typeDlg from './typeDlg.vue'
import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import{getAction, postAction} from "@/api"
export default {
    components: {
        typeDlg,
        Pagination
    },
    data() {
        return {
            dlgState: false,
            dlgType: '', // 弹框状态1-房屋 2-车位 3-车库
            backName: '',//选择的名字
            islabel: "选择房屋",
            payTypeList: [
                {
                    id: "1",
                    name: "房屋"
                },
                {
                    id: "2",
                    name: "车位"
                },
                {
                    id: "3",
                    name: "车库"
                }
            ],
            listQuery: {
                feeType: '',
                garageId: '',
                parkingId: '',
                roomId: '',
                page: 1,
                limit: 20,
                label: "",
                payType: "1",
                dateRange: [],
                startDate: '', //		body	false	string
                endDate: '', //		body	false	string
                isPrint: 0,
                status: "1"
            },
            list: [],
            total: 0,
            count: 0,
            listLoading: false,
            queryObj: '',
            feeFlagList: [
                {
                    id: '1',
                    name: '周期性费用',
                },
                {
                    id: '2',
                    name: '一次性费用',
                },
            ],
            costTypeList: [],//费用类型字典
            returnList: []
        };
    },
    created() {
        utils.getDataDict(this, 'costType', 'costTypeList')
        // this.getList();
        // this.userInfo = JSON.parse(window.localStorage.userInfo)
    },
    methods: {
        batchFunc(){
            if (this.returnList.length == 0) {
                this.$message.warning('请选择数据')
                return false
            }
            let arr = []
            for (let item of this.returnList) {
                arr.push(item.id)
            }
            this.printAjax(arr.join(','))
        },
        printingFun(row){
            this.printAjax(row.id+'')
        },
        printAjax(sumIds){
            let type = ''
            if (this.listQuery.payType == 1) type = 'room'
            if (this.listQuery.payType == 2) type = 'parking'
            if (this.listQuery.payType == 3) type = 'garage'

            let yzId = ''
            let yzName = ''
            let roomId = ''
            let roomName = ''
            if (this.listQuery.payType == 1) {
                yzId = this.queryObj.memberId
                yzName = this.queryObj.memberName

                roomId = this.queryObj.id
                roomName = this.queryObj.roomFullName
            } else  {
                yzId = this.queryObj.ownerId
                yzName = this.queryObj.ownerName

                roomId = this.queryObj.id
                roomName = this.queryObj.numStr
            }
            let sendObj = {
                "roomId":roomId,
                "roomName":roomName,
                "ownerId":yzId,
                "ownerName":yzName,

                "type":type, //收费对象类型  room 房屋  parking 车位  garage 车库
                "communityId":this.queryObj.communityId,
                "communityName":this.queryObj.communityName,
                "sumIds":sumIds
                // "sumIds":this.queryObj.id
            }
            console.log('发送：', sendObj)
            postAction('/unity/payBillReportPrint/add',sendObj).then(res0 => {
                let res = res0.data
                if (res.code == 200) {
                    console.log('接口返回:', res)
                    this.returnList=[]
                    this.getList()
                    let newWin = this.$router.resolve({ path: '/receiptPrintPaper', query: { batchNum: res.data.batchNum }})
                    window.open(newWin.href, '_blank')
                } else {
                    this.$message.warning(res.msg)
                }
                
            })
        },
        formatList() {
            for (let i of this.list) {
                i.feeName = utils.getNameById(i.feeType, this.costTypeList)
                i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList)
            }
        },
        num2Round(num, digit = 2) {
            return utils.num2Round(num, digit)
        },
        payTypeChange() {
            this.backName = ''
            this.listQuery.parkingId=''
            this.listQuery.roomId=''
            this.listQuery.garageId=''
            if (this.listQuery.payType == 1) {
                this.islabel = '选择房屋'
            } else if (this.listQuery.payType == 2) {
                this.islabel = '选择车位'
            } else {
                this.islabel = '选择车库'
            }
            this.returnList=[]
            this.list=[]
        },
        showDlg() {
            if (this.listQuery.payType == '') {
                this.$message({
                    type: 'warning',
                    message: '请先选择类型'
                })
                return false
            }
            this.dlgType = this.listQuery.payType
            this.dlgState = true
        },
        // 关闭弹窗
        closeDlg() {
            this.dlgState = false
        },
        backFunc(data) {
            this.queryObj = data
            console.log(data, 'dat');
            if (this.listQuery.payType == 1) {
                this.backName = data.roomFullName
                this.listQuery.roomId = data.id
            } else if (this.listQuery.payType == 2) {
                this.backName = data.numStr
                this.listQuery.parkingId = data.id
            } else {
                this.backName = data.numStr
                this.listQuery.garageId = data.id
            }
            this.returnList=[]
            this.list=[]
            this.getList()
        },
        getList() {
            if (utils.isNull(this.listQuery.payType)) {
                this.$message.error("请先选择类型")
                return
            }
            if (utils.isNull(this.backName)) {
                this.$message.error("请先选择房屋/车位/车库")
                return
            }
            this.count++
            this.listLoading = true
            let sendObj = JSON.parse(JSON.stringify(this.listQuery));
            if (sendObj.dateRange && sendObj.dateRange.length > 0) {
                sendObj.startDate = sendObj.dateRange[0];
                sendObj.endDate = sendObj.dateRange[1];
            } else {
                sendObj.startDate = "";
                sendObj.endDate = "";
            }
            delete sendObj.dateRange;
            findPayBillSumPage(sendObj).then(res => {
                this.listLoading = false
                if (res.data.code == 200) {
                    this.list = res.data.data
                    this.total = res.data.page.total
                    this.formatList()
                    this.$nextTick(() => {
                        this.list.forEach(row => {
                            this.returnList.forEach(row2 => {
                                if (row.id === row2.id) {
                                    this.$refs.multipleTable.toggleRowSelection(row, true);
                                }
                            });
                        });
                    });
                } else {
                    this.$message.error(res.data.msg)
                }
                console.log(res, 'res');
            })
        },
        listDataChange(val, row) {
            this.tableCheckBaseFunc(row);
        },
        // 单行操作方法
        tableCheckBaseFunc(row) {
            let isCheck = !this.returnList.some(item => item.id == row.id); // true-勾选状态，false-取下选择状态
            // 判断是否是勾选状态
            if (isCheck) {
                // 勾选
                this.returnList.push(row);
            } else {
                // 取消选择
                let returnList = this.returnList.filter(item => {
                    return item.id != row.id;
                });
                this.returnList = JSON.parse(JSON.stringify(returnList));
            }
            this.$forceUpdate();
        },
        tableSelectAll(arr) {
            console.log("全选", arr);
            let len = arr.length;
            // 长度为0 取消全选，将list中所有数据，从returnList中移除
            // 长度不为0，全选，将list中所有数据，追加到 returnList中
            let list = JSON.parse(JSON.stringify(this.list));
            let returnList = JSON.parse(JSON.stringify(this.returnList));
            if (len == 0) {
                let newList = [];
                for (let item of returnList) {
                    let hasId = list.some(item2 => {
                        return item2.id == item.id;
                    });
                    if (!hasId) {
                        newList.push(item);
                    }
                }
                returnList = JSON.parse(JSON.stringify(newList));
            } else {
                for (let item of list) {
                    let hasId = returnList.some(item2 => {
                        return item2.id == item.id;
                    });
                    if (!hasId) {
                        returnList.push(item);
                    }
                }
            }
            console.log("完美的选中数据", returnList);
            this.returnList = returnList;
        },

    }
};
</script>
