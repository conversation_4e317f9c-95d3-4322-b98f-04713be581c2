<template>
  <!-- 弹窗 新增/编辑 -->
  <!-- :title="dlgType === 'add' ? '添 加' : '修 改'" -->
  <el-dialog title="中心洗消单详情" :close-on-click-modal="false" :visible.sync="dlgState" append-to-body width="1000px" top="30px">
    <div class="gzb-box">
      <!-- <div>关联科室送洗单：</div>
      <el-table
        v-loading="listLoading"
        style="max-height: 36vh"
        class="m-small-table"
        ref="tableRef"
        :data="list"
        border
        fit
        highlight-current-row
        @sort-change="sortChange"
      >
        <el-table-column label="#" type="index" width="60"> </el-table-column>

        <el-table-column label="送洗单号" prop="label"></el-table-column>
        <el-table-column label="操作人" prop="label"></el-table-column>
        <el-table-column label="送洗时间" prop="label"></el-table-column>
        <el-table-column label="送洗数量" prop="label"></el-table-column>
        <el-table-column label="已接受数量" prop="label"></el-table-column>
      </el-table> -->

      <!-- <div>明细：</div> -->
      <el-table
        v-loading="listLoading"
        class="m-small-table"
        ref="tableRef"
        :data="list2"
        border
        fit
        highlight-current-row
        @sort-change="sortChange"
      >
        <el-table-column label="#" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="档案名称" prop="name"></el-table-column>
        <el-table-column label="材质" prop="clothMaterialText"></el-table-column>
        <el-table-column label="规格" prop="clothSpecificationText"></el-table-column>
        <el-table-column label="重量" prop="weight" width="120" align="center"></el-table-column>
        <el-table-column label="总数" prop="totalCount" width="120" align="center"></el-table-column>
        <!-- <el-table-column label="取回数量" prop="backCount" width="120" align="center"></el-table-column> -->
      </el-table>
      <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
      <div class="clear"></div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
import Pagination from '@/components/Pagination' // 分页

// 工具
// import { phoneReg } from '@/utils/regUtil'
import * as utils from '@/utils'

// 接口
import { postAction, getAction } from '@/api'

let listQueryEmpty = {
  rollId: '', //	工资表id	body	false	int32

  text: '', //	模糊查询
  page: 1,
  size: 20,

  sortOrder: '', //	倒序正序 asc:正序 desc:倒序	body	false	string
  sortParam: '', //	排序字段	body	false	string
}
export default {
  components: {
    Pagination,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        console.log('val', val)
        setTimeout(() => {
          // this.dlgType1 = this.dlgType.split('-')[0]
          // this.dlgType2 = this.dlgType.split('-')[1]
          // // console.log('this.dlgType1', this.dlgType2 == 'info')
          // // console.log('this.dlgType2', this.dlgType2 == 'shenpi')
          // if (this.dlgType1 == 'chushen') {
          //   if (this.dlgType2 == 'info') {
          //     this.dlgTitle = '初审详情'
          //   }
          //   if (this.dlgType2 == 'shenpi') {
          //     this.dlgTitle = '初审审批'
          //   }
          // }
          // if (this.dlgType1 == 'fushen') {
          //   if (this.dlgType2 == 'info') {
          //     this.dlgTitle = '复审详情'
          //   }
          //   if (this.dlgType2 == 'shenpi') {
          //     this.dlgTitle = '复审审批'
          //   }
          // }
          // this.listQuery.rollId = this.dlgQuery.id

          // this.rowMonthDayNum = new Date(this.dlgQuery.year, this.dlgQuery.month, 0).getDate()
          this.searchFunc()
        }, 50)
      } else {
        this.total = 0
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      // 弹窗
      dlgTitle: '',
      dlgType1: '',
      dlgType2: '',

      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        tableId: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      dlgSubLoading: false, // 提交loading
      // 其他数据 下拉框
      aaaaSelect: [], // 关联表单

      // 列表
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      list2: [],
    }
  },
  created() {},
  methods: {
    // -- << 列表
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.list = []
      this.listLoading = true
      let sendObj = JSON.parse(JSON.stringify(this.listQuery))

      getAction(`/cloth/coflow/flowDetail/${this.dlgQuery.id}`).then((res0) => {
        let res = res0.data
        this.listLoading = false
        if (res.code == 200) {
          this.list2 = res.data
          // this.total = res.page.total

          this.$nextTick(() => {
            this.$refs.tableRef.doLayout()
          })
        } else {
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },

    // 搜索框 清空单个条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },
    // 排序
    sortChange(data) {
      let type = data.column.sortBy
      let order = data.order
      if (order == null) {
        type = ''
        order = ''
      } else {
        if (order == 'descending') {
          order = 'desc'
        } else {
          order = 'asc'
        }
      }
      this.listQuery.sortParam = type
      this.listQuery.sortOrder = order
      this.getList()
    },
    // -- >> 列表

    // 弹窗提交 ------
    dlgSubFunc() {
      this.dlgSubLoading = true

      let sendObj = {
        rollId: this.listQuery.rollId,
      }
      if (this.dlgType1 == 'chushen') {
        sendObj.type = 1
      }
      if (this.dlgType1 == 'fushen') {
        sendObj.type = 2
      }

      // console.log('sendObj', sendObj)
      // return false
      auditPayRoll(sendObj).then((res0) => {
        this.subAjaxBack(res0)
      })
    },
    subAjaxBack(res0) {
      let res = res0.data
      this.dlgSubLoading = false
      if (res.code == 200) {
        this.$message.success(res.msg)
        this.dlgState = false
        this.$emit('getList')
        this.$emit('closeDlg')
      } else {
        this.$message({
          type: 'warning',
          message: res.msg,
        })
      }
    },

    closeDlg() {
      this.dlgLoading = false
      this.dlgSubLoading = false
      // this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')
    },

    // 更新外层列表
    upList1() {
      this.$emit('getList')
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.gzb-box {
  // padding: 0 20px;
  .gzb-top,
  .gzb-bottom {
    // margin-bottom: 10px;
    p {
      margin: 0px;
      display: inline-block;
      margin-right: 40px;
    }
  }
  .gzb-bottom {
    margin-top: 10px;
    p {
      margin-right: 20px;
    }
  }
}
</style>