import request from '@/utils/request'

// 查询排班列表
export function schedulingPage(data) {
  return request({
    url: '/workade/scheduling/page',
    method: 'post',
    data
  })
}

// 保存排班
export function saveSchedulingByUserId(userId, createDate) {
  return request({
    url: `/workade/saveSchedulingByUserId/${userId}/${createDate}`,
    method: 'get'
  })
}

// 修改排班
export function updateSchedule(data) {
  return request({
    url: '/workade/updateSchedule',
    method: 'post',
    data
  })
}



