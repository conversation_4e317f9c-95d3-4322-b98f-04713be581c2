import request from '@/utils/request'

// 投诉分页查询
export function membercomplaintPage(data){
  return request({
    url: `/unity/membercomplaint/page`,
    method: 'post',
    data
  })
}


// 新增/修改投诉
export function saveMemberComplaint(data){
  return request({
    url: `/unity/membercomplaint/saveMemberComplaint`,
    method: 'post',
    data
  })
}

// 删除投诉
export function updateFlagById(id) {
  return request({
    url: `/unity/membercomplaint/updateFlagById/${id}`,
    method: 'get',
  })
}

// 投诉处理
export function membercomplaintHandle(data){
  return request({
    url: `/unity/membercomplaint/tousuchuli`,
    method: 'post',
    data
  })
}

