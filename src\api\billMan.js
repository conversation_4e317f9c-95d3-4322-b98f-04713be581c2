/**
 * Created by 马振国 on 19/04/26.
 * Desc 接口 单据管理
 */
import request from '@/utils/request'
import { requestExcel } from '@/utils'

// 【【 1 岗位相关
// 1 增岗单
export function findBillAddPostByDynamic(data) {
  return request({
    url: `/sys/findBillAddPostByDynamic`,
    method: 'post',
    data
  })
}
// 2 岗位启停单
export function findBillUpdatePostTypeByDynamic(data) {
  return request({
    url: `/sys/findBillUpdatePostTypeByDynamic`,
    method: 'post',
    data
  })
}
// 3 减岗单
export function findBillBillReducePostByDynamic(data) {
  return request({
    url: `/sys/findBillBillReducePostByDynamic`,
    method: 'post',
    data
  })
}
// 4 岗位调薪单
export function findBillUpdatePostSalaryByDynamic(data) {
  return request({
    url: `/sys/findBillUpdatePostSalaryByDynamic`,
    method: 'post',
    data
  })
}

// 7 岗位互调单
export function findPostIntermodulationByDynamic(data) {
  return request({
    url: `/sys/findPostIntermodulationByDynamic`,
    method: 'post',
    data
  })
}

// 】】 1 岗位相关

// 【【 2 员工相关
// 1 入职单
export function findBillInductionByDynamic(data) {
  return request({
    url: `/sys/findBillInductionByDynamic`,
    method: 'post',
    data
  })
}
// 2 转正单
export function findRegularUserByDynamic(data) {
  return request({
    url: `/sys/findRegularUserByDynamic`,
    method: 'post',
    data
  })
}

// 3 调岗调薪单
export function findBillSalaryPostIncreaseByDynamic(data) {
  return request({
    url: `/sys/findBillSalaryPostIncreaseByDynamic`,
    method: 'post',
    data
  })
}

// 4 兼岗申请单
export function findBillStaffConPostByDynamic(data) {
  return request({
    url: `/sys/findBillStaffConPostByDynamic`,
    method: 'post',
    data
  })
}

// 5 取消兼岗 同上，改变条件

// 6 社保申请单
export function findSecurityByDynamic(data) {
  return request({
    url: `/sys/findSecurityByDynamic`,
    method: 'post',
    data
  })
}

// 7 银行卡变更单
export function billUpdateBankNumPage(data) {
  return request({
    url: `/sys/billUpdateBankNumPage`,
    method: 'post',
    data
  })
}

// 8 离职申请单
export function billSaveStaffLeaveRecordPage(data) {
  return request({
    url: `/sys/billSaveStaffLeaveRecordPage`,
    method: 'post',
    data
  })
}

// 9 外出考勤单
export function findOutPunchByDynamic(data) {
  return request({
    url: `/ade/findOutPunchByDynamic`,
    method: 'post',
    data
  })
}

// 10 考勤异常申请单
export function adeAbnormalDeclarePage(data) {
  return request({
    url: `/ade/adeAbnormalDeclarePage`,
    method: 'post',
    data
  })
}

// 】】 2 员工相关

// 【【 3 考勤单据
// 1 请假单 / 销假单
export function findLeaveRecordByDynamic(data) {
  return request({
    url: `/ade/findLeaveRecordByDynamic`,
    method: 'post',
    data
  })
}

// 2 加班单
export function findOverTimeByDynamic(data) {
  return request({
    url: `/ade/findOverTimeByDynamic`,
    method: 'post',
    data
  })
}

// 3 串休单
export function findStringBreakRecordByDynamic(data) {
  return request({
    url: `/ade/findStringBreakRecordByDynamic`,
    method: 'post',
    data
  })
}

// 】】 3 考勤单据
// 【【 4 薪酬相关
// 1 奖励，扣款，补助
export function paySanctionPage(data) {
  return request({
    url: `/ade/paySanctionPage`,
    method: 'post',
    data
  })
}
// 】】 4 薪酬相关
