<template>
  <!-- 弹窗-选择-->
  <el-dialog
    class="mazhenguo"
    title="选择派工岗位"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="1000px"
    top="30px"
  >
    <div class="clearfix">
      <el-input @keyup.enter.native="searchFunc" class="fl" placeholder="关键字" v-model="listQuery.label" style="width: 200px">
        <i @click="resetSearchItem(['label'])" slot="suffix" class="el-input__icon el-icon-error"></i>
      </el-input>

      <el-button icon="el-icon-search" type="primary" class="fl ml10" @click="getList" size="mini" style="padding: 7px 10px"
        >搜索</el-button
      >
      <el-popover class="fl" placement="bottom-end" width="800" @show="showPopover" trigger="click">
        <el-table ref="multipleTable" style="height: 400px; overflow: auto" :data="selectList">
          <el-table-column type="index" width="50" align="center"></el-table-column>

          <el-table-column label="岗位名称">
            <template slot-scope="scope">
              <span>{{ scope.row.label }}</span>
            </template>
          </el-table-column>

          <el-table-column label="部门" prop="branchName"></el-table-column>
          <el-table-column label="在职人" prop="userName" width="140"></el-table-column>
          <el-table-column property="" label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button @click="popRemoveRow(scope.row)" type="danger" size="mini" icon="el-icon-delete" plain></el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button class="fr search-right-btn" type="success" slot="reference" icon="el-icon-arrow-down">查看已选</el-button>
      </el-popover>
    </div>

    <el-table
      height="400"
      ref="tableRef"
      class="m-small-table mt10"
      v-loading="listLoading"
      :key="tableKey"
      :data="list"
      border
      fit
      highlight-current-row
      @select="tableSelectChange"
      @select-all="tableSelectAll"
      @row-click="tableRowClick"
    >
      <!-- 多选 -->
      <el-table-column type="selection" width="55" align="center"> </el-table-column>

      <el-table-column label="岗位名称">
        <template slot-scope="scope">
          <span>{{ scope.row.label }}</span>
        </template>
      </el-table-column>

      <el-table-column label="部门" prop="branchName"></el-table-column>
      <el-table-column label="在职人" prop="userName" width="140"></el-table-column>

      <!-- <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button @click="showDlg('info', scope.row)" icon="el-icon-document" size="mini" type="primary" title="详情" plain
            >详情</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>

    <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button v-if="dlgType != 'info'" :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">提交中...</span>
        <span v-else>确定</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
// 工具
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
import Pagination from '@/components/Pagination'
// 接口
import * as utils from '@/utils'
import * as regUtils from '@/utils/regUtils'

import { postAction, getAction } from '@/api'

let listQueryEmpty = {
  label: '', //	模糊查询	body	false	string
  page: 1,
  limit: 20,

  dateRange: [],
  // beginDate: '',//		body	false	string
  // endDate: '',//		body	false	string

  projectId: '', //		body	false	string

  warehouseId: '', //

  classCode: '', //		body	false	string
  code: '', //		body	false	string
  type: '', //		body	false	int32

  // status: 0,
  // processStatus: 1,
}

export default {
  components: {
    Pagination,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
    selectList0: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))

          if (this.selectList0) {
            this.selectList = this.selectList0
          } else {
            this.selectList = []
          }
          this.getList()
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),
      selectList: [], // 选中

      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      dlgSubLoading: false,

      // 弹窗
      dlgState: false,

      pgState: '1', // 1-范围内 2-范围外
    }
  },
  created() {
    // this.getDataDict()
  },
  methods: {
    // 数据字典
    getDataDict() {
      let keyList = (this.keyMap = [
        // { dbKey: "sex", pageSelectKey: "sexSelect", pageKey: "sex", pageName: 'sexText' },
        { dbKey: 'leaveCause', pageSelectKey: 'qjsySelect' }, // 请假事由
        { dbKey: 'timeOffType', pageSelectKey: 'jqlxSelect' }, // 假期类型
      ])
      utils.getDbItems(this, keyList)
    },
    // 弹窗提交 ------
    dlgSubFunc() {
      this.$emit('backFunc', this.selectList)
      this.closeDlg()
    },
    closeDlg() {
      this.$emit('closeDlg')
    },

    // ------ 列表
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.searchFunc()
    },
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.list = []

      let sendObj = { ...this.listQuery, ...this.dlgQuery }
      sendObj.label = sendObj.label.trim()

      sendObj.projectId = this.userInfo.projectId

      //       label: ""
      // limit: 20
      // page: 1
      // projectId: 45

      this.listLoading = true
      postAction('/org/findProjectPostPage', sendObj).then((res0) => {
        let res = res0.data
        this.listLoading = false
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = []
            this.total = 0
          } else {

            let list0 = res.data || []
            for (let item of list0) {
              item.id = item.postId
            }
            this.list = JSON.parse(JSON.stringify(list0))
            this.total = res.page.total

            this.$nextTick(() => {
              this.$refs.tableRef.doLayout()
            })

            // 如果复选设置表格勾选
            let list = this.list
            this.$nextTick(() => {
              console.log('this.selectList', this.selectList)
              if (this.selectList.length > 0) {
                for (let item of list) {
                  let isHas = this.selectList.some((row) => row.id == item.id)
                  if (isHas) {
                    this.$refs.tableRef.toggleRowSelection(item, true)
                  } else {
                    this.$refs.tableRef.toggleRowSelection(item, false)
                  }
                }
              } else {
                this.$refs.tableRef.clearSelection()
              }
            })
          }
        }
      })
    },

    // -- << 已选弹窗
    showPopover() {
      // for (let item of this.selectList) {
      //   this.$refs.multipleTable.toggleRowSelection(item, true)
      // }
    },
    // 已选删除
    popRemoveRow(row) {
      console.log('----row', row)
      let selectList = this.selectList.filter((item) => {
        return item.id != row.id
      })
      this.selectList = JSON.parse(JSON.stringify(selectList))

      // 选择商品弹窗，复选框同步改变
      console.log('this.list', this.list)
      console.log('selectList', this.selectList)
      for (let item of this.list) {
        let isHas = row.id == item.id
        console.log('----isHas', isHas)
        if (isHas) {
          this.$refs.tableRef.toggleRowSelection(item, false)
        }
      }
    },
    // >> 已选弹窗

    // 点击行

    // 点击复选框
    tableSelectChange(arr, row) {
      this.tableCheckBaseFunc(row)
    },
    // 点击行
    tableRowClick(row, column, event) {
      // let disabledCol = ['商品名称','商品编码','图片']
      // if (disabledCol.indexOf(column.label) >=0) return false
      // this.$refs.tableRef.toggleRowSelection(row)
      // this.tableCheckBaseFunc(row)
    },
    // 单行操作方法
    tableCheckBaseFunc(row) {
      let isCheck = !this.selectList.some((item) => item.id == row.id) // true-勾选状态，false-取下选择状态
      console.log('isCheck', isCheck)
      // 判断是否是勾选状态
      if (isCheck) {
        // 勾选
        this.selectList.push(row)
      } else {
        // 取消选择
        let selectList = this.selectList.filter((item) => {
          return item.id != row.id
        })
        this.selectList = JSON.parse(JSON.stringify(selectList))
      }
    },
    // 表格 全选
    tableSelectAll(arr) {
      let len = arr.length
      // console.log(arr.length)
      // 长度为0 取消全选，将list中所有数据，从selectList中移除
      // 长度不为0，全选，将list中所有数据，追加到 selectList中
      let list = JSON.parse(JSON.stringify(this.list))
      let selectList = JSON.parse(JSON.stringify(this.selectList))
      if (len == 0) {
        let newList = []
        for (let item of selectList) {
          let hasId = list.some((item2) => item2.id == item.id)
          if (!hasId) {
            newList.push(item)
          }
        }
        selectList = JSON.parse(JSON.stringify(newList))
      } else {
        for (let item of list) {
          let hasId = selectList.some((item2) => item2.id == item.id)
          if (!hasId) {
            selectList.push(item)
          }
        }
      }
      // console.log('完美的选中数据', selectList)
      this.selectList = selectList
    },
    // >> 新 复选
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>