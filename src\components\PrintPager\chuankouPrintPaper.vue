<template>
  <div class="app-container print">
    <div
      ref="paperContainer"
      :style="`width:${paperWidth}px;height:${paperHeight}px;`"
      class="paper-container clearfix"
      style="margin-top: -6px"
    >
      <div class="tac text-black fs18 text-bold">医疗机构新型冠状病毒肺炎流行病学史调查问卷</div>
      <div class="mt10 taj text-black text-bold" style="line-height: 1.2; text-indent: 2em">
        为响应国务院关于遏制新冠肺炎传播的各项要求，为了您和您身边每一位同胞的生命健康安全，根据《中华人民共和国传染病防治法》第七十七条法律法规，请您提供真实准确的流行病学史信息，故意隐瞒，当事人需承担相应法律责任，感谢您的配合。
      </div>
      <!-- 一 -->
      <div class="flexc page-title0 mt10">一、基本信息</div>
      <div>
        <div class="flexc lhbar">
          <div><span class="page-title">1.姓名：</span>{{ dlgData.name }}</div>
          <div><span class="page-title">2.性别：</span>{{ dlgData.sex }}</div>
          <div><span class="page-title">3.年龄：</span>{{ dlgData.age }}</div>
          <div><span class="page-title">4.联系方式：</span>{{ dlgData.phone }}</div>
          <div><span class="page-title">5.身份证/护照号：</span>{{ dlgData.idNumber }}</div>
        </div>
        <div class="flexlc lhbar">
          <div class="flex-sub"><span class="page-title">6.现住址：</span>{{ dlgData.address }}</div>
        </div>
        <div class="flexlc lhbar">
          <div class="flexlc">
            <span class="page-title">7.行程码：</span>
            <input type="radio" value="0" :checked="dlgData.travelCode == '0'" />&nbsp;正常
            <input class="ml10" type="radio" value="1" :checked="dlgData.travelCode == '1'" />&nbsp;异常
          </div>
          <div class="flexlc lhbar ml30">
            <span class="page-title">健康码：</span>
            <div class="flexlc lhbar">
              <div class="flexlc lhbar mr10" v-for="item of jkmSelect" :key="item.id">
                <input class="" type="checkbox" :value="item.id" :checked="dlgData.healthCode.indexOf(item.id) >= 0" />
                &nbsp;{{ item.name }}
              </div>
            </div>
          </div>
        </div>
        <div class="flexlc lhbar page-title">8.是否为以下特定职业人群：</div>
        <div class="clearfix">
          <div class="mr20 fl flexlc" style="height: 16px" v-for="item of tdrqSelect" :key="item.id">
            <input
              class=""
              type="checkbox"
              :value="item.id"
              :checked="dlgData.specificProfession.indexOf(item.id) >= 0"
            />
            &nbsp;{{ item.name }}
          </div>
        </div>
        <div class="flexlc lhbar page-title" style="margin-top: 4px">9.此次就诊相关信息：请填写具体时间</div>
        <div class="flexlc lhbar">
          <span>①&nbsp;</span>
          <div class="m-input">{{ dlgData.startTime }}</div>
          <span class="">&nbsp;至&nbsp;</span>
          <div class="m-input">{{ dlgData.endTime }}</div>
          &nbsp;&nbsp;
          <div class="">乘坐交通工具:</div>
          &nbsp;
          <div class="flexlc lhbar">
            <div class="flexlc lhbar">
              <input class="" type="checkbox" :checked="dlgData.isWalking == '1'" />
              &nbsp;步行
            </div>
            &nbsp;
            <div class="flexlc lhbar">
              <input class="" type="checkbox" :checked="dlgData.isTaxi == '1'" />
              &nbsp;出租车
            </div>
            &nbsp;
            <div class="flexlc lhbar">
              <input class="" type="checkbox" :checked="dlgData.isPrivateCars == '1'" />
              &nbsp;私家车
            </div>
            &nbsp;
            <div class="flexlc lhbar">
              <input class="" type="checkbox" :checked="dlgData.isBus == '1'" />
              <div class="m-input tac" style="width: 50px">{{ dlgData.isBusNum }}</div>
              路汽车
            </div>
            &nbsp;
            <div class="flexlc lhbar">
              <input class="" type="checkbox" :checked="dlgData.isTrain == '1'" />
              <div class="m-input tac" style="width: 50px">{{ dlgData.isTrainNum }}</div>
              次火车
            </div>
            &nbsp;
            <div class="flexlc lhbar">
              <input class="" type="checkbox" :checked="dlgData.isPlane == '1'" />
              <div class="m-input tac" style="width: 50px">{{ dlgData.isPlaneNum }}</div>
              次航班飞机
            </div>
          </div>
        </div>

        <div class="flexlc lhbar">
          <span>②&nbsp;有无陪同人员？</span>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isAccompany == '1'" />&nbsp;有
            <input class="ml10" type="radio" value="0" :checked="dlgData.isAccompany == '0'" />&nbsp;无
          </div>
          <span class="ml20" v-if="dlgData.isAccompany == '1'">陪同人员有几位？</span>
          <div class="flexlc lhbar" v-if="dlgData.isAccompany == '1'">
            <input type="radio" value="1" :checked="dlgData.isAccompanyNum == '1'" />&nbsp;一位
            <input
              class="ml10"
              type="radio"
              value="2"
              :checked="!dlgData.isAccompanyNum == '2'"
            />&nbsp;二位（仅限两位）
          </div>
        </div>
        <div class="flexlc lhbar" v-if="dlgData.isAccompany == '1'">
          <span>③&nbsp;主要陪同人员信息：</span>

          <span class="ml10" v-if="dlgData.accompanyUseraName">姓名：{{ dlgData.accompanyUseraName }}</span>
          <span class="ml10" v-if="dlgData.accompanyUseraName">电话：{{ dlgData.accompanyUseraPhone }}</span>
          <span class="ml30" v-if="dlgData.accompanyUserbName">姓名：{{ dlgData.accompanyUserbName }}</span>
          <span class="ml10" v-if="dlgData.accompanyUserbName">电话：{{ dlgData.accompanyUserbPhone }}</span>
        </div>
        <div class="flexlc lhbar">
          <div class="page-title">10.是否有医疗机构就诊史：</div>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isMedicalHistory == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isMedicalHistory == '0'" />&nbsp;否
          </div>

          <span v-if="dlgData.isMedicalHistory == '1'" class="ml20">就诊日期：</span>
          <div v-if="dlgData.isMedicalHistory == '1'" class="m-input tac" style="width: 80px">
            {{ dlgData.medicalHistoryDate }}
          </div>
          <span v-if="dlgData.isMedicalHistory == '1'" class="ml20">就诊医院：</span>
          <div v-if="dlgData.isMedicalHistory == '1'" class="m-input tac" style="min-width: 80px">
            {{ dlgData.medicalHistoryName }}
          </div>
        </div>
        <div class="flexlc lhbar">
          <div class="page-title">11.是否是新冠复阳患者：</div>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isFuyanghuanzhe == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isFuyanghuanzhe == '0'" />&nbsp;否
          </div>
        </div>
      </div>
      <!-- 二 -->
      <div class="lhbar flexc page-title0">二、病例信息</div>
      <div>
        <div class="flexlc" style="min-height: 26px">
          <div class="page-title">1.两周内是否出现发热情况：</div>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isFever == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isFever == '0'" />&nbsp;否

            <div class="ml10">现体温为：</div>
            <div class="m-input tac" style="width: 40px"></div>
            ℃
          </div>
        </div>

        <div class="flexlc" style="min-height: 26px">2.您此次就医的主要症状为：{{ dlgData.medicalSymptoms }}</div>
        <div class="flexlc lhbar">
          <div class="page-title">3.您目前是否有以下症状：</div>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isSymptoms == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isSymptoms == '0'" />&nbsp;否
          </div>
        </div>
        <div class="clearfix">
          <div class="mr20 fl flexlc" style="height: 16px" v-for="item of yxzzSelect" :key="item.id">
            <input class="" type="checkbox" :value="item.id" :checked="dlgData.isSymptomsVal.indexOf(item.id) >= 0" />
            &nbsp;{{ item.name }}
          </div>
        </div>

        <!-- <div class="flexlc lhbar">
          <div class="flexlc lhbar" v-for="item of yxzzSelect" :key="item.id">
            <input class="" type="checkbox" :value="item.id" :checked="dlgData.isSymptomsVal.indexOf(item.id) >= 0" />
            &nbsp;{{ item.name }}&nbsp;&nbsp;
          </div>
        </div> -->
        <!-- <div class="flexlc">
          <div class="flexlc lhbar" v-for="item of yxzzSelect2" :key="item.id">
            <input class="" type="checkbox" :value="item.id" :checked="dlgData.isSymptomsVal.indexOf(item.id) >= 0" />
            &nbsp;{{ item.name }}&nbsp;&nbsp;
          </div>
        </div> -->

        <div class="flexlc lhbar" style="margin-top: 4px">
          <div class="page-title">4.一周内是否做过下列相关检测：</div>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isDetection == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isDetection == '0'" />&nbsp;否
          </div>
          <div class="flexlc ml30">
            <div class="flexlc lhbar" v-for="item of xgjcSelect" :key="item.id">
              <input
                class=""
                type="checkbox"
                :value="item.id"
                :checked="dlgData.isDetectionVal.indexOf(item.id) >= 0"
              />
              &nbsp;{{ item.name }}&nbsp;&nbsp;
            </div>
          </div>
        </div>
        <div class="flexlc lhbar">
          <div class="page-title">5.近一周是否吃过退烧药：</div>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isEatFebrifuge == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isEatFebrifuge == '0'" />&nbsp;否
          </div>

          <div class="ml30 flexlc" v-if="dlgData.isEatFebrifuge == '1'">
            退烧药名称：
            <div class="m-input tac" style="min-width: 100px">{{ dlgData.isEatFebrifugeName }}</div>
          </div>
        </div>

        <div class="flexlt" style="margin-top: 3px">
          <div class="page-title">6.是否有集中隔离或居家隔离史，<br />如有请提供解除隔离证明：</div>

          <img
            class="ml30"
            v-if="dlgData.outQuarantineUrl"
            :src="dlgData.outQuarantineUrl"
            alt=""
            style="display: block; width: 140px; height: 60px"
          />
          <!-- <div v-else class="tac ml30" style="width: 140px; height: 60px; line-height: 60px; border: 1px solid #ccc">
            暂无图片
          </div> -->
        </div>
      </div>
      <!-- 三 -->
      <div class="lhbar flexlc page-title0">三、危险史与暴露史(*请如实填写)</div>
      <div>
        <div class="flexlc lhbar">
          <div class="page-title">1.就诊前14天内是否到过以下地区：</div>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isAchieveArea == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isAchieveArea == '0'" />&nbsp;否
          </div>
        </div>
        <div class="flexc lhbar">
          <div class="flexlc" style="width: 23%">
            境外(何地):
            <div class="m-input flex-sub tac">{{ dlgData.overseasArea }}</div>
          </div>
          <div class="flexlc ml10" style="width: 33%">
            中高风险地区(何地)：
            <div class="m-input flex-sub tac">{{ dlgData.riskArea }}</div>
          </div>
          <div class="flex-sub flexlc ml10">
            其他明确的新冠肺炎疫区(何地)：
            <div class="m-input flex-sub tac">{{ dlgData.otherArea }}</div>
          </div>
        </div>

        <div class="flexlc lhbar">
          <div class="page-title">2.近两周是否接触过明确新冠病毒感染者(核酸检测阳性者)或发热、咳嗽等症状人群：</div>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isContactPatient == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isContactPatient == '0'" />&nbsp;否
          </div>
        </div>
        <div class="flexlc lhbar">
          <div class="page-title">3.近期(一个月)是否有国外旅居史或与归国人员有过接触史：</div>
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isContactReturnees == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isContactReturnees == '0'" />&nbsp;否
          </div>
        </div>
      </div>
      <div class="mt10 taj text-black text-bold" style="line-height: 1.2; text-indent: 2em">
        我本人承诺上述情况属实，依照《中华人民共和国传染病防治法》第七十七条法律法规、如有隐瞒、导致传染病传播、流行，给他人人身、财产造成损失，我将承担法律责任。
      </div>
      <!-- 有签字图片 -->
      <div v-if="dlgData.signatureUrl" class="flexlt lhbar mt30">
        <div class="flexlt" style="width: 30%">
          <div class="page-title0 lhbar flexlc">就诊患者签字：</div>
          <img class="" :src="dlgData.signatureUrl" alt="" style="display: block; width: 100px; height: 50px" />
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">日期：</div>
          <div class="m-input flex-sub tac">{{ printDate }}</div>
        </div>
      </div>
      <!--  无签字图片 -->
      <div v-else class="flexlc lhbar mt30">
        <div class="flexlc" style="width: 30%">
          <div class="page-title0">就诊患者签字：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">日期：</div>
          <div class="m-input flex-sub tac">{{ printDate }}</div>
        </div>
      </div>

      <!-- 第二行 -->
      <div class="flexlc lhbar mt30">
        <div class="flexlc" style="width: 30%">
          <div class="page-title0">分诊人员签字：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">就诊医生签字：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
      </div>
    </div>
    <el-button class="no-print" icon="el-icon-printer" type="primary" size="medium" @click="printFunc()">
      打印</el-button
    >
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import * as utils from '@/utils'
import { return2Num } from '@/utils/calendarData'

// import {
//   findInfoById,
// } from '@/api/jyt/drugMail'

export default {
  components: {},
  data() {
    return {
      paperHeight: 0,
      paperWidth: 0,
      fieldList: [
        { name: '住宅物业管理费', desc: '111', money: '10' },
        { name: '电梯费', desc: '222', money: '20' },
        { name: '', desc: '', money: '' },
        { name: '', desc: '', money: '' },
      ],
      id: '',

      printDate: '',

      dlgData0: '',
      dlgData: '',

      // 下拉框
      jkmSelect: [], // 健康码
      tdrqSelect: [], // 是否为以下特定职业人群
      yxzzSelect: [], // 2.3.您目前是否有以下症状
      xgjcSelect: [], // 2.4.一周内是否做过下列相关检测:
      jzlxSelect: [], // 请选择就诊类型

      yxzzSelect2: [], // 2.3.您目前是否有以下症状
    }
  },
  computed: {},
  created() {
    this.setPrintDate()

    this.setFormData()
    this.getDbItems()
  },
  mounted() {
    this.$nextTick(() => {
      // console.log('utils.getDpiWidth(227)', utils.getDpiWidth(227))
      // this.paperWidth = utils.getDpiWidth(227)
      // this.paperHeight = utils.getDpiHeight(122)

      this.paperWidth = utils.getDpiWidth(188)
      this.paperHeight = utils.getDpiHeight(160)

      // this.paperWidth = utils.getDpiWidth(210)
      // this.paperHeight = utils.getDpiHeight(140)
    })
  },
  methods: {
    setPrintDate() {
      let today = new Date()
      let year = today.getFullYear()
      let month = return2Num(today.getMonth() + 1)
      let day = return2Num(today.getDate())
      this.printDate = `${year} 年 ${month} 月 ${day} 日`
    },
    setFormData() {
      let dlgData = JSON.parse(window.sessionStorage.printData)

      let info = utils.getInfoByIdNumber(dlgData.idNumber)
      if (info) {
        dlgData.sex = info.sex
        dlgData.age = info.age
      } else {
        dlgData.sex = ''
        dlgData.age = ''
      }

      dlgData.healthCode = dlgData.healthCode.split(',')
      dlgData.specificProfession = dlgData.specificProfession.split(',')

      dlgData.jtgjList = []

      console.log('dlgData', dlgData)

      if (dlgData.isWalking == '1') {
        dlgData.jtgjList.push('isWalking')
      }
      if (dlgData.isTaxi == '1') {
        dlgData.jtgjList.push('isTaxi')
      }
      if (dlgData.isPrivateCars == '1') {
        dlgData.jtgjList.push('isPrivateCars')
      }

      if (dlgData.isBus) {
        dlgData.isBusNum = dlgData.isBus
        dlgData.isBus = '1'
        // dlgData.jtgjList.push('isBus')
      } else {
        dlgData.isBusNum = ''
      }
      if (dlgData.isTrain) {
        dlgData.isTrainNum = dlgData.isTrain
        dlgData.isTrain = '1'
        // dlgData.jtgjList.push('isTrain')
      } else {
        dlgData.isTrainNum = ''
      }
      if (dlgData.isPlane) {
        dlgData.isPlaneNum = dlgData.isPlane
        dlgData.isPlane = '1'
        // dlgData.jtgjList.push('isPlane')
      } else {
        dlgData.isPlaneNum = ''
      }

      if (dlgData.isAccompany + '' === '0') {
        dlgData.isAccompanyNum = 0
        dlgData.isAccompany = '0'
      } else {
        dlgData.isAccompanyNum = dlgData.isAccompany
        dlgData.isAccompany = '1'
      }

      if (dlgData.isSymptoms) {
        dlgData.isSymptomsVal = dlgData.isSymptoms.split(',')
        dlgData.isSymptoms = '1'
      } else {
        dlgData.isSymptomsVal = []
        dlgData.isSymptoms = '0'
      }
      if (dlgData.isDetection) {
        dlgData.isDetectionVal = dlgData.isDetection.split(',')
        dlgData.isDetection = '1'
      } else {
        dlgData.isDetectionVal = []
        dlgData.isDetection = '0'
      }

      if (utils.isNull(dlgData.isEatFebrifuge)) {
        dlgData.isEatFebrifugeName = ''
        dlgData.isEatFebrifuge = '0'
      } else {
        dlgData.isEatFebrifugeName = dlgData.isEatFebrifuge
        dlgData.isEatFebrifuge = '1'
      }

      this.dlgData = this.dlgData0 = JSON.parse(JSON.stringify(dlgData))
    },
    getDbItems() {
      let keyArr = [
        'healthCode', // 健康码
        'specificProfession', // 是否为以下特定职业人群
        'isSymptoms', // 2-3 您目前是否有以下症状
        'isDetection', // 2-4 一周内是否做过下列相关检测
        'sickType', // 签字上边 请选择就诊类型
      ]
      let keyStr = keyArr.join(',')
      utils.getDbItems(keyStr).then((list) => {
        // 健康码
        this.jkmSelect = list[0]

        // 是否为以下特定职业人群
        this.tdrqSelect = list[1]

        //  2-3 您目前是否有以下症状
        let list2 = list[2]
        for (let i = 0; i < list2.length; i++) {
          if (i < 9) {
            this.yxzzSelect.push(list2[i])
          } else {
            this.yxzzSelect2.push(list2[i])
          }
        }

        // 2-4 一周内是否做过下列相关检测
        this.xgjcSelect = list[3]

        // 签字上边 请选择就诊类型
        this.jzlxSelect = list[4]

        // console.log('this.jzlxSelect', this.jzlxSelect)
      })
    },

    // ///////

    dealBigMoney(val) {
      console.log('之后的', utils.dealBigMoney(val))
      return utils.dealBigMoney(val)
    },

    // 打印
    printFunc() {
      this.dlgData = JSON.parse(JSON.stringify(this.dlgData0))
      this.$nextTick(() => {
        window.print()
      })
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-container.print {
  font-size: 14px;
}
.app-container {
  box-sizing: border-box;
  font-size: 12px;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 0;

  .paper-container {
    // background: url('/static/image/paper.jpg') no-repeat center;
    // background-size: 100%;
    background: #fff;
    position: relative;
    box-sizing: border-box;
    padding: 16px;
    font-size: 12px;
    // padding-top: 16px;
    .field {
      position: absolute;
      max-width: 360px;
      word-break: break-all;
    }
  }
  .el-button.no-print {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

@media print {
  .no-print {
    display: none;
  }
}

//
// 表格
.tac {
  text-align: center;
}
.tar {
  text-align: right;
}
.taj {
  text-align: justify;
}
.text-bold {
  font-weight: bold;
}
.mt16 {
  margin-top: 16px;
}
.m-table {
  // background:#666;
  border-top: 1px solid #666;
  border-left: 1px solid #666;
  border-spacing: 0px;
  font-size: 12px;
  td {
    background: #fff;
    min-height: 30px;
    box-sizing: border-box;
    padding: 1px;
    border-right: 1px solid #666;
    border-bottom: 1px solid #666;
  }
  .mh {
    min-height: 36px;
  }
}

/////
// .page-con {
//   color: #333;
// }
.page-title0 {
  font-weight: bold;
  color: #000;
}
.page-title {
  color: #000;
}
.lhbar {
  height: 26px;
}
.m-input {
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  padding: 0;
  height: 18px;
  line-height: 18px;
}
</style>