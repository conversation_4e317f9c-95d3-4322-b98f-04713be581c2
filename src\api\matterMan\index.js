import request from "@/utils/request";
// 冒泡
export function findNumsByUserIdJYT(projectId) {
  return request({
    // /report/order/findNumsByUserIdJYT/${projectId}
    url: `/report/order/findNumsByLxerp/${projectId}`,
    method: "get"
  });
}

// 报事列表
export function findOrderListPageJYT(data) {
  return request({
    // /report/order/findOrderListPageJYT
    url: `/report/order/findOrderListPage`,
    method: "post",
    data
  });
}

// 报事列表 已归档
export function findOrderListPageYiGuiDangJYT(data) {
  return request({
    // /report/order/findOrderListPageYiGuiDangJYT
    url: `/report/order/findOrderListPageYiGuiDang`,
    method: "post",
    data
  });
}

// 详情
export function orderFindOrderById(id) {
  return request({
    url: `/report/order/findOrderById/${id}`,
    method: "get"
  });
}

// 科目树
export function findSubjectTree(projectId) {
  return request({
    url: `/report/subject/findSubjectTree/${projectId}`,
    method: "get"
  });
}

// 报事区域树
export function findAreaTree(projectId) {
  return request({
    url: `/report/area/findAreaTree/${projectId}`,
    method: "get"
  });
}
