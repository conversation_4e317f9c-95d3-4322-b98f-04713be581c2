<template>
  <!-- 考勤管理 - 节假日设置 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入节假日名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>新增</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="节日名称">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>

        <el-table-column label="节日日期" width="200px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.startDate }} ~ {{ scope.row.endDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="260" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="dlgType === 'ADD'?'新增节假日':'编辑节假日'" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="120px">
        <el-form-item label="节日名称" prop="label">
          <el-input v-model="dlgData.label" placeholder="请输入节假日名称" style="width:350px;" />
        </el-form-item>

        <el-form-item label="节日日期" prop="rangeDate">
          <el-date-picker v-model="dlgData.rangeDate" type="daterange" range-separator="至" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findAdeHolidayPage, saveOrUAdeHoliday, deleteAdeHolidayById } from '@/api/attendanceMan/holidaySet.js'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  label: '', // 节日名称
  rangeDate: [],  // 日期区域
}


export default {
  name: 'holidaySet',
  extends: WorkSpaceBase,
  components: {
    Pagination
  },
  data () {
    return {
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',  // ADD\EDIT

      rules: {
        label: [{ required: true, message: '必填字段', trigger: 'blur' }],
        rangeDate: [{ required: true, message: '必填字段', trigger: 'change' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
      },
    }
  },
  computed: {

  },
  watch: {

  },
  created () {

  },

  methods: {

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      findAdeHolidayPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = {
            id: this.dlgData.id,
            label: this.dlgData.label,
            startDate: this.dlgData.rangeDate[0],
            endDate: this.dlgData.rangeDate[1],
          }
          postParam.year = postParam.startDate.substr(0, 4)
          postParam.month = postParam.startDate.substr(5, 2)
          this.dlgLoading = true
          saveOrUAdeHoliday(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgData.rangeDate = [data.startDate, data.endDate]
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })

    },

    // 删除
    delItem (data) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAdeHolidayById(data.id).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })

    },
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>


