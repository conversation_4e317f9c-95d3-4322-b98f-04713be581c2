<template>
  <!--类型名称-->
  <div class="tab-panel">
    <div class="filter-container">
      <el-form ref="searchForm" class='n-search' label-width="90px" @submit.native.prevent>
        <div class="n-search-bar">
          <div class='n-search-item n-search-item-r fr'>
            <el-select v-model="listQuery.type" clearable style="width: 160px" placeholder="类型">
              <el-option v-for="item of typeList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
            <el-input v-model="listQuery.str" placeholder='请输入金额/小时'>
              <i @click="resetStr" slot="suffix" class="el-input__icon el-icon-error"></i>
            </el-input>
            <el-button icon='el-icon-search' type="success" size='mini' style="height: 28px" @click="searchItem">搜索</el-button>
            <el-button type="primary" size='mini' style="height: 28px" @click="resetItem">重置</el-button>
            <el-button icon='el-icon-plus' type="primary" size='mini' style="height: 28px" @click="addItem">新增</el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="陪诊陪检名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="陪诊陪检类型">
          <template slot-scope="scope">
            <span>{{ scope.row.typeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="小时">
          <template slot-scope="scope">
            <span>{{ scope.row.pzHour }}</span>
          </template>
        </el-table-column>

        <el-table-column label="金额">
          <template slot-scope="scope">
            <span>{{ scope.row.pzMoney }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="primary" size='mini' style="height: 28px" @click="editItem(scope.row, scope.$index)">编辑</el-button>
            <el-button type="danger" size='mini' style="height: 28px" @click="delItem(scope.row, scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="dlgTitle" :visible.sync="dlgShow">
      <el-form :model="dlgData" ref="ruleForm" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="小时">
              <el-input-number v-model="dlgData.hour" :min="0" :max="24" label="请输入时长"></el-input-number>
              小时
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="金额">
              <el-input-number v-model="dlgData.fee" :min="0" :max="99999" label="请输入金额"></el-input-number>
              元
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="类型">
              <el-radio v-for="item in typeList" :key="item.id" v-model="dlgData.type" :label="item.id">{{item.name}}</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false">取 消</el-button>
        <el-button type="primary" @click="submitDialog()">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination' // 分页
import { getDataDict } from "@/utils";
import { findPzConfigLike, savePzConfig, delPzConfig } from "@/api/platformManSystem/systemConfig/accompanyMan"

export default {
  components: {
    Pagination
  },
  data () {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),
      list: [],
      listQuery: {
        str: "",
        projectId: '',
        type: '',
        page: 1,
        size: 20,
      },
      typeList: [
        {
          id: "1",
          name: "陪诊陪检"
        },
        {
          id: "2",
          name: "申请加时"
        }
      ],
      total: 0,
      listLoading: false,
      dlgTitle: "",
      dlgShow: false,
      dlgType: "",
      dlgData: {
        hour: '',
        fee: '',
        type: ''
      },
      rules: {

      },
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.list = []
      this.listLoading = true;
      this.listQuery['projectId'] = this.userInfo['projectId']
      findPzConfigLike(this.listQuery).then(res => {
        this.listLoading = false;
        let code = res.data.code;
        let msg = res.data.msg;
        if (code === "200") {
          let data = res.data.data;
          if (data === null) {
            this.$message({
              type: "warning",
              message: msg
            });
            return false
          }
          this.total = data.total
          this.list = res.data.list;
        }
        else {
          this.$message.error(msg);
        }
      });
    },
    searchItem () {
      this.getList()
    },
    resetItem () {
      this.listQuery.str = ''
      this.listQuery.type = ''
      this.getList()
    },
    resetStr () {
      this.listQuery.str = ''
      this.getList()
    },
    resetDlgData () {
      this.dlgData['hour'] = 1
      this.dlgData['fee'] = 1
      this.dlgData['type'] = "1"
    },
    addItem () {
      this.resetDlgData()
      this.dlgShow = true
      this.dlgTitle = '新增陪诊陪检类型'
      this.dlgType = "add"
    },
    editItem (data, idx) {
      this.dlgTitle = '编辑陪诊陪检类型'
      this.dlgType = "edit"
      this.dlgData['id'] = data['id']
      this.dlgData['hour'] = data['pzHours']
      this.dlgData['fee'] = data['pzMoneys']
      this.dlgData['type'] = data['type'].toString()
      this.dlgShow = true
    },
    delItem (data, idx) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let post_data =
        {
          id: data['id']
        }
        delPzConfig(post_data).then(res => {
          let code = res.data.code;
          let msg = res.data.msg;
          if (code === "200") {
            this.$message({
              type: "success",
              message: msg
            });
            this.list.splice(idx, 1)
          }
          else {
            this.$message.error(msg);
          }
        })
      }).catch(() => {

      });
    },
    submitDialog () {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let typeItem = this.typeList.filter((item) => {
            return item['id'] == this.dlgData.type
          })[0]
          let post_data =
          {
            pzHour: this.dlgData.hour,
            pzMoney: this.dlgData.fee,
            type: this.dlgData.type,
            typeText: typeItem['name']
          }
          if (this.dlgType === "add") {
            post_data['id'] = "0"
          }
          else {
            post_data['id'] = this.dlgData.id
          }
          savePzConfig(post_data).then(res => {
            let code = res.data.code;
            let msg = res.data.msg;
            if (code === "200") {
              this.$message({
                type: "success",
                message: msg
              });
              this.dlgShow = false
              this.getList()
            }
            else {
              this.$message.error(msg);
            }
          })
        }
      });
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.app-container {
  .n-search-item-r {
    margin-bottom: 18px;
  }
}
</style>