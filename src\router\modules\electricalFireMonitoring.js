/** 电气火灾监控 **/

import Layout from "@/views/layout/Layout";

const electricalFireMonitoringRouter = {
  path: "/electricalFireMonitoring",
  component: Layout,
  name: "electricalFireMonitoring",
  meta: {
    title: "电气火灾",
    icon: "dqhzjk",
    roles: ["dian<PERSON><PERSON><PERSON>aijiankong"]
  },
  children: [
    {
      path: "detectionPlatform",
      component: () =>
        import("@/views/electricalFireMonitoring/detectionPlatform/index"),
      name: "监测大屏",
      meta: {
        title: "监测大屏",
        roles: ["dianqi_shouye"]
      }
    },

    {
      path: "electricitySafety",
      component: () =>
        import("@/views/electricalFireMonitoring/electricitySafety"),
      name: "用电监测",
      meta: {
        title: "用电监测",
        roles: ["dianqi_yongdiananquan"]
      }
    },

    {
      path: "smokeDetector",
      component: () => import("@/views/electricalFireMonitoring/smokeDetector"),
      name: "烟雾监测",
      meta: {
        title: "烟雾监测",
        roles: ["dianqi_yangan"]
      }
    },
    // {
    //   path: "gas",
    //   component: () => import("@/views/electricalFireMonitoring/gas"),
    //   name: "燃气",
    //   meta: {
    //     title: "燃气",
    //     roles: ["dianqi_ranqi"]
    //   }
    // },
    {
      path: "waterImmersion",
      component: () =>
        import("@/views/electricalFireMonitoring/waterImmersion"),
      name: "漏水监测",
      meta: {
        title: "漏水监测",
        roles: ["dianqi_shuijin"]
      }
    },
    // ///////
    
    {
      path: "temperatureHumidityEquipment",
      component: () =>
        import("@/views/electricalFireMonitoring/temperatureHumidityEquipment"),
      name: "温湿度监测",
      meta: {
        title: "温湿度监测",
        roles: ["dianqi_wenshidushebei"]
      }
    },
    {
      path: "airSwitchEquipment",
      component: () =>
        import("@/views/electricalFireMonitoring/airSwitchEquipment"),
      name: "空开监测",
      meta: {
        title: "空开监测",
        roles: ["dianqi_kongkaishebei"]
      }
    },
    {
      path: "fireCameraMonitoring",
      component: () => import("@/views/equipSafeMan/fireCameraMonitoring"),
      name: "烟火监控",
      meta: {
        title: "烟火监控",
        roles: ["kongyanjiankong"]
      }
    },

    {
      path: "abnormalAlarmMan",
      component: () => import("@/views/equipSafeMan/abnormalAlarmMan"),
      name: "报警管理",
      meta: {
        title: "报警管理",
        roles: ["dianqi_baojingguanli"]
      }
    },

    {
      path: "sensorEquip",
      component: () => import("@/views/equipSafeMan/sensorEquip"),
      name: "传感器管理",
      meta: {
        title: "传感器管理",
        roles: ["dianqi_chuanganqiguanli"]
      }
    },
    {
      path: "cameraManagement",
      component: () => import("@/views/equipSafeMan/cameraManagement"),
      name: "摄像机管理",
      meta: {
        title: "摄像机管理",
        roles: ["dianqi_chuanganqiguanli"]
      }
    },

    // 、、、、
    {
      path: "statisticalAnalysis",
      component: () =>
        import("@/views/electricalFireMonitoring/statisticalAnalysis/index"),
      meta: {
        title: "统计分析",
        roles: ["dianqi_tongjifenxi"]
      },
      children: [

        {
          path: "warnInfoQuery",
          component: () =>
            import("@/views/electricalFireMonitoring/statisticalAnalysis/warnInfoQuery"),
          name: "报警信息查询",
          meta: {
            title: "报警信息查询",
            roles: ["dianqi_baojingxinxichaxun"]
          },
          children: []
        },
        {
          path: "alarmStatisticalAnalysis",
          component: () =>
            import("@/views/electricalFireMonitoring/statisticalAnalysis/alarmStatisticalAnalysis"),
          name: "报警信息统计",
          meta: {
            title: "报警信息统计",
            roles: ["dianqi_baojingtongjifenxi"]
          },
          children: []
        },
        {
          path: "alarmReminderRecord",
          component: () =>
            import("@/views/electricalFireMonitoring/statisticalAnalysis/alarmReminderRecord/index"),
          name: "报警提醒记录",
          meta: {
            title: "报警提醒记录",
            roles: ["dianqi_baojingtixingjilu"]
          },
          children: [],
        },
        {
          path: "controlOperationLog",
          component: () =>
            import("@/views/electricalFireMonitoring/statisticalAnalysis/controlOperationLog"),
          name: "控制操作日志",
          meta: {
            title: "控制操作日志",
            roles: ["dianqi_kongzhicaozuorizhi"]
          },
          children: []
        }
      ]
    }
  ]
};

export default electricalFireMonitoringRouter;
