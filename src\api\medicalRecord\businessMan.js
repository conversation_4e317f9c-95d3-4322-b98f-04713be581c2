import request from '@/utils/request'
// ------ << 业务办理 ------
// << 病历邮寄
// 列表
export function caseMailList(data) {
  return request({
    url: `/case/mailInfo/caseMailList`,
    method: 'post',
    data
  })
}
// 确认邮寄
export function caseMailConfirm(data) {
  return request({
    url: `/case/mailInfo/caseMailConfirm`,
    method: 'post',
    data
  })
}
// 转邮寄
export function mailInfoCaseOff({
  orderId,
  projectId
}) {
  return request({
    url: `/case/mailInfo/caseOff/${orderId}/${projectId}`,
    method: 'get'
  })
}

// >> 病历邮寄

// << 预约领取办理
// 读取身份证
export function getIDCardMsg() {
  return request({
    url: `/api8080/ReadMsg?cardImg=1`,
    method: 'get'
  })
}
// 领取确认
export function confirmSelfinfonew(data) {
  return request({
    url: `/case/print/confirmSelfinfonew`,
    method: 'post',
    data
  })
}
// >> 预约领取办理
// << 预约领取查询
// 列表
export function selfinfonewList(data) {
  return request({
    url: `/case/print/selfinfonewList`,
    method: 'post',
    data
  })
}
// >> 预约领取查询

// ------ >> 业务办理 ------

// ------ << 业务管理 ------
// << 邮寄信息查询
// 列表
export function mailQueryList(data) {
  return request({
    url: `/case/mailInfo/mailQueryList`,
    method: 'post',
    data
  })
}
// 根据运单号查物流
export function getEMSInfo({
  wayBillNo
}) {
  return request({
    url: `/case/mailInfo/getEMSInfo/${wayBillNo}`,
    method: 'get'
  })
}

// 

// EMS 月结
export function mailInfoList(data) {
  return request({
    url: `/case/mailInfo/list`,
    method: 'post',
    data
  })
}

// EMS月结-查询总金额
export function queryAmountTotal(data) {
  return request({
    url: `/case/mailInfo/queryAmountTotal`,
    method: 'post',
    data
  })
}

// 月结查询
export function printMonthList(data) {
  return request({
    url: `/case/print/monthList`,
    method: 'post',
    data
  })
}

// 邮寄审核 列表
export function auditPrintList(data) {
  return request({
    url: `/case/print/auditPrintList`,
    method: 'post',
    data
  })
}

// 邮寄审核/预约领取列表-单条详细
export function getOrderDetailInfo(orderId) {
  return request({
    url: `/case/print/getOrderDetailInfo/${orderId}`,
    method: 'get'
  })
}
export function auditPrint(data) {
  return request({
    url: `/case/print/auditPrint/${data.orderId}/${data.type}/${data.projectId}/${data.userName}`,
    method: 'get',
    data
  })
}

// 业务办理-邮寄审核/预约领取办理 -发送客服 功能
export function sendTelService(data) {
  return request({
    url: `/case/print/sendTelService`,
    method: 'post',
    data
  })
}

// 业务办理-邮寄审核/预约领取办理 -审核不通过 功能
export function printReject(data) {
  return request({
    url: `/case/print/reject`,
    method: 'post',
    data
  })
}

// 业务办理-邮寄审核/预约领取办理 -取消审核 功能
export function auditCancel(orderId, type, projectId) {
  return request({
    url: `/case/mailInfo/auditCancel/${orderId}/${type}/${projectId}`,
    method: 'get'
  })
}
// 邮寄审核-通过校验是否补缴
export function paymentCheck(orderId) {
  return request({
    url: `/case/paymentCheck/${orderId}`,
    method: 'get'
  })
}
// 否，调用之前接口
// 是，调用新接口
export function paymentOrderCreate(orderId) {
  return request({
    url: `/case/paymentOrderCreate/${orderId}`,
    method: 'get'
  })
}

// << 打印机盘点
// 列表
export function casePrintPrintList(data) {
  return request({
    url: `/case/print/printList`,
    method: 'post',
    data
  })
}
// 添加，修改，删除
export function casePrintSaveOrUpdate(data) {
  return request({
    url: `/case/print/saveOrUpdate`,
    method: 'post',
    data
  })
}
// >> 打印机盘点

// << 货位管理
// 列表
export function caseHuowinfoList(data) {
  return request({
    url: `/case/huowinfo/list`,
    method: 'post',
    data
  })
}
// 新增/修改
export function caseHuowinfoSaveOrUpdate(data) {
  return request({
    url: `/case/huowinfo/saveOrUpdate`,
    method: 'post',
    data
  })
}
// >> 货位管理
// << 业务-作废纸张管理
// 列表
export function casePaperScrapList(data) {
  return request({
    url: `/case/paperScrap/list`,
    method: 'post',
    data
  })
}
// 新增/修改
export function casePaperScrapSaveOrUpdate(data) {
  return request({
    url: `/case/paperScrap/saveOrUpdate`,
    method: 'post',
    data
  })
}
// >> 业务-作废纸张管理

// << 业务-日结盘点
export function detailedDaySettleList(data) {
  return request({
    url: `/case/detailed/daySettleList`,
    method: 'post',
    data
  })
}
// 新增/修改
export function detailedSaveOrUpdate(data) {
  return request({
    url: `/case/detailed/saveOrUpdate`,
    method: 'post',
    data
  })
}
// >> 业务-日结盘点


// << 业务-日结明细
export function detailedDaySettleQueryList(data) {
  return request({
    url: `/case/detailed/daySettleQueryList`,
    method: 'post',
    data
  })
}
// >> 业务-日结明细

// << 日结汇总
export function daySettleSumList(data) {
  return request({
    url: `/case/detailed/daySettleSumList`,
    method: 'post',
    data
  })
}
// >> 日结汇总

// 结退查询
export function refundList(data) {
  return request({
    url: `/case/refund/list`,
    method: 'post',
    data
  })
}

// >> 业务-结算补缴
// >> 查询患者订单
export function findOrderByUserIdPage(data) {
  return request({
    url: `/case/findOrderByUserIdPage`,
    method: 'post',
    data
  })
}
// >> 查询患者订单
// >> 修改订单备注
export function updateOrdersRemark(data) {
  return request({
    url: `/case/updateOrdersRemark`,
    method: 'post',
    data
  })
}

// 查询病案状态
export function findBlztByZyh(id) {
  return request({
    url: `/case/detailed/findBlztByZyh/${id}`,
    method: 'get'
  })
}

// >> 邮寄办理
// 邮寄导出列表
export function mailList(data) {
  return request({
    url: `/case/mailInfo/mailList`,
    method: 'post',
    data
  })
}

// 邮寄审核 修改身份证
export  function updateCertificateNumByZyh(orderNo, zyh){
  return request({
    url: `/case/detailed/updateCertificateNumByZyh/${orderNo}/${zyh}`,
    method: 'get'
  })
}


// 待邮寄查询
export function waitMailQueryList(data) {
  return request({
    url: `/case/mailInfo/waitMailQueryList`,
    method: 'post',
    data
  })
}

// >> 邮寄办理

// << 客服 异常处理
export function manageTelService(data) {
  return request({
    url: `/case/print/manageTelService`,
    method: 'post',
    data
  })
}
// 结算退款
export function wxpayRefund(data) {
  return request({
    url: `/case/wxpay/refund`,
    method: 'post',
    data
  })
}
// >> 客服 异常处理
// << 手写板，摄像头
// 传递摄像头像素
export function refundXztu(data) {
  return request({
    url: `/case/refund/xztu`,
    method: 'post',
    data
  })
}

// 摄像头像素 转 base64
export function xiangsuzhuanBase(data) {
  return request({
    url: `/case/refund/xiangsuzhuanBase`,
    method: 'post',
    data
  })
}

// >> 手写板，摄像头

// 客服催缴
export function kfcjOrder(orderNo) {
  return request({
    url: `/case/kfcjOrder/${orderNo}`,
    method: 'get'
  })
}

// 查询审核不通过订单的客服记录
export function findCustomerServiceRecord(orderNo) {
  return request({
    url: `/case/findCustomerServiceRecord/${orderNo}`,
    method: 'get'
  })
}

// 审核不通过订单-客服记录
export function saveCustomerServiceRecord(data) {
  return request({
    url: `/case/saveCustomerServiceRecord`,
    method: 'post',
    data
  })
}