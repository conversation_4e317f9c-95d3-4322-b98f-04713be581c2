<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' title="选择节假日" :visible.sync="hddState" width='1000px' append-to-body>
      <div class="dialog-hdd-bar">
        <el-table class='m-small-table' ref="multipleTable" v-loading="listLoading" :key="tableKey" :data="list" border fit highlight-current-row @selection-change="handleSelectionChange" style="width: 100%;" @sort-change="sortChange">
          <el-table-column align="center" type="selection" width="55">
          </el-table-column>
          <el-table-column label="序号" prop="index" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.index }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column label="员工ID" width="100px">
            <template slot-scope="scope">
              <span>{{ scope.row.id }}</span>
            </template>
          </el-table-column> -->

          <el-table-column label="节日名称" width="120px">
            <template slot-scope="scope">
              <span>{{ scope.row.label }}</span>
            </template>
          </el-table-column>

          <el-table-column label="节日日期">
            <template slot-scope="scope">
              <span>{{ scope.row.timeRange }}</span>
            </template>
          </el-table-column>

          <el-table-column label="节日类型" width="120px">
            <template slot-scope="scope">
              <span>{{ scope.row.type }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" style="padding-bottom: 0px; padding-top: 0px;" />
        <div class="clear"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <!-- <span class='dialog-footer-span' v-show='mNames'>当前选中：{{ mNames }}</span> -->
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="primary" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findAdeHolidayAll } from '@/api/attendanceGroupMan'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination
import { parseTime, num2week } from '@/utils'
// import adminDashboard from './admin'
// import editorDashboard from './editor'

export default {
  components: {
    Pagination
  },
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      isShow: false,

      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        size: 200
      },
      isFirst: true,

      mIds: '',
      mNames: '',

    }
  },
  computed: {
    ...mapGetters([
      'role'
    ]),
    hddState: {
      get: function () {
        let hddState = this.$store.getters.hddState
        if (hddState === true) {
          if (this.isShow === false) {
            this.isFirst = true
            this.getList()
            this.isShow = true
          }

        }
        return this.$store.getters.hddState
      },
      set: function (newVal) {
        this.$store.commit('SET_HDDSTATE', newVal)
      }
    },
    hddIds: {
      get: function () {
        // alert(this.$store.getters.hddIds)
        let ids = this.$store.getters.hddIds
        this.mIds = ids
        return ids
      },
      set: function (newVal) {

      }
    },
    hddNames: {
      get: function () {
        let names = this.$store.getters.hddNames
        this.mNames = names
        return this.$store.getters.hddNames
      },
      set: function (newVal) {
        // this.$store.commit('SET_HDDIDS', newVal)
        // this.$store.commit('SET_HDDNAMES', newVal)
      }
    }
  },
  watch: {

  },
  created() {

    // 部门
    // this.getList()
  },
  methods: {
    // 获取数据
    getList() {
      this.listLoading = true
      findAdeHolidayAll(this.listQuery).then(res => {
        let code = res.data.code
        let data = res.data.data
        let list = res.data.list
        let msg = res.data.msg

        this.total = data.total
        // this.total = 10

        for (let i = 0; i < list.length; i++) {
          let item = list[i]
          item.index = (this.listQuery.page - 1) * this.listQuery.size + i + 1

          let startDate = item.startDate.substr(0, 10)
          let endDate = item.endDate.substr(0, 10)

          let startWeek = num2week(item.startWeek)
          let endWeek = num2week(item.endWeek)

          item.timeRange = `${startDate}(${startWeek}) 至 ${endDate}(${endWeek})`

        }
        this.list = JSON.parse(JSON.stringify(list))
        this.listLoading = false

        this.isFirst = true

        setTimeout(() => {
          let mIds = ''
          let mNames = ''
          for (let item1 of this.list) {
            // console.log(item1.id)
            // console.log(this.hddIds)
            // console.log(this.hddIds.indexOf(item1.id))

            if (this.hddIds.indexOf(item1.id) >= 0) {
              this.$refs.multipleTable.toggleRowSelection(item1);
              mIds += item1.id + ','
              mNames += item1.label + ','
            }

          }
          mIds = mIds.substr(0, mIds.length - 1)
          mNames = mNames.substr(0, mNames.length - 1)

          this.mIds = mIds
          this.mNames = mNames
        }, 100)

        setTimeout(() => {
          this.isFirst = false
        }, 500)
      })
    },
    // 表格点击事件
    // hddNames
    // hddIds
    handleSelectionChange(selectArr) {
      if (this.isFirst) {
        return false
      }

      let mIds = ''
      let mNames = ''
      for (let item of selectArr) {
        mIds += item.id + ','
        mNames += item.label + ','
      }
      mIds = mIds.substr(0, mIds.length - 1)
      mNames = mNames.substr(0, mNames.length - 1)
      // if (selectArr.length === this.list.length) {
      //   hddNames = '全部'
      // } else {
      //   mNames = mNames.substr(0, mNames.length - 1)
      // }
      this.mIds = mIds
      this.mNames = mNames
    },

    // 选择部门提交
    bumenOkFunc() {
      this.$store.commit('SET_HDDIDS', this.mIds)
      this.$store.commit('SET_HDDNAMES', this.mNames)
      setTimeout(() => {
        this.$store.commit('SET_HDDIDS', this.mIds)
        this.$store.commit('SET_HDDNAMES', this.mNames)
        this.closeDialog()
      }, 50)
    },

    // 关闭弹窗 
    closeDialog() {
      this.hddState = false
      this.isShow = false
      this.$store.commit('SET_HDDSTATE', false)
    },

    // 排序
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'id') {
        this.sortByID(order)
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id'
      } else {
        this.listQuery.sort = '-id'
      }
      this.handleFilter()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss"></style>