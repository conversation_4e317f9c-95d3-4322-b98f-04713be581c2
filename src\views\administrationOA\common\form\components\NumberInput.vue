<template>
  <div>
    <div v-if="mode === 'DESIGN'">
      <el-input
        size="medium"
        disabled
        :placeholder="placeholder"
        type="number"
      />
    </div>
    <div v-else-if="mode === 'PC' && !readonly">
      <!-- <el-input
        v-model="_value"
        size="medium"
        clearable
        :placeholder="placeholder"
        type="number"
      /> -->
      <el-input
        v-model="_value"
        size="medium"
        clearable
        :placeholder="placeholder"
        @blur="valBlurFunc"
      />
    </div>
    <div v-else-if="mode === 'MOBILE' && !readonly">
      <field
        @blur="valBlurFunc"
        v-model="_value"
        type="number"
        :placeholder="placeholder"
      />
    </div>
    <div v-else>
      {{ _value }}
    </div>
    <!--    <div v-else>
      <field readonly clickable v-model="_value" type="number" @click="showKey = true" :placeholder="placeholder"></field>
      <number-keyboard v-model="_value" theme="custom" :extra-key="['-', '.']"
                       close-button-text="完成" :show="showKey" :maxlength="15" @blur="showKey = false"/>
    </div>-->
  </div>
</template>

<script>
import { Field, NumberKeyboard } from "vant";
import "vant/lib/field/style";
import componentMinxins from "../ComponentMinxins";
import * as utils from "@/utils";

export default {
  mixins: [componentMinxins],
  name: "NumberInput",
  components: { Field, NumberKeyboard },
  props: {
    value: {
      // type: Number,
      // default: undefined
    },
    placeholder: {
      type: String,
      default: "请输入数值"
    },
    precision: {
      type: Number,
      default: 0
    }
  },

  computed: {
    _value0: {
      get() {
        console.log("====this.value", this.value);
        this._value = this.parseNumber(this.value);
        return this.parseNumber(this.value);
      },
      set(val) {
        // console.log("===val111", val);
        // this.$emit("input", this.parseNumber(val));
      }
    }
  },
  data() {
    return {
      showKey: false
      // _value: "",
      // mVal: ""
    };
  },

  methods: {
    valBlurFunc() {
      console.log("===val0", this._value);
      console.log("==this.precision", this.precision);
      let val = utils.num2Round(this._value, this.precision);
      console.log("===val", val);
      // this._value = val;
      this.$emit("input", val);
    },

    parseNumber(val) {
      console.log("===val222", val);
      if (typeof val === "string") {
        return this.precision > 0
          ? parseFloat(parseFloat(val).toFixed(this.precision))
          : parseInt(val);
      }
      return val;
    },
    isNaN(value) {
      return typeof value === "number" && isNaN(value);
    }
  }
};
</script>

<style scoped></style>
