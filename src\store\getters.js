const getters = {
  sidebar: state => state.app.sidebar,
  language: state => state.app.language,
  size: state => state.app.size,
  // showAnim: state => state.app.showAnim,

  device: state => state.app.device,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  introduction: state => state.user.introduction,
  status: state => state.user.status,
  roles: state => state.user.roles,
  setting: state => state.user.setting,
  permission_routers: state => state.permission.routers,
  addRouters: state => state.permission.addRouters,
  errorLogs: state => state.errorLog.logs,

  // tab 切换关联页面
  kqzTabIndex: state => state.tabStore.kqzTabIndex, // 考勤组设置
  kqzTypeId: state => state.tabStore.kqzTypeId,

  yhqxTabIndex: state => state.tabStore.yhqxTabIndex, // 用户权限
  yhqxTypeId: state => state.tabStore.yhqxTypeId,

  // 组织机构
  vueOrgData: state => state.VueOrg.vueOrgData,
  vueOrgCanExport: state => state.VueOrg.vueOrgCanExport,
  vueOrgDirection: state => state.VueOrg.vueOrgDirection,

  // 部门 tree
  bmTreeState: state => state.bmTree.bmTreeState,
  bmTreeIsRole: state => state.bmTree.bmTreeIsRole,
  bmTreeBranchId: state => state.bmTree.bmTreeBranchId,
  bmTreeBranchName: state => state.bmTree.bmTreeBranchName,

  // 部门 tree
  bmTreeState1: state => state.bmTree1.bmTreeState1,
  bmTreeIsRole1: state => state.bmTree1.bmTreeIsRole1,
  bmTreeBranchId1: state => state.bmTree1.bmTreeBranchId1,
  bmTreeBranchName1: state => state.bmTree1.bmTreeBranchName1,

  // 部门多选
  bmsState: state => state.BmsDialog.bmsState,
  bmsArr: state => state.BmsDialog.bmsArr,
  bmsType: state => state.BmsDialog.bmsType,
  bmsOtherQuery: state => state.BmsDialog.bmsOtherQuery,

  // 核算部门弹窗多选，比例
  hsbmsState: state => state.HsbmsDialog.hsbmsState,
  hsbmsArr: state => state.HsbmsDialog.hsbmsArr,

  // 核算部门单选
  hsbmState: state => state.HsbmDialog.hsbmState,
  hsbmId: state => state.HsbmDialog.hsbmId,
  hsbmName: state => state.HsbmDialog.hsbmName,

  // 岗位多选
  gwsState: state => state.GwsDialog.gwsState,
  gwsArr: state => state.GwsDialog.gwsArr,

  // 岗位 tree
  gwTreeState: state => state.gwTree.gwTreeState,
  gwTreeBranchId: state => state.gwTree.gwTreeBranchId,
  gwTreeBranchName: state => state.gwTree.gwTreeBranchName,
  gwTreePostId: state => state.gwTree.gwTreePostId,
  gwTreePostName: state => state.gwTree.gwTreePostName,
  gwStr: state => state.gwTree.gwStr,
  gwTreeSqType: state => state.gwTree.gwTreeSqType,

  // 节假日弹窗
  hddState: state => state.holidayDialog.hddState,
  hddIds: state => state.holidayDialog.hddIds,
  hddNames: state => state.holidayDialog.hddNames,

  // 地图弹窗
  mapDialogIsFirst: state => state.mapDialog.mapDialogIsFirst,
  mapDialogState: state => state.mapDialog.mapDialogState,
  mapDialogLng: state => state.mapDialog.mapDialogLng,
  mapDialogLat: state => state.mapDialog.mapDialogLat,
  mapDialogStr: state => state.mapDialog.mapDialogStr,
  mapDialogJWD: state => state.mapDialog.mapDialogJWD,

  // 考勤组id
  kqState: state => state.kqDialog.kqState,
  kqId: state => state.kqDialog.kqId,
  kqName: state => state.kqDialog.kqName,

  // 图片放大
  imgEnlargeState: state => state.Imgenlarge.imgEnlargeState,
  imgEnlargeUrl: state => state.Imgenlarge.imgEnlargeUrl,
  imgEnlargeIsProcess: state => state.Imgenlarge.imgEnlargeIsProcess,

  // 根据 部门 选员工
  userTreeState: state => state.Usertree.userTreeState,
  userTreeBranchId: state => state.Usertree.userTreeBranchId,
  userTreeBranchName: state => state.Usertree.userTreeBranchName,
  userTreeUserId: state => state.Usertree.userTreeUserId,
  userTreeUserName: state => state.Usertree.userTreeUserName,
  userTreeSqType: state => state.Usertree.userTreeSqType,

  // 多选员工表格
  usersTableState: state => state.UsersTable.usersTableState,
  usersTableBranchId: state => state.UsersTable.usersTableBranchId,
  usersTableBranchName: state => state.UsersTable.usersTableBranchName,
  usersTableUserIds: state => state.UsersTable.usersTableUserIds,
  usersTableUserNames: state => state.UsersTable.usersTableUserNames,

  // 选择 快递员工
  dmanTableState: state => state.Dmantable.dmanTableState,
  dmanTableBranchId: state => state.Dmantable.dmanTableBranchId,
  dmanTableBranchName: state => state.Dmantable.dmanTableBranchName,
  dmanTableUserId: state => state.Dmantable.dmanTableUserId,
  dmanTableUserName: state => state.Dmantable.dmanTableUserName,
  dmanTableSqType: state => state.Dmantable.dmanTableSqType,

  // 员工多选弹窗
  usersState: state => state.Usersdialog.usersState,
  usersArr: state => state.Usersdialog.usersArr,

  // 部门或者员工多选弹窗
  usersOrBranState: state => state.UsersOrBranDialog.usersOrBranState,
  usersOrBranArr: state => state.UsersOrBranDialog.usersOrBranArr,
  usersOrBranArr2: state => state.UsersOrBranDialog.usersOrBranArr2,
  usersOrBranType: state => state.UsersOrBranDialog.usersOrBranType,
  usersOrBranTypeDis: state => state.UsersOrBranDialog.usersOrBranTypeDis,
  usersOrBrancanSelectBm: state =>
    state.UsersOrBranDialog.usersOrBrancanSelectBm,
  usersOrBranSqType: state => state.UsersOrBranDialog.usersOrBranSqType,
  // usersOrBranSync: state => state.UsersOrBranDialog.usersOrBranSync,

  // 人脸录入弹窗
  faceUserId: state => state.Facedialog.faceUserId,
  faceIsDesc: state => state.Facedialog.faceIsDesc,
  faceCanRefresh: state => state.Facedialog.faceCanRefresh,
  faceChouchaState: state => state.Facedialog.faceChouchaState,
  faceType: state => state.Facedialog.faceType,
  faceState: state => state.Facedialog.faceState,
  faceId: state => state.Facedialog.faceId,
  facePunshId: state => state.Facedialog.facePunshId,
  faceLabel: state => state.Facedialog.faceLabel,
  facePLabel: state => state.Facedialog.facePLabel,
  faceWorkNum: state => state.Facedialog.faceWorkNum,
  faceBLabel: state => state.Facedialog.faceBLabel,
  faceInputCauseType: state => state.Facedialog.faceInputCauseType,
  faceIdNumber: state => state.Facedialog.faceIdNumber,
  faceImg1: state => state.Facedialog.faceImg1,
  faceImg2: state => state.Facedialog.faceImg2,
  faceIsZdjg: state => state.Facedialog.faceIsZdjg,

  // 右侧切换 标签页
  navTagActive: state => state.NavTag.navTagActive,
  navTagArr: state => state.NavTag.navTagArr,

  //【【 流程管理 - 弹窗
  processRefreshPage: state => state.processMan.processRefreshPage, // 是否刷新页面
  processDialogType: state => state.processMan.processDialogType, // 弹窗状态
  processDialogIsZhihui: state => state.processMan.processDialogIsZhihui, // 知会状态
  processDialogJsonStr: state => state.processMan.processDialogJsonStr, // 弹窗数据
  processDialogTableKey: state => state.processMan.processDialogTableKey, // 弹窗 表单标识
  processDialogData: state => state.processMan.processDialogData, // 弹窗 表单数据

  zgdState: state => state.processMan.zgdState, // 增岗单
  gwqtdState: state => state.processMan.gwqtdState, // 岗位启停单
  jgdState: state => state.processMan.jgdState, // 减岗单
  tskqdState: state => state.processMan.tskqdState, // 特殊考勤申请单
  qxtskqdState: state => state.processMan.qxtskqdState, // 取消特殊考勤申请单
  gwhtdState: state => state.processMan.gwhtdState, // 岗位互调单

  // 员工关系
  rzsqdState: state => state.processMan.rzsqdState, // 入职申请单
  zzsqdState: state => state.processMan.zzsqdState, // 转正申请单
  tgtxdState: state => state.processMan.tgtxdState, // 调岗调薪单
  gwtxdState: state => state.processMan.gwtxdState, // 岗位调薪单
  jgsqdState: state => state.processMan.jgsqdState, // 兼岗申请单
  qxjgdState: state => state.processMan.qxjgdState, // 取消兼岗单
  sbsqdState: state => state.processMan.sbsqdState, // 社保申请单
  bgsqdState: state => state.processMan.bgsqdState, // 银行卡变更单
  lzsqdState: state => state.processMan.lzsqdState, // 离职申请单
  wckqdState: state => state.processMan.wckqdState, // 离职申请单
  // 考勤相关
  qjdState: state => state.processMan.qjdState, // 请假单
  xjdState: state => state.processMan.xjdState, // 销假单
  jbdState: state => state.processMan.jbdState, // 加班单
  cxdState: state => state.processMan.cxdState, // 串休单
  kqzsqdState: state => state.processMan.kqzsqdState, // 考勤组申请单
  kqycsbdState: state => state.processMan.kqycsbdState, // 考勤异常申报单

  // 薪酬相关
  jldState: state => state.processMan.jldState, // 奖励单
  kkdState: state => state.processMan.kkdState, // 扣款单
  bzdState: state => state.processMan.bzdState, // 补助单
  dfgzdState: state => state.processMan.dfgzdState, // 代发工资单
  zkfdState: state => state.processMan.zkfdState, // 暂/扣发单
  zfhfdState: state => state.processMan.zfhfdState, // 暂发恢复单

  // 物资物料
  wlsqdState: state => state.processMan.wlsqdState, // 物料申请单
  wllydState: state => state.processMan.wllydState, // 物料领用单

  // 财务业务
  jkdState: state => state.processMan.jkdState, // 借款单
  bxdState: state => state.processMan.bxdState, // 报销单
  // 】】 流程管理 - 弹窗

  // 【【 复选 部门树
  branchTreeState: state => state.Branchtree.branchTreeState,
  branchTreeKeys: state => state.Branchtree.branchTreeKeys,
  branchTreeNames: state => state.Branchtree.branchTreeNames,
  branchTreeHalfKeys: state => state.Branchtree.branchTreeHalfKeys,

  branchTreeNodes: state => state.Branchtree.branchTreeNodes,

  branchTreeKeysSet: state => state.Branchtree.branchTreeKeysSet,
  branchTreeNamesSet: state => state.Branchtree.branchTreeNamesSet,
  branchTreeIsSelectChild: state => state.Branchtree.branchTreeIsSelectChild,
  branchTreeIsRoot: state => state.Branchtree.branchTreeIsRoot,
  // 【【 复选 部门树

  // 【【 弹窗状态
  // 权限 部门树
  qxTreeState: state => state.dialogStore.qxTreeState,
  qxTreeKeys: state => state.dialogStore.qxTreeKeys,
  qxTreeNames: state => state.dialogStore.qxTreeNames,
  qxTreeHalfKeys: state => state.dialogStore.qxTreeHalfKeys,

  qxTreeNodes: state => state.dialogStore.qxTreeNodes,

  qxTreeKeysSet: state => state.dialogStore.qxTreeKeysSet,
  qxTreeNamesSet: state => state.dialogStore.qxTreeNamesSet,

  qxTreeIsSelectChild: state => state.dialogStore.qxTreeIsSelectChild,
  qxTreeIsRoot: state => state.dialogStore.qxTreeIsRoot,

  // 权限 部门树
  dpTreeState: state => state.dialogStore.dpTreeState,
  dpTreeKeys: state => state.dialogStore.dpTreeKeys,
  dpTreeNames: state => state.dialogStore.dpTreeNames,
  dpTreeHalfKeys: state => state.dialogStore.dpTreeHalfKeys,

  dpTreeNodes: state => state.dialogStore.dpTreeNodes,

  dpTreeKeysSet: state => state.dialogStore.dpTreeKeysSet,
  dpTreeNamesSet: state => state.dialogStore.dpTreeNamesSet,

  dpTreeIsSelectChild: state => state.dialogStore.dpTreeIsSelectChild,
  dpTreeIsRoot: state => state.dialogStore.dpTreeIsRoot,

  bpmnState: state => state.dialogStore.bpmnState, // 流程图
  bpmnIndex: state => state.dialogStore.bpmnIndex,
  bpmnType: state => state.dialogStore.bpmnType,
  bpmnXmlSet: state => state.dialogStore.bpmnXmlSet,
  bpmnXmlGet: state => state.dialogStore.bpmnXmlGet,

  userMsgNum: state => state.dialogStore.userMsgNum, // 用户未读消息集合
  userMsgNumXx: state => state.dialogStore.userMsgNumXx, // 消息未读
  userMsgNumTz: state => state.dialogStore.userMsgNumTz, // 通知未读
  userMsgNumGg: state => state.dialogStore.userMsgNumGg, // 公告未读

  // 供应链，单选，资产，商品，供应商
  gylTreeOneState: state => state.dialogStore.gylTreeOneState,
  gylTreeOneType: state => state.dialogStore.gylTreeOneType,
  gylTreeOneId: state => state.dialogStore.gylTreeOneId,
  gylTreeOneName: state => state.dialogStore.gylTreeOneName,

  // 供应链，多选 供应商
  gylTreeSelectsState: state => state.dialogStore.gylTreeSelectsState,
  gylTreeSelectsType: state => state.dialogStore.gylTreeSelectsType,
  gylTreeSelectsQuery: state => state.dialogStore.gylTreeSelectsQuery,
  gylTreeSelectsSet: state => state.dialogStore.gylTreeSelectsSet,
  gylTreeSelectsGet: state => state.dialogStore.gylTreeSelectsGet,

  // 供应链，多选商品
  gylGoodsState: state => state.dialogStore.gylGoodsState,
  gylGoodsType: state => state.dialogStore.gylGoodsType,
  gylGoodsArr: state => state.dialogStore.gylGoodsArr,
  gylGoodsArrSet: state => state.dialogStore.gylGoodsArrSet,

  // 规则配置
  rulePostState: state => state.dialogStore.rulePostState, // 弹窗状态
  rulePostQuery: state => state.dialogStore.rulePostQuery, // 查询条件
  rulePostSet: state => state.dialogStore.rulePostSet, // 设置的值
  rulePostGet: state => state.dialogStore.rulePostGet, // 选中的值

  //查询条件
  serviceManQuery: state => state.listQuery.serviceManQuery //调度中心
};
export default getters;
