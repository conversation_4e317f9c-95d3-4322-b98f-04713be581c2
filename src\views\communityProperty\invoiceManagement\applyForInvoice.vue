<template>
  <!-- 申请开票 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="关键字：">
          <el-input
            @keyup.enter.native="searchFunc"
            placeholder="请输入名称"
            v-model="listQuery.label"
          ></el-input>
        </el-form-item>
        <el-form-item label="选择小区:">
          <el-select
            v-model="listQuery.communityId"
            filterable
            clearable
            placeholder="请选择小区"
          >
            <el-option
              v-for="item in communityList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchFunc"
          >搜索</el-button
        >
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="费用主体" align="center" width="140">
          <template slot-scope="scope">
            <span>{{
              scope.row.roomName ||
              scope.row.parkingName ||
              scope.row.garageName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="业主姓名" align="center" width="90">
          <template slot-scope="scope">
            <span>{{ scope.row.ownerName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="业主手机号" align="center" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.ownerPhone }}</span>
          </template>
        </el-table-column>
        <el-table-column label="费用项目" align="center" width="240">
          <template slot-scope="scope">
            <span>{{ scope.row.configName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="应付金额" align="center"  width="110">
          <template slot-scope="scope">
            <span>{{ scope.row.receivableAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实付金额" align="center" width="110">
          <template slot-scope="scope">
            <span>{{ scope.row.receivedAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="缴费时间" align="center"  width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.payTime }}</span>
          </template>
        </el-table-column>
         <el-table-column label="缴费渠道" align="center" min-width="130">
          <template slot-scope="scope">
            <div v-if="scope.row.payInfoJsonStr">{{ scope.row.payInfoJsonStr}}</div>
            <div v-else>
              <span v-if="scope.row.payWay == 1">业主自助缴费</span>
              <span v-if="scope.row.payWay == 2"> 扫码支付</span>
              <span v-if="scope.row.payWay == 3&&scope.row.offlinePayWay==1">微信</span>
              <span v-if="scope.row.payWay == 3&&scope.row.offlinePayWay==2">支付宝</span>
              <span v-if="scope.row.payWay == 3&&scope.row.offlinePayWay==3">现金</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="发票状态"
          :formatter="formatState"
          prop="invoiceStatus"
          align="center"
          width="100"
        >
        </el-table-column>
        <el-table-column
          label="操作"
          width="240"
          header-align="center"
          align="left"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-more"
              plain
              @click="info(scope.row)"
              >详情</el-button
            >
            <el-button
              v-if="scope.row.invoiceStatus == 9"
              type="success"
              size="mini"
              icon="el-icon-check"
              plain
              @click="applyInvoice(scope.row)"
              >申请开票</el-button
            >
            <!-- <el-button
              v-if="scope.row.invoiceStatus == 99"
              type="success"
              size="mini"
              icon="el-icon-edit"
              plain
              @click="applyInvoice(scope.row)"
              >重新申请</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>
    <!-- 申请开票 -->
    <el-dialog
      title="申请开票"
      :visible.sync="dlgShow"
      width="600px"
      :close-on-click-modal="false"
      @closed="closeDlg"
    >
      <el-form :model="dlgData" :rules="rules" ref="informationChangeForm">
        <el-form-item label="">
          <span style="margin-right: 20px"
            ><span class="fontLable">费用主体: </span
            >{{
              dlgData.roomName || dlgData.garageName || dlgData.parkingName
            }}</span
          >
          <span
            ><span class="fontLable">业主姓名: </span
            >{{ dlgData.userName }}</span
          >
        </el-form-item>
        <el-form-item label="">
          <span style="margin-right: 20px"
            ><span class="fontLable">业主手机号:</span>
            {{ dlgData.phone }}</span
          >
          <span
            ><span class="fontLable">开票金额: </span>{{ dlgData.receivedAmount }}</span
          >
        </el-form-item>
        <el-form-item label="发票性质:" prop="invoiceNature">
          <el-radio-group v-model="dlgData.invoiceNature">
            <el-radio label="0">电子发票</el-radio>
            <el-radio label="1">纸质发票</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="历史抬头:" label-width="82px">
          <span> {{ oldInvoiceTitle }}&nbsp;&nbsp;&nbsp;</span>
        </el-form-item>
        <el-form-item label="发票类型:" prop="invoiceType">
          <el-radio-group v-model="dlgData.invoiceType">
            <el-radio label="0">普通发票</el-radio>
            <el-radio label="1" v-if="dlgData.isVatInvoice == 0"
              >增值税专用发票</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="抬头类型:" prop="titleType" v-if="dlgData.invoiceType!='1'">
          <el-radio-group v-model="dlgData.titleType" @change="titleTypeChange">
            <el-radio label="0">个人</el-radio>
            <el-radio label="1">单位</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票抬头" label-width="108px" prop="invoiceTitle">
          <el-input
           :disabled="dlgData.titleType=='0'&&dlgData.invoiceType!='1'"
            v-model="dlgData.invoiceTitle"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="dutyParagraph"
          label="单位税号"
          label-width="108px"
          :rules="
            dlgData.invoiceType == 1 || dlgData.titleType == 1
              ? [
                  {
                    required: true,
                    message: '请输入单位税号',
                    trigger: 'blur',
                  },
                  // { pattern: /[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}/, message: '请输入正确单位税号' }
                ]
              : []
          "
        >
          <el-input
            v-model="dlgData.dutyParagraph"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="bankName"
          label="开户银行名称"
          label-width="108px"
          :rules="
            dlgData.invoiceType == 1
              ? [
                  {
                    required: true,
                    message: '请输入银行名称',
                    trigger: 'blur',
                  },
                ]
              : []
          "
        >
          <el-input v-model="dlgData.bankName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="基本开户账号"
          prop="bankNum"
          label-width="108px"
          :rules="
            dlgData.invoiceType == 1
              ? [
                  {
                    required: true,
                    message: '请输入开户账号',
                    trigger: 'blur',
                  },
                ]
              : []
          "
        >
          <el-input v-model="dlgData.bankNum" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="地址"
          prop="address"
          label-width="108px"
          :rules="
            dlgData.invoiceType == 1
              ? [
                  {
                    required: true,
                    message: '请输入地址',
                    trigger: 'blur',
                  },
                ]
              : []
          "
        >
          <el-input v-model="dlgData.address" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="固定电话"
          label-width="108px"
          prop="telephone"
          :rules="
            dlgData.invoiceType == 1
              ? [
                  {
                    required: true,
                    message: '请输入固定电话',
                    trigger: 'blur',
                  },
                ]
              : []
          "
        >
          <el-input v-model="dlgData.telephone" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false">取 消</el-button>
        <el-button type="primary" @click="subDlg">提 交</el-button>
      </div>
    </el-dialog>

    <!-- 详情 -->
    <el-dialog
      :close-on-click-modal="false"
      title="费用信息"
      :visible.sync="infoShow"
      width="1200px"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :disabled="dlgType == 'VIEW'"
        :rules="rules"
        :model="dlgData"
        label-position="right"
        label-width="135px"
      >
        <el-divider>费用信息</el-divider>
        <el-row>
          <el-col :span="8">
            <el-form-item label="费用ID:">{{ dlgData.id }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费用标识：">{{
              dlgData.feeFlagName
            }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费用类型：">{{
              dlgData.feeName
            }}</el-form-item>
          </el-col>
        </el-row>
        <el-col :span="8">
          <el-form-item label="付费对象：">{{ dlgData.payName }}</el-form-item>
        </el-col>
        <el-row>
          <el-col :span="8">
            <el-form-item label="费用项：">{{
              dlgData.configName
            }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费用状态：">{{
              dlgData.status == 0 ? "未缴费" : "已缴费"
            }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="建账时间：">{{
              dlgData.createTime
            }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计费开始时间：">{{
              dlgData.startDate
            }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计费结束时间：">{{
              dlgData.endDate
            }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="说明：">{{ dlgData.remark }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优惠券:">{{ dlgData.couponMoney }}</el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="备注：">{{
              dlgData.orderRemark
            }}</el-form-item>
          </el-col>
        </el-row>

        <el-divider>缴费历史</el-divider>
        <el-table
          ref="dlgTableRef"
          key="id"
          class="m-small-table"
          :data="dlgData.list"
          border
          fit
          highlight-current-row
        >
          <el-table-column
            :selectable="dTDisFn"
            label="#"
            align="center"
            type="selection"
            width="50"
          >
          </el-table-column>

          <el-table-column label="费用类型">
            <template slot-scope="scope">
              <span>{{ scope.row.feeName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用项目">
            <template slot-scope="scope">
              <span>{{ scope.row.configName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用标识" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.feeFlagName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用摘要">
            <template slot-scope="scope">
              <span
                >{{ scope.row.startDate }} ~ {{ scope.row.endDate }}
                {{ scope.row.feeName }}
              </span>
            </template>
          </el-table-column>

          <el-table-column label="应收金额" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ num2Round(scope.row.receivableAmount) }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column label="实缴金额" align="center" width="110">
            <template slot-scope="scope">
              <span>{{
                num2Round(
                  scope.row.receivableAmount +
                    scope.row.zhinajin -
                    scope.row.preferentialAmount
                )
              }}</span>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="优惠申请" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ num2Round(scope.row.preferentialAmount) }}</span>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="优惠券">
            <template slot-scope="scope">
              <span>{{ scope.row.aaaa }}</span>
            </template>
          </el-table-column> -->

          <el-table-column label="费用金额">
            <template slot-scope="scope">
              <span>{{ scope.row.amount.toFixed(2) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="优惠金额">
            <template slot-scope="scope">
              <span>{{ scope.row.preferentialAmount }}</span>
            </template>
          </el-table-column>

          <el-table-column label="滞纳金" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ scope.row.zhinajin }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column label="审核状态" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.isAudit == 2" type="danger">已驳回</el-tag>
            </template>
          </el-table-column> -->
        </el-table>
      </el-form>

      <el-form
        :model="infoData"
        :rules="rules"
        v-if="invoiceStatus !== '9'"
        style="margin-top: 20px"
      >
        <el-form-item label="">
          <span class="information fontLable"
            >开票金额: {{ dlgData.amount }}</span
          >
        </el-form-item>
        <el-form-item label="发票性质:" label-width="95px">
          <el-radio-group v-model="infoData.invoiceNature" disabled>
            <el-radio label="0">电子发票</el-radio>
            <el-radio label="1">纸质发票</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票类型:" label-width="95px">
          <el-radio-group v-model="infoData.invoiceType" disabled>
            <el-radio label="0">普通发票</el-radio>
            <el-radio label="1" v-if="dlgData.isVatInvoice == 0"
              >增值税专用发票</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="抬头类型:" label-width="91px" v-if="infoData.invoiceType=='0'">
          <el-radio-group v-model="infoData.titleType" disabled >
            <el-radio label="0">个人</el-radio>
            <el-radio label="1">单位</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票抬头" label-width="90px">
          <el-input
            v-model="infoData.invoiceTitle"
            autocomplete="off"
            disabled
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="dutyParagraph"
          label="单位税号"
          label-width="90px"
          style="width: 340px"
        >
          <el-input
            v-model="infoData.dutyParagraph"
            autocomplete="off"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item prop="bankName" label="开户银行名称" label-width="118px">
          <el-input
            style="width: 220px"
            v-model="infoData.bankName"
            disabled
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="基本开户账号" prop="bankNum" label-width="118px">
          <el-input
            style="width: 220px"
            v-model="infoData.bankNum"
            disabled
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="地址" prop="address" label-width="61px">
          <el-input
            style="width: 280px"
            v-model="infoData.address"
            disabled
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="固定电话" label-width="90px" prop="telephone">
          <el-input
            style="width: 250px"
            v-model="infoData.telephone"
            disabled
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="infoShow = false" icon="el-icon-back"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  findPayBillSumPage,
  payInvoiceSave,
  getTitle,
  getInfo,
} from "@/api/invoiceManagement";
import * as utils from "@/utils";
import Pagination from "@/components/Pagination";
import { communityPage } from "@/api/communityMan";
import { getPayBillPage } from "@/api/costMan";
let dlgDataEmpty = {
  roomId: "",
  roomName: "",
  garageName: "",
  parkingName: "",
  userName: "",
  userId: "",
  phone: "",
  invoiceNature: "",
  invoiceTitle: "",
  invoiceType: "",
  titleType: "",
  dutyParagraph: "",
  bankName: "",
  bankNum: "",
  address: "",
  telephone: "",
  amount: "",
  billSumId: "",
  projectId: "",
  communityId: "",
  // id: "",
  payName: "", //付费对象
  feeFlagName: "", //费用标识
  feeName: "", //费用类型
};
export default {
  name: "applyForInvoice",
  components: {
    Pagination,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false, // 新增
      infoShow: false, //详情
      dlgType: "", // ADD\EDIT
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
        invoiceStatus: "0,1,2,9,99",
        communityId: "",
        orderSort:"payTime"
      },
      rules: {
        invoiceTitle: [
          { required: true, message: "请输入发票抬头", trigger: "blur" },
        ],
        invoiceNature: [
          { required: true, message: "请选择发票性质", trigger: "change" },
        ],
        invoiceType: [
          { required: true, message: "请选择发票类型", trigger: "change" },
        ],
        titleType: [
          { required: true, message: "请选择抬头类型", trigger: "change" },
        ],
      },
      costTypeList: [],
      feeFlagList: [
        {
          id: "1",
          name: "周期性费用",
        },
        {
          id: "2",
          name: "一次性费用",
        },
      ],
      isVatInvoice: "", //是否增值税
      oldInvoiceTitle: "", //历史发票抬头
      communityList: [], //小区
      invoiceStatus: "", //发票状态
      infoData: {},
    };
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    this.getList();
    this.getCommunityList();
    utils.getDataDict(this, "costType", "costTypeList");
  },

  methods: {
    closeDlg() {
      this.$refs.informationChangeForm.resetFields();
    },
    formatList() {
      for (let i of this.list) {
        i.feeName = utils.getNameById(i.feeType, this.costTypeList);
        i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList);
      }
    },
    // 获取数据
    getList() {
      this.list = [];
      findPayBillSumPage(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          let list = res.data.data;
          for(let item of list) {
            if (item.payInfoJson) {
              let payInfoJson = JSON.parse(item.payInfoJson)
              let payInfoJsonStr = ''
              if (payInfoJson.wxPay) {
                payInfoJsonStr += '微信、'
              }
              if (payInfoJson.zfbPay) {
                payInfoJsonStr += '支付宝、'
              }
              if (payInfoJson.cashPay) {
                payInfoJsonStr += '现金、'
              }
              item.payInfoJsonStr = payInfoJsonStr.substr(0, payInfoJsonStr.length-1)
              console.log('===payInfoJsonStr', item.payInfoJsonStr)

            }
          }
          this.list = list
          this.total = res.data.page ? res.data.page.total : 0;
          this.formatList();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
        projectId: this.userInfo.projectId,
      };
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data;
        }
      });
    },
    formatState(row, column) {
      switch (row.invoiceStatus) {
        case "0":
          return "已申请";
        case "1":
          return "已开票";
        case "2":
          return "已领取";
        case "9":
          return "待开票";
        default:
          return "已驳回";
      }
    },
    // payChannel(row, column) {
    //   switch (row.payWay) {
    //     case 1:
    //       return "业主自助缴费";
    //     case 2:
    //       return "扫码支付";
    //     case 3:
    //       return "线下现金缴费";
    //     default:
    //       return "";
    //   }
    // },
    //搜索
    searchFunc() {
      this.listQuery.page = 1;
      this.listQuery.size = 20;
      this.getList();
    },
    //申请弹框
    applyInvoice(row) {
      this.oldInvoiceTitle = "";
      console.log(row,"row");
      this.dlgData.receivedAmount=row.amount
      getTitle(row.ownerId).then((res) => {
        if (res.data.code == 200) {
          let data = res.data.data;
          console.log(data,"data");
          if(data!==null){
          this.oldInvoiceTitle = data.invoiceTitle;
          this.$set(this.dlgData, "invoiceTitle", data.invoiceTitle);
          this.$set(this.dlgData, "invoiceNature", data.invoiceNature);
          this.$set(this.dlgData, "invoiceType", data.invoiceType);
          this.$set(this.dlgData, "titleType", data.titleType);
          this.$set(this.dlgData, "dutyParagraph", data.dutyParagraph);
          this.$set(this.dlgData, "bankName", data.bankName);
          this.$set(this.dlgData, "bankNum", data.bankNum);
          this.$set(this.dlgData, "address", data.address);
          this.$set(this.dlgData, "telephone", data.telephone);
          if(this.dlgData.titleType=='0'){
              this.dlgData.invoiceTitle=this.dlgData.userName
          }
          }
        }
      });
      if (row.roomName) {
        this.dlgData.roomName = row.roomName;
      }  
      if (row.parkingName) {
        this.dlgData.roomName = row.parkingName;
      } 
      if(row.garageName){
        this.dlgData.roomName = row.garageName;
      }
      this.dlgData.userName = row.ownerName;
      this.dlgData.phone = row.ownerPhone;
      this.dlgData.amount = row.receivedAmount;
      console.log(this.dlgData.amount,"this.dlgData.amount");
      this.dlgData.roomId = row.roomId;
      this.dlgData.userId = row.ownerId;
      this.dlgData.billSumId = row.id;
      this.dlgData.communityId = row.communityId;
      this.dlgData.projectId = 0;
      this.dlgData.isVatInvoice = row.isVatInvoice;
      this.dlgData.enterpriseName = row.enterpriseName;
      this.dlgShow = true;
    },
    titleTypeChange(){
      if(this.dlgData.titleType=='0'){
          this.dlgData.invoiceTitle=this.dlgData.userName
      }else{
        this.dlgData.invoiceTitle=this.oldInvoiceTitle
      }
    },

    //提交
    subDlg() {
      this.$refs["informationChangeForm"].validate((valid) => {
        if (valid) {
          this.dlgData.projectId = this.userInfo.projectId;
          this.dlgData.id=""
          if (this.dlgData.invoiceType == "1") {
            this.dlgData.titleType = "";
          }
          console.log(this.dlgData,"this.dlgData");
          payInvoiceSave(this.dlgData).then((res) => {
            if (res.data.code == 200) {
              this.dlgShow = false;
              this.getList();
              this.$message.success("提交成功");
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    // 弹窗表格禁选
    dTDisFn(row, index) {
      return false;
    },
    num2Round(num, digit = 2) {
      return utils.num2Round(num, digit);
    },
    calcMoney() {
      let money = 0;
      for (let i of this.selectList) {
        money += i.receivableAmount + i.zhinajin - i.preferentialAmount;
      }
      this.totalMoney = utils.num2Round(money);
    },
    //详情
    info(row) {
      this.$nextTick(() => {
          if (row.roomName) {
            this.dlgData.payName = row["roomName"] + "(房屋)";
          } else if (row.parkingName) {
            this.dlgData.payName = row["parkingName"] + "(车位)";
          } else {
            this.dlgData.payName = row["garageName"] + "(车库)";
          }
      });
      if (row.invoiceStatus == "9") {
        this.invoiceStatus = "9";
      } else {
        this.invoiceStatus = "";
        getInfo(row.id).then((res) => {
          this.infoData = res.data.data;
          console.log(this.infoData,"infoData");
        });
      }
      this.dlgData = row;
      this.infoShow = true;
      // this.dlgData.id = row.id;
      this.dlgData.createTime = row.createTime;
      this.dlgData.endDate = row.endDate;
      this.dlgData.startDate = row.startDate;
      this.dlgData.orderRemark = row.orderRemark;
      this.dlgData.remark = row.remark;
      this.dlgData.configName = row.configName;
      let postParam = {
        page: 1,
        limit: 99,
        // status: type == 'EDIT' ? '0' : '1',
        // status: '1',
        billSumId: row.id,
      };
      getPayBillPage(postParam).then((res) => {
        if (res.data.code == 200) {
          for (let i of res.data.data) {
            i.feeName = utils.getNameById(i.feeType, this.costTypeList);
            i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList);
            i.amount = i.receivableAmount + i.zhinajin - i.preferentialAmount;
          }
          this.dlgData.list = JSON.parse(JSON.stringify(res.data.data));
          this.selectList = JSON.parse(JSON.stringify(res.data.data));
          this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          this.$nextTick(() => {
            this.calcMoney(); // 计算金额

            if (this.dlgType !== "VIEW") {
            } else {
              setTimeout(() => {
                for (let item of this.dlgData.list) {
                  this.$refs.dlgTableRef.toggleRowSelection(item, true);
                }
              }, 200);
              //  this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
            }
          });
        }
      });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.information {
  font-size: 16px;
}
.fontLable {
  font-weight: bolder;
}
</style>


