<template>
  <!-- 自助签约 -->
  <div class="app-container">
    <!-- 搜索 -->
    <div class="filter-container">
      <el-form inline @submit.native.prevent :model="listQuery">
        <el-form-item label="关键字：">
          <el-input
            @keyup.enter.native="searchFunc"
            placeholder="请输入名称"
            v-model="listQuery.label"
          ></el-input>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchFunc"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="handleCreate"
          >申请</el-button
        >
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :key="tableKey"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>
        <el-table-column
          label="营业名称"
          prop="shopName"
          align="center"
          width="300"
        >
        </el-table-column>

        <!-- group_text -->
        <el-table-column prop="legalName" label="姓名"> </el-table-column>

        <el-table-column label="手机号" width="200px" prop="legalMobile">
        </el-table-column>

        <el-table-column label="银行卡号" prop="bankAcctNo" width="300px">
        </el-table-column>

        <el-table-column label="申请日期" width="250px" prop="createTime">
        </el-table-column>
        <el-table-column
          :formatter="formatState"
          label="状态"
          width="100px"
          prop="status"
        >
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="350"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              v-if="
                scope.row.status == '00' ||
                scope.row.status == '01' ||
                scope.row.status == '02' ||
                scope.row.status == '06' ||
                scope.row.status == '88'
              "
              type="success"
              size="mini"
              plain
              @click="toDesc(scope.row)"
              icon="el-icon-document"
              >详情</el-button
            >

            <el-button
              v-if="
                scope.row.status == '04' ||
                scope.row.status == '05' ||
                scope.row.status == '11' ||
                scope.row.status == '28' ||
                scope.row.status == '31' ||
                scope.row.status == '99'
              "
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="toEdit(scope.row)"
              plain
              >修改</el-button
            >

            <el-button
              v-if="scope.row.status == '03'"
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="informationChange(scope.row)"
              plain
              >信息变更</el-button
            >
            <el-button
              v-if="scope.row.status == '03'"
              type="success"
              size="mini"
              icon="el-icon-document"
              @click="getChangeRecordTab(scope.row)"
              plain
              >变更记录</el-button
            >
            <el-button
              v-if="scope.row.status == '05'"
              type="warning"
              size="mini"
              icon="el-icon-refresh"
              @click="remitVerify(scope.row)"
              plain
              >对公打款验证</el-button
            >
            <el-button
              v-if="scope.row.status == '05'"
              type="warning"
              size="mini"
              icon="el-icon-refresh"
              @click="accountVerify(scope.row)"
              plain
              >对公账户认证</el-button
            >
            <el-button
              v-if="scope.row.status == '00'"
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="signaContract(scope.row)"
              plain
              >签约</el-button
            >

            <!-- <el-button type="danger" size="mini" plain @click="delFunc(scope.row.id)" icon="el-icon-delete">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>
    <!-- 弹框详情 -->
    <el-dialog
      class="spec-dialog"
      :close-on-click-modal="false"
      title="自助签约详情"
      append-to-body
      width="1100px"
      :visible.sync="infoCheck"
      @close="closeSpecDialog"
    >
      <h4 style="margin-left: 15px;position-relative;top: 0;left: 0;">
        身份信息
      </h4>
      <h4 style="margin-left: 50px">法定代表人信息</h4>
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="dlgData"
        label-position="left"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="身份证正反面照片" prop="legalIdCard">
          <!-- >> 新的 -->
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <p><span style="color: red">*</span> 身份证头像面</p>
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="dlgData.idFrontImg"
                    class="upload-img"
                    :preview-src-list="[dlgData.idFrontImg]"
                    :src="dlgData.idFrontImg"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc2"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="
                      (dlgData.idFrontImg && whetherDraft == 'saveDraft') ||
                      (dlgData.idFrontImg && whetherDraft == 'isEdit')
                    "
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc2"
                  >
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
            <el-col class="idBackImg">
              <div class="dialog-upload-item">
                <p><span style="color: red">*</span> 身份证有效期面</p>
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="dlgData.idBackImg"
                    class="upload-img"
                    :preview-src-list="[dlgData.idBackImg]"
                    :src="dlgData.idBackImg"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc3"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="
                      (dlgData.idBackImg && whetherDraft == 'saveDraft') ||
                      (dlgData.idBackImg && whetherDraft == 'isEdit')
                    "
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc3"
                  >
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item
          label=""
          prop="handIdCard"
          style="position: absolute; top: 178px; left: 568px"
        >
          <!-- >> 新的 -->
          <p><span style="color: red">*</span> 手持身份证自拍照</p>
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="dlgData.handIdCard"
                    class="upload-img"
                    :preview-src-list="[dlgData.handIdCard]"
                    :src="dlgData.handIdCard"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc12"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="
                      (dlgData.handIdCard && whetherDraft == 'saveDraft') ||
                      (dlgData.handIdCard && whetherDraft == 'isEdit')
                    "
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc12"
                  >
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>

        <div class="informationBox">
          <el-form-item
            label-width="140px"
            label="法定代表人姓名"
            prop="legalName"
          >
            <el-input
              v-model="dlgData.legalName"
              placeholder="请输入法定代表人姓名"
              :disabled="
                whetherDraft == 'saveDraft' || whetherDraft == 'isEdit'
                  ? false
                  : true
              "
            />
          </el-form-item>
          <el-form-item
            label-width="140px"
            label="法定代表人性别"
            prop="legalSex"
            :rules="
              this.chooseIndex == 3
                ? rules.legalSex
                : [
                    {
                      required: false,
                      message: '请选择法定代表人性别',
                      trigger: 'blur',
                    },
                  ]
            "
          >
            <el-select
              :disabled="
                whetherDraft == 'saveDraft' || whetherDraft == 'isEdit'
                  ? false
                  : true
              "
              v-model="dlgData.legalSex"
              placeholder="请选择"
            >
              <el-option
                v-for="item in legalSexList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="家庭地址"
            prop="legalmanHomeAddr"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.legalmanHomeAddr"
              placeholder="请输入法定代表人家庭地址"
              :disabled="
                whetherDraft == 'saveDraft' || whetherDraft == 'isEdit'
                  ? false
                  : true
              "
            />
          </el-form-item>
          <el-form-item
            label="身份证号码"
            prop="legalIdcardNo"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.legalIdcardNo"
              placeholder="请输入法定代表人身份证号码"
              :disabled="
                whetherDraft == 'saveDraft' || whetherDraft == 'isEdit'
                  ? false
                  : true
              "
            />
          </el-form-item>
          <el-form-item
            label="身份证有效期"
            label-width="120px"
            prop="legalCardDeadline"
          >
            <el-date-picker
              v-model="dlgData.legalCardDeadline"
              type="date"
              align="center"
              placeholder="请选择身份证有效期"
              style="width: 170px"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :disabled="
                (whetherDraft == 'saveDraft' && legalCardCheck == false) ||
                (whetherDraft == 'isEdit' && legalCardCheck == false)
                  ? false
                  : true
              "
            >
            </el-date-picker>
            <el-checkbox
              :disabled="
                (whetherDraft == 'saveDraft' && !dlgData.legalCardDeadline) ||
                (whetherDraft == 'isEdit' && !dlgData.legalCardDeadline)
                  ? true
                  : false
              "
              v-model="legalCardCheck"
              style="margin-left: 40px"
              >长期</el-checkbox
            >
          </el-form-item>
          <el-form-item
            label="法人职业"
            prop="legalOccupation"
            label-width="120px"
            :rules="
              this.chooseIndex == 3
                ? rules.legalOccupation
                : [
                    {
                      required: false,
                      message: '请选择法定代表人职业',
                      trigger: 'blur',
                    },
                  ]
            "
          >
            <el-select
              :disabled="
                whetherDraft == 'saveDraft' || whetherDraft == 'isEdit'
                  ? false
                  : true
              "
              v-model="dlgData.legalOccupation"
              placeholder="请选择"
              @change="legalOccupationC(value)"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="checkLegalmanCareerDesc"
            label="法人职业详细描述"
            prop="legalEmail"
            label-width="120px"
          >
            <el-input
              :disabled="
                whetherDraft == 'saveDraft' || whetherDraft == 'isEdit'
                  ? false
                  : true
              "
              v-model="dlgData.legalmanCareerDesc"
              placeholder="请输入法人职业详细描述"
            />
          </el-form-item>
          <el-form-item label="邮箱" prop="legalEmail" label-width="120px">
            <el-input
              :disabled="
                whetherDraft == 'saveDraft' || whetherDraft == 'isEdit'
                  ? false
                  : true
              "
              v-model="dlgData.legalEmail"
              placeholder="请输入法定代表人邮箱地址"
            />
          </el-form-item>
        </div>
        <h4
          style="display: inline-block; position: relative; top: 0; left: -34px"
        >
          商户信息
        </h4>
        <div class="commercialPic">
          <el-form-item label="" prop="businessLicense">
            <p>
              <span
                style="color: red"
                v-if="
                  this.dlgData.regMerType == '02' ||
                  this.dlgData.regMerType == '00'
                "
                >*</span
              >
              营业执照
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.businessLicense"
                      class="upload-img"
                      :preview-src-list="[dlgData.businessLicense]"
                      :src="dlgData.businessLicense"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc4"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="
                        (dlgData.businessLicense &&
                          whetherDraft == 'saveDraft') ||
                        (dlgData.businessLicense && whetherDraft == 'isEdit')
                      "
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc4"
                    >
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <!-- <el-form-item label="商户税务登记照" prop="taxRegistration">

          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <div class="imageUploadBox">
              
                  <el-image
                    v-if="dlgData.taxRegistration"
                    class="upload-img"
                    :preview-src-list="[dlgData.taxRegistration]"
                    :src="dlgData.taxRegistration"
                    alt=""
                  ></el-image>
          
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc5"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
               
                  <el-upload
                    v-if="dlgData.taxRegistration&&whetherDraft=='saveDraft'"
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc5"
                  >
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item> -->
          <el-form-item label="" prop="licenceOpeningAccounts">
            <!-- >> 新的 -->
            <p>
              <span style="color: red" v-if="this.dlgData.regMerType == '00'"
                >*</span
              >
              开户许可证
            </p>
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.licenceOpeningAccounts"
                      class="upload-img"
                      :preview-src-list="[dlgData.licenceOpeningAccounts]"
                      :src="dlgData.licenceOpeningAccounts"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc11"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="
                        (dlgData.licenceOpeningAccounts &&
                          whetherDraft == 'saveDraft') ||
                        (dlgData.licenceOpeningAccounts &&
                          whetherDraft == 'isEdit')
                      "
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc11"
                    >
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </div>

        <div class="commercialMessage">
          <el-form-item label="营业名称" prop="shopName" label-width="120px">
            <el-input
              v-model="dlgData.shopName"
              placeholder="请输入营业执照的注册营业名称"
              :disabled="
                whetherDraft == 'saveDraft' || whetherDraft == 'isEdit'
                  ? false
                  : true
              "
            />
          </el-form-item>
          <el-form-item
            label="营业地区"
            label-width="80px"
            style="margin-right: 150px"
            required
          >
            <el-form-item prop="shopProvinceId" style="width: 100px">
              <el-select
                v-model="dlgData.shopProvinceId"
                placeholder="省/直辖市"
                @change="provinceChange"
                class="shopCity"
              >
                <el-option
                  v-for="item in provinceList"
                  :key="item.id"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              prop="shopCityId"
              style="width: 100px; position: absolute; top: 0; left: 110px"
            >
              <el-select
                v-model="dlgData.shopCityId"
                placeholder="市区"
                @change="cityChange"
                class="shopCity"
              >
                <el-option
                  v-for="item in cityList"
                  :key="item.id"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              prop="shopCountryId"
              style="width: 100px; position: absolute; top: 0; left: 220px"
            >
              <el-select
                v-model="dlgData.shopCountryId"
                placeholder="区/县"
                class="shopCity"
              >
                <el-option
                  v-for="item in areaList"
                  :key="item.id"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form-item>
          <el-form-item label="详细地址" prop="shopAddrExt" label-width="120px">
            <el-input
              v-model="dlgData.shopAddrExt"
              placeholder="请输入有效的详细地址"
            />
          </el-form-item>
          <el-form-item
            label="统一社会信用码"
            prop="shopLic"
            label-width="140px"
          >
            <el-input
              v-model="dlgData.shopLic"
              placeholder="请输入统一社会信用码"
            />
          </el-form-item>
          <!-- <el-form-item label="所属行业" prop="product" label-width="120px">
          <el-select
            v-model="dlgData.product.product_id"
            multiple
            placeholder="请选择"
          >
            <el-option
              v-for="item in typeOfPayment"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
          <el-form-item label="业务选择" prop="product" label-width="120px">
            <el-select
              v-model="dlgData.product.product_id"
              multiple
              placeholder="请选择"
            >
              <el-option
                v-for="item in typeOfPayment"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否开通收支双线" prop="receipt2Line">
            <el-radio-group v-model="dlgData.product.receipt2Line">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="是否有营业场所"
            prop="havingFixedBusiAddr"
            :rules="
              this.chooseIndex == 3
                ? rules.havingFixedBusiAddr
                : [
                    {
                      required: false,
                      message: '请选择是否有营业场所',
                      trigger: 'blur',
                    },
                  ]
            "
          >
            <el-radio-group v-model="dlgData.havingFixedBusiAddr">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="商户传真" prop="fax" label-width="120px">
            <el-input v-model="dlgData.fax" placeholder="请输入商户传真" />
          </el-form-item>
          <el-form-item
            label="终端维护经理"
            prop="lastTerminalManager"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.lastTerminalManager"
              placeholder="请输入终端维护经理"
            />
          </el-form-item>
          <el-form-item
            label="客户维护经理"
            prop="lastClientManager"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.lastClientManager"
              placeholder="请输入客户维护经理"
            />
          </el-form-item>
          <el-form-item
            label="所属服务区域"
            prop="serviceDistrict"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.serviceDistrict"
              placeholder="请输入所属服务区域"
            />
          </el-form-item>
          <el-form-item
            label="细分服务区域"
            prop="detailDistrict"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.detailDistrict"
              placeholder="请输入细分服务区域"
            />
          </el-form-item>
          <el-form-item
            label="发展部门"
            prop="developingDept"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.developingDept"
              placeholder="请输入发展部门"
            />
          </el-form-item>
          <el-form-item
            label="发展人"
            prop="developingPersonId"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.developingPersonId"
              placeholder="请输入发展人"
            />
          </el-form-item>
        </div>

        <div class="closeAccountHoldingBox">
          <div>
            <h4
              style="
                display: inline-block;
                position: relative;
                top: 0;
                left: -34px;
              "
            >
              结算信息
            </h4>

            <el-form-item
              label="账户名称"
              prop="bankAcctName"
              label-width="120px"
            >
              <el-input
                v-model="dlgData.bankAcctName"
                placeholder="请输入与'注册营业名称'同名的账户名称"
              />
            </el-form-item>
            <el-form-item
              label="银行账号"
              prop="bankAcctNo"
              label-width="120px"
            >
              <el-input
                v-model="dlgData.bankAcctNo"
                placeholder="请输入银行账号"
              />
            </el-form-item>

            <el-form-item
              label="银行卡反面照片"
              prop="bankIdCard"
              v-if="this.dlgData.bankAcctType == '0'"
            >
              <!-- >> 新的 -->
              <el-row>
                <el-col>
                  <div class="dialog-upload-item">
                    <p><span style="color: red">*</span> 银行卡正面</p>
                    <div class="imageUploadBox">
                      <!-- 已上传的文件 -->
                      <el-image
                        v-if="dlgData.bankCardFront"
                        class="upload-img"
                        :preview-src-list="[dlgData.bankCardFront]"
                        :src="dlgData.bankCardFront"
                        alt=""
                      ></el-image>
                      <!-- 图片为空 -->
                      <el-upload
                        v-else
                        action=""
                        :show-file-list="false"
                        :before-upload="uploadFunc14"
                      >
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                      <!-- 重新上传图片 按钮 -->
                      <el-upload
                        v-if="
                          (dlgData.bankCardFront &&
                            whetherDraft == 'saveDraft') ||
                          (dlgData.bankCardFront && whetherDraft == 'isEdit')
                        "
                        action=""
                        :show-file-list="false"
                        :before-upload="uploadFunc14"
                      >
                        <el-button
                          title="点击重新上传"
                          icon="el-icon-upload2"
                          type="primary"
                          size="mini"
                          class="imageUploadBox-icon-upload"
                        ></el-button>
                      </el-upload>
                    </div>
                  </div>
                </el-col>
                <el-col class="idBackImg">
                  <div class="dialog-upload-item">
                    <p><span style="color: red">*</span> 银行卡反面</p>
                    <div class="imageUploadBox">
                      <!-- 已上传的文件 -->
                      <el-image
                        v-if="dlgData.bankCardReverse"
                        class="upload-img"
                        :preview-src-list="[dlgData.bankCardReverse]"
                        :src="dlgData.bankCardReverse"
                        alt=""
                      ></el-image>
                      <!-- 图片为空 -->
                      <el-upload
                        v-else
                        action=""
                        :show-file-list="false"
                        :before-upload="uploadFunc15"
                      >
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                      <!-- 重新上传图片 按钮 -->
                      <el-upload
                        v-if="
                          (dlgData.bankCardReverse &&
                            whetherDraft == 'saveDraft') ||
                          (dlgData.bankCardReverse && whetherDraft == 'isEdit')
                        "
                        action=""
                        :show-file-list="false"
                        :before-upload="uploadFunc15"
                      >
                        <el-button
                          title="点击重新上传"
                          icon="el-icon-upload2"
                          type="primary"
                          size="mini"
                          class="imageUploadBox-icon-upload"
                        ></el-button>
                      </el-upload>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item label="账户类型" prop="bankAcctType">
              <el-radio-group
                v-model="dlgData.bankAcctType"
                @change="bankAcctTypeChange"
              >
                <el-radio label="0">个人账户</el-radio>
                <el-radio label="1">公司账户</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="法人代表人手机号"
              prop="legalMobile"
              label-width="155px"
            >
              <el-input
                v-model="dlgData.legalMobile"
                placeholder="请输入法人代表手机号"
              />
            </el-form-item>
            <el-form-item label="开户所在地" label-width="100px">
              <el-form-item prop="openAccountProvince" style="width: 100px">
                <el-select
                  v-model="dlgData.openAccountProvince"
                  placeholder="省/直辖市"
                  @change="openAccountProvinceChange"
                  class="shopCity"
                >
                  <el-option
                    v-for="item in provinceList"
                    :key="item.id"
                    :label="item.areaName"
                    :value="item.areaCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                prop="openAccountCity"
                style="width: 100px; position: absolute; top: 0; left: 110px"
              >
                <el-select
                  v-model="dlgData.openAccountCity"
                  placeholder="市区"
                  @change="openAccountCityChange"
                  class="shopCity"
                >
                  <el-option
                    v-for="item in cityList"
                    :key="item.id"
                    :label="item.areaName"
                    :value="item.areaCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                prop="openAccountCountry"
                style="width: 100px; position: absolute; top: 0; left: 220px"
              >
                <el-select
                  v-model="dlgData.openAccountCountry"
                  placeholder="区/县"
                  class="shopCity"
                >
                  <el-option
                    v-for="item in areaList"
                    :key="item.id"
                    :label="item.areaName"
                    :value="item.areaCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form-item>
            <el-form-item label="所属支行" prop="bankName" label-width="90px">
              <el-input
                placeholder="请输入内容"
                v-model="dlgData.bankName"
                class="input-with-select"
              >
                <el-select
                  v-model="bankListBranchName"
                  @change="bankListChange"
                  slot="prepend"
                  placeholder="请选择银行"
                >
                  <el-option
                    v-for="item in bankOptions"
                    :key="item.code"
                    :label="item.bankBranchName"
                    :value="item"
                  >
                  </el-option>
                </el-select>
                <el-button
                  @click="searchBank"
                  slot="append"
                  icon="el-icon-search"
                ></el-button>
              </el-input>
            </el-form-item>

            <el-form-item label="备注">
              <el-input type="textarea" v-model="dlgData.remark"></el-input>
            </el-form-item>
          </div>
          <div>
            <h4
              style="
                display: inline-block;
                position: relative;
                top: 0;
                left: -34px;
              "
            >
              受益人/控股股东信息
            </h4>
            <h4>控股股东</h4>
            <el-form-item
              label="控股股东"
              prop="shareholderName"
              label-width="120px"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderName
                  : [
                      {
                        required: false,
                        message: '请输入控股股东姓名',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-input
                v-model="dlgData.shareholderName"
                style="width: 150px"
                placeholder="请输入控股股东姓名"
              />
              <el-button type="primary" @click="synchronizationLegalPerson"
                >同法人信息</el-button
              >
            </el-form-item>
            <el-form-item
              label="证件类型"
              prop="shareholderCertType"
              label-width="120px"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderCertType
                  : [
                      {
                        required: false,
                        message: '请选择证件类型',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-select
                v-model="dlgData.shareholderCertType"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in shareholderCertTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="证件号码"
              prop="shareholderCertno"
              label-width="120px"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderCertno
                  : [
                      {
                        required: false,
                        message: '请输入控股股东证件号码',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-input
                v-model="dlgData.shareholderCertno"
                placeholder="请输入控股股东证件号码"
              />
            </el-form-item>
            <el-form-item
              label="证件有效期"
              label-width="120px"
              prop="shareholderCertExpire"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderCertExpire
                  : [
                      {
                        required: false,
                        message: '请选择证件有效期',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-date-picker
                v-model="dlgData.shareholderCertExpire"
                type="date"
                align="center"
                placeholder="请选择证件有效期"
                style="width: 160px"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :disabled="shareholderCertificateChecked == true ? true : false"
              >
              </el-date-picker>
              <el-checkbox
                @change="certificateCheckedChange"
                :disabled="
                  dlgData.shareholderCertExpire == '9999-12-31' ||
                  !dlgData.shareholderCertExpire
                    ? false
                    : true
                "
                v-model="shareholderCertificateChecked"
                style="margin-left: 50px"
                >长期</el-checkbox
              >
            </el-form-item>
            <el-form-item
              label="家庭地址"
              prop="shareholderHomeAddr"
              label-width="120px"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderHomeAddr
                  : [
                      {
                        required: false,
                        message: '请输入控股股东家庭地址',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-input
                v-model="dlgData.shareholderHomeAddr"
                placeholder="请输入控股股东家庭地址"
              />
            </el-form-item>
            <h4
              style="
                display: inline-block;
                position: relative;
                top: 0;
                left: -34px;
              "
            >
              受益人
            </h4>

            <div v-for="(item, index) in dlgData.bnfList" :key="index">
              <h4 style="display: inline-block">受益人{{ index + 1 }}</h4>
              <el-button
                ref="deleteShareholder"
                plain
                size="mini"
                @click="deleteShareholder(item, index)"
                v-if="index > 0"
                >删除</el-button
              >
              <el-form-item
                label="受益人"
                prop="bnfName"
                label-width="120px"
                :rules="
                  chooseIndex !== 3
                    ? []
                    : [
                        {
                          required: false,
                          message: '请输入受益人姓名',
                          trigger: 'blur',
                        },
                      ]
                "
              >
                <el-input
                  v-model="item.bnfName"
                  style="width: 150px"
                  placeholder="请输入受益人姓名"
                />
                <el-button
                  type="primary"
                  @click="synchronizationShareholder(item, index)"
                  >同控股股东</el-button
                >
              </el-form-item>
              <el-form-item
                label="证件类型"
                prop="bnfCertType"
                label-width="120px"
                :rules="
                  chooseIndex !== 3
                    ? []
                    : [
                        {
                          required: false,
                          message: '请选择证件类型',
                          trigger: 'blur',
                        },
                      ]
                "
              >
                <el-select
                  v-model="item.bnfCertType"
                  value-key="value"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in shareholderCertTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="证件号码"
                prop="bnfCertno"
                label-width="120px"
                :rules="
                  chooseIndex !== 3
                    ? []
                    : [
                        {
                          required: false,
                          message: '请输入受益人证件号码',
                          trigger: 'blur',
                        },
                      ]
                "
              >
                <el-input
                  v-model="item.bnfCertno"
                  placeholder="请输入受益人证件号码"
                />
              </el-form-item>
              <el-form-item
                label="证件有效期"
                label-width="120px"
                prop="bnfCertExpire"
                :rules="
                  chooseIndex !== 3
                    ? []
                    : [
                        {
                          required: false,
                          message: '请选择证件有效期',
                          trigger: 'blur',
                        },
                      ]
                "
              >
                <el-date-picker
                  v-model="item.bnfCertExpire"
                  type="date"
                  align="center"
                  placeholder="请选择证件有效期"
                  style="width: 160px"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :disabled="item.bnfCertificateChecked == true ? true : false"
                >
                </el-date-picker>
                <el-checkbox
                  @change="bnfCertificateCheckedChange(item, index)"
                  :disabled="
                    !item.bnfCertExpire || item.bnfCertExpire == '9999-12-31'
                      ? false
                      : true
                  "
                  v-model="item.bnfCertificateChecked"
                  style="margin-left: 50px"
                  >长期</el-checkbox
                >
              </el-form-item>
              <el-form-item
                label="家庭地址"
                prop="bnfHomeAddr"
                label-width="120px"
                :rules="
                  chooseIndex !== 3
                    ? []
                    : [
                        {
                          required: false,
                          message: '请输入受益人家庭地址',
                          trigger: 'blur',
                        },
                      ]
                "
              >
                <el-input
                  v-model="item.bnfHomeAddr"
                  placeholder="请输入受益家庭地址"
                />
              </el-form-item>
            </div>
            <el-button @click="addBnf">+添加受益人</el-button>
          </div>
        </div>
        <h4>图片信息</h4>

        <div class="imgBox">
          <el-form-item label="" prop="shopFrontPho">
            <p>
              <span
                v-if="
                  (this.dlgData.regMerType == '03' && this,
                  dlgData.havingFixedBusiAddr == '1')
                "
                style="color: red"
                >*</span
              >
              店铺门面照
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.shopFrontPho"
                      class="upload-img"
                      :preview-src-list="[dlgData.shopFrontPho]"
                      :src="dlgData.shopFrontPho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc6"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="
                        (dlgData.shopFrontPho && whetherDraft == 'saveDraft') ||
                        (dlgData.shopFrontPho && whetherDraft == 'isEdit')
                      "
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc6"
                    >
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" prop="shopIndoorPho">
            <p>
              <span
                v-if="
                  (this.dlgData.regMerType == '03' && this,
                  dlgData.havingFixedBusiAddr == '1')
                "
                style="color: red"
                >*</span
              >
              店铺室内照
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.shopIndoorPho"
                      class="upload-img"
                      :preview-src-list="[dlgData.shopIndoorPho]"
                      :src="dlgData.shopIndoorPho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc7"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="
                        (dlgData.shopIndoorPho &&
                          whetherDraft == 'saveDraft') ||
                        (dlgData.shopIndoorPho && whetherDraft == 'isEdit')
                      "
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc7"
                    >
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" prop="assistEvidentiaryMaterialPho">
            <p>
              <span style="color: red" v-if="this.dlgData.regMerType == '03'"
                >*</span
              >
              辅助证明材料
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.assistEvidentiaryMaterialPho"
                      class="upload-img"
                      :preview-src-list="[dlgData.assistEvidentiaryMaterialPho]"
                      :src="dlgData.assistEvidentiaryMaterialPho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc8"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="
                        (dlgData.assistEvidentiaryMaterialPho &&
                          whetherDraft == 'saveDraft') ||
                        (dlgData.assistEvidentiaryMaterialPho &&
                          whetherDraft == 'isEdit')
                      "
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc8"
                    >
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </div>
        <div class="imgBox">
          <el-form-item label="" prop="nexAssistEvidentiaryMaterialPho">
            <p>辅助证明材料2</p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.nexAssistEvidentiaryMaterialPho"
                      class="upload-img"
                      :preview-src-list="[
                        dlgData.nexAssistEvidentiaryMaterialPho,
                      ]"
                      :src="dlgData.nexAssistEvidentiaryMaterialPho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc9"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="
                        (dlgData.nexAssistEvidentiaryMaterialPho &&
                          whetherDraft == 'saveDraft') ||
                        (dlgData.nexAssistEvidentiaryMaterialPho &&
                          whetherDraft == 'isEdit')
                      "
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc9"
                    >
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" prop="institutionalFrameworkCodePho">
            <p>
              <span style="color: red" v-if="this.dlgData.regMerType == '01'"
                >*</span
              >
              组织机构代码证
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.institutionalFrameworkCodePho"
                      class="upload-img"
                      :preview-src-list="[
                        dlgData.institutionalFrameworkCodePho,
                      ]"
                      :src="dlgData.institutionalFrameworkCodePho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFuncX"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="
                        (dlgData.institutionalFrameworkCodePho &&
                          whetherDraft == 'saveDraft') ||
                        (dlgData.institutionalFrameworkCodePho &&
                          whetherDraft == 'isEdit')
                      "
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFuncX"
                    >
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" prop="byPeopleCertificate">
            <p>民办非登记证书</p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.byPeopleCertificate"
                      class="upload-img"
                      :preview-src-list="[dlgData.byPeopleCertificate]"
                      :src="dlgData.byPeopleCertificate"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc16"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="
                        (dlgData.byPeopleCertificate &&
                          whetherDraft == 'saveDraft') ||
                        (dlgData.byPeopleCertificate &&
                          whetherDraft == 'isEdit')
                      "
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc16"
                    >
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </div>
        <div style="width:800px">
        <el-form-item label="" prop="wabAppPic" style="display:inline-block;">
            <p><span style="color: red">*</span> 商户网站/APP截图</p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.wabAppPic"
                      class="upload-img"
                      :preview-src-list="[dlgData.wabAppPic]"
                      :src="dlgData.wabAppPic"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc23"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="
                        (dlgData.wabAppPic &&
                          whetherDraft == 'saveDraft') ||
                        (dlgData.wabAppPic &&
                          whetherDraft == 'isEdit')
                      "
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc23"
                    >
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        <el-form-item label="" prop="otherMaterial" style="display:inline-block;margin-left:176px">
          <p>其他材料</p>
          <!-- >> 新的 -->
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="dlgData.otherMaterial"
                    class="upload-img"
                    :preview-src-list="[dlgData.otherMaterial]"
                    :src="dlgData.otherMaterial"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc13"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="
                      (dlgData.otherMaterial && whetherDraft == 'saveDraft') ||
                      (dlgData.otherMaterial && whetherDraft == 'isEdit')
                    "
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc13"
                  >
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="infoCheck = false">取 消</el-button>
        <el-button
          type="primary"
          v-if="whetherDraft == 'saveDraft'"
          @click="temporaryStorage"
          icon="el-icon-plus"
          >存草稿</el-button
        >
        <el-button
          type="success"
          v-if="whetherDraft == 'saveDraft' || whetherDraft == 'isEdit'"
          @click="addFunc"
          icon="el-icon-check"
          >提交</el-button
        >
      </div>
    </el-dialog>

    <!-- 对公账户验证弹框 -->
    <el-dialog
      title="对公账户验证"
      width="25%"
      :visible.sync="companyAccountVerifyDlg"
      :close-on-click-modal="false"
    >
      <el-form :model="moneyForm" :rules="moneyFormRules">
        <el-form-item
          label="打款金额"
          :label-width="formLabelWidth"
          prop="money"
        >
         <el-input-number :precision="0" :step="1"  placeholder="请输入打款金额" :min="1" :controls="false" v-model="moneyForm.money">
          <!-- <template slot="append">分</template> -->
         </el-input-number> 分
          <!-- <el-input v-model="moneyForm.money" placeholder="请输入打款金额">
            
          </el-input> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="companyAccountVerifyDlg = false">取 消</el-button>
        <el-button type="primary" @click="corporateAccount">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 账户信息变更 -->
    <el-dialog
      width="30%"
      title="账户信息变更"
      :visible.sync="informationChangeDlg"
      :close-on-click-modal="false"
      @close="cloInformationChangeDlg"
    >
      <el-form
        ref="informationChangeForm"
        :model="informationChangeForm"
        :rules="informationChangeRules"
      >
        <el-form-item label="商户号" label-width="130px">
          <el-input
            v-model="informationChangeForm.merNo"
            :disabled="true"
            placeholder="请输入商户号"
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item label="变更前开户行账号" label-width="130px">
          <el-input
            v-model="informationChangeForm.bankAcctNo"
            :disabled="true"
            placeholder="请输入变更前开户行账号"
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="银行预留手机号"
          label-width="130px"
          prop="bankMobile"
        >
          <el-input
            v-model="informationChangeForm.bankMobile"
            placeholder="请输入银行预留手机号"
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="变更后开户行账号"
          label-width="140px"
          prop="alterBankAcctNo"
        >
          <el-input
            v-model="informationChangeForm.alterBankAcctNo"
            placeholder="请输入变更后开户行账号"
            label-width="130px"
            style="width: 200px"
          ></el-input>
        </el-form-item>

        <el-form-item label="开户所在地" label-width="90px">
          <el-form-item prop="openAccountProvince" style="width: 100px">
            <el-select
              v-model="informationChangeForm.openAccountProvince"
              placeholder="省/直辖市"
              @change="informationProvinceChange"
              class="shopCity"
            >
              <el-option
                v-for="item in provinceList"
                :key="item.id"
                :label="item.areaName"
                :value="item.areaCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="openAccountCity"
            style="width: 100px; position: absolute; top: 0; left: 110px"
          >
            <el-select
              v-model="informationChangeForm.openAccountCity"
              placeholder="市区"
              @change="informationCityChange"
              class="shopCity"
            >
              <el-option
                v-for="item in cityList"
                :key="item.id"
                :label="item.areaName"
                :value="item.areaCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="openAccountCountry"
            style="width: 100px; position: absolute; top: 0; left: 220px"
          >
            <el-select
              v-model="informationChangeForm.openAccountCountry"
              placeholder="区/县"
              class="shopCity"
            >
              <el-option
                v-for="item in areaList"
                :key="item.id"
                :label="item.areaName"
                :value="item.areaCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form-item>

        <el-form-item label="所属支行" prop="bankName" label-width="80px">
          <el-input
            placeholder="请输入内容"
            v-model="informationChangeForm.bankName"
            class="input-with-select"
            style="width: 400px"
          >
            <el-select
              v-model="bankListBranchName"
              @change="bankListChange"
              slot="prepend"
              placeholder="请选择银行"
            >
              <el-option
                v-for="item in bankOptions"
                :key="item.code"
                :label="item.bankBranchName"
                :value="item"
              >
              </el-option>
            </el-select>
            <el-button
              @click="searchBank"
              slot="append"
              icon="el-icon-search"
            ></el-button>
          </el-input>
        </el-form-item>

        <el-form-item label="身份证头像面" prop="handIdCard">
          <!-- >> 新的 -->
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="informationChangeForm.idFrontImg"
                    class="upload-img"
                    :preview-src-list="[informationChangeForm.idFrontImg]"
                    :src="informationChangeForm.idFrontImg"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc20"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="informationChangeForm.idFrontImg"
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc20"
                  >
                    <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item
          label="银行卡正反面照片"
          prop="bankIdCard"
          v-if="isBankAcct"
        >
          <!-- >> 新的 -->
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <p>银行卡正面</p>
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="informationChangeForm.bankCardFront"
                    class="upload-img"
                    :preview-src-list="[informationChangeForm.bankCardFront]"
                    :src="informationChangeForm.bankCardFront"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc21"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="informationChangeForm.bankCardFront"
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc21"
                  >
                    <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
            <el-col class="idBackImg">
              <div class="dialog-upload-item">
                <p>银行卡反面</p>
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="informationChangeForm.bankCardReverse"
                    class="upload-img"
                    :preview-src-list="[informationChangeForm.bankCardReverse]"
                    :src="informationChangeForm.bankCardReverse"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc22"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="informationChangeForm.bankCardReverse"
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc22"
                  >
                    <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            :rows="2"
            type="textarea"
            v-model="informationChangeForm.remark"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="informationChangeDlg = false">取 消</el-button>
        <el-button type="primary" @click="confirmChange">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 信息变更详情 -->
    <el-dialog
      width="30%"
      title="变更详情"
      :visible.sync="alterationInfoDlg"
      :close-on-click-modal="false"
    >
      <el-form :model="alterationInfoForm" :rules="informationChangeRules">
        <el-form-item label="商户号" label-width="130px">
          <el-input
            v-model="alterationInfoForm.merNo"
            placeholder="请输入商户号"
            :disabled="true"
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item label="变更前开户行账号" label-width="130px">
          <el-input
            v-model="alterationInfoForm.bankAcctNo"
            placeholder="请输入变更前开户行账号"
            :disabled="true"
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item label="银行预留手机号" label-width="130px">
          <el-input
            v-model="alterationInfoForm.bankMobile"
            placeholder="请输入银行预留手机号"
            :disabled="true"
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item label="变更后开户行账号" label-width="130px">
          <el-input
            v-model="alterationInfoForm.alterBankAcctNo"
            placeholder="请输入变更后开户行账号"
            :disabled="true"
            style="width: 200px"
          ></el-input>
        </el-form-item>

        <el-form-item label="身份证头像面" prop="handIdCard">
          <!-- >> 新的 -->
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="alterationInfoForm.idFrontImg"
                    class="upload-img"
                    :preview-src-list="[alterationInfoForm.idFrontImg]"
                    :src="alterationInfoForm.idFrontImg"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc20"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item
          label="银行卡正反面照片"
          prop="bankIdCard"
          v-if="isBankAcct"
        >
          <!-- >> 新的 -->
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <p>银行卡正面</p>
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="alterationInfoForm.bankCardFront"
                    class="upload-img"
                    :preview-src-list="[alterationInfoForm.bankCardFront]"
                    :src="alterationInfoForm.bankCardFront"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc21"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </div>
              </div>
            </el-col>
            <el-col class="idBackImg">
              <div class="dialog-upload-item">
                <p>银行卡反面</p>
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="alterationInfoForm.bankCardReverse"
                    class="upload-img"
                    :preview-src-list="[alterationInfoForm.bankCardReverse]"
                    :src="alterationInfoForm.bankCardReverse"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc22"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            :disabled="true"
            type="textarea"
            v-model="alterationInfoForm.remark"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="alterationInfoDlg = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 账户变更记录弹框 -->
    <el-dialog
      title="账户信息变更记录"
      width="70%"
      :visible.sync="changeRecordDlg"
      :close-on-click-modal="false"
    >
      <el-table border fit highlight-current-row :data="changeRecordTab">
        <el-table-column
          property="bankAcctNo"
          label="变更前开户行帐号"
          width="150"
        ></el-table-column>
        <el-table-column
          property="alterBankAcctNo"
          label="变更后开户行帐号"
          width="150"
        ></el-table-column>
        <el-table-column
          property="bankMobile"
          label="银行预留手机号"
        ></el-table-column>
        <el-table-column
          property="applyTime"
          label="申请时间"
          width="200"
        ></el-table-column>
        <el-table-column
          :formatter="formatState"
          label="状态"
          width="80"
          prop="status"
        >
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              type="success"
              size="mini"
              plain
              @click="changeRecordInfo(scope.row)"
              icon="el-icon-document"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="page-container">
        <pagination
          :total="changeRecordTotal"
          :page.sync="changeRecordListQuery.page"
          :limit.sync="changeRecordListQuery.limit"
          @pagination="getChangeRecordTab"
        />
      </div>
    </el-dialog>

    <!-- 弹框商户切换 -->
    <el-dialog title="选择商户类型" :visible.sync="checkDialog" width="25%">
      <h4>请根据实际情况进行商户入网申请</h4>
      <div style="display: inline-block; margin-right: 10px">
        <div
          class="commercial"
          @click="choose(0)"
          :class="chooseIndex == 0 ? 'choose' : ''"
        >
          <h3>企业商户</h3>
          <p>商户拥有对公账户和营业执照</p>
        </div>
        <div
          style="margin-top: 40px"
          class="commercial"
          :class="chooseIndex == 1 ? 'choose' : ''"
          @click="choose(1)"
        >
          <h3>机关事业单位或社会团体</h3>
          <p>商户拥有对公账户和营业执照</p>
        </div>
      </div>
      <div style="display: inline-block">
        <div
          class="commercial"
          @click="choose(2)"
          :class="chooseIndex == 2 ? 'choose' : ''"
        >
          <h3>个体工商户</h3>
          <p>商户拥有个人结算账户和营业执照</p>
        </div>
        <div
          style="margin-top: 40px"
          class="commercial"
          @click="choose(3)"
          :class="chooseIndex == 3 ? 'choose' : ''"
        >
          <h3>小微商户</h3>
          <p>商户拥有个人结算账户</p>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="applyFor">开始申请</el-button>
        <el-button @click="checkDialog = false">取 消</el-button>
      </span>
    </el-dialog>

    <!-- 弹窗 新增 -->
    <el-dialog
      class="spec-dialog"
      :close-on-click-modal="false"
      title="自助签约申请"
      append-to-body
      width="1100px"
      :visible.sync="dialogFormVisible"
      @close="cloDialogFormVisible"
    >
      <h4 style="margin-left: 15px;position-relative;top: 0;left: 0;">
        身份信息
      </h4>
      <h4 style="margin-left: 50px">法定代表人信息</h4>
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="dlgData"
        label-position="left"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="身份证正反面照片" prop="legalIdCard">
          <!-- >> 新的 -->
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <p><span style="color: red">*</span> 身份证头像面</p>
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="dlgData.idFrontImg"
                    class="upload-img"
                    :preview-src-list="[dlgData.idFrontImg]"
                    :src="dlgData.idFrontImg"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc2"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="dlgData.idFrontImg"
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc2"
                  >
                    <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
            <el-col class="idBackImg">
              <div class="dialog-upload-item">
                <p><span style="color: red">*</span> 身份证有效期面</p>
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="dlgData.idBackImg"
                    class="upload-img"
                    :preview-src-list="[dlgData.idBackImg]"
                    :src="dlgData.idBackImg"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc3"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="dlgData.idBackImg"
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc3"
                  >
                    <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item
          label=""
          prop="handIdCard"
          style="position: absolute; top: 177px; left: 568px"
        >
          <!-- >> 新的 -->
          <el-row>
            <el-col>
              <p><span style="color: red">*</span> 手持身份证自拍照</p>
              <div class="dialog-upload-item">
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="dlgData.handIdCard"
                    class="upload-img"
                    :preview-src-list="[dlgData.handIdCard]"
                    :src="dlgData.handIdCard"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc12"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="dlgData.handIdCard"
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc12"
                  >
                    <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>

        <div class="informationBox">
          <el-form-item
            label-width="140px"
            label="法定代表人姓名"
            prop="legalName"
          >
            <el-input
              style="width: 200px"
              v-model="dlgData.legalName"
              placeholder="请输入法定代表人姓名"
            />
          </el-form-item>
          <el-form-item
            label-width="140px"
            label="法定代表人性别"
            prop="legalSex"
            :rules="
              this.chooseIndex == 3
                ? rules.legalSex
                : [
                    {
                      required: false,
                      message: '请选择法定代表人性别',
                      trigger: 'blur',
                    },
                  ]
            "
          >
            <el-select
              v-model="dlgData.legalSex"
              value-key="value"
              placeholder="请选择"
            >
              <el-option
                v-for="item in legalSexList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="家庭地址"
            prop="legalmanHomeAddr"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.legalmanHomeAddr"
              placeholder="请输入法定代表人家庭地址"
            />
          </el-form-item>
          <el-form-item
            label="身份证号码"
            prop="legalIdcardNo"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.legalIdcardNo"
              placeholder="请输入法定代表人身份证号码"
            />
          </el-form-item>
          <el-form-item
            label="身份证有效期"
            label-width="120px"
            prop="legalCardDeadline"
            :rules="
              legalCardCheck == true
                ? []
                : [
                    {
                      required: true,
                      message: '请选择身份证有效期',
                      trigger: 'blur',
                    },
                  ]
            "
          >
            <el-date-picker
              v-model="dlgData.legalCardDeadline"
              type="date"
              align="center"
              placeholder="请选择身份证有效期"
              style="width: 170px"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :disabled="legalCardCheck == true ? true : false"
            >
            </el-date-picker>
            <el-checkbox
              @change="legalCardCheckChange"
              :disabled="
                dlgData.legalCardDeadline == '9999-12-31' ||
                !dlgData.legalCardDeadline
                  ? false
                  : true
              "
              v-model="legalCardCheck"
              style="margin-left: 40px"
              >长期</el-checkbox
            >
          </el-form-item>
          <el-form-item
            label="法人职业"
            prop="legalOccupation"
            label-width="120px"
            :rules="
              this.chooseIndex == 3
                ? rules.legalOccupation
                : [
                    {
                      required: false,
                      message: '请选择法定代表人职业',
                      trigger: 'blur',
                    },
                  ]
            "
          >
            <el-select
              v-model="dlgData.legalOccupation"
              placeholder="请选择"
              @change="legalOccupationC(value)"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="checkLegalmanCareerDesc"
            label="法人职业详细描述"
            prop="legalEmail"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.legalmanCareerDesc"
              placeholder="请输入法人职业详细描述"
            />
          </el-form-item>
          <el-form-item label="邮箱" prop="legalEmail" label-width="120px">
            <el-input
              v-model="dlgData.legalEmail"
              placeholder="请输入法定代表人邮箱地址"
            />
          </el-form-item>
        </div>

        <h4
          style="display: inline-block; position: relative; top: 0; left: -34px"
        >
          商户信息
        </h4>
        <div class="commercialPic">
          <el-form-item label="" prop="businessLicense">
            <p>
              <span
                style="color: red"
                v-if="
                  this.dlgData.regMerType == '00' ||
                  this.dlgData.regMerType == '02'
                "
                >*</span
              >
              营业执照
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.businessLicense"
                      class="upload-img"
                      :preview-src-list="[dlgData.businessLicense]"
                      :src="dlgData.businessLicense"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc4"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="dlgData.businessLicense"
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc4"
                    >
                      <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <!-- <el-form-item label="商户税务登记照" prop="taxRegistration">
  
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <div class="imageUploadBox">
                
                  <el-image
                    v-if="dlgData.taxRegistration"
                    class="upload-img"
                    :preview-src-list="[dlgData.taxRegistration]"
                    :src="dlgData.taxRegistration"
                    alt=""
                  ></el-image>
           
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc5"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
               
                  <el-upload
                    v-if="dlgData.taxRegistration"
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc5"
                  >
                  
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item> -->
          <el-form-item label="" prop="licenceOpeningAccounts">
            <p>
              <span style="color: red" v-if="this.dlgData.regMerType == '00'"
                >*</span
              >
              开户许可证
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.licenceOpeningAccounts"
                      class="upload-img"
                      :preview-src-list="[dlgData.licenceOpeningAccounts]"
                      :src="dlgData.licenceOpeningAccounts"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc11"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="dlgData.licenceOpeningAccounts"
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc11"
                    >
                      <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </div>

        <div class="commercialMessage">
          <el-form-item label="营业名称" prop="shopName" label-width="120px">
            <el-input
              v-model="dlgData.shopName"
              placeholder="请输入营业执照的注册营业名称"
            />
          </el-form-item>
          <el-form-item
            label="营业地区"
            label-width="80px"
            style="margin-right: 150px"
            required
          >
            <el-form-item prop="shopProvinceId" style="width: 100px">
              <el-select
                v-model="dlgData.shopProvinceId"
                placeholder="省/直辖市"
                @change="provinceChange"
                class="shopCity"
              >
                <el-option
                  v-for="item in provinceList"
                  :key="item.id"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              prop="shopCityId"
              style="width: 100px; position: absolute; top: 0; left: 110px"
            >
              <el-select
                v-model="dlgData.shopCityId"
                placeholder="市区"
                @change="cityChange"
                class="shopCity"
              >
                <el-option
                  v-for="item in cityList"
                  :key="item.id"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              prop="shopCountryId"
              style="width: 100px; position: absolute; top: 0; left: 220px"
            >
              <el-select
                v-model="dlgData.shopCountryId"
                placeholder="区/县"
                class="shopCity"
              >
                <el-option
                  v-for="item in areaList"
                  :key="item.id"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form-item>
          <el-form-item label="详细地址" prop="shopAddrExt" label-width="120px">
            <el-input
              v-model="dlgData.shopAddrExt"
              placeholder="请输入有效的详细地址"
            />
          </el-form-item>
          <el-form-item
            label="统一社会信用码"
            prop="shopLic"
            label-width="140px"
          >
            <el-input
              v-model="dlgData.shopLic"
              placeholder="请输入统一社会信用码"
            />
          </el-form-item>
          <el-form-item label="业务选择" prop="product" label-width="120px">
            <el-select
              v-model="dlgData.product.product_id"
              multiple
              placeholder="请选择"
            >
              <el-option
                v-for="item in typeOfPayment"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否开通收支双线" prop="receipt2Line">
            <el-radio-group v-model="dlgData.product.receipt2Line">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="是否有营业场所"
            prop="havingFixedBusiAddr"
            :rules="
              this.chooseIndex == 3
                ? rules.havingFixedBusiAddr
                : [
                    {
                      required: false,
                      message: '请选择是否有营业场所',
                      trigger: 'blur',
                    },
                  ]
            "
          >
            <el-radio-group v-model="dlgData.havingFixedBusiAddr">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="商户传真" prop="fax" label-width="120px">
            <el-input v-model="dlgData.fax" placeholder="请输入商户传真" />
          </el-form-item>
          <el-form-item
            label="终端维护经理"
            prop="lastTerminalManager"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.lastTerminalManager"
              placeholder="请输入终端维护经理"
            />
          </el-form-item>
          <el-form-item
            label="客户维护经理"
            prop="lastClientManager"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.lastClientManager"
              placeholder="请输入客户维护经理"
            />
          </el-form-item>
          <el-form-item
            label="所属服务区域"
            prop="serviceDistrict"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.serviceDistrict"
              placeholder="请输入所属服务区域"
            />
          </el-form-item>
          <el-form-item
            label="细分服务区域"
            prop="detailDistrict"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.detailDistrict"
              placeholder="请输入细分服务区域"
            />
          </el-form-item>
          <el-form-item
            label="发展部门"
            prop="developingDept"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.developingDept"
              placeholder="请输入发展部门"
            />
          </el-form-item>
          <el-form-item
            label="发展人"
            prop="developingPersonId"
            label-width="120px"
          >
            <el-input
              v-model="dlgData.developingPersonId"
              placeholder="请输入发展人"
            />
          </el-form-item>
        </div>

        <div class="closeAccountHoldingBox">
          <div>
            <h4
              style="
                display: inline-block;
                position: relative;
                top: 0;
                left: -34px;
              "
            >
              结算信息
            </h4>

            <el-form-item
              label="账户名称"
              prop="bankAcctName"
              label-width="120px"
            >
              <el-input
                v-model="dlgData.bankAcctName"
                placeholder="请输入与'注册营业名称'同名的账户名称"
              />
            </el-form-item>
            <el-form-item
              label="银行账号"
              prop="bankAcctNo"
              label-width="120px"
            >
              <el-input
                v-model="dlgData.bankAcctNo"
                placeholder="请输入银行账号"
              />
            </el-form-item>

            <el-form-item
              label="银行卡正反面照片"
              prop="bankIdCard"
              v-if="isBankAcct"
            >
              <!-- >> 新的 -->
              <el-row>
                <el-col>
                  <div class="dialog-upload-item">
                    <p><span style="color: red">*</span> 银行卡正面</p>
                    <div class="imageUploadBox">
                      <!-- 已上传的文件 -->
                      <el-image
                        v-if="dlgData.bankCardFront"
                        class="upload-img"
                        :preview-src-list="[dlgData.bankCardFront]"
                        :src="dlgData.bankCardFront"
                        alt=""
                      ></el-image>
                      <!-- 图片为空 -->
                      <el-upload
                        v-else
                        action=""
                        :show-file-list="false"
                        :before-upload="uploadFunc14"
                      >
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                      <!-- 重新上传图片 按钮 -->
                      <el-upload
                        v-if="dlgData.bankCardFront"
                        action=""
                        :show-file-list="false"
                        :before-upload="uploadFunc14"
                      >
                        <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                        <el-button
                          title="点击重新上传"
                          icon="el-icon-upload2"
                          type="primary"
                          size="mini"
                          class="imageUploadBox-icon-upload"
                        ></el-button>
                      </el-upload>
                    </div>
                  </div>
                </el-col>
                <el-col class="idBackImg">
                  <div class="dialog-upload-item">
                    <p><span style="color: red">*</span> 银行卡反面</p>
                    <div class="imageUploadBox">
                      <!-- 已上传的文件 -->
                      <el-image
                        v-if="dlgData.bankCardReverse"
                        class="upload-img"
                        :preview-src-list="[dlgData.bankCardReverse]"
                        :src="dlgData.bankCardReverse"
                        alt=""
                      ></el-image>
                      <!-- 图片为空 -->
                      <el-upload
                        v-else
                        action=""
                        :show-file-list="false"
                        :before-upload="uploadFunc15"
                      >
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                      <!-- 重新上传图片 按钮 -->
                      <el-upload
                        v-if="dlgData.bankCardReverse"
                        action=""
                        :show-file-list="false"
                        :before-upload="uploadFunc15"
                      >
                        <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                        <el-button
                          title="点击重新上传"
                          icon="el-icon-upload2"
                          type="primary"
                          size="mini"
                          class="imageUploadBox-icon-upload"
                        ></el-button>
                      </el-upload>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item label="账户类型" prop="bankAcctType">
              <el-radio-group
                v-model="dlgData.bankAcctType"
                @change="bankAcctTypeChange"
              >
                <el-radio label="0">个人账户</el-radio>
                <el-radio label="1">公司账户</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="法人代表人手机号"
              prop="legalMobile"
              label-width="155px"
            >
              <el-input
                v-model="dlgData.legalMobile"
                placeholder="请输入法人代表手机号"
              />
            </el-form-item>
            <el-form-item label="开户所在地" label-width="100px">
              <el-form-item prop="openAccountProvince" style="width: 100px">
                <el-select
                  v-model="dlgData.openAccountProvince"
                  placeholder="省/直辖市"
                  @change="openAccountProvinceChange"
                  class="shopCity"
                >
                  <el-option
                    v-for="item in provinceList"
                    :key="item.id"
                    :label="item.areaName"
                    :value="item.areaCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                prop="openAccountCity"
                style="width: 100px; position: absolute; top: 0; left: 110px"
              >
                <el-select
                  v-model="dlgData.openAccountCity"
                  placeholder="市区"
                  @change="openAccountCityChange"
                  class="shopCity"
                >
                  <el-option
                    v-for="item in cityList"
                    :key="item.id"
                    :label="item.areaName"
                    :value="item.areaCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                prop="openAccountCountry"
                style="width: 100px; position: absolute; top: 0; left: 220px"
              >
                <el-select
                  v-model="dlgData.openAccountCountry"
                  placeholder="区/县"
                  class="shopCity"
                >
                  <el-option
                    v-for="item in areaList"
                    :key="item.id"
                    :label="item.areaName"
                    :value="item.areaCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form-item>
            <el-form-item label="所属支行" prop="bankName" label-width="90px">
              <el-input
                placeholder="请输入内容"
                v-model="dlgData.bankName"
                class="input-with-select"
              >
                <el-select
                  v-model="bankListBranchName"
                  @change="bankListChange"
                  slot="prepend"
                  placeholder="请选择银行"
                >
                  <el-option
                    v-for="item in bankOptions"
                    :key="item.code"
                    :label="item.bankBranchName"
                    :value="item"
                  >
                  </el-option>
                </el-select>
                <el-button
                  @click="searchBank"
                  slot="append"
                  icon="el-icon-search"
                ></el-button>
              </el-input>
            </el-form-item>

            <el-form-item label="备注">
              <el-input type="textarea" v-model="dlgData.remark"></el-input>
            </el-form-item>
          </div>
          <div>
            <h4
              style="
                display: inline-block;
                position: relative;
                top: 0;
                left: -34px;
              "
            >
              受益人/控股股东信息
            </h4>
            <h4>控股股东</h4>
            <el-form-item
              label="控股股东"
              prop="shareholderName"
              label-width="120px"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderName
                  : [
                      {
                        required: false,
                        message: '请输入控股股东姓名',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-input
                v-model="dlgData.shareholderName"
                style="width: 150px"
                placeholder="请输入控股股东姓名"
              />
              <el-button type="primary" @click="synchronizationLegalPerson"
                >同法人信息</el-button
              >
            </el-form-item>
            <el-form-item
              label="证件类型"
              prop="shareholderCertType"
              label-width="120px"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderCertType
                  : [
                      {
                        required: false,
                        message: '请选择证件类型',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-select
                v-model="dlgData.shareholderCertType"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in shareholderCertTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="证件号码"
              prop="shareholderCertno"
              label-width="120px"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderCertno
                  : [
                      {
                        required: false,
                        message: '请输入控股股东证件号码',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-input
                v-model="dlgData.shareholderCertno"
                placeholder="请输入控股股东证件号码"
              />
            </el-form-item>
            <el-form-item
              label="证件有效期"
              label-width="120px"
              prop="shareholderCertExpire"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderCertExpire
                  : [
                      {
                        required: false,
                        message: '请选择证件有效期',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-date-picker
                v-model="dlgData.shareholderCertExpire"
                type="date"
                align="center"
                placeholder="请选择证件有效期"
                style="width: 160px"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :disabled="shareholderCertificateChecked == true ? true : false"
              >
              </el-date-picker>
              <el-checkbox
                @change="certificateCheckedChange"
                v-model="shareholderCertificateChecked"
                style="margin-left: 50px"
                :disabled="
                  dlgData.shareholderCertExpire == '9999-12-31' ||
                  !dlgData.shareholderCertExpire
                    ? false
                    : true
                "
                >长期</el-checkbox
              >
            </el-form-item>
            <el-form-item
              label="家庭地址"
              prop="shareholderHomeAddr"
              label-width="120px"
              :rules="
                this.chooseIndex !== 3
                  ? rules.shareholderHomeAddr
                  : [
                      {
                        required: false,
                        message: '请输入控股股东家庭地址',
                        trigger: 'blur',
                      },
                    ]
              "
            >
              <el-input
                v-model="dlgData.shareholderHomeAddr"
                placeholder="请输入控股股东家庭地址"
              />
            </el-form-item>

            <h4
              style="
                display: inline-block;
                position: relative;
                top: 0;
                left: -34px;
              "
            >
              受益人
            </h4>

            <div v-for="(item, index) in dlgData.bnfList" :key="index">
              <h4 style="display: inline-block">受益人{{ index + 1 }}</h4>
              <el-button
                ref="deleteShareholder"
                plain
                size="mini"
                @click="deleteShareholder(item, index)"
                v-if="index > 0"
                >删除</el-button
              >
              <el-form-item
                label="受益人"
                :prop="'bnfList.' + index + '.bnfName'"
                label-width="120px"
                :rules="
                  chooseIndex !== 3
                    ? [
                        {
                          required: true,
                          message: '请输入受益人姓名',
                          trigger: 'blur',
                        },
                      ]
                    : [
                        {
                          required: false,
                        },
                      ]
                "
              >
                <el-input
                  v-model="item.bnfName"
                  style="width: 150px"
                  placeholder="请输入受益人姓名"
                />
                <el-button
                  v-if="index == 0"
                  type="primary"
                  @click="synchronizationShareholder(item, index)"
                  >同控股股东</el-button
                >
              </el-form-item>
              <el-form-item
                label="证件类型"
                :prop="'bnfList.' + index + '.bnfCertType'"
                label-width="120px"
                :rules="
                  chooseIndex !== 3
                    ? [
                        {
                          required: true,
                          message: '请选择证件类型',
                          trigger: 'blur',
                        },
                      ]
                    : [
                        {
                          required: false,
                        },
                      ]
                "
              >
                <el-select
                  v-model="item.bnfCertType"
                  value-key="value"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in shareholderCertTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="证件号码"
                :prop="'bnfList.' + index + '.bnfCertno'"
                label-width="120px"
                :rules="
                  chooseIndex !== 3
                    ? [
                        {
                          required: true,
                          message: '请输入受益人证件号码',
                          trigger: 'blur',
                        },
                      ]
                    : [
                        {
                          required: false,
                        },
                      ]
                "
              >
                <el-input
                  v-model="item.bnfCertno"
                  placeholder="请输入受益人证件号码"
                />
              </el-form-item>
              <el-form-item
                label="证件有效期"
                label-width="120px"
                :prop="'bnfList.' + index + '.bnfCertExpire'"
                :rules="
                  chooseIndex !== 3
                    ? [
                        {
                          required: true,
                          message: '请选择证件有效期',
                          trigger: 'blur',
                        },
                      ]
                    : [
                        {
                          required: false,
                        },
                      ]
                "
              >
                <el-date-picker
                  v-model="item.bnfCertExpire"
                  type="date"
                  align="center"
                  placeholder="请选择证件有效期"
                  style="width: 160px"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :disabled="item.bnfCertificateChecked == true ? true : false"
                >
                </el-date-picker>
                <el-checkbox
                  @change="bnfCertificateCheckedChange(item, index)"
                  v-model="item.bnfCertificateChecked"
                  style="margin-left: 50px"
                  :disabled="item.bnfCertExpire ? true : false"
                  >长期</el-checkbox
                >
              </el-form-item>
              <el-form-item
                label="家庭地址"
                :prop="'bnfList.' + index + '.bnfHomeAddr'"
                label-width="120px"
                :rules="
                  chooseIndex !== 3
                    ? [
                        {
                          required: true,
                          message: '请输入受益人家庭地址',
                          trigger: 'blur',
                        },
                      ]
                    : [
                        {
                          required: false,
                        },
                      ]
                "
              >
                <el-input
                  v-model="item.bnfHomeAddr"
                  placeholder="请输入受益家庭地址"
                />
              </el-form-item>
            </div>
            <el-button @click="addBnf">+添加受益人</el-button>
          </div>
        </div>

        <h4>图片信息</h4>
        <div class="imgBox">
          <el-form-item label="" prop="shopFrontPho">
            <p>
              <span
                v-if="
                  (this.dlgData.regMerType == '03' && this,
                  dlgData.havingFixedBusiAddr == '1')
                "
                style="color: red"
                >*</span
              >
              店铺门面照
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.shopFrontPho"
                      class="upload-img"
                      :preview-src-list="[dlgData.shopFrontPho]"
                      :src="dlgData.shopFrontPho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc6"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="dlgData.shopFrontPho"
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc6"
                    >
                      <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" prop="shopIndoorPho">
            <p>
              <span
                v-if="
                  (this.dlgData.regMerType == '03' && this,
                  dlgData.havingFixedBusiAddr == '1')
                "
                style="color: red"
                >*</span
              >
              店铺室内照
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.shopIndoorPho"
                      class="upload-img"
                      :preview-src-list="[dlgData.shopIndoorPho]"
                      :src="dlgData.shopIndoorPho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc7"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="dlgData.shopIndoorPho"
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc7"
                    >
                      <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" prop="assistEvidentiaryMaterialPho">
            <p>
              <span style="color: red" v-if="this.dlgData.regMerType == '03'"
                >*</span
              >
              辅助证明材料
            </p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.assistEvidentiaryMaterialPho"
                      class="upload-img"
                      :preview-src-list="[dlgData.assistEvidentiaryMaterialPho]"
                      :src="dlgData.assistEvidentiaryMaterialPho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc8"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="dlgData.assistEvidentiaryMaterialPho"
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc8"
                    >
                      <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </div>
        <div class="imgBox">
          <el-form-item label="" prop="nexAssistEvidentiaryMaterialPho">
            <p>辅助证明材料2</p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.nexAssistEvidentiaryMaterialPho"
                      class="upload-img"
                      :preview-src-list="[
                        dlgData.nexAssistEvidentiaryMaterialPho,
                      ]"
                      :src="dlgData.nexAssistEvidentiaryMaterialPho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc9"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="dlgData.nexAssistEvidentiaryMaterialPho"
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc9"
                    >
                      <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" prop="institutionalFrameworkCodePho">
            <p><span style="color: red">*</span> 组织机构代码证</p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.institutionalFrameworkCodePho"
                      class="upload-img"
                      :preview-src-list="[
                        dlgData.institutionalFrameworkCodePho,
                      ]"
                      :src="dlgData.institutionalFrameworkCodePho"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFuncX"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="dlgData.institutionalFrameworkCodePho"
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFuncX"
                    >
                      <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" prop="byPeopleCertificate">
            <p>民办非登记证书</p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.byPeopleCertificate"
                      class="upload-img"
                      :preview-src-list="[dlgData.byPeopleCertificate]"
                      :src="dlgData.byPeopleCertificate"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc16"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="dlgData.byPeopleCertificate"
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc16"
                    >
                      <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </div>
        <div style="width:800px">
        <el-form-item label="" prop="wabAppPic" style="display:inline-block">
          <p><span style="color: red">*</span> 商户网站/APP截图</p>
            <!-- >> 新的 -->
            <el-row>
              <el-col>
                <div class="dialog-upload-item">
                  <div class="imageUploadBox">
                    <!-- 已上传的文件 -->
                    <el-image
                      v-if="dlgData.wabAppPic"
                      class="upload-img"
                      :preview-src-list="[dlgData.wabAppPic]"
                      :src="dlgData.wabAppPic"
                      alt=""
                    ></el-image>
                    <!-- 图片为空 -->
                    <el-upload
                      v-else
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc23"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- 重新上传图片 按钮 -->
                    <el-upload
                      v-if="dlgData.wabAppPic"
                      action=""
                      :show-file-list="false"
                      :before-upload="uploadFunc23"
                    >
                      <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                      <el-button
                        title="点击重新上传"
                        icon="el-icon-upload2"
                        type="primary"
                        size="mini"
                        class="imageUploadBox-icon-upload"
                      ></el-button>
                    </el-upload>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        <el-form-item label="" prop="otherMaterial" style="display:inline-block;margin-left:176px">
          <p>其他材料</p>
          <!-- >> 新的 -->
          <el-row>
            <el-col>
              <div class="dialog-upload-item">
                <div class="imageUploadBox">
                  <!-- 已上传的文件 -->
                  <el-image
                    v-if="dlgData.otherMaterial"
                    class="upload-img"
                    :preview-src-list="[dlgData.otherMaterial]"
                    :src="dlgData.otherMaterial"
                    alt=""
                  ></el-image>
                  <!-- 图片为空 -->
                  <el-upload
                    v-else
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc13"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <!-- 重新上传图片 按钮 -->
                  <el-upload
                    v-if="dlgData.otherMaterial"
                    action=""
                    :show-file-list="false"
                    :before-upload="uploadFunc13"
                  >
                    <!-- <img title="点击重新上传" v-if="addData.imgUrl" :src="addData.imgUrl" class="upload-img"> -->
                    <el-button
                      title="点击重新上传"
                      icon="el-icon-upload2"
                      type="primary"
                      size="mini"
                      class="imageUploadBox-icon-upload"
                    ></el-button>
                  </el-upload>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false" icon="el-icon-back"
          >取消</el-button
        >
        <el-button type="primary" @click="temporaryStorage" icon="el-icon-plus"
          >存草稿</el-button
        >
        <el-button type="success" @click="addFunc" icon="el-icon-check"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
// import { findOrgBranchAll } from '@/api/dataDic'
import {
  communitySignPage,
  picUpload,
  threeLinkage, //省市区
  branchBankList, //所属支行
  complexUpload, //详细采集档案资料上传接口
  communitySignMcc, //获取行业信息
  agreementSign, //签约
  requestAccountVerify, //对公打款验证
  companyAccountVerify, //对公账户认证
  complexAlterAcctinfo, //商户信息变更
  alterPage, //信息变更记录
  complexUploadStaging, //存草稿
  // saveDdgroup,
  // delDdGroup,
  // upDateDdgroup,
} from "@/api/enterpriseMan/index";
import * as utils from "@/utils";
import { uploadImg, imgScale } from "@/utils/uploadImg";
// import { fetchList } from '@/api/article'
import waves from "@/directive/waves"; // Waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // Secondary package based on el-pagination
let dlgDataEmpty = {
  id: "",
  picAliUrl: [],
  accesser_id: "89815017372600D1",
  accesser_user_id: "",
  regMerType: "",
  idFrontImg: "", // 身份证头像面照片
  idBackImg: "", // 身份证有效期面照片
  handIdCard: "", //手持身份证自拍
  legalName: "", //法人姓名
  legalSex: "", //法人性别
  legalmanHomeAddr: "", //法人家庭地址
  legalIdcardNo: "", //法人身份证号码
  legalCardDeadline: "", //身份证有效期
  legalOccupation: "", //法人职业
  legalmanCareerDesc: "", //法人职业详细描述
  legalEmail: "", //邮箱
  shopName: "", //营业名称
  businessLicense: "", //营业执照照片
  mccCode: "5331", //营业执照类别
  shopProvinceId: "", //营业省份
  shopCityId: "", //营业市
  shopCountryId: "", //营业区
  shopAddrExt: "", //详细地址
  shopLic: "", //统一社会信用码
  // taxRegistration: "", //商户税务登记照
  licenceOpeningAccounts: "", //开户许可证
  bankAcctName: "", //开户帐号名称
  bankAcctNo: "", //银行账号
  bankCardFront: "", //银行卡正面
  bankCardReverse: "", //银行卡反面
  bankAcctType: "", //账户类型
  legalMobile: "", //法人手机号
  openAccountProvince: "", //开户所在省
  openAccountCity: "", //开户所在市
  openAccountCountry: "", //开户所在区
  bankName: "", //所属支行名
  bankNo: "", //所属支行行号
  specialSettleBusiness: "0",
  request_seq: "",
  legal_nationality: "0",
  sign_type: "SHA-256",
  service: "complex_upload",
  request_date: "**************",
  shareholderName: "", //控股股东
  shareholderCertType: "", //证件类型
  shareholderCertno: "", //证件号码
  shareholderCertExpire: "", //控股股东证件有限期
  shareholderHomeAddr: "", //控股股东家庭住址
  remark: "",
  //受益人列表
  bnfList: [
    {
      bnfName: "", //受益人姓名
      bnfCertType: "", //证件类型
      bnfCertno: "", //证件号码
      bnfCertExpire: "", //证件有效期
      bnfHomeAddr: "", //家庭地址
      bnfCertificateChecked: false,
    },
  ],
  shopFrontPho: "", //店铺门面照
  shopIndoorPho: "", //店铺室内照
  assistEvidentiaryMaterialPho: "", //辅助证明材料
  nexAssistEvidentiaryMaterialPho: "", //辅助证明材料2
  institutionalFrameworkCodePho: "", //组织机构代码证
  byPeopleCertificate: "", //民办证书
  wabAppPic: "", //商户网站/APP截图
  otherMaterial: "", //其他材料
  product: {
    product_id: [], //开通业务id
    receipt2Line: "", //是否开通收支双线
  },
  havingFixedBusiAddr: "", //是否有营业场所
  fax: "", //商户传真
  lastTerminalManager: "", //终端维护经理
  lastClientManager: "", //客户维护经理
  serviceDistrict: "", //所属服务区域
  detailDistrict: "", //细分服务区域
  developingDept: "", //发展部门
  developingPersonId: "", //发展人
  picList: [],
  // document_type: [], //图片类型
  // document_name: [], //图片名称
  // file_path: [], //图片路径
  // file_size: [], //图片大小
  umsQrcodeList: "", //二维码

  //开户行名称、地址、回显用
  bankAddressName: {},
};

// let newDlgData = {
//   id:"",
//   picAliUrl: [],
//   // accesserId:"89815017372600D1",
//   legalOccupation: "5",
//   shareholderCertType: "2",
//   legalMobile: "***********",
//   bankAcctType: "0",
//   bankNo: "************",
//   shopLic: "***************",
//   mccCode: "5331",
//   bankAcctName: "何自助",
//   shopProvinceId: "46",
//   legalEmail: "<EMAIL>",
//   specialSettleBusiness: "0",
//   legalName: "何自助",
//   legalSex: "5",
//   bankAcctNo: "****************",
//   // signType:"SHA-256",
//   shopCountryId: "460105",
//   product: [
//     {
//       receipt2Line: "0",
//       product_id: "8",
//     },
//   ],
//   shopAddrExt: "黄埔区科学城科研路12号",
//   shopName: "何自助小卖部",
//   shareholderName: "何自助",
//   legalIdcardNo: "320623195607134430",
//   shareholderHomeAddr: "这是股东家庭住址",
//   legalmanHomeAddr: "这是法人家庭住址",
//   legalNationality: "0",
//   // service:"complex_upload",
//   // requestDate:"**************",
//   legalCardDeadline: "2030-12-06",
//   shopCityId: "460100",
//   shareholderCertno: "320623195607134430",
//   picList: [
//     {
//       document_name: "法人身份证",
//       file_path: "c76ecc8a-8957-4b26-9d78-736a6888004f",
//       file_size: "9536",
//       document_type: "0001",
//     },
//     {
//       document_name: "法人身份证反面",
//       file_path: "c76ecc8a-8957-4b26-9d78-736a6888004f",
//       file_size: "9536",
//       document_type: "0011",
//     },
//     {
//       document_name: "银行卡正面照",
//       file_path: "c76ecc8a-8957-4b26-9d78-736a6888004f",
//       file_size: "9536",
//       document_type: "0025",
//     },
//     {
//       document_name: "商户营业执照",
//       file_path: "c76ecc8a-8957-4b26-9d78-736a6888004f",
//       file_size: "9536",
//       document_type: "0002",
//     },
//     {
//       document_name: "门头照片",
//       file_path: "c76ecc8a-8957-4b26-9d78-736a6888004f",
//       file_size: "9536",
//       document_type: "0005",
//     },
//     {
//       document_name: "室内照片",
//       file_path: "c76ecc8a-8957-4b26-9d78-736a6888004f",
//       file_size: "9536",
//       document_type: "0015",
//     },
//     {
//       document_name: "自拍照",
//       file_path: "c76ecc8a-8957-4b26-9d78-736a6888004f",
//       file_size: "9536",
//       document_type: "0007",
//     },
//   ],
//   accesserUserId: "**************0001",
//   regMerType: "01",
//   // requestSeq:"11aaf9d47c784593ab5993c1f6d49ba9",
//   shareholderCertExpire: "2030-11-11",
// };

let informationChangeFormEmpty = {
  bankAcctNo: "",
  bankMobile: "",
  alterBankAcctNo: "",
  bankName: "",
  idFrontImg: "",
  bankCardFront: "",
  bankCardReverse: "",
  picList: [],
  picAliUrl: [],
  openAccountProvince: "",
  openAccountCity: "",
  openAccountCountry: "",
  remark: "",
  merNo: "", //商户号
  alterBankNo: "", //变更后开户行号
};

export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      idFrontImg: "",

      bankOptions: [], //银行列表
      bankListBranchName: "",

      companyAccountVerifyDlg: false, //打款账户认证
      companyAccount: "", //自助签约对公账号
      umsRegId: "", //自助签约平台流水号

      informationChangeDlg: false, //信息变更弹框
      dlgIdentification: "", //弹框标识
      changeRecordDlg: false, //变更记录弹框
      changeRecordTab: [], //变更记录
      alterationInfoDlg: false, //变更详情
      whetherDraft: "", //草稿标识
      alterationInfoForm: {
        merNo: "",
        bankAcctNo: "",
        bankMobile: "",
        alterBankAcctNo: "",
        idFrontImg: "",
        bankCardFront: "",
        bankCardReverse: "",
        remark: "",
      },

      infoCheck: false,
      formLabelWidth: "120px",
      checkLegalmanCareerDesc: false, //法人职业详细
      chooseIndex: "", //选择商户类型
      commercialTenantCheck: "",

      shareholderNo: "",
      //省市区
      provinceList: [],
      cityList: [],
      areaList: [],
      //证件类型
      shareholderCertTypeList: [
        {
          value: "1",
          label: "身份证",
        },
        {
          value: "2",
          label: "护照",
        },
        {
          value: "3",
          label: "军官证",
        },
        {
          value: "4",
          label: "警官证",
        },
        {
          value: "5",
          label: "士兵证",
        },
        {
          value: "6",
          label: "台湾居民来往大陆通行证",
        },
        {
          value: "7",
          label: "回乡证",
        },
        {
          value: "8",
          label: "港澳居民来往内地通行证",
        },
        {
          value: "10",
          label: "港澳台居民居住证",
        },
        {
          value: "11",
          label: "营业执照",
        },
        {
          value: "12",
          label: "组织机构代码证",
        },
        {
          value: "13",
          label: "税务登记证",
        },
        {
          value: "14",
          label: "商业登记证",
        },
        {
          value: "15",
          label: "民办非企业登记证书",
        },
        {
          value: "16",
          label: "批文证明",
        },
      ],
      //支付类型
      typeOfPayment: [
        {
          value: "0",
          label: "银联卡",
        },
        {
          value: "1",
          label: "全民付",
        },
        {
          value: "2",
          label: "POS通",
        },
        {
          value: "4",
          label: "营销联盟",
        },
        {
          value: "8",
          label: "公共支付-通用",
        },
        {
          value: "16",
          label: "代付业务",
        },
        {
          value: "21",
          label: "统一会员卡",
        },
        {
          value: "in1",
          label: "APP支付",
        },
        {
          value: "in2",
          label: "H5支付",
        },
        {
          value: "in3",
          label: "公众号支付",
        },
        {
          value: "in4",
          label: "小程序支付",
        },
      ],

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      // dlgDataNew: JSON.parse(JSON.stringify(newDlgData)),
      //变更账户信息弹框
      informationChangeForm: JSON.parse(
        JSON.stringify(informationChangeFormEmpty)
      ),
      legalCardCheck: false, //法人身份证有效期是否长期
      shareholderCertificateChecked: false, //股东证件是否长期
      bnfCertificateChecked: false, //受益人证件是否长期
      tableKey: 0,
      oldList: [],
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        size: 20,
        limit: 20,
        label: "",
        type: "",
      },
      changeRecordListQuery: {
        page: 1,
        size: 20,
        limit: 20,
        label: "",
        type: "",
        merNo: "",
      },
      changeRecordTotal: 0,
      //法人性别
      legalSexList: [
        {
          value: "0",
          label: "未知",
        },
        {
          value: "1",
          label: "男性",
        },
        {
          value: "2",
          label: "女性",
        },
        {
          value: "5",
          label: "女性改（变）为男性",
        },
        {
          value: "6",
          label: "男性改（变）为女性",
        },
        {
          value: "9",
          label: "未说明的性别",
        },
      ],
      //法人职业
      options: [
        {
          value: "0",
          label: "各类专业、技术人员",
        },
        {
          value: "1",
          label: "国家机关、党群组织、企事业单位的负责人",
        },
        {
          value: "2",
          label: "办事人员和有关人员",
        },
        {
          value: "3",
          label: "商业工作人员",
        },
        {
          value: "4",
          label: "服务性工作人员",
        },
        {
          value: "5",
          label: "农林牧渔劳动者",
        },
        {
          value: "6",
          label: "生产工作、运输工作和部分体力劳动者",
        },
        {
          value: "7",
          label: "不便分类的其他劳动者",
        },
      ],
      dialogFormVisible: false,
      checkDialog: false,
      rules: {
        // legalIdCard: [
        //   {
        //     required: true,
        //     message: "请上传身份证正反面照片",
        //     trigger: "blur",
        //   },
        // ], //身份证正反
        legalName: [
          { required: true, message: "请输入法定代表人姓名", trigger: "blur" },
        ], //法人姓名
        legalmanHomeAddr: [
          { required: true, message: "请输入法人家庭地址", trigger: "blur" },
        ], //法人家庭地址
        legalSex: [
          {
            required: true,
            message: "请选择法定代表人性别",
            trigger: "blur",
          },
        ], //法人性别
        legalIdcardNo: [
          {
            required: true,
            message: "请输入法定代表人身份证号码",
            trigger: "blur",
          },
        ], //法人身份证号
        // legalCardDeadline: [
        //   { required: true, message: "请选择身份证有效期", trigger: "blur" },
        // ], //法人身份证有效期
        legalOccupation: [
          { required: true, message: "请选择法人职业", trigger: "blur" },
        ], //法人职业
        legalEmail: [
          { required: true, message: "请输入法人邮箱地址", trigger: "blur" },
        ], //法人邮箱
        // businessLicense: [
        //   { required: true, message: "请上传营业执照照片", trigger: "blur" },
        // ], //营业执照
        shopName: [
          { required: true, message: "请输入营业名称", trigger: "blur" },
        ], //营业名称
        shopProvinceId: [{ required: true, message: "请选择营业省份", trigger: "change" }], //营业省
        shopCityId: [{ required: true, message: "请选择营业市", trigger: "change" }], //营业市
        shopCountryId: [
          { required: true, message: "请选择营业地区", trigger: "change" },
        ], //营业区
        shopAddrExt: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ], //详细地址
        shopLic: [
          { required: true, message: "请输入统一社会信用码", trigger: "blur" },
        ], //统一社会信用码
        product: [{ required: true, message: "请选择业务", trigger: "blur" }], //业务选择
        havingFixedBusiAddr: [
          { required: true, message: "请选择是否有营业场所", trigger: "blur" },
        ], //营业场所
        // receipt2Line: [
        //   {
        //     required: true,
        //     message: "请选择是否开通收支双线",
        //     trigger: "blur",
        //   },
        // ], //收支双线
        // taxRegistration: [
        //   { required: true, message: "请上传商户税务登记照", trigger: "blur" },
        // ], //商户税务登记照
        bankAcctName: [
          { required: true, message: "请输入账户名称", trigger: "blur" },
        ], //账户名称
        bankAcctNo: [
          { required: true, message: "请输入银行账户", trigger: "blur" },
        ], //银行账号
        bankAcctType: [
          { required: true, message: "请选择账户类型", trigger: "blur" },
        ], //账户类型
        legalMobile: [
          { required: true, message: "请输入法人代表手机号", trigger: "blur" },
        ], //法人代表手机号
        // openAccountProvince: [
        //   { required: true, message: "请选择开户省份", trigger: "blur" },
        // ], //开户省份
        // openAccountCity: [
        //   { required: true, message: "请选择开户城市", trigger: "blur" },
        // ], //开户城市
        // openAccountCountry: [
        //   { required: true, message: "请选择开户地区", trigger: "blur" },
        // ], //开户地区
        bankName: [
          { required: true, message: "请输入所属支行", trigger: "blur" },
        ], //所属支行
        shareholderName: [
          { required: true, message: "请输入控股股东姓名", trigger: "change" },
        ], //控股股东姓名
        shareholderCertType: [
          { required: true, message: "请选择证件类型", trigger: "blur" },
        ], //证件类型
        shareholderCertno: [
          { required: true, message: "请输入股东证件号码", trigger: "blur" },
        ], //股东证件号码
        shareholderCertExpire: [
          { required: true, message: "请选择股东证件有效期", trigger: "blur" },
        ], //股东证件有效期
        shareholderHomeAddr: [
          { required: true, message: "请输入股东家庭地址", trigger: "change" },
        ], //股东家庭地址
        // bnfName: [
        //   { required: false, message: "请输入受益人姓名", trigger: "blur" },
        // ], //受益人姓名
        // bnfCertType: [
        //   { required: false, message: "请选择受益人证件类型", trigger: "blur" },
        // ], //受益人证件类型
        // bnfCertno: [
        //   { required: false, message: "请输入受益人证件号码", trigger: "blur" },
        // ], //受益人证件号码
        // bnfCertExpire: [
        //   {
        //     required: false,
        //     message: "请选择受益人证件有效期",
        //     trigger: "blur",
        //   },
        // ], //受益人证件有效期
        // bnfHomeAddr: [
        //   { required: false, message: "请输入受益人家庭地址", trigger: "blur" },
        // ], //受益人家庭地址
        // shopFrontPho: [
        //   { required: true, message: "请上传店铺门面照", trigger: "blur" },
        // ], //店铺门面照
        // shopIndoorPho: [
        //   { required: true, message: "请上传店铺室内照", trigger: "blur" },
        // ], //店铺室内照
        // assistEvidentiaryMaterialPho: [
        //   { required: true, message: "请上传辅助证明材料照", trigger: "blur" },
        // ], //辅助证明材料照
        // taxRegistrationPho: [
        //   { required: true, message: "请上传税务登记照", trigger: "blur" },
        // ], //税务登记照
        // institutionalFrameworkCodePho: [
        //   {
        //     required: true,
        //     message: "请上传组织机构代码证照",
        //     trigger: "blur",
        //   },
        // ], //组织机构代码证照
      },
      informationChangeRules: {
        bankMobile: [
          { required: true, message: "请输入银行绑定手机号", trigger: "blur" },
        ],
        alterBankAcctNo: [
          {
            required: true,
            message: "请输入变更后开户行账号",
            trigger: "blur",
          },
        ],
        bankName: [
          { required: true, message: "请输入开户行", trigger: "blur" },
        ],
      },
      moneyForm: {
        money: "",
      },
      //对公账户认证
      moneyFormRules: {
        money: [
          { required: true, message: "请输入打款金额/分", trigger: "blur" },
        ], //打款金额
      },
      downloadLoading: false,
      isProduct: "", //处理后Product
      isBankAcct: "", //银行卡正反显示
    };
  },
  created() {
    this.getList();
    this.getProvinceList();
    let userInfo = JSON.parse(window.localStorage.userInfo);
    this.dlgData.accesser_user_id = userInfo.id;
    this.getIndustryInformation();
  },
  methods: {
    //获取行业信息
    getIndustryInformation() {
      communitySignMcc().then((res) => {
        console.log(res);
      });
    },
    //商户选择
    choose(index) {
      this.chooseIndex = "0" + index;
      this.dlgData.regMerType = "0" + index;
      console.log(this.chooseIndex);
      console.log(this.dlgData.regMerType, "this.dlgData.regMerType");
    },
    //法人职业选择
    legalOccupationC() {
      if (this.legalOccupation == "7") {
        this.dlgData.checkLegalmanCareerDesc = true;
      }
      console.log(this.dlgData.legalOccupation);
    },
    formatState(row, column) {
      switch (row.status) {
        case "00":
          return "签约中";
        case "01":
          return "签约成功";
        case "02":
          return "入网审核中";
        case "03":
          return "入网成功";
        case "04":
          return "入网失败";
        case "05":
          return "对公账户待验证或异常";
        case "06":
          return "风控审核中";
        case "11":
          return "短信签生成合同成功";
        case "18":
          return "资料填写中";
        case "28":
          return "资料验证失败";
        case "31":
          return "冻结账户";
        case "88":
          return "草稿";
        default:
          return "其他错误";
      }
    },
    //关闭信息变更弹框
    cloInformationChangeDlg(){
         this.$refs.informationChangeForm.resetFields();
    },
    // 【【 阿里图片上传
    uploadFunc2(file) {
      console.log(file);
      // 身份证头像面
      // formAction('/unity/communitySign/picUpload', {file}).then((res)=>{
      //   console.log(res)
      // })
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        console.log(res);
        if (res.data.code == "200") {
          let newPic = {
            document_name: "法人身份证",
            file_path: "",
            file_size: "",
            document_type: "0001",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.idFrontImg = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ idFrontImg: res });
            this.informationChangeForm.picAliUrl.push({ idFrontImg: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc3(file) {
      // 身份证有效期面
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "身份证反面",
            file_path: "",
            file_size: "",
            document_type: "0011",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.idBackImg = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ idBackImg: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc4(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "商户营业执照",
            file_path: "",
            file_size: "",
            document_type: "0002",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.businessLicense = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ businessLicense: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    // uploadFunc5(file) {
    //   picUpload({ file: file }).then((res) => {
    //      if (res.data.code == "200") {
    //        let newPic = {
    //          document_name: "商户税务登记证",
    //          file_path: "",
    //          file_size: "",
    //          document_type: "0003",
    //        };
    //        newPic.file_path = res.data.data.file_path;
    //        newPic.file_size = res.data.data.file_size;
    //        this.dlgData.picList.push(newPic);
    //   uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
    //     this.dlgData.taxRegistration = res;
    //     this.dlgData.picAliUrl.push({ taxRegistration: res });
    //     this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    //   });

    //      }else {
    //       this.$message.error(res.data.msg);
    //     }
    //   });
    // },
    uploadFunc6(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "门头照片",
            file_path: "",
            file_size: "",
            document_type: "0005",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.shopFrontPho = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ shopFrontPho: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc7(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "室内照片",
            file_path: "",
            file_size: "",
            document_type: "0015",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.shopIndoorPho = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ shopIndoorPho: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc8(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "辅助证明材料1",
            file_path: "",
            file_size: "",
            document_type: "0013",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.assistEvidentiaryMaterialPho = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ assistEvidentiaryMaterialPho: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc9(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "辅助证明材料2",
            file_path: "",
            file_size: "",
            document_type: "0014",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.nexAssistEvidentiaryMaterialPho = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({
              nexAssistEvidentiaryMaterialPho: res,
            });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFuncX(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "组织机构代码证",
            file_path: "",
            file_size: "",
            document_type: "0004",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.institutionalFrameworkCodePho = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ institutionalFrameworkCodePho: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc11(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "开户许可证",
            file_path: "",
            file_size: "",
            document_type: "0006",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.licenceOpeningAccounts = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ licenceOpeningAccounts: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc12(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "手持身份证自拍照",
            file_path: "",
            file_size: "",
            document_type: "0006",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.handIdCard = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ handIdCard: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc13(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "其他材料",
            file_path: "",
            file_size: "",
            document_type: "0006",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.otherMaterial = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ otherMaterial: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc14(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "银行卡正面照",
            file_path: "",
            file_size: "",
            document_type: "0025",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.bankCardFront = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ bankCardFront: res });
            this.informationChangeForm.picAliUrl.push({ bankCardFront: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc15(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "银行卡反面照",
            file_path: "",
            file_size: "",
            document_type: "0026",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.bankCardReverse = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ bankCardReverse: res });
            this.informationChangeForm.picAliUrl.push({ bankCardReverse: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc16(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "民办非登记证书",
            file_path: "",
            file_size: "",
            document_type: "0032",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.byPeopleCertificate = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ byPeopleCertificate: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc20(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "法人身份证",
            file_path: "",
            file_size: "",
            document_type: "0001",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.informationChangeForm.idFrontImg = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.informationChangeForm.picAliUrl.push({ idFrontImg: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc21(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "银行卡正面照",
            file_path: "",
            file_size: "",
            document_type: "0025",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.informationChangeForm.bankCardFront = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.informationChangeForm.picAliUrl.push({ bankCardFront: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc22(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "银行卡反面照",
            file_path: "",
            file_size: "",
            document_type: "0026",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.informationChangeForm.bankCardReverse = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.informationChangeForm.picAliUrl.push({ bankCardReverse: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    uploadFunc23(file) {
      let loading = this.$loading({
        lock: true,
        text: "上传中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      picUpload({ file: file }).then((res) => {
        if (res.data.code == "200") {
          let newPic = {
            document_name: "商户网站/APP截图",
            file_path: "",
            file_size: "",
            document_type: "0034",
          };
          newPic.file_path = res.data.data.file_path;
          newPic.file_size = res.data.data.file_size;
          this.dlgData.picList.push(newPic);
          uploadImg(file, "hr/data/hr_IDCard2_").then((res) => {
            this.dlgData.wabAppPic = res;
            setTimeout(() => {
              loading.close();
              this.$message.success("上传成功");
            }, 200);
            this.dlgData.picAliUrl.push({ wabAppPic: res });
            this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
          });
        } else {
          setTimeout(() => {
            loading.close();
            this.$message.error(res.data.msg);
          }, 200);
        }
      });
    },
    // 获取数据
    getList() {
      this.list = [];
      this.listOld = [];
      this.listLoading = true;
      let sendObj = {
        page: this.listQuery.page,
        label: this.listQuery.label,
        limit: this.listQuery.limit,
      };
      communitySignPage(sendObj).then((res) => {
        this.listLoading = false;
        let code = res.data.code;
        let msg = res.data.msg;
        if (code === "200") {
          let data = res.data.data;
          // let list = res.data.list;
          if (data != null) {
            this.total = res.data.page.total;
            // this.total = 10
            this.oldList = JSON.parse(JSON.stringify(data));
            this.list = JSON.parse(JSON.stringify(data));
          } else {
            this.$message({
              type: "warning",
              message: msg,
            });
          }
        } else {
          this.$message.error(msg);
        }
      });
    },
    searchFunc() {
      this.listQuery.page = 1;
      this.listQuery.size = 20;
      this.getList();
    },
    // 获取省数据
    getProvinceList() {
      threeLinkage(101).then((res) => {
        if (res.data.code == 200) {
          this.provinceList = res.data.data;
        }
      });
    },
    legalCardCheckChange() {
      console.log(this.legalCardCheck, "this.dlgData.legalCardCheck");
      if (this.legalCardCheck == true) {
        this.dlgData.legalCardDeadline = "9999-12-31";
      }
    },
    certificateCheckedChange() {
      if (this.shareholderCertificateChecked == true) {
        this.dlgData.shareholderCertExpire = "9999-12-31";
      }
    },
    bnfCertificateCheckedChange(item, index) {
      if (this.dlgData.bnfList[index].bnfCertificateChecked == true) {
        this.dlgData.bnfList[index].bnfCertExpire = "9999-12-31";
      }
    },
    //账户类型改变
    bankAcctTypeChange() {
      if (this.dlgData.bankAcctType == 0) {
        this.isBankAcct = true;
      } else {
        this.isBankAcct = false;
        this.dlgData.bankCardFront = "";
        this.dlgData.bankCardReverse = "";
      }
    },
    // 省改变
    provinceChange() {
      this.getCityList(this.dlgData.shopProvinceId);
      this.dlgData.shopCityId = "";
      this.dlgData.shopCountryId = "";
      this.areaList = [];
    },
    // 市改变
    cityChange() {
      this.getAreaList(this.dlgData.shopCityId);
      this.dlgData.shopCountryId = "";
    },
    // 开户地市改变
    openAccountCityChange() {
      this.getAreaList(this.dlgData.openAccountCity);
      this.dlgData.openAccountCountry = "";
    },
    // 开户地省改变
    openAccountProvinceChange() {
      this.getCityList(this.dlgData.openAccountProvince);
      this.dlgData.openAccountCity = "";
      this.dlgData.openAccountCountry = "";
      this.areaList = [];
    },
    //搜索开户行
    searchBank() {
      if (!this.dlgData.openAccountProvince) {
        this.$message.error("请选择开户所在地");
      }
      let areaCodes = "";
      if (this.dlgData.openAccountProvince) {
        areaCodes = this.dlgData.openAccountProvince;
      }
      if (this.dlgData.openAccountCity) {
        areaCodes = this.dlgData.openAccountCity;
      }
      if (this.dlgData.openAccountCountry) {
        areaCodes = this.dlgData.openAccountCountry;
      }
      if (this.dlgData.bankName) {
        let sendObj = {
          areaCode: areaCodes, //	可以传2位的省，4位的市或者6位的区	body	false	string
          key: this.dlgData.bankName,
        };

        // this.dlgData.openAccountCity
        branchBankList(sendObj).then((res) => {
          this.bankOptions = res.data.data.branchBankList;
        });
      } else if (this.informationChangeForm.bankName) {
        let sendObj = {
          areaCode: this.informationChangeForm.openAccountCountry, //	可以传2位的省，4位的市或者6位的区	body	false	string
          key: this.informationChangeForm.bankName,
        };
        branchBankList(sendObj).then((res) => {
          this.bankOptions = res.data.data.branchBankList;
        });
      }
    },
    bankListChange(item) {
      if (this.dlgIdentification == "informationChangeForm") {
        this.informationChangeForm.bankName =
          this.bankListBranchName.bankBranchName;
        this.informationChangeForm.alterBankNo = item.code;
      } else {
        this.dlgData.bankName = this.bankListBranchName.bankBranchName;
        this.dlgData.bankNo = item.code;
      }
    },
    // 获取市数据
    getCityList(code) {
      if (utils.isNull(code)) {
        return;
      }
      this.cityList = [];
      threeLinkage(202, code).then((res) => {
        if (res.data.code == 200) {
          this.cityList = res.data.data;
        }
      });
    },

    // 获取县数据
    getAreaList(code) {
      if (utils.isNull(code)) {
        return;
      }
      this.areaList = [];
      threeLinkage(303, code).then((res) => {
        if (res.data.code == 200) {
          this.areaList = res.data.data;
          console.log(res, "this.areaList.res");
        }
      });
    },

    //控股股东同法人
    synchronizationLegalPerson() {
      this.dlgData.shareholderName = this.dlgData.legalName;
      this.dlgData.shareholderHomeAddr = this.dlgData.legalmanHomeAddr;
      this.dlgData.shareholderCertno = this.dlgData.legalIdcardNo;
      this.dlgData.shareholderCertType = "1";
      this.dlgData.shareholderCertExpire = this.dlgData.legalCardDeadline;
    },
    //受益人同股东
    synchronizationShareholder(item, index) {
      this.dlgData.bnfList[index].bnfName = this.dlgData.shareholderName;
      this.dlgData.bnfList[index].bnfCertType =
        this.dlgData.shareholderCertType;
      this.dlgData.bnfList[index].bnfCertno = this.dlgData.shareholderCertno;
      this.dlgData.bnfList[index].bnfCertExpire =
        this.dlgData.shareholderCertExpire;
      this.dlgData.bnfList[index].bnfHomeAddr =
        this.dlgData.shareholderHomeAddr;
    },
    //添加受益人
    addBnf(item) {
      this.dlgData.bnfList.push(
        //增加就push进数组一个新值
        {
          bnfCertExpire: "",
          bnfCertType: "",
          bnfCertno: "",
          bnfHomeAddr: "",
          bnfName: "",
        }
      );
    },
    //删除受益人
    deleteShareholder(item, index) {
      if (this.dlgData.bnfList.length <= 1) {
        //如果只有一个输入框则不显示
        return false;
      }
      this.dlgData.bnfList.splice(index, 1); //删除了数组中对应的数据也就将这个位置的输入框删除
    },
    // [[ 表格按钮事件
    //修改
    toEdit(row) {
      this.dlgData.id = row.id;
      if (row.product=="{'product_id':[],'receipt2Line':''}") {
        if (row.product != this.isProduct) {
          let newArr = [];
          let receipt2Line = "";
          let newProduct = JSON.parse(row.product);
          for (let i of newProduct) {
            newArr.push(i.product_id);
            receipt2Line = i.receipt2Line;
          }
          let oldProduct = {
            product_id: newArr,
            receipt2Line: receipt2Line,
          };
          row.product = JSON.stringify(oldProduct);
          this.isProduct = JSON.stringify(oldProduct);
        }
      }

      this.dlgData = JSON.parse(JSON.stringify(row));
      this.dlgData.shopProvinceId = this.dlgData.shopProvinceId + "0000";
      this.dlgData.shopCityId = this.dlgData.shopCityId + "00";
      console.log(this.dlgData.shopProvinceId, "this.dlgData.shopProvinceId");
      console.log(this.dlgData.shopCityId, "this.dlgData.shopCityId");
      this.getCityList(this.dlgData.shopProvinceId);
      this.getAreaList(this.dlgData.shopCityId);
      this.whetherDraft = "isEdit";
      this.infoCheck = true;
      let picAliUrl = JSON.parse(row.picAliUrl);

      picAliUrl.forEach((item) => {
        if (item.idFrontImg) {
          this.dlgData.idFrontImg = item.idFrontImg;
        }
        if (item.idBackImg) {
          this.dlgData.idBackImg = item.idBackImg;
        }
        if (item.businessLicense) {
          this.dlgData.businessLicense = item.businessLicense;
        }
        if (item.taxRegistration) {
          this.dlgData.taxRegistration = item.taxRegistration;
        }
        if (item.shopFrontPho) {
          this.dlgData.shopFrontPho = item.shopFrontPho;
        }
        if (item.shopIndoorPho) {
          this.dlgData.shopIndoorPho = item.shopIndoorPho;
        }
        if (item.assistEvidentiaryMaterialPho) {
          this.dlgData.assistEvidentiaryMaterialPho =
            item.assistEvidentiaryMaterialPho;
        }
        if (item.nexAssistEvidentiaryMaterialPho) {
          this.dlgData.nexAssistEvidentiaryMaterialPho =
            item.nexAssistEvidentiaryMaterialPho;
        }
        if (item.institutionalFrameworkCodePho) {
          this.dlgData.institutionalFrameworkCodePho =
            item.institutionalFrameworkCodePho;
        }
        if (item.licenceOpeningAccounts) {
          this.dlgData.licenceOpeningAccounts = item.licenceOpeningAccounts;
        }
        if (item.handIdCard) {
          this.dlgData.handIdCard = item.handIdCard;
        }
        if (item.otherMaterial) {
          this.dlgData.otherMaterial = item.otherMaterial;
        }
        if (item.wabAppPic) {
          this.dlgData.wabAppPic = item.wabAppPic;
        }
        if (item.bankCardFront) {
          this.dlgData.bankCardFront = item.bankCardFront;
        }
        if (item.bankCardReverse) {
          this.dlgData.bankCardReverse = item.bankCardReverse;
        }
        if (item.byPeopleCertificate) {
          this.dlgData.byPeopleCertificate = item.byPeopleCertificate;
        }
      });

      this.dlgData.picAliUrl = picAliUrl;
      this.dlgData.product = JSON.parse(row.product);
      this.dlgData.picList = JSON.parse(row.picList);
      this.dlgData.bnfList = JSON.parse(row.bnfList);
      this.dlgData.bankAddressName = JSON.parse(row.bankAddressName);

      this.dlgData.openAccountProvince =
        this.dlgData.bankAddressName.openAccountProvince;
      this.dlgData.openAccountCity =
        this.dlgData.bankAddressName.openAccountCity;
      this.dlgData.bankName = this.dlgData.bankAddressName.bankName;
      this.getCityList(this.dlgData.openAccountProvince);
      this.getAreaList(this.dlgData.openAccountCity);
    },
    // 详情
    toDesc(row) {
      this.dlgData.id = row.id;
      console.log(row.product,"row.product");
      // debugger
      if (row.product=="{'product_id':[],'receipt2Line':''}") {
        console.log(row.product,"row.product");
        console.log(this.isProduct,"this.isProduct");
        if (row.product != this.isProduct) {
          let newArr = [];
          let receipt2Line = "";
          let newProduct = JSON.parse(row.product);
          for (let i of newProduct) {
            newArr.push(i.product_id);
            receipt2Line = i.receipt2Line;
          }
          let oldProduct = {
            product_id: newArr,
            receipt2Line: receipt2Line,
          };
          row.product = JSON.stringify(oldProduct);
          this.isProduct = JSON.stringify(oldProduct);
        }
      }

      this.dlgData = JSON.parse(JSON.stringify(row));
      this.getCityList(this.dlgData.shopProvinceId);
      this.getAreaList(this.dlgData.shopCityId);
      if (row.status == "88") {
        this.whetherDraft = "saveDraft";
      } else {
        this.whetherDraft = "";
      }
      this.infoCheck = true;
      let picAliUrl = JSON.parse(row.picAliUrl);

      picAliUrl.forEach((item) => {
        if (item.idFrontImg) {
          this.dlgData.idFrontImg = item.idFrontImg;
        }
        if (item.idBackImg) {
          this.dlgData.idBackImg = item.idBackImg;
        }
        if (item.businessLicense) {
          this.dlgData.businessLicense = item.businessLicense;
        }
        if (item.taxRegistration) {
          this.dlgData.taxRegistration = item.taxRegistration;
        }
        if (item.shopFrontPho) {
          this.dlgData.shopFrontPho = item.shopFrontPho;
        }
        if (item.shopIndoorPho) {
          this.dlgData.shopIndoorPho = item.shopIndoorPho;
        }
        if (item.assistEvidentiaryMaterialPho) {
          this.dlgData.assistEvidentiaryMaterialPho =
            item.assistEvidentiaryMaterialPho;
        }
        if (item.nexAssistEvidentiaryMaterialPho) {
          this.dlgData.nexAssistEvidentiaryMaterialPho =
            item.nexAssistEvidentiaryMaterialPho;
        }
        if (item.institutionalFrameworkCodePho) {
          this.dlgData.institutionalFrameworkCodePho =
            item.institutionalFrameworkCodePho;
        }
        if (item.licenceOpeningAccounts) {
          this.dlgData.licenceOpeningAccounts = item.licenceOpeningAccounts;
        }
        if (item.handIdCard) {
          this.dlgData.handIdCard = item.handIdCard;
        }
        if (item.otherMaterial) {
          this.dlgData.otherMaterial = item.otherMaterial;
        }
        if (item.wabAppPic) {
          this.dlgData.wabAppPic = item.wabAppPic;
        }
        if (item.bankCardFront) {
          this.dlgData.bankCardFront = item.bankCardFront;
        }
        if (item.bankCardReverse) {
          this.dlgData.bankCardReverse = item.bankCardReverse;
        }
        if (item.byPeopleCertificate) {
          this.dlgData.byPeopleCertificate = item.byPeopleCertificate;
        }
      });

      this.dlgData.picAliUrl = picAliUrl;
      this.dlgData.product = JSON.parse(row.product);
      this.dlgData.picList = JSON.parse(row.picList);
      this.dlgData.bnfList = JSON.parse(row.bnfList);
      this.dlgData.bankAddressName = JSON.parse(row.bankAddressName);

      this.dlgData.openAccountProvince =
        this.dlgData.bankAddressName.openAccountProvince;
      this.dlgData.openAccountCity =
        this.dlgData.bankAddressName.openAccountCity;
      this.dlgData.bankName = this.dlgData.bankAddressName.bankName;
      this.getCityList(this.dlgData.openAccountProvince);
      this.getAreaList(this.dlgData.openAccountCity);
    },
    //关闭详情
    closeSpecDialog() {
      this.dlgData = {
        id: "",
        picAliUrl: [],
        accesser_id: "89815017372600D1",
        accesser_user_id: "",
        regMerType: "",
        idFrontImg: "", // 身份证头像面照片
        idBackImg: "", // 身份证有效期面照片
        handIdCard: "", //手持身份证自拍
        legalName: "", //法人姓名
        legalSex: "", //法人性别
        legalmanHomeAddr: "", //法人家庭地址
        legalIdcardNo: "", //法人身份证号码
        legalCardDeadline: "", //身份证有效期
        legalOccupation: "", //法人职业
        legalmanCareerDesc: "", //法人职业详细描述
        legalEmail: "", //邮箱
        shopName: "", //营业名称
        businessLicense: "", //营业执照照片
        mccCode: "5331", //营业执照类别
        shopProvinceId: "", //营业省份
        shopCityId: "", //营业市
        shopCountryId: "", //营业区
        shopAddrExt: "", //详细地址
        shopLic: "", //统一社会信用码
        // taxRegistration: "", //商户税务登记照
        licenceOpeningAccounts: "", //开户许可证
        bankAcctName: "", //开户帐号名称
        bankAcctNo: "", //银行账号
        bankCardFront: "", //银行卡正面
        bankCardReverse: "", //银行卡反面
        bankAcctType: "", //账户类型
        legalMobile: "", //法人手机号
        openAccountProvince: "", //开户所在省
        openAccountCity: "", //开户所在市
        openAccountCountry: "", //开户所在区
        bankName: "", //所属支行名
        bankNo: "", //所属支行行号
        specialSettleBusiness: "0",
        request_seq: "",
        legal_nationality: "0",
        sign_type: "SHA-256",
        service: "complex_upload",
        request_date: "**************",
        shareholderName: "", //控股股东
        shareholderCertType: "", //证件类型
        shareholderCertExpire: "", //控股股东证件有限期
        shareholderHomeAddr: "", //控股股东家庭住址
        remark: "",
        //受益人列表
        bnfList: [
          {
            bnfName: "", //受益人姓名
            bnfCertType: "", //证件类型
            bnfCertno: "", //证件号码
            bnfCertExpire: "", //证件有效期
            bnfHomeAddr: "", //家庭地址
          },
        ],
        shopFrontPho: "", //店铺门面照
        shopIndoorPho: "", //店铺室内照
        assistEvidentiaryMaterialPho: "", //辅助证明材料
        nexAssistEvidentiaryMaterialPho: "", //辅助证明材料2
        institutionalFrameworkCodePho: "", //组织机构代码证
        wabAppPic:"",//商户网站/APP截图
        byPeopleCertificate: "", //民办证书
        otherMaterial: "", //其他材料
        product: {
          product_id: [], //开通业务id
          receipt2Line: "", //是否开通收支双线
        },
        havingFixedBusiAddr: "", //是否有营业场所
        fax: "", //商户传真
        lastTerminalManager: "", //终端维护经理
        lastClientManager: "", //客户维护经理
        serviceDistrict: "", //所属服务区域
        detailDistrict: "", //细分服务区域
        developingDept: "", //发展部门
        developingPersonId: "", //发展人
        picList: [],
        // document_type: [], //图片类型
        // document_name: [], //图片名称
        // file_path: [], //图片路径
        // file_size: [], //图片大小
        umsQrcodeList: "", //二维码
      };

      // this.dlgData.idFrontImg=""
      // this.dlgData.idBackImg=""
      // this.dlgData.businessLicense=""
      // this.dlgData.taxRegistration=""
      // this.dlgData.shopFrontPho=""
      // this.dlgData.shopIndoorPho=""
      // this.dlgData.assistEvidentiaryMaterialPho=""
      // this.dlgData.nexAssistEvidentiaryMaterialPho=""
      // this.dlgData.institutionalFrameworkCodePho=""
      // this.dlgData.licenceOpeningAccounts=""
      // this.dlgData.handIdCard=""
      // this.dlgData.otherMaterial=""
      // this.dlgData.bankCardFront=""
      // this.dlgData.bankCardReverse=""
      // this.dlgData.byPeopleCertificate=""
      this.$refs.dataForm.clearValidate();
      // this.$refs['dataForm'].resetField()
    },
    //关闭新增
    cloDialogFormVisible() {
      this.closeSpecDialog();
    },
    //对公打款验证
    remitVerify(row) {
      let listQuery = {
        companyAccount: row.bankAcctNo,
        umsRegId: row.umsRegId,
      };
      requestAccountVerify(listQuery).then((res) => {
        if(res.data.code==200){
          this.$message.success(res.data.msg)
        }else{
          this.$message.error(res.data.msg)
        }
      });
    },
    //对公账户认证
    accountVerify(row) {
      this.companyAccountVerifyDlg = true;
      this.companyAccount = row.bankAcctNo;
      this.umsRegId = row.umsRegId;
    },
    corporateAccount() {
      let listQuery = {
        transAmt: this.moneyForm.money,
        companyAccount: this.companyAccount,
        umsRegId: this.umsRegId,
      };
      companyAccountVerify(listQuery).then((res) => {
        if(res.data.code==200){
          this.$message.success(res.data.msg)
        }else{
          this.$message.error(res.data.msg)
        }
      });
    },
    //账户信息变更
    informationChange(row) {
      this.informationChangeForm.bankAcctNo = row.bankAcctNo;
      this.informationChangeForm.merNo = row.merNo;
      this.informationChangeDlg = true;
      this.dlgIdentification = "informationChangeForm";
      console.log(row, "row");
    },
    // 账户信息开户地市改变
    informationCityChange() {
      this.getAreaList(this.informationChangeForm.openAccountCity);
      this.informationChangeForm.openAccountCountry = "";
    },
    // 账户信息开户地省改变
    informationProvinceChange() {
      this.getCityList(this.informationChangeForm.openAccountProvince);
      this.informationChangeForm.openAccountCity = "";
      this.informationChangeForm.openAccountCountry = "";
      this.areaList = [];
    },
    confirmChange() {
      this.$refs["informationChangeForm"].validate((valid) => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.informationChangeForm));
          sendObj.picList = JSON.stringify(sendObj.picList);
          sendObj.picAliUrl = JSON.stringify(sendObj.picAliUrl);
          complexAlterAcctinfo(sendObj).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.msg);
              this.informationChangeDlg = false;
              this.$nextTick(() => {
                this.$refs["informationChangeForm"].clearValidate();
                this.$refs["informationChangeForm"].resetFields();
                this.informationChangeForm.idFrontImg = "";
                this.informationChangeForm.bankCardFront = "";
                this.informationChangeForm.bankCardReverse = "";
              });
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    //账户信息变更查询
    getChangeRecordTab(row) {
      this.changeRecordDlg = true;
      if (this.changeRecordListQuery.merNo == "") {
        this.changeRecordListQuery.merNo = row.merNo;
      }
      console.log(this.changeRecordListQuery, "this.changeRecordListQuery");
      let sendObj = {
        page: this.changeRecordListQuery.page,
        size: this.changeRecordListQuery.size,
        limit: this.changeRecordListQuery.limit,
        merNo: this.changeRecordListQuery.merNo,
      };
      alterPage(sendObj).then((res) => {
        if (res.data.code == 200) {
          this.changeRecordTotal = res.data.page.total;
          this.changeRecordTab = res.data.data;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    //变更记录详情
    changeRecordInfo(row) {
      console.log(row);
      this.alterationInfoDlg = true;
      this.alterationInfoForm.merNo = row.merNo;
      this.alterationInfoForm.bankAcctNo = row.bankAcctNo;
      this.alterationInfoForm.bankMobile = row.bankMobile;
      this.alterationInfoForm.alterBankAcctNo = row.alterBankAcctNo;
      this.alterationInfoForm.remark = row.remark;
      // this.alterationInfoForm.idFrontImg=row.idFrontImg
      // this.alterationInfoForm.bankCardFront=row.bankCardFront
      // this.alterationInfoForm.bankCardReverse=row.bankCardReverse
      let picAliUrl = JSON.parse(row.picAliUrl);
      picAliUrl.forEach((item) => {
        if (item.idFrontImg) {
        }
        if (item.bankCardFront) {
          this.alterationInfoForm.bankCardFront = item.bankCardFront;
        }
        if (item.bankCardReverse) {
          this.alterationInfoForm.bankCardReverse = item.bankCardReverse;
        }
      });
    },
    // 修改
    // confirmEdit(row) {
    //   let index = row.index;
    //   this.oldList[index].groupText = row.groupText;
    //   this.oldList[index].dgdescribe = row.dgdescribe;
    //   row.edit = false;

    //   let sendObj = {
    //     id: row.id,
    //     groupText: row.groupText,
    //     groupChar: row.groupChar,
    //     scope: row.scope,
    //     creator: row.creator,
    //     dgdescribe: row.dgdescribe,
    //     groupValue: row.groupValue,
    //   };

    //   // upDateDdgroup(sendObj).then((res) => {
    //   //   let code = res.data.code;
    //   //   // let data = res.data.data
    //   //   // let list = res.data.list
    //   //   let msg = res.data.msg;

    //   //   if (code === "200") {
    //   //     this.$message({
    //   //       message: "数据字典修改成功！",
    //   //       type: "success",
    //   //     });
    //   //   } else {
    //   //     this.$message({
    //   //       message: msg,
    //   //       type: "error",
    //   //     });
    //   //   }
    //   // });
    // },
    // 签约
    signaContract(row) {
      let umsRegId = row.umsRegId;
      agreementSign(umsRegId).then((res) => {
        console.log(res, "umsRegId");
        if (res.data.code === "200") {
          window.open(res.data.data.url);
        } else {
          this.$message({
            message: msg,
            type: "error",
          });
        }
      });
    },
    // 删除
    delFunc(id) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        // delDdGroup({ id }).then((res) => {
        //   let code = res.data.code;
        //   // let data = res.data.data
        //   // let list = res.data.list
        //   let msg = res.data.msg;
        //   if (code === "200") {
        //     this.$message({
        //       message: "数据字典删除成功",
        //       type: "success",
        //     });
        //     this.getList();
        //   } else {
        //     this.$message({
        //       message: msg,
        //       type: "error",
        //     });
        //   }
        // });
      });
    },
    // 新增方法
    handleCreate() {
      this.checkDialog = true;
      // this.$nextTick(() => {
      //   this.$refs["dataForm"].clearValidate();
      // });
    },
    applyFor() {
      if (!this.chooseIndex) {
        this.chooseIndex = "00";
      }
      if (!this.dlgData.regMerType) {
        this.dlgData.regMerType = "00";
      }
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    //暂存
    temporaryStorage() {
      if ((this.legalCardCheck = true && !this.dlgData.legalCardDeadline)) {
        this.dlgData.legalCardDeadline = "9999-12-31";
      }
      if (this.shareholderCertificateChecked == true) {
        this.dlgData.shareholderCertExpire = "9999-12-31";
      }
      if (this.shareholderCertificateChecked == true) {
        this.dlgData.shareholderCertExpire = "9999-12-31";
      }
      if (this.dlgData.id) {
        this.dlgData.id = this.dlgData.id;
      } else {
        this.dlgData.id = 0;
      }
      if (this.dlgData.product.product_id) {
        let newArr = [];
        for (let i of this.dlgData.product.product_id) {
          let newObj = {
            product_id: i,
            receipt2Line: this.dlgData.product.receipt2Line?this.dlgData.product.receipt2Line:"0",
          };
          newArr.push(newObj);
        }

        this.dlgData.product = newArr;
      }
      // if(this.isBankAcct==false){
      //   this.dlgData.bankCardFront=""
      //   this.dlgData.bankCardReverse=""
      // }
      let sendObj = JSON.parse(JSON.stringify(this.dlgData));
      sendObj.bankAddressName = {
        openAccountProvince: sendObj.openAccountProvince,
        openAccountCity: sendObj.openAccountCity,
        openAccountCountry: sendObj.openAccountCountry,
        bankName: sendObj.bankName,
      };
      sendObj.bankAddressName = JSON.stringify(sendObj.bankAddressName);
      sendObj.picList = JSON.stringify(sendObj.picList);
      sendObj.bnfList = JSON.stringify(sendObj.bnfList);
      sendObj.product = JSON.stringify(sendObj.product);
      sendObj.picAliUrl = JSON.stringify(sendObj.picAliUrl);
      complexUploadStaging(sendObj).then((res) => {
        if (res.data.code == 200) {
          this.$message({
            message: "保存成功",
            type: "success",
          });
          this.infoCheck = false;
          this.getList();
        } else {
          this.$message({
            message: res.data.msg,
            type: "error",
          });
        }
      });
    },
    //提交
    addFunc() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          if (!this.dlgData.idFrontImg) {
            this.$message({
              type: "warning",
              message: "请上传身份证头像面照片",
            });
            return false;
          }
          if (!this.dlgData.idBackImg) {
            this.$message({
              type: "warning",
              message: "请上传身份证有效期面照片",
            });
            return false;
          }
          if (!this.dlgData.handIdCard) {
            this.$message({
              type: "warning",
              message: "请上传手持身份证自拍照",
            });
            return false;
          }
          if (!this.dlgData.wabAppPic) {
            this.$message({
              type: "warning",
              message: "请上传商户网站/APP截图",
            });
            return false;
          }
          if (
            this.dlgData.regMerType == "00" ||
            this.dlgData.regMerType == "02"
          ) {
            if (!this.dlgData.businessLicense) {
              this.$message({
                type: "warning",
                message: "请上传营业执照照片",
              });
              return false;
            }
          }
          // if (!this.dlgData.taxRegistration) {
          //       this.$message({
          //         type: 'warning',
          //         message: '请上传商户税务登记照片',
          //       })
          //     return false
          //   }
          if (this.dlgData.regMerType == "00") {
            if (!this.dlgData.licenceOpeningAccounts) {
              this.$message({
                type: "warning",
                message: "请上传开户许可证照片",
              });
              return false;
            }
          }
          if (this.dlgData.bankAcctType == "0") {
            if (!this.dlgData.bankCardFront) {
              this.$message({
                type: "warning",
                message: "请上传银行卡正面照片",
              });
              return false;
            }
          }
          if (this.dlgData.bankAcctType == "0") {
            if (!this.dlgData.bankCardReverse) {
              this.$message({
                type: "warning",
                message: "请上传银行卡反面照片",
              });
              return false;
            }
          }
          if (
            this.dlgData.regMerType == "03" &&
            this.dlgData.havingFixedBusiAddr == "1"
          ) {
            if (!this.dlgData.shopFrontPho) {
              this.$message({
                type: "warning",
                message: "请上传店铺门面照片",
              });
              return false;
            }
          }
          if (
            this.dlgData.regMerType == "03" &&
            this.dlgData.havingFixedBusiAddr == "1"
          ) {
            if (!this.dlgData.shopIndoorPho) {
              this.$message({
                type: "warning",
                message: "请上传店铺室内照片",
              });
              return false;
            }
          }
          if (this.dlgData.regMerType == "01") {
            if (!this.dlgData.institutionalFrameworkCodePho) {
              this.$message({
                type: "warning",
                message: "请上传组织机构代码证照片",
              });
              return false;
            }
          }
          // if (!this.dlgData.byPeopleCertificate) {
          //       this.$message({
          //         type: 'warning',
          //         message: '请上传民办证书照片',
          //       })
          //     return false
          //   }
          if (this.dlgData.regMerType == "03") {
            if (!this.dlgData.assistEvidentiaryMaterialPho) {
              this.$message({
                type: "warning",
                message: "请上传辅助证明材料",
              });
              return false;
            }
          }
          // if(this.isBankAcct==false){
          //   this.dlgData.bankCardFront=""
          //   this.dlgData.bankCardReverse=""
          // }

          if ((this.legalCardCheck = true && !this.dlgData.legalCardDeadline)) {
            this.dlgData.legalCardDeadline = "9999-12-31";
          }
          if ((this.shareholderCertificateChecked = true)) {
            this.dlgData.shareholderCertExpire = "9999-12-31";
          }
          if (this.dlgData.id) {
            this.dlgData.id = this.dlgData.id;
          }
          JSON.parse(
            JSON.stringify([this.dlgData.idFrontImg, this.dlgData.idBackImg])
          );
          let oldProduct = this.dlgData.product;
          if (this.dlgData.product.product_id) {
            let newArr = [];
            for (let i of this.dlgData.product.product_id) {
              let newObj = {
                product_id: i,
                receipt2Line: this.dlgData.product.receipt2Line
                  ? this.dlgData.product.receipt2Line
                  : "0",
              };
              newArr.push(newObj);
            }
            this.dlgData.product = newArr;
          }
          let oldShopProvinceId = this.dlgData.shopProvinceId;
          let oldShopCityId = this.dlgData.shopCityId;
          let shopProvinceId = this.dlgData.shopProvinceId.toString();
          this.dlgData.shopProvinceId = shopProvinceId.substring(
            0,
            shopProvinceId.length - 4
          );
          let shopCityId = this.dlgData.shopCityId.toString();
          this.dlgData.shopCityId = shopCityId.substring(
            0,
            shopCityId.length - 2
          );
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));
          sendObj.bankAddressName = {
            openAccountProvince: sendObj.openAccountProvince,
            openAccountCity: sendObj.openAccountCity,
            openAccountCountry: sendObj.openAccountCountry,
            bankName: sendObj.bankName,
          };
          sendObj.bankAddressName = JSON.stringify(sendObj.bankAddressName);
          sendObj.picList = JSON.stringify(sendObj.picList);
          sendObj.bnfList = JSON.stringify(sendObj.bnfList);
          sendObj.product = JSON.stringify(sendObj.product);
          console.log(sendObj.product);
          sendObj.picAliUrl = JSON.stringify(sendObj.picAliUrl);
          let loading = this.$loading({
            lock: true,
            text: "提交中...",
            background: "rgba(0, 0, 0, 0.7)",
          });
          complexUpload(sendObj).then((res) => {
            console.log(res, "tongletongle");
            if (res.data.code == 200) {
              setTimeout(() => {
                loading.close();
                this.$message({
                  message: "申请成功",
                  type: "success",
                });
              }, 200);
              this.dialogFormVisible = false;
              this.checkDialog = false;
              this.infoCheck=false
              this.getList();
            } else {
              this.dlgData.product = oldProduct;
              this.dlgData.shopProvinceId = oldShopProvinceId;
              this.dlgData.shopCityId = oldShopCityId;
              loading.close();
              this.$message({
                message: res.data.msg,
                type: "error",
              });
            }
          });
        }
      });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.idBackImg {
  position: absolute;
  left: 254px;
  top: 27px;
}
.spec-dialog {
  padding: 3px 30px;
  overflow-y: auto;
  height: calc(100vh - 140px);
}
.shopCity {
  display: inline-block;
  width: 100px;
}
.commercial {
  border: 1px solid #fff;
  text-align: center;
  align-items: center;
  height: 100px;
  width: 220px;
  cursor: pointer;
}
.commercial:hover {
  border: 1px solid #2db7f5;
}
.choose {
  border: 1px solid #2db7f5;
}
.informationBox {
  width: 800px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.commercialPic {
  width: 800px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.commercialMessage {
  width: 800px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.closeAccountHoldingBox {
  width: 950px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.imgBox {
  width: 800px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
</style>