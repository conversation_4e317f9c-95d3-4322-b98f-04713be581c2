// 坐席dlg 单选

const seatDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    seatId: "",

    seatName: "",
    seatInfo: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    seatId: state => state.seatId,

    seatName: state => state.seatName,
    seatInfo: state => state.seatInfo
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_SEATID: (state, val) => {
      state.seatId = val;
    },

    SET_SEATNAME: (state, val) => {
      state.seatName = val;
    },
    SET_SEATINFO: (state, val) => {
      state.seatInfo = val;
    }
  },

  actions: {}
};

export default seatDlg;
