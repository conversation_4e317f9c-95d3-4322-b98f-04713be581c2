<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' 
      title="选择核算部门" 
      
      :visible.sync="hsbmState" 
      width='600px'
      top='30px'
      append-to-body>
      <div class="">
        <div class='m-tips' style="height: 28px; padding-left: 10px; margin-bottom: 10px;">

          <div class='m-desc' style="float: left; line-height: 27px;">
            <i class='el-icon-warning'></i>&nbsp;只能选择【末级】核算部门
          </div>
        </div>
        <el-input
          placeholder="输入关键字进行过滤"
          style="margin-bottom: 10px;"
          v-model="filterBmLeftText">
        </el-input>
        <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
        <div class='m-dialog-h'>
          <el-tree 
            :data="treeData" 
            ref="treeDom"
            default-expand-all
            :filter-node-method="filterNode"
            @node-click="nodeClick">
          </el-tree>
        </div>
        
      </div>
      <div slot="footer" class="dialog-footer">
        <span class='dialog-footer-span' v-show='hsbmName'>当前选中：{{ hsbmName }}</span>
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
//import { findAccountBranchTree } from '@/api/dataDic'

import { setTimeout } from 'timers';
// import adminDashboard from './admin'
// import editorDashboard from './editor'

export default {
  name: 'Dashboard',
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      
      // 部门树
      treeData: [],
      selectNode: {},
      
      filterBmLeftText: '',  // 部门左侧筛选

      hsbmId: '',
      hsbmName: ''
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ]),
    hsbmState: {
      get: function() {
        let state = this.$store.getters.hsbmState
        if (state) {
          this.hsbmId = ''
          this.hsbmName = ''
          $('.tree-on').removeClass('tree-on')
        }
        return state
      },
      set: function(newVal) {
        this.$store.commit('SET_HSBMSTATE', newVal)
      }
    }
  },
  watch: {
    filterBmLeftText(val) {
      this.$refs.treeDom.filter(val);
    }
  },
  created() {
    // 部门
    //this.findAccountBranchTree()
  },
  methods: {
    // [[ 部门弹窗相关
    // 获取部门
    // findAccountBranchTree() {
    //   findAccountBranchTree().then(res => {
    //     let code = res.data.code
    //     let data = res.data.data
    //     let msg = res.data.msg

    //     if (code === '200') {
    //       this.treeData = JSON.parse(JSON.stringify(res.data.list))
    //     } else {
    //       this.$message.error(msg)
    //     }
    //   })
    // },

    // 节点点击事件
    nodeClick(data, node, mNode) {
      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      // 是否是末级节点
      if (data.finalStage === 0) {
        // this.filterText = data.label
        this.selectNode = data
        // alert(JSON.stringify(data))
        // 获取部门 下 岗位
        this.hsbmId = data.id
        this.hsbmName = data.label
      } else {
        this.$message({
          type: 'warning',
          message: '只能选择末级结算部门'
        })
      }
    },
    
    // 提交
    bumenOkFunc() {
      if (this.hsbmName) { 
        this.$store.commit('SET_HSBMID', '')
        this.$store.commit('SET_HSBMNAME', '')

        setTimeout(() => {
          this.$store.commit('SET_HSBMID', this.hsbmId)
          this.$store.commit('SET_HSBMNAME', this.hsbmName)
          this.closeDialog()
        }, 50)
      } else {
        this.$message({
          type: 'warning',
          message: '请选择部门'
        })
      }
    },
    
    // 筛选部门
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 关闭弹窗 
    closeDialog() {
      this.$store.commit('SET_HSBMSTATE', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.dialog-footer-span {
    font-size: 14px;
    color: #666;
    display: inline-block;
    padding-right: 10px;
  }
</style>