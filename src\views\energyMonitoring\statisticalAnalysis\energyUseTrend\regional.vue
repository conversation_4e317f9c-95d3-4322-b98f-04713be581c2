<template>
  <div class="app-container" style="padding: 0;">
    <div class="filter-container">
      <el-form
        inline
        size="small"
        ref="searchForm"
        class=""
        :model="listQuery"
        label-width="90px"
        @submit.native.prevent
      >
        <el-form-item label="日期类型">
          <el-radio-group
            v-model="listQuery.queryDateType"
            @change="queryDateTypeChange"
          >
            <el-radio label="day">按日</el-radio>
            <el-radio label="month">按月</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="日期" v-if="listQuery.queryDateType == 'day'">
          <el-date-picker
            style="width: 250px"
            @change="getList"
            v-model="listQuery.dateRange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="small"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="日期" v-if="listQuery.queryDateType == 'month'">
          <el-date-picker
            style="width: 250px"
            @change="getList"
            v-model="listQuery.dateRange"
            type="monthrange"
            format="yyyy-MM"
            value-format="yyyy-MM"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            size="small"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="能耗类型">
          <el-select
            v-model="listQuery.energyType"
            placeholder="能耗类型"
            style="width: 100px"
          >
            <el-option
              v-for="item of statusSelect"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="primary"
          size="small"
          @click="getList"
          >搜索</el-button
        >
      </el-form>
    </div>
    <div
      v-if="showChart2"
      id="echart-zhe-regional"
      style="height: 90%; margin-top: 16px"
    ></div>
  </div>
</template>
<script>
import { postAction, getAction } from "@/api";
import { mapGetters } from "vuex";
import * as echarts from "echarts";
import * as utils from "@/utils";
import moment from "moment";
export default {
  components: {},
  data() {
    return {
      showChart2: false,
      listQuery: {
        queryDateType: "day",
        dateRange: [],
        // label: "",
        startTime: "",
        endTime: "",
        queryType: "area",
        energyType: "water"
      },
      exceptionData: {},
      statusSelect: [
        { id: "water", name: "用水" },
        { id: "electricity", name: "用电" }
      ],
      times: []
    };
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    if (this.listQuery.queryDateType == "day") {
      this.listQuery.dateRange = utils.returnToMonth();
    }
    this.getList();
  },
  methods: {
    queryDateTypeChange() {
      if (this.listQuery.queryDateType == "day") {
        this.listQuery.dateRange = utils.returnToMonth();
      } else {
        this.setDefaultMonthRange();
      }
      this.getList()
      // this.listQuery.dateRange = [];
    },
    // 设置默认月份范围（本年一月到当前月）
    setDefaultMonthRange() {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1; // 月份是0-11

      this.listQuery.dateRange = [
        `${currentYear}-01`, // 一月
        `${currentYear}-${currentMonth.toString().padStart(2, "0")}` // 当前月
      ];
    },

    // 生成日期范围数组
    generateDateArray(startDate, endDate) {
      const dates = [];
      let currentDate = moment(startDate);
      const end = moment(endDate);

      while (currentDate <= end) {
        dates.push(currentDate.format("YYYY-MM-DD"));
        currentDate = currentDate.add(1, "days");
      }

      return dates;
    },

    // 生成月份范围数组
    generateMonthArray(startMonth, endMonth) {
      const months = [];
      let currentMonth = moment(startMonth);
      const end = moment(endMonth);

      while (currentMonth <= end) {
        months.push(currentMonth.format("YYYY-MM"));
        currentMonth = currentMonth.add(1, "months");
      }

      return months;
    },

    getList() {
      if (
        utils.isNull(this.listQuery.dateRange) ||
        this.listQuery.dateRange.length <= 0
      ) {
        this.$message.warning("请先选择日期");
        return;
      }
      // 获取日期范围
      const [start, end] = this.listQuery.dateRange;

      // 验证逻辑
      if (this.listQuery.queryDateType == "day") {
        const daysDiff = moment(end).diff(moment(start), "days");
        if (daysDiff > 89) {
          this.$message.error("日期范围不能超过90天");
          this.listQuery.dateRange = [];
          return;
        }
      } else if (this.listQuery.queryDateType == "month") {
        const monthsDiff = moment(end).diff(moment(start), "months");
        if (monthsDiff > 35) {
          this.$message.error("月份范围不能超过36个月");
          this.listQuery.dateRange = [];
          return;
        }
      }
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      if (this.listQuery.queryDateType == "day") {
        // 按日
        this.times = this.generateDateArray(
          sendObj.dateRange[0],
          sendObj.dateRange[1]
        );
      } else if (this.listQuery.queryDateType == "month") {
        // 按月
        this.times = this.generateMonthArray(
          sendObj.dateRange[0],
          sendObj.dateRange[1]
        );
      }
      console.log(this.times, "times");
      if (sendObj.dateRange && sendObj.dateRange.length > 0) {
        if (this.listQuery.queryDateType == "month") {
          sendObj.startTime = sendObj.dateRange[0] + "-01";
          sendObj.endTime = sendObj.dateRange[1] + "-01";
        } else {
          sendObj.startTime = sendObj.dateRange[0];
          sendObj.endTime = sendObj.dateRange[1];
        }
      }
      sendObj.projectId = this.userInfo.projectId;
      this.showChart2 = false;
      postAction(
        `/iot/energy-data/energyConsumptionTrendAnalysis`,
        sendObj
      ).then(res => {
        if (res.data.code == 200) {
          console.log(res.data, "111");

          this.exceptionData = res.data.list;
          console.log(this.exceptionData, "2222");
          this.showChart2 = true;
          this.$nextTick(() => {
            this.setEchartBar2();
          });
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    //创建折线图
    setEchartBar2() {
      if (!this.showChart2 || !this.exceptionData || !this.times) {
        return;
      }

      // 随机颜色生成函数
      const getRandomColor = () => {
        const letters = "0123456789ABCDEF";
        let color = "#";
        for (let i = 0; i < 6; i++) {
          color += letters[Math.floor(Math.random() * 16)];
        }
        return color;
      };

      // 准备图表数据
      const series = this.exceptionData.map(item => {
        const values = item.totalStandardCoal.split(", ").map(Number);
        return {
          name: item.area,
          type: "line",
          data: values,
          symbol: "circle",
          symbolSize: 6,
          lineStyle: {
            width: 2
          },
          itemStyle: {
            color: getRandomColor() // 使用随机颜色
          }
        };
      });

      // 设置图表配置
      const option = {
        title: {
          text: "",
          left: "center"
        },
        tooltip: {
          trigger: "axis",
          formatter: params => {
            let result = `${params[0].axisValue}<br>`;
            params.forEach(param => {
              result += `${param.seriesName}: ${param.value}<br>`;
            });
            return result;
          }
        },
        legend: {
          data: this.exceptionData.map(item => item.area),
          top: 10, // 图例放在顶部
          type: "scroll",
          padding: [5, 20]
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "10%", // 增加底部间距，让图表下移
          top: "20%", // 增加顶部间距
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.times,
          axisLabel: {
            rotate: 45,
            margin: 15 // 增加x轴标签与图表的间距
          }
        },
        yAxis: {
          type: "value",
          name: "",
          nameGap: 25 // 增加y轴名称与轴线的间距
        },
        series: series
      };

      // 渲染图表
      const myChart = echarts.init(
        document.getElementById("echart-zhe-regional")
      );
      myChart.clear();
      myChart.setOption(option);

      // 响应式调整
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.echart-container {
  height: 90%;
}
</style>
