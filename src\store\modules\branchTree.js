// 部门，树形，复选框选择
const bmTree = {
  state: {
    branchTreeState: false,  // 弹窗状态
    branchTreeKeys: '',  // 选中的 id 列表
    branchTreeNames: '',  // 选中的 name 列表
    branchTreeHalfKeys: '',  // 选中的 id 列表(半选)
    branchTreeNodes: '', // 选中的 节点 列表

    branchTreeKeysSet: '',  // 设置 选中的 id 列表
    branchTreeNamesSet: '',  // 设置 选中的 name 列表

    branchTreeIsSelectChild: true,  // 是否全选子节点

    branchTreeIsRoot: true  // 根据 权限

  },

  mutations: {
    SET_BRANCHTREESTATE: (state, val) => {
      state.branchTreeState = val
    },
    SET_BRANCHTREEKEYS: (state, val) => {
      state.branchTreeKeys = val
    },
    SET_BRANCHTREENAMES: (state, val) => {
      state.branchTreeNames = val
    },
    SET_BRANCHTREEHALFKEYS: (state, val) => {
      state.branchTreeHalfKeys = val
    },
    SET_BRANCHTREENODES: (state, val) => {
      state.branchTreeNodes = val
    },
    SET_BRANCHTREEKEYSSET: (state, val) => {
      state.branchTreeKeysSet = val
    },
    SET_BRANCHTREENAMESSET: (state, val) => {
      state.branchTreeNamesSet = val
    },
    SET_BRANCHTREEISSELECTCHILD: (state, val) => {
      state.branchTreeIsSelectChild = val
    },
    SET_BRANCHTREEISROOT: (state, val) => {
      state.branchTreeIsRoot = val
    },

    
  },

  actions: {
    
  }
}

export default bmTree
