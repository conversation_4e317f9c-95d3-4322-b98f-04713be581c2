// 岗位dlg 单选

const postDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    dlgType: "",

    postQuery: {},

    postId: "",

    postInfo: ""
  },

  getters: {
    dlgShow: state => state.dlgShow,

    dlgType: state => state.dlgType,

    postQuery: state => state.postQuery,

    postId: state => state.postId,

    postInfo: state => state.postInfo
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val;
    },

    SET_POSTQUERY: (state, val) => {
      state.postQuery = val;
    },

    SET_POSTID: (state, val) => {
      state.postId = val;
    },

    SET_POSTINFO: (state, val) => {
      state.postInfo = val;
    }
  },

  actions: {}
};

export default postDlg;
