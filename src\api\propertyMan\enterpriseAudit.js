import request from "@/utils/request";

/*
 * 企业审核
 */

// 列表
export function syscompanyPage(data) {
  return request({
    url: `/sys/syscompany/page`,
    method: "post",
    data
  });
}

// 审核
export function auditCompany(data) {
  return request({
    url: `/sys/syscompany/auditCompany`,
    method: "post",
    data
  });
}

// 修改企业模块
export function updateModuleByCompany(data) {
  return request({
    url: `/sys/syscompany/updateModuleByCompany`,
    method: "post",
    data
  });
}

// 查询绑定模块
export function chaxunbangdingmokuai(id) {
  return request({
    url: `/sys/syscompany/chaxunbangdingmokuai/${id}`,
    method: "get"
  });
}
