import request from '@/utils/request'
// import { requestForm } from '@/utils'

// 流程撤回
export function RevokeRejectJump(data) {
  return request({
    url: `/process/service/RevokeRejectJump`,
    method: 'post',
    data
  })
}

// 已办撤回
export function completedRevoke(data) {
  return request({
    url: `/process/service/completed/revoke`,
    method: 'post',
    data
  })
}

// 终止流程
export function hangUp(data) {
  return request({
    url: '/process/service/hangUp',
    method: 'post',
    data
  })
}

// 查询审批意见
export function findHandleOpinion(data) {
  return request({
    url: `/process/service/findHandleOpinion`,
    method: 'post',
    data
  })
}

// 审批操作
export function complete(data) {
  return request({
    url: `/process/service/complete`,
    method: 'post',
    data
  })
}

// 【【 入职申请
// 获取 表单 ID
export function getRzId() {
  return request({
    url: `/sys/findPostLike`,
    method: 'get'
  })
}

// 发起 入职申请单 流程
export function launchProcess(data) {
  return request({
    url: `/process/service/start`,
    method: 'post',
    data
  })
}

// 修改提交
export function stayhairMatterCompl(data) {
  return request({
    url: `/process/service/stayhairMatterCompl`,
    method: 'post',
    data
  })
}
// 】】 入职申请

// 【【  事项 列表
// 1 待发事项
export function stayhairMatter(data) {
  return request({
    url: `/process/service/stayhairMatter`,
    method: 'post',
    data
  })
}
// 待发删除
export function stayhairDel(id) {
  return request({
    url: `/process/stayhair/del/${id}`,
    method: 'get'
  })
}

// 2 已发事项
export function alreadyissued(data) {
  return request({
    url: `/process/service/alreadyissued`,
    method: 'post',
    data
  })
}

// 3 待办事项 page limit userId
export function taskList(data) {
  return request({
    url: `/process/service/taskList`,
    method: 'post',
    data
  })
}

// 4 已办事项 page limit userId
export function alreadydone(data) {
  return request({
    url: `/process/service/alreadydone`,
    method: 'post',
    data
  })
}
// 知会事项
export function findNotifyByDynamic(data) {
  return request({
    url: `/process/service/findNotifyByDynamic`,
    method: 'post',
    data
  })
}
// 知会提交
export function updateNotifyState(data) {
  return request({
    url: `/process/service/updateNotifyState`,
    method: 'post',
    data
  })
}
// 】】 事项 列表

// 单据管理
export function businessSingle(data) {
  return request({
    url: `/process/service/businessSingle`,
    method: 'post',
    data
  })
}

export function paySanctionPage(data) {
  return request({
    url: `/process/service/paySanctionPage`,
    method: 'post',
    data
  })
}


// 流程终止  processInstanceId
export function getProcessInfo(data) {
  return request({
    url: `/process/service/getProcessInfo`,
    method: 'post',
    data
  })
}

// 获取销假记录
export function getLeaveRecords(data) {
  return request({
    url: `/ade/getLeaveRecords`,
    method: 'post',
    data
  })
}

// 串休 - 时间校验

export function breakRecordDateCheck(data) {
  return request({
    url: `/ade/breakRecordDateCheck`,
    method: 'post',
    data
  })
}

// 增岗名称校验
export function addPostCheck(data) {
  return request({
    url: `/sys/addPostCheck`,
    method: 'post',
    data
  })
}

// 兼岗单校验 员工是否可以兼岗
export function staffConPostCheck(userId) {
  return request({
    url: `/sys/staffConPostCheck/${userId}`,
    method: 'get'
  })
}

// 加班单时间校验
export function overtimeProve(data) {
  return request({
    url: `/ade/overtime/prove`,
    method: 'post',
    data
  })
}

// 单据查询 查详情
export function findFromDetailsByProcessId(data) {
  return request({
    url: `/process/service/findFromDetailsByProcessId`,
    method: 'post',
    data
  })
}

// 入职流程前 调用接口
export function saveUserAndInf(data) {
  return request({
    url: `/sys/saveUserAndInf`,
    method: 'post',
    data
  })
}
// 撤销 入职
export function delUserByPhone(data) {
  return request({
    url: `/sys/delUserByPhone`,
    method: 'post',
    data
  })
}

// 增岗直接生效
export function savePostByForm(data) {
  return request({
    url: `/sys/savePostByForm`,
    method: 'post',
    data
  })
}
// 增岗撤销
export function delOrgPostByLabel(data) {
  return request({
    url: `/sys/delOrgPostByLabel`,
    method: 'post',
    data
  })
}

// 获取暂扣发记录
export function findHoldingRecord(data) {
  return request({
    url: `/ade/findHoldingRecord`,
    method: 'post',
    data
  })
}

// 取消特殊考勤单，校验
export function checkStateAtt(data) {
  return request({
    url: `/ade/checkStateAtt`,
    method: 'post',
    data
  })
}

// 事项列表
export function listAllTask(data) {
  return request({
    url: `/process/service/listAllTask`,
    method: 'post',
    data
  })
}

// 根据 员工ids 获取 员工信息列表
export function findUserPostLabelByUserIds(data) {
  return request({
    url: `/sys/findUserPostLabelByUserIds`,
    method: 'post',
    data
  })
}


// 业务协同
//已发
export function issued(data) {
  return request({
    url: `/act/list/issued`,
    method: 'post',
    data
  })
}
//提报
export function start(data) {
  return request({
    url: `/act/process/start`,
    method: 'post',
    data
  })
}
//已发详情
export function processInfo(data) {
  return request({
    url: `/act/process/info`,
    method: 'post',
    data
  })
}
//待办
export function listTodo(data) {
  return request({
    url: `/act/list/todo`,
    method: 'post',
    data
  })
}
//提交审批
export function handle(data) {
  return request({
    url: `/act/task/handle`,
    method: 'post',
    data
  })
}
//处理人意见/回退
export function historic(processInstanceId) {
  return request({
    url: `/act/task/historic/${processInstanceId}`,
    method: 'get',
  })
}
//已办
export function listDone(data) {
  return request({
    url: `/act/list/done`,
    method: 'post',
    data
  })
}
//已办
export function withdrawOrStop(data) {
  return request({
    url: `/act/process/withdrawOrStop`,
    method: 'post',
    data
  })
}
//待发
export function listPoised(data) {
  return request({
    url: `/act/list/poised`,
    method: 'post',
    data
  })
}
//待发删除
export function processDel(processInstanceId) {
  return request({
    url: `/act/process/del/${processInstanceId}`,
    method: 'get',
  })
}
//待办冒泡
export function bubbling(data) {
  return request({
    url: `/act/list/bubbling`,
    method: 'get',
    data
  })
}
//回退提交
export function taskBack(data) {
  return request({
    url: `/act/task/back`,
    method: 'post',
    data
  })
}
//流程创建列表
export function tablePage(data) {
  return request({
    url: `/act/table/page`,
    method: 'post',
    data
  })
}
//新增流程
export function tableAdd(data) {
  return request({
    url: `/act/table/add`,
    method: 'post',
    data
  })
}