<template>
  <el-dialog
    title="详情"
    :append-to-body="true"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    width="800px"
    top="30px"
  >
    <el-table
      ref="tableBar"
      class="m-small-table"
      v-loading="listLoading"
      :data="list"
      max-height="500px"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column
        label="#"
        type="index"
        align="center"
        width="60"
      ></el-table-column>
      <el-table-column label="名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.nodeArchName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.num }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="90"
        class-name="small-padding fixed-width"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="pageTo(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getAction } from "@/api";
export default {
  props: {
    dlgQuery: {
      type: Object,
      default: {}
    },

    dlgData0: {}
  },
  watch: {
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          this.rowData = JSON.parse(JSON.stringify(this.dlgQuery));

          console.log(this.rowData, "rowData");
          console.log(this.dlgQuery, "dlgQuery");

          this.getList();
        }, 100);
      } else {
        this.closeDlg();
      }
    }
  },
  data() {
    return {
      dlgState: false,
      rowData: {},
      list: [],
      listLoading: false
    };
  },
  methods: {
    getList() {
      let userInfo = JSON.parse(window.localStorage.userInfo);
      this.listLoading = true;
      getAction(
        `/iot/equip-node/queryNodeStateNumWithArchId?projectId=${userInfo.projectId}&equipType=${this.rowData.type}&state=${this.rowData.state}`
      ).then(res1 => {
        let res = res1.data;
        if (res.code == 200) {
          this.list = res.data;
          this.listLoading = false;
        } else {
          this.listLoading = false;
          this.$message.error(res.msg);
        }
      });
    },
    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.dlgState = false;
    },
    pageTo(row) {
      //type 摄像机2  物联1
      //pageType 1是设备安全  2是电气火灾  3是环境安全  4是门禁

      if (row.type == 2) {
        let state0 = row.state;
        if (row.state == 999) {
          state0 = 0;
        } else {
          state0 = row.state;
        }
        if (this.rowData.type == 3) {
          this.$router.push({
            path: `/environmentalSafety/cameraManagement`,
            query: { state: state0 }
          });
        }
        if (this.rowData.type == 1) {
          this.$router.push({
            path: `/equipSafeMan/cameraManNew`,
            query: { state: state0 }
          });
        }
        if (this.rowData.type == 2) {
          this.$router.push({
            path: `/electricalFireMonitoring/cameraManagement`,
            query: { state: state0 }
          });
        }
      }

      if (row.type == 1) {
        // 传过去以后
        //  { id: 0, name: "全部" },
        // { id: 3, name: "正常" },
        // { id: 1, name: "参数异常(报警异常)" },
        // { id: 2, name: "通讯异常" },
        let state0 = row.state;
        if (row.state == 999) {
          state0 = '0';
        } else if (row.state == 0) {
          state0 = '3';
        } else {
          state0 = row.state;
        }
        if (this.rowData.type == 3) {
          this.$router.push({
            path: `/environmentalSafety/sensorEquip`,
            query: {
              equipType: "3",
              nodeArchivesId: row.nodeArchId,
              state: state0
            }
          });
        }
        if (this.rowData.type == 1) {
          this.$router.push({
            path: `/equipSafeMan/sensorEquip`,
            query: {
              equipType: "1",
              nodeArchivesId: row.nodeArchId,
              state: state0
            }
          });
        }
        if (this.rowData.type == 2) {
          this.$router.push({
            path: `/electricalFireMonitoring/sensorEquip`,
            query: {
              equipType: "2",
              nodeArchivesId: row.nodeArchId,
              state: state0
            }
          });
        }
      }
    }
  }
};
</script>
