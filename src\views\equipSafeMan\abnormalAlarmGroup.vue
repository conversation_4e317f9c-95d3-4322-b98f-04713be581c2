<template>
  <!-- 异常报警管理 -->
  <div class="app-container">
    <div class="echart-container" id="echart-panel" style="width: 100%"></div>
    <el-dialog
      :close-on-click-modal="false"
      title="异常报警详情"
      :visible.sync="dlgShow"
      width="1200px"
      top="30px"
      append-to-body
    >
      <div class="filter-container">
        <el-form inline @submit.native.prevent>
          <el-form-item label="创建时间：">
            <el-date-picker
              v-model="listQuery.dateRange"
              type="daterange"
              range-separator="~"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            @click="searchFunc"
            >搜索</el-button
          >
        </el-form>
      </div>

      <div class="table-container">
        <el-table
          class="m-small-table"
          height="600px"
          v-loading="listLoading"
          :data="list"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" type="index" width="60" align="center">
          </el-table-column>

          <el-table-column label="传感器名称" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.node_name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="设备标识" width="150">
            <template slot-scope="scope">
               <span v-if="listQuery.type!='109'" class="m-a" @click="showBjDia(scope.row)">{{ scope.row.dev_eui }}</span>
              <span  v-else>{{ scope.row.dev_eui }}</span>
            </template>
          </el-table-column>

          <el-table-column label="所在设备间" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.equip_room_name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="所在设备" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.equip_name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="报警类型" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.alert_name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="报警次数" width="120" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.cishu }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="page-container">
        <pagination
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          @pagination="getList"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-back" @click="dlgShow = false"
          >取 消</el-button
        >
      </div>
    </el-dialog>


    
    <!-- << 弹窗 报警信息 -->
    <el-dialog
      title="历史记录"
      :close-on-click-modal="false"
      :append-to-body="true"
      :visible.sync="diaBjState"
      width="1200px"
      top="30px"
      icon-class="el-icon-info"
    >
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="列表" name="liebiao">
      <el-date-picker
        style="width: 300px"
        class="fl"
        @change="getBjList"
        v-model="diaBjQuery.dateRange"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      >
      </el-date-picker>
      <el-button icon="el-icon-search" type="success" size="mini" class="search-right-btn fl" @click="getBjList">搜索</el-button>

      <div class="clear"></div>

      <el-table
        ref="tableBar"
        class="m-small-table mt10"
        v-loading="listLoading"
        :key="diaBjTableKey"
        :data="diaBjList"
        border
        fit
        highlight-current-row
        style="width: 100%"
        max-height="500px"
      >
        <el-table-column label="#" type="index" align="center" width="70">
          <template slot-scope="scope">
            <span>{{ (diaBjQuery.page - 1) * diaBjQuery.limit + scope.$index + 1 }}</span>
          </template>
        </el-table-column>

        <el-table-column v-for="(item, index) of diaBjTHList" :key="index" :label="item.label">
          <template slot-scope="scope">
            <span>{{ scope.row[item.key] }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        class="mt10"
        v-show="diaBjTotal > 0"
        :total="diaBjTotal"
        :page.sync="diaBjQuery.page"
        :limit.sync="diaBjQuery.limit"
        @pagination="getBjList"
      />
      </el-tab-pane>
      <el-tab-pane label="趋势" name="qushi">
       <el-date-picker
          class="fl ml10"
          style="width: 350px"
          @change="getZxt"
          v-model="zxtQuery.dateRange"
          type="datetimerange"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          size="mini"
        >
        </el-date-picker>
        <el-select
        @change="handleSelectChange"
        class="fl ml10"
        style="width: 200px"
    v-model="zxtQuery.disRespVos"
    multiple
    collapse-tags
    placeholder="请选择">
    <el-option
      v-for="item in options"
      :key="item.type"
      :label="item.name"
      :value="`${item.type},${item.name}`">
    </el-option>
  </el-select>
        <el-button icon="el-icon-search" type="success" size="mini" class="search-right-btn fl" @click="getZxt">搜索</el-button>
  <div class="clear"></div>

        <div
          v-if="showChart2"
          id="echart-bar2"
          style="height: 500px; margin-top: 16px"
        ></div>
    </el-tab-pane>
    </el-tabs>
      <div class="clear"></div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="diaBjState = false" icon="el-icon-back">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Cookie from "js-cookie";
import * as echarts from "echarts";
import Pagination from "@/components/Pagination"; // 分页
import * as utils from "@/utils";
import moment, { localeData } from 'moment'//导入文件
import {
  arrId2Name, // 根据id 获取name
  isNull
} from '@/utils'
import {
  pageAbnormalOfType // 分页
} from "@/api/mzgApi";
import {
  integratedATj // 异常统计
} from "@/api/basicManSystem/comprehenOperateMonitor.js";
// 接口
import { getAction, postAction } from '../../api'
import {
  protocolLoran, // 报警信息
} from '@/api/safetyMonitoringApi'
let dlgDataEmpty = {
  id: "",
  remark: ""
};
//折线图
let zxtQueryEmpty={
  disRespVos:[],
  nodeId:'',
  startTime:'',
  endTime:'',
  dateRange: [],
}
// 报警信息弹窗
let diaBjQueryEmpty = {
  id: '',
  label: '',
  page: 1,
  limit: 10,
  dateRange: [],
}
export default {
  components: {
    Pagination
  },
  data() {
    return {
      dlgRules: {
        remark: [{ required: true, message: "必填字段", trigger: "blur" }]
      },
      list: [],
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
        type: "",
        beginDate: "",
        endDate: "",
        dateRange: ""
      },
      total: 0,
      listLoading: false,

      dlgType: "",
      dlgLoading: false,
      dlgShow: false,
      dlgData: {},

      exceptionData: {},

      echartPanel: null, // echart面板

      clickIndex: -1,

       // << 弹窗 历史记录
       activeName: 'liebiao',
      zxtQuery:JSON.parse(JSON.stringify(zxtQueryEmpty)),
      options:[],
      bjDiaRow:{},
      showChart2: false,
       echartRoom2: null,
       zxtSelect:[],
       // << 弹窗-报警信息
      diaBjTableKey: 0,
      diaBjState: false,
      diaBjTHList: [],
      diaBjList: [],
      diaBjTotal: 0,
      diaBjQuery: JSON.parse(JSON.stringify(diaBjQueryEmpty)),
    };
  },
  created() {
    if (this.$route.query.today) {
      this.listQuery.dateRange = [
        this.$route.query.today,
        this.$route.query.today
      ];
    }
    this.getExceptionATj();
  },
  methods: {
    // 创建图表
    createChart() {
      if (this.exceptionData.length == 0) {
        if (!utils.isNull(this.echartPanel)) {
          this.echartPanel.clear();
        }
        return;
      }
      let legendData = [];
      let xAxisData = [];
      let seriesData = [];
      for (let i of this.exceptionData) {
        legendData.push(i.name);
        xAxisData.push(i.name);
        seriesData.push(i.count);
      }
      // 基于准备好的dom，初始化echarts实例
      this.echartPanel = echarts.init(document.getElementById("echart-panel"));
      // 指定图表的配置项和数据
      let option = {
        // color: function (params) {
        //   let colorList = ['#249CF9', '#EB6F49', '#FDB628', '#00E4EC', '#69FD28', '#C490BF', '#FFF100', '#486A00', '#F6B37F', '#7ECEF4', '#22AC38', '#7E6B5A'];
        //   return colorList[params.dataIndex % colorList.length]
        // },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        formatter: val => {
          this.clickIndex = val[0].dataIndex;
          return val[0]["value"] + "<br>" + val[0]["axisValue"];
        },
        dataZoom: [
          {
            type: "inside"
          }
        ],
        grid: {
          left: "0",
          right: "2%",
          top: "10%",
          bottom: "2%",
          containLabel: true
        },
        legend: {
          data: legendData
        },
        xAxis: {
          type: "category",
          data: xAxisData,
          axisPointer: {
            type: "shadow"
          }
        },
        yAxis: {
          type: "value"
        },
        series: [
          {
            type: "bar",
            barWidth: "50%",
            data: seriesData,
            itemStyle: {
              // 不同颜色
              normal: {
                label: {
                  show: true,
                  position: "top"
                },
                color: function(params) {
                  var colorList0 = [
                    // '#42d2b9',
                    // '#4cb5f2',
                    // '#ddb146',
                    // '#19AA8D',
                    // '#cdbed1',

                    "#249CF9",
                    "#EB6F49",
                    "#FDB628",
                    "#00E4EC",
                    "#69FD28",
                    "#C490BF",
                    "#FFF100",
                    "#486A00",
                    "#F6B37F",
                    "#7ECEF4",
                    "#22AC38",
                    "#7E6B5A"
                  ];
                  let colorList = [];
                  if (seriesData.length > colorList0.length) {
                    for (let i = 0; i < seriesData.length; i++) {
                      let index = i % colorList0.length;

                      colorList.push(colorList0[index]);
                    }
                  } else {
                    colorList = colorList0;
                  }
                  return colorList[params.dataIndex];
                }
              }
            }

            // itemStyle: {
            //   normal: {
            //     label: {
            //       show: true,
            //       position: 'top',
            //     },
            //   },
            // },
          }
        ]
      };

      this.echartPanel.setOption(option);
      this.echartPanel.off("click");
      this.echartPanel.getZr().on("click", e => {
        this.listQuery.type = this.exceptionData[this.clickIndex]["alert_type"];
        this.dlgShow = true;
        this.getList();
      });

      window.addEventListener("resize", () => {
        this.echartPanel.resize();
      });
    },

    // 获取异常统计
    getExceptionATj() {
      getAction(`/iot/integratedATj?equipType=1`).then(res => {
        if (res.data.code == 200) {
          this.exceptionData = res.data.data;
          this.$nextTick(() => {
            this.createChart();
          });
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    getList() {
      this.list = [];
      this.listLoading = true;
      this.listQuery.beginDate = this.listQuery.dateRange
        ? this.listQuery.dateRange[0]
        : "";
      this.listQuery.endDate = this.listQuery.dateRange
        ? this.listQuery.dateRange[1]
        : "";

      pageAbnormalOfType(this.listQuery).then(res => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.total = res.data.page.total;
          this.list = JSON.parse(JSON.stringify(res.data.data));
        } else {
          this.$message.warning(res.data.msg);
        }
      });
    },

    clearQuery(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },

    searchFunc() {
      this.getList();
    },

    editFunc(data, type) {
      this.dlgType = type;
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgData.id = data.id;
      this.dlgShow = true;
    },

    // 弹窗提交
    submitDlg() {
      this.$refs["dlgForm"].validate(valid => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData));
          this.dlgLoading = true;
          let method = this.dlgType === "HANDLE" ? handleAlarm : ignoreAlarm;
          method(postParam).then(res => {
            this.dlgLoading = false;
            if (res.data.code == 200) {
              this.getList();
              this.dlgShow = false;
              this.$message.success(res.data.msg);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
     // << 弹窗-报警信息
     showBjDia(row) {
      // 调用接口
      this.activeName='liebiao',
      row.id=row.node_id
      this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty))
      this.diaBjQuery.id = row.id
      this.bjDiaRow=row
      this.zxtQuery=JSON.parse(JSON.stringify(zxtQueryEmpty))
      this.getBjList()
    },
      // 获取列表
      getBjList() {
      let beginTime = ''
      let endTime = ''
      if (this.diaBjQuery.dateRange != null && this.diaBjQuery.dateRange.length != 0) {
        beginTime = this.diaBjQuery.dateRange[0]
        endTime = this.diaBjQuery.dateRange[1]
      }
      let sendObj = {
        page: this.diaBjQuery.page,
        limit: this.diaBjQuery.limit,
        id: this.diaBjQuery.id,
        beginTime,
        endTime,
      }

      let loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      protocolLoran(sendObj).then((res1) => {
        loading.close()
        let res = res1.data
        if (res.code == '200') {
          if (res.data == null) {
            this.diaBjTotal = 0
            this.$message({
              type: 'warning',
              message: '暂无报警信息',
            })
            return false
          }
          // 表头
          let diaBjTHList = []
          for (let key in res.data.field) {
            let label = res.data.field[key]
            let obj = {
              key,
              label,
            }
            diaBjTHList.push(obj)
          }
          this.diaBjTHList = diaBjTHList

          // 表格数据
          this.diaBjTotal = res.data.total

          this.diaBjList = res.list
          this.diaBjState = true
          this.diaBjTableKey++
        } else {
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },
      //tab切换
      handleClick(tab, event) {
      this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty))
      this.zxtQuery=JSON.parse(JSON.stringify(zxtQueryEmpty))
      this.diaBjQuery.id = this.bjDiaRow.id
      this.zxtSelect=[]
      console.log(tab, event);
      if (tab.name=='liebiao') {
        this.getBjList()
      }else{
        const now = moment();
        const start =moment(now).subtract(3, 'hours');
        const end =  moment().format("YYYY-MM-DD HH:mm");
        let startTime = moment(start).format('YYYY-MM-DD HH:mm');
        // let endTime = moment(end).format('YYYY-MM-DD HH:mm');
        this.zxtQuery.dateRange=[startTime,end]
        this.getDxList()
        this.getZxt()
      }
    },
    getDxList(){
      getAction(`/iot/trend/nodeDataDis/${this.bjDiaRow.id}`).then((res1)=>{
        let res=res1.data
          if (res.code==200) {
            this.options=res.data
          }else{
            this.$message.error(res.msg)
          }
      })
    },
    handleSelectChange(){
      this.zxtSelect=[]
      this.zxtQuery.disRespVos.forEach(element => {
        console.log(element,"element");
          let [type, name] = element.split(",");
        this.zxtSelect.push({ type, name });
  });
    },
    getZxt(){
      this.showChart2 = false;
      if (isNull(this.zxtQuery.dateRange)||this.zxtQuery.dateRange.length<=0) {
        this.$message.warning('请先选择起止时间')
        return false;
      }
      let sendObj=JSON.parse(JSON.stringify(this.zxtQuery))
      // 日期范围
      sendObj.startTime = "";
      sendObj.endTime = "";
      if (
        !isNull(this.zxtQuery.dateRange) &&
        this.zxtQuery.dateRange.length > 0
      ) {
        sendObj.startTime = this.zxtQuery.dateRange[0];
        sendObj.endTime = this.zxtQuery.dateRange[1];
      }
      sendObj.disRespVos=this.zxtSelect
      sendObj.nodeId=this.bjDiaRow.id
      let loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      postAction('/iot/trend/nodeDataTrend',sendObj).then((res1)=>{
        loading.close()
        let res=res1.data
        if(res.code==200){
           if (!utils.isNull(res.data) && res.data.list.length > 0) {
            this.showChart2 = true;
            // this.list = res.data;
            setTimeout(() => {
              this.setEchartBar2(res.data.list,res.data.times);
              //   this.createRoom(res.data.list)
            }, 100);
          }
        }
      })
    },
    //创建折线图
   setEchartBar2(arr, dataMap) {
      console.log(arr, "arr");
      if (this.showChart2 == false) {
        // if (!utils.isNull(arr)) {
        //   this.echartRoom2.clear();
        // }
        return;
      }
      // << 本月1号到当天
      let xList = [];
      let xList0 = [];
      // let dateObj = new Date();
      // console.log("dateObj.getDate()", dateObj.getDate());
      // console.log(this.getEveryDayDateByBetweenDate(this.listQuery.dateRange[0],this.listQuery.dateRange[1]),'时间间隔');
      // let dayNum = parseInt(dateObj.getDate());
      // let month = utils.return2Num2(dateObj.getMonth() + 1);
      // let year = dateObj.getFullYear();
      // for (let i = 0; i < dayNum; i++) {
      //   let key = `${year}-${month}-${utils.return2Num2(i + 1)}`;
      //   xList.push(key);
      //   xList0.push(`${year}-${month}-${utils.return2Num2(i + 1)}`);
      // }

      // 拼接数据
      let data = [];
      let listData = [];
      for (let index = 0; index < dataMap.length; index++) {
        let obj = {
          yearMonthDate: dataMap[index],
          count: 0,
          type: ""
        };
        listData.push(obj);
      }
      for (let i = 0; i < arr.length; i++) {
        let itemLine = arr[i];
        let lineObj = {
          name: itemLine.name,
          type: "line",
          stack: "",
          data: []
        };
        let map = itemLine.list;
        // console.log('111111111map', map)
        // console.log('111111111listData', listData)
        for (let key = 0; key < map.length; key++) {
          for (let k = 0; k < listData.length; k++) {
            if (map[key].time == listData[k].yearMonthDate) {
              lineObj.data.push(map[key].value)
              // listData[k].value = map[key].value;
              // listData[k].type = map[key].type;
            }
          }
        }
        data.push(lineObj)
        // let arrData = [];
        // console.log("==listData", listData);
        // for (let o = 0; o < listData.length; o++) {
        //   arrData.push(listData[o].value);
        // }
        // console.log(arrData, "arrData");
        // lineObj.data = arrData;
        // data.push(lineObj);
      }
      console.log(data,"data--------------");
      // xList0.push(map[key].yearMonthDate)
      xList0 = dataMap;
      // 绘制图标
      var myChart = echarts.init(document.getElementById("echart-bar2"));
      var option = {
        title: {
          text: ""
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },

        legend: {
          left: 10
        },
        grid: {
          left: "2%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false, // true-刻度中间 false-刻度线上
          data: xList0
        },
        yAxis: {
          type: "value"
          // name: '单位（吨）',
          // nameTextStyle: {
          //   color: '#aaa',
          //   nameLocation: 'start',
          // },
        },
        series: data
        // series: [[1,2,3],[12,22,32],[13,23,33]]
      };
      myChart.clear();
      myChart.setOption(option);
      myChart.on("click", param => {
        // console.log('param', param)
        // // componentIndex
        // // dataIndex
        // let msg = `${this.echartLineData[param.componentIndex].name}：${
        //   this.echartLineData[param.componentIndex].data[param.dataIndex]
        // }`
        // alert(msg)
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.echart-container {
  height: 100%;
}
</style>
