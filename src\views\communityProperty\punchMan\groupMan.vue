<template>
  <!-- 考勤管理 - 考勤组管理 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="选择小区：">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入考勤组名称/姓名' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>新增</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="考勤组名称">
          <template slot-scope="scope">
            <span>{{ scope.row.groupName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="上班时间">
          <template slot-scope="scope">
            <span>{{ scope.row.workTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="下班时间">
          <template slot-scope="scope">
            <span>{{ scope.row.leaveTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="绑定人员" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
            <!-- <el-tag v-for="item in scope.row.adeGroupUsers" :key="item.id">{{ item.userName }}</el-tag> -->
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="260" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="dlgType === 'ADD'?'新增考勤组':'编辑考勤组'" :visible.sync="dlgShow" width='600px' top="30px" append-to-body>

      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="100px">

        <el-form-item label="选择小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考勤组名称" prop="groupName">
          <el-input v-model="dlgData.groupName" placeholder="请输入考勤组名称" />
        </el-form-item>
        <el-form-item label="上班时间" prop="workTime">
          <el-time-picker placeholder="上班时间" v-model="dlgData.workTime" format="HH:mm" value-format="HH:mm">
          </el-time-picker>
        </el-form-item>
        <el-form-item label="下班时间" prop="leaveTime">
          <el-time-picker placeholder="下班时间" v-model="dlgData.leaveTime" format="HH:mm" value-format="HH:mm">
          </el-time-picker>
        </el-form-item>

        <el-form-item label="备注">
          <el-input :autosize="{ minRows: 4, maxRows: 6}" v-model="dlgData.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
        <el-divider content-position="left">绑定人员 <el-button @click="showUserDlgMul" type='primary' icon='el-icon-edit'>更改绑定</el-button>
        </el-divider>
        <el-table class='m-small-table' :data="dlgData.adeGroupUsers" border fit highlight-current-row>
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="姓名">
            <template slot-scope="scope">
              <span>{{ scope.row.label }}</span>
            </template>
          </el-table-column>

          <el-table-column label="部门">
            <template slot-scope="scope">
              <span>{{ scope.row.branchName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delUser(scope.$index)">删除</el-button>
            </template>
          </el-table-column>

        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <userDlgMul />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'
import { findGroupByDynamic, saveAdeGroup, delgroupById } from '@/api/punchMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import userDlgMul from '@/components/Dialog/platformMan/userDlgMul'

let dlgDataEmpty = {
  id: '',
  groupName: '',
  communityId: '',
  leaveTime: '',
  scheduleName: '',
  workTime: '',
  remark: '',
  adeGroupUsers: []
}


export default {
  components: {
    Pagination,
    userDlgMul
  },
  data () {
    return {
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - (1000 * 60 * 60 * 24);
        },
      },

      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',  // ADD\EDIT

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        groupName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        hasNoon: [{ required: true, message: '必填字段', trigger: 'change' }],
        leaveTime: [{ required: true, message: '必填字段', trigger: 'change' }],
        levelTimeLatest: [{ required: true, message: '必填字段', trigger: 'change' }],
        noonWorkHour: [{ required: true, message: '必填字段', trigger: 'change' }],
        nooningEnd: [{ required: true, message: '必填字段', trigger: 'change' }],
        nooningStart: [{ required: true, message: '必填字段', trigger: 'change' }],
        punchMiddleNum: [{ required: true, message: '必填字段', trigger: 'change' }],
        scheduleName: [{ required: true, message: '必填字段', trigger: 'blur' },
        { min: 0, max: 2, message: '最多 2 个字符', trigger: 'blur' }],
        workHourTime: [{ required: true, message: '必填字段', trigger: 'change' }],
        workTime: [{ required: true, message: '必填字段', trigger: 'change' }],
        workTimeFirst: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      communityList: [],
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: ''
      },
    }
  },
  computed: {
    ...mapGetters('platformMan/userDlgMul', {
      userList: 'list'
    }),
  },
  watch: {
    userList (val) {
      let list = JSON.parse(JSON.stringify(val))
      let users = this.dlgData.adeGroupUsers
      let idList = users.map(item => item.id)
      for (let i of list) {
        if (!idList.includes(i.id)) {
          this.dlgData.adeGroupUsers.push({
            id: i.id,
            label: i.label,
            userId: i.id,
            userName: i.label,
            branchId: i.branchId,
            branchName: i.branchName,
            updateDate: '',
            type: 'ADD'
          })
        }
      }
      this.$forceUpdate()
    },
  },

  created () {
    this.getCommunityList()
  },

  methods: {

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 显示人员对话框
    showUserDlgMul () {
      this.$store.commit('platformMan/userDlgMul/SET_LIST', JSON.parse(JSON.stringify(this.dlgData.adeGroupUsers)))
      this.$store.commit('platformMan/userDlgMul/SET_DLGSHOW', true)
    },

    // 删除人员
    delUser (idx) {
      this.dlgData.adeGroupUsers.splice(idx, 1)
    },


    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取数据
    getList () {
      if (utils.isNull(this.listQuery.communityId)) {
        this.$message.warning("请先选择小区")
        return
      }
      this.count++
      this.listLoading = true
      findGroupByDynamic(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          for (let i = 0; i < this.list.length; i++) {
            this.list[i].userName=[]
            for (let j = 0; j < this.list[i].adeGroupUsers.length; j++) {
              this.list[i].userName.push(this.list[i].adeGroupUsers[j].userName)
            }
            this.list[i].userName=this.list[i].userName.join(',')
          }
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    addItem () {
      if (utils.isNull(this.listQuery.communityId)) {
        this.$message.warning("请先选择小区")
        return
      }
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.scheduleName = postParam.groupName
          postParam.creator = Cookie.get('userId')
          this.dlgLoading = true
          saveAdeGroup(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgData.noonWorkHour = this.dlgData.noonWorkHour == 1
      for (let i of this.dlgData.adeGroupUsers) {
        i.type = 'EDIT'
        i.id = i.userId
        i.label = i.userName
      }
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })

    },

    // 删除
    delItem (data) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delgroupById(data.id).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })

    },
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-tag {
  margin-right: 4px;
}
</style>


