/** 船口 流调管理 **/

import Layout from "@/views/layout/Layout";

const flowToneManRouter = {
  path: "/flowToneMan",
  component: Layout,
  name: "flowToneMan",
  meta: {
    title: "流调管理",
    icon: "form",
    roles: ["liudia<PERSON><PERSON><PERSON>"]
  },
  children: [
    {
      path: "streamingQuestionnairesMan",
      name: "流调问卷管理",
      component: () => import("@/views/flowToneMan/streamingQuestionnairesMan/index"),
      meta: {
        title: "流调问卷管理",
        roles: ["liudiaowenjuanguan<PERSON>"],
        // roles: ["pingtaiguanli"]
      },
      children: []
    },
  ]
};

export default flowToneManRouter;
