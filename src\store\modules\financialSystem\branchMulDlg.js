// 多选部门dlg组件

const branchMulDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    branchIds: '',

    branchNames: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    branchIds: state => state.branchIds,

    branchNames: state => state.branchNames
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_BRANCHIDS: (state, val) => {
      state.branchIds = val
    },

    SET_BRANCHNAMES: (state, val) => {
      state.branchNames = val
    }
  },

  actions: {

  }
}

export default branchMulDlg
