/** 基础运营管理系统 **/

import Layout from "@/views/layout/Layout";

const dataCenterRouter = {
  path: "/dataCenter",
  component: Layout,
  name: "dataCenter",
  meta: {
    title: "数据中心",
    icon: "sjzx",
    roles: ["shujuzhongxin"]
  },
  children: [
    {
      path: "bigScreen",
      component: () => import("@/views/equipSafeMan/bigScreen/index"),
      name: "综合运行监控",
      alwaysShow: true,
      meta: {
        title: "综合运行监控",
        roles: ["sjzx_zongheyunxingjiankong"]
      },
      
      children: []
    },
    {
      path: "abnormalAlarmMan",
      component: () => import("@/views/equipSafeMan/abnormalAlarmMan"),
      name: "异常告警管理",
      alwaysShow: true,
      meta: {
        title: "异常告警管理",
        roles: ["sjzx_yichangbaojingguanli"]
      },
      children: []
    },
    {
      path: "operatingParameter",
      component: () => import("@/views/equipSafeMan/operatingParameter"),
      name: "运行参数监控",
      alwaysShow: true,
      meta: {
        title: "运行参数监控",
        roles: ["sjzx_yunxingcanshu"]
      },
      children: []
    },
  ]
};

export default dataCenterRouter;
