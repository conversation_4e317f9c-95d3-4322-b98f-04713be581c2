//  权限dlg组件

const permissionDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    permissionIds: '',

    permissionNames: '',

  },

  getters: {
    dlgShow: state => state.dlgShow,

    permissionIds: state => state.permissionIds,

    permissionNames: state => state.permissionNames,

  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_PERMISSIONIDS: (state, val) => {
      state.permissionIds = val
    },

    SET_PERMISSIONNAMES: (state, val) => {
      state.permissionNames = val
    },
  },

  actions: {

  }
}

export default permissionDlgMul
