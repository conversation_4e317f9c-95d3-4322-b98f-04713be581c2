<template>
    <div class="deviceOperRec_form">
        <el-dialog @close="close" :title="title" :visible.sync="dialogVisible" width="500px"
            :close-on-click-modal="false">
            <el-form ref="elForm" :model="formData" :rules="rules" :disabled="dlgType == 'info'" label-position="left">
                <el-form-item label="项目" prop="projectId">
                    <el-input v-model="formData.projectName" disabled v-if="dlgType != 'add'">
                    </el-input>
                    <el-select v-else v-model="formData.projectId" placeholder="请选择项目" size="medium" clearable filterable :disabled="dlgType != 'add'"
                        @change="projectChange">
                        <el-option v-for="(item, index) in projectList" :key="index" :label="item.name" :value="item.id"
                            ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="选择模板" prop="modelId">
                    <el-input v-model="formData.modelName" disabled size="medium" v-if="dlgType != 'add'">
                    </el-input>
                    <el-select v-else v-model="formData.modelId" placeholder="请选择模板" clearable filterable size="medium"
                        @change="modelChange">
                        <el-option v-for="(item, index) in equModelsOptions" :key="index" :label="item.name"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>

            </el-form>
            <form-render ref="form" :forms="forms" mode="PC" v-model="customFormData" :disabledForm="dlgType === 'info'"></form-render>

            <div slot="footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button v-show="dlgType !== 'info'" type="success" @click="handelConfirm" :loading="btnLoading">确
                    定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { postAction, getAction, deleteAction, putAction } from "@/api";
import FormRender from "@/views/administrationOA/common/form/FormRender";

export default {
    components: {
        FormRender
    },
    props: {
        dlgType: {
            type: String,
            default: "add",
        },
    },
    data() {
        return {
            title: "添加设备运行记录",
            dialogVisible: false,
            btnLoading: false,
            formData: {
                modelId: undefined,
                projectId: undefined,
            },
            rules: {
                modelId: [{
                    required: true,
                    message: '请选择设备',
                    trigger: 'change'
                }],
                projectId: [{
                    required: true,
                    message: '请选择项目',
                    trigger: 'change'
                }],
            },
            forms: [],
            customFormData: {},
            projectList: [],
            equModelsOptions: [],
            equModel: {}
        }
    },
    computed: {

    },
    watch: {

    },
    created() {
        // this.projectList = JSON.parse(window.localStorage.ERPUserInfo).projects;
    },
    mounted() {
    },
    methods: {
        projectChange(val) {
            this.formData.modelId = ""
            this.forms = []
            this.getEquModelsOptions(val)
        },
        getEquModelsOptions(id) {
            getAction(
                `/green/equ/models/page?projectId=${id}&pageNo=1&pageSize=1000`
            ).then((res) => {
                let { code, data } = res.data;
                if (code === "200") {
                    this.equModelsOptions = data.list ? data.list : [];
                } else {
                    this.$message.error(res.data.msg);
                }
            });
        },
        getSelectedClass(cp) {
            // return this.selectFormItem && this.selectFormItem.id === cp.id
            //     ? "border-left: 4px solid #409eff"
            //     : "";
            return ""
        },
        modelChange(val) {
            if(!val){
                this.forms = []
                return
            }
            getAction(`/green/equ/models/get?id=${val}`).then((res) => {
                console.log(res.data);
                let { code, data } = res.data;
                if (code === "200") {
                    console.log(data);
                    this.equModel = JSON.parse(JSON.stringify(data))

                    let formItems = JSON.parse(data.formItems);
                    console.log(formItems, "formItems");

                    // for (let item of formItems) {
                    //     console.log(item.title);
                    //     if (!item.props.showLogic) {
                    //         item.props.showLogic = [];
                    //     }
                    // }
                    this.forms = formItems
                } else {
                    this.$message.error(res.data.msg)
                }
            });
        },

        close() {
            this.customFormData = []
            this.$refs['elForm'].resetFields()
            this.dialogVisible = false;
            this.forms = []
        },
        handelConfirm() {
            console.log(this.formData, "postData");
            console.log(this.forms, "forms");
            console.log(this.customFormData, "customFormData");
            if (this.dlgType == 'info') {
                this.$refs['elForm'].resetFields()
                this.dialogVisible = false;
                return
            }
            this.$refs['elForm'].validate(valid => {
                if (valid) {
                    this.$refs['form'].validate(valid1 => {
                        if (valid1) {
                            this.btnLoading = true;
                            let postData = JSON.parse(JSON.stringify(this.formData))
                            console.log(this.equModel, "this.equModel");


                            //模板信息
                            // postData.name = this.equModel.name
                            // postData.id = this.equModel.id
                            //项目，设备信息
                            let { projectName } = this.projectList.find(item => item.id === postData.projectId)
                            postData.projectName = projectName
                            let { name } = this.equModelsOptions.find(item => item.id === postData.modelId)
                            postData.modelName = name
                            //自定义表单信息
                            this.forms.map(item => {
                                for (const key in this.customFormData) {
                                    if (item.id === key) {
                                        item.value = this.customFormData[key]
                                    }
                                }
                            })
                            postData.dataInfoJson = JSON.stringify(this.forms)
                            console.log(this.forms);
                            console.log(postData, "postData");
                            if (this.dlgType == 'edit') {
                                postData.id = this.formData.id
                                putAction(`/green/equ/operation-record/update`, postData).then(
                                    (res) => {
                                        this.btnLoading = false;
                                        if (res.data.code === "200") {
                                            this.$message({
                                                type: "success", // success, warning, info, error
                                                message: "编辑成功！",
                                            });
                                            this.dialogVisible = false;
                                            this.$refs.elForm.resetFields();
                                            this.$parent.searchFunc()
                                        } else {
                                            this.$message.error(res.data.msg)
                                        }
                                    }
                                );
                            } else {
                                if(postData.id){
                                    delete postData.id
                                }
                                postAction(`/green/equ/operation-record/create`, postData).then(
                                    (res) => {
                                        this.btnLoading = false;
                                        if (res.data.code === "200") {
                                            this.$message({
                                                type: "success", // success, warning, info, error
                                                message: "添加成功！",
                                            });
                                            this.dialogVisible = false;
                                            this.$refs.elForm.resetFields();
                                            this.$parent.searchFunc()
                                        } else {
                                            this.$message.error(res.data.msg)
                                        }
                                    }
                                );
                            }
                        } else {
                            return
                        }
                    })

                } else {
                    return
                }
            })
        },

    }
}

</script>
<style lang="scss" scoped>
.deviceOperRec_form {
    /deep/ .el-form-item__label {
        height: 30px;
        line-height: 30px;
        padding: 0 0;
    }

    /deep/.el-form-item__content {
        margin-left: 0;
    }

}
</style>
