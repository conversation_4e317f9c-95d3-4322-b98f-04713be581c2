<template>
  <div class="app-container mazhenguo" style="margin-bottom: 32px; padding-bottom: 10px">
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">选择维修科目：</div>
            <el-select v-model="searchForm.type" placeholder="请选择维修科目" clearable filterable style="width: 160px" class="fl"
              @change="typeChange()">
              <el-option v-for="(item, index) in maintenanceSubjectOptions" :key="index" :label="item.name"
                :value="item.id"></el-option>
            </el-select>
          </div>
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input v-model="searchForm.workDescribe" placeholder="关键字" clearable class="fl" style="width: 160px"
              @change="searchFunc"></el-input>
            <el-button type="primary" icon="el-icon-search" @click="searchFunc" class="fl ml10">查询</el-button>
            <el-button type="success" @click="showDlg('add')" icon="el-icon-plus" class="fl ml10">添加</el-button>
          </div>
        </div>
      </div>
    </div>

    <el-table :data="tableData" height="calc(100vh - 270px)" ref="tableBar" class="m-small-table"
      v-loading="listLoading" :key="tableKey" border fit highlight-current-row style="width: 100%; height: auto">
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="typeStr" label="维修科目" width="auto" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="workDescribe" label="类别名称" width="auto" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="auto" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="createUserName" label="创建人姓名	" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="操作" width="350" align="center">
        <template slot-scope="scope">
          <el-button @click="showDlg('info', scope.row)" icon="el-icon-document" size="mini" type="primary" title="详情"
            plain>详情</el-button>
          <el-button type="primary" size="mini" @click="showDlg('edit', scope.row)" plain
            icon="el-icon-edit">编辑</el-button>
          <el-button type="danger" size="mini" @click="delFunc(scope.row)" plain icon="el-icon-delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination class="mt10" :total="total" :page.sync="searchForm.pageNo" :limit.sync="searchForm.pageSize"
      @pagination="searchFunc()" />
    <div class="clear"></div>

    <addEdit ref="addEdit" :dlgType="dlgType"></addEdit>
  </div>
</template>

<script>
import { getDataDict } from "@/utils";
import addEdit from "./addEdit";
import { postAction, getAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination"; // 分页
export default {
  components: {
    Pagination,
    addEdit
  },
  data() {
    return {
      searchForm: {
        type: "",
        workDescribe: "",
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      maintenanceSubjectOptions: [],
      total: 0,
      listLoading: false,
      dlgType: "add",
    }
  },
  created() {
  },
  mounted() {
    this.searchFunc()
    getDataDict(this, "maintenanceSubject", "maintenanceSubjectOptions");

  },
  methods: {
    typeChange(val) {
      this.searchFunc()
    },
    searchFunc() {
      let { workDescribe, pageNo, pageSize, type } = this.searchForm;
      this.listLoading = true;
      getAction(
        `/green/equ/repair-subject/page?type=${type}&workDescribe=${workDescribe}&pageNo=${pageNo}&pageSize=${pageSize}`
      ).then((res) => {
        this.listLoading = false;
        let { code, data } = res.data;
        if (code == 200) {
          this.tableData = data.list ? data.list : [];
          this.total = data.total ? data.total : 0;
        } else {
          this.$message.error(res.data.msg)
        }
      });
    },
    showDlg(type, row) {
      let addEdit = this.$refs.addEdit
      this.dlgType = type
      if (type == "add") {
        addEdit.title = "添加"
      } else if (type == "info") {
        addEdit.title = "详情"
      } else {
        addEdit.title = "编辑"
      }
      if (type !== "add") {
        getAction(`/green/equ/repair-subject/get?id=${row.id}`).then((res) => {
          let { code, data } = res.data;
          if (code == 200) {
            addEdit.formData = JSON.parse(JSON.stringify(data))
          } else {
            this.$message.error(res.data.msg)
          }
        });
      }
      this.$refs.addEdit.dialogVisible = true;

    },
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAction(`/green/equ/repair-subject/delete?id=${row.id}`).then(
          (res) => {
            if (res.data.code === "200") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.searchFunc();
            } else {
              this.$message.error(res.data.msg)
            }
          }
        );
      });
    }
  }
}
</script>

<style></style>