<template>
  <!-- 弹窗-选择-->
  <el-dialog
    class="mazhenguo"
    title="选择库存商品"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="1000px"
    top="30px"
  >
    <div class="clearfix">
      <el-input @keyup.enter.native="searchFunc" class="fl" placeholder="关键字" v-model="listQuery.label" style="width: 200px">
        <i @click="resetSearchItem(['label'])" slot="suffix" class="el-input__icon el-icon-error"></i>
      </el-input>

      <el-date-picker
        class="fl ml10"
        style="width: 230px"
        v-model="listQuery.dateRange"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="截止日期"
      >
      </el-date-picker>

      <el-button icon="el-icon-search" type="primary" class="fl ml10" @click="getList" size="mini" style="padding: 7px 10px"
        >搜索</el-button
      >
      <el-popover class="fl" placement="bottom-end" width="800" @show="showPopover" trigger="click">
        <el-table ref="multipleTable" style="height: 400px; overflow: auto" :data="selectList">
          <el-table-column type="index" width="50" align="center"></el-table-column>

          <el-table-column property="name" label="名称"></el-table-column>
          <el-table-column property="code" width="160" label="编码"></el-table-column>
          <!-- <el-table-column property="typeName" label="分类"></el-table-column> -->
          <el-table-column property="" label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button @click="popRemoveRow(scope.row)" type="danger" size="mini" icon="el-icon-delete" plain></el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button class="fr search-right-btn" type="success" slot="reference" icon="el-icon-arrow-down">查看已选</el-button>
      </el-popover>
    </div>

    <el-table
      height="400"
      ref="tableRef"
      class="m-small-table mt10"
      v-loading="listLoading"
      :key="tableKey"
      :data="list"
      border
      fit
      highlight-current-row
      @select="tableSelectChange"
      @select-all="tableSelectAll"
      @row-click="tableRowClick"
    >
      <!-- 多选 -->
      <el-table-column type="selection" width="55" align="center"> </el-table-column>

      <el-table-column label="商品名称" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="商品编码" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.code }}</span>
        </template>
      </el-table-column>

      <el-table-column label="规格型号" width="120" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.model }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批次号" width="120" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.batchNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单价（元）" width="120" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="有效期" width="120" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.termValidity }}</span>
        </template>
      </el-table-column>

      <el-table-column label="照片" width="70" align="center">
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.fileUrl"
            style="width: 40px; height: 40px"
            :preview-src-list="[scope.row.fileUrl]"
            :src="scope.row.fileUrl"
            alt=""
          ></el-image>
        </template>
      </el-table-column>

      <!-- <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button @click="showDlg('info', scope.row)" icon="el-icon-document" size="mini" type="primary" title="详情" plain
            >详情</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>

    <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limt" @pagination="getList" />

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button v-if="dlgType != 'info'" :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">提交中...</span>
        <span v-else>确定</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
// 工具
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
import Pagination from '@/components/Pagination'
// 接口
import * as utils from '@/utils'
import * as regUtils from '@/utils/regUtils'

import { postAction, getAction } from '@/api'

let listQueryEmpty = {
  label: '', //	模糊查询	body	false	string
  page: 1,
  limit: 20,

  dateRange: [],
  // beginDate: '',//		body	false	string
  // endDate: '',//		body	false	string

  projectId: '', //		body	false	string

  warehouseId: '', //

  classCode: '', //		body	false	string
  code: '', //		body	false	string
  type: '', //		body	false	int32

  // status: 0,
  // processStatus: 1,
}

export default {
  components: {
    Pagination,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
    selectList0: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))

          if (this.selectList0) {
            this.selectList = this.selectList0
          } else {
            this.selectList = []
          }
          this.getList()
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),
      selectList: [], // 选中

      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      dlgSubLoading: false,

      // 弹窗
      dlgState: false,
    }
  },
  created() {},
  methods: {
    dlgSubFunc() {
      this.$emit('backFunc', this.selectList)
      this.closeDlg()
    },
    closeDlg() {
      this.$emit('closeDlg')
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.searchFunc()
    },
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.list = []

      let sendObj = { ...this.listQuery, ...this.dlgQuery }
      // 日期范围
      sendObj.beginDate = ''
      sendObj.endDate = ''
      if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
        sendObj.beginDate = sendObj.dateRange[0]
        sendObj.endDate = sendObj.dateRange[1]
      }
      delete sendObj.dateRange
      sendObj.projectId = this.userInfo.projectId
      this.listLoading = true
      postAction('/schain/inventory/page', sendObj).then((res0) => {
        let res = res0.data
        this.listLoading = false
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = []
            this.total = 0
          } else {
            this.list = res.data || []
            this.total = res.page.total

            this.$nextTick(() => {
              this.$refs.tableRef.doLayout()
            })

            // 如果复选设置表格勾选
            let list = this.list
            this.$nextTick(() => {
              console.log('this.selectList', this.selectList)
              if (this.selectList.length > 0) {
                for (let item of list) {
                  let isHas = this.selectList.some((row) => row.id == item.id)
                  if (isHas) {
                    this.$refs.tableRef.toggleRowSelection(item, true)
                  } else {
                    this.$refs.tableRef.toggleRowSelection(item, false)
                  }
                }
              } else {
                this.$refs.tableRef.clearSelection()
              }
            })
          }
        } else {
          this.total = 0
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },

    showPopover() {
      // for (let item of this.selectList) {
      //   this.$refs.multipleTable.toggleRowSelection(item, true)
      // }
    },
    popRemoveRow(row) {
      let selectList = this.selectList.filter((item) => {
        return item.id != row.id
      })
      this.selectList = JSON.parse(JSON.stringify(selectList))

      for (let item of this.list) {
        let isHas = row.id == item.id
        if (isHas) {
          this.$refs.tableRef.toggleRowSelection(item, false)
        }
      }
    },

    tableSelectChange(arr, row) {
      this.tableCheckBaseFunc(row)
    },
    tableRowClick(row, column, event) {},
    tableCheckBaseFunc(row) {
      let isCheck = !this.selectList.some((item) => item.id == row.id)
      if (isCheck) {
        this.selectList.push(row)
      } else {
        let selectList = this.selectList.filter((item) => {
          return item.id != row.id
        })
        this.selectList = JSON.parse(JSON.stringify(selectList))
      }
    },
    tableSelectAll(arr) {
      let len = arr.length

      let list = JSON.parse(JSON.stringify(this.list))
      let selectList = JSON.parse(JSON.stringify(this.selectList))
      if (len == 0) {
        let newList = []
        for (let item of selectList) {
          let hasId = list.some((item2) => item2.id == item.id)
          if (!hasId) {
            newList.push(item)
          }
        }
        selectList = JSON.parse(JSON.stringify(newList))
      } else {
        for (let item of list) {
          let hasId = selectList.some((item2) => item2.id == item.id)
          if (!hasId) {
            selectList.push(item)
          }
        }
      }
      this.selectList = selectList
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>