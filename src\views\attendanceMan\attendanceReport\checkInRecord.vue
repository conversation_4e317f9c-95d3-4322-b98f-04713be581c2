<template>
  <!-- 考勤管理 -->
  <div class="app-container" ref="schedulingMan">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="月份">
          <el-date-picker
            :picker-options="pickerOptions"
            v-model="listQuery.date"
            value-format="yyyy-MM"
            format="yyyy-MM"
            type="month"
            placeholder="月份"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-radio-group @change="getList" v-model="listQuery.branchType">
            <el-radio label="0">当前部门</el-radio>
            <el-radio label="1">当前及所属部门</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="listQuery.branchName"
            @focus="showBranchDlg"
            :title="listQuery.branchName"
            placeholder="选择部门"
            readonly
          >
            <i
              @click="resetSearchItem(['branchId', 'branchName'])"
              slot="suffix"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.label" placeholder="请输入姓名">
            <i
              slot="suffix"
              @click="resetSearchItem(['label'])"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="getList"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-download"
          type="primary"
          size="mini"
          @click="exportExcel"
          >导出</el-button
        >
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="姓名" width="100px">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="部门" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="考勤组" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.adeGroupName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="工时合计" width="80px" align="center">
          <template slot-scope="scope">
            <span class="font-bold fblur">{{ scope.row.actualWorkHour }}</span>
          </template>
        </el-table-column>

        <el-table-column label="卡次" width="80px" align="center">
          <template slot-scope="scope">
            <div>
              {{ "上班卡" }}
            </div>
            <div>
              {{ "中间卡" }}
            </div>
            <div>
              {{ "下班卡" }}
            </div>
            <div class="fblur font-bold">
              {{ "工时" }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="出勤情况" align="center">
          <el-table-column
            align="center"
            v-for="(item, index) in dateList"
            :key="index"
            :label="item.day + '/' + item.week"
            :width="120"
            :render-header="renderheader"
          >
            <template slot-scope="scope">
              <div>
                {{ scope.row.schedulingInfos[index]["goWorkPunchTime"] }}
              </div>
              <el-tooltip
                :content="scope.row.schedulingInfos[index].midPunch"
                placement="top"
              >
                <div class="text-ellipsis">
                  {{ scope.row.schedulingInfos[index].midPunch }}
                </div>
              </el-tooltip>
              <div>
                {{ scope.row.schedulingInfos[index]["offWorkPunchTime"] }}
              </div>
              <div class="fblur font-bold">
                {{ scope.row.schedulingInfos[index]["workHour"] }}
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>

    <DialogBranch
      @superFunc="superBranch"
      :superDlgShow.sync="dlgShowBranch"
      :superSelectId="listQuery.branchId"
      :superSelectName="listQuery.branchName"
      :superPermission="true"
    />
  </div>
</template>

<script>
import Cookie from "js-cookie";
import { mapGetters } from "vuex";
import {
  checkInRecord,
  calculateMonthlyReport,
} from "@/api/attendanceMan/report.js";
import * as utils from "@/utils";
import Pagination from "@/components/Pagination";
import { uploadImg } from "@/utils/uploadImg";
import DialogBranch from "@/components/Dialog/platformMan/DialogBranch";

let maxMonth = utils.getDiffMonth("", -1);
export default {
  components: {
    Pagination,
    DialogBranch,
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return;
          // return time.getTime() > new Date(maxMonth);
        },
      },

      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
        date: "",
        branchId: "",
        branchName: "",
        branchType: "0",
      },
      dateList: [],

      userInfo: {},

      count: 0,

      dlgShowBranch: false,
    };
  },
  computed: {},
  watch: {},

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    this.listQuery.date = maxMonth;
    this.getDateList();
  },

  methods: {
    // 显示部门树
    showBranchDlg() {
      this.dlgShowBranch = true;
    },

    superBranch(params) {
      this.listQuery.branchId = params.selectId;
      this.listQuery.branchName = params.selectName;
    },

    // 表格头换行
    renderheader(h, { column, $index }) {
      return h("span", {}, [
        h("span", {}, column.label.split("/")[0]),
        h("br"),
        h("span", {}, column.label.split("/")[1]),
      ]);
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
    },

    // 获取日期数组
    getDateList() {
      this.dateList = [];

      let maxDate = utils.getMaxDate(this.listQuery.date);
      for (let i = 1; i <= maxDate; i++) {
        let day = i < 10 ? "0" + i : i.toString();
        let date = this.listQuery.date + "-" + day;
        this.dateList.push({
          date,
          day,
          week: utils.getWeek(date),
        });
      }
    },

    // 计算考勤月报
    calcItem() {
      calculateMonthlyReport(this.listQuery.date).then((res) => {
        if (res.data.code == 200) {
          this.getList();
          this.$message.success(res.data.msg);
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    // 导出
    exportExcel() {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery));
      exportParam.userId = this.userInfo.id;
      exportParam.projectId = this.userInfo.projectId;
      let param = Object.keys(exportParam)
        .map(function (key) {
          return (
            encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key])
          );
        })
        .join("&");

      let sendUrl =
        location.protocol +
        "//" +
        location.host +
        `/saapi/workade/dakajilubiao?` +
        param;
      window.open(sendUrl);
    },

    // 根据类型不同重建数组
    formatList(list) {
      for (let i of list) {
        let diff = this.dateList.length - i.schedulingInfos.length;
        if (diff > 0) {
          for (let j = diff; j > 0; j--) {
            i.schedulingInfos.unshift({
              goWorkPunchTime: "无",
              offWorkPunchTime: "无",
              workHour: 0,
              goWorkDate: this.listQuery.date + "-" + (j < 10 ? "0" + j : j),
            });
          }
        }
      }
    },

    // 获取数据
    getList() {
      if (utils.isNull(this.listQuery.date)) {
        this.$message.warning("请选择日期");
        return;
      }
      this.count++;
      this.listLoading = true;
      checkInRecord(this.listQuery).then((res) => {
        this.listLoading = false;
        if (res.data.code == 200) {
          let list = res.data.data
            ? JSON.parse(JSON.stringify(res.data.data))
            : [];
          this.formatList(list);
          this.list = list;
          this.total = res.data.page ? res.data.page.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.actual {
  color: #66b1ff;
}
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px; /* 根据列宽调整 */
}
</style>


