import request from "@/utils/request";
import { requestExcel } from "@/utils";

// 业主分页查询
export function buildingmemberPage(data) {
  return request({
    url: `/unity/buildingmember/page`,
    method: "post",
    data
  });
}

export function buildingmemberPageView(data) {
  return request({
    url: `/unity/buildingmember/page/view`,
    method: "post",
    data
  });
}

// 房间id查询成员
export function buildingmemberRoom(id) {
  return request({
    url: `/unity/buildingmember/room/${id}`,
    method: "get"
  });
}

// 新增/修改房屋成员或业主
export function buildingmemberAddOrUpdate(data) {
  return request({
    url: `/unity/buildingmember/add/owner/member`,
    method: "post",
    data
  });
}

// 删除房业主
export function buildingmemberOwner(id) {
  return request({
    url: `/unity/buildingmember/owner/${id}`,
    method: "get"
  });
}

// 删除房间成员
export function buildingmemberMember(id, roomId) {
  return request({
    url: `/unity/buildingmember/member/${id}/${roomId}`,
    method: "get"
  });
}

// 查询成员信息 根据电话
export function buildingmemberPhone(phone) {
  return request({
    url: `/unity/buildingmember/member/${phone}`,
    method: "get"
  });
}

// 查询业主信息
export function buildingmemberInfo(id) {
  return request({
    url: `/unity/buildingmember/info/${id}`,
    method: "get"
  });
}

// 业主绑定房屋信息
export function buildingmemberBinding(data) {
  return request({
    url: `/unity/buildingmember/binding`,
    method: "post",
    data
  });
}

// 业主解绑房屋信息
export function buildingmemberUnbundling(roomId, memberId) {
  return request({
    url: `/unity/buildingmember/unbundling/${roomId}/${memberId}`,
    method: "get"
  });
}

// 业主查车位 车库
export function findGarageParkingByMemberId(memberId) {
  return request({
    url: `/unity/buildingmember/findGarageParkingByMemberId/${memberId}`,
    method: "get"
  });
}

export function importBuildRoomMember(data) {
  return requestExcel("/unity/report/importBuildRoomMember", data);
}
