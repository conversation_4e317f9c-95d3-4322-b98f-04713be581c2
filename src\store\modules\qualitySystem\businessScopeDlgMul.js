// 选择业务范围dlg组件

const businessScopeDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    list: [],

    workType: "",

    projectId: "",

    projectName: ""
  },

  getters: {
    dlgShow: state => state.dlgShow,

    list: state => state.list,

    workType: state => state.workType,

    projectId: state => state.projectId,

    projectName: state => state.projectName
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_LIST: (state, val) => {
      state.list = val;
    },

    SET_WORKTYPE: (state, val) => {
      state.workType = val;
    },

    SET_PROJECTID: (state, val) => {
      state.projectId = val;
    },

    SET_PROJECTNAME: (state, val) => {
      state.projectName = val;
    }
  },

  actions: {}
};

export default businessScopeDlgMul;
