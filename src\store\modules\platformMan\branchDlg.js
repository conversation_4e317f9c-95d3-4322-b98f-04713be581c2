// 部门dlg组件

const branchDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    permission: true,

    branchId: "",

    branchName: ""
  },

  getters: {
    dlgShow: state => state.dlgShow,

    permission: state => state.permission,

    branchId: state => state.branchId,

    branchName: state => state.branchName
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_PERMISSION: (state, val) => {
      state.permission = val;
    },

    SET_BRANCHID: (state, val) => {
      state.branchId = val;
    },

    SET_BRANCHNAME: (state, val) => {
      state.branchName = val;
    }
  },

  actions: {}
};

export default branchDlg;
