<template>
    <div>
        <el-dialog @open="onOpen" @close="onClose" :title="title" :visible.sync="dialogVisible" width="700px"
            :close-on-click-modal="false">
            <el-form ref="elForm" :model="formData" :rules="rules" label-width="120px" :disabled="dlgType == 'info'">
                <el-form-item label="项目" prop="projectId">
                    <el-input v-model="formData.projectName" disabled v-if="dlgType != 'add'">
                    </el-input>
                    <el-select v-else v-model="formData.projectId" placeholder="请选择项目" clearable filterable
                        :style="{ width: '100%' }" @change="projectChange" ref="projectRef" :disabled="isWeixiudanDisabled || dlgType != 'add'">
                        <el-option v-for="(item, index) in projectList" :key="index" :label="item.name" :value="item.id"
                            :disabled="item.disabled"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="选择设备" prop="equId">
                    <el-input v-model="formData.equName" disabled v-if="dlgType != 'add'">
                    </el-input>
                    <el-select v-model="formData.equId" placeholder="请选择设备" clearable filterable ref="equRef" :disabled="isWeixiudanDisabled" v-else>
                        <el-option v-for="(item, index) in equOptions" :key="index" :label="item.equName"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="科目" class="formStyle">
                    <el-form :model="typeTableData" ref="typeTableRef" label-width="auto" :disabled="dlgType == 'info'"
                        class="formStyle">
                        <el-table :data="typeTableData" class="m-small-table">
                            <el-table-column label="序号" width="50">
                                <template slot-scope="scope">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="value" label="科目" style="padding: 0;">
                                <template slot-scope="scope">
                                    <el-form-item :prop="scope.$index + '.value'" :rules="{
                                        required: true,
                                        message: '科目不能为空',
                                        trigger: 'blur',
                                    }">
                                        <el-select v-model="scope.row.value" placeholder="请选择科目" clearable filterable
                                            @change="typeChange(scope.$index, scope.row.value)">
                                            <el-option v-for="(item, index) in maintenanceSubjectOptions" :key="index"
                                                :label="item.name" :value="item.id"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="contentId" label="类别">
                                <template slot-scope="scope">
                                    <el-form-item :prop="scope.$index + '.contentId'" :rules="{
                                        required: true,
                                        message: '类别不能为空',
                                        trigger: 'blur',
                                    }">
                                        <el-select v-model="scope.row.contentId" placeholder="请选择类别" clearable
                                             filterable>
                                            <el-option
                                                v-for="(item, index) in (scope.row.options ? scope.row.options : [])"
                                                :key="index" :label="item.workDescribe" :value="item.id"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column v-if="dlgType !== 'info'">
                                <template slot-scope="scope">
                                    <el-button @click="delTypeTable(scope.$index)" icon="el-icon-delete" size="mini"
                                        type="danger" title="删除" plain>删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form>
                </el-form-item>
                <el-form-item>
                    <el-button v-show="dlgType !== 'info'" @click="addTypeList" icon="el-icon-plus" type="primary" plain
                        style="margin-top: 10px">添加科目</el-button>
                </el-form-item>
                <el-form-item label="内容" prop="content">
                    <el-input v-model="formData.content" placeholder="请输入内容" clearable>
                    </el-input>
                </el-form-item>

                <el-form-item label="更换配件" class="formStyle">
                    <el-form :model="accessoryTableData" ref="accessoryTableRef" label-width="auto"
                        :disabled="dlgType == 'info'" class="formStyle">
                        <el-table :data="accessoryTableData" class="m-small-table">
                            <el-table-column label="序号" width="50">
                                <template slot-scope="scope">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="optionId" label="选项" width="140">
                                <template slot-scope="scope">
                                    <el-form-item :prop="scope.$index + '.optionId'" :rules="{
                                        required: true,
                                        message: '选项不能为空',
                                        trigger: 'change',
                                    }">
                                        <el-select v-model="accessoryTableData[scope.$index].optionId"
                                            placeholder="请选择选项" clearable filterable :style="{ width: '100%' }">
                                            <el-option v-for="(item, index) in maintainPlanOptions" :key="index"
                                                :label="item.name" :value="item.id"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="unit" label="单位">
                                <template slot-scope="scope">
                                    <el-form-item :prop="scope.$index + '.unit'" >
                                        <el-input v-model="accessoryTableData[scope.$index].unit"
                                            placeholder="请输入单位"></el-input>

                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="num" label="数量">
                                <template slot-scope="scope">
                                    <el-form-item :prop="scope.$index + '.num'" >
                                        <el-input-number size="mini" :min="1"
                                            v-model="accessoryTableData[scope.$index].num"
                                            style="width: 100px;"></el-input-number>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column v-if="dlgType !== 'info'">
                                <template slot-scope="scope">
                                    <el-button @click="delAccessoryTable(scope.$index)" icon="el-icon-delete"
                                        size="mini" type="danger" title="删除" plain>删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form>
                </el-form-item>
                <el-form-item>
                    <el-button v-show="dlgType !== 'info'" @click="addAccessoryList" icon="el-icon-plus" type="primary"
                        plain style="margin-top: 10px">添加配件</el-button>
                </el-form-item>
                <el-form-item label="附件" prop="fileUrl">
                    <qiniuUpload :fileList0="formData.fileUrl" ref="qiniuUploadRef" :limit="5" :maxSize="5"
                        @successBack="successBack" :dlgType="dlgType" />
                </el-form-item>
                <el-form-item label="维修状态" prop="status">
                    <el-select v-model="formData.status" placeholder="请选择维修状态" clearable filterable ref="statusRef">
                        <el-option v-for="(item, index) in maintenanceStatusOptions" :key="index" :label="item.name"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label=" 维修人员" prop="repairPersonName">
                    <el-input v-model="formData.repairPersonName" :title="formData.repairPersonName"
                        @focus="showUserTree" placeholder="请选择员工" />
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button v-show="dlgType !== 'info'" type="success" @click="handelConfirm" :loading="btnLoading">确
                    定</el-button>
            </div>
        </el-dialog>
        <Usertree/>

    </div>
</template>
<script>
import { postAction, getAction, deleteAction, putAction } from "@/api";
import qiniuUpload from "@/views/greenMan/components/qiniuUpload";
import { getDataDict } from "@/utils";
import * as utils from "@/utils";
import Usertree from "@/components/Dialog/Usertree"; // 员工弹窗
import { mapGetters } from "vuex";

export default {
    inheritAttrs: false,
    components: {
        qiniuUpload,
        Usertree
    },
    props: {
        dlgType: {
            type: String,
            default: "add",
        },
    },
    data() {
        return {
            isWeixiudanDisabled: false,//是否是维修单，是的话项目和设备数据带过来，不能更改
            title: "添加",
            dialogVisible: false,
            typeTableData: [{}],
            accessoryTableData: [{ options: [] }],
            btnLoading: false,
            maintenanceSubjectOptions: [],
            maintainPlanOptions: [],
            maintenanceStatusOptions: [],
            formData: {
                projectId: undefined,
                equId: undefined,
                content: undefined,
                fileUrl: undefined,
                status: undefined,
                repairPersonName: undefined,
                repairPersonId: undefined,
            },
            rules: {
                projectId: [{
                    required: true,
                    message: '请选择项目',
                    trigger: 'change'
                }],
                equId: [{
                    required: true,
                    message: '请选择设备',
                    trigger: 'change'
                }],
                content: [{
                    required: true,
                    message: '请输入内容',
                    trigger: 'blur'
                }],
                status: [{
                    required: true,
                    message: '请选择维修状态',
                    trigger: 'change'
                }],
                repairPersonName: [{
                    required: true,
                    message: '请选择维修人员',
                    trigger: 'change'
                }],
            },
            statusOptions: [],
            projectList: [],
            equOptions: [],

        }
    },
    computed: {
        // ...mapGetters(["userTreeGet"])

    },
    watch: {
        // userTreeGet(val) {
        //     console.log("====this.dlgState1111", this.dlgState);

        //     if (this.dialogVisible) {
        //         console.log("watch 返回员工返回：", val);
        //         if (val == "empty") {
        //             return false;
        //         }
        //         let list = JSON.parse(val);
        //         let idArr = [];
        //         let nameArr = [];
        //         for (let item of list) {
        //             idArr.push(item.id);
        //             nameArr.push(item.label);
        //         }
        //         this.formData.repairPersonId = idArr.join(",");
        //         this.formData.repairPersonName = nameArr.join(",");
        //     }
        // }
    },
    created() {
    },
    mounted() {
        utils.getDataDictERP(this, "maintenanceSubject", 'maintenanceSubjectOptions');
        utils.getDataDictERP(this, "maintainPlanOptions", 'maintainPlanOptions');
        utils.getDataDictERP(this, "maintenanceStatus", 'maintenanceStatusOptions');
    },
    methods: {
        typeChange(index, val) {
            console.log(index,val);
            this.typeTableData[index].contentId = ""
            if(val){
                this.getRepairSubjectOptions(index, val)
            }else{
                this.typeTableData[index].options = [];
            }
        },
        getRepairSubjectOptions(index, val) {
            getAction(
                `/green/equ/repair-subject/page?type=${val}&pageNo=1&pageSize=1000`
            ).then((res) => {
                let { code, data } = res.data;
                if (code === "200") {
                    this.typeTableData[index].options = data.list;
                    this.typeTableData = JSON.parse(JSON.stringify(this.typeTableData))
                    console.log(this.typeTableData, "this.typeTableData");
                } else {
                    this.$message.error(res.data.msg)
                }
            });
        },
        projectChange(val) {
            this.formData.equId = "";
            this.formData.equName = "";
            this.getEquOptions(val)
        },
        getEquOptions(id) {
            getAction(
                `/green/equipment-manage/page?responsiblePersonId=${window.localStorage.userId}&projectId=${id}&pageNo=1&pageSize=1000&equName=`
            ).then((res) => {
                let { code, data } = res.data;
                if (code === "200") {
                    this.equOptions = data.list ? data.list : [];
                } else {
                    this.$message.error(res.data.msg);
                }
            });
        },
        getRunTemplateIdOptions(id) {
            getAction(
                `/green/equ/models/page?projectId=${id}&pageNo=1&pageSize=1000`
            ).then((res) => {
                let { code, data } = res.data;
                if (code === "200") {
                    this.runTemplateIdOptions = data.list ? data.list : [];
                } else {
                    this.$message.error(res.data.msg)
                }
            });
        },
        onOpen() { },
        onClose() {
            this.typeTableData = [{}]
            this.accessoryTableData = [{ options: [] }]
            this.$refs['elForm'].resetFields()
            this.$refs['typeTableRef'].resetFields()
            this.$refs['accessoryTableRef'].resetFields()
            this.formData.fileUrl = undefined
            this.formData.equName = undefined
            this.formData.projectName = undefined
            this.formData.repairPersonId = undefined
            this.dialogVisible = false;
            this.isWeixiudanDisabled = false;
            console.log(this.formData, "formData");
        },

        // 员工弹窗
        showUserTree() {
            this.$store.commit("SET_USERTREE_ISS", true); // 是否多选
            let idArr = [];
            let nameArr = [];
            let listNew = [];

            console.log("666", this.formData.repairPersonId);
            if (this.formData.repairPersonId) {
                idArr = this.formData.repairPersonId.split(",");
                nameArr = this.formData.repairPersonName.split(",");
                for (let i = 0; i < idArr.length; i++) {
                    let obj = {
                    id: idArr[i],
                    label: nameArr[i]
                    };
                    listNew.push(obj);
                }
            }
            

            this.$store.commit(
                "SET_USERTREE_GET",
                listNew.length ? JSON.stringify(listNew) : "empty"
            );
            this.$store.commit("SET_USERTREE_DIATYPE", ""); // 是否多选 form_qjd_jjxjx
            this.$store.commit("SET_USERTREESQTYPE", "");
            this.$store.commit("SET_USERTREESTATE", true);
        },
        handelConfirm() {
            if (this.dlgType == 'info') {
                this.$refs['elForm'].resetFields()
                this.typeTableData = []
                this.accessoryTableData = []
                this.dialogVisible = false;
                return
            }
            this.$refs['elForm'].validate(valid => {
                if (valid) {
                    this.$refs['typeTableRef'].validate(valid1 => {
                        if (valid1) {
                            this.$refs['accessoryTableRef'].validate(valid2 => {
                                if (valid2) {

                                    console.log(this.formData, "this.formData");
                                    console.log(this.typeTableData, "this.typeTableData");
                                    console.log(this.accessoryTableData, "this.accessoryTableData");


                                    let postData = JSON.parse(JSON.stringify(this.formData))
                                    let typeTableData = JSON.parse(JSON.stringify(this.typeTableData))
                                    let accessoryTableData = JSON.parse(JSON.stringify(this.accessoryTableData))
                                    typeTableData.map(item => {
                                        let { workDescribe } = item.options.find(item1 => item1.id === item.contentId)
                                        item.content = workDescribe
                                        let { name } = this.maintenanceSubjectOptions.find(item1 => item1.id === item.value)
                                        item.name = name
                                        delete item.options
                                    })
                                    accessoryTableData.map(item => {
                                        let { name } = this.maintainPlanOptions.find(item1 => item1.id === item.optionId)
                                        item.option = name
                                    })
                                    postData.typeJson = JSON.stringify(typeTableData)
                                    postData.accessoryJson = JSON.stringify(accessoryTableData)
                                    postData.projectName = postData.projectName ? postData.projectName :this.$refs.projectRef.selected.label;
                                    postData.equName = postData.equName ? postData.equName : this.$refs.equRef.selected.label;

                                    if(this.dlgType == 'add'){
                                        let { equModel } = this.equOptions.find(item => item.id === postData.equId)
                                        postData.equModel = equModel 
                                    }
                                    

                                    postData.statusStr = this.$refs.statusRef.selected.label;
                                    postData.fileUrl = JSON.stringify(postData.fileUrl);


                                    console.log(postData, "postData");
                                    this.btnLoading = true;

                                    if (this.dlgType == 'edit') {
                                        putAction(`/green/equ/repair-manage/update`, postData).then(
                                            (res) => {
                                                this.btnLoading = false;
                                                if (res.data.code === "200") {
                                                    this.$message({
                                                        type: "success", // success, warning, info, error
                                                        message: "编辑成功！",
                                                    });
                                                    this.dialogVisible = false;
                                                    this.$refs.elForm.resetFields();
                                                    this.$parent.searchFunc()
                                                } else {
                                                    this.$message.error(res.data.msg)
                                                }
                                            }
                                        );
                                    } else {
                                        if (postData.id) {
                                            delete postData.id
                                        }
                                        postAction(`/green/equ/repair-manage/create`, postData).then(
                                            (res) => {
                                                this.btnLoading = false;
                                                if (res.data.code === "200") {
                                                    this.$message({
                                                        type: "success", // success, warning, info, error
                                                        message: "添加成功！",
                                                    });
                                                    this.dialogVisible = false;
                                                    this.$refs.elForm.resetFields();
                                                    this.$parent.searchFunc()
                                                } else {
                                                    this.$message.error(res.data.msg)
                                                }
                                            }
                                        );
                                    }
                                } else {
                                    return
                                }

                            })
                        }


                    })

                } else {
                    return
                }
            })
        },

        uploadQj1(file) {
            if (file.size > 5 * 1024 * 1024) {
                this.$message({
                    type: "warning",
                    message: "上传图片大小不能超过5M",
                });
                return false;
            }
            uploadImg(file, "ERP_web/greenMan/bch/bch_").then((res) => {
                this.formData.picUrl.push(res);
            });
        },
        delUploadImgByArr(index) {
            this.$confirm("是否删除？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning", // // success, warning, info, error
            }).then(() => {
                this.$message({
                    type: "success", // success, warning, info, error
                    message: "删除成功",
                });
                let formData = this.formData;
                formData.picUrl.splice(index, 1);
                this.formData = JSON.parse(JSON.stringify(formData));
            });
        },
        uploadQj2(file) {
            if (file.size > 5 * 1024 * 1024) {
                this.$message({
                    type: "warning",
                    message: "上传图片大小不能超过5M",
                });
                return false;
            }
            uploadImg(file, "ERP_web/greenMan/bch/bch_").then((res) => {
                this.formData.drawingUrl.push(res);
            });
        },
        delUploadImgByArr1(index) {
            this.$confirm("是否删除？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning", // // success, warning, info, error
            }).then(() => {
                this.$message({
                    type: "success", // success, warning, info, error
                    message: "删除成功",
                });
                let formData = this.formData;
                formData.drawingUrl.splice(index, 1);
                this.formData = JSON.parse(JSON.stringify(formData));
            });
        },
        successBack(fileList) {
            this.formData.fileUrl = fileList;
        },
        addTypeList() {
            this.typeTableData.push({ options: [] });
        },
        addAccessoryList() {
            this.accessoryTableData.push({});
        },
        delTypeTable(row) {
            this.typeTableData.splice(row, 1);
        },
        delAccessoryTable(row) {
            this.accessoryTableData.splice(row, 1);
        },
    }
}

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-dialog {

    /* 表格input不对齐 */
    .formStyle .el-form-item {
        margin: 0;
    }

    /* “设备参数”和表格对齐 */
    .formStyle .el-form-item__label {
        margin-top: 7px;
    }

}
</style>
