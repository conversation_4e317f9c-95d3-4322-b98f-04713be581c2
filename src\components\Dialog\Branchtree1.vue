<template>
  <div class="">
    <!-- 多选部门 -->
    <el-dialog :close-on-click-modal='false' 
      title="选择部门" 
      
      :visible.sync="branchTreeState" 
      width='600px'
      top='30px'
      append-to-body>
      <div class="">
        <!-- <el-input
          placeholder="输入关键字进行过滤"
          style="margin-bottom: 10px;"
          v-model="filterBmLeftText">
        </el-input> -->
        <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
        <div class='m-dialog-h'>
          <el-tree
            :data="treeData"
            :check-strictly='false'
            @check='treeCheck'
            show-checkbox
            default-expand-all
            ref="tree"
            node-key='id'
            highlight-current
            :default-checked-keys='defaultSelectKey'
            
            :props="defaultProps">
          </el-tree>
          <!-- <el-tree 
            :data="treeData" 
            ref="treeDom"
            default-expand-all
            :filter-node-method="filterNode"
            @node-click="nodeClick">
          </el-tree> -->
        </div>
        
      </div>
      <div slot="footer" class="dialog-footer">
        <!-- <span class='dialog-footer-span' v-show='branchName'>当前选中：{{ branchName }}</span> -->
        <el-button @click="closeDialog">返回</el-button>
        
        <el-button type="danger" @click="removeNode">清空</el-button>
        <el-button :loading="btnLoading" type="success" @click="bumenOkFunc">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findOrgBranchAll } from '@/api/dataDic'
import { setTimeout } from 'timers';
// import adminDashboard from './admin'
// import editorDashboard from './editor'

export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      
      // 部门树
      treeData: [],
      selectKeys: [],  // 选中的节点id集合
      checkChildIds: [],  // 选中当前节点下的 id 集合
      childNode: {},  // 选中的当前节点

      btnLoading: false,
      isFirst: false,

      // 树过滤
      defaultProps: {
        children: 'children',
        label: 'label',
      },

      // 返回的 id + name
      backIdName: [],

      // 默认数据
      defaultSelectKey: [],

    }
  },
  computed: {
    ...mapGetters([
      'branchTreeKeysSet',
      'branchTreeIsSelectChild'
    ]),
    branchTreeState: {
      get: function() {
        let state = this.$store.getters.branchTreeState
        if (state) {
          setTimeout(() => {
            this.$refs.tree.setCheckedKeys([])

            // 是否根据权限
            let branchTreeIsRoot = this.$store.getters.branchTreeIsRoot
            // if (branchTreeIsRoot) {
              findOrgBranchAll().then(res => {
                let code = res.data.code
                let data = res.data.data
                let msg = res.data.msg

                if (code === '200') {
                  this.treeData = JSON.parse(JSON.stringify(res.data.list))
                  setTimeout(() => {
                    this.defaultSelectKey = JSON.parse(this.$store.getters.branchTreeKeysSet)
                  }, 100)
                } else {
                  this.$message.error(msg)
                }
              })
            // } 
            // else {
            //  ).then(res => {
            //     let code = res.data.code
            //     let data = res.data.data
            //     let msg = res.data.msg
            //     if (code === '200') {
            //       this.treeData = JSON.parse(JSON.stringify(res.data.list))
            //       setTimeout(() => {
            //         this.defaultSelectKey = JSON.parse(this.$store.getters.branchTreeKeysSet)
            //       }, 100)
            //     } else {
            //       this.$message.error(msg)
            //     }
            //   })
            // }
          }, 100)
        }
        
        return state
      },
      set: function(newVal) {
        this.$store.commit('SET_BRANCHTREESTATE', newVal)
      }
    },

    branchTreekeys: {
      get: function() {
        let branchTreekeys = JSON.parse(this.$store.getters.branchTreekeys)
        this.$refs.tree.setCheckedKeys(branchTreekeys)
        return branchTreekeys
      },
      set: function(newVal) {
        
      }
    },
    branchTreeIsRoot: {
      get: function() {
        return branchTreeIsRoot
      },
      set: function(newVal) {
        
      }
    }
  },
  watch: {

  },
  created() {
    // 部门
    // this.findOrgBranchAll()
  },
  methods: {
    // 【【 节点相关
    // 清空选中节点
    removeNode() {
      this.$refs.tree.setCheckedKeys([]);
    },
    // 节点选中触发事件
    treeCheck(checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys ) {

    },

    // 【【 弹窗按钮
    // 提交
    bumenOkFunc() {
      // this.btnLoading = true
      this.$store.commit('SET_BRANCHTREEKEYS', '')
      this.$store.commit('SET_BRANCHTREEHALFKEYS', '')
      setTimeout(() => {
        this.$store.commit('SET_BRANCHTREEKEYS', JSON.stringify(this.$refs.tree.getCheckedNodes()))
        this.$store.commit('SET_BRANCHTREEHALFKEYS', JSON.stringify(this.$refs.tree.getHalfCheckedKeys()))
        
        this.closeDialog()
      }, 100)
    },

    // 关闭弹窗 
    closeDialog() {
      this.isFirst = true
      this.$store.commit('SET_BRANCHTREESTATE', false)
      
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.dialog-footer-span {
    font-size: 14px;
    color: #666;
    display: inline-block;
    padding-right: 10px;
  }
</style>