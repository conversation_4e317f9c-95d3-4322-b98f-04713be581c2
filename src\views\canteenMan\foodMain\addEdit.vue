<template>
  <el-dialog
    width="500px"
    :title="getDialogTitle(dlgType)"
    :visible.sync="dialogVisible"
    append-to-body
    @close="onClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      label-width="80px"
      :disabled="dlgType == 'info'"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入名称"
          clearable
          :style="{ width: '100%' }"
          maxlength="200"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="规格" prop="model">
        <el-input
          v-model="formData.model"
          placeholder="请输入规格"
          clearable
          :style="{ width: '100%' }"
          maxlength="200"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="单位" prop="unit">
        <el-input
          v-model="formData.unit"
          placeholder="请输入单位"
          clearable
          :style="{ width: '100%' }"
          maxlength="200"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="图片" style="width: 100%" prop="imgUrl">
        <div v-if="formData.imgUrl.length == 0 && dlgType == 'info'">无</div>
        <div v-else>
          <div
            class="upload-bar"
            v-for="(item, index) of formData.imgUrl"
            :key="index"
          >
            <el-image
              class="avatar"
              :preview-src-list="[item]"
              :z-index="9999"
              :src="item"
              alt=""
            ></el-image>
            <i
              @click="delUploadImgByArr(index)"
              class="el-icon-error avatar_icon"
              v-if="dlgType != 'info'"
            ></i>
          </div>
          <el-upload
            class="avatar-uploader"
            v-if="dlgType != 'info' && formData.imgUrl.length < 1"
            action=""
            :show-file-list="false"
            :before-upload="uploadQj1"
          >
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </div>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入描述"
          v-model="formData.description"
          maxlength="500"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="close" size="small">取 消</el-button>
      <el-button
        size="small"
        @click="handleConfirm"
        v-show="dlgType !== 'info'"
        type="success"
        icon="el-icon-check"
        :loading="btnLoading"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { postAction, getAction, putAction } from "@/api";
import { uploadImg } from "@/utils/uploadImg";

export default {
  components: {},
  props: {},
  data() {
    return {
      dlgType: "add",
      dialogVisible: false,
      btnLoading: false,
      formData: {
        name: undefined,
        model: undefined,
        unit: undefined,
        description: undefined,
        projectId: undefined,
        imgUrl: [],
      },
      rules: {
        name: [
          {
            required: true,
            message: "必填字段",
            trigger: "blur",
          },
        ],
        model: [
          {
            required: true,
            message: "必填字段",
            trigger: "blur",
          },
        ],
        unit: [
          {
            required: true,
            message: "必填字段",
            trigger: "blur",
          },
        ],
        description: [
          {
            required: true,
            message: "必填字段",
            trigger: "blur",
          },
        ],
        imgUrl: [
          {
            type: "array",
            required: true,
            message: "请上传图片",
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {},
  methods: {
    getDialogTitle(type) {
      const titleMap = {
        add: "添加",
        edit: "编辑",
        info: "详情",
      };
      return titleMap[type] || "详情";
    },
    uploadQj1(file) {
      if (file.size > 5 * 1024 * 1024) {
        this.$message({
          type: "warning",
          message: "上传图片大小不能超过5M",
        });
        return false;
      }
      uploadImg(file, "ERP_web/greenMan/bch/bch_").then((res) => {
        this.formData.imgUrl.push(res);
      });
    },
    delUploadImgByArr(index) {
      this.$confirm("是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning", // // success, warning, info, error
      }).then(() => {
        this.$message({
          type: "success", // success, warning, info, error
          message: "删除成功",
        });
        let formData = this.formData;
        formData.imgUrl.splice(index, 1);
      });
    },
    init(type, row) {
      this.dlgType = type;
      this.dialogVisible = true;
      if (type == "add") {
      } else {
        this.getInfo(row);
      }
    },
    getInfo(row) {
      getAction(`/canteen/cn/dishes/get?id=${row.id}`).then((res) => {
        console.log(res.data);
        let { code, data } = res.data;
        if (code === "200") {
          data.imgUrl = JSON.parse(data.imgUrl);
          this.formData = data ? JSON.parse(JSON.stringify(data)) : [];
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    onClose() {
      this.dialogVisible = false;
      this.$refs["elForm"].resetFields();
    },
    close() {
      this.dialogVisible = false;
      this.$refs["elForm"].resetFields();
    },
    handleConfirm() {
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          const postData = {
            ...this.formData,
            imgUrl: JSON.stringify(this.formData.imgUrl),
          };

          const actionUrl =
            this.dlgType === "edit"
              ? `/canteen/cn/dishes/update`
              : `/canteen/cn/dishes/create`;

          const actionMethod = this.dlgType === "edit" ? putAction : postAction;

          if (this.dlgType !== "edit" && postData.id) {
            delete postData.id;
          }

          actionMethod(actionUrl, postData).then((res) => {
            this.btnLoading = false;
            if (res.data.code === "200") {
              this.$message({
                type: "success",
                message: this.dlgType === "edit" ? "编辑成功！" : "添加成功！",
              });
              this.dialogVisible = false;
              this.resetFormAndSearch();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },

    resetFormAndSearch() {
      this.$refs["elForm"].resetFields();
      this.$parent.searchFunc();
    },
  },
};
</script>

<style></style>
