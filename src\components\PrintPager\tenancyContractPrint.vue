<template>
  <!-- 距离左侧差6.2cm -->
  <!-- 1cm = 38.09523px -->
  <!-- 6.2*38.09523 = 236.190476 -->
  <div class="app-container print">
    <div ref="paperContainer" :style="`width:${paperWidth}px;height:${paperHeight}px;font-size: 16px`" class="paper-container clearfix">
      <!-- 0-首页 -->
      <div v-if="pageTypeId == '0'" class="pre">
        <!-- left 23.5 -->
        <div class="pab" :style="`top: 0px; left: ${rightCha + 895.237905}px`">{{ dlgData.contractNo }}</div>
      </div>
      <!-- 第1页（出租人，承租人）-->
      <!-- 15 21.5 高 4.6，571.42845 819.047445 高 175.238058 -->
      <div v-if="pageTypeId == 1" class="pre">
        <div class="pab" :style="`top: 175.238058px; left:  ${rightCha + 571.42845}px`">{{ dlgData.projectName }}</div>
        <div class="pab" :style="`top: 175.238058px; left:  ${rightCha + 819.047445}px`">{{ dlgData.userName }}</div>
      </div>

      <!-- 第4,5页（合同有效期，签章日期） -->
      <div v-if="pageTypeId == '4,5'" class="pre">
        <!-- 4.9  8.9  高5.1 -->
        <!-- 186.666627  339.047547 高 190.47615 -->
        <!-- 20.9 高6.3 -->
        <!-- 796.190307 高239.999949 -->
        <div class="pab" :style="`top: 190.47615px; left:  ${rightCha + 186.666627}px`">{{ dlgData.startDate2 }}</div>
        <div class="pab" :style="`top: 190.47615px; left:  ${rightCha + 339.047547}px`">{{ dlgData.endDate2 }}</div>
        <div class="pab" :style="`top: 239.999949px; left:  ${rightCha + 796.190307}px`">{{ dlgData.signDate2 }}</div>
      </div>

      <!-- 第6页（承租人信息，房屋信息） -->
      <div v-if="pageTypeId == '6'" class="pre">
        <!-- 2.7 高1.6,102.857121  60.952368 -->
        <!-- 2.7 高2.9, 102.857121  110.476167 -->
        <!-- 2.7 高3.5, 102.857121 133.333305-->
        <!-- 8 高3.75,304.76184  133.333305 -->
        <!-- 10.7 高3.75, 407.618961 133.333305 -->
        <div class="pab" :style="`top: 60.952368px; left:  ${rightCha + 102.857121}px`">{{ dlgData.userName || '' }}</div>
        <div class="pab" :style="`top: 110.476167px; left:  ${rightCha + 102.857121}px`">{{ dlgData.unitAddr || '' }}</div>
        <div class="pab" :style="`top: 133.333305px; left:  ${rightCha + 102.857121}px`">
          {{ dlgData.communityName || '' }}{{ dlgData.floorName || '' }}
        </div>
        <div class="pab" :style="`top: 133.333305px; left:  ${rightCha + 304.76184}px`">{{ dlgData.unitName || '' }}</div>
        <div class="pab" :style="`top: 133.333305px; left:  ${rightCha + 407.618961}px`">
          {{ dlgData.layer || '' }}{{ dlgData.roomNum || '' }}
        </div>
      </div>
      <!-- 第8,9页（租金单价，用途，面积，应收租金） -->
      <div v-if="pageTypeId == '8,9'" class="pre">
        <!-- 7高3.2,266.66661 121.904736-->
        <!-- 8.5高3.2,323.809455 121.904736-->
        <!-- 10.3高3.2,392.380869 121.904736-->
        <!-- 18.8高3.2,716.190324 121.904736-->
        <div class="pab" :style="`top: 121.904736px; left:  ${rightCha + 266.66661}px`">{{ dlgData.price || '' }}</div>
        <div class="pab" :style="`top: 121.904736px; left:  ${rightCha + 323.809455}px`">{{ dlgData.typeName || '' }}</div>
        <div class="pab" :style="`top: 121.904736px; left:  ${rightCha + 392.380869}px`">{{ dlgData.insideArea || '' }}</div>

        <div class="pab" :style="`top: 121.904736px; left:  ${rightCha + 716.190324}px`">{{ dlgData.yszjMoney || '' }}</div>
      </div>
    </div>
    <el-button class="no-print" icon="el-icon-printer" type="primary" size="medium" @click="printFunc()"> 打印</el-button>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import * as utils from '@/utils'
import { return2Num } from '@/utils/calendarData'

// import {
//   findInfoById,
// } from '@/api/jyt/drugMail'

export default {
  components: {},
  data() {
    return {
      rightCha: -40, // px 测试时偏移量,测试时为0，正常时236.190476
      ////

      paperHeight: 0,
      paperWidth: 0,

      pageTypeId: '', // 第几页

      id: '',

      printDate: '',

      dlgData0: '',
      dlgData: '',
    }
  },
  computed: {},
  created() {
    console.log('this.$route', this.$route)
    this.pageTypeId = this.$route.query.id

    this.dlgData = JSON.parse(window.sessionStorage.printData)

    this.setPrintDate()
  },
  mounted() {
    this.$nextTick(() => {
      // console.log('utils.getDpiWidth(227)', utils.getDpiWidth(227))
      // this.paperWidth = utils.getDpiWidth(227)
      // this.paperHeight = utils.getDpiHeight(122)

      // this.paperWidth = utils.getDpiWidth(188)  // 701px 20cm   *9.4
      // this.paperHeight = utils.getDpiHeight(160)  // 604

      // this.paperWidth = utils.getDpiWidth(159) // 17cm
      this.paperWidth = utils.getDpiWidth(280) // 17*2cm
      this.paperHeight = utils.getDpiHeight(84) // 9cm

      // this.paperWidth = utils.getDpiWidth(210)
      // this.paperHeight = utils.getDpiHeight(140)

      setTimeout(() => {
        // this.printFunc()
        console.log('打印吧！！！')
      }, 1000)
    })
  },
  methods: {
    setPrintDate() {
      let today = new Date()
      let year = today.getFullYear()
      let month = return2Num(today.getMonth() + 1)
      let day = return2Num(today.getDate())
      this.printDate = `${year} 年 ${month} 月 ${day} 日`
    },

    // 打印
    printFunc() {
      this.$nextTick(() => {
        window.print()
      })
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-container.print {
  // font-weight: bold;
}
.app-container {
  box-sizing: border-box;
  font-size: 15px;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 0;

  .paper-container {
    // background: url('/static/image/paper.jpg') no-repeat center;
    // background-size: 100%;
    background: #fff;
    position: relative;
    box-sizing: border-box;
    padding: 16px;
    font-size: 12px;
    // padding-top: 16px;
    .field {
      position: absolute;
      max-width: 360px;
      word-break: break-all;
    }
  }
  .el-button.no-print {
    position: absolute;
    right: 10px;
    bottom: 10px;
  }
}

@media print {
  .no-print {
    // display: none;
  }
}

// ///
.pre {
  position: relative;
  width: 100%;
  height: 100%;
  font-size: 15px;
}
.pab {
  position: absolute;
}
.page0 {
  position: relative;
  width: 100%;
  height: 100%;
  .num {
    position: absolute;
    right: 30px;
    top: 10px;
  }
}
</style>