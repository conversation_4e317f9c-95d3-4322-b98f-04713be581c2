import request from '@/utils/request'

// EMS 月结
export function mailInfoList(data) {
  return request({
    url: `/case/mailInfo/list`,
    method: 'post',
    data
  })
}

// EMS月结-查询总金额
export function queryAmountTotal(data) {
  return request({
    url: `/case/mailInfo/queryAmountTotal`,
    method: 'post',
    data
  })
}

// 月结查询
export function printMonthList(data) {
  return request({
    url: `/case/print/monthList`,
    method: 'post',
    data
  })
}

// 待邮寄查询
export function waitMailQueryList(data) {
  return request({
    url: `/case/mailInfo/waitMailQueryList`,
    method: 'post',
    data
  })
}

// 邮寄审核
export function auditPrintList(data) {
  return request({
    url: `/case/print/auditPrintList`,
    method: 'post',
    data
  })
}

// 邮寄审核/预约领取列表-单条详细
export function getOrderDetailInfo(orderId) {
  return request({
    url: `/case/print/getOrderDetailInfo/${orderId}`,
    method: 'get'
  })
}

// 业务办理-邮寄审核/预约领取办理 -发送客服 功能
export function sendTelService(data) {
  return request({
    url: `/case/print/sendTelService`,
    method: 'post',
    data
  })
}

// 业务办理-邮寄审核/预约领取办理 -审核不通过 功能
export function printReject(data) {
  return request({
    url: `/case/print/reject`,
    method: 'post',
    data
  })
}

// 业务办理-邮寄审核/预约领取办理 -取消审核 功能
export function auditCancel(orderId, type, projectId) {
  return request({
    url: `/case/mailInfo/auditCancel/${orderId}/${type}/${projectId}`,
    method: 'get'
  })
}
