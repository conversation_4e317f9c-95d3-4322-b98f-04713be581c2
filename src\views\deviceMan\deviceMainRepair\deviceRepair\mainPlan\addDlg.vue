<template>
  <div>
    <el-dialog
      class="mazhenguo"
      :title="dlgType === 'add' ? '添加' : '编辑'"
      :close-on-click-modal="false"
      :visible.sync="dlgState"
      append-to-body
      width="800px"
      top="30px"
    >
      <el-form
        ref="dlgDataForm"
        :rules="dlgRules"
        :model="dlgData"
        label-position="right"
        label-width="120px"
        style="width: 750px"
        size="mini"
        @submit.native.prevent
        :disabled="dlgType == 'info'"
      >
        <el-form-item label="计划名称" prop="name">
          <el-input v-model="dlgData.name" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="项目" prop="projectId">
          <el-input placeholder="请输入" v-model="dlgData.projectName" disabled>
          </el-input>
        </el-form-item>
        <el-form-item label="设备名称" prop="equId">
          <el-input v-model="dlgData.equName" disabled v-if="dlgType != 'add'">
          </el-input>
          <el-select
            v-else
            v-model="dlgData.equId"
            @change="equIdChange"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item of deviceSelect"
              :key="item.id"
              :label="item.equName2"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="型号" prop="equModel">
          <el-input v-model="dlgData.equModel" placeholder="请输入" disabled />
        </el-form-item>
        <el-form-item label="设备位置" prop="equPosition">
          <el-input
            v-model="dlgData.equPosition"
            placeholder="请输入"
            disabled
          />
        </el-form-item>

        <el-form-item label="设备检修周期" prop="checkCycle">
          <!-- checkCycle
             checkCycleStr -->
          <el-select
            v-model="dlgData.checkCycle"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item of pzbyzqSelect"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="初次检修时间" prop="firstTime">
          <el-date-picker
            v-model="dlgData.firstTime"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="请选择"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="检修事项" prop="checkItem">
          <el-button @click="addItem" icon="el-icon-plus" type="primary" plain
            >添加</el-button
          >
          <el-table
            class="mt10"
            :data="dlgData.checkItem"
            fit
            border
            highlight-current-row
          >
            <el-table-column label="#" type="index" align="center" width="60">
            </el-table-column>
            <el-table-column label="检修事项">
              <template slot-scope="scope">
                <div v-if="dlgType == 'info'">{{ scope.row.name }}</div>

                <el-select
                  v-else
                  v-model="scope.row.value"
                  placeholder="请选择检修事项"
                  clearable
                  filterable
                  @change="typeChange(scope.$index, scope.row.value)"
                >
                  <el-option
                    v-for="(item, index) in maintenanceItemsOptions"
                    :key="index"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="检修内容">
              <template slot-scope="scope">
                <div v-if="dlgType == 'info'">{{ scope.row.content }}</div>

                <el-select
                  ref="selectJianxiu"
                  v-else
                  allow-create
                  default-first-option
                  v-model="scope.row.contentId"
                  placeholder="请选择检修内容"
                  clearable
                  filterable
                  :title="findLabel()"
                >
                  <el-option
                    v-for="(item, index) in scope.row.options
                      ? scope.row.options
                      : []"
                    :key="index"
                    :label="item.workDescribe"
                    :value="item.id"
                    :title="item.workDescribe"
                    style="width: 200px"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              v-if="dlgType != 'info'"
              label="操作"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  @click="delItem(scope.$index)"
                  icon="el-icon-delete"
                  size="mini"
                  type="danger"
                  title="删除"
                  plain
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <!-- <el-form-item label="检修方法" prop="checkMethod">
        <el-input v-model="dlgData.checkMethod" placeholder="请输入" />
      </el-form-item> -->

        <el-form-item label="员工" prop="checkPersonName">
          <el-input
            v-model="dlgData.checkPersonName"
            :title="dlgData.checkPersonName"
            @focus="showUserMulDlg"
            placeholder="请选择"
          />
        </el-form-item>

        <el-form-item label="计划图片" prop="fileUrl">
          <FileUpload
            :fileList0="dlgData.fileUrl"
            :onlyImage="true"
            ref="qiniuUploadRef"
            :limit="5"
            :maxSize="5"
            @successBack="successBack"
            :dlgType="dlgType"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
        <el-button
          v-if="dlgType != 'info'"
          :loading="dlgSubLoading"
          type="success"
          @click="dlgSubFunc"
          icon="el-icon-check"
        >
          <span v-if="dlgSubLoading">保存中...</span>
          <span v-else>保存</span>
        </el-button>
      </div>
    </el-dialog>

    <!-- 多选员工 -->
    <selectUserMulDlg
      ref="userMulDlgRef"
      :dlgType="dlgUserMulType"
      :dlgQuery="dlgUserMulQuery"
      :selectList0="dlgUserMulSelectList"
      @backFunc="dlgUserMulBackFunc"
      :noRule="noRule"
      :isOnJob="true"
    />
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import * as utils from "@/utils";
import { getDataDict } from "@/utils";

import { postAction, getAction, putAction } from "@/api";
import FileUpload from "@/views/deviceMan/components/fileUpload";
import selectUserMulDlg from "@/components/DialogWflow/selectUserMulDlg";

let dlgDataEmpty = {
  name: "", // 计划名称
  projectId: JSON.parse(window.localStorage.userInfo).projectId,
  projectName: JSON.parse(window.localStorage.userInfo).projectName,
  id: undefined,
  equId: undefined, // 设备名称
  equName: undefined,
  equModel: undefined, // 型号
  equPosition: undefined, // 设备所在位置
  checkPersonId: "", // 检修人员id
  checkPersonName: "",
  fileUrl: [],
  checkCycle: undefined, // 设备检修周期
  checkCycleStr: undefined,
  firstTime: "",
  checkItem: [{ contentId: "", value: "" }], // 检修事项
  // checkMethod: undefined, // 检修方法
};
export default {
  components: {
    FileUpload,
    selectUserMulDlg,
  },
  props: {
    dlgType: {
      type: String,
      default: "add",
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgData0: {
      type: Object,
      default: {},
    },
    projectList: {
      default: [],
    },
  },
  watch: {
     dlgState(val) {
      if (val) {
        setTimeout(() => {
          if (this.dlgType == "add") {
            this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
            this.$nextTick(() => {
              this.$refs["dlgDataForm"].clearValidate();
            });
          } else {
            this.getInit();
          }
        }, 50);
      } else {
        this.closeDlg();
      }
    },
  },
  computed: {},
  data() {
    return {
      selectDlgData: [],
      diaPostState2: false,
      toolShow: false,
      // 弹窗
      dlgState: false,
      dlgLoading: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        name: [{ required: true, message: "必填字段", trigger: "blur" }],
        projectId: [{ required: true, message: "必填字段", trigger: "change" }],
        equId: [{ required: true, message: "必填字段", trigger: "change" }],
        checkCycle: [
          { required: true, message: "必填字段", trigger: "change" },
        ],
        firstTime: [{ required: true, message: "必填字段", trigger: "change" }],
        checkItem: [{ required: true, message: "必填字段", trigger: "change" }],
        checkPersonName: [
          { required: true, message: "必填字段", trigger: "change" },
        ],
        // checkMethod: [{ required: true, message: "必填字段", trigger: "blur" }],
      },
      dlgSubLoading: false, // 提交loading

      deviceSelect: [],
      pzbyzqSelect: [],
      maintenanceItemsOptions: [],
      postDlgMulList: [],
      userInfo: JSON.parse(window.localStorage.userInfo),
      dlgUserMulQuery: {},
      dlgUserMulType: "", // 弹框状态add, edit
      dlgUserMulSelectList: "",
      noRule: false,
    };
  },
  created() {
    getDataDict(this, "maintenanceCycle", "pzbyzqSelect");
    getDataDict(this, "maintenanceItems", "maintenanceItemsOptions");
  },
  mounted() {
    this.getDeviceSelect(this.userInfo.projectId);
  },
  methods: {
    dlgUserMulBackFunc(list0) {
      console.log("弹窗发挥", list0);
      let id = list0.map((item) => item.id);
      let name = list0.map((item) => item.label);

      this.dlgData.checkPersonId = id.join(",");
      this.dlgData.checkPersonName = name.join(",");
    },
    showUserMulDlg() {
      if (this.dlgData.checkPersonId && this.dlgData.checkPersonName) {
        let idList = this.dlgData.checkPersonId.split(",");
        let nameList = this.dlgData.checkPersonName.split(",");
        let list = [];
        for (let i = 0; i < idList.length; i++) {
          list.push({
            id: idList[i],
            label: nameList[i],
          });
        }

        this.dlgUserMulSelectList = JSON.stringify(list);
      } else {
        this.dlgUserMulSelectList = "";
      }

      this.dlgUserMulQuery = {};

      this.dlgUserMulType = "edit";
      this.$refs.userMulDlgRef.show();
    },
    findLabel() {
      if (this.$refs.selectJianxiu) {
        return this.$refs.selectJianxiu.selected.label;
      }
    },
    typeChange(index, val) {
      this.dlgData.checkItem[index].contentId = "";
      if (val) {
        this.getRepairSubjectOptions(index, val);
      } else {
        this.dlgData.checkItem[index].options = [];
      }
    },
    getRepairSubjectOptions(index, val) {
      getAction(
        `sa/green/equ/check-item/page?type=${val}&pageNo=1&pageSize=1000`
      ).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          this.dlgData.checkItem[index].options = data.list;
          this.dlgData.checkItem = JSON.parse(
            JSON.stringify(this.dlgData.checkItem)
          );
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    projectIdChange(val) {
      this.dlgData.equId = "";
      this.dlgData.equName = "";
      this.dlgData.equModel = "";
      this.dlgData.equPosition = "";
      this.getDeviceSelect(val);
    },
    successBack(fileList) {
      this.dlgData.fileUrl = fileList;
    },
    async getDeviceSelect(projectId = "") {
      try {
        let res0 = await getAction(
          `sa/green/equipment-manage/page?responsiblePersonId=${JSON.parse(window.localStorage.userInfo).id}&equName=&pageNo=1&pageSize=200&projectId=${projectId}`
        );
        let res = res0.data;
        if (res.code == 200) {
          let list = res.data.list;
          for (let item of list) {
            item.equName2 = item.equName + "（" + item.equModel + "）";
          }
          this.deviceSelect = list;
        }
      } catch (err) {
        this.$message.error(err.msg);
      }
    },
    equIdChange(val) {
      let item = this.deviceSelect.filter((item) => item.id == val)[0];
      console.log("====item", item);
      this.dlgData.equName = item.equName;
      this.dlgData.equModel = item.equModel;
      this.dlgData.equPosition = item.equPosition;

      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    },

    addItem() {
      this.dlgData.checkItem.push({
        contentId: "",
        value: "",
      });
      this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
    },

    delItem(index) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.dlgData.checkItem.splice(index, 1);
        this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      });
    },

    async getInit() {
      console.log("====dlgData0", this.dlgData0);
      let id = this.dlgData0.id;
      try {
        let res0 = await getAction("sa/green/equ/check/get?id=" + id);
        let res = res0.data;
        if (res && res.code == 200) {
          // this.getDeviceSelect(res.data.projectId || "");

          let dlgData = JSON.parse(JSON.stringify(res.data));

          if (dlgData.checkItem) {
            dlgData.checkItem = JSON.parse(dlgData.checkItem);
            dlgData.checkItem.map((item) => {
              item.options = [
                {
                  id: item.contentId,
                  workDescribe: item.content,
                },
              ];
            });
          } else {
            dlgData.checkItem = [];
          }

          if (dlgData.fileUrl) {
            dlgData.fileUrl = JSON.parse(dlgData.fileUrl);
          } else {
            dlgData.fileUrl = [];
          }
          dlgData.checkPersonId += "";

          this.dlgData = JSON.parse(JSON.stringify(dlgData));
        }
      } catch (err) {
        console.log("====错误", err);
        this.$message.error(err.msg);
      }
    },
    dlgSubFunc() {
      this.$refs["dlgDataForm"].validate((valid) => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));

          let checkItem = sendObj.checkItem.filter((item) => {
            return item.name || item.value;
          });
          if (checkItem.length == 0) {
            this.$message.warning("检修内容不能为空");
            return false;
          }
          sendObj.checkItem.map((item) => {
            let content = item.options.find(
              (item1) => item1.id === item.contentId
            );
            if (content) {
              item.content = content.workDescribe;
            } else {
              item.content = item.contentId;
            }
            let { name } = this.maintenanceItemsOptions.find(
              (item1) => item1.id === item.value
            );
            item.name = name;
            delete item.options;
          });
          sendObj.checkItem = JSON.stringify(checkItem);
          sendObj.fileUrl = JSON.stringify(sendObj.fileUrl);

          sendObj.checkCycleStr = utils.arrId2Name(
            this.pzbyzqSelect,
            sendObj.checkCycle
          );

          let url = "";
          let func = postAction;
          if (this.dlgType == "add") {
            url = "sa/green/equ/check/create";
          } else if (this.dlgType == "copy") {
            delete sendObj.id;
            url = "sa/green/equ/check/create";
          } else {
            url = "sa/green/equ/check/update";
          }
          this.dlgSubLoading = true;
          func(url, sendObj).then((res0) => {
            this.dlgSubLoading = false;
            let res = res0.data;

            if (res.code == 200) {
              this.$message.success(res.msg);
              this.dlgState = false;
              this.$emit("getList");
              this.closeDlg();
            } else {
              this.$message({
                type: "warning",
                message: res.msg,
              });
            }
          });
        }
      });
    },

    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.dlgState = false;
      this.$refs["dlgDataForm"].resetFields();
      this.dlgData.checkPersonId = "";
      this.selectDlgData = [];
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>
