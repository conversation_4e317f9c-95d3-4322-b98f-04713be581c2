/**
 * desc 人力资源系统 - 薪酬管理
 * by 马振国
 */
import request from '@/utils/request'
import { requestExcel } from '@/utils'

// 【【 2 薪酬核算
export function findPayRollMonthByDynamic(data) {
  return request({
    url: `/ade/findPayRollMonthByDynamic`,
    method: 'post',
    data
  })
}

// 获取工资表明细  rollMonthId 工资表Id, page， size
export function findPayStaffMonthRoll(rollMonthId, page, size) {
  return request({
    url: `/ade/findPayStaffMonthRoll/${rollMonthId}/${page}/${size}`,
    method: 'get'
  })
}

// 获取工资表汇总
export function findPayMonthRoll(rollMonthId) {
  return request({
    url: `/ade/findPayMonthRoll/${rollMonthId}`,
    method: 'get'
  })
}

// 】】 2 薪酬核算

// 【【 3 薪酬审核
// 审核列表
export function findPayRollMonthByUser(data) {
  return request({
    url: `/ade/findPayRollMonthByUser`,
    method: 'post',
    data
  })
}
// 审核工资表 rollMonthId, status

export function updatePayRollUserStatus(data) {
  return request({
    url: `/ade/updatePayRollUserStatus`,
    method: 'post',
    data
  })
}

// 】】 3 薪酬审核

// 【【 1 薪酬维护
// 获取社保导入记录
export function findPayMonthSocialSecurityDynamic(data) {
  return request({
    url: `/ade/findPayMonthSocialSecurityDynamic`,
    method: 'post',
    data
  })
}

// 2 上传excel
export function payMonthSocialSecurityanalysisExcel(data) {
  return requestExcel('/ade/payMonthSocialSecurityanalysisExcel', data)
}

// 4 个税导入
export function findPayMonthPersonalIncomeFaxDynamic(data) {
  return request({
    url: `/ade/findPayMonthPersonalIncomeFaxDynamic`,
    method: 'post',
    data
  })
}
// 2 上传excel
export function payMonthPersonalIncomeFaxAnalysisExcel(data) {
  return requestExcel('/ade/payMonthPersonalIncomeFaxAnalysisExcel', data)
}

// 5 获取 绩效
export function findPerformanceDynamic(data) {
  return request({
    url: `/ade/findPerformanceDynamic`,
    method: 'post',
    data
  })
}
// 2 上传excel
export function analysisExcel(data) {
  return requestExcel('/ade/analysisExcel', data)
}

// 】】 1 薪酬维护

// 【【 5 工资表划分
// 增工资表
export function savePayRoll(data) {
  return request({
    url: `/ade/savePayRoll`,
    method: 'post',
    data
  })
}
// 编辑工资表
export function updatePayRoll(data) {
  return request({
    url: `/ade/savePayRoll`,
    method: 'post',
    data
  })
}



// 删除工资表（批量） { rollIds }
export function delPayRoll(data) {
  return request({
    url: `/ade/delPayRoll`,
    method: 'post',
    data
  })
}

// 查 工资表列表
export function findPayRollByDynamic(data) {
  return request({
    url: `/ade/findPayRollByDynamic`,
    method: 'post',
    data
  })
}

// 工资表详情
export function findPostByRollId(data) {
  return request({
    url: `/ade/findPostByRollId`,
    method: 'post',
    data
  })
}
// 选怎月份

export function savePayRollMonthByRollId(data) {
  return request({
    url: `/ade/savePayRollMonthByRollId`,
    method: 'post',
    data
  })
}


// 】】 5 工资表划分

// 【【 替岗工资维护
// 替岗工资保存 / 请假配置  saveSalaryAllocation
export function saveSysConfig(data) {
  return request({
    url: `/sys/saveSysConfig`,
    method: 'post',
    data
  })
}
// 
export function findConfigByType(data) {
  return request({
    url: `/sys/findConfigByType`,
    method: 'post',
    data
  })
}
// 】】 替岗工资维护

// 工资表扎帐
export function settleOfPayRoll(data) {
  return request({
    url: `/ade/settleOfPayRoll`,
    method: 'post',
    data
  })
}

// 【【 工资表拆分
// splitRoll,  // 拆分工资表
// findPaySplitMonthRoll,  // 查询工资表拆分表
// findPayStaffSplitRoll,  // 查询拆分工资表详细

// 拆分工资表  monthRollIds
export function splitRoll(data) {
  return request({
    url: `/ade/splitRoll`,
    method: 'post',
    data
  })
}

// 导出工资表
export function exportStaffMonthExcel(id) {
  return request({
    url: `/ade/exportStaffMonthExcel/${id}`,
    method: 'get'
  })
}

// 查询工资表拆分表  rollMonthId
export function findPaySplitMonthRoll(id) {
  return request({
    url: `/ade/findPaySplitMonthRoll/${id}`,
    method: 'get'
  })
}

// 查询拆分工资表详细  rollMonthId
export function findPayStaffSplitRoll(data) {
  return request({
    url: `/ade/findPayStaffSplitRoll`,
    method: 'post',
    data
  })
}
// 】】 工资表拆分

// 社保明细查询

export function findPaySocialSecurityDetailSummary(data) {
  return request({
    url: `/ade/findPaySocialSecurityDetailSummary`,
    method: 'post',
    data
  })
}