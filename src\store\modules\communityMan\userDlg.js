// 人员dlg组件

const userDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    userId: '',

    userName: '',

    userInfo: {},

    diaPostGet: [], // 选中的数值
    diaMulState:false,
    diaPostSel: '', // 单选选中的值
  },

  getters: {
    dlgShow: state => state.dlgShow,

    userId: state => state.userId,

    userName: state => state.userName,

    userInfo: state => state.userInfo,
    diaMulState: state => state.diaMulState,
    diaPostGet: state => state.diaPostGet,
    diaPostSel: state => state.diaPostSel,
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_USERID: (state, val) => {
      state.userId = val
    },

    SET_USERNAME: (state, val) => {
      state.userName = val
    },

    SET_USERINFO: (state, val) => {
      state.userInfo = val
    },
    // << 岗位多选
    SET_DIAPOST_GET: (state, val) => {
      state.diaPostGet = val
    },
    SET_DIAPOST_SEL: (state, val) => {
      state.diaPostSel = val
    },
    SET_DIAPOST_MUL: (state, val) => {
      state.diaMulState = val
    }
  },

  actions: {

  }
}

export default userDlg
