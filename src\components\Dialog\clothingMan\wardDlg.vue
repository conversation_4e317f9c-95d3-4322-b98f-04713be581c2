<template>
  <el-dialog :close-on-click-modal='false' :title="'病房列表'" :visible.sync="dlgShow">
    <div class="filter-container">
      <div class='fr'>
        <el-input v-model="listQuery.str" placeholder='请填写病房名称'>
          <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
        </el-input>
        <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">
          搜索
        </el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectWardId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>

        <el-table-column label="病房名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="区域">
          <template slot-scope="scope">
            <span>{{ scope.row.districtName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属科室">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'

import { findWardDynamic } from '@/api/medicalMatchManSystem/clothingMan/wardMan'

export default {
  components: {
    Pagination
  },
  data() {
    return {
      list: [],

      listQuery: {
        page: 1,
        size: 10,
        str: "",
        branchId: ""
      },

      total: 0,

      selectWardId: '',

      selectWardName: ''

    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.clothingMan.wardDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('clothingMan/wardDlg/SET_DLGSHOW', val)
      }
    },

    branchId: {
      get: function () {
        return this.$store.state.clothingMan.wardDlg.branchId
      },
      set: function (val) {
        this.$store.commit('clothingMan/wardDlg/SET_BRANCHID', val)
      }
    },

    wardId: {
      get: function () {
        return this.$store.state.clothingMan.wardDlg.wardId
      },
      set: function (val) {
        this.$store.commit('clothingMan/wardDlg/SET_WARDID', val)
      }
    },

    wardName: {
      get: function () {
        return this.$store.state.clothingMan.wardDlg.wardName
      },
      set: function (val) {
        this.$store.commit('clothingMan/wardDlg/SET_WARDNAME', val)
      }
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.wardId)) {
          this.selectWardId = ""
          this.selectWardName = ""
        }
        this.getList()
      }
    },

    branchId(val) {
      this.listQuery.branchId = val
    },

    wardId(val) {
      this.selectWardId = val
    },

    wardName(val) {
      this.selectWardName = val
    }
  },

  methods: {
    resetStr() {
      this.listQuery.str = ""
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    rowClick(row, column, event) {
      this.selectWardId = row['id']
      this.selectWardName = row['name']
    },

    getList() {
      this.list = []
      findWardDynamic(this.listQuery).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      this.wardId = this.selectWardId
      this.wardName = this.selectWardName
      this.$store.commit('clothingMan/wardDlg/SET_WARDID', this.wardId)
      this.$store.commit('clothingMan/wardDlg/SET_WARDNAME', this.wardName)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('clothingMan/wardDlg/SET_DLGSHOW', false)
    }
  }
}
</script>