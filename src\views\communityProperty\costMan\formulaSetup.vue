<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区：">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公式名称：">
          <el-input @keyup.enter.native="getList" placeholder="请输入公式名称" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="addItem">添加</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="公式名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="公式内容">
          <template slot-scope="scope">
            <span>{{ scope.row.content }}</span>
          </template>
        </el-table-column>

        <el-table-column label="创建人">
          <template slot-scope="scope">
            <span>{{ scope.row.createName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态">
          <template slot-scope="scope">
            <span>{{ scope.row.status == 0 ? '未使用' : '已使用' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row, 'EDIT')"
              >编辑</el-button
            >
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal="false" title="新增/编辑公式" :visible.sync="dlgShow" width="900px" append-to-body>
      <el-form
        ref="dlgForm"
        :disabled="dlgType == 'VIEW'"
        :rules="rules"
        :model="dlgData"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="所在小区：" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="公式名称：" prop="name">
          <el-input v-model="dlgData.name" placeholder="请输入公式名称" />
        </el-form-item>

        <el-form-item label="公式操作符：">
          <el-button icon="el-icon-plus" type="primary" size="mini" @click="addFormula(1)">参数</el-button>
          <el-button icon="el-icon-plus" type="warning" size="mini" @click="addFormula(2)">数值</el-button>
          <el-button icon="el-icon-plus" type="success" size="mini" @click="addFormula(3)">符号</el-button>
          <el-button icon="el-icon-plus" type="warning" size="mini" @click="addFormula(4)">租金单价</el-button>
          <el-button icon="el-icon-delete" type="danger" size="mini" @click="delFormula()">删除</el-button>
        </el-form-item>

        <el-form-item label="公式编辑：" prop="contentJson">
          <span v-for="(item, index) in dlgData.contentJson" :key="index">
            <el-select
              @change="calcFormula()"
              style="width: 100px"
              v-if="item.type == 1"
              v-model="item.value"
              placeholder="参数"
            >
              <el-option v-for="it in item.list" :key="it.id" :label="it.name" :value="it.id"> </el-option>
            </el-select>
            <el-input-number
              @change="calcFormula()"
              style="width: 100px"
              v-if="item.type == 2"
              v-model="item.value"
              :controls="false"
              :min="0"
              :precision="2"
              :step="1"
            ></el-input-number>
            <el-input-number
              @change="calcFormula()"
              style="width: 100px"
              v-if="item.type == 4"
              v-model="item.value"
              :controls="false"
              :min="0"
              :precision="2"
              :step="1"
            ></el-input-number>
            <el-select
              @change="calcFormula()"
              style="width: 80px"
              v-if="item.type == 3"
              v-model="item.value"
              placeholder="符号"
            >
              <el-option v-for="it in item.list" :key="it.id" :label="it.name" :value="it.id"> </el-option>
            </el-select>
          </span>
        </el-form-item>

        <el-form-item label="公式预览：" prop="content"> 计费金额 = {{ dlgData.content }} </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type="success" :loading="dlgLoading" @click="subDlg" icon="el-icon-check">
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'
import { formulaPage, formulaAddOrUpdate, formulaDel } from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  communityId: '',
  communityName: '',
  contentJson: [], // 1参数 bud2数值 3符号
  contentValue: '',
  content: '',
  name: '',
}

export default {
  name: 'formulaSetup',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        content: [{ required: true, message: '必填字段', trigger: 'change' }],
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
      },
      communityList: [],
      userInfo: {},

      ruleTypeList: [
        {
          id: 'builtUpArea',
          name: '建筑面积',
        },
        {
          id: 'insideArea',
          name: '套内面积',
        },
        {
          id: 'layer',
          name: '楼层',
        },
      ],

      symbolList: [
        {
          id: '+',
          name: '+',
        },
        {
          id: '-',
          name: '-',
        },
        {
          id: '*',
          name: '*',
        },
        {
          id: '/',
          name: '/',
        },
        {
          id: '(',
          name: '(',
        },
        {
          id: ')',
          name: ')',
        },
      ],
    }
  },

  created() {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
      }
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    addFormula(type) {
      console.log(this.dlgData.contentJson,"this.dlgData.contentJson");
      if(type==4){
        for(let i of this.dlgData.contentJson){
          if(i.type==4){
            this.$message({
            type: 'warning',
            message: '已添加租金单价',
          })
          return false
          }
        }
      }
      let param = {
        type,
        value: '',
      }
      if (type == 1) {
        param.list = JSON.parse(JSON.stringify(this.ruleTypeList))
      } else if (type == 3) {
        param.list = JSON.parse(JSON.stringify(this.symbolList))
      }
      this.dlgData.contentJson.push(param)
    },

    delFormula() {
      this.dlgData.contentJson.pop()
      this.calcFormula()
    },

    calcFormula() {
      let content = ''
      let contentValue = ''
      for (let i of this.dlgData.contentJson) {
        contentValue += i.value
        if (i.type == 1) {
          content += utils.getNameById(i.value, i.list)
        } else if (i.type == 2) {
          content += i.value
        } else if (i.type == 3) {
          content += utils.getNameById(i.value, i.list)
        }else if (i.type == 4) {
          content += i.value
        }
      }
      this.dlgData.content = content
      this.dlgData.contentValue = contentValue
    },
    formatList() {},

    // 获取数据
    getList() {
      if (utils.isNull(this.listQuery.communityId)) {
        this.$message.warning('请选择小区')
        return false
      }

      this.count++
      this.listLoading = true

      formulaPage(this.listQuery).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page.total ? res.data.page.total : 0
          this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    addItem() {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg() {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          for(let i of postParam.contentJson){
          if(i.type==4){
            postParam.contractPrice=i.value
          }
        }
          postParam.contentJson = JSON.stringify(postParam.contentJson)
          postParam.projectId = this.userInfo.projectId
          postParam.creator = Cookie.get('userId')
          postParam.createName = Cookie.get('userName')
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          this.dlgLoading = true
          formulaAddOrUpdate(postParam).then((res) => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem(data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgData.contentJson = JSON.parse(this.dlgData.contentJson)
      this.dlgType = type
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 启用停用
    delItem(data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        formulaDel(data.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


