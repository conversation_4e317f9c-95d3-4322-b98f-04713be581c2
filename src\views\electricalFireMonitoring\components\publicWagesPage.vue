<template>
  <!-- 电气火灾监控公共tab -->
  <div id="dqhz">
    <el-row
      type="flex"
      justify="space-between"
      v-if="boxState0"
      :gutter="20"
      style="margin-bottom: 20px"
    >
      <el-col :span="6" style="position: relative;">
        <el-card style="height: 256px;" shadow="never">
          <div slot="header" class="clearfix" style="font-weight: bolder;">
            <span style="font-size: 16px;">{{ t1Title }}</span>
          </div>
          <div id="echart-panel" class="echart"></div>
        </el-card>
        <div
          style="width: 20px;height: 60px;position: absolute;top: 135px;right: 4%;"
        >
          <div style="margin-bottom: 7px;color: #ff5c57;">
            {{ fanDiagram.baojing }}
          </div>
          <div style="margin-bottom: 8px;color: #7ef1c0;">
            {{ fanDiagram.lixian }}
          </div>
          <div style="color: #49a2ff;">{{ fanDiagram.zhengchang }}</div>
        </div>
      </el-col>
      <el-col :span="18">
        <el-card style="height: 256px;" class="card1" shadow="never">
          <div slot="header" class="clearfix" style="font-weight: bolder;">
            <div style="font-size: 16px;">
              报警信息<span
                class="fdanger"
                style="margin-left: 20px; font-weight: bold"
                >{{ total }}条</span
              >
            </div>
          </div>
          <div
            v-for="(item, index) in list"
            :key="index"
            class="alarmMessageListBox"
          >
            <div style="color: #ff5c57;display: inline-block;width: 10%;">
              <img
                class="mr10"
                style="height: 15px;"
                src="/static/image/overload-alarm.png"
                alt=""
              /><span>{{ item.alertName }}</span>
            </div>
            <div style="display: inline-block;width: 20%;">
              {{ item.content }}
            </div>
            <!-- background-color: #ffe8e8;border-radius:15px; -->
            <div style="display: inline-block;padding: 5px;width: 50%;">
              <div style="width: 100%;color: #ff5c57;">
                <div style="display: inline-block;margin-right: 30px;">
                  标准值:{{ item.alarmValue }}
                </div>
                <div style="display: inline-block;margin-right: 30px;">
                  正常范围:{{ item.startValue + ~+item.endValue }}
                </div>
                <div style="display: inline-block;margin-right: 30px;">
                  所在区域:{{
                    item.equipRoomName + item.equipName + item.nodeName
                  }}
                </div>
              </div>
            </div>
            <el-button
              type="danger"
              size="mini"
              round
              @click="showGiveAlarmDlg(item, 'add')"
              >查看详情</el-button
            >
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- :style="boxState0 ? 'height: calc(100% - 250px)' : 'height:100%'" -->
    <el-card :class="boxState0 ? 'card2_1' : 'card2_2'" shadow="never">
      <div slot="header" class="clearfix" style="font-weight: bolder;">
        <span style="font-size: 16px;" class="mr10">设备视图</span>
        <el-radio-group
          v-model="listQuery2.state"
          style="display: inline-block;"
          @change="getList2"
        >
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button label="0">正常</el-radio-button>
          <el-radio-button label="2">离线</el-radio-button>
          <el-radio-button label="1">报警</el-radio-button>
        </el-radio-group>
      </div>
      <div
        v-for="(item, index) in list2"
        :key="index"
        class="deviceViewBox item-bg-red"
      >
        <!-- <el-switch
            style="margin-bottom: 20px;"
            disabled
            v-model="item.value"
            active-color="#4c8af6"
            inactive-color="#d8d8d8"
          >
          </el-switch> -->
        <div style="width: 248px;padding-left: 20px">
          <div
            style="font-size: 14px;"
            :class="
              `${item.state + '' === '0' ? ' text-blue' : ''}${
                item.state + '' === '1' ? ' text-red' : ''
              }`
            "
          >
            <span class="mr10">{{ item.equipRoomName }}</span>
            <span>{{ item.name }}</span>
          </div>
          <div style="font-size: 12px;" class="mt10 c999">
            最新遥测：{{ item.updateTime }}
          </div>
        </div>

        <div
          v-if="item.nodeMonitorConfigs2"
          class="flexc"
          style="width: 540px; justify-content: start;"
        >
          <div
            :class="
              `list-yuan-btn bg-green ${
                item.nodeMonitorConfigs2.d1038State == '1' ? 'bg-red' : ''
              }`
            "
          >
            剩余电流：{{ item.nodeMonitorConfigs2.d1038 }}
          </div>
          <div
            class="
             list-yuan-btn bg-yellow
            "
          >
            用电量：{{ item.nodeMonitorConfigs2.d1007 }}
          </div>
          <div
            :class="
              `list-yuan-btn bg-blue ${
                item.nodeMonitorConfigs2.d1014State == '1' ? 'bg-red' : ''
              }`
            "
          >
            功率：{{ item.nodeMonitorConfigs2.d1014 }}
          </div>
        </div>

        <div class="flexc" style="color: #666">
          <div v-if="item.nodeMonitorConfigs2" class="">
            <div>
              <span class="">温度：</span
              ><span class="ml16">{{
                item.nodeMonitorConfigs2.d1031 || 0
              }}</span
              ><span class="ml16">{{
                item.nodeMonitorConfigs2.d1032 || 0
              }}</span
              ><span class="ml16">{{
                item.nodeMonitorConfigs2.d1033 || 0
              }}</span>
              <span class="ml16">{{
                item.nodeMonitorConfigs2.d1034 || 0
              }}</span>
            </div>
            <div>
              <span class="">电压：</span
              ><span class="ml16">{{
                item.nodeMonitorConfigs2.d1008 || 0
              }}</span
              ><span class="ml16">{{
                item.nodeMonitorConfigs2.d1009 || 0
              }}</span
              ><span class="ml16">{{
                item.nodeMonitorConfigs2.d1010 || 0
              }}</span>
              <span class="ml16">{{
                item.nodeMonitorConfigs2.d1028 || 0
              }}</span>
            </div>
            <div>
              <span class="">电流：</span
              ><span class="ml16">{{
                item.nodeMonitorConfigs2.d1011 || 0
              }}</span
              ><span class="ml16">{{
                item.nodeMonitorConfigs2.d1012 || 0
              }}</span
              ><span class="ml16">{{
                item.nodeMonitorConfigs2.d1013 || 0
              }}</span>
              <span class="ml16">{{
                item.nodeMonitorConfigs2.d1030 || 0
              }}</span>
            </div>
          </div>
          <div v-else-if="pageType == 'smokeDetector'">
            <div
              v-for="(item2, index2) of item.nodeMonitorConfigs"
              :key="index2"
            >
              {{ item2.dataTypeText + item2.currentValue + item2.unit }}
            </div>
          </div>
          <!-- <div v-else-if="pageType == 'gas'">
            <div
              v-for="(item2, index2) of item.nodeMonitorConfigs"
              :key="index2"
            >
              {{ item2.dataTypeText + item2.currentValue + item2.unit }}
            </div>
          </div> -->
          <div v-else-if="pageType == 'waterImmersion'">
            <div
              style="display: inline-block;margin-right: 60px;"
              v-for="(item2, index2) of item.nodeMonitorConfigs"
              :key="index2"
            >
              {{ item2.dataTypeText +':'+ item2.currentValue+item2.unit }}
            </div>
          </div>
          <div v-else-if="pageType == 'temperatureHumidityEquipment'">
            <div
              v-for="(item2, index2) of item.nodeMonitorConfigs"
              :key="index2"
            >
              {{ item2.dataTypeText + item2.currentValue + item2.unit }}
            </div>
          </div>
          <div v-else-if="pageType == 'airSwitchEquipment'">
            <div
              v-for="(item2, index2) of item.nodeMonitorConfigs"
              :key="index2"
            >
              {{ item2.dataTypeText + item2.currentValue + item2.unit }}
            </div>
          </div>
        </div>

        <div class="" style="width: 215px; justify-content: start;">
          <div>
            <div>
              <el-button
                v-if="item.abnormalAlarmCount != ''"
                type="danger"
                size="mini"
                round
                @click="showAlarmRecord(item, $event)"
                >报警！{{ item.abnormalAlarmCount }}条警告！查看详情</el-button
              >
            </div>
            <div style="margin-top: 6px;margin-left: 85px;">
              <el-button
                type="primary"
                size="mini"
                round
                @click="showDeviceDetailsDlg(item, $event)"
                >设备详情</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </el-card>
    <div class="page-container">
      <pagination
        :total="deviceViewTotal"
        :page.sync="listQuery2.page"
        :limit.sync="listQuery2.limit"
        @pagination="getList2"
      />
    </div>

    <el-dialog
      title="报警详情"
      :close-on-click-modal="false"
      :visible.sync="giveAlarmDlg"
      width="1200px"
      append-to-body
    >
      <el-row type="flex" justify="space-between">
        <el-col :span="11" class="giveAlarmDlgCol">
          <div
            style="font-weight: bolder;color: #49a2ff;line-height: 16px;position: relative;"
          >
            <span style="position: absolute;top: -1px;">|</span>
            <span class="ml10">设备图纸</span>
            <el-button
              style="margin-left:320px"
              type="success"
              @click="viewLargerImg"
              size="mini"
              icon="el-icon-check"
            >
              <span>查看大图</span>
            </el-button>
          </div>
          <div style="flex: 1;position: relative" class="mt10 dflex">
            <div class="drop-box dflex" v-if="equipmentDrawing">
              <div class="drop-right">
                <!-- <el-upload style="width: 980px; height: 660px" v-if="!dropDiaImgUrl" class="avatar-uploader" title="点击上传图片" action="" :show-file-list="false" :before-upload="uploadFunc1">
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload> -->

                <img
                  :src="equipmentDrawing"
                  :style="`width:${imgWidth}px; height:${imgHeight}px`"
                />
                <!-- <el-image
                  class="drop-right-img"
                  :preview-src-list="equipmentDrawingList"
                  :src="equipmentDrawing"
                  alt=""
                >
                  <div slot="error" class="image-slot">
                    <i class=""></i>
                  </div>
                </el-image> -->

                <!-- <div style="width: 400px; height: 400px; border: 1px solid #000">
            
          </div> -->
              </div>

              <div class="drop-left flex1">
                <!-- 相对定位 可移动的 -->
                <div class="drop-left-p">
                  <img
                    class="icon_cgq_right"
                    style="position: absolute;"
                    v-for="(item, index) of dropDiaXYList"
                    :key="index"
                    :style="
                      `left:${(item.left + 10 + 10) * iconScale1 -
                        10}px; top:${(item.top + 10 + 10) * iconScale1 - 10}px`
                    "
                    :data-index="index"
                    name="sensor-p-item"
                    :title="item.name"
                    @mousedown="moveRightFunc($event, index)"
                    :src="`/static/image/${item.imgName || 'icon_sbxq'}.gif`"
                    alt=""
                  />
                  <!-- <div
              class="icon_cgq_right success"
              v-for="(item,index) of dropDiaXYList"
              :key="index"
              :data-index="index"
              :style="`left:${item.left}px; top:${item.top}px`"
              name='sensor-p-item'
              :title="item.name"
              @mousedown="moveRightFunc($event, index)">
            </div> -->
                </div>
                <!-- 绝对定位 固定的 -->
                <div class="drop-left-a" style="display:none">
                  <div
                    v-for="(item, index) of dropDiaList"
                    :key="index"
                    :data-index="index"
                    @mousedown="moveLeftFunc($event, index)"
                    class="sensor-item sensor-a-item"
                  >
                    <div class="flex sensor-item-top">
                      <!-- << 图片 -->
                      <img
                        class="icon_cgq"
                        :src="`/static/image/${item.imgName || 'icon_cgq'}.png`"
                        alt=""
                      />
                      <!-- <div class="icon_cgq success"></div> -->
                      <!-- >> 图片 -->

                      <div
                        :class="
                          `flex1 not-select ${
                            item.isSelected ? 'fsuccess fbold' : ''
                          } `
                        "
                        style="line-height: 23px"
                      >
                        {{ item.name }}
                      </div>
                      <i
                        v-if="item.iconState"
                        class="el-icon-back fdanger fr"
                        @click="backFunc(index)"
                        style="font-size: 22px"
                        title="撤销"
                      ></i>
                      <!-- {{positionX}}
                {{positionY}} -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div></div>
          </div>
        </el-col>
        <el-col :span="6" class="giveAlarmDlgCol">
          <div
            style="font-weight: bolder;color: #49a2ff;line-height: 16px;position: relative;"
          >
            <span style="position: absolute;top: -1px;">|</span>
            <span class="ml10">报警信息</span>
          </div>
          <div>
            <span style="font-weight: bolder;">报警设备:</span>
            <span style="color: #767778;">{{ giveAlarmRow.name }}</span>
          </div>
          <div>
            <span style="font-weight: bolder;">报警区域:</span>
            <span style="color: #767778;">{{
              giveAlarmRow.equipRoomName + giveAlarmRow.equipName
            }}</span>
          </div>
          <div>
            <span style="font-weight: bolder;">报警类型:</span>
            <span style="color: #767778;">{{ giveAlarmRow.alertName }}</span>
          </div>
          <div>
            <span style="font-weight: bolder;">报警时间:</span>
            <span style="color: #767778;">{{ giveAlarmRow.createTime }}</span>
          </div>
          <div>
            <span style="font-weight: bolder;">责任人:</span>
            <span style="color: #767778;"></span>
          </div>
          <div>
            <span style="font-weight: bolder;">责任人联系电话:</span>
            <span style="color: #767778;"></span>
          </div>
          <div>
            <span style="font-weight: bolder;">报警内容:</span>
            <span style="color: #767778;">{{ giveAlarmRow.content }}</span>
          </div>
        </el-col>
        <el-col :span="6" class="giveAlarmDlgCol">
          <div
            style="font-weight: bolder;color: #49a2ff;line-height: 16px;position: relative;"
          >
            <span style="position: absolute;top: -1px;">|</span>
            <span class="ml10">设备信息</span>
          </div>
          <div>
            <span style="font-weight: bolder;">{{
              alarmDetailData.pojo.name
            }}</span>
          </div>
          <div>
            <span style="font-weight: bolder;">设备类型:</span>
            <span style="color: #767778;">{{
              alarmDetailData.pojo.nodeArchivesName
            }}</span>
          </div>
          <!-- <div>
            <span style="font-weight: bolder;">资产状态:</span>
            <span style="color: #49a73e;">闲置{{}}</span>
          </div> -->
          <div>
            <span style="font-weight: bolder;">IOT设备状态:</span>
            <span style="color: #767778;" v-if="alarmDetailData.pojo.state == 2"
              >离线</span
            >
            <span style="color: #767778;" v-if="alarmDetailData.pojo.state == 1"
              >报警</span
            >
            <span style="color: #767778;" v-if="alarmDetailData.pojo.state == 0"
              >正常</span
            >
          </div>
          <div>
            <span style="font-weight: bolder;">设备机构:</span>
            <span style="color: #767778;">{{ userInfo.projectName }}</span>
          </div>
          <div>
            <span style="font-weight: bolder;">设备区域:</span>
            <span style="color: #767778;">{{
              alarmDetailData.pojo.equipRoomName
            }}</span>
          </div>
          <div>
            <span style="font-weight: bolder;">安装位置:</span>
            <span style="color: #767778;">{{
              alarmDetailData.pojo.installAddr
            }}</span>
          </div>
        </el-col>
      </el-row>
      <div class="mt20" style="font-size: 14px;font-weight: bolder;">
        报警处理
      </div>
      <el-form
        class="mt20"
        ref="dlgForm"
        :rules="dlgRules"
        :model="dlgData"
        label-position="right"
        label-width="110px"
        size="mini"
        @submit.native.prevent
      >
      <el-row>
          <el-col :span="6">
            <el-form-item label="处理类型" prop="status">
              <el-radio-group v-model="dlgData.status" @change="radioChange()">
                <el-radio :label="1" :disabled="isDis">处理中</el-radio>
                <el-radio :label="2">已处理</el-radio>
             </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计处理时长" v-if="dlgData.status =='1'" prop="handlerHours">
              <el-radio-group v-model="dlgData.handlerHours" :disabled="isDis">
                <el-radio :label="1">1小时</el-radio>
                <el-radio :label="6">6小时</el-radio>
                <el-radio :label="12">12小时</el-radio>
                <el-radio :label="24">24小时</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="结果" prop="handlerResultStr" v-if="dlgData.status =='2'">
          <el-select
            style="width: 200px;"
            v-model="dlgData.handlerResultStr"
            placeholder="请选择"
            @change="gjSelectProductType"
          >
            <el-option
              v-for="item in gjSelect"
              :key="item.id"
              :label="item.name"
              :value="{ value: item.id, label: item.name }"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-row v-if="dlgData.status =='2'">
          <el-col :span="6">
            <el-form-item label="现场确认人员" prop="handler">
              <el-input
                style="width: 200px;"
                v-model="dlgData.handler"
                @focus="showUserTree"
                placeholder="请选择现场确认人员"
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="确认时间" prop="confirmationTime">
              <el-date-picker
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="dlgData.confirmationTime"
                type="datetime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="handlerRemark" v-if="dlgData.status =='2'">
          <el-input
            :rows="3"
            type="textarea"
            resize="none"
            v-model="dlgData.handlerRemark"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="giveAlarmDlg = false" icon="el-icon-back"
          >关闭</el-button
        >
        <el-button type="success" @click="subGiveAlarmDlg" icon="el-icon-check">
          <span>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="报警记录"
      :close-on-click-modal="false"
      :visible.sync="alarmRecordDlg"
      width="1200px"
      append-to-body
    >
      <!-- 搜素按钮组 -->
      <div class="filter-container">
        <el-form inline>
          <el-form-item label="报警类型">
            <el-select
              @change="searchAlarmRecord"
              v-model="alarmRecordQuery.alertType"
              clearable
              filterable
              placeholder="异常类型"
              style="width: 140px"
            >
              <el-option
                v-for="item of alertSelect"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="处理状态">
            <el-select
              @change="searchAlarmRecord"
              v-model="alarmRecordQuery.status"
              clearable
              placeholder="处理状态"
              style="width: 140px"
            >
              <el-option
                v-for="item of handleStatusSelect"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item label="设备类型">
            <el-select
              v-model="alarmRecordQuery.deviceType"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in deviceTypeList"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            @click="searchAlarmRecord"
            >搜索</el-button
          >
          <!-- <el-button
            icon="el-icon-document-copy"
            type="primary"
            size="mini"
            plain
            @click="batchProcessing"
            >批量处理</el-button
          > -->
          <!-- <el-button
            icon="el-icon-download"
            type="success"
            size="mini"
            plain
            @click="exportingAlarmInformation"
            >导出报警信息</el-button
          > -->
        </el-form>
      </div>
      <!-- 表格 -->
      <el-table
        class="m-small-table"
        height="500px"
        :data="alarmRecordList"
        border
        fit
        highlight-current-row
        ref="multipleTable"
        row-key="id"
      >
        <!-- @select="alarmRecordListDataChange"
        @row-click="alarmRecordClick" -->
        <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
        <el-table-column label="#" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="报警时间" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警对象">
          <template slot-scope="scope">
            <span>{{ scope.row.nodeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警类型">
          <template slot-scope="scope">
            <span>{{ scope.row.alertName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警级别" width="90" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.alarmLevel == '1'">一级报警</span>
            <span v-if="scope.row.alarmLevel == '2'">二级报警</span>
            <span v-if="scope.row.alarmLevel == '3'">三级报警</span>
          </template>
        </el-table-column>

        <el-table-column label="报警内容" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.content }}</span>
          </template>
        </el-table-column>

        <el-table-column label="设备间名称">
          <template slot-scope="scope">
            <span>{{ scope.row.equipRoomName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="设备名称">
          <template slot-scope="scope">
            <span>{{ scope.row.equipName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="单位" width="70" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.unit }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警值">
          <template slot-scope="scope">
            <span>{{ scope.row.alarmValue }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警限值">
          <template slot-scope="scope">
            <span>{{ scope.row.endValue }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.status==0" style="color: #ff5c57;">待处理</span>
            <span v-if="scope.row.status==1" class="fwarning">处理中</span>
            <span v-if="scope.row.status==2" class="fsuccess">已处理</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="120"
          class-name="small-padding fixed-width"
          label="操作"
        >
          <template slot-scope="scope">
            <!-- <el-button type="text" size="mini" @click="showGiveAlarmDlg(scope.row, 'info')">查看</el-button> -->
            <el-button
              type="text"
              size="mini"
              @click="showGiveAlarmDlg(scope.row, 'add')"
              >处理</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="page-container">
        <pagination
          :total="alarmRecordTotal"
          :page.sync="alarmRecordQuery.page"
          :limit.sync="alarmRecordQuery.limit"
          @pagination="getAlarmRecord"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="alarmRecordDlg = false" icon="el-icon-back"
          >关闭</el-button
        >
        <!-- <el-button type="success" @click="subGiveAlarmDlg" icon="el-icon-check">
          <span>提交</span>
        </el-button> -->
      </div>
    </el-dialog>

    <el-dialog
      @close="cloDeviceDetailsDlg"
      title="设备详情"
      :close-on-click-modal="false"
      :visible.sync="deviceDetailsDlg"
      width="1200px"
      append-to-body
    >
      <el-tabs v-model="deviceDetailsQuery.activeName" @tab-click="handleClick">
        <el-tab-pane label="设备详情" name="first">
          <el-descriptions title="" :column="2" border>
            <el-descriptions-item
              label="名称:"
              labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
            >
              <span style="font-weight: bolder;">{{ deviceViewRow.name }}</span>
            </el-descriptions-item>
            <el-descriptions-item
              label="设备标识:"
              labelStyle="font-weight: bolder;font-size: 14px;text-align: center;width: 130px"
            >
              <span style="font-weight: bolder;">{{
                deviceViewRow.devEui
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item
              label="绑定设备间:"
              labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
            >
              <span style="font-weight: bolder;">{{
                deviceViewRow.equipRoomName
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item
              label="绑定设备:"
              labelStyle="font-weight: bolder;font-size: 14px;text-align: center;width: 130px"
            >
              <span style="font-weight: bolder;">{{
                deviceViewRow.equipName
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item
              :span="2"
              label="设备档案:"
              labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
            >
              <span style="font-weight: bolder;">{{
                deviceViewRow.nodeArchivesName
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item
              :span="2"
              label="最大报警间隔:"
              labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
            >
              <span style="font-weight: bolder;">{{
                deviceViewRow.alarmInterval
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item
              :span="2"
              label="通讯地址:"
              labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
            >
              <span style="font-weight: bolder;">{{ deviceViewRow.addr }}</span>
            </el-descriptions-item>
            <el-descriptions-item
              :span="2"
              label="位置:"
              labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
            >
              <span style="font-weight: bolder;">{{
                deviceViewRow.installAddr
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item
              :span="2"
              label="是否开启联动:"
              labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
            >
              <span style="font-weight: bolder;">{{
                deviceViewRow.isOpenLink == 1 ? "是" : "否"
              }}</span>
            </el-descriptions-item>
          </el-descriptions>
          <!-- <el-descriptions title="" :column="1" border style="border-top: none !;">
            <el-descriptions-item label="设备档案:" labelStyle="font-weight: bolder;font-size: 14px">
              <span style="font-weight: bolder;">博高可燃气体</span>
            </el-descriptions-item>
          </el-descriptions> -->
        </el-tab-pane>
        <el-tab-pane label="报警配置" name="second">
          <el-table
            ref="tableBar"
            class="m-small-table"
            v-loading="listLoading"
            :data="alarmConfigurationList"
            max-height="500px"
            border
            fit
            highlight-current-row
            style="width: 100%; height: auto"
          >
            <el-table-column
              label="#"
              type="index"
              align="center"
              width="60"
            ></el-table-column>
            <el-table-column label="参数" align="center">
              <template slot-scope="scope">
                <span>{{
                  scope.row.dataTypeText + "(" + "单位:" + scope.row.unit + ")"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="标准值" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.standardValue }}</span>
              </template>
            </el-table-column>
            <el-table-column label="正常范围" align="center">
              <template slot-scope="scope">
                <span>{{
                  scope.row.startValue + "~" + scope.row.endValue
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="忽略报警时间段" align="center">
              <template slot-scope="scope">
                <span>{{
                  scope.row.ignoreStartTime != null &&
                  scope.row.ignoreStartTime != ""
                    ? scope.row.ignoreStartTime + "~" + scope.row.ignoreEndTime
                    : ""
                }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="忽略报警值开关/开关状态" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.assetCode }}</span>
              </template>
            </el-table-column>
            <el-table-column label="高于报警值开关/开关状态" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.assetCode }}</span>
              </template>
            </el-table-column> -->
            <!-- <el-table-column
              align="center"
              width="90"
              class-name="small-padding fixed-width"
              label="操作"
            >
              <template slot-scope="scope">
                <el-button type="text" size="mini">删除</el-button>
              </template>
            </el-table-column> -->
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="历史记录" name="third">
          <!-- 搜素按钮组 -->
          <div class="filter-container">
            <el-form inline>
              <el-form-item label="">
                <el-date-picker
                  @change="searchHistoryFunc"
                  v-model="historyQuery.dateRange"
                  type="daterange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                >
                </el-date-picker>
              </el-form-item>
              <el-button
                icon="el-icon-search"
                type="success"
                size="mini"
                @click="searchHistoryFunc"
                >搜索</el-button
              >
            </el-form>
          </div>
          <el-table
            ref="tableBar"
            class="m-small-table"
            v-loading="listLoading"
            :data="historyList"
            max-height="500px"
            border
            fit
            highlight-current-row
            style="width: 100%; height: auto"
          >
            <el-table-column
              label="#"
              type="index"
              align="center"
              width="60"
            ></el-table-column>
            <el-table-column
              v-for="(item, index) of fieldList"
              :key="index"
              :label="item.label"
              width="200"
            >
              <template slot-scope="scope">
                <span>{{ scope.row[item.key] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="page-container">
            <pagination
              :total="historyTotal"
              :page.sync="historyQuery.page"
              :limit.sync="historyQuery.limit"
              @pagination="getHistory"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="数据趋势" name="fourth">
          <el-date-picker
            class="fl ml10"
            style="width: 350px"
            @change="getZxt"
            v-model="zxtQuery.dateRange"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="mini"
          >
          </el-date-picker>
          <el-select
            @change="handleSelectChange"
            class="fl ml10"
            style="width: 200px"
            v-model="zxtQuery.disRespVos"
            multiple
            collapse-tags
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.type"
              :label="item.name"
              :value="`${item.type},${item.name}`"
            >
            </el-option>
          </el-select>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            class="search-right-btn fl"
            @click="getZxt"
            >搜索</el-button
          >
          <div class="clear"></div>

          <div
            v-if="showChart2"
            id="echart-bar2"
            style="height: 500px; margin-top: 16px"
          ></div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deviceDetailsDlg = false" icon="el-icon-back"
          >关闭</el-button
        >
        <!-- <el-button type="success" @click="subGiveAlarmDlg" icon="el-icon-check">
          <span>提交</span>
        </el-button> -->
      </div>
    </el-dialog>

    <el-dialog
      title="设备图纸"
      :close-on-click-modal="false"
      :visible.sync="dropDiaState"
      width="1024px"
      top="30px"
      append-to-body
    >
      <div style="width: 981px; overflow-x:hidden">
        <div class="dflex" style="position: relative;border: 1px solid #e8e8e8;">
        <div style="width: 980px;">
          <img
            style=" width: 980px;-webkit-user-drag: none;"
            :src="equipmentDrawing"
            alt=""
          />
        </div>
        <div class="drop-left flex1" :style="`height:100px; padding: 10px`">
          <!-- 相对定位 可移动的 -->
          <!-- :style="`left:${item.left + 100}px; top:${item.top}px`" -->
          <div class="drop-left-p">
            <img
              class="icon_cgq_right"
              style="position: absolute;width:40px;"
              v-for="(item, index) of dropDiaXYList"
              :key="index"
              :style="`left:${item.left - 7}px; top:${item.top - 5}px`"
              :data-index="index"
              name="sensor-p-item"
              :title="item.name"
              :src="`/static/image/${item.imgName || 'icon_sbxq'}.gif`"
              alt=""
            />
            <!-- <div
              class="icon_cgq_right success"
              v-for="(item,index) of dropDiaXYList"
              :key="index"
              :data-index="index"
              :style="`left:${item.left}px; top:${item.top}px`"
              name='sensor-p-item'
              :title="item.name"
              @mousedown="moveRightFunc($event, index)">
            </div> -->
          </div>
          <!-- 绝对定位 固定的 -->
          <div class="drop-left-a" style="display:none">
            <div
              v-for="(item, index) of dropDiaList"
              :key="index"
              :data-index="index"
              class="sensor-item sensor-a-item"
            >
              <div class="flex sensor-item-top">
                <!-- << 图片 -->
                <img
                  class="icon_cgq"
                  :src="`/static/image/${item.imgName || 'icon_cgq'}.png`"
                  alt=""
                />
                <!-- <div class="icon_cgq success"></div> -->
                <!-- >> 图片 -->

                <div
                  :class="
                    `flex1 not-select ${
                      item.isSelected ? 'fsuccess fbold' : ''
                    } `
                  "
                  style="line-height: 23px"
                >
                  {{ item.name }}
                </div>
                <i
                  v-if="item.iconState"
                  class="el-icon-back fdanger fr"
                  @click="backFunc(index)"
                  style="font-size: 22px"
                  title="撤销"
                ></i>
                <!-- {{positionX}}
                {{positionY}} -->
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dropDiaState = false" icon="el-icon-back"
          >关闭</el-button
        >
        <!-- <el-button type="success" @click="subGiveAlarmDlg" icon="el-icon-check">
          <span>提交</span>
        </el-button> -->
      </div>
    </el-dialog>

    <Usertree />
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import Cookie, { get } from "js-cookie";
import * as echarts from "echarts";
import Usertree from "@/components/Dialog/Usertree";
import Pagination from "@/components/Pagination";
import * as utils from "@/utils";
import moment, { localeData } from "moment"; //导入文件
import { findEquioPage } from "@/api/safetyMonitoringApi";
import { structureEnergyAnalysis } from "@/api/basicManSystem/energyMonitor.js";
import {
  arrId2Name, // 根据id 获取name
  getDataDictOther, // 数据字典
  isNull
} from "@/utils";
import { postAction, getAction } from "@/api";
import jsCookie from "js-cookie";
let dlgDataEmpty = {
  id: "",
  handlerId: "",
  handler: "",
  handlerRemark: "",
  confirmationTime: "",
  status :'',
  handlerHours:'',
  handlerResultStr:'',
  handlerResult:''
};
//折线图
let zxtQueryEmpty = {
  disRespVos: [],
  nodeId: "",
  startTime: "",
  endTime: "",
  dateRange: []
};
//报警记录
let alarmRecordQueryEmpty={
  page: 1,
  limit: 20,
  alertType: "",
  status: "0,1",
  nodeId: ""
};
export default {
  components: {
    Pagination,
    Usertree
  },
  props: {
    // listQuery: {
    //   type: Object,
    //   default: {}
    // },
    pageType: {
      type: String,
      default: ""
      // electricitySafety 用电安全
      // smokeDetector 烟感
    },
    boxState: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    boxState(val) {
      this.boxState0 = val;
    },
    // 用户tree
    userTreeUserId(nVal) {
      if (this.giveAlarmDlg) {
        this.dlgData.handlerId = nVal;

        //console.log(this.dlgData.chargeLeaderId + "22222222222222");
        // this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      }
    },
    userTreeUserName(nVal) {
      if (this.giveAlarmDlg) {
        this.dlgData.handler = nVal;
        // this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      }
    }
  },
  computed: {
    ...mapGetters([
      // 部门
      "bmTreeBranchId",
      "bmTreeBranchName"
    ]),
    // 部门选人相关
    userTreeState: {
      get: function() {
        let userTreeState = this.$store.getters.userTreeState;
        return userTreeState;
      },
      set: function(newVal) {
        // this.$store.commit('SET_USERTREESTATE', newVal)
      }
    },
    userTreeUserId: {
      get: function() {
        let userTreeUserId = this.$store.getters.userTreeUserId;
        return userTreeUserId;
      },
      set: function(newVal) {
        // this.$store.commit('SET_USERTREEUSERID', newVal)
      }
    },
    userTreeUserName: {
      get: function() {
        let userTreeUserName = this.$store.getters.userTreeUserName;
        return userTreeUserName;
      },
      set: function(newVal) {
        // this.$store.commit('SET_USERTREEUSERNAME', newVal)
      }
    }
  },
  data() {
    return {
      listQuery:{},
      isDis:false,
      t1Title: "",
      protocolType: "",
      /////////
      value: true,
      radio1: "全部",
      boxState0: true,

      echartPanel: null,
      formData: {},
      list: [
        // {
        //   id: 1,
        //   name: "萨达所大所多撒多撒多撒",
        //   text: "过载报警",
        //   bz: "45kp",
        //   fw: "45kp",
        //   qy: "1号楼"
        // },
      ],
      total: 0,
      deviceViewTotal: 0,
      listQuery2: {
        page: 1,
        limit: 20,
        state: "" // 传感器状态 0正常 1参数异常(报警异常) 2通讯异常-离线
      },
      list2: [],
      fanDiagram: [],
      listLoading: false,

      //报警详情
      giveAlarmDlg: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        handlerRemark: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        handler: [{ required: true, message: "必填字段", trigger: "change" }],
        confirmationTime: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        handlerResultStr: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        status :[
          { required: true, message: "必填字段", trigger: "change" }
        ],
        handlerHours:[
          { required: true, message: "必填字段", trigger: "change" }
        ],
      },
      //报警记录
      alarmRecordDlg: false,
      alarmRecordRow: {},
      alarmRecordList: [],
      alarmRecordQuery:JSON.parse(JSON.stringify(alarmRecordQueryEmpty)),
       
      handleStatusSelect: [
        { id: "0", name: "待处理" },
        { id: "1", name: "处理中" },
        { id: "2", name: "已完成" }
      ],
      alertSelect: [
        { id: "a1001", name: "水压高" },
        { id: "a1002", name: "水压低" },
        { id: "a1003", name: "水位高" },
        { id: "a1004", name: "水位低" },
        { id: "a1005", name: "温度高" },
        // { id: "a1006", name: "温度低" },
        { id: "a1007", name: "湿度高" },
        { id: "a1008", name: "湿度低" },
        // { id: "d", name: "光照高" },
        // { id: "a1010", name: "光照低" },
        { id: "a1011", name: "过压" },
        { id: "a1012", name: "欠压" },
        // { id: "a1013", name: "电量低" },
        { id: "a1014", name: "人体活动" },
        // { id: "a1015", name: "长时间未上报" },
        { id: "a1016", name: "烟雾报警" },
        { id: "a1017", name: "三相电压不平衡" },
        { id: "a1018", name: "三相电流不平衡" },
        { id: "a1019", name: "高负载" },
        // { id: "a1020", name: "低负载" },
        { id: "a1021", name: "漏水报警" },
        { id: "a1022", name: "可燃气体报警" },
        { id: "a1023", name: "环境温度高" },
        { id: "a1024", name: "环境温度低" },
        { id: "a1025", name: "线缆温度高" },
        // { id: "a1026", name: "线缆温度低" },
        { id: "a1027", name: "有功功率高" },
        { id: "a1028", name: "有功功率低" },
        // { id: "a1029", name: "无功功率高" },
        // { id: "a1030", name: "无功功率低" },
        //{ id: "a1031", name: "视在功率高" },
        // { id: "a1032", name: "视在功率低" },
        { id: "a1033", name: "井盖打开" },
        { id: "a1034", name: "漏电电流高" },
        // { id: "a1035", name: "漏电电流低" },
        { id: "a1036", name: "差压高" },
        { id: "a1037", name: "差压低" },
        // { id: "a1038", name: "可燃气体浓度高" },
        // { id: "a1039", name: "可燃气体浓度低" },
        { id: "a1040", name: "短路报警" },
        { id: "a1041", name: "浪涌报警" },
        { id: "a1042", name: "缺项报警" },
        { id: "a1043", name: "打火报警" },
        { id: "a1044", name: "掉电报警" },
        { id: "a1045", name: "离线报警" },
        {id: "109",name: "烟火监测报警"},
        {id: "110",name: "烟雾监测报警"},
        {id: "a1048",name: "瞬时流量高"},
        {id: "a1049",name: "瞬时流量低"},
        // {id: "18",name: "移动侦测报警"},
        // {id: "20",name: "门铃按键报警"},
        // {id: "38",name: "烟感检测报警"},
        // {id: "39",name: "离岗检测报警"},
        // {id: "40",name: "婴儿哭声检测报警"},
        // {id: "41",name: "人形检测报警"},
      ], //报警类型
      deviceTypeList: [], //设备类型
      processingStateList: [], //处理状态
      alarmRecordTotal: 0,
      returnList: [],
      gjSelect: [],

      //设备详情
      equipmentDrawing: "",
      equipmentDrawingList: [],
      deviceDetailsDlg: false,
      deviceDetailsQuery: {
        id: "",
        activeName: "first"
      },
      alarmConfigurationList: [], //报警配置
      historyQuery: {
        page: 1,
        limit: 20,
        dateRange: [],
        beginTime: "",
        endTime: "",
        id: ""
      }, //历史记录
      historyTotal: 0,
      historyList: [],
      fieldList: [], //表头

      userInfo: {},

      deviceViewRow: {},
      giveAlarmRow: {}, //报警详情/报警信息
      alarmDetailData: {
        pojo: {}
      },

      dropDiaXYList: [], // 坐标
      imgHeight: "",
      imgWidth: "",
      iconScale1: 1,

      dropDiaList: [
        { id: 1, name: "用电" },
        { id: 2, name: "环境参数" },
        { id: 23, name: "环境参数23" }
      ], // 传感器列表
      sbImgH: "",
      //查看大图dlg
      dropDiaState: false,
      //趋势
      zxtQuery: JSON.parse(JSON.stringify(zxtQueryEmpty)),
      options: [],
      bjDiaRow: {},
      showChart2: false,
      echartRoom2: null,
      zxtSelect: []
    };
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    // this.getList();
    this.componentInitFunc();
    getDataDictOther(this, "gaojingchulijieguo", "gjSelect"); // 报警处理结果
    console.log(this.pageType, "pageType");
    console.log(this.userInfo, "this.userInfo");
  },

  mounted() {
    window.addEventListener("resize", () => {
      if (!utils.isNull(this.echartPanel)) {
        this.echartPanel.resize();
      }
    });
  },

  methods: {
    radioChange(){
      this.dlgData.handlerId=''
      this.dlgData.handler=''
      this.dlgData.handlerRemark=''
      this.dlgData.confirmationTime=''
      this.dlgData.handlerHours=''
      this.dlgData.handlerResultStr = ''
      this.dlgData.handlerResult = ''
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },
    // 选人弹窗
    showUserTree() {
      this.$store.commit("SET_USERTREESQTYPE", "");
      this.$store.commit("SET_USERTREESTATE", true);
    },
    gjSelectProductType(data) {
      let { value, label } = data;
      this.dlgData.handlerResultStr = label;
      this.dlgData.handlerResult = value;
    },
    componentInitFunc() {
      if (this.pageType == "electricitySafety") {
        this.t1Title = "安全用电设备统计";
      } else if (this.pageType == "smokeDetector") {
        this.t1Title = "烟感器设备统计";
      } else if (this.pageType == "gas") {
        this.t1Title = "可燃气体设备统计";
      } else if (this.pageType == "waterImmersion") {
        this.t1Title = "智能水浸设备统计";
      } else if (this.pageType == "temperatureHumidityEquipment") {
        this.t1Title = "温湿度设备统计";
      } else if (this.pageType == "airSwitchEquipment") {
        this.t1Title = "空开设备统计";
      }
    },
    ////////////
    // 创建图表
    createChart() {
      //   if (utils.isNull(this.formData.thisMonth)) {
      if (!utils.isNull(this.echartPanel)) {
        this.echartPanel.clear();
      }
      // return
      //   }
      let legendData = [`报警设备`, `离线设备`, `正常设备`];
      let seriesData = [];
      seriesData = [
        {
          name: "报警设备",
          value: this.fanDiagram.baojing
        },
        {
          name: "离线设备",
          value: this.fanDiagram.lixian
        },
        {
          name: "正常设备",
          value: this.fanDiagram.zhengchang
        }
      ];
      // seriesData.push({
      //   name: "用水",
      //   value: 1
      // });
      // seriesData.push({
      //   name: "用电",
      //   value: 2
      // });
      // seriesData.push({
      //   name: "用气",
      //   value: 3
      // });
      // 基于准备好的dom，初始化echarts实例
      this.echartPanel = echarts.init(document.getElementById("echart-panel"));
      // 指定图表的配置项和数据
      let option = {
        color: ["#ff7272", "#7ef1c0", "#4570f6"],
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)"
        },
        legend: {
          orient: "vertical",
          right: "10%",
          top: "38%",
          data: legendData,
          textStyle: {
            color: "#249CF9"
          }
        },

        series: [
          {
            name: "",
            type: "pie",
            center: ["40%", "50%"],
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            // label: {
            //   show: true,
            //   formatter: "{d}%"
            // },
            // emphasis: {
            //   label: {
            //     show: true,
            //     fontSize: "14"
            //   }
            // },
            data: seriesData
            // labelLine: {
            //   show: false
            // },
            // label : {
            //   show : false
            // },
          }
        ]
      };
      console.log(option, "option");
      this.echartPanel.setOption(option);
    },

    getList(listQuery) {
      if (this.pageType == "electricitySafety") {
        this.protocolType = "1033";
      } else if (this.pageType == "smokeDetector") {
        this.protocolType = "1037,0009";
      } else if (this.pageType == "gas") {
        this.protocolType = "1032,1013";
      } else if (this.pageType == "waterImmersion") {
        this.protocolType = "1018,1034";
      } else if (this.pageType == "temperatureHumidityEquipment") {
        this.protocolType = "1038";
      } else if (this.pageType == "airSwitchEquipment") {
        this.protocolType = "aaaa";
      }
      this.listQuery=listQuery
      this.total=0
      this.getList1();
      this.getList2();
      this.getFanDiagram(); //设备统计
    },

    getList1() {
      let sendObj = {
        label: this.listQuery.label,
        limit: 99999,
        page: this.listQuery2.page,
        projectId: this.userInfo.projectId,
        startDate: this.listQuery.startDate,
        endDate: this.listQuery.endDate,

        protocolType: this.protocolType, // 协议类型 4位值，对应不同厂家的设备解析
        // * protocolType
        // * 用电安全  1033曼顿火灾
        // * 烟感      1037星纵烟感  0009烟雾报警器
        // * 燃气   1032博高可燃气体  1013诺顿可燃气体
        // * 水浸    1018博高水浸  1034星纵水浸
        state: this.listQuery2.state, // 传感器状态 0正常 1参数异常(报警异常) 2通讯异常-离线

        status: "", // 运行状态 0:正常 1:异常 2停止
        areaId: "",
        equipRoom: this.listQuery.equipRoom,
        equipType:2
      };

      this.list = [];
      postAction("/iot/findAbnormalAlarmList", sendObj).then(res1 => {
        let res = res1.data;
        if (res.code == "200") {
          this.list = res.data;
          this.total = res.dataMap.count;
        } else {
          // this.$message({
          //   type: "warning",
          //   message: res.msg
          // });
        }
      });
    },

    getList2() {
      console.log("===this.listQuery", this.listQuery);
      let sendObj = {
        label: this.listQuery.label,
        limit: this.listQuery2.limit,
        page: this.listQuery2.page,
        projectId: this.userInfo.projectId,
        startDate: this.listQuery.startDate,
        endDate: this.listQuery.endDate,

        protocolType: this.protocolType, // 协议类型 4位值，对应不同厂家的设备解析
        // * protocolType
        // * 用电安全  1033曼顿火灾
        // * 烟感      1037星纵烟感  0009烟雾报警器
        // * 燃气   1032博高可燃气体  1013诺顿可燃气体
        // * 水浸    1018博高水浸  1034星纵水浸
        state: this.listQuery2.state, // 传感器状态 0正常 1参数异常(报警异常) 2通讯异常-离线

        status: "", // 运行状态 0:正常 1:异常 2停止
        areaId: "",
        equipRoom: this.listQuery.equipRoom,
        equipType:2
      };
      this.list2 = [];
      postAction("/iot/findEquipInfo", sendObj).then(res1 => {
        let res = res1.data;
        if (res.code == "200") {
          let list2 = res.data;
          for (let i of list2) {
            if (i.protocolType === "1017" || i.protocolType === "1033") {
              let nodeDescs = {};
              for (let k of i.nodeMonitorConfigs) {
                nodeDescs[k.dataType] = k.currentValue + k.unit;
                nodeDescs[k.dataType + "State"] = k.status;
              }
              i.nodeMonitorConfigs2 = nodeDescs;
            }
          }
          this.deviceViewTotal = res.page.total;
          this.list2 = list2;
        } else {
          this.deviceViewTotal = 0;
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    //设备统计扇形图
    getFanDiagram() {
      let sendObj = {
        label: this.listQuery.label,
        limit: this.listQuery2.limit,
        page: this.listQuery2.page,
        projectId: this.userInfo.projectId,
        startDate: this.listQuery.startDate,
        endDate: this.listQuery.endDate,

        protocolType: this.protocolType, // 协议类型 4位值，对应不同厂家的设备解析
        // * protocolType
        // * 用电安全  1033曼顿火灾
        // * 烟感      1037星纵烟感  0009烟雾报警器
        // * 燃气   1032博高可燃气体  1013诺顿可燃气体
        // * 水浸    1018博高水浸  1034星纵水浸
        state: this.listQuery2.state, // 传感器状态 0正常 1参数异常(报警异常) 2通讯异常-离线

        status: "", // 运行状态 0:正常 1:异常 2停止
        areaId: "",
        equipRoom: this.listQuery.equipRoom,
        equipType:2
      };
      this.fanDiagram = [];
      postAction("/iot/findEquipNodeCount", sendObj).then(res1 => {
        let res = res1.data;
        if (res.code == "200") {
          this.fanDiagram = res.data;
          this.createChart();
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    getInfo1(id) {
      getAction(`/iot/findEquipNodeById/${id}`).then(res => {
        if (res.data.code == 200) {
          this.alarmDetailData = res.data.data;
          console.log(this.alarmDetailData, "this.alarmDetailData");
          this.getEquipmentDrawing(this.alarmDetailData.pojo.equipRoomId);
         
        } else {
          this.$message({
            type: "warning",
            message: res.data.msg
          });
        }
      });
    },

    //报警详情
    showGiveAlarmDlg(row) {
      if (row.status!=1&&row.status!=2) {
        this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
        this.isDis=false
      }else if(row.status==1){
        this.dlgData.status=row.status
        this.dlgData.handlerHours=row.handlerHours
        this.isDis=true
      }else{
        this.isDis=false
        this.dlgData.status=row.status
        this.dlgData.handlerHours=0
      }
      this.getInfo1(row.nodeId);
      this.giveAlarmRow = row;
    },
    getEquipmentDrawing(id) {
      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      this.equipmentDrawing = "";
      this.equipmentDrawingList = [];
      getAction(`/iot/findListByEquipId/${id}`).then(res1 => {
        loading.close()
        let res = res1.data;
        if (res.code == 200) {
          let dropDiaList = res.list;
          if (res.data.imgUrl != null && res.data.imgUrl != "") {
            this.equipmentDrawing = res.data.imgUrl;
            this.equipmentDrawingList = res.data.imgUrl.split(",");

            // 坐标 - 并将坐标渲染到画布上
            if (res.data.coordinate == null || res.data.coordinate == "") {
              this.dropDiaXYList = [];
            } else {
              let arr = [];
              let coordinate = JSON.parse(res.data.coordinate);
              let obj = coordinate.find(obj => {
                return obj.id == this.giveAlarmRow.nodeId;
              });
              if (obj != undefined) {
                arr.push(obj);
              } else {
                arr = [];
              }
              // console.log(obj,"obj");
              let dropDiaXYList = (this.dropDiaXYList = arr);
              // this.dropDiaXYList = JSON.parse(JSON.stringify(dropDiaXYList))
            }
            console.log(this.dropDiaXYList, "this.dropDiaXYList");
          } else {
            this.equipmentDrawing = "";
            this.equipmentDrawingList = [];
          }
          let img = new Image();
          img.src = this.equipmentDrawing;
          img.onload = async () => {
            console.log(400 / img.width);
            console.log(202 / img.height);

            this.imgWidth = 400 / img.width;
            this.imgHeight = 202 / img.height;
            if (img.width / img.height > 400 / 202) {
              this.imgWidth = 400;
              this.imgHeight = (img.height * 400) / img.width;
            } else {
              this.imgWidth = (img.width * 202) / img.height;
              this.imgHeight = 202;
            }
            this.iconScale1 = this.imgWidth / 980;
          };


          // this.imgWidth = 400 / img.width;
          // this.imgHeight = 202 / img.height;
          this.dropDiaList = JSON.parse(JSON.stringify(dropDiaList));
          // 图片高度
          setTimeout(() => {
            this.sbImgH = $(".drop-right-img").height() + 4;
          }, 500);

          this.giveAlarmDlg = true;
          this.$nextTick(() => {
            this.$refs["dlgForm"].clearValidate();
          });

          

        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    //查看大图
    viewLargerImg() {
      this.dropDiaState = true;
    },
    subGiveAlarmDlg() {
      this.$refs["dlgForm"].validate(valid => {
        if (valid) {
          if (this.giveAlarmRow.status==1&&this.dlgData.status!=2) {
            this.$message.error("处理中报警不允许重复提交!");
             return false
          }
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));
          sendObj.id = this.giveAlarmRow.id;
          if (sendObj.status=='1') {
            sendObj.handlerId=0
          }
          if (this.giveAlarmRow.status=='1') {
            sendObj.handlerHours = this.giveAlarmRow.handlerHours;
          }
          // console.log(sendObj,"sendObj");
          // return
          let loading = this.$loading({
            lock: true,
            text: '提交中',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          postAction("/iot/aas/updateStatus/v2", sendObj).then(res => {
            loading.close()
            if (res.data.code == 200) {
              if (this.giveAlarmDlg && !this.alarmRecordDlg) {
                this.giveAlarmDlg = false;
                this.getList();
              }
              if (this.alarmRecordDlg) {
                this.giveAlarmDlg = false;
                this.getAlarmRecord();
                this.getList();
              }
              this.$message.success(res.data.msg);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    //报警记录
    searchAlarmRecord() {
      this.alarmRecordQuery.page = 1;
      this.getAlarmRecord();
    },
    showAlarmRecord(row) {
      this.alarmRecordQuery=JSON.parse(JSON.stringify(alarmRecordQueryEmpty)) 
      this.alarmRecordDlg = true;
      this.alarmRecordRow = row;
      this.getAlarmRecord();
    },
    getAlarmRecord() {
      this.alarmRecordList=[]
      let sendObj = JSON.parse(JSON.stringify(this.alarmRecordQuery));
      sendObj.nodeId = this.alarmRecordRow.id;
      postAction("/iot/findAbnormalAlarmPage", sendObj).then(res1 => {
        let res = res1.data;
        if (res.code == 200) {
          this.alarmRecordList = res.list;
          this.alarmRecordTotal = res.data.total;
          // 设置表格勾选
          // this.$nextTick(() => {
          //   for (let item of this.alarmRecordList) {
          //     let isHas = this.returnList.some(row => {
          //       // if (row.id == item.id) {
          //       // }
          //       return row.id == item.id;
          //     });
          //     if (isHas) {
          //       this.$refs.multipleTable.toggleRowSelection(item, true);
          //     } else {
          //       this.$refs.multipleTable.toggleRowSelection(item, false);
          //     }
          //   }
          // });
          console.log(res);
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // alarmRecordListDataChange(val, row) {
    //   this.tableCheckBaseFunc(row);
    // },
    // 设置选中行
    // toggleRowSelection() {
    //   let idList = [];
    //   for (let i of this.returnList) {
    //     idList.push(i["id"]);
    //   }
    //   this.$nextTick(() => {
    //     this.$refs.multipleTable.clearSelection();
    //     this.list.forEach(item => {
    //       for (let i in idList) {
    //         if (idList[i] == item.id) {
    //           this.$refs.multipleTable.toggleRowSelection(item, true);
    //         }
    //       }
    //     });
    //   });
    // },
    // alarmRecordClick(row) {
    //   console.log(row);
    // },
    // 单行操作方法
    // tableCheckBaseFunc(row) {
    //   let isCheck = !this.returnList.some(item => item.id == row.id); // true-勾选状态，false-取下选择状态
    //   console.log("isCheck", isCheck);
    //   // 判断是否是勾选状态
    //   if (isCheck) {
    //     // 勾选
    //     this.returnList.push(row);
    //   } else {
    //     // 取消选择
    //     let returnList = this.returnList.filter(item => {
    //       return item.id != row.id;
    //     });
    //     this.returnList = JSON.parse(JSON.stringify(returnList));
    //   }
    // },
    //批量处理
    batchProcessing() {
      if (this.returnList.length <= 0) {
        this.$message({
          type: "warning",
          message: "请选择记录"
        });
        return false;
      }
      console.log(this.returnList, "returnList");
    },
    //导出报警信息
    exportingAlarmInformation() {},
    //设备详情
    showDeviceDetailsDlg(row) {
      this.deviceViewRow = row;
      console.log(row);
      this.getInfo2(row.id);
      this.getHistory();
    },
    getInfo2(id) {
      this.alarmConfigurationList = [];
      getAction(`/iot/findNodeConfigs/${id}`).then(res1 => {
        let res = res1.data;
        console.log(res);
        if (res.code == 200) {
          this.deviceDetailsDlg = true;
          this.alarmConfigurationList = res.list;
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    handleClick(tab, event) {
      console.log(tab, event);
      this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
      this.zxtSelect = [];
      if (tab.name == "fourth") {
        const now = moment();
        const start = moment(now).subtract(3, "hours");
        const end = moment().format("YYYY-MM-DD HH:mm");
        let startTime = moment(start).format("YYYY-MM-DD HH:mm");
        // let endTime = moment(end).format('YYYY-MM-DD HH:mm');
        this.zxtQuery.dateRange = [startTime, end];
        this.getDxList();
        this.getZxt();
      }
      // this.getDeviceDetails();
    },
    getDxList() {
      getAction(`/iot/trend/nodeDataDis/${this.deviceViewRow.id}`).then(
        res1 => {
          let res = res1.data;
          if (res.code == 200) {
            this.options = res.data;
          } else {
            this.$message.error(res.msg);
          }
        }
      );
    },
    handleSelectChange() {
      this.zxtSelect = [];
      this.zxtQuery.disRespVos.forEach(element => {
        console.log(element, "element");
        let [type, name] = element.split(",");
        this.zxtSelect.push({ type, name });
      });
    },
    getZxt() {
      this.showChart2 = false;
      if (
        isNull(this.zxtQuery.dateRange) ||
        this.zxtQuery.dateRange.length <= 0
      ) {
        this.$message.warning("请先选择起止时间");
        return false;
      }
      let sendObj = JSON.parse(JSON.stringify(this.zxtQuery));
      // 日期范围
      sendObj.startTime = "";
      sendObj.endTime = "";
      if (
        !isNull(this.zxtQuery.dateRange) &&
        this.zxtQuery.dateRange.length > 0
      ) {
        sendObj.startTime = this.zxtQuery.dateRange[0];
        sendObj.endTime = this.zxtQuery.dateRange[1];
      }
      sendObj.disRespVos = this.zxtSelect;
      sendObj.nodeId = this.deviceViewRow.id;
      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      postAction("/iot/trend/nodeDataTrend", sendObj).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == 200) {
          if (!utils.isNull(res.data) && res.data.list.length > 0) {
            this.showChart2 = true;
            // this.list = res.data;
            setTimeout(() => {
              this.setEchartBar2(res.data.list, res.data.times);
              //   this.createRoom(res.data.list)
            }, 100);
          }
        }
      });
    },
    //创建折线图
    setEchartBar2(arr, dataMap) {
      console.log(arr, "arr");
      if (this.showChart2 == false) {
        // if (!utils.isNull(arr)) {
        //   this.echartRoom2.clear();
        // }
        return;
      }
      // << 本月1号到当天
      let xList = [];
      let xList0 = [];
      // let dateObj = new Date();
      // console.log("dateObj.getDate()", dateObj.getDate());
      // console.log(this.getEveryDayDateByBetweenDate(this.listQuery.dateRange[0],this.listQuery.dateRange[1]),'时间间隔');
      // let dayNum = parseInt(dateObj.getDate());
      // let month = utils.return2Num2(dateObj.getMonth() + 1);
      // let year = dateObj.getFullYear();
      // for (let i = 0; i < dayNum; i++) {
      //   let key = `${year}-${month}-${utils.return2Num2(i + 1)}`;
      //   xList.push(key);
      //   xList0.push(`${year}-${month}-${utils.return2Num2(i + 1)}`);
      // }

      // 拼接数据
      let data = [];
      let listData = [];
      for (let index = 0; index < dataMap.length; index++) {
        let obj = {
          yearMonthDate: dataMap[index],
          count: 0,
          type: ""
        };
        listData.push(obj);
      }
      for (let i = 0; i < arr.length; i++) {
        let itemLine = arr[i];
        let lineObj = {
          name: itemLine.name,
          type: "line",
          stack: "",
          data: []
        };
        let map = itemLine.list;
        // console.log('111111111map', map)
        // console.log('111111111listData', listData)
        for (let key = 0; key < map.length; key++) {
          for (let k = 0; k < listData.length; k++) {
            if (map[key].time == listData[k].yearMonthDate) {
              lineObj.data.push(map[key].value);
              // listData[k].value = map[key].value;
              // listData[k].type = map[key].type;
            }
          }
        }
        data.push(lineObj);
        // let arrData = [];
        // console.log("==listData", listData);
        // for (let o = 0; o < listData.length; o++) {
        //   arrData.push(listData[o].value);
        // }
        // console.log(arrData, "arrData");
        // lineObj.data = arrData;
        // data.push(lineObj);
      }
      console.log(data, "data--------------");
      // xList0.push(map[key].yearMonthDate)
      xList0 = dataMap;
      // 绘制图标
      var myChart = echarts.init(document.getElementById("echart-bar2"));
      var option = {
        title: {
          text: ""
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },

        legend: {
          left: 10
        },
        grid: {
          left: "2%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false, // true-刻度中间 false-刻度线上
          data: xList0
        },
        yAxis: {
          type: "value"
          // name: '单位（吨）',
          // nameTextStyle: {
          //   color: '#aaa',
          //   nameLocation: 'start',
          // },
        },
        series: data
        // series: [[1,2,3],[12,22,32],[13,23,33]]
      };
      myChart.clear();
      myChart.setOption(option);
      myChart.on("click", param => {
        // console.log('param', param)
        // // componentIndex
        // // dataIndex
        // let msg = `${this.echartLineData[param.componentIndex].name}：${
        //   this.echartLineData[param.componentIndex].data[param.dataIndex]
        // }`
        // alert(msg)
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    cloDeviceDetailsDlg() {
      console.log(
        this.deviceDetailsQuery.activeName,
        "this.deviceDetailsQuery.activeName"
      );
      this.deviceDetailsQuery.activeName = "first";
    },
    //获取历史记录
    getHistory() {
      this.fieldList = [];
      this.historyList = [];
      let sendObj = JSON.parse(JSON.stringify(this.historyQuery));
      // 日期范围
      sendObj.beginTime = "";
      sendObj.endTime = "";
      sendObj.id = this.deviceViewRow.id;
      if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
        sendObj.beginTime = sendObj.dateRange[0];
        sendObj.endTime = sendObj.dateRange[1];
      }
      this.listLoading = true;
      postAction("/iot/protocolLoran", sendObj).then(res1 => {
        this.listLoading = false;
        let res = res1.data;
        if (res.code == 200) {
          for (let key in res.data.field) {
            let label = res.data.field[key];
            this.fieldList.push({
              key,
              label
            });
          }
          this.historyList = res.list;
          this.historyTotal = res.data.total;
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    searchHistoryFunc() {
      this.historyQuery.page = 1;
      this.getHistory();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.table-container {
  margin-top: 10px;
}

.card2_1 {
  /deep/.el-card__body {
    height: 270px;
    overflow-y: auto;
  }
}

.card2_2 {
  /deep/.el-card__body {
    height: 580px;
    overflow-y: auto;
  }
}
.drop-right-img {
  // height: 100%;
  width: 400px;
  height: 202px;
  -webkit-user-drag: none;
}
.oneBtn {
  position: absolute;
  right: 20px;
  top: 22px;
}

.giveAlarmDlgCol {
  border: 1px solid #eff6ff;
  height: 260px;
  padding: 15px;
  background-color: #f8faff;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.towBtn {
  position: absolute;
  right: 20px;
  top: 0;
}

.alarmMessageListBox {
  display: flex;
  justify-content: space-between;
  font-weight: bolder;
  background-color: #fff7f6;
  margin-bottom: 10px;
  padding: 6px;
}

.deviceViewBox {
  // font-weight: bolder;
  // margin-bottom: 10px;
  padding: 10px;
  border-bottom: 1px solid #f1efef;
  // position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card1 {
  /deep/.el-card__body {
    height: calc(100% - 53px);
    overflow-y: auto;
  }
}

.echart {
  height: 200px;
}

////////////////
.list-yuan-btn {
  height: 28px;
  line-height: 28px;

  padding: 0 10px;
  border-radius: 14px;
  margin-right: 10px;
}

.bg-red {
  background: #ff0900 !important;
  color: white !important;
  box-shadow: 0 4px 10px #ffc5c3 !important;
}

.bg-yellow {
  background: #fef6e2;
  color: #e5ab35;
}

.bg-blue {
  background: #ecf5ff;
  color: #5eadfe;
}

.bg-green {
  background: #e2f9ee;
  color: #69dfb0;
}

//
.item-bg-red {
  background: linear-gradient(to right, #fff2f1, #fff, #fff, #fff, #fff);
}

//
.ml16 {
  margin-right: 16px;
}
.drop-left-p {
  position: absolute;
  z-index: 1;
}
.icon_cgq_right {
  width: 20px;
}
.icon_cgq_right.success {
  animation-name: animation_success; /* 执行动画名称 */
  background-color: #e1f3d8; /* 背景颜色 */
}
.icon_cgq_right.danger {
  animation-name: animation_danger; /* 执行动画名称 */
  background-color: #fde2e2; /* 背景颜色 */
}
.icon_cgq_right.warning {
  animation-name: animation_warning; /* 执行动画名称 */
  background-color: #faecd8; /* 背景颜色 */
}
// #dqhz .el-card__body {
//   padding: 0 !important;
// }
/deep/.el-card__body {
  padding: 0;
  // padding-left: 20px;
}
</style>
