import request from "@/utils/request";
import { requestExcel } from "@/utils";
// 物业收费日结表
export function getDayInfo(data) {
  return request({
    url: `/unity/payBillDayRecord/getDayInfo`,
    method: "post",
    data
  });
}
// 物业收费汇总表
export function getSumInfo(data) {
  return request({
    url: `/unity/payBillDayRecord/getSumInfo`,
    method: "post",
    data
  });
}

// 业主统计
export function formMember(data) {
  return request({
    url: `/unity/form/member`,
    method: "post",
    data
  });
}

// 投诉咨询统计
export function formComplaint(data) {
  return request({
    url: `/unity/form/complaint`,
    method: "post",
    data
  });
}

// 报修统计
export function formRepair(data) {
  return request({
    url: `/unity/form/repair`,
    method: "post",
    data
  });
}

// 广告位统计
export function formAdvertising(data) {
  return request({
    url: `/unity/form/advertising`,
    method: "post",
    data
  });
}

// 广告位统计详情
export function formAdvertisingInfo(data) {
  return request({
    url: `/unity/form/advertising/info`,
    method: "post",
    data
  });
}

// 数据大屏
export function formScreen() {
  return request({
    url: `/unity/form/screen`,
    method: "get"
  });
}

// 缴费汇总表
export function jiaofeihuizong(data) {
  return request({
    url: `/unity/payfeebill/jiaofeihuizong`,
    method: "post",
    data
  });
}

// 缴费明细表
export function jiaofeimingxi(data) {
  return request({
    url: `/unity/payfeebill/jiaofeimingxi`,
    method: "post",
    data
  });
}

// 欠费统计表/欠费信息
export function qianfeitongji(data) {
  return request({
    url: `/unity/payfeebill/qianfeitongji`,
    method: "post",
    data
  });
}

// 报事统计

// << 报事设置 - 报事区域
// 报事区域树
export function findAreaTree(projectId) {
  return request({
    url: `/report/area/findAreaTree/${projectId}`,
    method: "get"
  });
}
// 保存报事区域
export function reportAreaSave(data) {
  return request({
    url: `/report/area/save`,
    method: "post",
    data
  });
}
// 删除
export function areaUpdateIsDel(data) {
  return request({
    url: `/report/area/updateIsDel/${data.isDel}/${data.id}`,
    method: "get"
  });
}
// 移动
export function areaUpdateAreaSortNum(data) {
  return request({
    url: `/report/area/updateAreaSortNum`,
    method: "post",
    data
  });
}

// << 新 报事科目模板
// 树
export function subjectTemplateFindSubjectTree() {
  return request({
    url: `/report/subjectTemplate/findSubjectTree`,
    method: "get"
  });
}
// 保存
export function subjectTemplateSubjectSave(data) {
  return request({
    url: `/report/subjectTemplate/save`,
    method: "post",
    data
  });
}
// 删除
export function subjectTemplateUpdateIsDel(data) {
  return request({
    url: `/report/subjectTemplate/updateIsDel/${data.isDel}/${data.id}`,
    method: "get"
  });
}
// 移动
export function subjectTemplateUpdateSubjectSortNum(data) {
  return request({
    url: `/report/subjectTemplate/updateSubjectSortNum`,
    method: "post",
    data
  });
}
// 保存多条报事科目
export function subjectSaveList(data) {
  return request({
    url: `/report/subject/saveList`,
    method: "post",
    data
  });
}
// >> 新 报事科目模板

// << 2 报事科目
// 查询树
export function findSubjectTree(projectId) {
  return request({
    url: `/report/subject/findSubjectTree/${projectId}`,
    method: "get"
  });
}
export function subjectFindSubjectTree(projectId) {
  return request({
    url: `/report/subject/findSubjectTree/${projectId}`,
    method: "get"
  });
}
// 保存科目
export function reportSubjectSave(data) {
  return request({
    url: `/report/subject/save`,
    method: "post",
    data
  });
}
// 删除
export function subjectUpdateIsDel(data) {
  return request({
    url: `/report/subject/updateIsDel/${data.isDel}/${data.id}`,
    method: "get"
  });
}
// 移动
export function subjectUpdateSubjectSortNum(data) {
  return request({
    url: `/report/subject/updateSubjectSortNum`,
    method: "post",
    data
  });
}

// << 3 派工设置
// 查询
export function findProjectPostPage(data) {
  return request({
    url: `/org/findProjectPostPage`,
    method: "post",
    data
  });
}
// 查询所有的
findProjectPostList;
export function findProjectPostList(projectId) {
  return request({
    url: `/org/findProjectPostList/${projectId}`,
    method: "get"
  });
}
// 保存
export function saveProjectPost(data) {
  return request({
    url: `/org/saveProjectPost`,
    method: "post",
    data
  });
}
// 删除
export function updateIsDel(data) {
  return request({
    url: `/org/updateIsDel/${data.projectId}/${data.postId}`,
    method: "get"
  });
}
// << 4 工单步骤
// 查询
export function orderConfigFindConfigByProjectId(projectId) {
  return request({
    url: `/report/orderConfig/findConfigByProjectId/${projectId}`,
    method: "get"
  });
}
// 保存
export function orderConfigSave(data) {
  return request({
    url: `/report/orderConfig/save`,
    method: "post",
    data
  });
}

///////// << 报事管理
// 获取权限 项目列表
export function spusPage(data) {
  return request({
    url: `/org/spusPage`,
    method: "post",
    data
  });
}
// 模板下拉框
export function findProjectIdsByArea() {
  return request({
    url: `/report/orderConfig/findProjectIdsByArea`,
    method: "get"
  });
}
// 冒泡
export function orderFindNumsByUserId() {
  return request({
    url: `/report/order/findNumsByUserId`,
    method: "get"
  });
}
// 列表
export function findOrderListPage(data) {
  return request({
    url: `/report/order/findOrderListPage`,
    method: "post",
    data
  });
}

// 已归档列表
export function findOrderListPageYiGuiDang(data) {
  return request({
    url: `/report/order/findOrderListPageYiGuiDang`,
    method: "post",
    data
  });
}

// 已归档列表导出
export function orderListYiGuiDangExport(data) {
  return request({
    url: `/report/order/orderListYiGuiDangExport`,
    method: "post",
    data
  });
}

// 验证审核权限  projectId  orderId
export function checkIsAuditButten(data) {
  return request({
    url: `/report/order/checkIsAuditButten/${data.projectId}/${data.orderId}`,
    method: "get"
  });
}
// 详情
export function orderFindOrderById(id) {
  return request({
    url: `/report/order/findOrderById/${id}`,
    method: "get"
  });
}
// << 1 报事登记
export function baoShiDengJi(data) {
  return request({
    url: `/report/order/baoShiDengJi`,
    method: "post",
    data
  });
}

// 3 报事审核
export function baoshishenhe(data) {
  return request({
    url: `/report/order/baoshishenhe`,
    method: "post",
    data
  });
}
// 4 派工
export function baoshipaigong(data) {
  return request({
    url: `/report/order/baoshipaigong`,
    method: "post",
    data
  });
}
// 5 报事回访
export function baoshihuifang(data) {
  return request({
    url: `/report/order/baoshihuifang`,
    method: "post",
    data
  });
}
// 6 报事归档
export function baoshiguidang(data) {
  return request({
    url: `/report/order/baoshiguidang`,
    method: "post",
    data
  });
}
// 7 改派
export function baoshigaipai(data) {
  return request({
    url: `/report/order/baoshigaipai`,
    method: "post",
    data
  });
}
// 8 跟进
export function baoshigenjin(data) {
  return request({
    url: `/report/order/baoshigenjin`,
    method: "post",
    data
  });
}
// 9 关注
export function baoshiguanzhu(data) {
  return request({
    url: `/report/order/baoshiguanzhu`,
    method: "post",
    data
  });
}
// 10 作废
export function baoshizuofei(data) {
  return request({
    url: `/report/order/baoshizuofei`,
    method: "post",
    data
  });
}

export function yewuliuzhuan(data) {
  return request({
    url: `/report/order/yewuliuzhuan`,
    method: "post",
    data
  });
}

// 点位改派
export function modifyArea(data) {
  return request({
    url: `/report/order/modifyArea`,
    method: "post",
    data
  });
}

// 导入
export function importRareaExcel(data) {
  return requestExcel("/report/area/importRareaExcel", data);
}

///////// >> 报事管理
