import request from "@/utils/request";
import { requestExcel } from "@/utils";

// 三级联动
export function threeLinkage(type, code) {
  return request({
    url: `/unity/community/threeLinkage/${type}/${code}`,
    method: "get"
  });
}

// 小区分页
export function communityPage(data) {
  return request({
    url: `/unity/community/page`,
    method: "post",
    data
  });
}

// 小区分页 所有
export function communityPageAll(data) {
  return request({
    url: `/unity/community/pageAll`,
    method: "post",
    data
  });
}

// 新增修改小区
export function communityAddOrUpdate(data) {
  return request({
    url: `/unity/community/addOrUpdate`,
    method: "post",
    data
  });
}

// 小区删除启用停用
export function communityDisable(id, flag) {
  return request({
    url: `/unity/community/disable/${id}/${flag}`,
    method: "get"
  });
}

// 楼栋分页
export function cofloorPage(data) {
  return request({
    url: `/unity/cofloor/page`,
    method: "post",
    data
  });
}

// 新增修改楼栋
export function cofloorAddOrUpdate(data) {
  return request({
    url: `/unity/cofloor/addOrUpdate`,
    method: "post",
    data
  });
}

// 楼栋删除启用停用
export function cofloorDisable(id, flag) {
  return request({
    url: `/unity/cofloor/disable/${id}/${flag}`,
    method: "get"
  });
}

// 查询小区内所有楼栋
export function cofloorCommunity(id) {
  return request({
    url: `/unity/cofloor/community/${id}`,
    method: "get"
  });
}

// 单元分页
export function buildingunitPage(data) {
  return request({
    url: `/unity/buildingunit/page`,
    method: "post",
    data
  });
}

// 新增修改单元
export function buildingunitAddOrUpdate(data) {
  return request({
    url: `/unity/buildingunit/addOrUpdate`,
    method: "post",
    data
  });
}

// 单元删除启用停用
export function buildingunitDisable(id, flag) {
  return request({
    url: `/unity/buildingunit/disable/${id}/${flag}`,
    method: "get"
  });
}

// 按楼栋查单元
export function buildingunitFloor(id) {
  return request({
    url: `/unity/buildingunit/floor/${id}`,
    method: "get"
  });
}

// 房屋分页
export function buildingroomPage(data) {
  return request({
    url: `/unity/buildingroom/page`,
    method: "post",
    data
  });
}

// 新增修改房屋
export function buildingroomAddOrUpdate(data) {
  return request({
    url: `/unity/buildingroom/addOrUpdate`,
    method: "post",
    data
  });
}

// 房屋删除启用停用
export function buildingroomDisable(id, flag) {
  return request({
    url: `/unity/buildingroom/disable/${id}/${flag}`,
    method: "get"
  });
}

// 车库分页
export function cogaragePage(data) {
  return request({
    url: `/unity/cogarage/page`,
    method: "post",
    data
  });
}

// 车库分页（未绑定）
export function cogaragePageGarage(data) {
  return request({
    url: `/unity/cogarage/pageGarage`,
    method: "post",
    data
  });
}

// 新增修改车库
export function cogarageAddOrUpdate(data) {
  return request({
    url: `/unity/cogarage/addOrUpdate`,
    method: "post",
    data
  });
}

// 车库删除启用停用
export function cogarageDisable(id, flag) {
  return request({
    url: `/unity/cogarage/disable/${id}/${flag}`,
    method: "get"
  });
}

// 业主绑定车库
export function cogarageBinding(id, memberId) {
  return request({
    url: `/unity/cogarage/binding/${id}/${memberId}`,
    method: "get"
  });
}

// 业主解绑车库
export function cogarageUnbinding(id) {
  return request({
    url: `/unity/cogarage/unbundling/${id}`,
    method: "get"
  });
}

// 车位分页
export function coparkingPage(data) {
  return request({
    url: `/unity/coparking/page`,
    method: "post",
    data
  });
}

// 车位出租
export function coparkingLease(data) {
  return request({
    url: `/unity/coparking/lease`,
    method: "post",
    data
  });
}

// 车位分页（未绑定）
export function coparkingPageParking(data) {
  return request({
    url: `/unity/coparking/pageParking`,
    method: "post",
    data
  });
}

// 新增修改车位
export function coparkingAddOrUpdate(data) {
  return request({
    url: `/unity/coparking/addOrUpdate`,
    method: "post",
    data
  });
}

// 车位删除启用停用
export function coparkingDisable(id, flag) {
  return request({
    url: `/unity/coparking/disable/${id}/${flag}`,
    method: "get"
  });
}

// 业主绑定车位
export function coparkingBinding(id, memberId) {
  return request({
    url: `/unity/coparking/binding/${id}/${memberId}`,
    method: "get"
  });
}

// 业主解绑车位
export function coparkingUnbinding(id) {
  return request({
    url: `/unity/coparking/unbundling/${id}`,
    method: "get"
  });
}

// 管家分页
export function cohousekeeperPage(data) {
  return request({
    url: `/unity/cohousekeeper/page`,
    method: "post",
    data
  });
}

// 新增修改管家
export function cohousekeeperAddOrUpdate(data) {
  return request({
    url: `/unity/cohousekeeper/addOrUpdate`,
    method: "post",
    data
  });
}

// 管家删除启用停用
export function cohousekeeperDisable(id, flag) {
  return request({
    url: `/unity/cohousekeeper/disable/${id}`,
    method: "get"
  });
}

// 管家详情
export function cohousekeeperInfo(id) {
  return request({
    url: `/unity/cohousekeeper/info/${id}`,
    method: "get"
  });
}

// 业主绑定房屋信息
export function buildingmemberBinding(data) {
  return request({
    url: `/unity/buildingmember/binding`,
    method: "post",
    data
  });
}

// 业主绑定房屋信息
export function buildingmemberUnbundling(roomId, memberId) {
  return request({
    url: `/unity/buildingmember/unbundling/${roomId}/${memberId}`,
    method: "get"
  });
}

// 查询小区公告
export function receiveNoticeHeaderByUserId({ page, size }) {
  return request({
    url: `/unity/receiveNoticeHeaderByUserId/${page}/${size}`,
    method: "get"
  });
}

// 发送通知公告
export function sendNotice(data) {
  return request({
    url: `/unity/sendNotice`,
    method: "post",
    data
  });
}

// 查询通知详情
export function receiveNotice(msgId) {
  return request({
    url: `/unity/receiveNotice/${msgId}`,
    method: "get"
  });
}

// 根据登陆人查询绑定小区ids
export function findCommunityByUserId(userId) {
  return request({
    url: `/sys/sysusercommunity/findCommunityByUserId/${userId}`,
    method: "get"
  });
}

// 人员绑定小区
export function sysusercommunityAddOrUpdate(data) {
  return request({
    url: `/sys/sysusercommunity/addOrUpdate`,
    method: "post",
    data
  });
}

// 维修基金分页
export function repairfundPage(data) {
  return request({
    url: `/unity/repairfund/page`,
    method: "post",
    data
  });
}

// 维修基金新增
export function repairfundAddOrUpdate(data) {
  return request({
    url: `/unity/repairfund/addOrUpdate`,
    method: "post",
    data
  });
}

// 维修基金删除
export function repairfundDel(id) {
  return request({
    url: `/unity/repairfund/del/${id}`,
    method: "get"
  });
}

// 广告位分页
export function advertisingspacePage(data) {
  return request({
    url: `/unity/advertisingspace/page`,
    method: "post",
    data
  });
}

// 广告位新增
export function advertisingspaceAddOrUpdate(data) {
  return request({
    url: `/unity/advertisingspace/addOrUpdate`,
    method: "post",
    data
  });
}

// 广告位删除
export function advertisingspaceDel(id) {
  return request({
    url: `/unity/advertisingspace/del/${id}`,
    method: "get"
  });
}

// 广告位出租
export function advertisingspaceLease(data) {
  return request({
    url: `/unity/advertisingspace/lease`,
    method: "post",
    data
  });
}

// 广告位查询信息
export function findInfoBySpaceId(id) {
  return request({
    url: `/unity/advertisingspace/findInfoBySpaceId/${id}`,
    method: "get"
  });
}

// 批量删除出租信息
export function delBySpaceId(id) {
  return request({
    url: `/unity/advertisingspace/delBySpaceId/${id}`,
    method: "get"
  });
}

export function delByDataId(data) {
  return request({
    url: `/unity/advertisingspace/delByDataId`,
    method: "post",
    data
  });
}

//小区房屋类型分页
export function roomTypePage(data) {
  return request({
    url: `/unity/roomType/page`,
    method: "post",
    data
  });
}
//小区房屋类型保存修改
export function saveOrU(data) {
  return request({
    url: `/unity/roomType/saveOrU`,
    method: "post",
    data
  });
}
//小区房屋类型删除
export function roomTypeDel(id) {
  return request({
    url: `/unity/roomType/del/${id}`,
    method: "get",
  });
}

// 导出
export function importCommunity(data) {
  return requestExcel("/unity/report/importCommunity", data);
}

export function importFloor(data) {
  return requestExcel("/unity/report/importFloor", data);
}

export function importUnit(data) {
  return requestExcel("/unity/report/importUnit", data);
}

export function importRoom(data) {
  return requestExcel("/unity/report/importRoom", data);
}

export function importAll(data) {
  return requestExcel("/unity/report/importAll", data);
}

export function importCoGarage(data) {
  return requestExcel("/unity/report/importCoGarage", data);
}

export function importCoParkin(data) {
  return requestExcel("/unity/report/importCoParkin", data);
}

export function importAdvertising(data) {
  return requestExcel("/unity/report/importAdvertising", data);
}
