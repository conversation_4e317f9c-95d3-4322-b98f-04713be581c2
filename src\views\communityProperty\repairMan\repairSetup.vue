<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>

        <el-form-item label="选择小区" prop="communityId">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="派单方式">
          <el-select v-model="listQuery.repairWay" filterable clearable placeholder="请选择派单方式">
            <el-option v-for="item in repairWayList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入报修名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="listQuery.payFeeFlag" filterable clearable placeholder="请选择收费方式">
            <el-option v-for="item in payFeeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>添加报修</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="分类名称">
          <template slot-scope="scope">
            <span>{{ scope.row.repairTypeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报事名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="派单方式">
          <template slot-scope="scope">
            <span>{{ scope.row.repairWayName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="维修费用(元)">
          <template slot-scope="scope">
            <span>{{ scope.row.priceScope }}</span>
          </template>
        </el-table-column>

        <el-table-column label="回访设置">
          <template slot-scope="scope">
            <span>{{ scope.row.returnVisitName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="超时阈值（分钟）">
          <template slot-scope="scope">
            <span>{{ scope.row.overTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row, 'EDIT')">修改</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑报修信息" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :disabled="dlgType === 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">

        <el-form-item label="选择小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="报修分类" prop="repairTypeName">
          <el-input v-model="dlgData.repairTypeName" @focus="showSubjectDlg" placeholder="请选择报修分类" readonly>
          </el-input>
        </el-form-item>

        <el-form-item label="报修名称" prop="name">
          <el-input v-model="dlgData.name" placeholder="请填写报修名称"></el-input>
        </el-form-item>

        <el-form-item label="派单方式" prop="repairWay">
          <el-select v-model="dlgData.repairWay" filterable clearable placeholder="请选择派单方式">
            <el-option v-for="item in repairWayList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="收费方式" prop="payFeeFlag">
              <el-select v-model="dlgData.payFeeFlag" filterable clearable placeholder="请选择收费方式">
                <el-option v-for="item in payFeeList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dlgData.payFeeFlag == 'T'">
            <el-form-item label="收费金额" prop="priceScope">
              <el-input-number v-model="dlgData.priceScope" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="阈值(分钟)" prop="overTime">
          <el-input-number v-model="dlgData.overTime" :controls='false' :min="0" :precision="0" :step="1"></el-input-number>
        </el-form-item>

        <el-form-item label="回访设置" prop="returnVisitFlag">
          <el-select v-model="dlgData.returnVisitFlag" filterable clearable placeholder="请选择回访设置">
            <el-option v-for="item in visitList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="说明">
          <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.remark" placeholder="请输入说明" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <subjectDlg />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'

import {
  repairSettingPage,
  repairsettingAddOrUpdate,
  delRepairSetting
} from '@/api/repairMan'

import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import subjectDlg from '@/components/Dialog/communityMan/subjectDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: 0,
  communityId: '',
  communityName: '',
  name: '',
  overTime: '',
  payFeeFlag: '',
  priceScope: 0,
  remark: '',
  repairTypeId: '',
  repairTypeName: '',
  repairWay: '',
  returnVisitFlag: '',
}


export default {
  name: 'repairSetup',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    subjectDlg
  },
  data () {
    return {
      // 弹窗 状态
      dlgShowBind: false,
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        repairTypeName: [{ required: true, message: '必填字段', trigger: 'change' }],
        repairWay: [{ required: true, message: '必填字段', trigger: 'change' }],
        payFeeFlag: [{ required: true, message: '必填字段', trigger: 'change' }],
        overTime: [{ required: true, message: '必填字段', trigger: 'blur' }],
        returnVisitFlag: [{ required: true, message: '必填字段', trigger: 'change' }],
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        priceScope: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        payFeeFlag: '',
        repairWay: '',
        logType: '1',
      },
      userInfo: {},
      communityList: [],
      payFeeList: [{
        id: 'T',
        name: '收费'
      },
      {
        id: 'F',
        name: '免费'
      }],
      repairWayList: [
        {
          id: '100',
          name: '抢单'
        },
        {
          id: '200',
          name: '指派'
        },
        {
          id: '300',
          name: '轮询'
        }
      ],
      visitList: [
        {
          id: 1,
          name: '都不回访'
        },
        {
          id: 2,
          name: '已评价不回访'
        },
        {
          id: 3,
          name: '都回访'
        }
      ]
    }
  },

  computed: {
    ...mapGetters('communityMan/subjectDlg', {
      subjectId: 'subjectId',
      subjectName: 'subjectName',
    }),

  },

  watch: {
    subjectId (val) {
      this.dlgData.repairTypeId = val
    },

    subjectName (val) {
      this.dlgData.repairTypeName = val
    },

  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 显示房间对话框
    showSubjectDlg () {
      this.$store.commit('communityMan/subjectDlg/SET_SUBJECTID', this.dlgData.repairTypeId)
      this.$store.commit('communityMan/subjectDlg/SET_SUBJECTNAME', this.dlgData.repairTypeName)
      this.$store.commit('communityMan/subjectDlg/SET_DLGSHOW', true)
    },

    formatList () {
      for (let i of this.list) {
        i.repairWayName = utils.getNameById(i.repairWay, this.repairWayList)
        i.payFeeName = utils.getNameById(i.payFeeFlag, this.payFeeList)
        i.returnVisitName = utils.getNameById(i.returnVisitFlag, this.visitList)
      }
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      repairSettingPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.formatList()
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          this.dlgLoading = true
          repairsettingAddOrUpdate(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgType = type
      this.dlgShow = true
    },

    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delRepairSetting(data.id, flag).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/ownerInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


