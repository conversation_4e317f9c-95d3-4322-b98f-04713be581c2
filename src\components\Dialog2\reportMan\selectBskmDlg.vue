<template>
  <el-dialog
    class="mazhenguo"
    title="选择报事科目"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="600px"
    top="30px"
  >
    <el-input placeholder="输入关键字进行过滤" v-model="filterBranch"> </el-input>
    <el-tree
      class="mt10"
      ref="branchTree"
      highlight-current
      node-key="id"
      :data="treeData"
      :props="defaultProps"
      @node-click="treeNodeClick"
      :filter-node-method="filterNode"
      :expand-on-click-node="false"
      :default-expanded-keys="defaultOpenList"
      style="height: 500px; overflow: auto"
    >
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span :title="data.name">{{ data.name }}</span>

        <span v-if="data.type == 0" class="fdanger">(分类)</span>
        <span v-if="data.type == 1" class="fsuccess">(科目)</span>
        <span v-if="data.status == 0" class="fsuccess">(启用)</span>
        <span v-if="data.status == 1" class="fdanger">(停用)</span>
      </span>
    </el-tree>
    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-span" v-if="selectNode && selectNode.name">当前选中：{{ selectNode.name }}</span>
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button v-if="dlgType != 'info'" :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">确定中...</span>
        <span v-else>确定</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
// 工具
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
// 接口
import * as utils from '@/utils'
import * as regUtils from '@/utils/regUtils'

import { postAction, getAction } from '@/api'

export default {
  components: {},
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgSelectData: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          console.log('--this.dlgSelectData2222', this.dlgSelectData)
          if (this.dlgSelectData) {
            this.selectNode = this.dlgSelectData

            this.$nextTick(() => {
              $('.tree-on').removeClass('tree-on')
              this.$refs.branchTree.setCurrentKey(this.selectNode.id)
            })
          } else {
            this.selectNode = ''
          }
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },

    filterBranch(val) {
      this.$refs.branchTree.filter(val)
    },
  },
  data() {
    return {
      // 树初始化
      defaultOpenList: [], // 默认展开
      defaultProps: {
        children: 'children',
        label: 'name',
      },

      filterBranch: '',
      treeData: '',
      selectNode: {},

      // 弹窗
      dlgState: false,

      dlgSubLoading: false, // 提交loading
    }
  },
  created() {
    this.getTree()
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    getTree() {
      let userInfo = JSON.parse(window.localStorage.userInfo)
      // 根据权限
      getAction(`/report/subject/findSubjectTree/${userInfo.projectId}`).then((res0) => {
        let res = res0.data

        if (res.code === '200') {
          this.treeData = JSON.parse(JSON.stringify(res.data))

          let openIdList = []
          for (let item of this.treeData) {
            openIdList.push(item.id)
          }
          this.defaultOpenList = openIdList
        } else {
          this.$message.error(res.msg)
        }
      })
    },

    treeNodeClick(data) {
      if (data.type == 0) {
        this.$message({
          type: 'warning',
          message: '只能选择科目节点',
        })
        return false
      }

      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      let data0 = JSON.parse(JSON.stringify(data))
      delete data0.children
      this.selectNode = data0
    },

    // 弹窗提交 ------
    dlgSubFunc() {
      this.$emit('backFunc', this.selectNode)
      this.closeDlg()
    },

    closeDlg() {
      this.$emit('closeDlg')
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>