<template>
  <el-dialog
    title="选择菜品"
    :visible.sync="visible"
    width="50%"
    @close="handleClose"
    append-to-body
    :close-on-click-modal="false"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input
              v-model="searchForm.name"
              placeholder="请输入菜品名称"
              clearable
              class="fl"
              style="width: 160px"
            ></el-input>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10 btnStyle"
              >查询</el-button
            >
            <el-popover
              class="fr"
              placement="bottom"
              width="800"
              trigger="click"
            >
              <el-table
                ref="multipleTable"
                style="height: 300px; overflow: auto"
                :data="selectList"
              >
                <el-table-column
                  type="index"
                  width="50"
                  align="center"
                ></el-table-column>

                <el-table-column
                  prop="equTypeStr"
                  label="菜品图片"
                  width="auto"
                  align="center"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-image
                      style="width: 30px; height: 30px"
                      :src="scope.row.imgUrl[0]"
                      :preview-src-list="scope.row.imgUrl"
                    ></el-image>
                  </template>
                </el-table-column>
                <!-- 其他列省略... -->
                <el-table-column
                  prop="name"
                  label="菜品名称"
                  width="auto"
                  align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="model"
                  label="规格"
                  width="auto"
                  align="center"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="unit"
                  label="单位"
                  width="auto"
                  align="center"
                  show-overflow-tooltip
                >
                </el-table-column>

                <el-table-column
                  property=""
                  label="操作"
                  width="100"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-button
                      @click="popRemoveRow(scope.row)"
                      type="danger"
                      size="mini"
                      icon="el-icon-delete"
                      plain
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-button
                class="fr search-right-btn"
                type="success"
                slot="reference"
                icon="el-icon-arrow-down"
                >查看已选</el-button
              >
            </el-popover>
          </div>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData"
      height="600px"
      ref="tableRef"
      class="m-small-table"
      :loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
      @select="handleSelectionChange"
      @select-all="tableSelectAll"
      :reserve-selection="true"
      :row-key="
        (row) => {
          return row.id;
        }
      "
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        prop="equTypeStr"
        label="菜品图片"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <el-image
            style="width: 30px; height: 30px"
            :src="scope.row.imgUrl[0]"
            :preview-src-list="scope.row.imgUrl"
          ></el-image>
        </template>
      </el-table-column>
      <!-- 其他列省略... -->
      <el-table-column
        prop="name"
        label="菜品名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="model"
        label="规格"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="unit"
        label="单位"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建日期"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="creatorName"
        label="创建人"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getList"
    />

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="small">取 消</el-button>
      <el-button
        type="success"
        @click="handleConfirm"
        size="small"
        icon="el-icon-check"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import Pagination from "@/components/Pagination";
import { getAction, deleteAction } from "@/api";

export default {
  components: {
    Pagination,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    foods: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.selectList = JSON.parse(JSON.stringify(this.foods));
        this.getList();
      }
    },
  },
  data() {
    return {
      selectList: [],
      tableData: [],
      searchForm: {
        name: "",
        pageNo: 1,
        pageSize: 20,
      },
      total: 0,
      listLoading: false,
    };
  },
  methods: {
    popRemoveRow(row) {

      let selectList = this.selectList.filter((item) => {
        return item.id != row.id;
      });
      this.selectList = JSON.parse(JSON.stringify(selectList));
      for (let item of this.tableData) {
        let isHas = row.id == item.id;
        if (isHas) {
          this.$refs.tableRef.toggleRowSelection(item, false);
        }
      }
    },
    searchFunc() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    getList() {
      let postData = { ...this.searchForm };
      this.listLoading = true;

      getAction(`/canteen/cn/dishes/page`, postData).then((res) => {
        let { code, data } = res.data;
        this.listLoading = false;
        if (code === "200") {
          data.list.forEach((item) => {
            if (item.imgUrl) {
              item.imgUrl = JSON.parse(item.imgUrl);
            }
          });
          this.tableData = data.list || [];
          this.total = data.total || 0;
          console.log(this.tableData);
          console.log(this.selectList, "selectList");
          console.log(this.foods, "foods");
          
          this.$nextTick(() => {
            if (this.selectList.length > 0) {
              for (let item of this.tableData) {
                let isHas = this.selectList.some((row) => row.id == item.id);
                if (isHas) {
                  this.$refs.tableRef.toggleRowSelection(item, true);
                } else {
                  this.$refs.tableRef.toggleRowSelection(item, false);
                }
              }
            } else {
              this.$refs.tableRef.clearSelection();
            }
          });
        } else {
          this.$message.error(res.data.msg);
        }
        // this.selectList = JSON.parse(JSON.stringify(this.foods));
      });
    },
    handleSelectionChange(arr, row) {
      this.tableCheckBaseFunc(row);
    },

    tableCheckBaseFunc(row) {

      let isCheck = !this.selectList.some((item) => item.id == row.id);

      if (isCheck) {
        this.selectList.push(row);
      } else {
        let selectList = this.selectList.filter((item) => {
          return item.id != row.id;
        });
        this.selectList = JSON.parse(JSON.stringify(selectList));
      }
    },
    tableSelectAll(arr) {
      let len = arr.length;
      let list = JSON.parse(JSON.stringify(this.tableData));
      let selectList = JSON.parse(JSON.stringify(this.selectList));
      if (len == 0) {
        let newList = [];
        for (let item of selectList) {
          let hasId = list.some((item2) => item2.id == item.id);
          if (!hasId) {
            newList.push(item);
          }
        }
        selectList = JSON.parse(JSON.stringify(newList));
      } else {
        for (let item of list) {
          let hasId = selectList.some((item2) => item2.id == item.id);
          if (!hasId) {
            selectList.push(item);
          }
        }
      }
      this.selectList = selectList;
    },
    handleClose() {
      this.$emit("close");
      this.selectList = [];
    },
    handleConfirm() {
      this.$emit("confirm", this.selectList);
    },
  },
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
.btnStyle {
  padding: 6px 10px;
  height: 28px;
}
</style>