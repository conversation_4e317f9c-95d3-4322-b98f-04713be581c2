<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="付费对象：">
          <el-select v-model="listQuery.payType" filterable clearable placeholder="请选择付费对象">
            <el-option v-for="item in payTypeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="确认状态：">
          <el-select v-model="listQuery.isAudit" filterable clearable placeholder="请选择状态">
            <el-option v-for="item in auditList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native="getList" placeholder="请输入房号/车位编号/车库编号" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList">查询</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="房屋">
          <template slot-scope="scope">
            <span>{{ scope.row.roomName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="费用项目">
          <template slot-scope="scope">
            <span>{{ scope.row.configName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="应付金额"  width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.receivableAmount ? scope.row.receivableAmount.toFixed(2) : 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="实付金额"  width="120"  align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.payAmount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作员工"  width="100"  align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.createName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="缴费时间" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="确认状态" align="center" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isAudit == 0" type="warning">待确认</el-tag>
            <el-tag v-if="scope.row.isAudit == 1" type="success">已通过</el-tag>
            <el-tag v-if="scope.row.isAudit == 2" type="danger">未通过</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="缴费类型" align="center">
          <template slot-scope="scope">

            <div v-if="scope.row.payInfoJsonStr">
              {{ scope.row.payInfoJsonStr }}
            </div>
            <div v-else>
              <span v-if="scope.row.payWay == 1">业主自助缴费</span>
              <span v-if="scope.row.payWay == 2">线上扫码付费</span>
              <span v-if="scope.row.payWay == 3">线下现金缴费</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.auditRemark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="缴费备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.isAudit == 0"
              type="primary"
              size="mini"
              icon="el-icon-edit"
              plain
              @click="editItem(scope.row, 'EDIT')"
              >确认</el-button
            >
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog
      :close-on-click-modal="false"
      :title="dlgType == 'EDIT' ? '确认信息' : '费用信息'"
      :visible.sync="dlgShow"
      :width="dlgType == 'EDIT' ? '600px' : '1200px'"
      append-to-body
    >
      <el-form ref="dlgForm" :disabled="dlgType == 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="135px">
        <template v-if="dlgType == 'EDIT'">
          <el-form-item label="确认状态" prop="isAudit" v-if="dlgData.payWay == 3">
            <el-radio-group v-model="dlgData.isAudit">
              <el-radio :label="1">通过</el-radio>
              <el-radio :label="2">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="确认状态" v-else>
            <el-checkbox v-model="dlgData.isAudit">通过</el-checkbox>
          </el-form-item>
          <el-form-item label="收款凭证:" v-if="dlgData.imgUrl">
            <el-image fit="contain" :src="dlgData.imgUrl" :preview-src-list="[dlgData.imgUrl]"> </el-image>
          </el-form-item>
          <el-form-item label="收费方式:" v-if="dlgData.offlinePayWay && dlgData.payInfoJsonStr2">
            {{ dlgData.payInfoJsonStr2 }}
          </el-form-item>
          <el-form-item label="收费方式:" v-else-if="dlgData.offlinePayWay">
            {{
              payWayList.filter((item) => {
                return item.id == dlgData.offlinePayWay
              })[0]['name']
            }}
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" v-model="dlgData.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
        <template v-else>
          <el-divider v-if="dlgType == 'VIEW'">费用信息</el-divider>
          <el-row>
            <el-col :span="8">
              <el-form-item label="费用ID：">{{ dlgData.id }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="费用标识：">{{ dlgData.feeFlagName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="费用类型：">{{ dlgData.feeName }}</el-form-item>
            </el-col>
          </el-row>
          <el-col :span="8">
            <el-form-item label="付费对象：">{{ dlgData.roomName }}</el-form-item>
          </el-col>
          <el-row>
            <el-col :span="8">
              <el-form-item label="费用项：">{{ dlgData.configName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="费用状态：">{{ dlgData.status == 0 ? '未缴费' : '已缴费' }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="建账时间：">{{ dlgData.createTime }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计费开始时间：">{{ dlgData.startDate }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计费结束时间：">{{ dlgData.endDate }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="收款凭证:" v-if="dlgData.imgUrl">
                <el-image fit="contain" :src="dlgData.imgUrl" :preview-src-list="[dlgData.imgUrl]"> </el-image>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="收费方式:" v-if="dlgData.offlinePayWay && dlgData.payInfoJsonStr2">
                {{ dlgData.payInfoJsonStr2 }}
              </el-form-item>

              <el-form-item label="收费方式:" v-else-if="dlgData.offlinePayWay">
                {{
                  payWayList.filter((item) => {
                    return item.id == dlgData.offlinePayWay
                  })[0]['name']
                }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="说明：">{{ dlgData.remark }}</el-form-item>
            </el-col>
          </el-row>

          <el-divider v-if="dlgType == 'VIEW'">缴费历史</el-divider>
          <el-table class="m-small-table" :data="dlgData.list" border fit highlight-current-row>
            <el-table-column label="费用类型">
              <template slot-scope="scope">
                <span>{{ scope.row.feeName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用项目">
              <template slot-scope="scope">
                <span>{{ scope.row.configName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用标识">
              <template slot-scope="scope">
                <span>{{ scope.row.feeFlagName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用摘要" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <span>{{ scope.row.startDate }} ~ {{ scope.row.endDate }} {{ scope.row.feeName }} </span>
              </template>
            </el-table-column>

            <el-table-column label="应收金额">
              <template slot-scope="scope">
                <span>{{ scope.row.receivableAmount }}</span>
              </template>
            </el-table-column>

            <el-table-column label="费用金额">
              <template slot-scope="scope">
                <span>{{ scope.row.amount.toFixed(2) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="优惠金额">
              <template slot-scope="scope">
                <span>{{ scope.row.preferentialAmount.toFixed(2) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="滞纳金">
              <template slot-scope="scope">
                <span>{{ scope.row.zhinajin.toFixed(2) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type="success" :loading="dlgLoading" @click="subDlg" icon="el-icon-check">
          <span>提交</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { orderPage, getPayBillPage, auditPayOrder } from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  list: [],
  isAudit: 1,
  remark: '',
}

export default {
  name: 'payCostConfirm',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data() {
    return {
      id: '',
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        isAudit: [{ required: true, message: '必填字段', trigger: 'change' }],
        remark: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        isAudit: '',
        payType: '',
        logType: '1',
      },
      totalMoney: 0,
      discountList: [],
      userInfo: {},
      costTypeList: [],
      selectList: [],
      feeFlagList: [
        {
          id: '1',
          name: '周期性费用',
        },
        {
          id: '2',
          name: '一次性费用',
        },
      ],
      auditList: [
        {
          id: '',
          name: '全部',
        },
        {
          id: '0',
          name: '待确认',
        },
        {
          id: '1',
          name: '通过',
        },
        {
          id: '2',
          name: '未通过',
        },
      ],
      payTypeList: [
        {
          id: '1',
          name: '房屋',
        },
        {
          id: '2',
          name: '车位',
        },
        {
          id: '3',
          name: '车库',
        },
      ],
      payWayList: [
        {
          id: '0',
          name: '线上支付',
        },
        {
          id: '1',
          name: '微信',
        },
        {
          id: '2',
          name: '支付宝',
        },
        {
          id: '3',
          name: '现金',
        },
      ],
    }
  },

  created() {
    utils.getDataDict(this, 'costType', 'costTypeList')
    this.getList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    formatList() {
      for (let i of this.list) {
        let orderInfoJson = JSON.parse(i.orderInfoJson)
        if (orderInfoJson.length > 0) {
          i.configName = orderInfoJson[0].configName
          i.feeName = utils.getNameById(orderInfoJson[0].feeType, this.costTypeList)
          i.feeFlagName = utils.getNameById(orderInfoJson[0].feeFlag, this.feeFlagList)
          let receivableAmount = 0
          for (let j of orderInfoJson) {
            receivableAmount += j.receivableAmount
          }
          i.receivableAmount = receivableAmount
          i.startDate = orderInfoJson[0].startDate
          i.endDate = orderInfoJson[0].endDate
        }
      }
    },

    // 获取数据
    getList() {
      this.count++
      this.listLoading = true
      orderPage(this.listQuery).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          let list = res.data.data ? JSON.parse(JSON.stringify(res.data.data)) : []

          for(let item0 of list) {
            let item0PayInfoJson = ''
            for (let item of item0.feeBills) {
              // item.payInfoJson = JSON.stringify({zfbPay: 999,wxPay:888})
              if (item.payInfoJson) {
                if (item0PayInfoJson == '') {
                  item0PayInfoJson={wxPay:0,zfbPay:0,cashPay:0 }
                }
                let payInfoJson = JSON.parse(item.payInfoJson)
                console.log('===payInfoJson', payInfoJson)
                if (payInfoJson.wxPay && payInfoJson.wxPay>0) {
                  item0PayInfoJson.wxPay += payInfoJson.wxPay
                }
                if (payInfoJson.zfbPay ) {
                  item0PayInfoJson.zfbPay += payInfoJson.zfbPay
                }
                if (payInfoJson.cashPay ) {
                  item0PayInfoJson.cashPay += payInfoJson.cashPay
                }
              }
            }
            
            if (item0PayInfoJson) {
              let payInfoJsonStr = ''
              let payInfoJsonStr2 = ''
              if (item0PayInfoJson.wxPay) {
                payInfoJsonStr += `微信：${utils.num2Round(item0PayInfoJson.wxPay)}、`
                payInfoJsonStr2 += "微信、"
              }
              if (item0PayInfoJson.zfbPay) {
                payInfoJsonStr += `支付宝：${utils.num2Round(item0PayInfoJson.zfbPay)}、`
                payInfoJsonStr2 += "支付宝、"

              }
              if (item0PayInfoJson.cashPay) {
                payInfoJsonStr += `现金：${utils.num2Round(item0PayInfoJson.cashPay)}、`
                payInfoJsonStr2 += "现金、"

              }
              item0.payInfoJsonStr = payInfoJsonStr.substr(0, payInfoJsonStr.length-1)
              item0.payInfoJsonStr2 = payInfoJsonStr2.substr(0, payInfoJsonStr2.length-1)
            }
          }
          this.list = list
          this.total = res.data.page.total
          this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 编辑
    editItem(data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgData.id = data.id
      this.dlgType = type
      this.dlgShow = true
      let orderInfoJson = JSON.parse(data.orderInfoJson)
      for (let i of orderInfoJson) {
        i.feeName = utils.getNameById(i.feeType, this.costTypeList)
        i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList)
        let preferentialAmount = 0
        let zhinajin = 0
        for (let j of i.discountList) {
          if (j.type == 1) {
            zhinajin += j.discountAmount || 0
          } else if (j.type == 2) {
            preferentialAmount += j.discountAmount || 0
          }
        }
        i.preferentialAmount = preferentialAmount
        i.zhinajin = zhinajin
        i.amount = i.receivableAmount + i.zhinajin - i.preferentialAmount
      }
      this.dlgData.list = JSON.parse(JSON.stringify(orderInfoJson))
      if (type == 'EDIT') {
        this.dlgData.isAudit = this.dlgData.payWay == 3 ? 1 : true
        this.dlgData.remark = this.dlgData.auditRemark
      }

      console.log('this.dlgData', JSON.stringify(this.dlgData))
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg() {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          this.$confirm('确定提交?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            let postParam = {
              id: this.dlgData.id,
              remark: this.dlgData.remark,
            }
            if (this.dlgData.payWay == 3) {
              postParam.isAudit = this.dlgData.isAudit
            } else {
              postParam.isAudit = this.dlgData.isAudit ? 1 : 0
            }
            this.dlgLoading = true
            auditPayOrder(postParam).then((res) => {
              this.dlgLoading = false
              if (res.data.code == 200) {
                this.getList()
                this.dlgShow = false
                this.$message.success(res.data.msg)
              } else {
                this.$message.error(res.data.msg)
              }
            })
          })
        }
      })
    },

    // 上传对话框图片
    beforeUpload(file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then((res) => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg() {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-image {
  width: 150px;
  height: 150px;
}
</style>


