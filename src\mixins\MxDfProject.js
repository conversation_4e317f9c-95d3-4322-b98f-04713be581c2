import * as utils from "@/utils";
export const MxDfProject = {
  data() {
    return {
      userProjectsList: [],
      dfProjectId: "", // 选择项目/
      dfProjectName: ""
    };
  },
  created() {
    console.log("mixins-created");
    let userInfo = JSON.parse(window.localStorage.ERPUserInfo);
    this.userProjectsList = userInfo.projects;

    if (window.sessionStorage.dfProjectId) {
      this.dfProjectId = window.sessionStorage.dfProjectId;
      this.dfProjectName = window.sessionStorage.dfProjectName;
    }
  },
  mounted() {
    console.log("mixins mounted");
  },
  methods: {
    // setMx...
    setMxDfProjectId(val) {
      window.sessionStorage.dfProjectId = val;
      this.dfProjectId = val;
      this.dfProjectName = utils.arrId2Name(this.userProjectsList, val);
      window.sessionStorage.dfProjectName = this.dfProjectName;
    }
    // setMxDfProjectName(val) {
    //   window.sessionStorage.dfProjectName = val;
    //   this.dfProjectName = val;
    // }
  }
};
