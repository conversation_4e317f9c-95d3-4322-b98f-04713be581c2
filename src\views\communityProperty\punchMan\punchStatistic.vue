<template>
  <!-- 考勤管理 -->
  <div class="app-container" ref="schedulingMan">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="月份">
          <el-date-picker :picker-options="pickerOptions" v-model="listQuery.date" value-format="yyyy-MM" format="yyyy-MM" type="month" placeholder="月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-radio-group @change="getList" v-model="listQuery.branchType">
            <el-radio label="0">当前部门</el-radio>
            <el-radio label="1">当前及所属部门</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.branchName" @focus="showBranchDlg" :title='listQuery.branchName' placeholder="选择部门" readonly>
            <i @click='resetSearchItem(["branchId", "branchName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.label" placeholder='请输入姓名'>
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-download' type="primary" size='mini' @click='exportExcel'>导出</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="姓名" width="100px">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>

        <el-table-column label="部门" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.departmentName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="岗位" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.userJob }}</span>
          </template>
        </el-table-column>

        <el-table-column label="出勤情况" align="center">
          <el-table-column :class-name="'text-left'" align="center" v-for="(item, index) in dateList" :key="index" :label="item.day + '/' + item.week" :width="120" :render-header="renderheader" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <div :class="(scope.row.punchInfos[item.date]['goPunch'] && scope.row.punchInfos[item.date]['goPunch'].includes('迟到')) ? 'fdanger' : ''">{{scope.row.punchInfos[item.date]['goPunch']}}</div>
              <div>{{scope.row.punchInfos[item.date]['midPunch']}}</div>
              <div :class="(scope.row.punchInfos[item.date]['offPunch'] && scope.row.punchInfos[item.date]['offPunch'].includes('早退')) ? 'fdanger' : ''">{{scope.row.punchInfos[item.date]['offPunch']}}</div>
            </template>
          </el-table-column>
        </el-table-column>

      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <branchDlg :pageType="pageType" />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { punchStatistic } from '@/api/punchMan/report.js'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import branchDlg from '@/components/Dialog/platformMan/branchDlg'

let maxMonth = utils.getDiffMonth("")
export default {
  components: {
    Pagination,
    branchDlg
  },
  data () {
    return {
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() > new Date(maxMonth);
        },
      },

      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        date: '',
        branchId: '',
        branchName: '',
        branchType: '0'
      },
      dateList: [],

      userInfo: {},

      count: 0,
      pageType:""
    }
  },
  computed: {
    ...mapGetters('platformMan/branchDlg', {
      branchId: 'branchId',
      branchName: 'branchName'
    }),
  },
  watch: {

    branchId (val) {
      this.listQuery.branchId = val
    },

    branchName (val) {
      this.listQuery.branchName = val
    },

  },

  created () {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.listQuery.date = maxMonth
    this.getDateList()
  },

  methods: {
    // 显示部门树
    showBranchDlg () {
      let branchId = this.listQuery.branchId
      let branchName = this.listQuery.branchName
      this.pageType='lysgl'
      this.$store.commit('platformMan/branchDlg/SET_BRANCHID', branchId)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', branchName)
      this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', true)
    },

    // 表格头换行
    renderheader (h, { column, $index }) {
      return h('span', {}, [
        h('span', {}, column.label.split('/')[0]),
        h('br'),
        h('span', {}, column.label.split('/')[1])
      ])
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },


    // 获取日期数组
    getDateList () {
      this.dateList = []
      let maxDate = utils.getMaxDate(this.listQuery.date)
      for (let i = 1; i <= maxDate; i++) {
        let day = i < 10 ? '0' + i : i.toString()
        let date = this.listQuery.date + '-' + day
        this.dateList.push({
          date,
          day,
          week: utils.getWeek(date)
        })
      }
    },

    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/sys/syscompany/dakatongjiExport?` + param
      window.open(sendUrl)
    },


    // 根据类型不同重建数组
    formatList (list) {

    },


    // 获取数据
    getList () {
      if (utils.isNull(this.listQuery.date)) {
        this.$message.warning("请选择日期")
        return
      }
      this.getDateList()
      this.count++
      this.listLoading = true
      punchStatistic(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          let list = res.data.data ? JSON.parse(JSON.stringify(res.data.data)) : []
          this.formatList(list)
          this.list = list
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ td.text-left .cell {
  text-align: left;
}
</style>


