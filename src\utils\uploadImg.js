// 阿里图片直传  需在阿里 控制台中配置跨域权限
// 阿里
import OSS from "ali-oss";
import { Message } from "element-ui";

let client = new OSS({
  region: "oss-cn-beijing",
  accessKeyId: "LTAIkVUa1mIiHkJi",
  accessKeySecret: "kQefbVH2kut7NAITelvm9IYDCjp8Rb",
  bucket: "wlines", // 你要上传到那个文件夹下； 这个需要在阿里云服务器建立
  endpoint: "https://oss-cn-beijing.aliyuncs.com",
  secure: true
});
export function uploadImg(file, uploadUrl) {
  var suffix = file.name.substr(file.name.indexOf(".")); // 后缀名
  var obj = timestamp(); // 这里是生成文件名
  let suffix_list = [
    "image/jpg",
    "image/jpeg",
    "image/png",
    "image/git",
    "image/bmp"
  ];
  var storeAs = uploadUrl + obj + suffix; // 文件路径
  return new Promise((resolve, rejust) => {
    try {
      if (!suffix_list.includes(file.type)) {
        Message({
          type: "warning",
          message: "不支持的文件类型，请上传图片类型的文件！"
        });
        return false;
      }
      client.put(storeAs, file).then(res => {
        // resolve(res.url + stylePrams)
        resolve(res.url);
      });
    } catch (e) {
      rejust(e);
    }
  });
  async function put() {
    try {
      let result = await client.put(storeAs, file);
      return result.url;
    } catch (e) {
      return e;
    }
  }
}

function timestamp() {
  var time = new Date();
  var y = time.getFullYear();
  var m = time.getMonth() + 1;
  var d = time.getDate();
  var h = time.getHours();
  var mm = time.getMinutes();
  var s = time.getSeconds();
  return "" + y + add0(m) + add0(d) + add0(h) + add0(mm) + add0(s);
}

function add0(m) {
  return m < 10 ? "0" + m : m;
}

// 图片 + wav
export function uploadImg3(file, uploadUrl) {
  var suffixArr = file.name.split(".");
  var suffix = suffixArr[suffixArr.length - 1].toLowerCase();
  // var suffix = file.name.substr(file.name.indexOf(".")); // 后缀名
  console.log(suffix);
  var obj = timestamp(); // 这里是生成文件名
  // var styleName = '?x-oss-process=style/HRImgUpload'
  if (
    suffix !== "jpg" &&
    suffix !== "png" &&
    suffix !== "jpeg" &&
    suffix !== "wav"
  ) {
    Message({
      type: "warning",
      message: "只能上传jgp/jpeg/png/wav文件"
    });
    return false;
  }
  var storeAs = uploadUrl + obj + "." + suffix; // 文件路径
  console.log(storeAs);
  return new Promise((resolve, rejust) => {
    try {
      client.put(storeAs, file).then(res => {
        // resolve(res.url + stylePrams)
        resolve(res.url);
      });
    } catch (e) {
      rejust(e);
    }
  });
  async function put() {
    try {
      let result = await client.put(storeAs, file);
      return result.url;
    } catch (e) {
      return e;
    }
  }
}

export function base64ToFile(base64Str) {
  let dateObj = new Date();
  let time = dateObj.getTime() + "";
  let str = time.slice(-5);
  let fileName = "file_" + str + ".png";

  var arr = base64Str.split(","),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  let nBlob = new Blob([u8arr], { type: mime });

  console.log("===nBlob", nBlob);

  nBlob.lastModifiedDate = new Date(); // 文件最后的修改日期
  nBlob.name = fileName; // 文件名
  return new File([nBlob], fileName, {
    type: nBlob.type,
    lastModified: Date.now()
  });
}
