<template>
  <div>
    <el-dialog
      @open="onOpen"
      @close="onClose"
      :title="title"
      :visible.sync="dialogVisible"
      width="650px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        :disabled="dlgType == 'info'"
      >
        <!-- <el-form-item label="项目" prop="projectId">
          <el-select
            v-model="formData.projectId"
            placeholder="请选择项目"
            clearable
            filterable
            :style="{ width: '100%' }"
            @change="projectChange"
          >
            <el-option
              v-for="(item, index) in projectList"
              :key="index"
              :label="item.name"
              :value="item.id"
              :disabled="item.disabled"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="设备名称" prop="equName">
          <el-input
            v-model="formData.equName"
            placeholder="请输入设备名称"
            clearable
            :style="{ width: '100%' }"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="设备型号" prop="equModel">
          <el-input
            v-model="formData.equModel"
            placeholder="请输入设备型号"
            clearable
            :style="{ width: '100%' }"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="设备控制区域" prop="controlArea">
          <el-input
            v-model="formData.controlArea"
            placeholder="请输入设备控制区域"
            clearable
            :style="{ width: '100%' }"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="设备类型" prop="equType">
          <el-select
            v-model="formData.equType"
            placeholder="请选择设备类型"
            clearable
            :style="{ width: '100%' }"
          >
            <el-option
              v-for="(item, index) in equipmentTypeOptions"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备使用状态" prop="status">
          <el-select
            v-model="formData.status"
            placeholder="请选择设备使用状态"
            clearable
            :style="{ width: '100%' }"
          >
            <el-option
              v-for="(item, index) in statusOptions"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label=" 设备责任人" prop="responsiblePersonName">
          <el-input
            v-model="formData.responsiblePersonName"
            :title="formData.responsiblePersonName"
            @focus="showUserDlg"
            placeholder="请选择员工"
          />
        </el-form-item>
        <el-form-item label="设备图片" style="width: 100%" prop="picUrl">
          <div v-if="formData.picUrl.length == 0 && dlgType == 'info'">无</div>
          <div v-else>
            <div
              class="upload-bar"
              v-for="(item, index) of formData.picUrl"
              :key="index"
            >
              <el-image
                class="avatar"
                :preview-src-list="[item]"
                :z-index="9999"
                :src="item"
                alt=""
              ></el-image>
              <i
                @click="delUploadImgByArr(index)"
                class="el-icon-error avatar_icon"
                v-if="dlgType != 'info'"
              ></i>
            </div>
            <el-upload
              class="avatar-uploader"
              v-if="dlgType != 'info' && formData.picUrl.length < 5"
              action=""
              :show-file-list="false"
              :before-upload="uploadQj1"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item label="设备所在位置" prop="equPosition">
          <el-select
            v-model="formData.equPosition"
            placeholder="请选择设备所在位置"
            clearable
            filterable
            :style="{ width: '100%' }"
          >
            <el-option
              v-for="(item, index) in equPositionList"
              :key="index"
              :label="item.equPosition"
              :value="item.equPosition"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备生产厂家" prop="manufacturer">
          <el-input
            v-model="formData.manufacturer"
            placeholder="请输入设备生产厂家"
            clearable
            :style="{ width: '100%' }"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="设备出厂时间" prop="leaveTime">
          <el-date-picker
            v-model="formData.leaveTime"
            format="yyyy-MM"
            value-format="yyyy-MM"
            :style="{ width: '100%' }"
            placeholder="请选择设备出厂时间"
            clearable
            type="month"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="设备安装时间" prop="installTime">
          <el-date-picker
            v-model="formData.installTime"
            format="yyyy-MM"
            value-format="yyyy-MM"
            :style="{ width: '100%' }"
            placeholder="请选择设备安装时间"
            clearable
            type="month"
          ></el-date-picker>
        </el-form-item>
        <el-row>
          <el-col :span="11">
            <el-form-item label="参考使用年限" prop="useAgeStart">
              <el-input-number
                v-model="formData.useAgeStart"
                placeholder="请输入"
                :step="1"
                :min="0"
                :precision="0"
                style="width: 142px"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="1">
            <el-form-item label-width="0">~</el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label-width="0" prop="useAgeEnd">
              <el-input-number
                v-model="formData.useAgeEnd"
                placeholder="请输入"
                :step="1"
                :min="0"
                :precision="0"
                style="width: 139px"
              ></el-input-number>
              <span style="margin-left: 5px">年</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="设备检测周期" prop="checkCycleStr">
          <el-input
            v-model="formData.checkCycleStr"
            placeholder="请输入设备检测周期"
            clearable
            :style="{ width: '100%' }"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="设备图纸" prop="drawingUrl">
          <div v-if="formData.drawingUrl.length == 0 && dlgType == 'info'">
            无
          </div>
          <div v-else>
            <div
              class="upload-bar"
              v-for="(item, index) of formData.drawingUrl"
              :key="index"
            >
              <el-image
                class="avatar"
                :preview-src-list="[item]"
                :z-index="9999"
                :src="item"
                alt=""
              ></el-image>
              <i
                @click="delUploadImgByArr1(index)"
                class="el-icon-error avatar_icon"
                v-if="dlgType != 'info'"
              ></i>
            </div>
            <el-upload
              class="avatar-uploader"
              v-if="dlgType != 'info' && formData.drawingUrl.length < 5"
              action=""
              :show-file-list="false"
              :before-upload="uploadQj2"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item label="设备参数" class="formStyle">
          <el-form
            :model="listData"
            ref="tableFormRef"
            label-width="auto"
            :disabled="dlgType == 'info'"
            class="formStyle"
          >
            <el-table :data="listData" class="m-small-table">
              <el-table-column label="序号">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="name" label="参数名" style="padding: 0">
                <template slot-scope="scope">
                  <el-form-item
                    :prop="scope.$index + '.name'"
                    :rules="{
                      required: true,
                      message: '参数名不能为空',
                      trigger: 'blur',
                    }"
                  >
                    <el-input
                      v-model="listData[scope.$index].name"
                      placeholder="请选择参数名"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="value" label="参数值">
                <template slot-scope="scope">
                  <el-form-item
                    :prop="scope.$index + '.value'"
                    :rules="{
                      required: true,
                      message: '参数值不能为空',
                      trigger: 'change',
                    }"
                  >
                    <el-input
                      v-model="listData[scope.$index].value"
                      placeholder="请输入参数值"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column v-if="dlgType !== 'info'">
                <template slot-scope="scope">
                  <el-button
                    @click="delTable(scope.$index)"
                    icon="el-icon-delete"
                    size="mini"
                    type="danger"
                    title="删除"
                    plain
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-form-item>
        <el-form-item>
          <el-button
            v-show="dlgType !== 'info'"
            @click="addList"
            icon="el-icon-plus"
            type="primary"
            plain
            style="margin-top: 10px"
            >添加</el-button
          >
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button
          v-show="dlgType !== 'info'"
          type="success"
          @click="handelConfirm"
          :loading="btnLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
      <!-- 选择员工 -->
      <selectUserDlg
      :dlgState0="dlgUserState"
      :dlgData0="dlgUserData"
      :dlgType="dlgUserType"
      :dlgQuery="dlgUserQuery"
      @closeDlg="closeUserDlg"
      @dlgUserSubFunc="dlgUserSubFunc"
    />
  </div>
</template>
<script>
import * as utils from '@/utils'
import { postAction, getAction, deleteAction } from "@/api";
import { uploadImg } from "@/utils/uploadImg";
import qiniuUpload from "@/views/greenMan/components/qiniuUpload";
import { getDataDict } from "@/utils";
import selectUserDlg from '@/components/Dialog2/selectUserDlg'
import { mapGetters } from "vuex";

export default {
  inheritAttrs: false,
  components: {
    qiniuUpload,
    selectUserDlg,
  },
  props: {
    dlgType: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),
      title: "添加设备",
      dialogVisible: false,
      listData: [{}],
      btnLoading: false,
      formData: {
        equType: undefined,
        projectId: "",
        equName: undefined,
        equModel: undefined,
        controlArea: undefined,
        status: undefined,
        responsiblePersonId: undefined,
        responsiblePersonName: undefined,
        equPosition: undefined,
        leaveTime: null,
        installTime: null,
        useAgeStart: 0,
        useAgeEnd: 0,
        checkCycleStr: undefined,
        picUrl: [],
        drawingUrl: [],
        manufacturer: undefined,
      },
      rules: {
        projectId: [
          {
            required: true,
            message: "请选择项目",
            trigger: "change",
          },
        ],
        equName: [
          {
            required: true,
            message: "请输入设备名称",
            trigger: "blur",
          },
        ],
        equModel: [
          {
            required: true,
            message: "请输入设备型号",
            trigger: "blur",
          },
        ],
        controlArea: [
          {
            required: true,
            message: "请输入设备控制区域",
            trigger: "blur",
          },
        ],
        equType: [
          {
            required: true,
            message: "请选择设备使用类型",
            trigger: "change",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择设备使用状态",
            trigger: "change",
          },
        ],
        responsiblePersonName: [
          {
            required: true,
            message: "请输入设备责任人",
            trigger: "change",
          },
        ],
        equPosition: [
          {
            required: true,
            message: "请输入设备所在位置",
            trigger: "blur",
          },
        ],
        manufacturer: [
          {
            required: true,
            message: "请输入设备生产厂家",
            trigger: "blur",
          },
        ],
        leaveTime: [
          {
            required: true,
            message: "请选择设备出厂时间",
            trigger: "change",
          },
        ],
        installTime: [
          {
            required: true,
            message: "请选择设备安装时间",
            trigger: "change",
          },
        ],
        useAgeStart: [
          {
            required: true,
            message: "请输入参考使用年限",
            trigger: "change",
          },
        ],
        useAgeEnd: [
          {
            required: true,
            message: "请输入参考使用年限",
            trigger: "change",
          },
        ],
        checkCycleStr: [
          {
            required: true,
            message: "请输入设备检测周期",
            trigger: "blur",
          },
        ],
      },
      statusOptions: [],
      equipmentTypeOptions: [],
      projectList: [],
      equPositionList: [],
      dlgUserQuery: {},
      dlgUserState: false,
      dlgUserType: '', // 弹框状态add, edit
      dlgUserData: {},
    };
  },
  computed: {
  },
  watch: {
  },
  created() {},
  mounted() {
    getDataDict(this, "equipmentUsageStatus", "statusOptions");
    getDataDict(this, "equipmentType", "equipmentTypeOptions");
    this.formData.projectId = this.userInfo.projectId;
    this.getEquPositionList(this.formData.projectId);
  },
  methods: {
    showUserDlg() {
      this.dlgUserQuery = ''
      this.dlgUserState = true
    },
     // 关闭弹窗
     closeUserDlg() {
      this.dlgUserState = false
    },
    // 选择员工返回
    dlgUserSubFunc(data) {
      console.log('车辆返回', data)
      if (utils.isNull(data)) return false
      this.formData.responsiblePersonId = data.id
      this.formData.responsiblePersonName = data.label
    },
    // async projectChange(val) {
    //   if (val) {
    //     await this.getEquPositionList(val);
    //   } else {
    //     this.equPositionList = [];
    //     this.searchForm.equPosition = "";
    //   }
    // },
    async getEquPositionList(val) {
      try {
        const res = await getAction(
          `sa/green/equ/equ-position/page?projectId=${val}&equPosition=&pageNo=1&pageSize=1000`
        );
        const { code, data } = res.data;
        if (code === "200") {
          this.equPositionList = data.list || [];
        } else {
          this.$message.error(res.data.msg);
        }
      } catch (error) {
        this.$message.error("获取数据失败");
      }
    },
    onOpen() {},
    onClose() {
      this.listData = [];
      this.$refs["elForm"].resetFields();
    },
    close() {
      this.listData = [];
      this.$refs["elForm"].resetFields();
      this.dialogVisible = false;
      // this.equPositionList = [];
      console.log(this.formData, "formData");
    },
    handelConfirm() {
      if (this.dlgType == "info") {
        this.$refs["elForm"].resetFields();
        this.listData = [];
        this.dialogVisible = false;
        return;
      }
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          this.$refs["tableFormRef"].validate((valid1) => {
            if (valid1) {
              let postData = JSON.parse(JSON.stringify(this.formData));
              if (postData.useAgeStart == 0 || postData.useAgeEnd == 0) {
                this.$message.warning("参考使用年限不能为0！");
                return;
              }
              if (postData.useAgeStart > postData.useAgeEnd) {
                this.$message.warning("参考使用年限最小年限应小于最大年限！");
                return;
              }
              this.btnLoading = true;
              postData.useAge = `${postData.useAgeStart}~${postData.useAgeEnd}`;
              delete postData["useAgeStart"];
              delete postData["useAgeEnd"];
              postData.picUrl = postData.picUrl.toString();
              postData.drawingUrl = postData.drawingUrl.toString();

              this.statusOptions.map((item) => {
                if (item.id == postData.status) {
                  postData.statusStr = item.name;
                }
              });
              this.equipmentTypeOptions.map((item) => {
                if (item.id == postData.equType) {
                  postData.equTypeStr = item.name;
                }
              });
              postData.projectName = this.userInfo.projectName;
              postData.list = this.listData;
              console.log(postData, "postData");
              console.log(this.listData, "listData");
              if (this.dlgType == "edit") {
                postAction(`sa/green/equipment-manage/update`, postData).then(
                  (res) => {
                    this.btnLoading = false;
                    if (res.data.code === "200") {
                      this.$message({
                        type: "success", // success, warning, info, error
                        message: "编辑成功！",
                      });
                      this.dialogVisible = false;
                      this.$parent.searchFunc();
                      this.$refs.elForm.resetFields();
                    } else {
                      this.$message.error(res.data.msg);
                    }
                  }
                );
              } else {
                if (postData.id) {
                  delete postData.id;
                }
                postAction(`sa/green/equipment-manage/create`, postData).then(
                  (res) => {
                    this.btnLoading = false;
                    if (res.data.code === "200") {
                      this.$message({
                        type: "success", // success, warning, info, error
                        message: "添加成功！",
                      });
                      this.dialogVisible = false;
                      this.$parent.searchFunc();
                      this.$refs.elForm.resetFields();
                    } else {
                      this.$message.error(res.data.msg);
                    }
                  }
                );
              }
            } else {
              return;
            }
          });
        } else {
          return;
        }
      });
    },

    uploadQj1(file) {
      if (file.size > 5 * 1024 * 1024) {
        this.$message({
          type: "warning",
          message: "上传图片大小不能超过5M",
        });
        return false;
      }
      uploadImg(file, "ERP_web/greenMan/bch/bch_").then((res) => {
        this.formData.picUrl.push(res);
      });
    },
    delUploadImgByArr(index) {
      this.$confirm("是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning", // // success, warning, info, error
      }).then(() => {
        this.$message({
          type: "success", // success, warning, info, error
          message: "删除成功",
        });
        let formData = this.formData;
        formData.picUrl.splice(index, 1);
        this.formData = JSON.parse(JSON.stringify(formData));
      });
    },
    uploadQj2(file) {
      if (file.size > 5 * 1024 * 1024) {
        this.$message({
          type: "warning",
          message: "上传图片大小不能超过5M",
        });
        return false;
      }
      uploadImg(file, "ERP_web/greenMan/bch/bch_").then((res) => {
        this.formData.drawingUrl.push(res);
      });
    },
    delUploadImgByArr1(index) {
      this.$confirm("是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning", // // success, warning, info, error
      }).then(() => {
        this.$message({
          type: "success", // success, warning, info, error
          message: "删除成功",
        });
        let formData = this.formData;
        formData.drawingUrl.splice(index, 1);
        this.formData = JSON.parse(JSON.stringify(formData));
      });
    },
    successBack(fileList) {
      this.formData.fileList = fileList;
    },
    addList() {
      this.listData.push({});
    },
    delTable(row) {
      this.listData.splice(row, 1);
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-dialog {
  /* 表格input不对齐 */
  .formStyle .el-form-item {
    margin: 0;
  }

  /* “设备参数”和表格对齐 */
  .formStyle .el-form-item__label {
    margin-top: 7px;
  }
}
</style>
