<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>

        <el-form-item label="选择小区" prop="communityId">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="费用类型：">
          <el-select
            v-model="listQuery.feeType"
            filterable
            clearable
            placeholder="请选择费用类型"
          >
            <el-option v-for="item in costTypeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="年度">
          <el-date-picker :picker-options="pickerOptions" v-model="listQuery.year" value-format="yyyy" format="yyyy" type="year" placeholder="年度">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入缴费对象' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>

        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <el-form inline>
      <el-form-item label="应收金额:">
        {{totalItem.yingshouSum || 0}} 元
      </el-form-item>
      <el-form-item label="实收金额:">
        {{totalItem.shishouSum || 0}} 元
      </el-form-item>
    </el-form>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="收费对象">
          <template slot-scope="scope">
            <span v-if="scope.row.payType == 1">房屋：{{ scope.row.roomName }}</span>
            <span v-if="scope.row.payType == 2">车位：{{ scope.row.parkingName }}</span>
            <span v-if="scope.row.payType == 3">车库：{{ scope.row.garageName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="费用项">
          <template slot-scope="scope">
            <div>{{ scope.row.configName }}</div>
          </template>
        </el-table-column>

        <el-table-column label="费用开始时间">
          <template slot-scope="scope">
            <span>{{ scope.row.startDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="费用结束时间">
          <template slot-scope="scope">
            <span>{{ scope.row.endDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="缴费时间">
          <template slot-scope="scope">
            <span>{{ scope.row.payTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="应收金额(单位:元)">
          <template slot-scope="scope">
            <span>{{ scope.row.amount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="实收金额(单位:元)">
          <template slot-scope="scope">
            <span>{{ scope.row.shishoujine }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'

import {
  jiaofeimingxi,
} from '@/api/reportMan'

import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {

}
export default {
  name: 'payDetail',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {

      pickerOptions: {
        disabledDate (time) {
          return time.getTime() > Date.now() - (1000 * 60 * 60 * 24);
        },
      },
      // 弹窗 类型
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        danyuanId: [{ required: true, message: '必填字段', trigger: 'change' }],
        fangwuId: [{ required: true, message: '必填字段', trigger: 'change' }],
        loudongId: [{ required: true, message: '必填字段', trigger: 'change' }],
        valuateUserName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        valuateType: [{ required: true, message: '必填字段', trigger: 'change' }],
        valuateContent: [{ required: true, message: '必填字段', trigger: 'blur' }],
        valuateGenzong: [{ required: true, message: '必填字段', trigger: 'blur' }],
        valuatePhone: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: '号码格式有误！',
            trigger: 'blur'
          }
        ],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        year: '',
        feeType:"",
      },
      costTypeList:[],//费用类型字典
      totalItem: {},
      userInfo: {},
      communityList: [],
      buildingList: [],
      unitList: [],
      roomList: [],
      typeList: [
        {
          id: 1,
          name: '投诉'
        },
        {
          id: 2,
          name: '建议'
        },
      ],
    }
  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.listQuery.year = new Date().getFullYear().toString()
    utils.getDataDict(this, "costType", "costTypeList");
  },

  methods: {
    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    formatList () {
      for (let i of this.list) {
        i.communityName = utils.getNameById(this.listQuery.communityId, this.communityList)
      }
    },

    // 获取数据
    getList () {
      // if (utils.isNull(this.listQuery.communityId)&&utils.isNull(this.listQuery.feeType)&&utils.isNull(this.listQuery.label)) {
      //   this.$message.warning("请选择小区")
      //   return
      // }
      if (utils.isNull(this.listQuery.year)) {
        this.$message.warning("请选择年度")
        return
      }
      this.count++
      this.listLoading = true
      jiaofeimingxi(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.formatList()
          this.total = res.data.page ? res.data.page.total : 0
          this.totalItem = res.data.dataMap ? res.data.dataMap : {}
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.table-container {
  height: calc(100% - 150px);
}
</style>


