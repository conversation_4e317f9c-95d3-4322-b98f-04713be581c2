import request from '@/utils/request'

/*
* 陪护床管理
*/

// 动态查询陪护床信息 
export function findBedInfoDynamic (data) {
  return request({
    url: `/u/usapi/ph/findBedInfoDynamic`,
    method: 'post',
    data
  })
}

// 新增修改陪护床信息
export function saveOrUBedInfo (data) {
  return request({
    url: `/u/usapi/ph/saveOrUBedInfo`,
    method: 'post',
    data
  })
}

// 修改陪护床状态
export function updateBedFlag (data) {
  return request({
    url: `/u/usapi/ph/updateBedFlag`,
    method: 'post',
    data
  })
}

// 查询陪护床使用详情
export function findUseInfoByDvName (data) {
  return request({
    url: `/u/usapi/ph/findUseInfoByDvName`,
    method: 'post',
    data
  })
}

// 动态查询陪护床订单
export function findBedOrderDynamic (data) {
  return request({
    url: `/u/usapi/ph/findBedOrderDynamic`,
    method: 'post',
    data
  })
}

// 订单退款
export function bedOrderRefund (data) {
  return request({
    url: `/u/usapi/ph/bedOrderRefund`,
    method: 'post',
    data
  })
}

// 按陪护床id查询使用价格
export function findBedPrice (data) {
  return request({
    url: `/u/usapi/ph/findBedPrice`,
    method: 'post',
    data
  })
}

// 新增修改陪护床使用价格
export function saveOrUBedPrice (data) {
  return request({
    url: `/u/usapi/ph/saveOrUBedPrice`,
    method: 'post',
    data
  })
}

// 动态查询陪护床使用记录
export function findBedUseInfoDynamic (data) {
  return request({
    url: `/u/usapi/ph/findBedUseInfoDynamic`,
    method: 'post',
    data
  })
}

// 动态查询陪护床使用记录
export function udpateUseStatus (id, useStatus) {
  return request({
    url: `/u/usapi/ph/udpateUseStatus/${id}/${useStatus}`,
    method: 'get'
  })
}

// 代送被褥冒泡
export function daisongdamaopao (id) {
  return request({
    url: `/u/usapi/ph/daisongdamaopao/${id}`,
    method: 'get'
  })
}