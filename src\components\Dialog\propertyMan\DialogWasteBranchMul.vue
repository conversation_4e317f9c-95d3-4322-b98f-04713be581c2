<template>
  <el-dialog :close-on-click-modal="false" :title="'选择医废暂存点'" :visible.sync="dlgShow" @close="closeDlg">
    <el-input placeholder="输入名称进行过滤" v-model="filterText"> </el-input>
    <el-tree ref="treeDom" show-checkbox highlight-current node-key="id" @check="treeCheck" :check-strictly="true" :data="list" :default-expanded-keys="defaultOpenList" :filter-node-method="filterNode" :expand-on-click-node="false">
    </el-tree>
    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-back" @click="closeDlg">
        取 消
      </el-button>
      <el-button icon="el-icon-delete" type="danger" @click="clearDlg">
        清 空
      </el-button>
      <el-button icon="el-icon-check" type="success" @click="submitDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'
import * as utils from '@/utils'
import { findBranchTreeByProject } from '@/api/propertyMan/wasteMaintenance.js'

export default {
  components: {
  },

  props: {
    superDlgShow: {
      type: Boolean,
      required: true,
      default: false,
    },

    superDlgType: {
      type: String,
      required: false,
      default: '',
    },

    superProjectId: {
      type: Number | String,
      required: false,
      default: ''
    },

    superSelectIds: {
      type: String,
      required: false,
      default: ''
    },

    superSelectNames: {
      type: String,
      required: false,
      default: ''
    },
  },

  data () {
    return {
      dlgShow: this.superDlgShow,

      defaultOpenList: [],

      list: [],

      filterText: '',

      selectKeys: [],

      selectList: [],

      selectIds: '',

      selectNames: ''
    }
  },

  computed: {


  },

  watch: {
    filterText (val) {
      this.$refs.treeDom.filter(val);
    },

    superDlgShow: {
      immediate: true,
      handler (val) {
        this.dlgShow = val
        if (val) {
          this.selectIds = this.superSelectIds
          this.selectNames = this.superSelectNames
          this.selectKeys = []
          this.selectList = []
          this.getList()
        }
      }
    },

  },

  methods: {
    filterNode (value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 递归全选当前下的节点
    selectAllNode (childrenList, type) {
      for (let item of childrenList) {
        // 全选，全部取消
        if (type == 'select') {
          if (!this.selectKeys.includes(item.id)) {
            this.selectKeys.push(item.id)
            this.selectList.push(item)
          }
        } else {
          if (this.selectKeys.includes(item.id)) {
            let mIndex = this.selectKeys.indexOf(item.id)

            this.selectKeys.splice(mIndex, 1)
            this.selectList.splice(mIndex, 1)
          }
        }
        if (item.children) {
          this.selectAllNode(item.children, type)
        }
      }
    },

    // 树节点点击
    treeCheck (checkedNodes, checkedKeys) {
      if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        // select-全选；remove-取消全选
        this.selectAllNode(checkedNodes.children, 'select')
      } else {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        this.selectAllNode(checkedNodes.children, 'remove')
      }
      this.$refs.treeDom.setCheckedKeys(this.selectKeys)
    },

    setCheckedKeys () {
      if (utils.isNull(this.selectIds)) {
        this.$refs.treeDom.setCheckedKeys([])
        return
      }
      this.$nextTick(() => {
        let currentKey = []
        let selectIdList = this.selectIds.split(",")
        for (let i in selectIdList) {
          currentKey.push(parseInt(selectIdList[i]))
        }
        this.$refs.treeDom.setCheckedKeys(currentKey)
      })
    },

    getList () {
      this.list = []
      findBranchTreeByProject(this.superProjectId).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.list
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = this.list.length > 0 ? [this.list[0]['id']] : []
          }
          this.setCheckedKeys()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    submitDlg () {
      let checkNodes = this.$refs.treeDom.getCheckedNodes()
      let selectIdList = []
      let selectNameList = []
      for (let i of checkNodes) {
        selectIdList.push(i['id'])
        selectNameList.push(i['label'])
      }
      this.selectIds = selectIdList.join(",")
      this.selectNames = selectNameList.join(",")
      let superParam = {
        selectIds: this.selectIds,
        selectNames: this.selectNames
      }
      this.$emit('superFunc', superParam)
      this.closeDlg()
    },

    clearDlg () {
      this.selectIds = ""
      this.selectNames = ""
      this.setCheckedKeys()
    },

    closeDlg () {
      this.$emit('update:superDlgShow', false)
    }


  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 800px;

  .el-dialog__body {
    height: calc(100% - 110px);

    .el-tree {
      margin-top: 10px;
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
}
</style>