<template>
  <el-dialog :close-on-click-modal="false" :title="'选择部门'" :visible.sync="dlgShow">
    <el-input placeholder="输入部门名称进行过滤" v-model="filterBranch"> </el-input>
    <!-- :default-expanded-keys="defaultOpenList" -->
    <el-tree ref="branchTree" highlight-current node-key="id" :data="list" @node-click="treeNodeClick" default-expand-all :filter-node-method="filterNode" :expand-on-click-node="false">
    </el-tree>

    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-span" v-if="selectBranchName">当前选中：{{ selectBranchName }}</span>
      <el-button icon="el-icon-back" @click="closeDlg"> 取 消 </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'

import { findBranchTreeByProject } from '@/api/propertyMan/deptMaintenance.js'

export default {
  components: {},
  data () {
    return {
      filterBranch: '',

      list: [],

      defaultOpenList: [],

      selectBranchId: '',

      selectBranchName: '',
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.propertyMan.branchDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('propertyMan/branchDlg/SET_DLGSHOW', val)
      },
    },

    branchId: {
      get: function () {
        return this.$store.state.propertyMan.branchDlg.branchId
      },
      set: function (val) {
        this.$store.commit('propertyMan/branchDlg/SET_BRANCHID', val)
      },
    },

    branchName: {
      get: function () {
        return this.$store.state.propertyMan.branchDlg.branchName
      },
      set: function (val) {
        this.$store.commit('propertyMan/branchDlg/SET_BRANCHNAME', val)
      },
    },

    projectId: {
      get: function () {
        return this.$store.state.propertyMan.branchDlg.projectId
      },
      set: function (val) {
        this.$store.commit('propertyMan/branchDlg/SET_PROJCETID', val)
      },
    },
  },

  watch: {
    filterBranch (val) {
      this.$refs.branchTree.filter(val)
    },

    dlgShow (val) {
      if (val) {
        if (utils.isNull(this.branchId)) {
          this.selectBranchId = ''
          this.selectBranchName = ''
        }
        this.getList()
      }
    },

    branchId (val) {
      this.selectBranchId = val
    },

    branchName (val) {
      this.selectBranchName = val
    },

    projectId (val) { },
  },

  methods: {
    filterNode (value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },

    treeNodeClick (data) {
      this.selectBranchId = data.id
      this.selectBranchName = data.label
    },

    getList () {
      this.list = []
      findBranchTreeByProject(this.projectId).then((res) => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let list = res.data.list
          this.list = list
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = [list[0]['id']]
          }
          this.$nextTick(() => {
            this.$refs.branchTree.setCurrentKey(this.selectBranchId)
          })
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg () {
      this.branchId = this.selectBranchId
      this.branchName = this.selectBranchName
      this.$store.commit('propertyMan/branchDlg/SET_BRANCHID', this.branchId)
      this.$store.commit('propertyMan/branchDlg/SET_BRANCHNAME', this.branchName)
      this.closeDlg()
    },

    closeDlg () {
      this.$store.commit('propertyMan/branchDlg/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-tree {
  margin-top: 10px;
  height: 500px;
  overflow-y: auto;
}
</style>