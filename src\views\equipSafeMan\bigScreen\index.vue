<template>
  <div class="app-container">
    <iframe 
      :src="iframeSrc" 
      frameborder="0" 
      style="width: 100%; height: 100%;"
    ></iframe>
  </div>
</template>

<script>
import <PERSON><PERSON> from "js-cookie";

export default {
  data() {
    return {
      loading: true,
      iframeSrc: '' // 用于存储 iframe 的 src 属性值
    };
  },
  created() {
    let token = Cookie.get("Token");
    let userInfo = JSON.parse(window.localStorage.userInfo);
    window.localStorage.DR_ACCESS_TOKEN = token; // 必传
    window.localStorage.DR_USER_TYPE = "jyt1"; // staff-龙行员工  man-龙行管理者 jyt1-龙行云  jyt2-简E通2.0
    window.localStorage.DR_USER_ID = userInfo.id;
    
    // 设置 iframe 的 src 属性值
    this.iframeSrc = "https://longxingcloud.cn/screen/#/bigscreen/preview?code=bigScreen_gwsLXpDE17";
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-container {
  padding: 0;
  iframe {
    width: 100%;
    height: 100%;
    vertical-align: top;
    border: none;
  }
}
</style>