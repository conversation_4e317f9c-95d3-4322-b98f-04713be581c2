<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="归属小区：" prop="communityId">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="归属车库：">
          <el-input @keyup.enter.native='getList' placeholder='请输入车库编号' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>批量创建</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车库编号">
          <template slot-scope="scope">
            <span>{{ scope.row.numStr }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车库类型">
          <template slot-scope="scope">
            <span>{{ scope.row.typeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="区域">
          <template slot-scope="scope">
            <span>{{ scope.row.area }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车库状态" align="center">
          <template slot-scope="scope">
            <span class="fsuccess" v-if="scope.row.state == 1">已售</span>
            <span class="fwarning" v-else>未售</span>
          </template>
        </el-table-column>

        <el-table-column label="业主">
          <template slot-scope="scope">
            <span>{{ scope.row.ownerName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="电话">
          <template slot-scope="scope">
            <span>{{ scope.row.ownerPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">欠费催缴</el-button>
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="viewItem(scope.row)">查看费用</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="创建缴费" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="100px">

        <el-form-item label="收费范围" prop="buildType">
          <el-select  @change="buildTypeChange" v-model="dlgData.buildType" filterable clearable placeholder="请选择收费范围">
            <el-option v-for="item in buildTypeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="归属小区" prop="communityId" v-if="dlgData.buildType >= 1">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="归属车库" prop="garageId" v-if="dlgData.buildType >= 4">
          <el-select v-model="dlgData.garageId" filterable clearable placeholder="请选择车库">
            <el-option v-for="item in garageList" :key="item.id" :label="item.numStr" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="费用类型" prop="feeType">
          <el-select v-model="dlgData.feeType" filterable clearable placeholder="请选择费用类型" @change="feeTypeChange">
            <el-option v-for="item in costTypeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="收费项目" prop="configId">
          <el-select v-model="dlgData.configId" filterable clearable placeholder="请选择收费项目" @change="configChange">
            <el-option v-for="item in configList" :key="item.id" :label="item.feeName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="费用标识" prop="feeFlag">
          <el-select v-model="dlgData.feeFlag" disabled filterable clearable placeholder="请选择费用标识">
            <el-option v-for="item in feeFlagList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="车库状态">
          <el-checkbox-group v-model="dlgData.state">
            <el-checkbox label="0">未售</el-checkbox>
            <el-checkbox label="1">已售</el-checkbox>
            <el-checkbox label="2">出租</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="dlgData.communityId" label="房产类型"  prop="roomTypeIds">
          <el-checkbox-group v-model="dlgData.roomTypeIds">
            <el-checkbox v-for="(item,index) of houseTypeList" :key="index" :label="item.id">{{item.name}}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="缴费日期" prop="rangeDate" v-if="dlgData.feeFlag == 1">
          <el-radio-group v-model="dlgData.dateType" @change="radioChange">
            <el-radio-button label="1">按天</el-radio-button>
            <el-radio-button label="2">按月</el-radio-button>
          </el-radio-group>
          <el-date-picker :key="1" style="width:300px;" v-if="dlgData.dateType == 1" v-model="dlgData.rangeDate" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
          <el-date-picker :key="3" v-else v-model="dlgData.rangeDate" type="monthrange" range-separator="~" format="yyyy-MM" value-format="yyyy-MM" start-placeholder="开始月份" end-placeholder="结束月份">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="缴费日期" prop="rangeDate" v-if="dlgData.feeFlag == 2">
          <el-date-picker :key="2" v-model="dlgData.rangeDate" type="daterange"    
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期">
          </el-date-picker>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, cofloorCommunity, buildingunitFloor, cogaragePage, roomTypePage } from '@/api/communityMan'
import { payFeeConfigPage, addPayFeeBills, getPayBillPage } from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  buildType: '',
  configId: '',
  configName: '',
  startDate: '',
  endDate: '',
  feeFlag: '',
  feeType: '',
  payType: '3',
  communityId: '',
  garageId: '',
  garageName: '',
  roomType: '',
  state: [],
  rangeDate: '',
  dateType: '1',
  roomTypeIds: [],
}


export default {
  name: 'garagePay',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {
      houseTypeList: [],
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        buildType: [{ required: true, message: '必填字段', trigger: 'change' }],
        roomId: [{ required: true, message: '必填字段', trigger: 'change' }],
        unitId: [{ required: true, message: '必填字段', trigger: 'change' }],
        floorId: [{ required: true, message: '必填字段', trigger: 'change' }],
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        garageId: [{ required: true, message: '必填字段', trigger: 'change' }],
        roomType: [{ required: true, message: '必填字段', trigger: 'change' }],
        feeFlag: [{ required: true, message: '必填字段', trigger: 'change' }],
        feeType: [{ required: true, message: '必填字段', trigger: 'change' }],
        configId: [{ required: true, message: '必填字段', trigger: 'change' }],
        rangeDate: [{ required: true, message: '必填字段', trigger: 'change' }],
        startDate: [{ required: true, message: '必填字段', trigger: 'change' }],
        roomTypeIds:  [{ required: true, message: '必填字段', trigger: 'change' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        floorId: '',
        unitId: ''
      },
      buildTypeList: [
        {
          id: '1',
          name: '小区'
        },
        {
          id: '4',
          name: '车库'
        },
      ],
      feeFlagList: [
        {
          id: '1',
          name: '周期性费用'
        },
        {
          id: '2',
          name: '一次性费用'
        },
      ],
      configList: [],
      communityList: [],
      garageList: [],
      userInfo: {},
      // 字典
      costTypeList: [],
    }
  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    utils.getDataDict(this, 'costType', 'costTypeList')
    this.getList()
  },

  methods: {
    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },

    communityChange () {
      this.dlgData.configId = ''
      this.dlgData.garageId = ''
      this.garageList = []
      this.configList = []
      this.getConfigList()
      this.getGarageList()

      this.getRoomType(this.dlgData.communityId);
    },
    getRoomType(communityId) {
      if(communityId){
        let postParam = {
          page: 1,
          limit: 200,
          communityId:communityId
        };
        roomTypePage(postParam).then((res) => {
          if (res.data.code == 200) {
            this.houseTypeList = res.data.data;
          }
        });
        }
    },

    buildTypeChange () {
      this.getGarageList()
    },

    feeTypeChange () {
      this.dlgData.configId = ''
      this.configList = []
      this.getConfigList()
    },

    configChange () {
      let configItem = this.configList.filter(item => {
        return item.id == this.dlgData.configId
      })
      if (configItem.length > 0) {
        this.dlgData.feeFlag = configItem[0]['feeFlag']
      }
    },

    // 获取小区收费项
    getConfigList () {
      let postParam = {
        page: 1,
        limit: 20,
        communityId: this.dlgData.communityId,
        feeTypeCd: this.dlgData.feeType
      }
      payFeeConfigPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.configList = JSON.parse(JSON.stringify(res.data.data))
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取车库列表
    getGarageList () {
      if (this.dlgData.buildType != 4) {
        return
      }
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.dlgData.communityId,
      }
      cogaragePage(postParam).then(res => {
        if (res.data.code == 200) {
          this.garageList = res.data.data
        }
      })
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      cogaragePage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    addItem () {
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      // this.dlgData.communityId = this.listQuery.communityId
      this.garageList = []
      this.houseTypeList = []
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.roomTypeIds = postParam.roomTypeIds.join(',')
          postParam.projectId = this.userInfo.projectId
          postParam.configName = utils.getNameById(postParam.configId, this.configList, 'id', 'feeName')
          postParam.state = postParam.state.join(",")
          if (postParam.feeFlag == 1) {
            if (postParam.dateType == 1) {
              postParam.startDate = postParam.rangeDate[0]
              postParam.endDate = postParam.rangeDate[1]
            } else {
              postParam.startDate = postParam.rangeDate[0] + '-01'
              postParam.endDate = postParam.rangeDate[1] + '-' + utils.getMaxDate(postParam.rangeDate[1])
            }
          }
          //  else {
          //   postParam.endDate = postParam.startDate
          // }
          if (postParam.feeFlag == 2) {
            postParam.startDate = postParam.rangeDate[0]
            postParam.endDate = postParam.rangeDate[1]
          }
          this.dlgLoading = true
          addPayFeeBills(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data) {
      let postParam = {
        page: 1,
        limit: 99,
        isOver: '1',
        status: '0',
        garageId: data.id,
        payType: '3'
      }
      getPayBillPage(postParam).then(res => {
        if (res.data.code == 200) {
          if (res.data.data.length == 0) {
            this.$message.warning('无缴费信息')
          } else {
            this.$router.push({ path: `/communityProperty/costMan/payCost/${data.id}/${dlgDataEmpty.payType}` })
          }
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 启用停用
    viewItem (data, flag) {
      this.$router.push({ path: `/communityProperty/costMan/payCostDesc/${data.id}/${dlgDataEmpty.payType}` })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


