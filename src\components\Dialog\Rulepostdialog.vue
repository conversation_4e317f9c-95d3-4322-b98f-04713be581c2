<template>
  <div class="">
    <!-- 供应链 弹窗 多选商品 -->
    <el-dialog :close-on-click-modal='false' title="选择商品" :append-to-body='true' :visible.sync="gylGoodsState" width='900px' top='30px' icon-class='el-icon-info'>
      <div class="dialog-bms-bar">
        <!-- 部门 -->
        <div class='dialog-bms-left'>
          <el-input placeholder="输入关键字进行过滤" style="margin-bottom: 10px;" v-model="bmsText">
          </el-input>
          <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
          <div class="bms-tree">
            <el-tree :data="bmsData" ref="bmsDom" default-expand-all :filter-node-method="bmsFilter" :props="defaultProps" @node-click="bmsClick">
            </el-tree>
          </div>
        </div>
        <div class='dialog-bms-right'>

          <div>
            <el-popover class='fr popDom' placement="bottom-end" width="800" @show="showPopover" trigger="click">
              <el-table ref="returnListRef" :data="returnList">
                <el-table-column type='index' width='50' align="center"></el-table-column>
                <el-table-column property="name" label="商品名称"></el-table-column>
                <el-table-column property="bm" width="160" label="编码"></el-table-column>
                <el-table-column property="typeName" label="分类"></el-table-column>
                <el-table-column property="" label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button @click="popRemoveRow(scope.row)" type="danger" size="mini" icon="el-icon-delete" plain>删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-button class="fr search-right-btn" type="success" slot="reference" icon='el-icon-arrow-down'>查看已选</el-button>
            </el-popover>

            <el-button class='fr search-right-btn' icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
            <el-input @change='getList' class='m-shaixuan-input fr' placeholder='商品名称' style="width:200px" v-model="listQuery.commodityName">
              <i @click='resetSearchItem(["commodityName"])' slot="suffix" class="el-input__icon el-icon-error"></i>
            </el-input>
            <div class="clear"></div>
          </div>

          <!-- 表格 -->
          <el-table ref="multipleTable" class='m-small-table mt10' v-loading="listLoading" :key="10210811" :data="list" border fit highlight-current-row style="width: 100%;" tooltip-effect="dark" @select="tableSelectChange" @select-all='tableSelectAll' :default-sort="{prop: 'aaa', order: 'descending'}">

            <el-table-column fixed align='center' type="selection" width="55">
            </el-table-column>

            <el-table-column label="商品名称">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="商品编码">
              <template slot-scope="scope">
                <span>{{ scope.row.bm }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
          <div class="clear"></div>

        </div>
        <!-- 部门多选框 -->
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import { findOrgBranchAll } from '@/api/dataDic'

import {
  getPostsByConfigId,  // 获取树
} from '@/api/attendanceMan'

import Pagination from '@/components/Pagination' // Secondary package based on el-pagination

let listQueryEmpty = {
  configId: '',  // 规则id
  payRuleType: '',  // 1:缺卡规则 2:旷工规则 3:迟到早退规则

}

export default {
  components: { Pagination },
  data() {
    return {
      // 部门树
      bmsData: [],
      bmsSelect: {},
      bmsText: '',  // 部门左侧筛选

      // 右侧表格
      // {id, bm, typeId, name}  bm-编码
      list: [
        { id: 11, bm: '0001', name: '商品1' },
        { id: 12, bm: '0002', name: '商品2' },
        { id: 13, bm: '0003', name: '商品3' },
      ],
      listLoading: false,
      total: 0,
      tableSelectList: '',  // 复选
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      returnList: [],  // 返回的列表集合
      // 树过滤
      defaultProps: {
        children: 'children',
        label: 'label',
      }

    }
  },
  computed: {
    ...mapGetters([
      'rulePostSet',
      'rulePostQuery'
    ]),

    rulePostState: {
      get: function () {
        let state = this.$store.getters.rulePostState
        if (state == false) {
          this.returnList = []
        } else {
          setTimeout(() => {
            this.listQuery = this.$store.getters.rulePostQuery
            this.getList()
          }, 50)
        }
        return state
      },
      set: function (newVal) {
        this.$store.commit('SET_RULEPOSTSTATE', newVal)
      }
    },

  },
  watch: {
    bmsText(val) {
      this.$refs.bmsDom.filter(val);
    },
    rulePostSet(val) {
      this.returnList = JSON.parse(JSON.stringify(val))
    }
  },
  created() {
    // this.findOrgBranchAll()

    setTimeout(() => {

    }, 1000)
  },
  methods: {
    // [[ 新的
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },
    // 获取商品分类 树
    getTree() {
      cacfsTree().then(res => {
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code === '200') {
          this.bmsData = JSON.parse(JSON.stringify(res.data.list))
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 节点左键点击事件
    bmsClick(data, node, mNode) {
      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      let relationId = data.id
      // 获取分类下商品
      this.listQuery = JSON.parse(JSON.stringify(listQueryEmpty))
      this.listQuery.id = relationId
      this.getList(relationId)

    },
    // 获取分类下商品
    getList() {
      this.listLoading = true
      getPostsByConfigId(this.listQuery).then(res1 => {
        this.listLoading = false
        let res = res1.data

        if (res.code === '200') {
          this.total = res.data.total
          let list = JSON.parse(JSON.stringify(res.list))
          for (let i = 0; i < list.length; i++) {
            let item = list[i]
            item.mIndex = (this.listQuery.page - 1) * this.listQuery.limit + i + 1

            let isSelect = this.returnList.some(item2 => {
              item2.id == item.id
            })

            if (isSelect) {
              this.$refs.multipleTable.toggleRowSelection(item, true);
            }

          }
          this.list = JSON.parse(JSON.stringify(list))


        } else {
          this.$message({
            type: 'error',  // success, warning, info, error
            message: res.msg
          });
        }

      })

    },
    // 表格复选框
    tableSelectChange(arr, row) {
      let checkedState = false  // true-勾选状态，false-取下选择状态
      for (let item of arr) {
        if (item.id == row.id) {
          checkedState = true
          break;
        }
      }
      // 判断是否是勾选状态
      if (checkedState) {
        this.returnList.push(row)
        console.log('勾选', this.returnList)
      } else {
        let returnList = this.returnList.filter(item => {
          return item.id != row.id
        })
        this.returnList = JSON.parse(JSON.stringify(returnList))
        console.log('取消选择', returnList)
      }
    },
    // 表格 全选
    tableSelectAll(arr) {
      let len = arr.length
      console.log(arr.length)
      // 长度为0 取消全选，将list中所有数据，从returnList中移除
      // 长度不为0，全选，将list中所有数据，追加到 returnList中

      let list = JSON.parse(JSON.stringify(this.list))
      let returnList = JSON.parse(JSON.stringify(this.returnList))
      if (len == 0) {
        let newList = []
        for (let item of returnList) {
          let hasId = list.some(item2 => {
            return item2.id == item.id
          })
          if (!hasId) {
            newList.push(item)
          }
        }
        returnList = JSON.parse(JSON.stringify(newList))
      } else {
        for (let item of list) {
          let hasId = returnList.some(item2 => {
            return item2.id == item.id
          })
          if (!hasId) {
            returnList.push(item)
          }
        }
      }

      console.log('完美的选中数据', returnList)
      this.returnList = returnList
    },

    // 已选框
    showPopover() {
      for (let item of this.returnList) {
        this.$refs.returnListRef.toggleRowSelection(item, true);
      }
    },
    // 弹窗 复选框改变事件
    // 删除库
    popRemoveRow(row) {
      let returnList = this.returnList.filter(item => {
        return item.id != row.id
      })
      this.returnList = JSON.parse(JSON.stringify(returnList))

      // 选择商品弹窗，复选框同步改变
      for (let item of this.list) {
        let isHas = this.returnList.some(item2 => {
          return item2.id == item.id
        })
        if (isHas) {
          this.$refs.multipleTable.toggleRowSelection(item, true);
        } else {
          this.$refs.multipleTable.toggleRowSelection(item, false);
        }
      }

    },
    // 表格多选重置
    tableCheckFunc() {
      let list = this.list
      for (let i = 0; i < list.length; i++) {
        let item = list[i]

        let isSelect = this.returnList.some(item2 => {
          item2.id == item.id
        })

        if (isSelect) {
          this.$refs.multipleTable.toggleRowSelection(item, true);
        }

      }
      this.list = JSON.parse(JSON.stringify(list))
    },
    // 选择部门提交
    bumenOkFunc() {
      console.log('返回数据', this.returnList)
      this.$store.commit('SET_RULEPOSTSET', 'empty')

      setTimeout(() => {
        this.$store.commit('SET_RULEPOSTSET', this.returnList)
        this.closeDialog()
      }, 50)
    },
    // ]] 新的
    ////////////////
    // 【【 左侧相关
    // 获取部门
    findOrgBranchAll() {
      findOrgBranchAll().then(res => {
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code === '200') {
          this.bmsData = JSON.parse(JSON.stringify(res.data.list))
        } else {
          this.$message.error(msg)
        }
      })
    },

    // 筛选部门
    bmsFilter(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 】】 左侧相关

    // 【【 右侧相关
    // 右侧删除部门方法
    delBmFunc(relationId, relationName) {
      let nArr = this.bmsArr2.filter(item => {
        return item.relationId !== relationId
      })
      this.bmsArr2 = JSON.parse(JSON.stringify(nArr))
    },
    // 】】 右侧相关

    // 【【 其他

    // 关闭弹窗 
    closeDialog() {
      this.$store.commit('SET_RULEPOSTSTATE', false)
    }
    // 】】 其他



    // 筛选岗位
    // filterBmrightListOld
    // filterGangwei(val) {
    //   this.filterBmrightList = JSON.parse(JSON.stringify(this.filterBmrightListOld))
    //   this.filterBmrightList = this.filterBmrightList.filter(item => {
    //     return item.label.indexOf(val) >= 0
    //   })
    // },


  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dialog-bms-bar {
  display: flex;
  justify-content: space-between;
}
.dialog-bms-left {
  width: 360px;
  margin-right: 10px;
}
// .dialog-bms-left,.dialog-bms-right {
//   width: 49%;
// }
.bms-tree {
  height: 400px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
}
.dialog-bms-right {
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 10px;
  flex: 1;
  .bms-a {
    margin-bottom: 10px;
    margin-left: 0px;
    margin-right: 6px;
  }
  i {
    display: inline-block;
    margin-left: 3px;
  }
}

// .popDom.el-table .cell {
//   height: 23px;
// }
</style>