<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入管家名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>新增管家</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="管家姓名">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="管家电话">
          <template slot-scope="scope">
            <span>{{ scope.row.phone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')">详情</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row, 'EDIT')">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑管家信息" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :disabled="dlgType == 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="所在小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="管家姓名" prop="name">
          <el-input v-model="dlgData.name" @focus="showUserDlg" placeholder="请选择员工" readonly></el-input>
        </el-form-item>

        <el-form-item label="管家电话" prop="phone">
          <el-input v-model="dlgData.phone" placeholder="请输入电话" />
        </el-form-item>

        <el-form-item label="照片">
          <el-upload class="avatar-uploader" action='' :show-file-list="false" :before-upload="beforeUpload">
            <el-image v-if="dlgData.photo" class='upload-img' :src="dlgData.photo" alt=""></el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <i v-if="dlgType !== 'VIEW' && dlgData.photo" @click.stop='delUploadImg()' class="el-icon-error avatar_icon"></i>
          </el-upload>
        </el-form-item>

        <el-form-item label="管理区域" prop="floorList">
          <el-select v-model="dlgData.floorList" multiple filterable clearable placeholder="请选择楼栋">
            <el-option v-for="item in buildingList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.remark" placeholder="请输入备注" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <userDlg />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, cofloorCommunity, cohousekeeperPage, cohousekeeperAddOrUpdate, cohousekeeperDisable, cohousekeeperInfo } from '@/api/communityMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import userDlg from '@/components/Dialog/communityMan/userDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  communityId: '',
  communityName: '',
  floorList: [],
  name: '',
  phone: '',
  userInfo: {},
  photo: '',
  remark: '',
  staffId: '',
}


export default {
  name: 'stewardInfo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    userDlg
  },
  data () {
    return {
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'blur' }],
        name: [{ required: true, message: '必填字段', trigger: 'change' }],
        floorList: [{ required: true, message: '必填字段', trigger: 'change' }],
        phone: [
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: '报修电话格式有误！',
            trigger: 'blur'
          }
        ],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        state:0
      },
      communityList: [],
      buildingList: [],
      userInfo: {}
    }
  },
  computed: {
    ...mapGetters('communityMan/userDlg', {
      userId: 'userId',
      userName: 'userName',
      dlgUserInfo: 'userInfo'
    }),
  },
  watch: {
    userId (val) {
      this.dlgData.staffId = val
    },

    userName (val) {
      this.dlgData.name = val
    },

    dlgUserInfo (val) {
      this.dlgData.userInfo = val
      this.dlgData.phone = val.account
    },
  },
  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/workade/kaoqinyuebaodaochu?` + param
      window.open(sendUrl)
    },

    // 下载
    downloadItem () {
      let url =
        'https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E7%97%85%E7%A7%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xls'
      window.open(url)
    },

    // 上传
    uploadItem (file) {
      let name = file.name.split('.')
      let suffix = name[name.length - 1]

      if (suffix !== 'xls' && suffix !== 'xlsx') {
        this.$message({
          type: 'warning',
          message: '只能上传xls/xlsx文件'
        })
        return false
      }

      let loading = this.$loading({
        lock: true,
        text: '导入中',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      let postParam = {
        file
      }

      importExcelDisease(postParam).then(res => {
        loading.close()
        if (res.data.code == 200) {
          this.$message.success('导入成功')
          this.getList()
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })

      return false
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 显示人员dlg
    showUserDlg () {
      let userId = this.dlgData.staffId
      let userName = this.dlgData.name
      let userInfo = this.dlgData.userInfo
      this.$store.commit('communityMan/userDlg/SET_USERID', userId)
      this.$store.commit('communityMan/userDlg/SET_USERNAME', userName)
      this.$store.commit('communityMan/userDlg/SET_USERINFO', userInfo)
      this.$store.commit('communityMan/userDlg/SET_DLGSHOW', true)
    },


    communityChange () {
      this.getBuildingList(this.dlgData.communityId)
      this.dlgData.floorList = []
    },

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取楼栋列表
    getBuildingList (id) {
      if (utils.isNull(id)) {
        return
      }
      cofloorCommunity(id).then(res => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data
        }
      })
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      cohousekeeperPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.getBuildingList(this.dlgData.communityId)
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          let hkFloors = []
          for (let i of postParam.floorList) {
            hkFloors.push({
              floorId: i
            })
          }
          postParam.hkFloors = hkFloors
          this.dlgLoading = true
          cohousekeeperAddOrUpdate(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgType = type
      this.dlgShow = true
      this.getBuildingList(this.dlgData.communityId)
      cohousekeeperInfo(data.id).then(res => {
        if (res.data.code == 200) {
          let hkFloors = res.data.data.hkFloors
          let floorList = []
          for (let i of hkFloors) {
            floorList.push(i.floorId)
          }
          this.dlgData.floorList = floorList
          this.$forceUpdate()
          this.$nextTick(() => {
            this.$refs['dlgForm'].clearValidate()
          })
        }
      })
    },


    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cohousekeeperDisable(data.id, flag).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


