<template>

</template>

<script>
import { mapGetters } from 'vuex'
import * as utils from '@/utils'

export default {
  name: 'WorkSpaceBase',

  data() {
    return {
      listQueryCache: {},

      count: 0
    }
  },

  computed: {
    listQueryStore: {
      get: function () {
        return {}
        // return this.$store.state.businessBase.listQueryStore
      },
      set: function (val) {
        // this.$store.commit('businessBase/SET_LISTQUERY', val)
      }
    },
  },

  watch: {
    listQueryStore: {
      handler(val) {

      },
      deep: true,
      immediate: true
    }
  },

  methods: {
  },

  created() {
    if (!utils.isNull(this.listQueryStore) && !utils.isEmptyObject(this.listQueryStore)) {
      this.listQueryCache = this.listQueryStore[this.$options.name]
    }
  },

  beforeDestroy() {
    console.log('beforeDestroy')
    // this.$store.commit('businessBase/SET_NAME', this.$options.name)
    // this.$store.commit('businessBase/SET_LISTQUERY', this.listQuery)
  }
}
</script>