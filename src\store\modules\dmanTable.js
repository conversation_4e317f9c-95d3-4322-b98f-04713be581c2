
const dmanTable = {
  state: {
    dmanTableState: false,
    dmanTableBranchId: '',
    dmanTableBranchName: '',
    dmanTableUserId: '',
    dmanTableUserName: '',

    dmanTableSqType: ''
  },

  mutations: {
    SET_DMANTABLESTATE: (state, dmanTableState) => {
      state.dmanTableState = dmanTableState
    },
    SET_DMANTABLEBRANCHID: (state, dmanTableBranchId) => {
      state.dmanTableBranchId = dmanTableBranchId
    },
    SET_DMANTABLEBRANCHNAME: (state, dmanTableBranchName) => {
      state.dmanTableBranchName = dmanTableBranchName
    },
    SET_DMANTABLEUSERID: (state, dmanTableUserId) => {
      state.dmanTableUserId = dmanTableUserId
    },
    SET_DMANTABLEUSERNAME: (state, dmanTableUserName) => {
      state.dmanTableUserName = dmanTableUserName
    },
    SET_DMANTABLESQTYPE: (state, val) => {
      state.dmanTableSqType = val
    }
  },

  actions: {
    
  }
}

export default dmanTable
