<template>
  <div class="app-container print">
    <!-- height:${paperHeight}px; -->
    <!-- page-break-before:always; -->
    <div
      v-for="(dlgData, index) of dlgDataList"
      :key="index"
      ref="paperContainer"
      :style="`width:${paperWidth}px;${index !== 0 ? 'page-break-before:always' : ''}`"
      class="paper-container clearfix"
      style="margin-top: -6px"
    >
      <div class="tac text-black fs18 text-bold">黑龙江体育运动创伤康复医院</div>
      <div class="tac text-black fs18 text-bold mt10">医疗机构新型冠状病毒肺炎流行病学史调查问卷</div>
      <div class="mt10 taj text-black text-bold" style="line-height: 1.2; text-indent: 2em">
        为做好新型冠状病毒疫情防控工作，依据《中华人民共和国传染病防治法》第七十七条法律法规规定，请您提供真实准确的流行病学史信息，如有故意隐瞒，导致传染病传播、流行的，当事人需承担相应的法律责任，感谢您的配合！
      </div>
      <!-- 一 -->
      <div class="flexc page-title0 mt10">一、基本信息</div>
      <div class="mt6">
        <div class="flexc">
          <span class="page-title flex-sub">1.姓名：{{ dlgData.name }}</span>
          <span class="page-title flex-sub">性别：{{ dlgData.sex }}</span>
          <span class="page-title flex-sub">年龄：{{ dlgData.age }}</span>

          <!-- <div class="jbxxBox">
            <span class="page-title">1.姓名：{{ dlgData.name }}</span>
            <span class="page-title">2.身份证/护照号：{{ dlgData.idNumber }}</span>
            <span class="page-title">4.现住址（哈市详细住址，具体到门牌号）：{{ dlgData.idNumber }}</span>
          </div>
          <div class="jbxxBox">
            <span class="page-title">性别：{{ dlgData.sex }}</span>
            <span class="page-title">3.联系方式: {{ dlgData.phone }}</span>
            <span class="page-title">5.亲属联络方式：{{ dlgData.idNumber }}</span>
          </div>
          <div class="jbxxBox">
            <span class="page-title">年龄：{{ dlgData.age }}</span>
          </div> -->
        </div>
        <div class="flexc mt6">
          <span class="page-title" style="flex: 2">2.身份证号：{{ dlgData.idNumber }}</span>
          <span class="page-title" style="flex: 1">3.联系方式：{{ dlgData.phone }}</span>
        </div>
        <div class="flexc mt6">
          <span class="page-title">4.现住址（哈市详细住址，具体到门牌号）：{{ dlgData.address }}</span>
        </div>
        <div class="flexc mt6">
          <span class="page-title">5.亲属联络方式：{{ dlgData.accompanyUseraPhone }}</span>
        </div>
      </div>

      <div class="flexc page-title0 mt10">二、病历信息</div>
      <div class="mt6">
        <div class="flexc page-title0 mt10">1.一周内是否出现发热情况：</div>
        <div class="flexlc lhbar">
          <input type="radio" value="1" :checked="dlgData.isFever == '1'" />&nbsp;是
          <input class="ml10" type="radio" value="0" :checked="dlgData.isFever + '' === '0'" />&nbsp;否

          <div class="flexlc lhbar" style="margin-left: 20px">
            现体温：
            <div class="m-input flex-sub tac" style="width: 100px">{{ dlgData.temp }}</div>
            &nbsp;℃
          </div>
        </div>

        <div class="flexc page-title0 mt10">2.来我院目的：</div>
        <div class="flexlc lhbar">
          <span v-for="(item, index) of sickTypeList" :key="item.id" class="mr10 flexlc lhbar">
            <input class="" type="radio" :value="item.id" :checked="dlgData.sickType + '' === item.id" />&nbsp;{{ item.name }}
          </span>
          <div v-if="dlgData.sickType == '3'" class="flexlc lhbar" style="margin-left: 20px">
            来院目的：
            <div class="m-input tac" style="padding-left: 10px; padding-right: 10px; min-width: 100px">
              {{ dlgData.sickTypeText }}
            </div>
          </div>
        </div>
        <div class="flexc page-title0 mt10">3.目前是否有以下症状：</div>
        <div class="clearfix">
          <div class="mr20 fl flexlc lhbar" style="height: 16px" v-for="item of medicalSymptomsList" :key="item.id">
            <input class="" type="checkbox" :value="item.id" :checked="dlgData.medicalSymptoms.indexOf(item.id) >= 0" />
            &nbsp;{{ item.name }}
          </div>
        </div>

        <div class="flexc page-title0 mt10">4.一周内是否做过下类哪项相关检测：</div>
        <div class="clearfix">
          <input type="radio" value="1" :checked="dlgData.isDetection == '1'" />&nbsp;是
          <input class="ml10" type="radio" value="0" :checked="dlgData.isDetection + '' === '0'" />&nbsp;否
        </div>
        <div class="clearfix">
          <div class="mr20 fl flexlc lhbar" style="height: 16px" v-for="item of isDetectionList" :key="item.id">
            <input class="" type="checkbox" :value="item.id" :checked="dlgData.isDetectionVal.indexOf(item.id) >= 0" />
            &nbsp;{{ item.name }}
          </div>
        </div>

        <div class="flexc page-title0 mt10">5.近一周是否吃过退烧药：</div>
        <div class="clearfix lhbar">
          <div class="fl flexc lhbar">
            <input type="radio" value="1" :checked="dlgData.isEatFebrifuge == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isEatFebrifuge + '' === '0'" />&nbsp;否
          </div>
          <div v-if="dlgData.isEatFebrifuge == '1'" class="fl flexc" style="margin-left: 20px">
            退烧药名称：
            <div class="m-input tac" style="padding-left: 10px; padding-right: 10px; min-width: 100px; display: inline-block">
              {{ dlgData.isEatFebrifugeName }}
            </div>
          </div>
        </div>
      </div>

      <div class="flexc page-title0 mt10">三、危险史与暴露史</div>
      <div class="mt6">
        <div class="flexc page-title0 mt10">1.就诊前两周是否到过境外或者国内疫区（中高风险地区）：</div>
        <div class="clearfix lhbar">
          <div class="fl flexc lhbar">
            <input type="radio" value="1" :checked="dlgData.isAchieveArea == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isAchieveArea + '' === '0'" />&nbsp;否
          </div>
        </div>

        <div v-if="dlgData.isAchieveArea == '1'" class="clearfix lhbar">
          境外（何地）：
          <div class="m-input tac" style="padding-left: 10px; padding-right: 10px; min-width: 100px; display: inline-block">
            {{ dlgData.overseasArea }}
          </div>
        </div>
        <div v-if="dlgData.isAchieveArea == '1'" class="clearfix lhbar">
          国内疫区(中高风险地区)：
          <div class="m-input tac" style="padding-left: 10px; padding-right: 10px; min-width: 100px; display: inline-block">
            {{ dlgData.riskArea }}
          </div>
        </div>

        <div class="flexc page-title0 mt10">2.近两周是否接触过明确新冠病毒感染者(核酸检测阳性者)或发热、咳嗽等症状人群：</div>
        <div class="clearfix lhbar">
          <div class="fl flexc lhbar">
            <input type="radio" value="1" :checked="dlgData.isContactPatient == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isContactPatient + '' === '0'" />&nbsp;否
          </div>
        </div>

        <div class="flexc page-title0 mt10">3.近期(一个月)是否有国外旅居史或与归国人员有过接触史：</div>
        <div class="clearfix lhbar">
          <div class="fl flexc lhbar">
            <input type="radio" value="1" :checked="dlgData.isContactReturnees == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isContactReturnees + '' === '0'" />&nbsp;否
          </div>
        </div>

        <div class="flexc page-title0 mt10">4.上述情况是否属实：</div>
        <div class="clearfix lhbar">
          <div class="fl flexc lhbar">
            <input type="radio" value="1" :checked="dlgData.isShuShi == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isShuShi + '' === '0'" />&nbsp;否
          </div>
        </div>
      </div>

      <!-- <div
        class="mt10 taj text-black text-bold"
        style="line-height: 1.2; text-indent: 2em"
      >
        我本人承诺上述情况属实，依照《中华人民共和国传染病防治法》第七十七条法律法规、如有隐瞒、导致传染病传播、流行，给他人人身、财产造成损失，我将承担法律责任。
      </div> -->

      <div class="flexlc lhbar mt30">
        <div class="flexlc" style="width: 30%">
          <div class="page-title0">接诊医生：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">填写日期：</div>
          <div class="m-input flex-sub tac">{{ dlgData.createTime }}</div>
        </div>
      </div>

      <!-- 有签字图片 -->
      <div v-if="dlgData.signatureUrl" class="flexlt mt30">
        <div class="flexlt" style="width: 30%">
          <div class="page-title0 lhbar flexlc">就诊患者签字：</div>
          <img class="" :src="dlgData.signatureUrl" alt="" style="display: block; width: 100px; height: 70px" />
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">打印日期：</div>
          <div class="m-input flex-sub tac">{{ printDate }}</div>
        </div>
      </div>
      <!--  无签字图片 -->
      <div v-else class="flexlc lhbar mt30">
        <div class="flexlc" style="width: 30%">
          <div class="page-title0">就诊患者签字：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">打印日期：</div>
          <div class="m-input flex-sub tac">{{ printDate }}</div>
        </div>
      </div>

      <!-- 第二行 -->
      <!-- <div class="flexlc lhbar mt30">
        <div class="flexlc" style="width: 30%">
          <div class="page-title0">分诊人员签字：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">就诊医生签字：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
      </div> -->
    </div>
    <el-button class="no-print" icon="el-icon-printer" type="primary" size="medium" @click="printFunc()"> 打印</el-button>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import * as utils from '@/utils'
import { return2Num } from '@/utils/calendarData'
import { TYJK_CONTANTS } from '@/utils/constants'
// import {
//   findInfoById,
// } from '@/api/jyt/drugMail'

export default {
  components: {},
  data() {
    return {
      paperHeight: 0,
      paperWidth: 0,
      fieldList: [
        { name: '住宅物业管理费', desc: '111', money: '10' },
        { name: '电梯费', desc: '222', money: '20' },
        { name: '', desc: '', money: '' },
        { name: '', desc: '', money: '' },
      ],
      id: '',

      printDate: '',

      dlgData0: '',
      dlgData: '',

      // 下拉框
      sickTypeList: TYJK_CONTANTS.sickTypeList, // 2.2来我院目的
      medicalSymptomsList: TYJK_CONTANTS.medicalSymptomsList, // 2.3目前是否有以下症状
      isDetectionList: TYJK_CONTANTS.isDetectionList, // 2.4一周内是否做过下类哪项相关检测

      dlgDataList0: [],
      dlgDataList: [],
    }
  },
  computed: {},
  created() {
    this.setPrintDate()

    this.setFormData()
    // this.getDbItems()
  },
  mounted() {
    this.$nextTick(() => {
      // console.log('utils.getDpiWidth(227)', utils.getDpiWidth(227))
      // this.paperWidth = utils.getDpiWidth(227)
      // this.paperHeight = utils.getDpiHeight(122)

      this.paperWidth = utils.getDpiWidth(188)
      this.paperHeight = utils.getDpiHeight(1600)

      // this.paperWidth = utils.getDpiWidth(210)
      // this.paperHeight = utils.getDpiHeight(140)
    })
  },
  methods: {
    setPrintDate() {
      let today = new Date()
      let year = today.getFullYear()
      let month = return2Num(today.getMonth() + 1)
      let day = return2Num(today.getDate())
      this.printDate = `${year} 年 ${month} 月 ${day} 日`
    },
    setFormData() {
      console.log('window.sessionStorage.printData', window.sessionStorage.printData)
      let printData = JSON.parse(window.sessionStorage.printData)

      for (let dlgData of printData) {
        dlgData.isShuShi = '1'
        // 来我院目的
        dlgData.sickTypeText = ''
        if (dlgData.sickType != '1' && dlgData.sickType != '2') {
          dlgData.sickTypeText = dlgData.sickType
          dlgData.sickType = '3'
        }

        dlgData.medicalSymptoms += ''
        dlgData.medicalSymptoms0 = dlgData.medicalSymptoms ? dlgData.medicalSymptoms.split(',') : []
        dlgData.medicalSymptoms = dlgData.medicalSymptoms ? dlgData.medicalSymptoms.split(',') : []
        // 相关检测
        if (dlgData.isDetection) {
          dlgData.isDetectionVal = dlgData.isDetection.split(',')
          dlgData.isDetection = '1'
        } else {
          dlgData.isDetectionVal = []
          dlgData.isDetection = '0'
        }

        // 退烧药
        if (dlgData.isEatFebrifuge) {
          dlgData.isEatFebrifugeName = dlgData.isEatFebrifuge
          dlgData.isEatFebrifuge = '1'
        } else {
          dlgData.isEatFebrifugeName = ''
          dlgData.isEatFebrifuge = '0'
        }
      }

      this.dlgDataList0 = JSON.parse(JSON.stringify(printData))
      this.dlgDataList = JSON.parse(JSON.stringify(printData))
    },
    getDbItems() {
      console.log(11111111)
      let keyArr = [
        'yd6_specificProfession', // 以下职业
        'yd6_medicalSymptoms', // 主要症状
        'yd6_patientsGo', // 患者去向
      ]
      console.log(keyArr, 'keyArr')
      let keyStr = keyArr.join(',')
      console.log(keyStr, 'keyStr')
      utils.getDbItems(keyStr, 110).then((list) => {
        console.log(list, 'list')
        // 是否为以下特定职业人群
        this.tdrqSelect = list[0]
        console.log(this.tdrqSelect, 'this.tdrqSelect')
        //  您目前是否有以下症状
        this.zyzzSelect = list[1]

        // 患者去向
        this.hzqxSelect = list[2]
      })
    },

    // ///////

    dealBigMoney(val) {
      console.log('之后的', utils.dealBigMoney(val))
      return utils.dealBigMoney(val)
    },

    // 打印
    printFunc() {
      this.dlgDataList = JSON.parse(JSON.stringify(this.dlgDataList0))
      this.$nextTick(() => {
        window.print()
      })
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss">
body {
  height: auto !important;
}
#app {
  height: auto !important;
}
</style>

<style rel="stylesheet/scss" lang="scss">
body {
  height: auto !important;
}
#app {
  height: auto !important;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
.app-container.print {
  font-size: 14px;
}
.app-container {
  box-sizing: border-box;
  font-size: 12px;
  position: relative;
  width: 100%;
  // height: 100%;
  overflow: auto;
  padding: 0;

  .paper-container {
    // background: url('/static/image/paper.jpg') no-repeat center;
    // background-size: 100%;
    background: #fff;
    position: relative;
    box-sizing: border-box;
    padding: 16px;
    font-size: 12px;
    // padding-top: 16px;
    .field {
      position: absolute;
      max-width: 360px;
      word-break: break-all;
    }
  }
  .el-button.no-print {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

@media print {
  .no-print {
    display: none;
  }
}

//
// 表格
.tac {
  text-align: center;
}
.tar {
  text-align: right;
}
.taj {
  text-align: justify;
}
.text-bold {
  font-weight: bold;
}
.mt6 {
  margin-top: 6px;
}
.mt10 {
  margin-top: 10px;
}
.mt16 {
  margin-top: 16px;
}

.mr10 {
  margin-right: 10px;
}
.m-table {
  // background:#666;
  border-top: 1px solid #666;
  border-left: 1px solid #666;
  border-spacing: 0px;
  font-size: 12px;
  td {
    background: #fff;
    min-height: 30px;
    box-sizing: border-box;
    padding: 1px;
    border-right: 1px solid #666;
    border-bottom: 1px solid #666;
  }
  .mh {
    min-height: 36px;
  }
}

/////
// .page-con {
//   color: #333;
// }
.page-title0 {
  font-weight: bold;
  color: #000;
}
.page-title {
  color: #000;
}
.lhbar {
  height: 18px;
  .jbxxBox {
    display: flex;
    flex-direction: column;
  }
}
.m-input {
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  padding: 0;
  height: 18px;
  line-height: 18px;
}
</style>