import request from '@/utils/request'
import {
  requestExcel
} from '@/utils'

/*
 * 护工管理
 */

// 查询护工管理信息 
export function queryCareManage(data) {
  return request({
    url: `/u/usapi/hg/queryCareManage`,
    method: 'post',
    data
  })
}

// 查询审核通过的护工设置信息 
export function queryCarezsManage(data) {
  return request({
    url: `/u/usapi/hg/queryCarezsManage`,
    method: 'post',
    data
  })
}

// 保存护工 
export function saveCareManage(data) {
  return request({
    url: `/u/usapi/hg/saveCareManage`,
    method: 'post',
    data
  })
}

// 删除护工
export function deleteManager(data) {
  return request({
    url: `/u/usapi/hg/deleteManager`,
    method: 'post',
    data
  })
}

// 护工审核 
export function checkCareManage(data) {
  return request({
    url: `/u/usapi/hg/checkCareManage`,
    method: 'post',
    data
  })
}

// 根据护工careId 查询正式护工信息 
export function findCarezsManageByCareId(data) {
  return request({
    url: `/u/usapi/hg/findCarezsManageByCareId`,
    method: 'post',
    data
  })
}

// 导入
export function importExcelManage(data) {
  return requestExcel('/u/usapi/hg/importExcelManage', data)
}
