<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">选择设备类型：</div>
            <el-select
              class="fl"
              style="width: 220px"
              v-model="searchForm.equType"
              placeholder="选择设备类型"
              @change="equTypeChange"
              filterable
              clearable
            >
              <el-option
                v-for="item of equipmentTypeOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input
              v-model="searchForm.fileName"
              placeholder="关键字"
              clearable
              class="fl"
              style="width: 160px"
              @change="searchFunc"
            ></el-input>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
            <el-button
              type="success"
              @click="showDlg('add')"
              icon="el-icon-plus"
              class="fl ml10"
              >添加</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc(100vh - 290px)"
      ref="tableBar"
      class="m-small-table"
      v-loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equTypeStr"
        label="设备类型"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="fileName"
        label="文件名"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="文档文件"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" width="280" align="center">
        <template slot-scope="scope">
          <el-button
            @click="showDlg('info', scope.row)"
            icon="el-icon-document"
            size="mini"
            type="primary"
            title="详情"
            plain
            >详情</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="showDlg('edit', scope.row)"
            plain
            icon="el-icon-edit"
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="delFunc(scope.row)"
            plain
            icon="el-icon-delete"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="searchFunc()"
    />
    <div class="clear"></div>

    <addEdit
      ref="addEdit"
      :dlgType="dlgType"
    ></addEdit>
  </div>
</template>

<script>
import addEdit from "./addEdit";
import {  getAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination"; // 分页
import { getDataDict } from "@/utils";

export default {
  components: {
    addEdit,
    Pagination,
  },
  data() {
    return {
      searchForm: {
        equType: "",
        fileName: "",
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      total: 0,
      listLoading: false,
      dlgType: "add",
      equipmentTypeOptions: [],
    };
  },
  created() {},
  mounted() {
    getDataDict(this, "equipmentType", "equipmentTypeOptions");
    this.searchFunc();
  },
  methods: {
    equTypeChange() {
      this.searchFunc();
    },
    showDlg(type, row) {
      this.$refs.addEdit.init(type, row);
    },
    searchFunc() {
      let postData = JSON.parse(JSON.stringify(this.searchForm));
      this.listLoading = true;

      getAction(`sa/green/equ/equipment-manage-manual/page`, postData).then(
        (res) => {
          let { code, data } = res.data;
          this.listLoading = false;
          if (code === "200") {
            console.log(data.list);
            for (let i = 0; i < data.list.length; i++) {
              let name = [];
              data.list[i].fileUrl = JSON.parse(data.list[i].fileUrl);
              data.list[i].fileUrl.map((item) => {
                name.push(item.name);
              });
              data.list[i].name = name.toString();
            }

            console.log(data.list, "data.list");
            this.tableData = data.list ? data.list : [];
            this.total = data.total ? data.total : 0;
          } else {
            this.$message.error(res.data.msg);
          }
        }
      );
    },
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAction(`sa/green/equ/equipment-manage-manual/delete?id=${row.id}`).then(
          (res) => {
            if (res.data.code === "200") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.searchFunc();
            } else {
              this.$message.error(res.data.msg);
            }
          }
        );
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
</style>