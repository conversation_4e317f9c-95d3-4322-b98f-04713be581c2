<template>
  <el-dialog :close-on-click-modal='false' :title="'选择权限'" :visible.sync="dlgShow">
    <div class="filter-container">
      <el-input placeholder="输入权限名称进行过滤" v-model="filterPermission"></el-input>
    </div>
    <div class='m-dialog-h'>
      <el-tree ref="permissionTreeMul" show-checkbox highlight-current node-key="id" @check="treeCheck" @check-change="treeCheckChange" :check-strictly="true" :data="list" :filter-node-method="filterNode" :expand-on-click-node="false">
      </el-tree>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-back" @click="closeDlg">
        取 消
      </el-button>
      <el-button icon="el-icon-delete" type="danger" @click="clearDlg">
        清 空
      </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'

import { findPermissionTree } from '@/api/propertyMan/roleMan.js'

export default {
  components: {
  },
  data () {
    return {
      filterPermission: "",

      list: [],

      selectPermissions: [],

      selectPermissionIds: "",

      selectPermissionNames: "",
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.propertyMan.permissionDlgMul.dlgShow
      },
      set: function (val) {
        this.$store.commit('propertyMan/permissionDlgMul/SET_DLGSHOW', val)
      }
    },

    permissionIds: {
      get: function () {
        return this.$store.state.propertyMan.permissionDlgMul.permissionIds
      },
      set: function (val) {
        this.$store.commit('propertyMan/permissionDlgMul/SET_PERMISSIONIDS', val)
      }
    },

    permissionNames: {
      get: function () {
        return this.$store.state.propertyMan.permissionDlgMul.permissionNames
      },
      set: function (val) {
        this.$store.commit('propertyMan/permissionDlgMul/SET_PERMISSIONNAMES', val)
      }
    },

  },

  watch: {
    filterPermission (val) {
      this.$refs.permissionTreeMul.filter(val);
    },

    dlgShow (val) {
      if (val) {
        this.selectPermissionIds = this.permissionIds
        this.selectPermissionNames = this.permissionNames
        this.getList()
      }
    },

    permissionIds (val) {
      this.selectPermissionIds = val
    },

    permissionNames (val) {
      this.selectPermissionNames = val
    }
  },

  methods: {

    treeCheckChange (obj, isChecked, isChildChecked) {
      // 取消，子节点只要有一个被取消父节点就被取消
      if (!isChecked) {
        this.$refs.permissionTreeMul.setChecked(obj.upPermissionId, false)
      }
    },

    treeCheck (currentObj, treeStatus) {
      // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
      let selected = treeStatus.checkedKeys.indexOf(currentObj.id) // -1未选中
      // 选中
      if (selected !== -1) {
        // 子节点只要被选中父节点就被选中
        this.selectedChildren(currentObj)
        // 统一处理子节点为相同的勾选状态
        this.uniteChildSame(currentObj, true)
      } else {
        // 未选中 处理子节点全部未选中
        if (currentObj.children && currentObj.children.length !== 0) {
          this.uniteChildSame(currentObj, false)
        }
      }
    },

    // 统一处理子节点为相同的勾选状态
    uniteChildSame (treeList, isSelected) {
      this.$refs.permissionTreeMul.setChecked(treeList.id, isSelected)
      let childLen = treeList.children ? treeList.children.length : 0
      if (childLen) {
        for (let i = 0; i < childLen; i++) {
          this.uniteChildSame(treeList.children[i], isSelected)
        }
      }
    },

    // 统一处理子节点为选中
    selectedChildren (currentObj) {
      let currentNode = this.$refs.permissionTreeMul.getNode(currentObj)
      currentNode.childNodes.map(item => {
        if (item.key !== undefined) {
          this.$refs.permissionTreeMul.setChecked(item, true)
          this.selectedChildren(item)
        }
      })
    },

    filterNode (value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    setCheckedKeys () {
      this.selectPermissions = []
      if (utils.isNull(this.selectPermissionIds)) {
        this.$refs.permissionTreeMul.setCheckedKeys(this.selectPermissions)
        return
      }
      this.$nextTick(() => {
        let currentKey = []
        let selectPermissionIdList = this.selectPermissionIds.split(",")
        let selectPermissionNameList = this.selectPermissionNames.split(",")
        for (let i in selectPermissionIdList) {
          currentKey.push(parseInt(selectPermissionIdList[i]))
          this.selectPermissions.push({
            id: selectPermissionIdList[i],
            label: selectPermissionNameList[i]
          })
        }
        this.$refs.permissionTreeMul.setCheckedKeys(currentKey)
      })
    },

    getList () {
      this.list = []
      findPermissionTree().then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = res.data.list
          this.setCheckedKeys()
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg () {
      let checkNodes = this.$refs.permissionTreeMul.getCheckedNodes()
      let halfCheckNodes = this.$refs.permissionTreeMul.getHalfCheckedNodes()
      this.selectPermissions = checkNodes

      let permissionIdList = []
      let permissionNameList = []
      for (let i of this.selectPermissions) {
        permissionIdList.push(i['id'])
        permissionNameList.push(i['label'])
      }
      this.permissionIds = permissionIdList.join(",")
      this.permissionNames = permissionNameList.join(",")
      this.$store.commit('propertyMan/permissionDlgMul/SET_PERMISSIONIDS', this.permissionIds)
      this.$store.commit('propertyMan/permissionDlgMul/SET_PERMISSIONNAMES', this.permissionNames)
      this.closeDlg()
    },

    clearDlg () {
      this.selectPermissionIds = ""
      this.selectPermissionNames = ""
      this.setCheckedKeys()
    },

    closeDlg () {
      this.$store.commit('propertyMan/permissionDlgMul/SET_DLGSHOW', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-tree {
  margin-top: 10px;
}
.filter-container {
  .fl {
    width: 100%;
  }
}
</style>