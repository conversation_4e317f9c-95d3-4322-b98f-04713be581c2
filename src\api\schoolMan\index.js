import request from "@/utils/request";

// 查询小区公告
export function receiveNoticeHeaderByUserId({ page, size }) {
  return request({
    url: `/msg/noticeSchool/receiveNoticeHeaderByUserId/${page}/${size}`,
    method: "get"
  });
}

// 发送通知公告
export function sendNotice(data) {
  return request({
    url: `/msg/noticeSchool/sendNotice`,
    method: "post",
    data
  });
}

// 查询通知详情
export function receiveNotice(msgId) {
  return request({
    url: `/msg/noticeSchool/receiveNotice/${msgId}`,
    method: "get"
  });
}

// 获取消息范围
export function findNoticeUserName(data) {
  return request({
    url: `/msg/noticeSchool/findNoticeUserName`,
    method: "post",
    data
  });
}
