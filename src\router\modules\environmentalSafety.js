/** 环境安全 **/

import Layout from "@/views/layout/Layout";
const environmentalSafetyRouter = {
  path: "/environmentalSafety",
  component: Layout,
  name: "environmentalSafety",
  meta: {
    title: "环境安全",
    icon: "hjaq",
    roles: ["huanjing_huanjinganquan"]
  },
  children: [
    {
      path: "detectionPlatform",
      component: () =>
        import("@/views/electricalFireMonitoring/detectionPlatform/index0"),
      name: "监测大屏",
      meta: {
        title: "监测大屏",
        roles: ["huanjing_jiancedaping"]
      }
    },
    {
      path: "abnormalAlarmMan",
      component: () => import("@/views/equipSafeMan/abnormalAlarmMan"),
      name: "报警管理",
      meta: {
        title: "报警管理",
        roles: ["huanjing_baojingguanli"]
      }
    },
    {
      path: "sensorEquip",
      component: () => import("@/views/equipSafeMan/sensorEquip"),
      name: "传感器管理",
      meta: {
        title: "传感器管理",
        roles: ["huanjing_chuanganqiguanli"]
      }
    },
    {
      path: "cameraManagement",
      component: () => import("@/views/equipSafeMan/cameraManagement"),
      name: "摄像机管理",
      meta: {
        title: "摄像机管理",
        roles: ["huanjing_shexiangjiguanli"]
      }
    },
    {
      path: "statisticalAnalysis",
      component: () =>
        import("@/views/electricalFireMonitoring/statisticalAnalysis/index"),
      meta: {
        title: "统计分析",
        roles: ["huanjing_tongjifenxi"]
      },
      children: [
        {
          path: "warnInfoQuery",
          component: () =>
            import(
              "@/views/electricalFireMonitoring/statisticalAnalysis/warnInfoQuery"
            ),
          name: "报警信息查询",
          meta: {
            title: "报警信息查询",
            roles: ["huanjing_baojingxinxichaxun"]
          },
          children: []
        },
        {
          path: "alarmStatisticalAnalysis",
          component: () =>
            import(
              "@/views/electricalFireMonitoring/statisticalAnalysis/alarmStatisticalAnalysis"
            ),
          name: "报警信息统计",
          meta: {
            title: "报警信息统计",
            roles: ["huanjing_baojingxinxitongji"]
          },
          children: []
        },
        {
          path: "alarmReminderRecord",
          component: () =>
            import(
              "@/views/electricalFireMonitoring/statisticalAnalysis/alarmReminderRecord/index"
            ),
          name: "报警提醒记录",
          meta: {
            title: "报警提醒记录",
            roles: ["huanjing_baojingtixingjilu"]
          },
          children: []
        }
      ]
    }
  ]
};
export default environmentalSafetyRouter;
