<template>
  <!-- 报警配置 -->
  <div
    class="app-container zhangkexin"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <el-form
      size="small"
      ref="searchForm"
      class=""
      :model="listQuery"
      label-width=""
      @submit.native.prevent
    >
      <el-form-item label="">
        <!-- <el-input
          @keyup.enter.native="searchFunc"
          class="m-shaixuan-input fl mr10"
          placeholder="请输入"
          v-model="listQuery.label"
          style="width: 200px"
          size="small"
        >
          <i
            @click="resetSearchItem(['label'])"
            slot="suffix"
            class="el-input__icon el-icon-error"
          ></i>
        </el-input> -->
        <el-cascader
          class="m-shaixuan-input fl mr10"
          ref="refSubCat"
          clearable
          placeholder="请选择区域"
          :props="{ value: 'id', label: 'name', emitPath: false }"
          :show-all-levels="false"
          @change="areaChange"
          v-model="listQuery.areaId"
          :options="areaList"
        ></el-cascader>
        <el-select
          class="m-shaixuan-input fl"
          filterable
          v-model="listQuery.equipRoomId"
          clearable
          placeholder="请选择设备间"
          style="width: 180px"
          @change="searchFunc"
          value-key="id"
        >
          <el-option
            v-for="item of sbjSelect"
            :key="item.id"
            :label="item.name"
            :value="item"
          >
          </el-option>
        </el-select>
        <!-- <el-date-picker
            class="fl ml10"
            style="width: 250px"
            @change="searchFunc"
            v-model="listQuery.dateRange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="small"
          >
          </el-date-picker> -->

        <el-button
          icon="el-icon-refresh"
          type="primary"
          size="small"
          class="fl ml10"
          @click="searchFunc"
          >刷新</el-button
        >
        <el-button
          icon="el-icon-c-scale-to-original"
          type="success"
          size="small"
          class="fl ml10"
          @click="showDlg"
          >批量绑定</el-button
        >
      </el-form-item>
      <div class="clear"></div>
    </el-form>
    <el-table
      ref="tableBar"
      class="m-small-table"
      height="calc(100vh - 320px)"
      v-loading="listLoading"
      :row-key="getRowKeys"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @row-click="tableRowClick"
      @selection-change="tableSelectChange"
    >
      <el-table-column align="center" type="selection" width="55">
      </el-table-column>

      <el-table-column label="报警类型">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绑定规则">
        <template slot-scope="scope">
          <span>{{ scope.row.msgConfigName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button
            type="danger"
            v-if="scope.row.msgConfigId && scope.row.msgConfigId != ''"
            size="mini"
            icon="el-icon-delete"
            @click="delFun(scope.row)"
            plain
            >删除</el-button
          >
          <!-- <el-button
            type="primary"
            size="mini"
            icon="el-icon-document"
            @click.stop="showDia(scope.row)"
            plain
            >查看详情</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <!-- <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div> -->

    <el-dialog
      :title="title"
      :close-on-click-modal="false"
      :visible.sync="dlgState"
      width="600px"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :rules="dlgRules"
        :model="dlgData"
        label-position="right"
        label-width="90px"
        size="mini"
        @submit.native.prevent
      >
        <el-form-item label="区域" prop="areaId">
          <el-cascader
            ref="refSubCat0"
            clearable
            placeholder="请选择区域"
            :props="{ value: 'id', label: 'name', emitPath: false }"
            :show-all-levels="false"
            @change="areaChange"
            v-model="dlgData.areaId"
            :options="areaList"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="设备间" prop="equipRoomId">
          <el-select
            class="fl"
            filterable
            v-model="dlgData.equipRoomId"
            clearable
            placeholder="请选择设备间"
            style="width: 180px"
            value-key="id"
          >
            <el-option
              v-for="item of sbjSelect0"
              :key="item.id"
              :label="item.name"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规则名称" prop="msgConfigName">
          <el-select
            @change="selectProductType"
            style="width: 50%"
            v-model="dlgData.msgConfigName"
            placeholder="请选择"
          >
            <el-option
              v-for="item in ruleList"
              :key="item.id"
              :label="item.name"
              :value="{ value: item.id, label: item.name }"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <div class="table-container">
          <el-table
            ref="multipleTable"
            class="m-small-table"
            :data="tableSelectList"
            border
            fit
            highlight-current-row
            max-height="500"
          >
            <el-table-column type="index" label="#" align="center" width="60">
            </el-table-column>
            <el-table-column label="报警类型">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="绑定规则">
              <template slot-scope="scope">
                <span>{{ scope.row.msgConfigName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="60" align="center">
              <template slot-scope="scope">
                <el-button
                  @click="delSelectRow(scope.$index)"
                  type="danger"
                  size="mini"
                  icon="el-icon-delete"
                  plain
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgState = false" icon="el-icon-close"
          >关闭</el-button
        >
        <el-button type="success" @click="subFunc" icon="el-icon-check">
          <span>提交</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { regionTree } from "@/api/basicManSystem/comprehenOperateMonitor.js"; //查区域
import Pagination from "@/components/Pagination"; // Secondary package based on el-pagination
import { postAction, getAction } from "@/api";
import {
  findEquioPage // 列表
} from "@/api/safetyMonitoringApi";
import jsCookie from "js-cookie";
import * as utils from "@/utils";
import {
  isNull,
  getDbItems,
  objToParam,
  arrId2Name,
  arrIds2Names,
  getPreMonth
} from "@/utils";
let dlgDataEmpty = {
  msgConfigName: "",
  msgConfigId: "",
  alertTypes: "",
  alertNames: "",
  areaId: "",
  area: ""
};
let listEmpty = [
{ id: "a1001", name: "水压高" },
        { id: "a1002", name: "水压低" },
        { id: "a1003", name: "水位高" },
        { id: "a1004", name: "水位低" },
        { id: "a1005", name: "温度高" },
        // { id: "a1006", name: "温度低" },
        { id: "a1007", name: "湿度高" },
        { id: "a1008", name: "湿度低" },
        // { id: "d", name: "光照高" },
        // { id: "a1010", name: "光照低" },
        { id: "a1011", name: "过压" },
        { id: "a1012", name: "欠压" },
        // { id: "a1013", name: "电量低" },
        { id: "a1014", name: "人体活动" },
        // { id: "a1015", name: "长时间未上报" },
        { id: "a1016", name: "烟雾报警" },
        { id: "a1017", name: "三相电压不平衡" },
        { id: "a1018", name: "三相电流不平衡" },
        { id: "a1019", name: "高负载" },
        // { id: "a1020", name: "低负载" },
        { id: "a1021", name: "漏水报警" },
        { id: "a1022", name: "可燃气体报警" },
        { id: "a1023", name: "环境温度高" },
        { id: "a1024", name: "环境温度低" },
        { id: "a1025", name: "线缆温度高" },
        // { id: "a1026", name: "线缆温度低" },
        { id: "a1027", name: "有功功率高" },
        { id: "a1028", name: "有功功率低" },
        // { id: "a1029", name: "无功功率高" },
        // { id: "a1030", name: "无功功率低" },
        //{ id: "a1031", name: "视在功率高" },
        // { id: "a1032", name: "视在功率低" },
        { id: "a1033", name: "井盖打开" },
        { id: "a1034", name: "漏电电流高" },
        // { id: "a1035", name: "漏电电流低" },
        { id: "a1036", name: "差压高" },
        { id: "a1037", name: "差压低" },
        // { id: "a1038", name: "可燃气体浓度高" },
        // { id: "a1039", name: "可燃气体浓度低" },
        { id: "a1040", name: "短路报警" },
        { id: "a1041", name: "浪涌报警" },
        { id: "a1042", name: "缺项报警" },
        { id: "a1043", name: "打火报警" },
        { id: "a1044", name: "掉电报警" },
        { id: "a1045", name: "离线报警" },
        {id: "109",name: "烟火监测报警"},
        {id: "110",name: "烟雾监测报警"},
        {id: "a1048",name: "瞬时流量高"},
        {id: "a1049",name: "瞬时流量低"},
        // {id: "18",name: "移动侦测报警"},
        // {id: "20",name: "门铃按键报警"},
        // {id: "38",name: "烟感检测报警"},
        // {id: "39",name: "离岗检测报警"},
        // {id: "40",name: "婴儿哭声检测报警"},
        // {id: "41",name: "人形检测报警"},
];
export default {
  components: {
    Pagination
  },
  data() {
    return {
      sbjSelect: [], // 设备间
      sbjSelect0: [], // 设备间弹框的
      areaList: [], //区域
      // 弹窗
      title: "",
      dlgState: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        msgConfigName: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        areaId: [{ required: true, message: "必填字段", trigger: "change" }]
      },

      list: JSON.parse(JSON.stringify(listEmpty)),

      listQuery: {
        equipRoomId: "",
        areaId: ""
      },
      total: 0,
      tableSelectList: [],
      listLoading: false,

      ruleListQuery: {
        // dateRange: [],
        label: "",
        page: 1,
        limit: 20
        // beginDate: "",
        // endDate: ""
      },
      ruleList: []
    };
  },
  created() {
    this.list = [];
    this.getSbjSelect();
    this.getList();
    this.getAreaList();
  },
  methods: {
    //区域
    getAreaList() {
      this.areaList = [];
      let sendObj = {
        page: 1,
        size: 9999
      };
      regionTree(sendObj).then(res => {
        if (res.data.code == "200") {
          this.areaList = this.getTreeData(res.data.data);
        } else {
          this.$message({
            type: "warning",
            message: res.data.msg
          });
        }
      });
    },
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    areaChange(selectedValues) {
      if (selectedValues) {
        if (this.dlgState) {
          this.dlgData.area = this.$refs[
            "refSubCat0"
          ].getCheckedNodes()[0].label; //获取选中name
        } else {
          this.listQuery.equipRoomId = "";
          this.listQuery.equipRoomName = "";
          this.listQuery.area = this.$refs[
            "refSubCat"
          ].getCheckedNodes()[0].label; //获取选中name
        }
      }else{
        this.dlgData.equipRoomId = "";
        this.dlgData.equipRoomName = "";
      }
      this.getSbjSelect();
    },
    getSbjSelect() {
      let sendObj = {
        label: "",
        page: 1,
        limit: 9999,
        isEquipRoom: 0, // 0设备间 1设备 不传查所有
        areaId: this.dlgState?this.dlgData.areaId:this.listQuery.areaId
      };
      findEquioPage(sendObj).then(res1 => {
        this.listLoading = false;
        let res = res1.data;
        if (res.code == "200") {
          this.dlgState?this.sbjSelect0 = JSON.parse(JSON.stringify(res.list)):this.sbjSelect = JSON.parse(JSON.stringify(res.list))
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // 清空搜索条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },
    searchFunc() {
      console.log(this.listQuery);
      // this.listQuery.page = 1;
      this.list = [];
      this.getList();
    },

    getRuleList() {
      let sendObj = JSON.parse(JSON.stringify(this.ruleListQuery));
      postAction("/iot/sendMsgConfig/page", sendObj).then(res => {
        if (res.data.code == 200) {
          this.ruleList = res.data.data;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    getList() {
      if (
        utils.isNull(this.listQuery.equipRoomId) &&
        utils.isNull(this.listQuery.areaId)
      ) {
        this.$message({
          type: "warning",
          message: "请选择区域或设备间"
        });
        return false;
      }
      this.list = JSON.parse(JSON.stringify(listEmpty));
      // let sendObj = JSON.parse(JSON.stringify(this.dlgData));
      // // 日期范围
      // sendObj.beginDate = "";
      // sendObj.endDate = "";
      // if (
      //   !isNull(this.listQuery.dateRange) &&
      //   this.listQuery.dateRange.length > 0
      // ) {
      //   sendObj.beginDate = this.listQuery.dateRange[0];
      //   sendObj.endDate = this.listQuery.dateRange[1];
      // }
      // this.listLoading = true
      let url = "";
      console.log(this.listQuery.equipRoomId.id,'this.listQuery.equipRoomId.id');
      console.log(this.listQuery.areaId,'this.listQuery.areaId');
      if (!utils.isNull(this.listQuery.areaId)&& utils.isNull(this.listQuery.equipRoomId)) {
        url =
          "/iot/sendMsgConfig/alermTypeConfigList/v2?areaId=" + this.listQuery.areaId;
      } else if (!utils.isNull(this.listQuery.areaId) && !utils.isNull(this.listQuery.equipRoomId.id)) {
        url =
          "/iot/sendMsgConfig/alermTypeConfigList/v2?areaId=" +
          this.listQuery.areaId +
          "&equipRoomId=" +
          this.listQuery.equipRoomId.id;
      } else {
        url =
          "/iot/sendMsgConfig/alermTypeConfigList/v2?equipRoomId=" +
          this.listQuery.equipRoomId.id;
      }
      getAction(url).then(res => {
        if (res.data.code == 200) {
          for (let index = 0; index < this.list.length; index++) {
            for (let index2 = 0; index2 < res.data.data.length; index2++) {
              if (this.list[index].id == res.data.data[index2].alertType) {
                this.list[index].msgConfigName =
                  res.data.data[index2].msgConfigName;
                this.list[index].msgConfigId =
                  res.data.data[index2].msgConfigId;
                this.list[index].delId =
                  res.data.data[index2].id;
              }
            }
          }
          this.list = JSON.parse(JSON.stringify(this.list));
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    delFun(row) {
      console.log(row, "row");
      this.$confirm("是否删除当前绑定的规则？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // let url = ''
        // if (this.listQuery.areaId) {
        //   url=`/iot/sendMsgConfig/delAlermTypeConfig/${row.id}/${this.listQuery.areaId}`
        // }else if (this.listQuery.areaId&&this.listQuery.equipRoomId) {
        //   url=`/iot/sendMsgConfig/delAlermTypeConfig/${row.id}/${this.listQuery.areaId}/${this.listQuery.equipRoomId.id}`
        // }else{
        //   url=`/iot/sendMsgConfig/delAlermTypeConfig/${row.id}/${this.listQuery.equipRoomId.id}`
        // }
        getAction(
          `/iot/sendMsgConfig/delAlermTypeConfigById/${row.delId}`
        ).then(res => {
          if (res.data.code == 200) {
            this.getList();
            this.$message.success(res.data.msg);
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    // 表格多选
    tableRowClick(row, column, event) {
      this.$refs.tableBar.toggleRowSelection(row);
    },
    getRowKeys(row) {
      return row.id;
    },
    tableSelectChange(val) {
      this.tableSelectList = JSON.parse(JSON.stringify(val));
    },
    delSelectRow(idx) {
      this.tableSelectList.splice(idx, 1);
      for (let item of this.list) {
        let isHas = this.tableSelectList.some(item2 => {
          return item2.id == item.id;
        });
        if (isHas) {
          this.$refs.tableBar.toggleRowSelection(item, true);
        } else {
          this.$refs.tableBar.toggleRowSelection(item, false);
        }
      }
      this.$forceUpdate();
    },
    showDlg() {
      if (this.tableSelectList.length <= 0) {
        this.$message({
          type: "warning",
          message: "请先选择报警类型"
        });
        return false;
      }
      this.title = "批量绑定";
      this.getRuleList();
      this.getSbjSelect()
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgState = true;
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },
    selectProductType(data) {
      let { value, label } = data;
      this.dlgData.msgConfigName = label;
      this.dlgData.msgConfigId = value;
    },
    subFunc() {
      if (this.tableSelectList.length <= 0) {
        this.$message({
          type: "warning",
          message: "请先选择报警类型"
        });
        return false;
      }
      this.$refs["dlgForm"].validate(valid => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));
          let alertTypes = [];
          let alertNames = [];
          for (let index = 0; index < this.tableSelectList.length; index++) {
            alertTypes.push(this.tableSelectList[index].id);
            alertNames.push(this.tableSelectList[index].name);
          }
          if (this.dlgData.equipRoomId) {
            sendObj.equipRoomId = this.dlgData.equipRoomId.id;
            sendObj.equipRoomName = arrId2Name(
              this.sbjSelect,
              sendObj.equipRoomId
            );
          }
          sendObj.alertTypes = alertTypes.join(",");
          sendObj.alertNames = alertNames.join(",");
          // console.log(sendObj, "sendObj");
          // return;
          postAction("/iot/sendMsgConfig/alermTypeBind", sendObj).then(res => {
            if (res.data.code == 200) {
              this.dlgState = false;
              this.getList();
              this.$message.success(res.data.msg);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
