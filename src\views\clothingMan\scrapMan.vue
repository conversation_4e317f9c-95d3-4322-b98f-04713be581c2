<template>
  <!--报废查询-->
  <div class="app-container">
    <div class="filter-container">
      <el-form ref="searchForm" class='clearfix' label-width="90px" @submit.native.prevent>
        <div class="fl">
          <el-form-item label="报废日期：">
            <el-date-picker v-model="listQuery.createTime" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class='fr'>
          <el-radio-group v-model="listQuery.auditType" @change="radioChange">
            <el-radio-button label="0">未审核</el-radio-button>
            <el-radio-button label="1">审核通过</el-radio-button>
            <el-radio-button label="2">审核不通过</el-radio-button>
          </el-radio-group>
          <el-input @focus="showBranchDlg()" v-model="listQuery.branchName" readonly placeholder="请选择部门">
            <i slot="suffix" @click="resetStr('branch')" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-input v-model="listQuery.str" placeholder='请输入申请人/申请科室'>
            <i slot="suffix" @click="resetStr('str')" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-button @click="searchItem" icon='el-icon-search' type="success" size='mini'>搜索</el-button>
          <el-button @click="resetItem" icon='el-icon-refresh' type="primary" size='mini'>重置</el-button>
        </div>
      </el-form>
    </div>

    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="报废日期">
          <template slot-scope="scope">
            <span>{{ scope.row.scrapDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报废科室">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报废数量">
          <template slot-scope="scope">
            <span>{{ scope.row.scrapNum }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报废原因">
          <template slot-scope="scope">
            <span>{{ scope.row.scrapTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="提报人">
          <template slot-scope="scope">
            <span>{{ scope.row.createName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.auditType == 0">未审核</span>
            <span v-else-if="scope.row.auditType == 1">审核通过</span>
            <span v-else-if="scope.row.auditType == 2">审核不通过</span>
          </template>
        </el-table-column>

        <el-table-column label="审核人">
          <template slot-scope="scope">
            <span>{{ scope.row.auditUserName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button icon="el-icon-view" type="success" size="mini" @click="viewItem(scope.row)" plain>查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="查看详情" :visible.sync="dlgShow">
      <el-form label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="报废科室:">
              {{dlgData.branchName}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报废日期:">
              {{dlgData.scrapDate}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="报废原因:">
          {{dlgData.scrapTypeText}}
        </el-form-item>
      </el-form>
      <div class="table-container">
        <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="detailList" border fit highlight-current-row>
          <el-table-column label="序号" type="index" width="50" align="center">
          </el-table-column>

          <el-table-column label="名称">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="规格">
            <template slot-scope="scope">
              <span>{{ scope.row.clothSpecificationText }}</span>
            </template>
          </el-table-column>

          <el-table-column label="寿命(月)">
            <template slot-scope="scope">
              <span>{{ scope.row.serviceLife }}</span>
            </template>
          </el-table-column>

          <el-table-column label="使用者">
            <template slot-scope="scope">
              <span>{{ scope.row.userName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="RFID" width="240px">
            <template slot-scope="scope">
              <span>{{ scope.row.rfid }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-container">
        <pagination v-show="detailTotal>0" :total="detailTotal" :page.sync="detailListQuery.page" :limit.sync="detailListQuery.size" @pagination="getDetailList" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false">关闭</el-button>
      </div>
    </el-dialog>
    <branchDlg />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import branchDlg from '@/components/Dialog/platformMan/branchDlg'

import {
  findScrapDynamic,
  findScrapInfo
} from '@/api/medicalMatchManSystem/clothingMan/scrapMan'

export default {
  components: {
    Pagination,
    branchDlg
  },
  data () {
    return {
      list: [],
      listQuery: {
        page: 1,
        size: 20,
        str: '',
        createTime: '',
        auditType: '',
        branchId: '',
        branchName: '',
      },
      total: 0,
      listLoading: false,
      dlgShow: false,
      dlgData: {},
      detailListQuery: {
        page: 1,
        size: 10
      },
      detailTotal: 0,
      detailList: [
      ]
    }
  },


  computed: {
    ...mapGetters('platformMan/branchDlg', {
      branchId: 'branchId',
      branchName: 'branchName'
    }),
  },

  watch: {
    branchId (val) {
      this.listQuery.branchId = val
    },
    branchName (val) {
      this.listQuery.branchName = val
    }
  },

  created () {
    this.getList()
  },


  methods: {
    // 部门相关
    showBranchDlg () {
      let branchId = this.listQuery.branchId
      let branchName = this.listQuery.branchName
      this.$store.commit('platformMan/branchDlg/SET_BRANCHID', branchId)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', branchName)
      this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', true)
    },

    // 主表格相关
    radioChange (val) {
      this.listQuery.auditType = val
      this.getList()
    },

    resetStr (flag) {
      if (flag == 'str') {
        this.listQuery.str = ""
      }
      else if (flag == 'branch') {
        this.listQuery.branchId = ""
        this.listQuery.branchName = ""
      }
      this.getList()
    },

    searchItem () {
      this.getList()
    },

    resetItem () {
      this.listQuery.str = ""
      this.listQuery.createTime = ""
      this.listQuery.auditType = ""
      this.listQuery.branchId = ""
      this.listQuery.branchName = ""
      this.getList()
    },

    getList () {
      if (utils.isNull(this.listQuery.createTime)) {
        this.listQuery.startTime = ""
        this.listQuery.endTime = ""
      }
      else {
        this.listQuery.startTime = this.listQuery.createTime[0]
        this.listQuery.endTime = this.listQuery.createTime[1]
      }
      this.list = []
      this.listLoading = true
      findScrapDynamic(this.listQuery).then(res => {
        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    getDetailList () {
      let postParam = this.detailListQuery
      postParam['scrapId'] = this.dlgData.id
      this.detailList = []
      findScrapInfo(postParam).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.detailList = utils.isNull(res.data.list) ? [] : res.data.list
          this.detailTotal = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    viewItem (data) {
      this.dlgData = data
      this.dlgShow = true
      this.getDetailList()
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>