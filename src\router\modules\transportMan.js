/** 运送管理 **/

import Layout from "@/views/layout/Layout";

const transportManRouter = {
  path: "/transportMan",
  component: Layout,
  name: "transportMan",
  meta: {
    title: "运送管理",
    icon: "ysgl",
    roles: ["yunsong<PERSON>n<PERSON>"]
  },
  children: [
    {
      path: "serviceMan",
      name: "调度中心",
      component: () => import("@/views/transportMan/serviceMan"),
      meta: {
        title: "调度中心",
        roles: ["diaoduzhongxin"]
      }
    },
    {
      path: "tranConfig",
      component: () => import("@/views/transportMan/tranConfig/index"),
      name: "运送配置",
      meta: {
        title: "运送配置",
        roles: ["yunsongpeizhi"]
      },
      children: []
    },
    {
      path: "tranLoop",
      component: () => import("@/views/transportMan/tranLoop"),
      name: "循环运送",
      meta: {
        title: "循环运送",
        roles: ["xunhuany<PERSON>ong"]
      },
      children: []
    }
  ]
};

export default transportManRouter;
