<template>
  <el-dialog :close-on-click-modal="false" :title="'选择点位'" :before-close="closeDlg" :visible.sync="dlgShow" append-to-body>
    <el-input placeholder="输入科目进行过滤" v-model="filterArea"> </el-input>
    <el-tree
      ref="areaTree"
      highlight-current
      node-key="id"
      :data="list"
      @node-click="treeNodeClick"
      :default-expanded-keys="defaultOpenList"
      :filter-node-method="filterNode"
      :expand-on-click-node="false"
      @node-expand="handleNodeExpand"
      @node-collapse="handleNodeCollapse"
    >
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span :title="data.name">{{ data.name }}</span>
        <span v-if="data.type == 0" class="fdanger">(区域分类)</span>
        <span v-if="data.type == 1" class="fsuccess">(点位)</span>
        <span v-if="data.status == 0" class="fsuccess">(启用)</span>
        <span v-if="data.status == 1" class="fdanger">(停用)</span>
      </span>
    </el-tree>

    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-span" v-if="selectAreaName">当前选中：{{ selectAreaName }}</span>
      <el-button icon="el-icon-back" @click="closeDlg"> 取 消 </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'

import { findAreaTree } from '@/api/reportMan'

export default {
  components: {},
  data() {
    return {
      filterArea: '',

      list: [],

      selectAreaId: '',

      selectAreaName: '',

      defaultOpenList: [],

      selectAreaNode: '',
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.customerCenter.areaDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('customerCenter/areaDlg/SET_DLGSHOW', val)
      },
    },

    dlgType: {
      get: function () {
        return this.$store.state.customerCenter.areaDlg.dlgType
      },
      set: function (val) {
        this.$store.commit('customerCenter/areaDlg/SET_DLGTYPE', val)
      },
    },

    curAreaId: {
      get: function () {
        return this.$store.state.customerCenter.areaDlg.curAreaId
      },
      set: function (val) {
        this.$store.commit('customerCenter/areaDlg/SET_CURAREAID', val)
      },
    },

    areaId: {
      get: function () {
        return this.$store.state.customerCenter.areaDlg.areaId
      },
      set: function (val) {
        // this.$store.commit('customerCenter/areaDlg/SET_AREAID', val)
      },
    },

    areaName: {
      get: function () {
        return this.$store.state.customerCenter.areaDlg.areaName
      },
      set: function (val) {
        this.$store.commit('customerCenter/areaDlg/SET_AREANAME', val)
      },
    },

    projectId: {
      get: function () {
        return this.$store.state.customerCenter.areaDlg.projectId
      },
      set: function (val) {
        this.$store.commit('customerCenter/areaDlg/SET_PROJECTID', val)
      },
    },
  },

  watch: {
    filterArea(val) {
      this.$refs.areaTree.filter(val)
    },

    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.areaId)) {
          this.selectAreaId = ''
          this.selectAreaName = ''
        }
        this.getList()
      }
    },

    areaId(val) {
      this.selectAreaId = val
    },

    areaName(val) {
      this.selectAreaName = val
    },

    dlgType(val) {},

    curAreaId(val) {},
  },

  methods: {
    // 筛选节点搜索
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },

    // 树节点展开
    handleNodeExpand(data) {
      // 保存当前展开的节点
      let flag = false
      this.defaultOpenList.some((item) => {
        if (item === data.id) {
          // 判断当前节点是否存在， 存在不做处理
          flag = true
          return true
        }
      })
      if (!flag) {
        // 不存在则存到数组里
        this.defaultOpenList.push(data.id)
      }
    },

    // 树节点关闭
    handleNodeCollapse(data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i
        }
      })
    },

    // 重置树状态
    resetTree() {
      this.$nextTick(() => {
        if (utils.isNull(this.selectAreaId)) {
          this.$refs.areaTree.setCurrentKey()
        } else {
          this.$refs.areaTree.setCurrentKey(this.selectAreaId)
        }
      })
    },

    // 树节点点击事件
    treeNodeClick(data) {
      if (this.dlgType === 'EDIT') {
        if (data.type == 1) {
          this.resetTree()
          this.$message({
            type: 'warning',
            message: '只能选择分类节点',
          })
          return false
        }

        this.selectAreaId = data.id
        this.selectAreaName = data.name
        return
      }
      if (data.status == 1) {
        this.resetTree()
        this.$message({
          type: 'warning',
          message: '当前点位已停用，无法选择',
        })
        return false
      }
      if (data.type == 0) {
        this.resetTree()
        this.$message({
          type: 'warning',
          message: '只能选择点位节点',
        })
        return false
      }

      this.selectAreaNode = data
      this.selectAreaId = data.id
      this.selectAreaName = data.name

      console.log('点击后selectAreaNode', this.selectAreaNode)
    },

    // 格式化数组 删除当前节点的 children
    formatList(list) {
      for (let i of list) {
        if (i.id == this.curAreaId) {
          i.children = []
          return
        }
        this.formatList(i['children'])
      }
      console.log(list)
    },

    getList() {
      if (utils.isNull(this.projectId)) {
        return
      }
      this.list = []
      findAreaTree(this.projectId).then((res) => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let list = res.data.data
          if (this.dlgType === 'EDIT') {
            this.formatList(list)
          }
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = [list[0].id]
          }
          this.list = list
          this.$nextTick(() => {
            this.$refs.areaTree.setCurrentKey(this.selectAreaId)
          })
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      console.log('---this.selectAreaId', this.selectAreaId)
      let areaId = this.selectAreaId
      this.areaName = this.selectAreaName
      if (utils.isNull(areaId)) {
        this.$message.warning('请选择一个区域')
        return
      }
      this.$store.commit('customerCenter/areaDlg/SET_AREANAME2', '')
      this.$store.commit('customerCenter/areaDlg/SET_AREANAME', '')
      this.$store.commit('customerCenter/areaDlg/SET_AREAID', '')

      setTimeout(() => {
        this.$store.commit('customerCenter/areaDlg/SET_AREANAME2', this.selectAreaNode ? this.selectAreaNode.names : '')
        this.$store.commit('customerCenter/areaDlg/SET_AREANAME', this.areaName)
        this.$store.commit('customerCenter/areaDlg/SET_AREAID', areaId)
        this.closeDlg()
      }, 200)
    },

    closeDlg() {
      this.$store.commit('customerCenter/areaDlg/SET_DLGTYPE', '')
      this.$store.commit('customerCenter/areaDlg/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

/deep/ .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f0f7ff;
}

/deep/ .el-tree-node:focus > .el-tree-node__content {
  background-color: transparent;
}
</style>