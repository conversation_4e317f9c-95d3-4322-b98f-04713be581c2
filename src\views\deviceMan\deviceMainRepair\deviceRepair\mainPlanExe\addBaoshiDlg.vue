<template>
  <el-dialog
    class="mazhenguo"
    title="报事登记"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="1060px"
    top="30px"
  >
    <div class="dflex">
      <el-form
        ref="dlgDataForm"
        :rules="dlgRules"
        :model="dlgData"
        label-position="right"
        label-width="110px"
        class="dia-left"
        style="padding-left: 0"
        size="mini"
        @submit.native.prevent
        :disabled="dlgType == 'info'"
      >
        <!-- <el-form ref="dia2Form" label-width="110px" class="dia-left" style="padding-left: 0" :rules="dia2Rules" :model="dlgData"> -->

        <!-- 12 报事登记 登记处理 -->
        <div class="dia-left-item" style="margin-top: 24px">
          <div class="dia-bar-title">报事登记</div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="单号">
                {{ dlgData.id == "" ? "系统自动生成" : dlgData.orderNo }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目" prop="projectId">
                <span >{{ dlgData.projectName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- v-if="dlgData.buildingName&&dlgData.buildingName!=null&&dlgData.buildingName!=''&&dia2TypeIndex=='2'" -->
            <el-col :span="12">
              <el-form-item
                v-if="dlgData.buildingName"
                label="楼栋信息"
                prop="buildingName"
              >
                <span>{{ dlgData.buildingName }}</span>
              </el-form-item>
            </el-col>
            <!-- v-if="dlgData.address&&dlgData.address!=null&&dlgData.address!=''&&dia2TypeIndex=='2'" -->
            <el-col v-if="dlgData.address" :span="12">
              <el-form-item label="详细地址" prop="address">
                <span>{{ dlgData.address }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="点位" prop="areaName">
                <el-input
                  v-if="dia2TypeIndex == 1 || dia2TypeIndex == 2"
                  @focus="showProjectDia('dianwei')"
                  v-model="dlgData.areaName"
                  placeholder="请选择点位"
                  readonly
                ></el-input>
                <span v-else>{{ dlgData.areaName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="科目" prop="subjectName">
                <el-input
                  v-if="dia2TypeIndex == 1 || dia2TypeIndex == 2"
                  @focus="showProjectDia('baoshi-kemu')"
                  v-model="dlgData.subjectName"
                  placeholder="请选择科目"
                  readonly
                ></el-input>
                <span v-else>{{ dlgData.subjectName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="内容描述" prop="content">
            <el-input
              v-if="dia2TypeIndex == 1 || dia2TypeIndex == 2"
              :autosize="{ minRows: 3, maxRows: 3 }"
              v-model="dlgData.content"
              type="textarea"
              resize="none"
              placeholder="请输入内容描述"
            />
            <span v-else>{{ dlgData.content }}</span>
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="报事人" prop="orderUserName">
                <el-input
                  v-if="dia2TypeIndex == 1"
                  v-model="dlgData.orderUserName"
                  placeholder="请输入报事人"
                ></el-input>
                <span v-else>{{ dlgData.orderUserName }}</span>
              </el-form-item>
            </el-col>
            <el-col
              :span="12"
              v-if="reportType == 9 && [0, 2].includes(dia2TypeIndex)"
            >
              <el-form-item label="报事人科室" prop="departmentName">
                <span>{{ dlgData.departmentName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报事人电话" prop="orderUserPhone">
                <el-input
                  v-if="dia2TypeIndex == 1"
                  v-model="dlgData.orderUserPhone"
                  placeholder="请输入报事人电话"
                ></el-input>
                <span v-else>{{ dlgData.orderUserPhone }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="来源">
                {{ dlgData.orderSource }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="紧急度" prop="urgency">
                <el-select
                  v-if="dia2TypeIndex == 1 || dia2TypeIndex == 2"
                  v-model="dlgData.urgency"
                  placeholder="请选择紧急度"
                >
                  <el-option
                    v-for="item of dia2JjdSelect"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
                <span v-else>{{ dlgData.urgencyText }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否预约" prop="isArrival">
                <el-radio-group
                  v-if="dia2TypeIndex == 1 || dia2TypeIndex == 2"
                  v-model="dlgData.isArrival"
                >
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
                <span v-else>{{ dlgData.isArrival == 1 ? "是" : "否" }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="类型" prop="userType">
                <el-radio-group
                  v-if="dia2TypeIndex == 1 || dia2TypeIndex == 2"
                  v-model="dlgData.userType"
                >
                  <el-radio label="LXERP">龙行erp</el-radio>
                  <el-radio label="JYTUSER">简E通</el-radio>
                </el-radio-group>
                <!-- <span v-else-if="dlgData.userType == 'LXERP'">{{ dlgData.userType == 'LXERP' ? '龙行erp' : '简E通' }}</span> -->
                <span v-else-if="dlgData.userType == 'LXERP'">龙行erp</span>
                <span v-else-if="dlgData.userType == 'JYTUSER'">简E通</span>
                <span v-else></span>
              </el-form-item>
            </el-col>

            <el-col :span="12" v-if="dlgData.isArrival == 1">
              <el-form-item label="预约到达时间" prop="arrivalTime">
                <el-date-picker
                  v-if="dia2TypeIndex == 1 || dia2TypeIndex == 2"
                  v-model="dlgData.arrivalTime"
                  type="datetime"
                  style="width: 100%"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择预约到达时间"
                >
                </el-date-picker>
                <span v-else>{{ dlgData.arrivalTime }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div class="dia-right flex1">
        <div class="">
          <div class="dia-right-title">附件</div>
          <div class="mt10">
            <div v-for="(item, index) of dlgData.otherFiles" :key="index">
              <div
                v-if="item.type == 'wav' || item.type == 'mp3'"
                style="margin-top: 4px; margin-bottom: 4px"
              >
                <div class="fl" style="line-height: 36px">语音：</div>
                <audio
                  controls
                  class="fl"
                  style="width: 280px; height: 36px"
                  :id="`audio-${index}`"
                  :src="item.url"
                ></audio>
                <!-- <div class="fl dia-fj-name elli" :title="item.name">{{item.name}}</div> -->
                <!-- <div class="fr m-a" @click="playAudio(index)">播放</div> -->
                <div class="clear"></div>
              </div>
              <div v-else>
                <div class="fl">图片：</div>
                <div class="fl dia-fj-name elli" :title="item.name">
                  {{ item.name }}
                </div>
                <el-image
                  :id="`dia-fj-${index}`"
                  style="width: 0px; height: 0px"
                  :preview-src-list="[item.url]"
                  :src="item.url"
                  alt=""
                ></el-image>
                <div @click="showBigImg(index)" class="fr m-a">查看</div>
                <div class="clear"></div>
              </div>
            </div>
          </div>
          <el-upload
            action=""
            :show-file-list="false"
            :before-upload="uploadFunc2"
          >
            <el-button
              v-if="dia2TypeIndex == 1"
              class="mt10"
              icon="el-icon-plus"
              type="primary"
              >选择本地文件</el-button
            >
          </el-upload>
        </div>
        <div class="mt40" v-if="dlgData.orderFollowupList.length > 0">
          <div class="dia-right-title">跟进</div>

          <div
            v-for="(item, index) of dlgData.orderFollowupList"
            :key="index"
            class="mt20"
          >
            <div class="fbold">第 {{ index + 1 }} 次跟进</div>
            <div>跟进方式：{{ item.followWayText }}</div>
            <div>跟进人：{{ item.createUser }}</div>
            <div>跟进时间：{{ item.createTime }}</div>
            <div>未完成原因：{{ item.undoneReasonText }}</div>
            <div>跟进备注：{{ item.remark }}</div>
            <div>预计完成时间：{{ item.expectedTime }}</div>
          </div>
        </div>
        <div class="mt40" v-if="dlgData.attentionName">
          <div class="dia-right-title">关注</div>

          <div class="mt20">
            <div>关注人：{{ dlgData.attentionName }}</div>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button
        v-if="dlgType != 'info'"
        :loading="dlgSubLoading"
        type="success"
        @click="dlgSubFunc"
        icon="el-icon-check"
      >
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
    <Bmtree v-if="dlgState" />
  </el-dialog>
</template>
<script>
import { mapGetters } from "vuex";
import * as utils from "@/utils";
import { postAction, getAction, putAction } from "@/api";
import Bmtree from "@/components/Dialog/Bmtree"; // 部门弹窗
import { uploadImg3 } from "@/utils/uploadImg";

import {
  spusPage, // 获取项目列表
  baoShiDengJi
} from "@/api/reportMan";

let dlgDataEmpty = {
  id: "",
  projectId: JSON.parse(window.localStorage.userInfo).projectId, // 项目id
  projectName: JSON.parse(window.localStorage.userInfo).projectName, // 项目名称
  areaId: "", // 报事区域id
  areaName: "", // 报事区域名称
  subjectId: "", // 报事科目id
  subjectName: "", // 报事科目名称
  content: "", // 报事内容
  orderUserName: "", // 报事人姓名
  orderUserId: "0", // 报事人id
  orderUserPhone: "", // 报事人电话
  urgency: "", // 紧急度
  urgencyText: "", // 紧急度
  isArrival: "", // 是否预约  0 不预约   1 预约
  userType: "LXERP", // LXERP-龙行erp  JYTUSER-简E通

  arrivalTime: "", // 预约到达时间
  otherFiles: [], // 附件信息地址 arr
  status: "0", // 0:已登记 1:已审核 2:已派工 3:已确认 4：已到达 5：已回单 6：已回访 7：已归档 8：已作废
  orderSource: "设备检修", // 报事来源  设备检修 、 设备检修

  // 3 报事审核
  auditResult: "", // 审核结果
  auditResultText: "",
  auditContent: "", // 审核意见
  auditName: "", // 审核人
  auditTime: "", // 审核时间

  // 4 派工
  // orderId -- id
  modelType: "", // 接单模式  1:共同执行 2:竞争执行
  modelTypeText: "", // 接单模式  1:共同执行 2:竞争执行
  usersInfo: [], // 派工信息 userId userName postId postName
  usersInfoNames: "", // 岗位s

  // 5 报事回访
  // id: dlgData.id,
  returnVisit: "", // 回访方式
  returnVisitText: "", // 回访方式
  isSatisfied: "", // 满意度
  isSatisfiedText: "", // 满意度
  returnRemark: "", // 回访记录

  // 7 改派
  // orderId -- id
  modelType1: "", // 接单模式  1:共同执行 2:竞争执行
  modelTypeText1: "", // 接单模式  1:共同执行 2:竞争执行
  usersInfo1: [], // 派工信息 userId userName postId postName
  usersInfoNames1: "", // 岗位s

  // 8 跟进
  followWay: "", // 跟进方式
  followWayText: "",
  undoneReason: "", // 未完成原因
  undoneReasonText: "", //
  remark: "", // 跟进备注
  expectedTime: "", // 预约完成时间
  followNum: "", // 跟进班次

  //暂缓
  orderSuspendCheckLogs: [],
  //物料
  materialDetails: [],

  // 关注
  gzUserInfos: [],
  gzUserInfosNames: "",
  gzRemark: "",

  orderFollowupList: []
};
export default {
  components: {
    Bmtree
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {
      type: Object,
      default: {}
    },

    dlgData0: {
      type: Object,
      default: {}
    },
  },
  data() {
    var arrivalTimeValid = (rule, value, callback) => {
      if (this.dia2TypeIndex == 1 || this.dia2TypeIndex == 2) {
        if (!value) {
          callback(new Error("请选择预约到达时间"));
        }
        let t_date = new Date();
        let t_time = t_date.getTime();
        value += ":00";
        console.log("合并后value", value);
        let v_date = new Date(value);
        let v_time = v_date.getTime();

        if (v_time <= t_time) {
          callback(new Error("只能选择当前时刻以后的时间"));
        }
      }

      callback();
    };
    return {
      dlgState: false,
      dlgLoading: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        projectId: [{ required: true, message: "必填字段", trigger: "change" }],
        areaName: [{ required: true, message: "必填字段", trigger: "change" }],
        subjectName: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        content: [{ required: true, message: "必填字段", trigger: "blur" }],
        orderUserName: [
          { required: true, message: "必填字段", trigger: "blur" }
        ],
        orderUserPhone: [
          { required: true, message: "必填字段", trigger: "blur" }
        ],
        urgency: [{ required: true, message: "必填字段", trigger: "change" }],
        isArrival: [{ required: true, message: "必填字段", trigger: "change" }],
        userType: [{ required: true, message: "必填字段", trigger: "change" }],
        arrivalTime: [
          { required: true, message: "必填字段", trigger: "change" },
          { validator: arrivalTimeValid, trigger: "change" }
        ]
      },
      dlgSubLoading: false, // 提交loading
      ////////////////
      userInfo: "",
      reportType: 9,
      dia2TypeIndex: 1,
      projectList: [],
      dia2JjdSelect: [
        { id: "1", name: "不紧急" },
        { id: "2", name: "一般" },
        { id: "3", name: "急迫" }
      ]
    };
  },
  watch: {
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          if (this.dlgType == "add") {
            this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
            this.$nextTick(() => {
              this.$refs["dlgDataForm"].clearValidate();
            });
          } else {
            this.getInit();
          }
        }, 50);
      } else {
        this.closeDlg();
      }
    },
    bmTreeSelect(val) {
      if (val === "empty") {
        return false;
      }
      console.log("返回数据", val);
      if (this.dlgState) {
        let bmObj = JSON.parse(val);
        if (this.otherDiaType == "project") {
          this.dlgData.projectId = bmObj.id;
          this.dlgData.projectName = bmObj.label;
        }
        if (this.otherDiaType == "dianwei") {
          // 点位（区域）
          this.dlgData.areaId = bmObj.id;
          this.dlgData.areaName = bmObj.labels;
        }
        if (this.otherDiaType == "baoshi-kemu") {
          this.dlgData.subjectId = bmObj.id;
          this.dlgData.subjectName = bmObj.labels;
        }
      }
    }
  },
  computed: {
    ...mapGetters(["bmTreeSelect"])
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);

    this.getTreeList();
  },
  methods: {
    // 查看图片
    showBigImg(index) {
      $(`#dia-fj-${index}`).click();
    },
    // 上传附件
    uploadFunc2(file) {
      uploadImg3(file, "ERP_web/reportMan/enclosure/report_enclosure_").then(
        res => {
          this.dlgData.otherFiles.push(this.imgUrl2Obj(res));
        }
      );
    },
    imgUrl2Obj(url) {
      let name = url.split("/");
      name = name[name.length - 1];
      let type = name.split(".")[1];
      if (type != "wav" && type != "mp3") {
        type = "image";
      }
      let obj = {
        type,
        url,
        name
      };
      return obj;
    },
    // 弹出选择项目弹窗
    showProjectDia(type) {
      this.otherDiaType = type;
      if (type == "baoshi-kemu" || type == "dianwei") {
        console.log("this.dlgData.projectId", this.dlgData.projectId);
        if (this.dlgData.projectId === "") {
          this.$message({
            type: "warning",
            message: "请先选择项目"
          });
          return false;
        }
        let query = {
          projectId: this.dlgData.projectId
        };
        this.$store.commit("SET_BMTREE_QUERY", JSON.stringify(query));
      }
      this.$store.commit("SET_BMTREE_TYPE", type);
      this.$store.commit("SET_BMTREESTATE", true);
    },
    // 对话框项目切换
    projectChangeDlg(e) {
      this.dlgData.areaId = "";
      this.dlgData.areaName = "";
      this.dlgData.subjectId = "";
      this.dlgData.subjectName = "";
    },
    // 获取项目树
    getTreeList() {
      this.projectList = [];
      let sendObj = {
        postId: this.userInfo.postId,
        page: 1,
        size: 99999
      };
      spusPage(sendObj).then(res1 => {
        let res = res1.data;
        if (res.code == 200) {
          let list = res.list;
          for (let item of list) {
            item.id = item.projectId;
            item.name = item.projectName;
          }
          console.log("下拉框", list);
          this.projectList = list;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    ///////////////////

    // 弹窗提交 ------
    dlgSubFunc() {
      this.$refs["dlgDataForm"].validate(valid => {
        if (valid) {
          let otherFiles = [];
          for (let item of this.dlgData.otherFiles) {
            otherFiles.push(item.url);
          }

          let loading = this.$loading({
            lock: true,
            text: "提交中",
            background: "rgba(0, 0, 0, 0.7)"
          });
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));
          // 项目名称
          sendObj.urgencyText = utils.arrId2Name(this.dia2JjdSelect, sendObj.urgency);

          // 紧急度
          sendObj.otherFiles = otherFiles.join(","); // 附件信息地址 arr

          sendObj.status = "0"; // dlgData.status 0:已登记 1:已审核 2:已派工 3:已确认 4：已到达 5：已回单 6：已回访 7：已归档 8：已作废
          // let sendObj = {
          //   id: dlgData.id,
          //   projectId: dlgData.projectId, // 项目id
          //   projectName: arrId2Name(this.projectList, dlgData.projectId), // 项目名称
          //   areaId: dlgData.areaId, // 报事区域id
          //   areaName: dlgData.areaName, // 报事区域名称
          //   subjectId: dlgData.subjectId, // 报事科目id
          //   subjectName: dlgData.subjectName, // 报事科目名称
          //   content: dlgData.content, // 报事内容
          //   orderUserName: dlgData.orderUserName, // 报事人姓名
          //   orderUserId: dlgData.orderUserId, // 报事人id
          //   orderUserPhone: dlgData.orderUserPhone, // 报事人电话
          //   urgency: dlgData.urgency, // 紧急度
          //   urgencyText: arrId2Name(this.dia2JjdSelect, dlgData.urgency), // 紧急度
          //   orderWay: dlgData.orderWay, // 报事还是品质
          //   isArrival: dlgData.isArrival, // 是否预约  0 不预约   1 预约

          //   userType: dlgData.userType,

          //   arrivalTime: dlgData.arrivalTime, // 预约到达时间
          //   otherFiles: otherFiles.join(','), // 附件信息地址 arr
          //   status: '0', // dlgData.status 0:已登记 1:已审核 2:已派工 3:已确认 4：已到达 5：已回单 6：已回访 7：已归档 8：已作废
          //   orderSource: dlgData.orderSource, // 报事来源

          //   address: dlgData.address || '',
          //   buildingName: dlgData.buildingName || '',
          // }

          baoShiDengJi(sendObj).then(res1 => {
            loading.close();
            let res = res1.data;
            if (res.code == 200) {
              this.$message.success(res.msg);
              this.dlgState = false;
              this.$emit("getList");
              this.closeDlg();
              this.$emit("upList1");
            } else {
              this.$message({
                type: "warning",
                message: res.msg
              });
            }
          });

          ///////////////////////////////////////////
          // let sendObj = JSON.parse(JSON.stringify(this.dlgData));

          // // == 数据字典
          // // sendObj.checkCycleStr = utils.arrId2Name(
          // //   this.pzbyzqSelect,
          // //   sendObj.checkCycle
          // // );

          // let url = "";
          // let func = "";
          // if (this.dlgType == "add") {
          //   url = "/aaaa/create";
          //   func = postAction;
          // } else {
          //   url = "/aaaa/update";
          //   func = putAction;
          // }

          // this.dlgSubLoading = true;
          // func(url, sendObj).then(res0 => {
          //   this.dlgSubLoading = false;
          //   let res = res0.data;

          //   if (res.code == 200) {
          //     this.$message.success(res.msg);
          //     this.dlgState = false;
          //     this.$emit("getList");
          //     this.closeDlg();
          //     this.$emit("upList1");
          //   } else {
          //     this.$message({
          //       type: "warning",
          //       message: res.msg
          //     });
          //   }
          // });
        }
      });
    },

    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.dlgState = false;
      // this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.$refs["dlgDataForm"].resetFields();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.mt20 {
  margin-top: 20px;
}
.mt40 {
  margin-top: 40px;
}
.dia-left {
  border-right: 1px solid #ccc;
  width: 640px;
}
.dia-left-item {
  position: relative;
  border-top: 1px solid #ccc;
  padding-top: 20px;
  margin-right: 20px;
  margin-top: 4px;
}
.dia-bar-title {
  position: absolute;
  top: -15px;
  width: 90px;
  background: #fff;
  line-height: 30px;
  font-size: 15px;
  font-weight: bold;
}

.dia-right {
  padding: 20px;
}
.dia-right-title {
  font-size: 14px;
  font-weight: bold;
}

.dia-fj-name {
  width: 160px;
}
.el-table .bg-yellow {
  background: #fef4e5;
}

.el-image.archiveUrl {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
