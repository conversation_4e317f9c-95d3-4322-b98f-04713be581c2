// 库房dlg组件

const storageDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    storageId: '',

    storageName: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    storageId: state => state.storageId,

    storageName: state => state.storageName
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_STORAGEID: (state, val) => {
      state.storageId = val
    },

    SET_STORAGENAME: (state, val) => {
      state.storageName = val
    }
  },

  actions: {

  }
}

export default storageDlg
