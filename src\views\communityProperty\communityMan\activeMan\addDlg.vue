<template>
  <!-- 弹窗 新增/编辑 -->
  <el-dialog
    class="mazhenguo"
    title="活动维护"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="800px"
    top="30px"
  >
    <el-form
      ref="dlgDataForm"
      :rules="dlgRules"
      :model="dlgData"
      label-position="right"
      label-width="100px"
      style="width: 750px"
      size="mini"
      @submit.native.prevent
    >
      <el-form-item label="小区名称" prop="communityId">
        <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区">
          <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="活动名称" prop="name">
        <el-input v-model="dlgData.name" placeholder="请输入" />
      </el-form-item>

      <el-row>
        <el-col :span="12">
          <!-- 上传图片-单张 -->
          <el-form-item label="活动logo" prop="logoImgUrl">
            <div v-if="dlgData.logoImgUrl && dlgType == 'info'">无</div>
            <div v-else>
              <el-upload
                class="avatar-uploader"
                v-if="!dlgData.logoImgUrl"
                action=""
                :show-file-list="false"
                :before-upload="uploadImg1"
              >
                <i class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-else class="upload-bar">
                <el-image
                  class="avatar"
                  :preview-src-list="[dlgData.logoImgUrl]"
                  :z-index="9999"
                  :src="dlgData.logoImgUrl"
                  alt=""
                ></el-image>
                <i @click="delImg1" class="el-icon-error avatar_icon"></i>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- 上传图片-单张 -->
          <el-form-item label="活动图片" prop="imgUrl">
            <div v-if="dlgData.imgUrl && dlgType == 'info'">无</div>
            <div v-else>
              <el-upload
                class="avatar-uploader"
                v-if="!dlgData.imgUrl"
                action=""
                :show-file-list="false"
                :before-upload="uploadImg2"
              >
                <i class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-else class="upload-bar">
                <el-image
                  class="avatar"
                  :preview-src-list="[dlgData.imgUrl]"
                  :z-index="9999"
                  :src="dlgData.imgUrl"
                  alt=""
                ></el-image>
                <i @click="delImg2" class="el-icon-error avatar_icon"></i>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="活动介绍" prop="remark">
        <el-input
          :autosize="{ minRows: 3, maxRows: 4 }"
          v-model="dlgData.remark"
          type="textarea"
          placeholder="请输入"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="活动时间" prop="dateRange">
        <el-date-picker
          style="width: 100%"
          v-model="dlgData.dateRange"
          type="datetimerange"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="截止时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="活动奖励商品">
        <el-button class="mb10" icon="el-icon-plus" type="primary" @click="dlgAddFn">添加</el-button>

        <!-- 活动列表 -->
        <div>
          <div class="flexct mb10" v-for="(item, index) of dlgData.activityGoods" :key="index">
            <div class="flex-sub">
              <el-input
                v-model="item.name"
                placeholder="商品名称"
                :disabled="dlgType == 'info' || (dlgType == 'edit' && !item.itemType)"
              />
              <el-input
                class="mt10"
                :autosize="{ minRows: 3, maxRows: 4 }"
                v-model="item.remark"
                type="textarea"
                placeholder="商品描述(限200字)"
                style="width: 100%"
                :disabled="dlgType == 'info' || (dlgType == 'edit' && !item.itemType)"
              />

              <div class="flexlc mt10">
                <el-input-number
                  class="mr10"
                  v-model="item.maxCount"
                  :min="dlgType == 'add' ? 0 : item.useCount"
                  placeholder="商品最大数量"
                  :step="1"
                  :step-strictly="true"
                  controls-position="right"
                  style="width: 180px"
                  :disabled="dlgType == 'info'"
                />
                <el-button v-if="dlgType == 'edit' && !item.itemType" type="primary" @click="itemChangeNum(index)"
                  >修改数量</el-button
                >
                <span class="ml10" v-if="dlgType != 'add'">已领取数量：{{ item.useCount }}</span>
              </div>
            </div>
            <div class="ml10 mr10">
              <el-upload
                class="avatar-uploader"
                v-if="!item.imgUrl"
                action=""
                :show-file-list="false"
                :before-upload="(file) => uploadImgItem(file, index)"
              >
                <i class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-else class="upload-bar" style="margin-right: 0; margin-bottom: 0">
                <el-image
                  class="avatar"
                  :preview-src-list="[item.imgUrl]"
                  :z-index="9999"
                  :src="item.imgUrl"
                  alt=""
                ></el-image>
                <i @click="delImgItem(index)" class="el-icon-error avatar_icon" v-if="dlgType == 'add'"></i>
              </div>
            </div>

            <el-button
              @click="delItem(index)"
              style="margin-top: 62px"
              size="mini"
              type="danger"
              icon="el-icon-delete"
              plain
              >删除</el-button
            >
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button
        v-if="dlgType != 'info'"
        :loading="dlgSubLoading"
        type="success"
        @click="dlgSubFunc"
        icon="el-icon-check"
      >
        <span v-if="dlgSubLoading">保存中...</span>
        <span v-else>保存</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
import Tinymce from '@/components/Tinymce' // 富文本组件
// 工具
// import { phoneReg } from '@/utils/regUtil'
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
// 接口
import * as utils from '@/utils'
import { postAction, getAction } from '@/api'
import { communityPage } from '@/api/communityMan'
import { payFeeConfigPage } from '@/api/costMan'

let dlgDataEmpty = {
  id: '0', //		body	false	int32
  communityId: '', //	小区id	body	false	int32
  communityName: '', //	小区名称	body	false	string
  name: '', //	活动名称，	body	false	string
  logoImgUrl: '', //	活动logo	body	false	string
  imgUrl: '', //		活动图片	false	string
  remark: '', //		活动介绍	false	string

  dateRange: [],
  startDate: '', // 活动时间
  endDate: '', //	使用券截至时间	body	false	date-time

  activityGoods: [
    // {
    //   activityId: '', //	活动id	body	false	int32
    //   id: '', //		body	false	int32
    //   name: '', //	活动名称，	body	false	string
    //   remark: '', //		body	false	string
    //   maxCount: '', //	最大数量	body	false	int32
    //   useCount: '', //	已领取数量
    //   imgUrl: '', //		body	false	string
    //   flag: '', //	0 正常 1 删除	body	false	int32
    // },
  ], //		body	false	活动商品信息	活动商品信息

  // 不需要
  // activityCode: '', //	活动码	body	false	string
  // activityOwnerUsers: '', //		body	false	活动参与用户	活动参与用户

  // flag: '', //	0:正常 1:删除	body	false	int32
  // projectId: '', //	项目id	body	false	int32
  // qrCodeUrl: '', //	二维码连接	body	false	string

  // status: '', //	0:进行中 1:已结束	body	false	int32
  // updateBy: '', //		body	false	int32
  // updateTime: '', //	更新时间	body	false	date-time
  // userCount: '', //	使用数量
}

export default {
  components: {
    Tinymce,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},

    communityList: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          let dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
          if (this.dlgType != 'add') {
            dlgData = this.dlgQuery

            dlgData.dateRange = [dlgData.startDate, dlgData.endDate]
            // dlgData.rollId = this.dlgQuery.rollId
            // dlgData.userId = this.dlgQuery.userId
            // dlgData.name = this.dlgQuery.staffName
            // dlgData.branchName = this.dlgQuery.staffName
            // 净领额
          } else {
            setTimeout(() => {
              this.dlgAddFn()
            }, 1000)
          }
          this.dlgData = JSON.parse(JSON.stringify(dlgData))
          this.$nextTick(() => {
            this.$refs['dlgDataForm'].clearValidate()
          })

          if (this.dlgType != 'add') {
            this.getGoodList()
          }
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      userInfo: '',
      // 弹窗
      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        logoImgUrl: [{ required: true, message: '必填字段', trigger: 'change' }],
        imgUrl: [{ required: true, message: '必填字段', trigger: 'change' }],
        remark: [{ required: true, message: '必填字段', trigger: 'blur' }],
        dateRange: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      dlgSubLoading: false, // 提交loading
    }
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },
  methods: {
    // 获取商品列表
    getGoodList() {
      getAction(`/unity/activity/info/${this.dlgData.id}`).then((res0) => {
        let res = res0.data
        if (res.code == 200) {
          this.dlgData.activityGoods = res.data.activityGoods
        } else {
          this.$message.error(res.msg)
        }
      })
    },

    // << 弹窗列表
    dlgAddFn() {
      let obj = {
        activityId: this.dlgData.id, //	活动id	body	false	int32
        id: '0', //		body	false	int32
        name: '', //	活动名称，	body	false	string
        remark: '', //		body	false	string
        maxCount: '', //	最大数量	body	false	int32
        useCount: 0, //	已领取数量
        imgUrl: '', //		body	false	string
        flag: '0',
      }

      if (this.dlgType == 'edit') {
        obj.itemType = 'editAdd'
      }
      console.log('this.dlgData.activityGoods', this.dlgData.activityGoods)
      this.dlgData.activityGoods.push(obj)
    },

    uploadImgItem(file, index) {
      if (file.size > 2 * 1024 * 1024) {
        this.$message({
          type: 'warning',
          message: '上传图片大小不能超过2M',
        })
        return false
      }
      uploadImg(file, 'jianyitong/community_activities/activity_goods/activity_good_').then((res) => {
        this.dlgData.activityGoods[index].imgUrl = res
      })
    },
    delImgItem(index) {
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning', // // success, warning, info, error
      }).then(() => {
        this.dlgData.activityGoods[index].imgUrl = ''
      })
    },
    // 删除整行
    delItem(index) {
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning', // // success, warning, info, error
      }).then(() => {
        if (this.dlgType == 'add') {
          this.dlgData.activityGoods.splice(index, 1)
        } else if (this.dlgType == 'edit') {
          let item = this.dlgData.activityGoods[index]
          if (item.itemType && item.itemType == 'editAdd') {
            this.dlgData.activityGoods.splice(index, 1)
          } else {
            let sendObj = {
              activityId: this.dlgData.id,
              id: this.dlgData.activityGoods[index].id,
              maxCount: this.dlgData.activityGoods[index].maxCount,
              flag: 1, // 0 正常 1 删除
            }
            postAction(`/unity/activity/updateGoods`, sendObj).then((res0) => {
              let res = res0.data
              if (res.code == 200) {
                this.dlgData.activityGoods.splice(index, 1)
              } else {
                this.$message.warning(res.msg)
              }
            })
          }
        }
      })
    },
    itemChangeNum(index) {
      let sendObj = {
        activityId: this.dlgData.id,
        id: this.dlgData.activityGoods[index].id,
        maxCount: this.dlgData.activityGoods[index].maxCount,
        flag: 0,
      }
      postAction(`/unity/activity/updateGoods`, sendObj).then((res0) => {
        let res = res0.data
        if (res.code == 200) {
          this.$message.success(res.msg)
        } else {
          this.$message.warning(res.msg)
        }
      })
    },

    // >> 弹窗列表

    // << 单张上传
    uploadImg1(file) {
      if (file.size > 2 * 1024 * 1024) {
        this.$message({
          type: 'warning',
          message: '上传图片大小不能超过2M',
        })
        return false
      }
      // 'product/subImage/subImage_
      uploadImg(file, 'jianyitong/community_activities/activity_logo/activity_logo_').then((res) => {
        this.dlgData.logoImgUrl = res
      })
    },
    delImg1() {
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning', // // success, warning, info, error
      }).then(() => {
        this.dlgData.logoImgUrl = ''
      })
    },
    // >> 单张上传
    // << 单张上传
    uploadImg2(file) {
      if (file.size > 2 * 1024 * 1024) {
        this.$message({
          type: 'warning',
          message: '上传图片大小不能超过2M',
        })
        return false
      }
      // 'product/subImage/subImage_
      uploadImg(file, 'jianyitong/community_activities/activity_img/activity_img_').then((res) => {
        this.dlgData.imgUrl = res
      })
    },
    delImg2() {
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning', // // success, warning, info, error
      }).then(() => {
        this.dlgData.imgUrl = ''
      })
    },
    // >> 单张上传

    // 数据字典

    // 弹窗提交 ------
    dlgSubFunc() {
      this.$refs['dlgDataForm'].validate((valid) => {
        if (valid) {
          // 列表验证
          for (let i = 0; i < this.dlgData.activityGoods.length; i++) {
            let item = this.dlgData.activityGoods[i]
            if (utils.isNull(item.name)) {
              this.$message.warning(`请输入第${i + 1}行【商品名称】`)
              return false
            }
            if (utils.isNull(item.remark)) {
              this.$message.warning(`请输入第${i + 1}行的【商品描述】`)
              return false
            }
            if (utils.isNull(item.maxCount)) {
              this.$message.warning(`请输入第${i + 1}行的【最大数量】`)
              return false
            }
            if (utils.isNull(item.imgUrl)) {
              this.$message.warning(`请输入第${i + 1}行的【活动奖励商品图片】`)
              return false
            }
          }

          let sendObj = JSON.parse(JSON.stringify(this.dlgData))
          sendObj.communityName = utils.arrId2Name(this.communityList, sendObj.communityId)
          if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
            sendObj.startDate = sendObj.dateRange[0]
            sendObj.endDate = sendObj.dateRange[1]
          }
          delete sendObj.dateRange

          if (this.dlgType == 'edit') {
            let activityGoodsNew = []
            for (let item of sendObj.activityGoods) {
              if (item.itemType && item.itemType == 'editAdd') {
                delete item.itemType
                activityGoodsNew.push(item)
              }
            }
            sendObj.activityGoods = activityGoodsNew
          } else {
            sendObj.projectId = this.userInfo.projectId
          }

          console.log('sendObj', sendObj)
          // return false
          this.dlgSubLoading = true
          postAction('unity/activity/saveOrUpdate', sendObj).then((res0) => {
            let res = res0.data
            this.dlgSubLoading = false
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.dlgState = false
              this.$emit('getList')
              this.$emit('closeDlg')
              this.$emit('upList1')
            } else {
              this.$message({
                type: 'warning',
                message: res.msg,
              })
            }
          })
        }
      })
    },

    closeDlg() {
      this.dlgLoading = false
      this.dlgSubLoading = false
      this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')

      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.$nextTick(() => {
        this.$refs['dlgDataForm'].clearValidate()
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>