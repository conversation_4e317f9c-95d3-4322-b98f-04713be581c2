<template>
  <!--被服洗消-->
  <div class="app-container">
    <div class="filter-container">
      <el-form ref="searchForm" class='clearfix' label-width="90px" @submit.native.prevent>
        <div class="fl">
          <el-form-item label="送洗日期：">
            <el-date-picker v-model="listQuery.createTime" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class='fr'>
          <el-input @focus="showBranchDlg()" v-model="listQuery.branchName" readonly placeholder="请选择部门">
            <i slot="suffix" @click="resetStr('branch')" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-input v-model="listQuery.str" placeholder='请输入名称'>
            <i slot="suffix" @click="resetStr('str')" class="el-input__icon el-icon-error"></i>
          </el-input>
          <el-button @click="searchItem" icon='el-icon-search' type="success" size='mini'>搜索</el-button>
          <el-button @click="resetItem" icon='el-icon-refresh' type="primary" size='mini'>重置</el-button>
        </div>
      </el-form>
    </div>

    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50" align="center">
        </el-table-column>

        <el-table-column label="部门科室" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="材质">
          <template slot-scope="scope">
            <span>{{ scope.row.clothMaterialText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="规格">
          <template slot-scope="scope">
            <span>{{ scope.row.clothSpecificationText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="RFID" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.rfid }}</span>
          </template>
        </el-table-column>

        <el-table-column label="洗消次数">
          <template slot-scope="scope">
            <span>{{ scope.row.hasWashCount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="送洗时间" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.sxRecordDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="送洗转运时间" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.sxzzRecordDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="洗消接收时间" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.xxjsRecordDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="洗消发放时间" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.xxffRecordDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="发放转运时间" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.ffzzRecordDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="科室接收时间" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.qhRecordDate }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>

    <branchDlg />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import branchDlg from '@/components/Dialog/platformMan/branchDlg'

import {
  findWashOpeRecordDynamicWeb
} from '@/api/medicalMatchManSystem/clothingMan/clothingClean'

export default {
  components: {
    Pagination,
    branchDlg
  },
  data () {
    return {
      list: [],
      listQuery: {
        page: 1,
        size: 20,
        str: '',
        createTime: '',
        branchId: '',
        branchName: '',
      },
      total: 0,
      listLoading: false,
      dlgShow: false,
      dlgData: {},
      detailListQuery: {
        page: 1,
        size: 10
      },
      detailTotal: 0,
      detailList: [
      ]
    }
  },


  computed: {
    ...mapGetters('platformMan/branchDlg', {
      branchId: 'branchId',
      branchName: 'branchName'
    }),
  },

  watch: {
    branchId (val) {
      this.listQuery.branchId = val
    },
    branchName (val) {
      this.listQuery.branchName = val
    }
  },

  created () {
    this.getList()
  },


  methods: {
    // 部门相关
    showBranchDlg () {
      let branchId = this.listQuery.branchId
      let branchName = this.listQuery.branchName
      this.$store.commit('platformMan/branchDlg/SET_BRANCHID', branchId)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', branchName)
      this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', true)
    },

    resetStr (flag) {
      if (flag == 'str') {
        this.listQuery.str = ""
      }
      else if (flag == 'branch') {
        this.listQuery.branchId = ""
        this.listQuery.branchName = ""
      }
      this.getList()
    },

    searchItem () {
      this.getList()
    },

    resetItem () {
      this.listQuery.str = ""
      this.listQuery.createTime = ""
      this.listQuery.auditType = ""
      this.listQuery.branchId = ""
      this.listQuery.branchName = ""
      this.getList()
    },

    getList () {
      if (utils.isNull(this.listQuery.createTime)) {
        this.listQuery.startTime = ""
        this.listQuery.endTime = ""
      }
      else {
        this.listQuery.startTime = this.listQuery.createTime[0]
        this.listQuery.endTime = this.listQuery.createTime[1]
      }
      this.list = []
      this.listLoading = true
      findWashOpeRecordDynamicWeb(this.listQuery).then(res => {
        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>