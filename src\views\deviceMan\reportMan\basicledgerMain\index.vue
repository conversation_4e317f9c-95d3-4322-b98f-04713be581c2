<template>
  <div
    class="app-container mazhenguo"
    style="padding-top: 10px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-select
              v-model="searchForm.equPosition"
              placeholder="请选择设备所在位置"
              clearable
              filterable
              class="fl"
              style="width: 180px"
            >
              <el-option
                v-for="(item, index) in equPositionList"
                :key="index"
                :label="item.equPosition"
                :value="item.equPosition"
              ></el-option>
            </el-select>
          </div>
          <div class="search-item">
            <el-date-picker
              v-model="timeRange"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="fl"
              style="width: 220px"
            >
            </el-date-picker>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      ref="tableBar"
      height="calc((100vh - 290px)/2 - 30px)"
      class="m-small-table"
      v-loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equPosition"
        label="设备所在位置"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="equName"
        label="设备名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="maintainCount"
        label="保养次数"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="m-a" @click="showDlg(scope.row, '保养次数')">{{
            scope.row.maintainCount
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="invokeCount"
        label="检修次数"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="m-a" @click="showDlg(scope.row, '检修次数')">{{
            scope.row.invokeCount
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="repairCount"
        label="维修次数"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="m-a" @click="showDlg(scope.row, '维修次数')">{{
            scope.row.repairCount
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="userCount"
        label="所用人工"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="m-a" @click="showDlg(scope.row, '所用人工')">{{
            scope.row.userCount
          }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getList"
    />
    <div class="clear"></div>

    <div ref="barChart" style="height: calc((100vh - 290px) / 2)"></div>

    <el-dialog
      width="1500px"
      :visible.sync="dialogVisible"
      :title="`${searchForm.equPosition}   ${name}   ${seriesName}`"
      :close-on-click-modal="false"
    >
      <basic-table
        ref="basicTable"
        class="homeTable"
        :TableHeaderList="TableHeaderList"
        :tableData="basicTableData"
        :searchForm="basicTableSearchForm"
        :seriesName="seriesName"
        :name="name"
        :total="basicTotal"
        :hasPagination="seriesName === '所用人工' ? false : true"
        :tableHeight="600"
        :rowHeight="30"
        @getRenYuanTableData="getRenYuanTableData"
        @getWeiXiuTableData="getWeiXiuTableData"
        @getJianXiuTableData="getJianXiuTableData"
        @getBaoYangTableData="getBaoYangTableData"
      ></basic-table>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { postAction, getAction, deleteAction } from "@/api";
import basicTable from "./basicTable.vue"; // 公用table组件
import { getDefaultDateRange } from "@/utils";
import * as echarts from "echarts";
import { equInvokeMaintainStatistic } from "@/api/deviceMan/reportMan.js";
import Pagination from "@/components/Pagination"; // 分页
let dlgDataEmpty = {
  id: 0,
  remark: "",
};
export default {
  components: {
    Pagination,
    basicTable,
  },
  data() {
    return {
      basicTableSearchForm: {
        pageNo: 1,
        pageSize: 20,
      },
      name: "",
      basicTotal: 0,
      seriesName: "",
      TableHeaderList: [], //table表头数据
      basicTableData: [], //table数据
      timeRange: undefined,
      barChartInstance: null,
      time: "",
      dlgBtnLoading: false,
      dlgShow: false,
      // 表单验证
      dlgRules: {
        remark: [{ required: true, message: "必填字段", trigger: "blur" }],
      },
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)), // 弹窗值
      dialogVisible: false,
      exportList: [],
      listQueryExport: {
        pageNo: 1,
        pageSize: 10,
        type: "TABLE_SBBY",
      },
      totalExport: 0,
      searchForm: {
        statisticStartTime: "",
        statisticEndTime: "",
        projectId: JSON.parse(window.localStorage.userInfo).projectId,
        equPosition: "",
        pageNo: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [],
      listLoading: false,
      equPositionList: [],
    };
  },
  created() {},
  mounted() {
    this.getEquPositionList(JSON.parse(window.localStorage.userInfo).projectId);
    this.timeRange = getDefaultDateRange();
  },
  methods: {
    async getEquPositionList(val) {
      try {
        const res = await getAction(
          `sa/green/equ/equ-position/page?projectId=${val}&equPosition=&pageNo=1&pageSize=1000`
        );
        const { code, data } = res.data;
        if (code === "200") {
          this.equPositionList = data.list || [];
        } else {
          this.$message.error(res.data.msg);
        }
      } catch (error) {
        this.$message.error("获取数据失败");
      }
    },
    async searchFunc() {
      this.searchForm.pageNo = 1;
      await this.getList();
    },

    validateForm() {
      const { projectId, equPosition } = this.searchForm;
      const errorMessages = [];
      if (!projectId) {
        errorMessages.push("请选择项目");
      }
      if (!equPosition) {
        errorMessages.push("请选择设备所在位置");
      }

      if (errorMessages.length) {
        this.$message.warning(errorMessages.join("；"));
        return false;
      }

      return true;
    },
    async getList() {
      if (!this.validateForm()) {
        return;
      }
      this.listLoading = true; // 开始加载数据，设置加载状态为true
      let postData = JSON.parse(JSON.stringify(this.searchForm));
      if (this.timeRange) {
        postData.statisticStartTime = this.timeRange[0];
        postData.statisticEndTime = this.timeRange[1];
      }

      try {
        const res = await equInvokeMaintainStatistic(postData);
        let { code, data } = res.data;
        if (code === "200") {
          this.tableData = data.list || [];
          this.total = data.total || 0;

          if (this.tableData.length > 0) {
            this.barChartInit(); // 只有当数据非空时才初始化图表
          } else {
            this.barChartInstance && this.barChartInstance.dispose(); // 销毁图表
            this.barChartInstance = null;
          }
        } else {
          this.$message.error(data.msg);
        }
      } catch (error) {
        console.log(error);

        this.$message.error("获取数据失败");
      } finally {
        this.listLoading = false; // 无论成功或失败，最后都设置加载状态为false
      }
    },
    // 渲染地图
    barChartResize() {
      if (this.barChartInstance) {
        this.barChartInstance.resize(); // 调整图表大小
      }
    },
    barChartInit() {
      if (!this.barChartInstance) {
        this.barChartInstance = echarts.init(this.$refs.barChart);
      }
      window.addEventListener("resize", this.barChartResize); // 添加响应式调整大小的监听

      const barCommonConfig = {
        label: {
          show: true,
          position: "top",
          color: "#666",
          fontSize: 12,
        },
        barWidth: 20,
        showBackground: true,
      };

      const option = {
        title: {
          top: 15,
          left: 15,
          text: "设备维保记录",
          textStyle: {
            color: "#262626",
            fontWeight: 500,
            fontSize: 18,
          },
        },
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            if (params.seriesName === "保养次数") {
              return (
                params.name +
                "<br/>" +
                params.seriesName +
                ": " +
                params.value.maintainCount +
                "次"
              );
            }
            if (params.seriesName === "检修次数") {
              return (
                params.name +
                "<br/>" +
                params.seriesName +
                ": " +
                params.value.invokeCount +
                "次"
              );
            }
            if (params.seriesName === "维修次数") {
              return (
                params.name +
                "<br/>" +
                params.seriesName +
                ": " +
                params.value.repairCount +
                "次"
              );
            }
            if (params.seriesName === "所用人工") {
              return (
                params.name +
                "<br/>" +
                params.seriesName +
                ": " +
                params.value.userCount +
                "人"
              );
            }
          },
        },
        legend: {
          data: ["保养次数", "检修次数", "维修次数", "所用人工"], // 系列名称列表，与 series 中的 name 对应
          top: 20, // legend 组件离容器顶部的距离
          left: "center", // legend 组件在容器中的水平位置，可以是 'left'、'center' 或 'right'，也可以是具体的像素值
          textStyle: {
            // 图例文字的样式
            color: "#333",
            fontSize: 14,
          },
          itemGap: 10, // 每个图例项之间的间隔
          itemWidth: 25, // 图例标记的图形宽度
          itemHeight: 14, // 图例标记的图形高度
        },
        xAxis: {
          type: "category",
          axisLabel: {
            color: "#666",
            interval: 0,
          },
          axisTick: {
            show: false, //x轴刻度线
          },
          axisLine: {
            //X轴线
            show: true,
            lineStyle: {
              color: "#E0E0E0",
            },
          },
        },
        yAxis: {
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#666",
          },
          splitLine: {
            lineStyle: {
              color: "#fff", // 虚线颜色
            },
          }, //去除背景网格线
        },
        grid: {
          top: "80px",
          left: "50px",
          right: "24px",
          bottom: "36px",
        },
        dataset: {
          dimensions: [
            "equName",
            "maintainCount",
            "invokeCount",
            "repairCount",
            "userCount",
          ],
          // 使用函数确保在获取数据时tableData已经被正确赋值
          source: this.tableData,
        },
        series: [
          {
            ...barCommonConfig,
            type: "bar",
            name: "保养次数",
            itemStyle: { color: "#4874CB" },
          },
          {
            ...barCommonConfig,
            type: "bar",
            name: "检修次数",
            itemStyle: { color: "#EE822F" },
          },
          {
            ...barCommonConfig,
            type: "bar",
            name: "维修次数",
            itemStyle: { color: "#F2BA02" },
          },
          {
            ...barCommonConfig,
            type: "bar",
            name: "所用人工",
            itemStyle: { color: "#75BD42" },
          },
        ],
        //滚动条
        dataZoom: [
          {
            show: this.tableData.length > 10 ? true : false,
            height: 12,
            bottom: 0,
            startValue: 0, //起始值
            endValue: 9, //结束值
            showDetail: false,
            fillerColor: "rgba(1, 132, 213, 0.4)", // 滚动条颜色
            borderColor: "rgba(17, 100, 210, 0.12)",
            backgroundColor: "rgba(221, 220, 107, .1)", //两边未选中的滑动条区域的颜色
            handleSize: 1, //两边手柄尺寸
            showDetail: false, //拖拽时是否展示滚动条两侧的文字
            zoomLock: true, //是否只平移不缩放
            moveOnMouseMove: false, //鼠标移动能触发数据窗口平移
            // minValueSpan: 5,  // 放大到最少几个
            // maxValueSpan: 5,  //  缩小到最多几个
          },
          {
            type: "inside", // 支持内部鼠标滚动平移
            start: 0,
            // end: 20,
            startValue: 0, // 从头开始。
            endValue: 10, // 最多5个
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
          },
        ],
      };
      this.barChartInstance.setOption(option);
      this.barChartInstance.off("click");
      this.barChartInstance.on("click", async (param) => {
        console.log(param, "点击");

        this.name = param.name;
        this.seriesName = param.seriesName;
        this.basicTableSearchForm = {
          pageNo: 1,
          pageSize: 20,
        };
        if (param.seriesName === "所用人工") {
          await this.getRenYuanTableData({
            pageNo: 1,
            pageSize: param.data.userCount,
          });
        } else if (param.seriesName === "维修次数") {
          await this.getWeiXiuTableData();
        } else if (param.seriesName === "检修次数") {
          await this.getJianXiuTableData();
        } else if (param.seriesName === "保养次数") {
          await this.getBaoYangTableData();
        }
        // 点击弹窗方法
      });
    },
    async showDlg(row, type) {
      this.basicTableSearchForm = {
        pageNo: 1,
        pageSize: 20,
      };
      this.name = row.equName;
      this.seriesName = type;
      if (type === "所用人工") {
        await this.getRenYuanTableData({ pageNo: 1, pageSize: row.userCount });
      } else if (type === "维修次数") {
        await this.getWeiXiuTableData();
      } else if (type === "检修次数") {
        await this.getJianXiuTableData();
      } else if (type === "保养次数") {
        await this.getBaoYangTableData();
      }
    },
    getTimeRangeForApi() {
      return this.timeRange ? [this.timeRange[0], this.timeRange[1]] : ["", ""];
    },
    // 新增的接口调用方法
    async fetchUserInvokeData(pageNo, pageSize) {
      const [statisticStartTime, statisticEndTime] = this.getTimeRangeForApi();

      const params = {
        projectId: this.searchForm.projectId,
        equName: this.name,
        pageNo,
        pageSize,
        statisticStartTime,
        statisticEndTime,
      };

      const res = await getAction(
        "sa/green/equipment-manage/invoke-maintain-statistic/user-statistic",
        params
      );
      return res.data; // 直接返回接口返回的数据
    },

    async getRenYuanTableData(query = { pageNo: 1, pageSize: 10 }) {
      this.TableHeaderList = [
        {
          FieldDisplayName: "用户名称",
          Field: "userName",
        },
        {
          FieldDisplayName: "保养次数",
          Field: "maintainCount",
        },
        {
          FieldDisplayName: "检修次数",
          Field: "invokeCount",
        },
        {
          FieldDisplayName: "维修次数",
          Field: "repairCount",
        },
      ];
      // 调用接口方法获取数据
      const { code, data, msg } = await this.fetchUserInvokeData(
        query.pageNo,
        query.pageSize
      );
      if (code === "200") {
        if (Array.isArray(data) && data.length === 0) {
          this.$message.error("暂无数据");
          return; // 如果data为空，则直接返回，不再执行后续代码
        }
        let list = data;
        for (let item of list) {
          if (item.maintenanceItem) {
            item.maintenanceItem = JSON.parse(item.maintenanceItem);
          } else {
            item.maintenanceItem = [];
          }
        }
        this.basicTableData = list || []; // 使用空数组作为默认值
        // this.basicTotal = data.total || 0;
        this.dialogVisible = true;
      } else {
        this.$message.error(msg || "接口返回错误"); // 如果msg不存在，则显示默认错误信息
      }
    },
    async fetchRepairManageData(pageNo, pageSize) {
      const [statisticStartTime, statisticEndTime] = this.getTimeRangeForApi();

      const params = {
        projectId: this.searchForm.projectId,
        name: this.name,
        pageNo,
        pageSize,
        statisticStartTime,
        statisticEndTime,
        status: 0,
      };

      const res = await getAction("sa/green/equ/repair-manage/page", params);
      return res.data; // 直接返回接口返回的数据
    },

    async getWeiXiuTableData(query = { pageNo: 1, pageSize: 10 }) {
      this.TableHeaderList = [
        {
          FieldDisplayName: "设备编码",
          Field: "equCode",
        },
        {
          FieldDisplayName: "设备名称",
          Field: "equName",
        },
        {
          FieldDisplayName: "内容描述",
          Field: "content",
        },
        {
          FieldDisplayName: "维修状态",
          Field: "statusStr",
        },
        {
          FieldDisplayName: "完成时间",
          Field: "finishTime",
        },
        {
          FieldDisplayName: "维修人员",
          Field: "repairPersonName",
        },
      ];
      // 调用接口方法获取数据
      const { code, data, msg } = await this.fetchRepairManageData(
        query.pageNo,
        query.pageSize
      );

      if (code === "200") {
        if (Array.isArray(data.list) && data.list.length === 0) {
          this.$message.error("暂无数据");
          return; // 如果data为空，则直接返回，不再执行后续代码
        }
        this.basicTableData = data.list || []; // 使用空数组作为默认值
        this.basicTotal = data.total || 0;
        this.dialogVisible = true;
      } else {
        this.$message.error(msg || "接口返回错误"); // 如果msg不存在，则显示默认错误信息
      }
    },
    // 新增的接口调用方法
    async fetchCheckInvokeData(pageNo, pageSize) {
      const [statisticStartTime, statisticEndTime] = this.getTimeRangeForApi();

      const params = {
        projectId: this.searchForm.projectId,
        name: this.name,
        pageNo,
        pageSize,
        statisticStartTime,
        statisticEndTime,
        invokeStatus: 1,
      };

      const res = await getAction("sa/green/equ/check-invoke/page", params);
      return res.data; // 直接返回接口返回的数据
    },

    async getJianXiuTableData(query = { pageNo: 1, pageSize: 10 }) {
      this.TableHeaderList = [
        {
          FieldDisplayName: "设备编码",
          Field: "equCode",
        },
        {
          FieldDisplayName: "计划名称",
          Field: "checkName",
        },
        {
          FieldDisplayName: "设备名称",
          Field: "equName",
        },
        {
          FieldDisplayName: "设备型号",
          Field: "equModel",
        },
        {
          FieldDisplayName: "设备位置",
          Field: "equPosition",
        },
        {
          FieldDisplayName: "计划检修日期",
          Field: "checkDate",
        },
        {
          FieldDisplayName: "检修事项",
          Field: "checkItem", // 注意：这个字段可能需要特殊处理，因为它在模板中使用了自定义模板
        },
        {
          FieldDisplayName: "执行时间",
          Field: "invokeTime",
        },
        {
          FieldDisplayName: "执行情况",
          Field: "invokeStatusStr", // 假设这是从scope.row.invokeStatusStr获取的数据
        },
        // {
        //   FieldDisplayName: "更换配件",
        //   Field: "accessoryJson", // 这个字段在模板中被注释掉了，如果需要可以取消注释
        // },
        {
          FieldDisplayName: "执行人",
          Field: "invokeUserName",
        },
        {
          FieldDisplayName: "备注",
          Field: "info",
        },
        {
          FieldDisplayName: "检修周期",
          Field: "checkCycleStr",
        },
      ];
      // 调用接口方法获取数据
      const { code, data, msg } = await this.fetchCheckInvokeData(
        query.pageNo,
        query.pageSize
      );

      if (code === "200") {
        if (Array.isArray(data.list) && data.list.length === 0) {
          this.$message.error("暂无数据");
          return; // 如果data为空，则直接返回，不再执行后续代码
        }
        let list = data.list;
        for (let item of list) {
          if (item.checkItem) {
            item.checkItem = JSON.parse(item.checkItem);
          } else {
            item.checkItem = [];
          }
        }
        this.basicTableData = list || []; // 使用空数组作为默认值
        this.basicTotal = data.total || 0;
        this.dialogVisible = true;
      } else {
        this.$message.error(msg || "接口返回错误"); // 如果msg不存在，则显示默认错误信息
      }
    },
    // 新增的接口调用方法
    async fetchMaintenanceInvokeData(pageNo, pageSize) {
      const [statisticStartTime, statisticEndTime] = this.getTimeRangeForApi();

      const params = {
        projectId: this.searchForm.projectId,
        name: this.name,
        pageNo,
        pageSize,
        statisticStartTime,
        statisticEndTime,
        invokeStatus: 1,
      };

      const res = await getAction(
        "sa/green/equ/maintenance-invoke/page",
        params
      );
      return res.data; // 直接返回接口返回的数据
    },

    async getBaoYangTableData(query = { pageNo: 1, pageSize: 10 }) {
      this.TableHeaderList = [
        {
          FieldDisplayName: "计划名称",
          Field: "maintenanceName",
        },
        {
          FieldDisplayName: "设备名称",
          Field: "equName",
        },
        {
          FieldDisplayName: "设备型号",
          Field: "equModel",
        },
        {
          FieldDisplayName: "设备位置",
          Field: "equPosition",
        },
        {
          FieldDisplayName: "计划保养日期",
          Field: "maintenanceDate",
        },
        {
          FieldDisplayName: "保养事项",
          Field: "maintenanceItem", // 注意：这个字段可能需要特殊处理，因为它在模板中使用了自定义模板
        },
        {
          FieldDisplayName: "执行时间",
          Field: "invokeTime",
        },
        {
          FieldDisplayName: "执行情况",
          Field: "invokeStatusStr", // 假设这是从scope.row.invokeStatusStr获取的数据
        },
        // {
        //   FieldDisplayName: "更换配件",
        //   Field: "accessoryJson", // 这个字段在模板中被注释掉了，如果需要可以取消注释
        // },
        {
          FieldDisplayName: "执行人",
          Field: "invokeUserName",
        },
        {
          FieldDisplayName: "备注",
          Field: "info",
        },
        {
          FieldDisplayName: "保养周期",
          Field: "maintenanceCycleStr",
        },
      ];
      // 调用接口方法获取数据
      const { code, data, msg } = await this.fetchMaintenanceInvokeData(
        query.pageNo,
        query.pageSize
      );

      if (code === "200") {
        if (Array.isArray(data.list) && data.list.length === 0) {
          this.$message.error("暂无数据");
          return; // 如果data为空，则直接返回，不再执行后续代码
        }
        let list = data.list;
        for (let item of list) {
          if (item.maintenanceItem) {
            item.maintenanceItem = JSON.parse(item.maintenanceItem);
          } else {
            item.maintenanceItem = [];
          }
        }
        this.basicTableData = list || []; // 使用空数组作为默认值
        this.basicTotal = data.total || 0;
        this.dialogVisible = true;
      } else {
        this.$message.error(msg || "接口返回错误"); // 如果msg不存在，则显示默认错误信息
      }
    },
  },
  // 在组件销毁时移除监听器
  beforeDestroy() {
    window.removeEventListener("resize", this.barChartResize);
    // 如果需要，也可以销毁 echarts 实例
    if (this.barChartInstance) {
      this.barChartInstance.dispose();
    }
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped></style>