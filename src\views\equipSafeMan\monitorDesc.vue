<template>
  <!-- 综合运行监控详情 -->
  <div class="app-container">
    <div class="config-container clearfix">
      <div
        class="config-item"
        v-for="(item, index) in configList"
        :key="index"
        @click="pageTo(index)"
      >
        <div class="config-tip">{{ item.name }}</div>
        <div class="config-count">
          <span :title="item.count">{{ item.count }}</span> {{ item.unit }}
        </div>
        <img :src="`/static/image/equip/${item.icon}-<EMAIL>`" alt="" />
      </div>
      <el-button size="small" plain @click="pageBack">返回</el-button>
    </div>
    <div class="tab-container clearfix">
      <el-button
        class="el-icon-arrow-left"
        circle
        size="small"
        @click="scrollBar('LEFT')"
      ></el-button>
      <div class="tab-menu" ref="tabMenu">
        <span
          @click="tabClick(item.id)"
          class="tab-item"
          :class="{ active: equipId == item.id }"
          v-for="item in equipList"
          :key="item.id"
          >{{ item.name }}</span
        >
      </div>
      <el-button
        class="el-icon-arrow-right"
        circle
        size="small"
        @click="scrollBar('RIGHT')"
      ></el-button>
    </div>
    <div class="equip-container clearfix">
      <div class="equip-item" v-for="(item, index) in list" :key="index">
        <div class="title">
          {{ item.equipName }} ({{ item.equipNodes.length }})
        </div>
        <div class="node-container clearfix">
          <div
            class="node-item"
            v-for="(item1, index1) in item.equipNodes"
            :key="index1"
            @click="viewItem(item1)"
          >
            <div :title="item1.name" class="node-name">{{ item1.name }}</div>
            <div class="node-info">
              <template
                v-if="
                  item1.protocolType == '1017' || item1.protocolType == '1033'
                "
              >
                <div>
                  U：<span
                    :class="item1.nodeDescs.d1008Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1008 || 0 }}V</span
                  >
                  <span :class="item1.nodeDescs.d1009Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1009 || 0 }}V</span
                  >
                  <span :class="item1.nodeDescs.d1010Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1010 || 0 }}V</span
                  >
                </div>
                <div>
                  A：<span
                    :class="item1.nodeDescs.d1011Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1011 || 0 }}A</span
                  >
                  <span :class="item1.nodeDescs.d1012Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1012 || 0 }}A</span
                  >
                  <span :class="item1.nodeDescs.d1013Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1013 || 0 }}A</span
                  >
                </div>
                <div>
                  有功：<span
                    :class="item1.nodeDescs.d1014Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1014 || 0 }}w</span
                  >， 电量：
                  <span :class="item1.nodeDescs.d1041Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1041 || 0 }}kwh</span
                  >
                  <!-- <span :class="item1.nodeDescs.d1041Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1041 || 0 }}kwh</span
                  > -->
                </div>

                <div v-if="item1.protocolType == '1033'">
                  <!-- 漏电电流 -->
                  漏电电流：
                  <span :class="item1.nodeDescs.d1038Status == 1 ? 'error' : ''"
                    >{{ item1.nodeDescs.d1038 || 0 }}mA</span
                  >
                </div>
              </template>
              <div
                v-else
                class="node-info-item"
                :class="item2.status == 1 ? 'error' : ''"
                v-for="(item2, index2) in item1.nodeDescs"
                :key="index2"
              >
                {{ item2.text }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      title="历史记录"
      :close-on-click-modal="false"
      append-to-body
      :visible.sync="dlgShow"
      width="900px"
      top="30px"
    >
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="列表" name="liebiao">
          <div class="filter-container">
            <el-form inline :model="listQuery" @submit.native.prevent>
              <el-date-picker
                @change="searchFunc"
                v-model="listQuery.dateRange"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
              <el-button
                icon="el-icon-search"
                type="success"
                size="mini"
                @click="searchFunc"
                >搜索</el-button
              >
            </el-form>
          </div>

          <div class="table-container">
            <el-table
              ref="tableBar"
              class="m-small-table"
              :data="historyList"
              border
              fit
              highlight-current-row
            >
              <el-table-column label="#" type="index" align="center" width="60">
              </el-table-column>

              <el-table-column
                v-for="(item, index) of fieldList"
                :key="index"
                :label="item.label"
                width="200"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row[item.key] }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="page-container">
            <pagination
              :total="historyTotal"
              :page.sync="listQuery.page"
              :limit.sync="listQuery.limit"
              @pagination="getHistoryList"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="趋势" name="qushi">
          <el-date-picker
            class="fl ml10"
            style="width: 350px"
            @change="getZxt"
            v-model="zxtQuery.dateRange"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="mini"
          >
          </el-date-picker>
          <el-select
            @change="handleSelectChange"
            class="fl ml10"
            style="width: 200px"
            v-model="zxtQuery.disRespVos"
            multiple
            collapse-tags
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.type"
              :label="item.name"
              :value="`${item.type},${item.name}`"
            >
            </el-option>
          </el-select>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            class="search-right-btn fl"
            @click="getZxt"
            >搜索</el-button
          >
          <div class="clear"></div>

          <div
            v-if="showChart2"
            id="echart-bar2"
            style="height: 500px; margin-top: 16px"
          ></div>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
      </div>
    </el-dialog>
    <CameraDlg
      ref="cameraDlg"
      :dlgData0="dlgCameraState"
      :dlgQuery="dlgCameraQuery"
    />
  </div>
</template>

<script>
import CameraDlg from "../electricalFireMonitoring/components/cameraDlg";
import { mapGetters } from "vuex";
import Cookie from "js-cookie";
import * as echarts from "echarts";
import moment, { localeData } from "moment"; //导入文件
import { getAction, postAction } from "../../api";
import * as utils from "@/utils";
import {
  arrId2Name, // 根据id 获取name
  isNull,
  getDataDictOther // 数据字典
} from "@/utils";
import {
  integratedOperationMonitoring,
  findEquipNodeMonitorConfig
} from "@/api/basicManSystem/comprehenOperateMonitor.js";
import {
  protocolLoran // 报警信息
} from "@/api/safetyMonitoringApi";
import Pagination from "@/components/Pagination";
//折线图
let zxtQueryEmpty = {
  disRespVos: [],
  nodeId: "",
  startTime: "",
  endTime: "",
  dateRange: []
};
export default {
  components: {
    Pagination,
    CameraDlg
  },
  data() {
    return {
      activeName: "liebiao",
      zxtQuery: JSON.parse(JSON.stringify(zxtQueryEmpty)),
      options: [],
      bjDiaRow: {},
      showChart2: false,
      echartRoom2: null,
      zxtSelect: [],
      equipId: "",

      list: [],

      equipList: [], // 设备间列表

      configList: [
        {
          name: "设备总数",
          count: 0,
          unit: "台",
          icon: "zongshebei"
        },
        {
          name: "在线数量",
          count: 0,
          unit: "台",
          icon: "zaixianshebei"
        },
        {
          name: "离线数量",
          count: 0,
          unit: "台",
          icon: "lixianshebei"
        },
        {
          name: "报警总数",
          count: 0,
          unit: "次",
          icon: "baojingzongshu"
        },
        {
          name: "今日报警",
          count: 0,
          unit: "次",
          icon: "jinribaojing"
        },
        {
          name: "待处理报警",
          count: 0,
          unit: "次",
          icon: "daichuli"
        }
      ],

      listQuery: {
        id: "",
        label: "",
        page: 1,
        limit: 10,
        dateRange: ""
      },
      dlgCameraQuery: {},
      dlgCameraState: false,
      userInfo: JSON.parse(window.localStorage.userInfo),
      dlgShow: false,
      historyList: [],
      historyTotal: 0,
      fieldList: []
    };
  },

  created() {
    this.equipId = this.$route.params.equipId;
    this.getMonitor();
    this.getEquipNodeStatistics();
    this.getList();
  },

  mounted() {},

  methods: {
    // 滚动条滑动
    scrollBar(type) {
      let step = 120;
      let $tabMenu = this.$refs.tabMenu;
      $tabMenu.scrollLeft =
        type === "LEFT"
          ? $tabMenu.scrollLeft - step
          : $tabMenu.scrollLeft + step;
    },

    // 项目设备获取数据
    getMonitor() {
      getAction("/iot/integratedOperationMonitoring/v2?equipType=1").then(
        res => {
          if (res.data.code == 200) {
            this.equipList = JSON.parse(
              JSON.stringify(res.data.data.equipRoomItems)
            );
          } else {
            this.$message.error(res.data.msg);
          }
        }
      );
    },
    chengeNumDanwei(val) {
      let num = parseInt(val);
      if (val > 10000) {
        num = parseInt(num / 1000);
        num = num / 10 + "万";
      }
      return num;
    },
    getEquipNodeStatistics() {
      this.deviceData = "";
      getAction(
        `/iot/equip-node/equipNodeStatisticsScreens?projectId=${this.userInfo.projectId}&equipType=1`
      ).then(res => {
        if (res.data.code == 200) {
          console.log(res, "res");
          this.configList[0]["count"] =
            this.chengeNumDanwei(res.data.data.countEquipNum) +
            this.chengeNumDanwei(res.data.data.countCameraNum);
          this.configList[1]["count"] =
            this.chengeNumDanwei(res.data.data.countOnlineNum) +
            this.chengeNumDanwei(res.data.data.cameraOnlineNum);
          this.configList[2]["count"] =
            this.chengeNumDanwei(res.data.data.countOfflineNum) +
            this.chengeNumDanwei(res.data.data.cameraOfflineNum);
          this.configList[3]["count"] = res.data.data.countAlarmNum;
          this.configList[4]["count"] = res.data.data.todayAlarmNum;
          this.configList[5]["count"] = res.data.data.waitForAlarmNum;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    showCameraDlg(idx) {
      let item = {
        state: idx,
        type: 1
      };
      this.dlgCameraQuery = item;
      this.$refs.cameraDlg.dlgState = true;
    },
    // 跳转
    pageTo(idx) {
      if (idx == 0) {
        //总数
        this.showCameraDlg(999);
      } else if (idx == 1) {
        //在线
        this.showCameraDlg(0);
      } else if (idx == 2) {
        //离线
        this.showCameraDlg(2);
      } else if (idx == 3) {
        this.$router.push({ path: `/equipSafeMan/abnormalAlarmGroup` });
      } else if (idx == 4) {
        this.$router.push({
          path: `/equipSafeMan/abnormalAlarmGroup`,
          query: { today: utils.getToday() }
        });
      } else if (idx == 5) {
        this.$router.push({
          path: `/equipSafeMan/abnormalAlarmMan`,
          query: { status: "0" }
        });
      }
    },

    // 页面返回
    pageBack() {
      // this.$router.push("/equipSafeMan/comprehenOperateMonitor");
      this.$router.go(-1);
    },

    // 选项卡切换
    tabClick(id) {
      this.equipId = id;
      this.getList();
    },

    // 历史数据
    getHistoryList() {
      let postParam = JSON.parse(JSON.stringify(this.listQuery));
      postParam.beginTime = this.listQuery.dateRange
        ? this.listQuery.dateRange[0]
        : "";
      postParam.endTime = this.listQuery.dateRange
        ? this.listQuery.dateRange[1]
        : "";

      protocolLoran(postParam).then(res => {
        if (res.data.code == 200) {
          // 表头
          this.fieldList = [];
          for (let key in res.data.data.field) {
            let label = res.data.data.field[key];
            this.fieldList.push({
              key,
              label
            });
          }
          this.historyTotal = res.data.data.total;
          this.historyList = res.data.list;
        } else {
          this.$message.warning(res.data.msg);
        }
      });
    },

    searchFunc() {
      this.listQuery.page = 1;
      this.getHistoryList();
    },

    viewItem(data) {
      this.activeName = "liebiao";
      this.dlgShow = true;
      this.listQuery.id = data.id;
      this.bjDiaRow = data;
      console.log(data, "data");
      this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
      this.searchFunc();
    },

    // 格式化数据
    formatList() {
      for (let i of this.list) {
        for (let j of i.equipNodes) {
          if (j.protocolType === "1017" || j.protocolType === "1033") {
            let nodeDescs = {};
            for (let k of j.nodeDescs) {
              nodeDescs[k.dataType] = k.text;
              nodeDescs[k.dataType + "Status"] = k.status;
            }
            j.nodeDescs = nodeDescs;
          }
        }
      }
    },

    // 获取数据
    getList() {
      if (utils.isNull(this.equipId)) {
        return;
      }
      findEquipNodeMonitorConfig(this.equipId).then(res => {
        if (res.data.code == 200) {
          this.list = res.data.data || [];
          this.formatList();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    //tab切换
    handleClick(tab, event) {
      this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
      this.listQuery.id = this.bjDiaRow.id;
      this.zxtSelect = [];
      console.log(tab, event);
      if (tab.name == "liebiao") {
        this.searchFunc();
      } else {
        const now = moment();
        const start = moment(now).subtract(3, "hours");
        const end = moment().format("YYYY-MM-DD HH:mm");
        let startTime = moment(start).format("YYYY-MM-DD HH:mm");
        // let endTime = moment(end).format('YYYY-MM-DD HH:mm');
        this.zxtQuery.dateRange = [startTime, end];
        this.getDxList();
        this.getZxt();
      }
    },
    getDxList() {
      getAction(`/iot/trend/nodeDataDis/${this.bjDiaRow.id}`).then(res1 => {
        let res = res1.data;
        if (res.code == 200) {
          this.options = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleSelectChange() {
      this.zxtSelect = [];
      this.zxtQuery.disRespVos.forEach(element => {
        console.log(element, "element");
        let [type, name] = element.split(",");
        this.zxtSelect.push({ type, name });
      });
    },
    getZxt() {
      this.showChart2 = false;
      if (
        isNull(this.zxtQuery.dateRange) ||
        this.zxtQuery.dateRange.length <= 0
      ) {
        this.$message.warning("请先选择起止时间");
        return false;
      }
      let sendObj = JSON.parse(JSON.stringify(this.zxtQuery));
      // 日期范围
      sendObj.startTime = "";
      sendObj.endTime = "";
      if (
        !isNull(this.zxtQuery.dateRange) &&
        this.zxtQuery.dateRange.length > 0
      ) {
        sendObj.startTime = this.zxtQuery.dateRange[0];
        sendObj.endTime = this.zxtQuery.dateRange[1];
      }
      sendObj.disRespVos = this.zxtSelect;
      sendObj.nodeId = this.bjDiaRow.id;
      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      postAction("/iot/trend/nodeDataTrend", sendObj).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == 200) {
          if (!utils.isNull(res.data) && res.data.list.length > 0) {
            this.showChart2 = true;
            // this.list = res.data;
            setTimeout(() => {
              this.setEchartBar2(res.data.list, res.data.times);
              //   this.createRoom(res.data.list)
            }, 100);
          }
        }
      });
    },
    //创建折线图
    setEchartBar2(arr, dataMap) {
      console.log(arr, "arr");
      if (this.showChart2 == false) {
        // if (!utils.isNull(arr)) {
        //   this.echartRoom2.clear();
        // }
        return;
      }
      // << 本月1号到当天
      let xList = [];
      let xList0 = [];
      // let dateObj = new Date();
      // console.log("dateObj.getDate()", dateObj.getDate());
      // console.log(this.getEveryDayDateByBetweenDate(this.listQuery.dateRange[0],this.listQuery.dateRange[1]),'时间间隔');
      // let dayNum = parseInt(dateObj.getDate());
      // let month = utils.return2Num2(dateObj.getMonth() + 1);
      // let year = dateObj.getFullYear();
      // for (let i = 0; i < dayNum; i++) {
      //   let key = `${year}-${month}-${utils.return2Num2(i + 1)}`;
      //   xList.push(key);
      //   xList0.push(`${year}-${month}-${utils.return2Num2(i + 1)}`);
      // }

      // 拼接数据
      let data = [];
      let listData = [];
      for (let index = 0; index < dataMap.length; index++) {
        let obj = {
          yearMonthDate: dataMap[index],
          count: 0,
          type: ""
        };
        listData.push(obj);
      }
      for (let i = 0; i < arr.length; i++) {
        let itemLine = arr[i];
        let lineObj = {
          name: itemLine.name,
          type: "line",
          stack: "",
          data: []
        };
        let map = itemLine.list;
        // console.log('111111111map', map)
        // console.log('111111111listData', listData)
        for (let key = 0; key < map.length; key++) {
          for (let k = 0; k < listData.length; k++) {
            if (map[key].time == listData[k].yearMonthDate) {
              lineObj.data.push(map[key].value);
              // listData[k].value = map[key].value;
              // listData[k].type = map[key].type;
            }
          }
        }
        data.push(lineObj);
        // let arrData = [];
        // console.log("==listData", listData);
        // for (let o = 0; o < listData.length; o++) {
        //   arrData.push(listData[o].value);
        // }
        // console.log(arrData, "arrData");
        // lineObj.data = arrData;
        // data.push(lineObj);
      }
      console.log(data, "data--------------");
      // xList0.push(map[key].yearMonthDate)
      xList0 = dataMap;
      // 绘制图标
      var myChart = echarts.init(document.getElementById("echart-bar2"));
      var option = {
        title: {
          text: ""
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },

        legend: {
          left: 10
        },
        grid: {
          left: "2%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false, // true-刻度中间 false-刻度线上
          data: xList0
        },
        yAxis: {
          type: "value"
          // name: '单位（吨）',
          // nameTextStyle: {
          //   color: '#aaa',
          //   nameLocation: 'start',
          // },
        },
        series: data
        // series: [[1,2,3],[12,22,32],[13,23,33]]
      };
      myChart.clear();
      myChart.setOption(option);
      myChart.on("click", param => {
        // console.log('param', param)
        // // componentIndex
        // // dataIndex
        // let msg = `${this.echartLineData[param.componentIndex].name}：${
        //   this.echartLineData[param.componentIndex].data[param.dataIndex]
        // }`
        // alert(msg)
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.config-container {
  position: relative;
  margin-bottom: 20px;
  .el-button {
    position: absolute;
    right: 0;
    top: 0;
  }
  .config-item {
    position: relative;
    float: left;
    width: 235px;
    height: 120px;
    padding: 20px;
    border-radius: 8px;
    margin-right: 30px;
    color: #ffffff;
    font-size: 22px;
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }
    .config-tip {
      margin-bottom: 16px;
    }
    img {
      position: absolute;
      right: 20px;
      top: 20px;
    }
    span {
      display: inline-block;
      font-size: 40px;
      max-width: 90px;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: -12px;
    }
  }
  .config-item:nth-child(1) {
    background: linear-gradient(120deg, #7edd77 0%, #1ca690 100%);
  }
  .config-item:nth-child(2) {
    background: linear-gradient(-45deg, #3b5df9 0%, #16c4fd 100%);
  }
  .config-item:nth-child(3) {
    background: linear-gradient(-45deg, #5d5d5d 0%, #bababa 100%);
  }
  .config-item:nth-child(4) {
    background: linear-gradient(-72deg, #ef1606 0%, #fd8b69 100%);
  }
  .config-item:nth-child(5) {
    background: linear-gradient(135deg, #f9a363 0%, #ff6d00 100%);
  }
  .config-item:nth-child(6) {
    background: linear-gradient(135deg, #f664a2 0%, #e14398 99%);
  }
}
.tab-container {
  position: relative;
  height: 44px;
  margin: 0 auto 20px;
  .el-button {
    position: absolute;
    &:first-child {
      left: 0;
      top: 6px;
    }
    &:last-child {
      right: 0;
      top: 6px;
    }
  }
  .tab-menu {
    width: calc(100% - 80px);
    margin: 0 auto;
    border-bottom: 4px solid #dbdbdb;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      height: 0px;
    }
    span.tab-item {
      display: inline-block;
      height: 40px;
      margin-right: 14px;
      font-size: 14px;
      padding: 10px;
      border: 1px solid #e4e4e4;
      border-radius: 8px 8px 0px 0px;
      background: #dbdbdb;
      color: #a5a5a5;
      cursor: pointer;
      &.active {
        background: #035593;
        color: #ffffff;
      }
      &:hover {
        opacity: 0.7;
      }
    }
  }
}

.equip-container {
  position: relative;
  height: calc(100vh - 416px);
  overflow-y: auto;
  .equip-item {
    float: left;
    width: 100%;
    .title {
      font-size: 16px;
      color: #464646;
      margin-bottom: 10px;
    }
  }
  .node-container {
    margin-bottom: 10px;
    .node-item {
      float: left;
      width: 230px;
      height: 140px;
      margin-right: 10px;
      margin-bottom: 10px;
      padding: 10px;
      border: 1px solid #bebebe;
      border-radius: 8px;
      cursor: pointer;
      &:hover {
        opacity: 0.7;
      }
      .node-name {
        width: 100%;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 6px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .node-info {
        overflow: hidden;
        height: calc(100% - 24px);
        line-height: 1.6;
        font-size: 12px;
        color: #333333;
      }
    }
  }
}

.error {
  color: #fc010d;
}
</style>
