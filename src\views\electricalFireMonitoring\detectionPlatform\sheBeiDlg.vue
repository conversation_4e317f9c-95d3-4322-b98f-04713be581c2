<template>
  <!-- 弹窗 新增/编辑 -->
  <el-dialog
    class="mazhenguo"
    title="设备详情"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    width="1200px"
    top="30px"
  >
    <!-- @tab-click="handleClick" -->
    <el-tabs v-model="deviceDetailsQuery.activeName" @tab-click="handleClick">
      <el-tab-pane label="设备详情" name="first">
        <el-descriptions title="" :column="2" border>
          <el-descriptions-item
            label="名称:"
            labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
          >
            <span style="font-weight: bolder;">{{ deviceViewRow.name }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="设备标识:"
            labelStyle="font-weight: bolder;font-size: 14px;text-align: center;width: 130px"
          >
            <span style="font-weight: bolder;">{{ deviceViewRow.devEui }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="绑定设备间:"
            labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
          >
            <span style="font-weight: bolder;">{{
              deviceViewRow.equipRoomName
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="绑定设备:"
            labelStyle="font-weight: bolder;font-size: 14px;text-align: center;width: 130px"
          >
            <span style="font-weight: bolder;">{{
              deviceViewRow.equipName
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            :span="2"
            label="设备档案:"
            labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
          >
            <span style="font-weight: bolder;">{{
              deviceViewRow.nodeArchivesName
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            :span="2"
            label="最大报警间隔:"
            labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
          >
            <span style="font-weight: bolder;">{{
              deviceViewRow.alarmInterval
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            :span="2"
            label="通讯地址:"
            labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
          >
            <span style="font-weight: bolder;">{{ deviceViewRow.addr }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            :span="2"
            label="位置:"
            labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
          >
            <span style="font-weight: bolder;">{{
              deviceViewRow.installAddr
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            :span="2"
            label="是否开启联动:"
            labelStyle="font-weight: bolder;font-size: 14px;text-align: right;width: 130px"
          >
            <span style="font-weight: bolder;">{{
              deviceViewRow.isOpenLink == 1 ? "是" : "否"
            }}</span>
          </el-descriptions-item>
        </el-descriptions>
        <!-- <el-descriptions title="" :column="1" border style="border-top: none !;">
            <el-descriptions-item label="设备档案:" labelStyle="font-weight: bolder;font-size: 14px">
              <span style="font-weight: bolder;">博高可燃气体</span>
            </el-descriptions-item>
          </el-descriptions> -->
      </el-tab-pane>
      <el-tab-pane label="报警配置" name="second">
        <el-table
          ref="tableBar"
          class="m-small-table"
          v-loading="listLoading"
          :data="alarmConfigurationList"
          max-height="500px"
          border
          fit
          highlight-current-row
          style="width: 100%; height: auto"
        >
          <el-table-column
            label="#"
            type="index"
            align="center"
            width="60"
          ></el-table-column>
          <el-table-column label="参数" align="center">
            <template slot-scope="scope">
              <span>{{
                scope.row.dataTypeText + "(" + "单位:" + scope.row.unit + ")"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标准值" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.standardValue }}</span>
            </template>
          </el-table-column>
          <el-table-column label="正常范围" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.startValue + "~" + scope.row.endValue }}</span>
            </template>
          </el-table-column>
          <el-table-column label="忽略报警时间段" align="center">
            <template slot-scope="scope">
              <span>{{
                scope.row.ignoreStartTime != null &&
                scope.row.ignoreStartTime != ""
                  ? scope.row.ignoreStartTime + "~" + scope.row.ignoreEndTime
                  : ""
              }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="忽略报警值开关/开关状态" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.assetCode }}</span>
              </template>
            </el-table-column>
            <el-table-column label="高于报警值开关/开关状态" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.assetCode }}</span>
              </template>
            </el-table-column> -->
          <!-- <el-table-column
              align="center"
              width="90"
              class-name="small-padding fixed-width"
              label="操作"
            >
              <template slot-scope="scope">
                <el-button type="text" size="mini">删除</el-button>
              </template>
            </el-table-column> -->
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="历史记录" name="third">
        <!-- 搜素按钮组 -->
        <div class="filter-container">
          <el-form inline>
            <el-form-item label="">
              <el-date-picker
                @change="searchHistoryFunc"
                v-model="historyQuery.dateRange"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :append-to-body="false" 
              >
              </el-date-picker>
            </el-form-item>
            <el-button
              icon="el-icon-search"
              type="success"
              size="mini"
              @click="searchHistoryFunc"
              >搜索</el-button
            >
          </el-form>
        </div>
        <el-table
          ref="tableBar"
          class="m-small-table"
          v-loading="listLoading"
          :data="historyList"
          max-height="500px"
          border
          fit
          highlight-current-row
          style="width: 100%; height: auto"
        >
          <el-table-column
            label="#"
            type="index"
            align="center"
            width="60"
          ></el-table-column>
          <el-table-column
            v-for="(item, index) of fieldList"
            :key="index"
            :label="item.label"
            width="200"
          >
            <template slot-scope="scope">
              <span>{{ scope.row[item.key] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="page-container">
          <pagination
            :total="historyTotal"
            :page.sync="historyQuery.page"
            :limit.sync="historyQuery.limit"
            @pagination="getHistory"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="数据趋势" name="fourth">
          <el-date-picker
          class="fl ml10"
          style="width: 350px"
          @change="getZxt"
          v-model="zxtQuery.dateRange"
          type="datetimerange"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          size="mini"
          :append-to-body="false" 
        >
        </el-date-picker>
        <el-select
        @change="handleSelectChange"
        class="fl ml10"
        style="width: 200px"
    v-model="zxtQuery.disRespVos"
    multiple
    collapse-tags
    :popper-append-to-body="false" 
    placeholder="请选择">
    <el-option
      v-for="item in options"
      :key="item.type"
      :label="item.name"
      :value="`${item.type},${item.name}`">
    </el-option>
  </el-select>
        <el-button icon="el-icon-search" type="success" size="mini" class="search-right-btn fl" @click="getZxt">搜索</el-button>
  <div class="clear"></div>

  <div style="height: 500px;margin-top: 16px">
    <div
          v-if="showChart2"
          id="echart-bar2"
          style="height: 500px"
        ></div>
  </div>
        
        </el-tab-pane>
    </el-tabs>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
   
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters } from "vuex";

import { uploadImg, uploadImg2 } from "@/utils/uploadImg";
// 接口
import * as utils from "@/utils";
import { postAction, getAction } from "@/api";
import Pagination from "@/components/Pagination"; // 分页
import moment, { localeData } from 'moment'//导入文件
import * as echarts from "echarts";
import {
  arrId2Name, // 根据id 获取name
  getDataDictOther, // 数据字典
  isNull
} from "@/utils";
let dlgDataEmpty = {
  id: "",
  handlerId: "",
  handler: "",
  handlerRemark: "",
  confirmationTime: ""
};
//折线图
let zxtQueryEmpty={
  disRespVos:[],
  nodeId:'',
  startTime:'',
  endTime:'',
  dateRange: [],
}
export default {
  components: {
    Pagination
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {
      type: Object,
      default: {}
    },

    dlgData0: {}
  },
  watch: {
    dlgState(val) {
      if (val) {
        utils.getDataDictOther(this, "gaojingchulijieguo", "gjSelect"); // 报警处理结果

        setTimeout(() => {
          this.deviceDetailsQuery.activeName= "first"
          this.deviceViewRow = JSON.parse(JSON.stringify(this.dlgQuery));
          this.getInfo2(this.deviceViewRow.id);
          this.getHistory();
        
        }, 100);
      } else {
        this.closeDlg();
      }
    }
  },

  data() {
    return {
      historyQuery: {
        page: 1,
        limit: 20,
        dateRange: [],
        beginTime: "",
        endTime: "",
        id: ""
      }, //历史记录
      deviceViewRow: "",

      //设备详情
      equipmentDrawing: "",
      equipmentDrawingList: [],
      deviceDetailsDlg: false,
      deviceDetailsQuery: {
        id: "",
        activeName: "first"
      },
      listLoading: false,
      historyTotal: 0,
      historyList: [],
      fieldList: [], //表头
      alarmConfigurationList: [], //报警配置
      //////

      // 弹窗
      dlgState: false,
      dlgLoading: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgRules: {
        handlerRemark: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        handler: [{ required: true, message: "必填字段", trigger: "change" }],
        confirmationTime: [
          { required: true, message: "必填字段", trigger: "change" }
        ]
        // handlerResultStr: [
        //   { required: true, message: "必填字段", trigger: "change" }
        // ]
      },
      dlgSubLoading: false, // 提交loading

      // 下拉框
      aaaaSelect: [{ id: 1, name: "下拉1" }], //

      // 附件上传
      fileList: [],
      uploadCount0: 0, // 上传总数
      uploadCount: 0, // 上传计数

         //趋势
      zxtQuery:JSON.parse(JSON.stringify(zxtQueryEmpty)),
      options:[],
      bjDiaRow:{},
      showChart2: false,
       echartRoom2: null,
       zxtSelect:[]
    };
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
  },
  methods: {
    //报警详情
    getInfo2(id) {
      this.alarmConfigurationList = [];
      getAction(`/iot/findNodeConfigs/${id}`).then(res1 => {
        let res = res1.data;
        console.log(res);
        if (res.code == 200) {
          this.deviceDetailsDlg = true;
          this.alarmConfigurationList = res.list;
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    //获取历史记录
    getHistory() {
      this.fieldList = [];
      this.historyList = [];
      let sendObj = JSON.parse(JSON.stringify(this.historyQuery));
      // 日期范围
      sendObj.beginTime = "";
      sendObj.endTime = "";
      sendObj.id = this.deviceViewRow.id;
      if (!utils.isNull(sendObj.dateRange) && sendObj.dateRange.length > 0) {
        sendObj.beginTime = sendObj.dateRange[0];
        sendObj.endTime = sendObj.dateRange[1];
      }
      this.listLoading = true;
      postAction("/iot/protocolLoran", sendObj).then(res1 => {
        this.listLoading = false;
        let res = res1.data;
        if (res.code == 200) {
          for (let key in res.data.field) {
            let label = res.data.field[key];
            this.fieldList.push({
              key,
              label
            });
          }
          this.historyList = res.list;
          this.historyTotal = res.data.total;
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    searchHistoryFunc() {
      this.historyQuery.page = 1;
      this.getHistory();
    },

    /////////////////

    // 弹窗提交 ------
    dlgSubFunc() {
      this.$refs["dlgDataForm"].validate(valid => {
        if (valid) {
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));
          sendObj.id = this.giveAlarmRow.id;
          console.log(sendObj, "sendObj");
          // return false
          postAction("/iot/aas/updateStatus/v2", sendObj).then(res => {
            console.log(res);
            if (res.data.code == 200) {
              this.$message.success(res.data.msg);
              this.dlgState = false;
              this.$emit("getList");
              this.closeDlg();
              this.$emit("upList1");
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },

    closeDlg() {
      this.dlgLoading = false;
      this.dlgSubLoading = false;
      this.dlgState = false;

    },

    // << 5张图片上传
    uploadQj1(file) {
      if (file.size > 2 * 1024 * 1024) {
        this.$message({
          type: "warning",
          message: "上传图片大小不能超过2M"
        });
        return false;
      }
      uploadImg(file, "ERP_web/test/test_").then(res => {
        this.dlgData.subImagesArr.push(res);
      });
    },
    delUploadImgByArr(index) {
      this.$confirm("是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning" // // success, warning, info, error
      }).then(() => {
        this.$message({
          type: "success", // success, warning, info, error
          message: "删除成功"
        });
        let dlgData = this.dlgData;
        dlgData.subImagesArr.splice(index, 1);
        this.dlgData = JSON.parse(JSON.stringify(dlgData));
      });
    },
     handleClick(tab, event) {
      console.log(tab, event);
      this.zxtQuery=JSON.parse(JSON.stringify(zxtQueryEmpty))
      this.zxtSelect=[]
      if (tab.name=='fourth') {
         const now = moment();
        const start =moment(now).subtract(3, 'hours');
        const end =  moment().format("YYYY-MM-DD HH:mm");
        let startTime = moment(start).format('YYYY-MM-DD HH:mm');
        // let endTime = moment(end).format('YYYY-MM-DD HH:mm');
        this.zxtQuery.dateRange=[startTime,end]
        this.getDxList()
        this.getZxt()
      }
      // this.getDeviceDetails();
    },
     getDxList(){
      getAction(`/iot/trend/nodeDataDis/${this.deviceViewRow.id}`).then((res1)=>{
        let res=res1.data
          if (res.code==200) {
            this.options=res.data
          }else{
            this.$message.error(res.msg)
          }
      })
    },
     handleSelectChange(){
      this.zxtSelect=[]
      this.zxtQuery.disRespVos.forEach(element => {
        console.log(element,"element");
          let [type, name] = element.split(",");
        this.zxtSelect.push({ type, name });
  });
    },
     getZxt(){
      this.showChart2 = false;
      if (isNull(this.zxtQuery.dateRange)||this.zxtQuery.dateRange.length<=0) {
        this.$message.warning('请先选择起止时间')
        return false;
      }
      let sendObj=JSON.parse(JSON.stringify(this.zxtQuery))
      // 日期范围
      sendObj.startTime = "";
      sendObj.endTime = "";
      if (
        !isNull(this.zxtQuery.dateRange) &&
        this.zxtQuery.dateRange.length > 0
      ) {
        sendObj.startTime = this.zxtQuery.dateRange[0];
        sendObj.endTime = this.zxtQuery.dateRange[1];
      }
      sendObj.disRespVos=this.zxtSelect
      sendObj.nodeId=this.deviceViewRow.id
      let loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      postAction('/iot/trend/nodeDataTrend',sendObj).then((res1)=>{
        loading.close()
        let res=res1.data
        if(res.code==200){
           if (!utils.isNull(res.data) && res.data.list.length > 0) {
            this.showChart2 = true;
            // this.list = res.data;
            setTimeout(() => {
              this.setEchartBar2(res.data.list,res.data.times);
              //   this.createRoom(res.data.list)
            }, 100);
          }
        }
      })
    },
     //创建折线图
      setEchartBar2(arr, dataMap) {
      console.log(arr, "arr");
      if (this.showChart2 == false) {
        // if (!utils.isNull(arr)) {
        //   this.echartRoom2.clear();
        // }
        return;
      }
      // << 本月1号到当天
      let xList = [];
      let xList0 = [];
      // let dateObj = new Date();
      // console.log("dateObj.getDate()", dateObj.getDate());
      // console.log(this.getEveryDayDateByBetweenDate(this.listQuery.dateRange[0],this.listQuery.dateRange[1]),'时间间隔');
      // let dayNum = parseInt(dateObj.getDate());
      // let month = utils.return2Num2(dateObj.getMonth() + 1);
      // let year = dateObj.getFullYear();
      // for (let i = 0; i < dayNum; i++) {
      //   let key = `${year}-${month}-${utils.return2Num2(i + 1)}`;
      //   xList.push(key);
      //   xList0.push(`${year}-${month}-${utils.return2Num2(i + 1)}`);
      // }

      // 拼接数据
      let data = [];
      let listData = [];
      for (let index = 0; index < dataMap.length; index++) {
        let obj = {
          yearMonthDate: dataMap[index],
          count: 0,
          type: ""
        };
        listData.push(obj);
      }
      for (let i = 0; i < arr.length; i++) {
        let itemLine = arr[i];
        let lineObj = {
          name: itemLine.name,
          type: "line",
          stack: "",
          data: []
        };
        let map = itemLine.list;
        // console.log('111111111map', map)
        // console.log('111111111listData', listData)
        for (let key = 0; key < map.length; key++) {
          for (let k = 0; k < listData.length; k++) {
            if (map[key].time == listData[k].yearMonthDate) {
              lineObj.data.push(map[key].value)
              // listData[k].value = map[key].value;
              // listData[k].type = map[key].type;
            }
          }
        }
        data.push(lineObj)
        // let arrData = [];
        // console.log("==listData", listData);
        // for (let o = 0; o < listData.length; o++) {
        //   arrData.push(listData[o].value);
        // }
        // console.log(arrData, "arrData");
        // lineObj.data = arrData;
        // data.push(lineObj);
      }
      console.log(data,"data--------------");
      // xList0.push(map[key].yearMonthDate)
      xList0 = dataMap;
      // 绘制图标
      var myChart = echarts.init(document.getElementById("echart-bar2"));
      var option = {
        title: {
          text: ""
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },

        legend: {
          left: 10
        },
        grid: {
          left: "2%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false, // true-刻度中间 false-刻度线上
          data: xList0
        },
        yAxis: {
          type: "value"
          // name: '单位（吨）',
          // nameTextStyle: {
          //   color: '#aaa',
          //   nameLocation: 'start',
          // },
        },
        series: data
      };
      myChart.clear();
      myChart.setOption(option);
      myChart.on("click", param => {
        // console.log('param', param)
        // // componentIndex
        // // dataIndex
        // let msg = `${this.echartLineData[param.componentIndex].name}：${
        //   this.echartLineData[param.componentIndex].data[param.dataIndex]
        // }`
        // alert(msg)
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.table-container {
  margin-top: 10px;
}

.card2_1 {
  /deep/.el-card__body {
    height: 270px;
    overflow-y: auto;
  }
}

.card2_2 {
  /deep/.el-card__body {
    height: 580px;
    overflow-y: auto;
  }
}
.drop-right-img {
  height: 202px;
  -webkit-user-drag: none;
}
.oneBtn {
  position: absolute;
  right: 20px;
  top: 22px;
}

.giveAlarmDlgCol {
  border: 1px solid #eff6ff;
  height: 260px;
  padding: 15px;
  background-color: #f8faff;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.towBtn {
  position: absolute;
  right: 20px;
  top: 0;
}

.alarmMessageListBox {
  display: flex;
  justify-content: space-between;
  font-weight: bolder;
  background-color: #fff7f6;
  margin-bottom: 10px;
  padding: 6px;
}

.deviceViewBox {
  // font-weight: bolder;
  // margin-bottom: 10px;
  padding: 10px;
  border-bottom: 1px solid #f1efef;
  // position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card1 {
  /deep/.el-card__body {
    height: calc(100% - 53px);
    overflow-y: auto;
  }
}

.echart {
  height: 200px;
}

////////////////
.list-yuan-btn {
  height: 28px;
  line-height: 28px;

  padding: 0 10px;
  border-radius: 14px;
  margin-right: 10px;
}

.bg-red {
  background: #ff0900 !important;
  color: white !important;
  box-shadow: 0 4px 10px #ffc5c3 !important;
}

.bg-yellow {
  background: #fef6e2;
  color: #e5ab35;
}

.bg-blue {
  background: #ecf5ff;
  color: #5eadfe;
}

.bg-green {
  background: #e2f9ee;
  color: #69dfb0;
}

//
.item-bg-red {
  background: linear-gradient(to right, #fff2f1, #fff, #fff, #fff, #fff);
}

//
.ml16 {
  margin-right: 16px;
}

// #dqhz .el-card__body {
//   padding: 0 !important;
// }
/deep/.el-card__body {
  padding: 0;
  // padding-left: 20px;
}
</style>
