<template>
  <div class="">
    <!-- 弹窗 图片放大 -->
    <el-dialog :close-on-click-modal='false' 
      title="查看图片" 
      
      :visible.sync="imgEnlargeState" 
      fullscreen
      append-to-body>
      <div class='hide-box'>
        <img ref='hideImg' :src="imgEnlargeUrl" alt="">
      </div>
      
      <div class="imgEnlarge-box" v-loading='loading' style="min-height: 400px; background: rgba(0,0,0,.5);">
        <img v-show='mImgUrl' :src="imgEnlargeUrl" alt="">
      </div>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">关闭</el-button>
      </div> -->
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { setTimeout } from 'timers';
export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      imgScale: '',
      boxW: '',
      boxH: '',
      boxScale: '',  // 宽/高
      loading: true,
      mImgUrl: ''
    }
  },
  computed: {
    ...mapGetters([
      // 'imgEnlargeUrl',
      'imgEnlargeIsProcess'
    ]),
    imgEnlargeUrl: {
      get: function() {
        return this.$store.getters.imgEnlargeUrl
      },
      set: function(newVal) {
        this.$store.commit('SET_ENLARGEURL', newVal)
      }
    },
    imgEnlargeState: {
      get: function() {
        return this.$store.getters.imgEnlargeState
      },
      set: function(newVal) {
        this.$store.commit('SET_ENLARGESTATE', newVal)
      }
    }
  },
  watch: {
    imgEnlargeUrl(val) {
      console.log('dddd', val)
      if (val === '') {
        return false
      }
      this.loading = true
      setTimeout(() => {
        // alert(this.imgEnlargeIsProcess)
        console.log('imgEnlargeIsProcess', this.imgEnlargeIsProcess)
        if (this.imgEnlargeIsProcess) {
          setTimeout(() => {
            this.mImgUrl = true
            // alert('3000')
            this.formResize()

            let imgW = this.$refs.hideImg.width
            let imgH = this.$refs.hideImg.height
            this.imgScale = imgW/imgH

            if (this.imgScale > this.boxScale) {
              // alert(1)
              $('.imgEnlarge-box img').width(this.boxW)
              $('.imgEnlarge-box img').height('auto')
            } else {
              // alert(2)
              $('.imgEnlarge-box img').width('auto')
              $('.imgEnlarge-box img').height(this.boxH)
            }
            
            // loading.close()
            this.loading = false
          }, 2000)
      
        } else {
          this.formResize()

          let imgW = this.$refs.hideImg.width
          let imgH = this.$refs.hideImg.height
          this.imgScale = imgW/imgH

          if (this.imgScale > this.boxScale) {
            // alert(1)
            $('.imgEnlarge-box img').width(this.boxW)
            $('.imgEnlarge-box img').height('auto')
          } else {
            // alert(2)
            $('.imgEnlarge-box img').width('auto')
            $('.imgEnlarge-box img').height(this.boxH)
          }
          
          // loading.close()
          this.loading = false
          this.mImgUrl = true
        }

      }, 50)
    }
  },
  created() {
    let boxW = $(window).width() - 40
    let boxH = $(window).height() - 100
    $('.imgEnlarge-box').width(boxW)
    this.boxScale = boxW / boxH 
    
  },
  mounted() {
    // alert('图片加载完成')
    // $('.imgEnlarge-box img').resize(() => {
    //   // alert('图片加载完成')
    //   this.formResize() 
    // })
    
  },
  methods: {
    // 窗口改变
    formResize() {
      let boxW = $(window).width() - 40
      let boxH = $(window).height() - 100
      $('.imgEnlarge-box').width(boxW)
      this.boxScale = boxW / boxH 
      if (this.imgScale > this.boxScale) {
        // console.log(boxW)
        $('.imgEnlarge-box img').width(boxW)
        $('.imgEnlarge-box img').height('auto')
      } else {
        $('.imgEnlarge-box img').width('auto')
        $('.imgEnlarge-box img').height(this.boxH)
      }
      this.boxW = boxW
      this.boxH = boxH
      
    },
    // 关闭弹窗 
    closeDialog() {
      this.imgEnlargeState = false
      this.mImgUrl = false
      this.imgEnlargeUrl = ''
      
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.imgEnlarge-box {
  margin: 0 auto;
  width: 100%;
  position: relative;
  margin-top: -10px;
  // height: 400px;
  img {
    width: 100%;
    display: block;
    margin: 0 auto;
  }
}

.hide-box {
  position: absolute;
  left: -9999px;
  top: -9999px;
}
</style>