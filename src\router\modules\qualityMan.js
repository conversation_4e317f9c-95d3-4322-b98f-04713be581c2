/** 品质系统 **/
import Layout from "@/views/layout/Layout";
const qualityManRouter = {
  path: "/qualityMan",
  component: Layout,
  name: "qualityMan",
  meta: {
    title: "巡检管理",
    icon: "clipboard",
    roles: ["pinzhixitong_old"]
  },
  children: [
    {
      path: "workInstruction",
      component: () => import("@/views/qualityMan/workInstruction/index"),
      meta: {
        title: "作业指导书",
        roles: ["pinzhixitong"]
      },
      children: [
        {
          path: "workItemSetup",
          component: () =>
            import("@/views/qualityMan/workInstruction/workItemSetup"),
          name: "工作项设置",
          meta: {
            title: "工作项设置",
            roles: ["pinzhixitong"]
          },
          children: []
        },
        {
          path: "workScheduleSetup",
          component: () =>
            import("@/views/qualityMan/workInstruction/workScheduleSetup"),
          name: "作业规程设置",
          meta: {
            title: "作业规程设置",
            roles: ["pinzhixitong"]
          },
          children: []
        },
        {
          path: "workFlowTmpl",
          component: () =>
            import("@/views/qualityMan/workInstruction/workFlowTmpl"),
          name: "作业流程模板",
          meta: {
            title: "作业流程模板",
            roles: ["pinzhixitong"]
          },
          children: []
        }
      ]
    },
    {
      path: "projectWorkSetup",
      component: () => import("@/views/qualityMan/projectWorkSetup/index"),
      meta: {
        title: "项目作业设置",
        roles: ["pinzhixitong"]
      },
      children: [
        {
          path: "workArea",
          component: () =>
            import("@/views/qualityMan/projectWorkSetup/workArea"),
          name: "作业区域",
          meta: {
            title: "作业区域",
            roles: ["pinzhixitong"]
          },
          children: []
        },
        {
          path: "workFlow",
          component: () =>
            import("@/views/qualityMan/projectWorkSetup/workFlow"),
          name: "作业流程",
          meta: {
            title: "作业流程",
            roles: ["pinzhixitong"]
          },
          children: []
        }
      ]
    },
    {
      path: "projectPerform",
      component: () => import("@/views/qualityMan/projectPerform/index"),
      meta: {
        title: "项目执行",
        roles: ["pinzhixitong"]
      },
      children: [
        {
          path: "projectWorkOutline",
          component: () =>
            import("@/views/qualityMan/projectPerform/projectWorkOutline"),
          name: "项目作业大纲",
          meta: {
            title: "项目作业大纲",
            roles: ["pinzhixitong"]
          },
          children: []
        },
        {
          path: "performMan",
          component: () =>
            import("@/views/qualityMan/projectPerform/performMan"),
          name: "执行管理",
          meta: {
            title: "执行管理",
            roles: ["pinzhixitong"]
          },
          children: []
        },
        {
          path: "taskMan",
          component: () => import("@/views/qualityMan/projectPerform/taskMan"),
          name: "任务管理",
          meta: {
            title: "任务管理",
            roles: ["pinzhixitong"]
          },
          children: []
        }
      ],
    },
     //品质7月改 旧
     {
      path: "taskManNew",
      component: () =>
        import("@/views/qualitySystem/projectPerform/taskManNew"),
      name: "作业任务管理",
      meta: {
        title: "作业任务管理",
        roles: ["renwuguanli_web_old"],
        keepAlive: true
      },
      children: []
    },
     //品质7月改 旧
     {
      path: "statisticalAnalysisNew",
      // component: () => import("@/views/qualitySystem/statisticalAnalysisNew"),
      component: () => import("@/views/qualitySystem/statisticalAnalysis"),  // 品质系统、品质管理 使用同一个页面
      name: "统计分析",
      meta: {
        title: "统计分析",
        roles: ["pinzhifenxi_old"]
      }
    }
  ]
};

export default qualityManRouter;
