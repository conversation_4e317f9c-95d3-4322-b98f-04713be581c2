
const Facedialog = {
  state: {
    faceCanRefresh: false,  // 是否刷新页面
    faceUserId: '',
    faceChouchaState: '',
    faceType: '',  // 弹出窗状态 0 人脸录入审核，1 监管， 2 考勤抽查
    faceIsDesc: false,  // 是否是详情
    faceState: false,
    faceId: '',  // id
    facePunshId: '',  // 取消监管的 punch_id
    faceLabel: '', // 姓名
    facePLabel: '', // 岗位
    faceWorkNum: '', // 工号
    faceBLabel: '', // 部门
    faceInputCauseType: '', // 录入原因
    faceIdNumber: '',  // 身份证号
    faceImg1: '',  // 打卡录入信息
    faceImg2: '', // 身份证头像面照片

    faceIsZdjg: '', // 录入原因
  },

  mutations: {
    SET_FACEISDESC: (state, val) => {
      state.faceIsDesc = val
    },
    
    SET_FACEUSERID: (state, faceUserId) => {
      state.faceUserId = faceUserId
    },
    SET_FACECANREFRESH: (state, val) => {
      state.faceCanRefresh = val
    },
    SET_FACECHOUCHASTATE: (state, faceChouchaState) => {
      state.faceChouchaState = faceChouchaState
    },
    SET_FACETYPE: (state, faceType) => {
      state.faceType = faceType
    },
    SET_FACESTATE: (state, faceState) => {
      state.faceState = faceState
    },
    SET_FACEID: (state, faceId) => {
      state.faceId = faceId
    },
    SET_FACEPUNSHID: (state, val) => {
      state.facePunshId = val
    },
    SET_FACELABEL: (state, faceLabel) => {
      state.faceLabel = faceLabel
    },
    SET_FACEPLABEL: (state, facePLabel) => {
      state.facePLabel = facePLabel
    },
    SET_FACEWORKNUM: (state, faceWorkNum) => {
      state.faceWorkNum = faceWorkNum
    },
    SET_FACEBLABEL: (state, faceBLabel) => {
      state.faceBLabel = faceBLabel
    },
    SET_FACEINPUTCAUSETYPE: (state, faceInputCauseType) => {
      state.faceInputCauseType = faceInputCauseType
    },
    SET_FACEIDNUMBER: (state, faceIdNumber) => {
      state.faceIdNumber = faceIdNumber
    },
    SET_FACEIMG1: (state, faceImg1) => {
      state.faceImg1 = faceImg1
      console.log(state.faceImg1);
    },
    SET_FACEIMG2: (state, faceImg2) => {
      state.faceImg2 = faceImg2
    },

    SET_FACEISZDJG: (state, val) => {
      state.faceIsZdjg = val
    }
  },

  actions: {
    
  }
}

export default Facedialog
