<template>
  <el-dialog :close-on-click-modal='false' :title="'选择项目'" :visible.sync="dlgShow" append-to-body>
    <el-input placeholder="输入项目名称进行过滤" v-model="filterProject">
    </el-input>
    <el-tree ref="projectTree" highlight-current node-key="id" :data="list" @node-click="treeNodeClick" :props="defaultProps" :default-expanded-keys="expandedKeys" :filter-node-method="filterNode" :expand-on-click-node="true">
    </el-tree>

    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-span" v-if="selectProjectName">当前选中：{{selectProjectName}}</span>
      <el-button icon="el-icon-back" @click="closeDlg">
        取 消
      </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'

import { findProjectTree } from '@/api/propertyMan/projectMaintenance.js'

export default {
  components: {
  },
  data () {
    return {
      filterProject: "",

      list: [],

      selectProjectId: '',

      selectProjectName: '',

      defaultProps: {
        children: 'children',
        label: 'projectName'
      },

      expandedKeys: []

    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.propertyMan.projectDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('propertyMan/projectDlg/SET_DLGSHOW', val)
      }
    },

    projectId: {
      get: function () {
        return this.$store.state.propertyMan.projectDlg.projectId
      },
      set: function (val) {
        this.$store.commit('propertyMan/projectDlg/SET_PROJECTID', val)
      }
    },

    projectName: {
      get: function () {
        return this.$store.state.propertyMan.projectDlg.projectName
      },
      set: function (val) {
        this.$store.commit('propertyMan/projectDlg/SET_PROJECTNAME', val)
      }
    },
  },

  watch: {
    filterProject (val) {
      this.$refs.projectTree.filter(val);
    },

    dlgShow (val) {
      if (val) {
        if (utils.isNull(this.projectId)) {
          this.selectProjectId = ""
          this.selectProjectName = ""
        }
        this.getList()
      }
    },

    projectId (val) {
      this.selectProjectId = val
    },

    projectName (val) {
      this.selectProjectName = val
    }
  },

  methods: {
    filterNode (value, data) {
      if (!value) return true;
      return data.projectName.indexOf(value) !== -1;
    },

    treeNodeClick (data) {
      if (data.state == 1) {
        return
      }
      this.selectProjectId = data.id
      this.selectProjectName = data.projectName
    },

    getList () {
      this.list = []
      findProjectTree().then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let list = res.data.list
          this.expandedKeys = [list[0].id]
          this.list = list
          this.$nextTick(() => {
            this.$refs.projectTree.setCurrentKey(this.selectProjectId)
          })
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg () {
      if (utils.isNull(this.selectProjectId)) {
        this.$message.error("请选择一个项目")
        return
      }

      this.projectId = this.selectProjectId
      this.projectName = this.selectProjectName
      this.$store.commit('propertyMan/projectDlg/SET_PROJECTID', this.projectId)
      this.$store.commit('propertyMan/projectDlg/SET_PROJECTNAME', this.projectName)
      this.closeDlg()
    },

    closeDlg () {
      this.$store.commit('propertyMan/projectDlg/SET_DLGSHOW', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}
</style>