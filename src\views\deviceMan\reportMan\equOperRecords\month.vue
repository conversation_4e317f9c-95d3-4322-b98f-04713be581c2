<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <el-date-picker
              v-model="searchForm.yearMonth"
              style="width: 180px"
              class="fl"
              type="month"
              value-format="yyyy-MM"
              placeholder="选择月"
            >
            </el-date-picker>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc(100vh - 300px)"
      ref="tableBar"
      class="m-small-table"
      v-loading="listLoading"
      :key="tableKey"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="projectName"
        label="项目名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="modelName"
        label="模板名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="frequency"
        label="巡检频率"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <!-- 动态渲染 monthCounts 列 -->
      <el-table-column
        v-for="(day, index) in Object.keys(
          (tableData[0] && tableData[0].monthCounts) || {}
        )"
        :key="index"
        :label="day"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.monthCounts[day] }}
        </template>
      </el-table-column>
      <el-table-column
        prop="count"
        label="合计"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getList()"
    />
    <div class="clear"></div>
  </div>
</template>

<script>
import { getDataDict } from "@/utils";
import { postAction, getAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination"; // 分页
export default {
  components: {
    Pagination,
  },
  data() {
    return {
      tableKey: 0,
      searchForm: {
        yearMonth: null,
        pageNo: 1,
        pageSize: 20,
        projectId: JSON.parse(window.localStorage.userInfo).projectId,
      },
      tableData: [],
      total: 0,
      listLoading: false,
    };
  },
  created() {},
  mounted() {},
  methods: {
    async searchFunc() {
      this.searchForm.pageNo = 1;
      await this.getList();
    },
    async getList() {
      const { yearMonth, pageNo, pageSize, projectId } = this.searchForm;

      if (!yearMonth) {
        return this.$message.warning("请选择月份");
      }

      let year, month;
      [year, month] = yearMonth.split("-");

      this.listLoading = true;
      try {
        const params = new URLSearchParams({
          year,
          month,
          pageNo,
          pageSize,
          projectId,
        });
        const res = await getAction(
          `/sa/green/equ/operation-record/month-statistics?${params.toString()}`
        );
        console.log(res.data, "res.data");

        const { code, data } = res.data;
        if (code === "200") {
          this.tableData = data.list || [];
          this.total = data.total || 0;
          this.tableKey++;
        } else {
          this.$message.error(res.data.msg);
        }
      } catch (error) {
        this.$message.error(error.message || "请求失败");
      } finally {
        this.listLoading = false;
        console.log("finally", this.tableData);
      }
    },
  },
};
</script>

<style></style>
