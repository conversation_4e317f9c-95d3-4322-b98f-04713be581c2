<template>
  <div>
    <!-- 新增/编辑/详情对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-form
        ref="dataForm"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        :disabled="dlgType == 'info'"
      >
        <!-- 基础表单 -->
        <BasicForm
          ref="basicForm"
          :form-data="formData"
          :dlg-type="dlgType"
          @show-post-dialog="handleShowPostDialog"
        />

        <!-- 文件上传 -->
        <FileUpload
          ref="fileUpload"
          :form-data="formData"
          :dlg-type="dlgType"
          :hide-files="useGetItem && dlgType === 'info'"
        />
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{
          dlgType === "info" ? "关闭" : "取消"
        }}</el-button>
        <el-button
          v-if="dlgType !== 'info'"
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ isEdit ? "更新" : "创建" }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 岗位选择对话框 -->
    <diaPostList
      :diaPostState2="diaPostState"
      @closeDlg="closeSelectPostDlg"
      :hiddenTime="false"
      :fromBaoshi="true"
      :showPostCode="true"
    />

    <!-- 部门树选择组件 -->
    <Bmtree />
  </div>
</template>

<script>
import BasicForm from "./BasicForm.vue";
import FileUpload from "./FileUpload.vue";
import diaPostList from "@/components/Dialog/diaPostList"; // 选人选岗 通用弹窗
import Bmtree from "@/components/Dialog/Bmtree"; // 部门树选择组件
import {
  createTechnicalData,
  updateTechnicalData,
  getAllItem,
  getItem,
} from "@/api/technicalData";
import { postAction } from "@/api";

export default {
  name: "addEdit",
  components: {
    BasicForm,
    FileUpload,
    diaPostList,
    Bmtree,
  },
  props: {
    dlgType: {
      type: String,
      default: "",
    },
    useGetItem: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dialogVisible: false,
      isEdit: false,
      submitLoading: false,

      // 岗位选择对话框状态
      diaPostState: false,

      // 表单数据 - 按照API接口格式定义
      formData: {
        id: null, // 编辑时的ID
        annexUrl: "", // 附件路径
        branchId: null, // 部门ID
        branchName: "", // 部门名称
        branchOperationId: null, // 事业部ID
        branchOperationName: "", // 事业部名称
        branchProjectId: null, // 项目部ID
        branchProjectName: "", // 项目部名称
        fileUrl: "", // 原始文件路径
        imgUrl: "", // 图片路径
        name: "", // 技术资料名称
        postId: null, // 岗位ID
        postName: "", // 岗位名称
        premises: "", // 楼宇
        projectId: null, // 项目ID
        projectName: "", // 项目名称
        remark: "", // 备注
        storey: "", // 楼层
        type: null, // 图纸类型
        typeStr: "", // 图纸类型名称
        userPostId: null, // 用户岗位ID

        // 前端文件管理字段（用于组件交互，提交时转换为URL）
        imgUrl: [], // 共享图片文件列表
        fileUrl: [], // 原始文件列表
        annexUrl: [], // 附件文件列表
      },

      // 岗位选择数据（用于回显）
      selectDlgData: [],

      // 表单验证规则
      formRules: {
        projectId: [
          { required: true, message: "请选择项目", trigger: "change" },
        ],
        branchName: [
          { required: true, message: "请选择部门", trigger: "change" },
        ],
        // businessUnit: [
        //   { required: true, message: '请选择事业部', trigger: 'change' }
        // ],
        // projectDept: [
        //   { required: true, message: '请选择项目部', trigger: 'change' }
        // ],
        name: [
          { required: true, message: "请输入技术资料名称", trigger: "blur" },
          {
            min: 2,
            max: 100,
            message: "长度在 2 到 100 个字符",
            trigger: "blur",
          },
        ],
        type: [
          { required: true, message: "请选择图纸类型", trigger: "change" },
        ],
        premises: [{ required: true, message: "请输入楼宇", trigger: "blur" }],
        storey: [{ required: true, message: "请输入楼层", trigger: "blur" }],
        postName: [
          { required: true, message: "请选择岗位", trigger: "change" },
        ],
        imgUrl: [
          { required: true, message: "请上传共享图片", trigger: "change" },
        ],
        fileUrl: [
          { required: true, message: "请上传原始文件", trigger: "change" },
        ],
        remark: [{ max: 500, message: "备注不能超过500字", trigger: "blur" }],
      },
    };
  },

  computed: {
    dialogTitle() {
      if (this.dlgType === "info") return "技术资料详情";
      return this.isEdit ? "编辑技术资料" : "新增技术资料";
    },

    // 岗位选择结果
    diaPostGet: {
      get: () => this.$store.getters.diaPostGet,
      // 移除未使用的参数 val
      set: () => this.$store.commit("SET_DIAPOST_GET", ""),
    },

    // 部门树选择结果
    bmTreeBranchId() {
      return this.$store.getters.bmTreeBranchId;
    },
    bmTreeBranchName() {
      return this.$store.getters.bmTreeBranchName;
    },
  },

  watch: {
    // 监听岗位选择结果
    diaPostGet(val) {
      if (!val) {
        return false;
      }
      let postList = JSON.parse(val);
      console.log("岗位选择返回:", postList);

      // 更新岗位选择数据用于回显
      this.selectDlgData = JSON.parse(JSON.stringify(postList));

      let postIds = [];
      let postNames = [];
      postList.map((item) => {
        postIds.push(item.postId);
        postNames.push(
          item.label +
            "(" +
            item.postCode +
            (item.userName ? item.userName : "空岗") +
            ")"
        );
      });

      this.formData.postId = postIds.toString();
      this.formData.postName = postNames.toString();
    },

    // 监听部门选择
    bmTreeBranchId(val) {
      if (val === "empty") {
        return false;
      }
      this.formData.branchId = val;
    },
    bmTreeBranchName(val) {
      if (val === "empty") {
        return false;
      }
      this.formData.branchName = val;
    },
  },

  methods: {
    /**
     * 显示岗位选择对话框
     */
    handleShowPostDialog() {
      this.diaPostState = true;
    },

    /**
     * 关闭岗位选择对话框
     */
    closeSelectPostDlg() {
      this.diaPostState = false;
    },

    /**
     * 显示对话框
     */
    async showDlg(data = null) {
      this.dialogVisible = true;

      this.isEdit = this.dlgType === "edit";

      if (data && (this.isEdit || this.dlgType === "info")) {
        // 编辑和详情模式：根据 useGetItem 选择接口
        try {
          const apiCall = this.useGetItem ? getItem : getAllItem;
          const { data: response } = await apiCall(data.id);
          if (response.code === "200") {
            console.log(response, "response.data");
            let fullData = response.data || {};
            if (fullData.imgUrl) {
              fullData.imgUrl = JSON.parse(fullData.imgUrl);
            } else {
              fullData.imgUrl = [];
            }
            if (fullData.fileUrl) {
              fullData.fileUrl = JSON.parse(fullData.fileUrl);
            } else {
              fullData.fileUrl = [];
            }
            if (fullData.annexUrl) {
              fullData.annexUrl = JSON.parse(fullData.annexUrl);
            } else {
              fullData.annexUrl = [];
            }

            // 处理岗位回显
            if (fullData.postId) {
              await this.loadPostInfo(fullData.postId);
            }

            this.formData = fullData
              ? JSON.parse(JSON.stringify(fullData))
              : [];
          } else {
            this.$message.error(response.msg || "获取数据失败");
            this.formData = Object.assign({}, data);
          }
        } catch (error) {
          console.error("获取数据失败:", error);
          this.$message.error("获取数据失败");
          this.formData = Object.assign({}, data);
        }
      } else {
        this.resetForm();
        this.selectDlgData = []; // 清空岗位选择数据
      }

      this.$nextTick(() => {
        this.$refs.dataForm && this.$refs.dataForm.clearValidate();
      });
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.formData = {
        id: null, // 编辑时的ID
        annexUrl: "", // 附件路径
        branchId: null, // 部门ID
        branchName: "", // 部门名称
        branchOperationId: null, // 事业部ID
        branchOperationName: "", // 事业部名称
        branchProjectId: null, // 项目部ID
        branchProjectName: "", // 项目部名称
        fileUrl: "", // 原始文件路径
        imgUrl: "", // 图片路径
        name: "", // 技术资料名称
        postId: null, // 岗位ID
        postName: "", // 岗位名称
        premises: "", // 楼宇
        projectId: null, // 项目ID
        projectName: "", // 项目名称
        remark: "", // 备注
        storey: "", // 楼层
        type: null, // 图纸类型
        typeStr: "", // 图纸类型名称
        userPostId: null, // 用户岗位ID

        // 前端文件管理字段（用于组件交互，提交时转换为URL）
        imgUrl: [], // 共享图片文件列表
        fileUrl: [], // 原始文件列表
        annexUrl: [], // 附件文件列表
      };
      this.selectDlgData = []; // 清空岗位选择数据
    },

    /**
     * 关闭对话框
     */
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
      // 通知父组件清空 dlgType
      this.$emit('close-dialog');
    },

    /**
     * 提交表单
     */
    async handleSubmit() {
      try {
        // 表单验证
        const valid = await this.validateForm();
        if (!valid) return;

        this.submitLoading = true;

        // 准备提交数据，按照API接口格式
        const submitData = this.formatSubmitData();

        console.log("提交数据:", submitData);

        // 调用API保存数据
        let result;
        if (this.isEdit) {
          result = await updateTechnicalData(submitData);
        } else {
          result = await createTechnicalData(submitData);
        }
        console.log("提交结果:", result);

        if (result.data.code === "200") {
          this.$message.success(this.isEdit ? "更新成功" : "创建成功");
          this.$parent.getDrawingTypeOptions();
          this.$parent.searchFunc();
          this.handleClose();
        } else {
          this.$message.error(result.data.msg || "操作失败");
        }
      } catch (error) {
        console.error("提交失败:", error);
        this.$message.error(error.message || "操作失败");
      } finally {
        this.submitLoading = false;
      }
    },

    /**
     * 格式化提交数据，转换为API接口格式
     */
    formatSubmitData() {
      // 获取当前用户岗位ID
      const userInfo = JSON.parse(window.localStorage.ERPUserInfo || "{}");
      const userPostId = userInfo.postId || 0;

      const data = {
        // 基本信息
        name: this.formData.name,
        type: parseInt(this.formData.type) || 1,
        typeStr: this.formData.typeStr || "",
        premises: this.formData.premises || "",
        storey: this.formData.storey || "",
        remark: this.formData.remark || "",

        // 项目和部门信息
        projectId: parseInt(this.formData.projectId) || 0,
        projectName: this.formData.projectName || "",
        branchId: parseInt(this.formData.branchId) || 0,
        branchName: this.formData.branchName || "",
        branchOperationId: parseInt(this.formData.branchOperationId) || 0,
        branchOperationName: this.formData.branchOperationName || "",
        branchProjectId: parseInt(this.formData.branchProjectId) || 0,
        branchProjectName: this.formData.branchProjectName || "",

        // 岗位信息
        postId: this.formData.postId || "",
        postName: this.formData.postName || "",
        userPostId: userPostId,

        // 文件路径
        imgUrl: this.handleFile(this.formData.imgUrl),
        fileUrl: this.handleFile(this.formData.fileUrl),
        annexUrl: this.handleFile(this.formData.annexUrl),
      };

      // 如果是编辑模式，添加ID
      if (this.isEdit && this.formData.id) {
        data.id = this.formData.id;
      }

      return data;
    },

    handleFile(file) {
      let fileList = [];
      for (let item of file) {
        let obj = {
          name: item.name,
          url: item.url,
        };
        fileList.push(obj);
      }
      return JSON.stringify(fileList);
    },
    /**
     * 表单验证
     */
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.dataForm.validate((valid) => {
          resolve(valid);
        });
      });
    },

    /**
     * 根据岗位ID获取岗位详细信息用于回显
     */
    async loadPostInfo(postIds) {
      try {
        if (!postIds) return;

        const postIdArray = postIds.split(",").filter((id) => id.trim());
        if (postIdArray.length === 0) return;

        const res = await postAction(
          "/org/findUserNameAndPostCodeByPostIds",
          { postIds }
        );

        if (res.data.code === "200") {
          const postData = res.data.data;

          // 直接使用接口返回的数据，因为接口已经返回了完整的岗位信息
          this.selectDlgData = postData.map((item) => {
            return {
              postId: item.postId, // 使用接口返回的 postId
              label: item.postName, // 岗位名称
              postCode: item.postCode, // 岗位代码
              userName: item.userName, // 用户名
            };
          });
          this.$refs.basicForm.selectDlgData = this.selectDlgData;
          console.log("岗位回显数据:", this.selectDlgData);
        } else {
          console.error("获取岗位信息失败:", res.data.msg);
        }
      } catch (error) {
        console.error("获取岗位信息失败:", error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
