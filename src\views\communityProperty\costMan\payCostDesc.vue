<template>
  <!-- 列表少 
实缴金额 优惠申请 -->
  <!-- 缴费管理 > 查看费用 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item v-if="list.length > 0">
          <span v-if="listQuery.payType == 1"> 房屋：{{ list[0]['roomName'] }} </span>
          <span v-if="listQuery.payType == 2"> 车位：{{ list[0]['parkingName'] }} </span>
          <span v-if="listQuery.payType == 3"> 车库：{{ list[0]['garageName'] }} </span>
        </el-form-item>
        <el-form-item class="fr">
          <el-button icon="el-icon-edit" type="primary" size="mini" @click="payItem">欠费催缴</el-button>
          <!-- <el-button icon="el-icon-more-outline" type="primary" size="mini" @click="batchPayment">缴费</el-button> -->
          <el-button icon="el-icon-search" type="success" size="mini" @click="getList">查询</el-button>
          <el-button icon="el-icon-back" size="mini" @click="backItem">返回</el-button>
        </el-form-item>
        <el-form-item class="fr">
          <el-select clearable v-model="listQuery.status" style="width: 160px" placeholder="缴费状态">
            <el-option label="已缴费" value="1"></el-option>
            <el-option label="未缴费" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="fr">
          <el-select v-model="listQuery.feeType" filterable clearable placeholder="请选择费用类型">
            <el-option v-for="item in costTypeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class="m-small-table" height="100%" v-loading="listLoading" :data="list" border fit ref="multipleTable"
        :key="tableKey" :row-key="getRowKeys" @row-click="tableRowClick" @selection-change="selectionChange"
        highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <!-- <el-table-column :selectable="checkInit" align="center" :reserve-selection="true" type="selection" width="55"
          fixed> </el-table-column> -->
        <el-table-column label="费用类型">
          <template slot-scope="scope">
            <span>{{ scope.row.feeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="费用项目">
          <template slot-scope="scope">
            <span>{{ scope.row.configName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="费用标识">
          <template slot-scope="scope">
            <span>{{ scope.row.feeFlagName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="应收金额">
          <template slot-scope="scope">
            <span>{{ scope.row.receivableAmount ? scope.row.receivableAmount.toFixed(2) : 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="建账时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计费开始时间">
          <template slot-scope="scope">
            <span>{{ scope.row.startDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计费结束时间">
          <template slot-scope="scope">
            <span>{{ scope.row.endDate }}</span>
          </template>
        </el-table-column>

        <el-table-column label="说明" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <template v-if="scope.row.remark">
              <div v-for="(item, index) in scope.row.remark.split(',')" :key="index">{{ item }}</div>
            </template>
          </template>
        </el-table-column>

        <el-table-column label="缴费状态" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 1" type="success">已缴费</el-tag>
            <el-tag v-else-if="scope.row.status == 0 && scope.row.isPay == 1" type="primary">支付中</el-tag>
            <el-tag v-else-if="scope.row.status == 0" type="danger">未缴费</el-tag>
            
          </template>
        </el-table-column>

        <el-table-column label="缴费时间" prop="payTime" width="160" align="center">
        </el-table-column>

        <el-table-column label="操作" align="center" width="380" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status == 0" type="primary" size="mini" icon="el-icon-money" plain
              @click="batchPayment(scope.row)">缴费</el-button>
            <el-button type="success" size="mini" icon="el-icon-view" plain
              @click="editItem(scope.row, 'VIEW')">缴费历史</el-button>
            <el-button v-if="scope.row.status == 0" type="danger" size="mini" icon="el-icon-back" plain
              @click="delItem(scope.row)">取消费用</el-button>
            <!-- <el-button type="primary" size="mini" icon="el-icon-printer" plain @click="printItem(scope.row)"
              >打印</el-button
            > -->
            <!-- {{scope.row.status}}
            {{scope.row.isPay}} -->
            <el-button v-if='scope.row.status == 0 && scope.row.isPay == 1' type="primary" size="mini" icon="el-icon-refresh" plain 
              @click="resetPayStateFunc(scope.row)"
              >重置支付状态</el-button
            >
            
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal="false" :title="dlgType == 'EDIT' ? '缴费' : '缴费历史'" :visible.sync="dlgShow"
      width="1200px" append-to-body>
      <el-form ref="dlgForm" :disabled="dlgType == 'VIEW'" :rules="rules" :model="dlgData" label-position="right"
        label-width="135px">
        <el-divider v-if="dlgType == 'VIEW'">费用信息</el-divider>
        <el-row>
          <el-col :span="8">
            <el-form-item label="费用ID：">{{ dlgData.id }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费用标识：">{{ dlgData.feeFlagName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费用类型：">{{ dlgData.feeName }}</el-form-item>
          </el-col>
        </el-row>
        <el-col :span="8">
          <el-form-item label="付费对象：">{{ dlgData.payName }}</el-form-item>
        </el-col>
        <el-row>
          <el-col :span="8">
            <el-form-item label="费用项：">{{ dlgData.configName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费用状态：">{{ dlgData.status == 0 ? '未缴费' : '已缴费' }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="缴费时间：">{{
              dlgData.payTime
            }}</el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="建账时间：">{{ dlgData.createTime }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计费开始时间：">{{ dlgData.startDate }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计费结束时间：">{{ dlgData.endDate }}</el-form-item>
          </el-col>

        </el-row>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="说明：">{{ dlgData.remark }}</el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="备注：">{{ dlgData.orderRemark }}</el-form-item>
          </el-col>
        </el-row>

        <el-divider v-if="dlgType == 'VIEW'">缴费历史</el-divider>
        <el-table ref="dlgTableRef" key="id" class="m-small-table" :data="dlgData.list" border fit highlight-current-row>
          <el-table-column :selectable="dTDisFn" label="#" align="center" type="selection" width="50">
          </el-table-column>

          <el-table-column label="费用类型">
            <template slot-scope="scope">
              <span>{{ scope.row.feeName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用项目">
            <template slot-scope="scope">
              <span>{{ scope.row.configName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用标识" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.feeFlagName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用摘要">
            <template slot-scope="scope">
              <span>{{ scope.row.startDate }} ~ {{ scope.row.endDate }} {{ scope.row.feeName }} </span>
            </template>
          </el-table-column>

          <el-table-column label="应收金额" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ num2Round(scope.row.receivableAmount) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="实缴金额" align="center" width="110">
            <template slot-scope="scope">
              <span v-if="dlgType == 'VIEW'">{{
                num2Round(scope.row.receivedAmount)
              }}</span>
              <span v-else>{{
                num2Round(scope.row.receivableAmount + scope.row.zhinajin - scope.row.preferentialAmount)
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="优惠申请" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ num2Round(scope.row.preferentialAmount) }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="优惠券">
            <template slot-scope="scope">
              <span>{{ scope.row.aaaa }}</span>
            </template>
          </el-table-column> -->

          <!-- <el-table-column label="费用金额">
            <template slot-scope="scope">
              <span>{{ scope.row.amount.toFixed(2) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="优惠金额">
            <template slot-scope="scope">
              <span>{{ scope.row.preferentialAmount }}</span>
            </template>
          </el-table-column> -->

          <el-table-column label="滞纳金" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ scope.row.zhinajin }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column label="审核状态" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.isAudit == 2" type="danger">已驳回</el-tag>
            </template>
          </el-table-column> -->
        </el-table>
        <!-- v-if="dlgType == 'EDIT'" -->

        <!-- 缴费历史 -->
        <el-form-item class="mt10" label="收费方式：" prop="offlinePayWay">
          <el-checkbox-group v-model="dlgData.offlinePayWayArr" disabled>
            <el-checkbox v-for="item of payWayList" :key="item.id" :label="item.id" name="type">{{
              item.name
            }}</el-checkbox>
          </el-checkbox-group>


          <!-- <div v-if="dlgData.offlinePayWay == 0 && dlgType != 'EDIT'">线上支付</div>
          <el-select v-else style="width: 200px" v-model="dlgData.offlinePayWay" filterable clearable placeholder="收费方式"
            :disabled="dlgType != 'EDIT'">
            <el-option v-for="item in payWayList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>-->
        </el-form-item> 

        <el-form-item v-for="(item,index) of dlgData.offlinePayWayList" :label="item.label" :key="index">

          <el-input-number v-model="item.money" :precision="2" :step="0.01"  placeholder="金额" disabled></el-input-number> 元
        </el-form-item>

        <el-form-item label="优惠券：">
          <el-tag type="primary">{{ dlgData.couponMoney }} 元</el-tag>
        </el-form-item>

        <!-- v-if="dlgType == 'EDIT'" -->
        <el-form-item label="合计缴费金额：">
          <div>
            <span class="fdanger">{{ dlgData.totalMoney }} 元</span>
            (实缴金额:{{ totalMoney }} - 优惠券:{{ this.dlgData.couponMoney }})
          </div>
          <!-- <span class="fdanger font24">{{ totalMoney.toFixed(2) }}元</span> -->
        </el-form-item>
        <!-- <el-form-item v-if="dlgType != 'EDIT'" label="备注：">
          <el-input
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.remark"
            type="textarea"
            placeholder="请输入备注"
            :disabled="dlgType != 'EDIT'"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>

        <el-button v-if="dlgType !== 'VIEW'" type="success" :loading="dlgLoading" @click="subDlg" icon="el-icon-check">
          <span>缴费</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" title="缴费" @close="closeBatchPaymentDlg" :visible.sync="batchPaymentDlg"
      width="1200px" append-to-body>
      <el-form ref="batchPaymentForm" :rules="batchPaymentRules" :model="batchPaymentData" label-position="right"
        label-width="135px">
        <el-table ref="batchPayTableRef" v-loading="batchPaymentLoading" key="id" class="m-small-table"
          :data="batchPaymentData.list" border fit highlight-current-row>
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="付费对象" width="160">
            <template slot-scope="scope">
              <span v-if="scope.row.payType == '1'">{{ scope.row.roomName + '(房屋)' }}</span>
              <span v-else-if="scope.row.payType == '2'">{{ scope.row.parkingName + '(车位)' }}</span>
              <span v-else>{{ scope.row.garageName + '(车库)' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用类型">
            <template slot-scope="scope">
              <span>{{ scope.row.feeName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用项目">
            <template slot-scope="scope">
              <span>{{ scope.row.configName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用标识" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.feeFlagName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="费用摘要">
            <template slot-scope="scope">
              <span>{{ scope.row.startDate }} ~ {{ scope.row.endDate }} {{ scope.row.feeName }} </span>
            </template>
          </el-table-column>

          <el-table-column label="应收金额" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ num2Round(scope.row.receivableAmount) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="实缴金额" align="center" width="110">
            <template slot-scope="scope">
              <span>{{
                (batchPaymentMoney > oldBatchPaymentMoney ? num2Round(scope.row.receivableAmount + scope.row.changeAmount
                  +
                  scope.row.zhinajin - scope.row.preferentialAmount) : num2Round(scope.row.receivableAmount -
                    scope.row.changeAmount + scope.row.zhinajin - scope.row.preferentialAmount)).toFixed(2)||0
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="优惠申请" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ num2Round(scope.row.preferentialAmount) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="滞纳金" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ num2Round(scope.row.zhinajin) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="60" align="center">
            <template slot-scope="scope">
              <el-button @click="delSelectRow(scope.$index)" type="danger" size="mini" icon="el-icon-delete"
                plain></el-button>
            </template>
          </el-table-column>

        </el-table>
        <!-- aaaa -->
        <el-form-item class="mt10" label="收费方式：" prop="offlinePayWayArr">
          <!-- <div v-if="batchPaymentData.offlinePayWay == 0">线上支付</div> -->
          
          <!-- <el-select style="width: 200px" v-model="batchPaymentData.offlinePayWay" filterable clearable
            placeholder="收费方式">
            <el-option v-for="item in payWayList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select> -->
          <!-- {{ batchPaymentData.offlinePayWayArr }}-dd -->
          <el-checkbox-group v-model="batchPaymentData.offlinePayWayArr" @change="offlinePayWayArrChange">
            <el-checkbox v-for="item of payWayList" :key="item.id" :label="item.id" name="type">{{
              item.name
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item v-for="(item,index) of batchPaymentData.offlinePayWayList" :label="item.label" :key="index">

          <el-input-number @change="itemMoneyChange" v-model="item.money" :precision="2" :step="0.01"  placeholder="金额"></el-input-number> 元
        </el-form-item>


        <el-form-item label="优惠券：">
          <el-tag type="primary">{{ batchPaymentData.couponMoney }} 元</el-tag>
        </el-form-item>

        <el-form-item label="合计缴费金额：">
          <div>
            <el-input v-model="batchPaymentData.totalMoney" placeholder="合计金额" style="width: 100px" disabled /> 元
            <span class="ml10">(实缴金额:{{ sjMoney }} - 优惠券:{{ batchPaymentData.couponMoney }})</span>
          </div>
          <!-- <div>
            合计缴费金额应在 {{ smBatchPaymentMoney }} 至 {{ bigBatchPaymentMoney }} 元之间
          </div> -->
        </el-form-item>


        
        <!-- max-{{ bigBatchPaymentMoney }},min-{{ smBatchPaymentMoney }} -->
        <!-- <el-form-item label="合计金额原：">
          <div>
            <el-input-number @change="batchPaymentMoneyChange" v-model="batchPaymentMoney" :max="bigBatchPaymentMoney"
              :min="smBatchPaymentMoney" :precision="2" :step="0.01"></el-input-number>元
            (实缴金额:{{ sjMoney }} - 优惠券:{{ batchPaymentData.couponMoney }})
          </div>
        </el-form-item> -->

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchPaymentDlg = false" icon="el-icon-back">取消</el-button>

        <el-button type="success" @click="subBatchPaymentDlg" icon="el-icon-check">缴费</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { findPayBillSumPage, payOweAmount, payOweAmountV, delPayFeeBillSumById, getPayBillPage } from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'
import { postAction, getAction } from '@/api'

let dlgDataEmpty = {
  list: [],
  offlinePayWay: '',
}
let batchPaymentDataEmpty = {
  list: [],
  offlinePayWay: '',
  couponMoney: 0,
  couponId: "",

  offlinePayWayArr: [],
  offlinePayWayList:[],
  totalMoney: ''
}
export default {
  name: 'payCostDesc',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data() {
    return {
      id: '',
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        remark: [{ required: true, message: '必填字段', trigger: 'blur' }],
        offlinePayWayArr: [{ required: true, message: '必填字段', trigger: 'change' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),

      //批量弹框
      batchPaymentData: JSON.parse(JSON.stringify(batchPaymentDataEmpty)),
      batchPaymentDlg: false,
      batchPaymentRules: {
        offlinePayWayArr: [{ required: true, message: '必填字段', trigger: 'change' }],
      },


      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        status: '',
        roomId: '',
        parkingId: '',
        garageId: '',
        payType: '',
        feeType: ""
      },
      totalMoney: 0,
      discountList: [],
      userInfo: {},
      costTypeList: [],
      selectList: [],
      costTypeList: [],//费用类型字典
      feeFlagList: [
        {
          id: '1',
          name: '周期性费用',
        },
        {
          id: '2',
          name: '一次性费用',
        },
      ],
      payWayList: [
        {
          id: '1',
          name: '微信',
        },
        {
          id: '2',
          name: '支付宝',
        },
        {
          id: '3',
          name: '现金',
        },
      ],

      // 弹窗 优惠券
      yhqMoney: 0,

      diaPostList: [],//多选数据
      tableKey: 0,
      batchPaymentMoney: 0,
      bigBatchPaymentMoney: 0,
      smBatchPaymentMoney: 0,
      oldBatchPaymentMoney: 0,//未修改前金额
      selectBatchPaymentList: [],
      sjMoney: 0,
      batchPaymentLoading: false
    }
  },

  created() {
    this.listQuery.payType = this.$route.params.type
    this.id = this.$route.params.id
    if (this.$route.params.type == 1) {
      this.listQuery.roomId = this.$route.params.id
    } else if (this.$route.params.type == 2) {
      this.listQuery.parkingId = this.$route.params.id
    } else if (this.$route.params.type == 3) {
      this.listQuery.garageId = this.$route.params.id
    }
    utils.getDataDict(this, 'costType', 'costTypeList')
    setTimeout(() => {
      this.getList()
    }, 400)
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    utils.getDataDict(this, "costType", "costTypeList");
  },

  methods: {
    itemMoneyChange(){
      let totalMoney = 0
      for (let item of this.batchPaymentData.offlinePayWayList) {
        if (item.money) totalMoney += item.money
      }
      this.batchPaymentData.totalMoney = utils.num2Round(totalMoney)
    },
    offlinePayWayArrChange(value) {
      console.log('===value', value)
      let offlinePayWayList = []
      let old_offlinePayWayList = JSON.parse(JSON.stringify(this.batchPaymentData.offlinePayWayList))
      let totalMoney = 0
      if (value.indexOf("1")>=0) {
        let obj = {
          key:'wxPay',
          label: '微信缴费金额：',
          money:undefined,
        }
        let index = old_offlinePayWayList.findIndex(item=>item.key=='wxPay')
        if (index >=0){
          obj.money = old_offlinePayWayList[index].money
          totalMoney += obj.money 
        }
        offlinePayWayList.push(obj)
      }
      if (value.indexOf("2")>=0) {
        let obj = {
          key:'zfbPay',
          label: '支付宝缴费金额：',
          money:undefined,
        }
        let index = old_offlinePayWayList.findIndex(item=>item.key=='zfbPay')
        if (index >=0){
          obj.money = old_offlinePayWayList[index].money
          totalMoney += obj.money 
        }
        offlinePayWayList.push(obj)
      }
      if (value.indexOf("3")>=0) {
        let obj = {
          key:'cashPay',
          label: '现金缴费金额：',
          money:undefined,
        }
        let index = old_offlinePayWayList.findIndex(item=>item.key=='cashPay')
        if (index >=0){
          obj.money = old_offlinePayWayList[index].money
          totalMoney += obj.money 
        }
        offlinePayWayList.push(obj)
      }
      this.batchPaymentData.offlinePayWayList = offlinePayWayList
      this.batchPaymentData.totalMoney = utils.num2Round(totalMoney)

    },

    /////////////////
    resetPayStateFunc(data){
      this.$confirm('确定重置支付状态?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        getAction('/unity/order/updateIsPay/'+data.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })

    },

    //////
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    num2Round(num, digit = 2) {
      return utils.num2Round(num, digit)
    },
    // 打印
    printItem(row) {
      let newWin = this.$router.resolve({ path: '/printPaper', query: { id: row.id } })
      window.open(newWin.href, '_blank')
    },

    // 弹窗表格禁选
    dTDisFn(row, index) {
      console.log('row', row)
      console.log('index', index)

      return false
    },

    ////

    // selectionChange(val) {
    //   this.selectList = JSON.parse(JSON.stringify(val))
    //   this.calcMoney()
    // },

    calcMoney() {
      if (this.batchPaymentDlg == true) {
        let money = 0
        for (let i of this.batchPaymentData.list) {
          money += i.receivableAmount + i.zhinajin - i.preferentialAmount
        }
        this.sjMoney = utils.num2Round(money)
        if (this.batchPaymentData.couponMoney > 0) {
          money = utils.num2Round(money - this.batchPaymentData.couponMoney)
        }
        console.log(this.batchPaymentData.list, "this.batchPaymentData.list");
        this.batchPaymentMoney = utils.num2Round(money)
        this.bigBatchPaymentMoney = 0
        this.smBatchPaymentMoney = 0
        this.oldBatchPaymentMoney = 0 //未修改前金额
        this.bigBatchPaymentMoney = utils.num2Round(this.batchPaymentMoney + 1)
        if(utils.num2Round(this.batchPaymentMoney - 1)>0){
          this.smBatchPaymentMoney=utils.num2Round(this.batchPaymentMoney - 1)
        }else{
          this.smBatchPaymentMoney=0
        }
        this.oldBatchPaymentMoney = JSON.parse(JSON.stringify(this.batchPaymentMoney))
      } else {
        let money = 0
        for (let i of this.selectList) {
          money += i.receivedAmount + i.zhinajin - i.preferentialAmount
        }
        this.totalMoney = utils.num2Round(money)
      }
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    formatList() {
      for (let i of this.list) {
        i.feeName = utils.getNameById(i.feeType, this.costTypeList)
        i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList)
      }
    },

    // 获取数据
    getList() {
      this.count++
      this.listLoading = true
      findPayBillSumPage(this.listQuery).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = res.data.data ? JSON.parse(JSON.stringify(res.data.data)) : []
          this.total = res.data.page ? res.data.page.total : 0
          this.$refs.multipleTable.clearSelection();
          this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    checkInit(row, index) {
      if (row.isPay == '1'||row.status=='1') {
        return false
      } else {
        return true
      }
    },

    // 欠费催缴
    payItem() {
      this.$router.push({ path: `/communityProperty/costMan/payCost/${this.id}/${this.listQuery.payType}` })
    },

    // 返回
    backItem() {
      this.$router.go(-1)
    },

    // 编辑
    editItem(data, type) {
      // if(data.isPay == '1'){
      //   this.$message.warning('存在支付中缴费,请勿重复提交')
      //   return
      // }
      this.dlgData = {...dlgDataEmpty,...data }
      // this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      if (this.list.length > 0) {
        this.dlgData.payName =
          this.listQuery.payType == 1
            ? this.list[0]['roomName'] + '(房屋)'
            : this.listQuery.payType == 2
              ? this.list[0]['parkingName'] + '(车位)'
              : this.list[0]['garageName'] + '(车库)'
      }
      this.dlgType = type
      this.dlgShow = true
      if(type=='VIEW'){
        this.$nextTick(()=>{
          this.$refs['dlgForm'].clearValidate()
        })
      }
      let postParam = {
        page: 1,
        limit: 99,
        status: type == 'EDIT' ? '0' : '1',
        // status: '1',
        billSumId: [data.id],
      }
      postParam.billSumId=postParam.billSumId.join(',')
      getPayBillPage(postParam).then((res) => {
        if (res.data.code == 200) {
          for (let i of res.data.data) {
            i.feeName = utils.getNameById(i.feeType, this.costTypeList)
            i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList)
            i.amount = i.receivableAmount + i.zhinajin - i.preferentialAmount
          }
          this.dlgData.list = JSON.parse(JSON.stringify(res.data.data))
          this.selectList = JSON.parse(JSON.stringify(res.data.data))
          this.$nextTick(() => {
            
            if (this.dlgType !== 'VIEW') {
              this.getYhqList(JSON.parse(JSON.stringify(res.data.data))) // 获取优惠券
            } else {
              setTimeout(() => {
                for (let item of this.dlgData.list) {
                  this.$refs.dlgTableRef.toggleRowSelection(item, true)
                }
              }, 200)
              //  this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
            }
            this.calcMoney() // 计算金额

            let infoDate = JSON.parse(JSON.stringify(res.data.data[0]))
            infoDate.offlinePayWay = data.offlinePayWay
            this.totalMoney = infoDate.receivedAmount

            let totalMoney = 0
            console.log('==aaaainfoDate.payInfoJson', infoDate.payInfoJson)
            if (infoDate.payInfoJson) {
              console.log('111', infoDate)
              let offlinePayWayArr = []
              let offlinePayWayList = []
              let payInfoJson = JSON.parse(infoDate.payInfoJson)
              if (payInfoJson.wxPay) {
                offlinePayWayArr.push('1')
                offlinePayWayList.push({key:'wxPay',
                  label: '微信缴费金额：',
                  money:payInfoJson.wxPay,})
                  totalMoney+= parseFloat(payInfoJson.wxPay)
              }
              if (payInfoJson.zfbPay) {
                offlinePayWayArr.push('2')
                offlinePayWayList.push({key:'zfbPay',
                  label: '支付宝缴费金额：',
                  money:payInfoJson.zfbPay,})
                  totalMoney+= parseFloat(payInfoJson.zfbPay)
                
              }
              if (payInfoJson.cashPay) {
                offlinePayWayArr.push('3')
                offlinePayWayList.push({key:'cashPay',
                  label: '现金缴费金额：',
                  money:payInfoJson.cashPay,})
                  totalMoney+= parseFloat(payInfoJson.cashPay)

              }
              this.dlgData.totalMoney = utils.num2Round(totalMoney)
              this.dlgData.offlinePayWayArr = offlinePayWayArr
              this.dlgData.offlinePayWayList = offlinePayWayList
              console.log('=== this.dlgData',  this.dlgData)
            } else {
              this.dlgData.totalMoney = utils.num2Round(infoDate.receivedAmount- this.dlgData.couponMoney)
              console.log('222', infoDate)
              this.dlgData.offlinePayWayArr = [infoDate.offlinePayWay+'']
              if (infoDate.offlinePayWay == 1) {
                this.dlgData.offlinePayWayList = [{key:'wxPay',
                  label: '微信缴费金额：',
                  money:this.dlgData.totalMoney,}]
              }
              if (infoDate.offlinePayWay == 2) {
                this.dlgData.offlinePayWayList = [{key:'zfbPay',
                  label: '支付宝缴费金额：',
                  money:this.dlgData.totalMoney,}]
              }
              if (infoDate.offlinePayWay == 3) {
                this.dlgData.offlinePayWayList = [{key:'cashPay',
                  label: '现金缴费金额：',
                  money:this.dlgData.totalMoney,}]
              }
              // console.log('=== this.dlgData.offlinePayWayList',  this.dlgData)
            }

            this.dlgData= JSON.parse(JSON.stringify( this.dlgData))
          })
        }
      })
    },
    // 获取优惠券接口
    getYhqList() {
      let list = this.dlgData.list
      if (!list.length) return false
      let ids = []
      let amount = 0
      for (let item of list) {
        amount += item.receivableAmount
      }

      amount = utils.num2Round(amount)

      getAction(`/unity/coupon/matchCoupon/${this.dlgData.id}/${amount}`).then((res0) => {
        let res = res0.data
        if (res.code == 200) {
          console.log('res', res)
          // for (let i = 0; i < res.data.length; i++) {
          //   list[i].yhq = res.data[i].yhq
          // }
          // this.dlgData.list = JSON.parse(JSON.stringify(list))

          console.log('res.data.couponMoney', res.data.couponMoney)

          this.dlgData.couponId = res.data.couponId // 优惠券
          this.dlgData.couponMoney = res.data.couponMoney

          this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
        } else {
          this.$message.warning(res.msg)
        }

        setTimeout(() => {
          for (let item of this.dlgData.list) {
            this.$refs.dlgTableRef.toggleRowSelection(item, true)
          }
        }, 200)
      })
    },

    // 删除
    delItem(data) {
      this.$confirm('确定取消费用?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        delPayFeeBillSumById(data.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 显示弹窗
    addItem() {
      if (this.selectList.length == 0) {
        this.$message.warning('请选择收费项')
        return
      }
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg() {
      if (this.selectList.length == 0) {
        this.$message.warning('请选择缴费记录')
        return
      }
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          this.$confirm('确定缴费?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            let postParam = {}
            // 原来的
            // postParam.projectId = this.userInfo.projectId
            // let billIds = []
            // for (let i of this.selectList) {
            //   billIds.push(i.id)
            // }
            // postParam.billIds = billIds.join(',')
            // postParam.payType = this.listQuery.payType
            // postParam.roomId = this.selectList[0]['roomId']
            // postParam.roomName = this.selectList[0]['roomName']
            // postParam.communityId = this.selectList[0]['communityId']
            // postParam.creator = Cookie.get('userId')
            // postParam.createName = this.userInfo.label
            // postParam.offlinePayWay = this.dlgData.offlinePayWay

            // 现在
            // let billIds = []
            // for (let i of this.selectList) {
            //   billIds.push(i.id)
            // }
            let sendObj = {
              billSumId: this.dlgData.id,
              communityId: this.selectList[0]['communityId'],
              openId: '',
              payType: this.listQuery.payType, // 1-房屋 2-车位 3-车库

              // 小程序线上支付   payWay: 1,offlinePayWay:0
              // web现场支付   payWay: 3,offlinePayWay: 1 微信 2 支付宝 3 现金
              createName:this.userInfo.label,
              payWay: 3,
              offlinePayWay: this.dlgData.offlinePayWay,
              roomId: this.selectList[0]['roomId'],
              roomName: this.selectList[0]['roomName'],
              couponId: this.dlgData.couponId,
              couponMoney: this.dlgData.couponMoney,
            }

            // console.log('sendObj', sendObj)
            // return false

            this.dlgLoading = true
            payOweAmount(sendObj).then((res) => {
              this.dlgLoading = false
              if (res.data.code == 200) {
                this.getList()
                this.calcMoney()
                this.dlgShow = false
                this.$message.success(res.data.msg)
              } else if (res.data.code == '406') {
                this.$message.warning(res.data.msg)
                this.getYhqList()
              } else {
                this.$message.warning(res.data.msg)
              }
            })
          })
        }
      })
    },

    // 上传对话框图片
    beforeUpload(file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then((res) => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg() {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },
    tableRowClick(row, column, event) {
      // if (column.label == '操作') return false

      // this.$refs.multipleTable.toggleRowSelection(row)
    },
    // selectionChange(val) {
    //   console.log('=====val', val)
    //   this.batchPaymentData.list = []
    //   this.selectBatchPaymentList = []
    //   this.batchPaymentData.list = JSON.parse(JSON.stringify(val))
    //   this.selectBatchPaymentList = JSON.parse(JSON.stringify(val))
    // },
    closeBatchPaymentDlg() {
      this.batchPaymentData.list = JSON.parse(JSON.stringify(this.selectBatchPaymentList))
    },
    getRowKeys(row) {
      return row.id
    },
    //批量缴费弹框
    batchPayment(row) {
      this.batchPaymentData.list = [row]

      this.batchPaymentData.offlinePayWayArr = []
      this.batchPaymentData.offlinePayWayList = []
      this.batchPaymentData.totalMoney = ''
      if (this.batchPaymentData.list.length <= 0) {
        this.$message({
          type: 'warning',
          message: '请选择缴费项',
        })
        return false
      }
      for (let i of this.batchPaymentData.list) {
        i.changeAmount = 0
        if (i.status != 0) {
          this.$message({
            type: 'warning',
            message: '请统一选择未缴费项',
          })
          return false
        }
        if (i.receivableAmount <= 0) {
          this.$message({
            type: 'warning',
            message: '应收金额为0请重新选择',
          })
          return false
        }
      }
      this.getSelectYhqList()
      this.getSelectInfo()
      this.batchPaymentDlg = true
    },
    //多选查缴费单信息
    getSelectInfo() {
      let idList = []
      for (let index = 0; index < this.batchPaymentData.list.length; index++) {
        idList.push(this.batchPaymentData.list[index].id)

      }
      let postParam = {
        page: 1,
        limit: 99,
        status: '0',
        // status: '1',
        billSumIds: idList.join(','),
      }
      this.batchPaymentLoading = true
      // aaaa
      getPayBillPage(postParam).then((res) => {
        this.batchPaymentLoading = false
        if (res.data.code == 200) {
          for (let i of res.data.data) {
            i.feeName = utils.getNameById(i.feeType, this.costTypeList)
            i.feeFlagName = utils.getNameById(i.feeFlag, this.feeFlagList)
            i.amount = i.receivableAmount + i.zhinajin - i.preferentialAmount
          }
          this.batchPaymentData.list = JSON.parse(JSON.stringify(res.data.data))
          for (let i of this.batchPaymentData.list) {
            i.changeAmount = 0
          }
          // this.selectBatchPaymentList = JSON.parse(JSON.stringify(res.data.data))
          this.$nextTick(() => {
            this.calcMoney() // 计算金额
            // if (this.dlgType !== 'VIEW') {
            //   this.getYhqList(JSON.parse(JSON.stringify(res.data.data))) // 获取优惠券
            // } else {
            //   setTimeout(() => {
            //     for (let item of this.dlgData.list) {
            //       this.$refs.dlgTableRef.toggleRowSelection(item, true)
            //     }
            //   }, 200)
            //   //  this.dlgData = JSON.parse(JSON.stringify(this.dlgData))
            // }
          })
        }
      })
    },
    // 多选获取优惠券接口
    getSelectYhqList() {
      let list = this.selectBatchPaymentList
      if (!list.length) return false
      let ids = []
      let amount = 0
      for (let item of list) {
        amount += item.receivableAmount
      }
      amount = utils.num2Round(amount)
      for (let index = 0; index < list.length; index++) {
        ids.push(list[index].id)
      }

      getAction(`/unity/coupon/matchCoupon/${ids.join(',')}/${amount}`).then((res0) => {
        let res = res0.data
        if (res.code == 200) {
          console.log('res', res)
          // for (let i = 0; i < res.data.length; i++) {
          //   list[i].yhq = res.data[i].yhq
          // }
          // this.dlgData.list = JSON.parse(JSON.stringify(list))

          if (res.data != null) {
            this.batchPaymentData.couponId = res.data.couponId // 优惠券
            this.batchPaymentData.couponMoney = res.data.couponMoney
          } else {
            this.batchPaymentData.couponId = ''
            this.batchPaymentData.couponMoney = 0
          }

          this.batchPaymentData = JSON.parse(JSON.stringify(this.batchPaymentData))
        } else {
          this.$message.warning(res.msg)
        }

        setTimeout(() => {
          for (let item of this.dlgData.list) {
            this.$refs.dlgTableRef.toggleRowSelection(item, true)
          }
        }, 200)
      })
    },
    delSelectRow(idx) {
      this.batchPaymentData.list.splice(idx, 1)
      for (let item of this.list) {
        let isHas = this.batchPaymentData.list.some((item2) => {
          return item2.id == item.id
        })
        if (isHas) {
          this.$refs.multipleTable.toggleRowSelection(item, true)
        } else {
          this.$refs.multipleTable.toggleRowSelection(item, false)
        }
      }
      let money = 0
      for (let i of this.batchPaymentData.list) {
        money += i.receivableAmount + i.zhinajin - i.preferentialAmount
      }
      this.batchPaymentMoney = utils.num2Round(money)
      this.bigBatchPaymentMoney = 0
      this.smBatchPaymentMoney = 0
      this.oldBatchPaymentMoney = 0 //未修改前金额
      this.bigBatchPaymentMoney = utils.num2Round(this.batchPaymentMoney + 1)
      if(utils.num2Round(this.batchPaymentMoney - 1)>0.01){
          this.smBatchPaymentMoney=utils.num2Round(this.batchPaymentMoney - 1)
        }else{
          this.smBatchPaymentMoney=0
        }
      this.oldBatchPaymentMoney = JSON.parse(JSON.stringify(this.batchPaymentMoney))
      if (this.selectBatchPaymentList.length > 0) {
        this.getSelectYhqList()
        this.getSelectInfo()
        this.batchPaymentMoneyChange()
      } else {
        this.sjMoney = 0
      }
      this.$forceUpdate()
    },
    batchPaymentMoneyChange() {
      //分配金额
      let list = this.batchPaymentData.list
      // value 数值
      // amount 分配数量
      let value = this.batchPaymentMoney < this.oldBatchPaymentMoney ? utils.num2Round(this.oldBatchPaymentMoney - this.batchPaymentMoney) : utils.num2Round(this.batchPaymentMoney - this.oldBatchPaymentMoney)
      let amount = this.batchPaymentData.list.length
      let first = utils.num2Round(utils.num2Round(Math.floor(parseInt((value / amount) * 100) / 100 * Math.pow(10, 4))) / Math.pow(10, 4))
      first = parseInt(first * 100) / 100
      let last = (value - first * (amount - 1)).toFixed(4)
      for (let i = 0; i <= list.length; i++) {
        if (i < list.length - 1) {
          list[i].changeAmount = 0
          list[i].changeAmount = utils.num2Round(first)
        } else {
          list[list.length - 1].changeAmount = 0
          list[list.length - 1].changeAmount = utils.num2Round(last)
        }
      }
      if(this.batchPaymentMoney==undefined){
        for (let i of list) {
          i.changeAmount=0
        }
      }
      let money = 0
        for (let i of list) {
          this.batchPaymentMoney > this.oldBatchPaymentMoney ? money += i.receivableAmount + i.changeAmount + i.zhinajin - i.preferentialAmount : money += i.receivableAmount - i.changeAmount + i.zhinajin - i.preferentialAmount
        }
      console.log(this.batchPaymentMoney,"batchPaymentMoney");
      
      this.sjMoney = utils.num2Round(money)
    },
    subBatchPaymentDlg() {
       setTimeout( () =>{
        if (this.batchPaymentData.list.length == 0) {
        this.$message({
          type: 'warning',
          message: '请选择缴费记录',
        })
        return false
      }
      this.$refs['batchPaymentForm'].validate((valid) => {
        if (valid) {
          
          if(this.batchPaymentData.offlinePayWayArr.length<=0){
            this.$message({
              type: 'warning',
              message: '请选择缴费方式',
            })
            return false
          }
          if(utils.isNull(this.batchPaymentData.totalMoney)){
            this.$message({
              type: 'warning',
              message: '请输入缴费金额',
            })
            return false
          }
          if (!(this.batchPaymentData.totalMoney>=this.smBatchPaymentMoney && this.batchPaymentData.totalMoney<=this.bigBatchPaymentMoney)){
            this.$message({
              type: 'warning',
              message: `缴费总金额应在 ${this.smBatchPaymentMoney} 至 ${this.bigBatchPaymentMoney} 之间`,
            })
            return false
          }
          this.$confirm('确定缴费?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            let newList = this.batchPaymentData.list
            let billList = []
            for (let index = 0; index < newList.length; index++) {
              let obj = {}
              obj.billSumId = newList[index].billSumId
              obj.billId = newList[index].id
              obj.receivedAmount = this.batchPaymentMoney > this.oldBatchPaymentMoney ? utils.num2Round(newList[index].receivableAmount + newList[index].changeAmount) : utils.num2Round(newList[index].receivableAmount -
                newList[index].changeAmount).toFixed(2)
              obj.receivableAmount = newList[index].receivableAmount
              obj.configId = newList[index].configId
              billList.push(obj)
            }

            let payInfoJson = {}
            for (let item of this.batchPaymentData.offlinePayWayList) {
              if (utils.isNull(item.money) || item.money == 0) {
                continue;
              }
              payInfoJson[item.key]=item.money
            }

            let sendObj = {
              payInfoJson: JSON.stringify(payInfoJson),

              communityId: this.batchPaymentData.list[0]['communityId'],
              openId: "",
              payType: this.listQuery.payType,
              payWay: 3,
              offlinePayWay: 1,
              roomId: this.batchPaymentData.list[0]['roomId'],
              roomName: this.batchPaymentData.list[0]['roomName'],
              roomTypeId: this.batchPaymentData.list[0]['roomTypeId'],
              couponId: this.batchPaymentData.couponId,
              couponMoney: this.batchPaymentData.couponMoney,
              feeBillList: billList,
              createName:this.userInfo.label,
            }
            if (sendObj.payType == "2") {
              sendObj.roomId = this.batchPaymentData.list[0].parkingId;
              sendObj.roomName = this.batchPaymentData.list[0].parkingName;
            }
            if (sendObj.payType == "3") {
              sendObj.roomId = this.batchPaymentData.list[0].garageId;
              sendObj.roomName = this.batchPaymentData.list[0].garageName;
            }
            if (sendObj.roomTypeId == 0) {
              this.$message({
                type: 'warning',
                message: '未绑定房产类型,该缴费单无效',
              })
              return false
            }
            payOweAmountV(sendObj).then((res) => {
              console.log(res);
              if (res.data.code == 200) {
                this.batchPaymentDlg = false
                this.getList()
                this.$refs.multipleTable.clearSelection();
                this.$message.success(res.data.msg)
              } else {
                this.$message.error(res.data.msg)
              }
            })

          })
        }
      })

       }, 10)
      
    }
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


