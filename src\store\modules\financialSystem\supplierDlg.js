// 供应商dlg组件

const supplierDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    supplierId: '',

    supplierName: '',

    supplierType: '',

    supplierInfo: {},
  },

  getters: {
    dlgShow: state => state.dlgShow,

    supplierId: state => state.supplierId,

    supplierName: state => state.supplierName,

    supplierType: state => state.supplierType,

    supplierInfo: state => state.supplierInfo
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_SUPPLIERID: (state, val) => {
      state.supplierId = val
    },

    SET_SUPPLIERNAME: (state, val) => {
      state.supplierName = val
    },

    SET_SUPPLIERTYPE: (state, val) => {
      state.supplierType = val
    },

    SET_SUPPLIERINFO: (state, val) => {
      state.supplierInfo = val
    },
  },

  actions: {

  }
}

export default supplierDlg
