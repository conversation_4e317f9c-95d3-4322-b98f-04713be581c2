<template>
  <div class="app-container">
    <template v-if="isShow">
      <div class="company-info">企业/公司信息</div>
      <div class="compant-form">
        <el-form label-width="130px" disabled :model="dlgData">
          <el-form-item label="公司名称：" prop="name">
            <el-input v-model="dlgData.name" placeholder="请输入公司名称" />
          </el-form-item>
          <el-form-item label="公司地址：" prop="addressShengCode">
            <el-input v-model="dlgData.addressSheng + dlgData.addressShi + dlgData.addressXian + dlgData.address" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入公司地址" />
          </el-form-item>
          <el-form-item label="联系电话：" prop="phone">
            <el-input v-model="dlgData.phone" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入联系电话" />
          </el-form-item>
          <el-form-item label="公司业务：" prop="companyBusinessValue">
            <el-select v-model="dlgData.companyBusinessValue" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请选择公司业务">
              <el-option v-for="item in businessList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="附近建筑：" prop="nearBuilds">
            <el-input v-model="dlgData.nearBuilds" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入附近建筑" />
          </el-form-item>
          <el-form-item label="公司法人：" prop="legalPerson">
            <el-input v-model="dlgData.legalPerson" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入公司法人" />
          </el-form-item>
          <el-form-item label="注册资本(万元)：" prop="registeredCapital">
            <el-input-number v-model="dlgData.registeredCapital" :disabled="dlgData.isAudit && dlgData.isAudit != 2" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item label="成立日期：" prop="buildDate">
            <el-date-picker v-model="dlgData.buildDate" type="date" :disabled="dlgData.isAudit && dlgData.isAudit != 2" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="成立日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="登记机关：" prop="registrationAuthority">
            <el-input v-model="dlgData.registrationAuthority" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入登记机关" />
          </el-form-item>
          <el-form-item label="证件号码：" prop="certificateCode">
            <el-input v-model="dlgData.certificateCode" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入证件号码" />
          </el-form-item>
          <el-form-item label="有效期：" prop="validityDate">
            <el-date-picker v-model="dlgData.validityDate" :disabled="dlgData.isAudit && dlgData.isAudit != 2" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="有效期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="营业执照照片：" prop="certificateUrl">
            <el-image :preview-src-list="[dlgData.certificateUrl]" class='upload-img' :src="dlgData.certificateUrl" alt="">
              <div class="el-image__error" slot="error">
                暂无照片
              </div>
            </el-image>
          </el-form-item>
          <el-form-item label="审核备注：">
            <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" :disabled="dlgData.isAudit && dlgData.isAudit != 2" v-model="dlgData.remark" />
          </el-form-item>
          <el-form-item label="审核状态：">
            <el-tag type="warning" v-if="dlgData.isAudit == 0">待审核</el-tag>
            <el-tag type="success" v-if="dlgData.isAudit == 1">审核通过</el-tag>
            <el-tag type="danger" v-if="dlgData.isAudit == 2">审核不通过</el-tag>
            <el-tag type="warning" v-if="dlgData.isAudit == 3">变更待审核</el-tag>
          </el-form-item>
        </el-form>
        <el-form label-width="130px">
          <el-form-item label="操作：">
            <el-button v-if="dlgData.isAudit == 3 || dlgData.isAudit == 0" type="primary" icon="el-icon-edit" @click="isShow = false; active = 0">修改</el-button>
            <el-button v-if="dlgData.isAudit == 2 || dlgData.isAudit == 1" type="primary" icon="el-icon-check" @click="isShow = false; active = 0">申请变更</el-button>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template v-else>
      <el-steps :active="active" align-center finish-status="success">
        <el-step title="基本信息"></el-step>
        <el-step title="扩展信息"></el-step>
        <el-step title="证件信息"></el-step>
        <el-step title="用户须知"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
      <el-form ref="dlgForm" class="dlg-form" :class="'form' + active" :rules="rules" label-width="130px" :model="dlgData" @submit.native.prevent>
        <template v-if="active == 0">
          <div class="title">公司基本信息</div>
          <el-form-item label="公司名称：" prop="name">
            <el-input v-model="dlgData.name" placeholder="请输入公司名称" />
          </el-form-item>
          <el-form-item label="公司地址：" prop="addressShengCode">
            <el-select v-model="dlgData.addressShengCode" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请选择省" @change="provinceChange" style="width:153px">
              <el-option v-for="item in provinceList" :key="item.id" :label="item.areaName" :value="item.areaCode">
              </el-option>
            </el-select>
            <el-select v-model="dlgData.addressShiCode" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请选择市" @change="cityChange" style="width:153px">
              <el-option v-for="item in cityList" :key="item.id" :label="item.areaName" :value="item.areaCode">
              </el-option>
            </el-select>
            <el-select v-model="dlgData.addressXianCode" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请选择县/区" style="width:153px">
              <el-option v-for="item in areaList" :key="item.id" :label="item.areaName" :value="item.areaCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="address">
            <el-input v-model="dlgData.address" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入公司地址" />
          </el-form-item>
          <el-form-item label="联系电话：" prop="phone">
            <el-input v-model="dlgData.phone" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入联系电话" />
          </el-form-item>
          <el-form-item label="公司业务：" prop="companyBusinessValue">
            <el-select v-model="dlgData.companyBusinessValue" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请选择公司业务">
              <el-option v-for="item in businessList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="附近建筑：" prop="nearBuilds">
            <el-input v-model="dlgData.nearBuilds" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入附近建筑" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" class="fr next-btn" @click="nextItem">下一步</el-button>
          </el-form-item>
        </template>
        <template v-else-if="active == 1">
          <div class="title">公司扩展信息</div>
          <el-form-item label="公司法人：" prop="legalPerson">
            <el-input v-model="dlgData.legalPerson" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入公司法人" />
          </el-form-item>
          <el-form-item label="注册资本(万元)：" prop="registeredCapital">
            <el-input-number v-model="dlgData.registeredCapital" :disabled="dlgData.isAudit && dlgData.isAudit != 2" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item label="成立日期：" prop="buildDate">
            <el-date-picker v-model="dlgData.buildDate" type="date" :disabled="dlgData.isAudit && dlgData.isAudit != 2" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="成立日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="登记机关：" prop="registrationAuthority">
            <el-input v-model="dlgData.registrationAuthority" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入登记机关" />
          </el-form-item>
          <el-form-item label="经营范围：" prop="scopeBusiness">
            <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" :disabled="dlgData.isAudit && dlgData.isAudit != 2" v-model="dlgData.scopeBusiness" placeholder="请输入经营范围" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" class="fr next-btn" @click="nextItem">下一步</el-button>
            <el-button class="fr" @click="prevItem">上一步</el-button>
          </el-form-item>
        </template>
        <template v-else-if="active == 2">
          <div class="title">营业执照信息</div>
          <el-form-item label="证件号码：" prop="certificateCode">
            <el-input v-model="dlgData.certificateCode" :disabled="dlgData.isAudit && dlgData.isAudit != 2" placeholder="请输入证件号码" />
          </el-form-item>
          <el-form-item label="有效期：" prop="validityDate">
            <el-date-picker v-model="dlgData.validityDate" :disabled="dlgData.isAudit && dlgData.isAudit != 2" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="有效期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="营业执照照片：" prop="certificateUrl">
            <el-image v-if="dlgData.isAudit == 1" :preview-src-list="[dlgData.certificateUrl]" class='upload-img' :src="dlgData.certificateUrl" alt="">
              <div class="el-image__error" slot="error">
                暂无照片
              </div>
            </el-image>
            <el-upload v-else class="avatar-uploader" action='' :show-file-list="false" :before-upload="beforeUpload">
              <el-image v-if="dlgData.certificateUrl" class='upload-img' :src="dlgData.certificateUrl" alt=""></el-image>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <i v-if="dlgData.certificateUrl" @click.stop='delUploadImg()' class="el-icon-error avatar_icon"></i>
            </el-upload>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" class="fr next-btn" @click="nextItem">下一步</el-button>
            <el-button class="fr" @click="prevItem">上一步</el-button>
          </el-form-item>
        </template>
        <template v-else-if="active == 3">
          <div class="title notice">用户须知</div>
          <p class="notice-content"></p>
          <el-form-item label-width="0">
            <el-checkbox v-model="dlgData.isChecked">我同意并遵守以上信息</el-checkbox>
            <el-button type="primary" class="fr next-btn" @click="subDlg">提 交</el-button>
            <el-button class="fr" @click="prevItem">上一步</el-button>
          </el-form-item>
        </template>
        <template v-else-if="active == 4">
          <img src="/static/image/finish.png" alt="">
          <div class="finish-tip">操作成功</div>
          <div class="audit-tip">审核中，请耐心等待</div>
          <el-button @click="isShow = true">点击查看</el-button>
        </template>
      </el-form>
    </template>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { syscompanyPage, syscompanyAddOrUpdate } from '@/api/enterpriseMan'
import { threeLinkage } from '@/api/communityMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import * as constConfig from '@/configs/const.js'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: 0,
  address: '',
  addressSheng: '',
  addressShengCode: '',
  addressShi: '',
  addressShiCode: '',
  addressXian: '',
  addressXianCode: '',
  buildDate: '',
  certificateCode: '',
  certificateUrl: '',
  companyBusiness: '',
  companyBusinessValue: '',
  legalPerson: '',
  name: '',
  nearBuilds: '',
  projectId: '',
  phone: '',
  registeredCapital: '',
  registrationAuthority: '',
  scopeBusiness: '',
  validityDate: '',
  isChecked: true
}


export default {
  name: 'enterpriseInfo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {
      isShow: false,
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题
      active: 0,
      rules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        addressShengCode: [{ required: true, message: '必填字段', trigger: 'change' }],
        addressShiCode: [{ required: true, message: '必填字段', trigger: 'change' }],
        addressXianCode: [{ required: true, message: '必填字段', trigger: 'change' }],
        address: [{ required: true, message: '必填字段', trigger: 'blur' }],
        companyBusinessValue: [{ required: true, message: '必填字段', trigger: 'change' }],
        nearBuilds: [{ required: true, message: '必填字段', trigger: 'blur' }],
        phone: [
          { required: true, message: '必填字段', trigger: 'blur' },
        ],
        certificateCode: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: constConfig.LICENSE_REG,
            message: '营业执照号码格式有误！',
            trigger: 'blur'
          }
        ],
        legalPerson: [{ required: true, message: '必填字段', trigger: 'blur' }],
        registeredCapital: [{ required: true, message: '必填字段', trigger: 'change' }],
        buildDate: [{ required: true, message: '必填字段', trigger: 'change' }],
        registrationAuthority: [{ required: true, message: '必填字段', trigger: 'blur' }],
        scopeBusiness: [{ required: true, message: '必填字段', trigger: 'blur' }],
        validityDate: [{ required: true, message: '必填字段', trigger: 'change' }],
        certificateUrl: [{ required: true, message: '必填字段', trigger: 'change' }],
      },

      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        logType: '1'
      },
      userInfo: {},

      //省市区
      provinceList: [],
      cityList: [],
      areaList: [],
      businessList: []
    }
  },

  computed: {

  },

  watch: {

  },

  created () {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.getList()
    this.getProvinceList()
    utils.getDataDictMap(this, 'companyBusiness', 'businessList', 0)
  },

  methods: {
    // 获取省数据
    getProvinceList () {
      threeLinkage(101).then(res => {
        if (res.data.code == 200) {
          this.provinceList = res.data.data
        }
      })
    },

    // 获取市数据
    getCityList (code) {
      if (utils.isNull(code)) {
        return
      }
      this.cityList = []
      threeLinkage(202, code).then(res => {
        if (res.data.code == 200) {
          this.cityList = res.data.data
        }
      })
    },

    // 获取县数据
    getAreaList (code) {
      if (utils.isNull(code)) {
        return
      }
      this.areaList = []
      threeLinkage(303, code).then(res => {
        if (res.data.code == 200) {
          this.areaList = res.data.data
        }
      })
    },

    // 省改变
    provinceChange () {
      this.getCityList(this.dlgData.addressShengCode)
      this.dlgData.addressShiCode = ""
      this.dlgData.addressXianCode = ""
      this.areaList = []
    },

    // 市改变
    cityChange () {
      this.getAreaList(this.dlgData.addressShiCode)
      this.dlgData.addressXianCode = ""
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      syscompanyPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          if (res.data.data.length > 0) {
            res.data.data[0].isChecked = true
            this.dlgData = JSON.parse(JSON.stringify(res.data.data[0]))
            this.getCityList(this.dlgData.addressShengCode)
            this.getAreaList(this.dlgData.addressShiCode)
          } else {
            this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
          }
          this.isShow = res.data.data.length > 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 弹窗提交
    subDlg () {
      if (!this.dlgData.isChecked) {
        this.$message.warning("请同意用户须知")
        return
      }
      this.$confirm('确认提交吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs['dlgForm'].validate((valid) => {
          if (valid) {
            let postParam = JSON.parse(JSON.stringify(this.dlgData))
            postParam.projectId = this.userInfo.projectId
            postParam.addressSheng = utils.getNameById(postParam.addressShengCode, this.provinceList, 'areaCode', 'areaName')
            postParam.addressShi = utils.getNameById(postParam.addressShiCode, this.cityList, 'areaCode', 'areaName')
            postParam.addressXian = utils.getNameById(postParam.addressXianCode, this.areaList, 'areaCode', 'areaName')
            postParam.companyBusiness = utils.getNameById(postParam.companyBusinessValue, this.businessList)
            this.dlgLoading = true
            syscompanyAddOrUpdate(postParam).then(res => {
              this.dlgLoading = false
              if (res.data.code == 200) {
                this.getList()
                this.dlgShow = false
                this.$message.success(res.data.msg)
                this.active++
              } else {
                this.$message.error(res.data.msg)
              }
            })
          }
        })
      }).catch(() => {

      });

    },

    // 上一步
    prevItem () {
      this.active--
      this.$refs.dlgForm.clearValidate()
    },

    // 下一步
    nextItem () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          this.active++
          this.$refs.dlgForm.clearValidate()
        }
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let suffix_list = ['image/jpg', 'image/jpeg', 'image/png', 'image/git', 'image/bmp']
      if (!suffix_list.includes(file.type)) {
        this.$message.error("不支持的文件类型，请上传图片类型的文件！");
        return false
      }
      let _this = this
      uploadImg(file, 'jianyitong/web/enterpriseInfo_').then(res => {
        _this.dlgData['certificateUrl'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['certificateUrl'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.company-info {
  color: #000000;
  font-size: 20px;
  font-weight: bold;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9e9e9;
}

.compant-form {
  width: 100%;
  height: calc(100% - 44px);
  overflow-y: auto;
  padding-top: 24px;
  padding-right: calc(100% - 600px);
  box-sizing: border-box;
  .upload-img {
    width: 100px;
    height: 100px;
  }
}

/deep/ .el-steps {
  margin-top: 70px;
  .el-step__head {
    .el-step__line {
      height: 1px;
      top: 16px;
      width: calc(100% - 120px);
      margin-left: 90px;
    }
    .el-step__icon {
      width: 32px;
      height: 32px;
      line-height: 32px;
    }
  }
  .el-step__head.is-process {
    .el-step__icon {
      background: #42b983;
      color: #ffffff;
      border: 1px solid #42b983;
    }
  }
  .el-step__main {
    position: absolute;
    left: 57%;
    top: 0;
    .el-step__title {
      line-height: 32px;
      font-size: 14px;
    }
  }
}

/deep/ .el-form-item__label {
  font-weight: normal;
}

/deep/ .el-input-number {
  width: 100%;
  input {
    text-align: left;
  }
}
/deep/ .el-date-editor {
  width: 100%;
}

/deep/ .dlg-form {
  width: 600px;
  height: calc(100% - 150px);
  margin: 44px auto 0;
  .el-button {
    width: 74px;
    height: 32px;
  }
  .next-btn {
    background: #42b983;
    border: 1px solid #42b983;
    margin-left: 10px;
    &:hover {
      opacity: 0.7;
    }
  }
  .title {
    color: #000000;
    font-size: 26px;
    text-align: center;
    margin-bottom: 24px;
  }
  .title.notice {
    text-align: left;
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #dcdcdc;
  }
  .notice-content {
    height: calc(100% - 150px);
    background: #f5f5f6;
    color: #323233;
    font-size: 12px;
    line-height: 1.6em;
    overflow-y: auto;
    padding: 24px;
  }
}

.dlg-form.form3 {
  width: 960px;
}

.dlg-form.form4 {
  img {
    display: block;
    margin: 0 auto;
    margin-bottom: 24px;
  }
  div {
    text-align: center;
    margin-bottom: 8px;
  }
  div.finish-tip {
    font-size: 24px;
    color: #000000;
  }
  div.audit-tip {
    color: #9a9a9a;
    margin-bottom: 24px;
  }
  .el-button {
    display: block;
    margin: 0 auto;
  }
}
</style>


