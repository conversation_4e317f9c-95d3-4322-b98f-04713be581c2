<template>
  <div class="app-container mazhenguo" style="margin-bottom: 32px; padding-top: 6px; padding-bottom: 10px">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="m-tabs">
      <el-tab-pane v-for="(item, index) of navList" :key="index" :label="item.label" :name="index + ''">
        <components v-if="activeName == index + ''" :is="testTemplate"></components>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// 工具
import * as utils from '@/utils'

import tab1 from './tab1'
import tab2 from './tab2'

export default {
  components: {
    tab1,
    tab2,
  },

  // props: {},
  data() {
    return {
      navList: [
        { id: '1', label: '送洗记录', template: tab1 },
        { id: '2', label: '分类汇总', template: tab2 },

        // { roleId: 'aaaa', roleText: '操作日志', template: OperationLog },
      ],
      activeName: '0',
      testTemplate: tab1,
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleClick(tab, event) {
      console.log('tab', tab)
      let index = parseInt(tab.index)
      this.activeName = tab.index + ''
      this.testTemplate = this.navList[index].template
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>


