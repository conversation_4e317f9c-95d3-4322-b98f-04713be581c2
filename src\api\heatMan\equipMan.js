import request from '@/utils/request'

// 分页 
export function page (data) {
  return request({
    url: `/nbiot/equipment/page`,
    method: 'post',
    data
  })
}

// 新增
export function saveOrUpdate (data) {
  return request({
    url: `/nbiot/equipment/saveOrU`,
    method: 'post',
    data
  })
}


// 删除
export function del (id) {
  return request({
    url: `/nbiot/equipment/del/${id}`,
    method: 'get'
  })
}

// 启用停用
export function updateStatus (id, status) {
  return request({
    url: `/nbiot/equipment/updateStatus/${id}/${status}`,
    method: 'get'
  })
}

// 批量发送设备上传间隔
export function send (data) {
  return request({
    url: `/nbiot/equipment/send`,
    method: 'post',
    data
  })
}