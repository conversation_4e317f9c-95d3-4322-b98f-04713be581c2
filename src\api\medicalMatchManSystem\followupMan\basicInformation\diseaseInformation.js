import request from '@/utils/request'
import { requestExcel } from '@/utils'

/*
*病种信息
*/

// 动态查询病种信息 
export function findSfDiseaseDynamic(data) 
{
	return request({
		url: `/follow/findSfDiseaseDynamic`,
		method: 'post',
		data
	})
}

// 修改病种信息状态（删除病种信息）
export function updateSfDisease(data)
{
	return request({
		url: `/follow/updateSfDisease`,
		method: 'post',
		data
	})
}

//新增/修改病种信息接口
export function saveOrUSfDisease(data)
{
	return request({
		url: `/follow/saveOrUSfDisease`,
		method: 'post',
		data
	})
}

// 导入
export function importExcelDisease(data)
{
	return requestExcel('/follow/importExcelDisease', data)
}




