<template>
  <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="dlgShow" :before-close="closeDlg">
    <div class="filter-container">
      <div class="fl">
        <el-input placeholder="输入部门名称进行过滤" v-model="filterBranch"></el-input>
      </div>
    </div>
    <el-tree
      ref="branchTreeMul"
      show-checkbox
      highlight-current
      node-key="id"
      @check="treeCheck"
      :check-strictly="true"
      :data="list"
      :default-expanded-keys="defaultOpenList"
      :filter-node-method="filterNode"
      @node-expand="handleNodeExpand"
      @node-collapse="handleNodeCollapse"
      :expand-on-click-node="false"
    >
    </el-tree>

    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-back" @click="closeDlg"> 取 消 </el-button>
      <el-button icon="el-icon-delete" type="danger" @click="clearDlg"> 清 空 </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'
import * as utils from '@/utils'

import { listBranchWhiteTreeByUserId,findTreeByFromAndProjectId } from '@/api/medicalMatchManSystem/medicalWasteMan/wasteBusinessMan.js'

export default {
  components: {},
  data() {
    return {
      filterBranch: '',

      list: [],

      selectKeys: [],

      selectList: [],

      defaultOpenList: [],

      selectBranchs: [],

      selectBranchIds: '',

      selectBranchNames: '',

      userInfo: {},
      title:"",
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.wasteMan.branchDlgMul.dlgShow
      },
      set: function (val) {
        this.$store.commit('wasteMan/branchDlgMul/SET_DLGSHOW', val)
      },
    },

    dlgType: {
      get: function () {
        return this.$store.state.wasteMan.branchDlgMul.dlgType
      },
      set: function (val) {
        this.$store.commit('wasteMan/branchDlgMul/SET_DLGTYPE', val)
      },
    },

    branchIds: {
      get: function () {
        return this.$store.state.wasteMan.branchDlgMul.branchIds
      },
      set: function (val) {
        this.$store.commit('wasteMan/branchDlgMul/SET_BRANCHIDS', val)
      },
    },

    branchNames: {
      get: function () {
        return this.$store.state.wasteMan.branchDlgMul.branchNames
      },
      set: function (val) {
        this.$store.commit('wasteMan/branchDlgMul/SET_BRANCHNAMES', val)
      },
    },
  },

  watch: {
    filterBranch(val) {
      this.$refs.branchTreeMul.filter(val)
    },

    dlgShow(val) {
      if (val) {
        this.selectBranchIds = this.branchIds
        this.selectBranchNames = this.branchNames
        this.getList()
      }
    },

    branchIds(val) {
      this.selectBranchIds = val
    },

    branchNames(val) {
      this.selectBranchNames = val
    },
  },

  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 树节点展开
    handleNodeExpand(data) {
      // 保存当前展开的节点
      let flag = false
      this.defaultOpenList.some((item) => {
        if (item === data.id) {
          // 判断当前节点是否存在， 存在不做处理
          flag = true
          return true
        }
      })
      if (!flag) {
        // 不存在则存到数组里
        this.defaultOpenList.push(data.id)
      }
    },

    // 树节点关闭
    handleNodeCollapse(data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i
        }
      })
    },

    // 递归全选当前下的节点
    selectAllNode(childrenList, type) {
      for (let item of childrenList) {
        // 全选，全部取消
        if (type == 'select') {
          if (!this.selectKeys.includes(item.id)) {
            this.selectKeys.push(item.id)
            this.selectList.push(item)
          }
        } else {
          if (this.selectKeys.includes(item.id)) {
            let mIndex = this.selectKeys.indexOf(item.id)

            this.selectKeys.splice(mIndex, 1)
            this.selectList.splice(mIndex, 1)
          }
        }
        if (item.children) {
          this.selectAllNode(item.children, type)
        }
      }
    },

    // 树节点点击
    treeCheck(checkedNodes, checkedKeys) {
      if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        // select-全选；remove-取消全选
        this.selectAllNode(checkedNodes.children, 'select')
      } else {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        this.selectAllNode(checkedNodes.children, 'remove')
      }
      this.$refs.branchTreeMul.setCheckedKeys(this.selectKeys)
    },

    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },

    setCheckedKeys() {
      this.selectBranchs = []
      if (utils.isNull(this.selectBranchIds)) {
        this.selectKeys = this.selectBranchs
        this.$refs.branchTreeMul.setCheckedKeys(this.selectBranchs)
        return
      }
      this.$nextTick(() => {
        let currentKey = []
        let selectBranchIdList = this.selectBranchIds.split(',')
        let selectBranchNameList = this.selectBranchNames.split(',')
        for (let i in selectBranchIdList) {
          currentKey.push(parseInt(selectBranchIdList[i]))
          this.selectBranchs.push({
            id: selectBranchIdList[i],
            branchName: selectBranchNameList[i],
          })
        }
        this.selectKeys = currentKey
        this.$refs.branchTreeMul.setCheckedKeys(currentKey)
      })
    },

    getList() {
      this.list = []
      let method
      let sendObj
      console.log(this.dlgType,'this.dlgType');
      if(this.dlgType === 'KESHI'){
        sendObj={
          projectId:this.userInfo.projectId
        }
        method=findTreeByFromAndProjectId
        this.title='选择科室'
      }else{
        method=listBranchWhiteTreeByUserId
        sendObj=this.userInfo.id
        this.title='选择暂存间'
      }
      method(sendObj).then((res) => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = res.data.list
          this.setCheckedKeys()
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      let checkNodes = this.$refs.branchTreeMul.getCheckedNodes()
      let halfCheckNodes = this.$refs.branchTreeMul.getHalfCheckedNodes()
      this.selectBranchs = checkNodes

      let branchIdList = []
      let branchNameList = []
      for (let i of this.selectBranchs) {
        branchIdList.push(i['id'])
        branchNameList.push(i['label'])
      }
      this.branchIds = branchIdList.join(',')
      this.branchNames = branchNameList.join(',')
      this.$store.commit('wasteMan/branchDlgMul/SET_BRANCHIDS', this.branchIds)
      this.$store.commit('wasteMan/branchDlgMul/SET_BRANCHNAMES', this.branchNames)
      this.closeDlg()
    },

    clearDlg() {
      this.selectBranchIds = ''
      this.selectBranchNames = ''
      this.setCheckedKeys()
    },

    closeDlg() {
      this.$store.commit('wasteMan/branchDlgMul/SET_DLGTYPE', '')
      this.$store.commit('wasteMan/branchDlgMul/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 50px);
  overflow-y: auto;
}
.filter-container {
  .fl {
    width: 100%;
  }
}
</style>