<template>
  <el-dialog :close-on-click-modal="false" :title="'选择岗位'" :visible.sync="dlgShow" top="5vh" append-to-body>
    <div class="filter-container">
      <div class="fr">
        <el-input v-model="listQuery.label" placeholder="请输入岗位/在岗员工" @keyup.enter.native="searchItem">
          <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
        </el-input>
        <el-button icon="el-icon-search" type="success" size="mini" @click="searchItem"> 搜索 </el-button>
      </div>
    </div>
   
      <el-table
        height="380"
        class="m-small-table"
        ref="multipleTable"
        :data="list"
        :row-key="getRowKeys"
        @selection-change="selectionChange"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="#" align="center" type="selection" :reserve-selection="true" width="50">
        </el-table-column>
        <el-table-column label="岗位名称">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属部门">
          <template slot-scope="scope">
            <span>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="在岗员工" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="岗位类型" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.postCategoryText }}</span>
          </template>
        </el-table-column>
      </el-table>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back"> 取 消 </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'

import {
  pagePostUser, // 获取岗位
} from '@/api/postMan' // 查岗位

export default {
  components: {
    Pagination,
  },
  data() {
    return {
      list: [],

      listQuery: {
        size: 10,
        label: '',
        page: 1,
        postStatus: ''
      },

      total: 0,

      selectList: [],
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.propertyMan.postDlgMul.dlgShow
      },
      set: function (val) {
        this.$store.commit('propertyMan/postDlgMul/SET_DLGSHOW', val)
      },
    },

    postList: {
      get: function () {
        return this.$store.state.propertyMan.postDlgMul.list
      },
      set: function (val) {
        this.$store.commit('propertyMan/postDlgMul/SET_LIST', val)
      },
    },
  },

  watch: {
    dlgShow(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.multipleTable.clearSelection()
          this.listQuery.label = ''
          this.getList()
        })
      }
    },

    postList(val) {
      this.selectList = JSON.parse(JSON.stringify(val))
    },
  },

  methods: {
    getRowKeys(row) {
      return row.postId
    },

    resetStr() {
      this.listQuery.label = ''
      this.getList()
    },

    searchItem() {
      this.getList()
    },

    selectionChange(val) {
      this.selectList = JSON.parse(JSON.stringify(val))
    },

    // 设置选中行
    toggleRowSelection() {
      let idList = []
      for (let i of this.selectList) {
        if (!idList.includes(i['postId'])) {
          idList.push(i['postId'])
        }
      }
      if (idList.length == 0) {
        this.$refs.multipleTable.clearSelection()
      } else {
        this.$nextTick(() => {
          this.list.forEach((item) => {
            for (let i in idList) {
              if (idList[i] == item.postId) {
                this.$refs.multipleTable.toggleRowSelection(item, true)
              }
            }
          })
        })
      }
    },

    getList() {
      this.list = []
      pagePostUser(this.listQuery).then((res) => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let data = res.data.data
          if (utils.isNull(data)) {
            this.total = 0
            return
          }
          console.log(res);
          let list = res.data.data
          this.list = list
          this.total = res.data.page.total
          if (this.selectList.length > 0) {
            this.toggleRowSelection()
          }
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg() {
      this.postList = JSON.parse(JSON.stringify(this.selectList))
      this.$store.commit('propertyMan/postDlgMul/SET_LIST', this.postList)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('propertyMan/postDlgMul/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>