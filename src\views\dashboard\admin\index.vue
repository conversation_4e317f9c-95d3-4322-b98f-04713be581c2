<template>
  <div class="dashboard-wrap">
    <div
      class="dashboard-container clearfix"
      v-if="userRoles.includes('shouye')"
    >
      <div class="left-container clearfix">
        <div class="dashboard-item">
          <div class="title">考勤信息</div>
          <div class="content" id="echart-attendance"></div>
        </div>
        <div class="dashboard-item">
          <div class="title">运送信息</div>
          <div class="content" id="echart-tran"></div>
        </div>
        <div class="dashboard-item">
          <div class="title">被服信息</div>
          <div class="content" id="echart-cloth"></div>
        </div>
        <div class="dashboard-item">
          <div class="title">医废信息</div>
          <div class="content" id="echart-waste"></div>
        </div>
        <div class="dashboard-item half">
          <div class="title">随访信息</div>
          <div class="content clearfix">
            <div class="content-item">
              <span class="content-tips"> 待随访 </span>
              <span class="content-num"> 321 </span>
            </div>
            <div class="content-item">
              <span class="content-tips"> 全部 </span>
              <span class="content-num"> 1365 </span>
            </div>
            <div class="content-item">
              <span class="content-tips"> 已随访 </span>
              <span class="content-num"> 1044 </span>
            </div>
          </div>
        </div>
        <div class="dashboard-item half">
          <div class="title">报事品质</div>
          <div class="content">
            <div class="content-item">
              <span class="content-tips"> 待处理 </span>
              <span class="content-num"> 23 </span>
            </div>
            <div class="content-item">
              <span class="content-tips"> 待整改 </span>
              <span class="content-num"> 0 </span>
            </div>
            <div class="content-item">
              <span class="content-tips"> 累计报事 </span>
              <span class="content-num"> 1322 </span>
            </div>
            <div class="content-item">
              <span class="content-tips"> 已处理 </span>
              <span class="content-num"> 1299 </span>
            </div>
          </div>
        </div>
      </div>
      <div class="right-container">
        <div class="dashboard-item">
          <div class="title">设备能耗信息</div>
          <div class="content">
            <div class="content-item">
              <span class="content-tips"> 设备总数 </span>
              <span class="content-num"> 50 </span>
            </div>
            <div class="content-item">
              <span class="content-tips"> 在线 </span>
              <span class="content-num"> 42 </span>
            </div>
            <div class="content-item">
              <span class="content-tips"> 累计报警 </span>
              <span class="content-num">
                366
                <b>次</b>
              </span>
            </div>
            <div class="content-item">
              <span class="content-tips"> 用电 </span>
              <span class="content-num">
                4854.63
                <b>kwh</b>
              </span>
            </div>
            <div class="content-item">
              <span class="content-tips"> 用水 </span>
              <span class="content-num">
                4468.20
                <b>t</b>
              </span>
            </div>
            <div class="content-item"></div>
          </div>
        </div>
        <div class="dashboard-item notice">
          <div class="title">通知公告</div>
          <el-tabs v-model="activeName">
            <el-tab-pane label="消息" name="first"></el-tab-pane>
            <el-tab-pane label="通知" name="second"></el-tab-pane>
            <el-tab-pane label="公告" name="third"></el-tab-pane>
          </el-tabs>
          <ul class="notice-list">
            <li>
              <span class="notice-num"> 1 </span>
              <span class="notice-content">您有一条报事单,请及时查看</span>
              <span class="notice-time">2020-05-31</span>
            </li>
            <li>
              <span class="notice-num"> 2 </span>
              <span class="notice-content"
                >您有一条待随访的记录,请及时查看</span
              >
              <span class="notice-time">2020-05-22</span>
            </li>
            <li>
              <span class="notice-num"> 3 </span>
              <span class="notice-content">五一假日放假时间安排</span>
              <span class="notice-time">2020-04-28</span>
            </li>
            <li>
              <span class="notice-num"> 4 </span>
              <span class="notice-content">清明节放假时间安排</span>
              <span class="notice-time">2020-03-30</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div
      v-else-if="projectInfo && projectInfo.isCustomIndex == 1"
      class="welcome-box"
      style=""
    >
      <img class="welcome-img" :src="projectInfo.indexImgUrl" style="" />
      <div class="welcome-str" style="">
        {{ projectInfo.indexProjectName }}
      </div>
    </div>

    <div v-else-if="projectInfo" class="welcome-box" style="">
      <img class="welcome-img" src="/static/image/index.jpg" style="" />
      <div class="welcome-str" v-if="userInfo && userInfo.projectName" style="">
        {{ userInfo.projectName }}
      </div>
    </div>
  </div>
</template>

<script>
import * as utils from "@/utils";
import * as echarts from "echarts";
import { postAction } from "@/api";

export default {
  components: {},
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),
      projectInfo: '',

      activeName: "first",

      echartAttendance: null,

      echartTran: null,

      echartCloth: null,

      echartWaste: null,

      userRoles: []
    };
  },
  created() {
    this.userRoles = JSON.parse(decodeURI(window.localStorage.userRoles));

    this.getProjectInfo();
  },
  mounted() {
    if (this.userRoles.includes("shouye")) {
      this.createAttendance();
      this.createTran();
      this.createCloth();
      this.createWaste();
      window.addEventListener("resize", () => {
        if (!utils.isNull(this.echartAttendance)) {
          this.echartAttendance.resize();
        }
        if (!utils.isNull(this.echartTran)) {
          this.echartTran.resize();
        }
        if (!utils.isNull(this.echartCloth)) {
          this.echartCloth.resize();
        }
        if (!utils.isNull(this.echartWaste)) {
          this.echartWaste.resize();
        }
      });
    }
  },
  methods: {
    getProjectInfo() {
      postAction("/sys/queryProjectById", {
        id: this.userInfo.projectId
      }).then(res0 => {
        // console.log("6666", JSON.stringify(res0.data.data));
        // window.localStorage.userProjectInfo = JSON.stringify(res0.data.data);
        // res0.data.data.isCustomIndex = 0
        // res0.data.data.indexProjectName = '显示的文字'
        // res0.data.data.indexImgUrl = 'https://wlines.oss-cn-beijing.aliyuncs.com/ERP_web/uploadFile/customIndex/2024/11/20/1732073711807-7c8146b7.jpg'
        this.projectInfo = res0.data.data;
      });
    },
    ///////////
    createAttendance() {
      // 基于准备好的dom，初始化echarts实例
      this.echartAttendance = echarts.init(
        document.getElementById("echart-attendance")
      );
      // 指定图表的配置项和数据
      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        color: ["#5B8FF9"],
        xAxis: {
          type: "category",
          data: ["应出勤", "实际出勤", "迟到", "请假"]
        },
        yAxis: {
          type: "value"
        },
        series: [
          {
            data: [120, 200, 150, 80],
            barWidth: 30,
            type: "bar"
          }
        ]
      };
      this.echartAttendance.setOption(option);
    },

    createTran() {
      // 基于准备好的dom，初始化echarts实例
      this.echartTran = echarts.init(document.getElementById("echart-tran"));
      // 指定图表的配置项和数据
      let option = {
        color: [
          "#5B8FF9",
          "#13C2C2",
          "#2FC25B",
          "#FACC14",
          "#E86452",
          "#8543E0"
        ],
        tooltip: {
          trigger: "item"
        },
        legend: {
          orient: "vertical",
          right: "10%",
          top: "center"
        },
        series: [
          {
            name: "",
            type: "pie",
            center: ["40%", "50%"],
            radius: ["55%", "70%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2
            },
            label: {
              show: false,
              position: "center"
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "12"
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 1048, name: "标本运送" },
              { value: 735, name: "文件运送" },
              { value: 580, name: "药品运送" },
              { value: 484, name: "物品运送" },
              { value: 300, name: "病人运送" },
              { value: 500, name: "其他" }
            ]
          }
        ]
      };

      this.echartTran.setOption(option);
    },

    createCloth() {
      // 基于准备好的dom，初始化echarts实例
      this.echartCloth = echarts.init(document.getElementById("echart-cloth"));
      // 指定图表的配置项和数据
      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        color: ["#5AD8A6"],
        xAxis: {
          type: "category",
          data: ["被服数量", "报损数量", "丢失数量", "库存数量"]
        },
        yAxis: {
          type: "value"
        },
        series: [
          {
            data: [120, 200, 150, 80],
            barWidth: 30,
            type: "bar"
          }
        ]
      };
      this.echartCloth.setOption(option);
    },

    createWaste() {
      // 基于准备好的dom，初始化echarts实例
      this.echartWaste = echarts.init(document.getElementById("echart-waste"));
      // 指定图表的配置项和数据
      let option = {
        color: ["#6495FA", "#6BDCAF", "#6D7E9D", "#F8C42D", "#E86452"],
        tooltip: {
          trigger: "item"
        },
        legend: {
          orient: "vertical",
          right: "10%",
          top: "center"
        },
        series: [
          {
            name: "",
            type: "pie",
            center: ["40%", "50%"],
            label: {
              //饼图图形上的文本标签
              normal: {
                show: true,
                position: "inner", //标签的位置
                textStyle: {
                  fontSize: 12 //文字的字体大小
                },
                formatter: "{d}%"
              }
            },
            data: [
              { value: 1048, name: "感染性" },
              { value: 735, name: "损伤性" },
              { value: 580, name: "药物性" },
              { value: 484, name: "病理性" },
              { value: 300, name: "化学性" }
            ]
          }
        ]
      };

      this.echartWaste.setOption(option);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.welcome-box {
  width: 100%;
  height: 100%;
  position: relative;
  .welcome-img {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 0;
  }
  .welcome-str {
    font-size: 44px;
    color: #fff;
    text-align: center;
    position: relative;
    z-index: 10;
    width: 100%;
    text-align: center;
    line-height: 100px;
    top: calc(50% - 80px);
    letter-spacing: 10px;
  }
}

.dashboard-wrap {
  width: 100%;
  height: 100%;
  margin: 0;
  background: #f5f5f5;
  overflow-y: auto;
  img.common-bg {
    width: 100%;
    height: 100%;
  }
  /deep/ .dashboard-container {
    .left-container {
      float: left;
      width: calc(67% - 16px);
      .dashboard-item:nth-child(even) {
        margin-right: 0;
      }
    }
    .right-container {
      float: right;
      width: calc(33% - 8px);
      .dashboard-item {
        width: 100%;
        height: 240px;
        .content {
          padding: 24px 46px 0;
          display: flex;
          flex-wrap: wrap;
          .content-item {
            width: 33%;
            margin-right: 0;
            height: 50%;
          }
        }
      }
      .dashboard-item.notice {
        height: 688px;
        .notice-list {
          height: calc(100% - 108px);
          overflow-y: auto;
          margin: 0;
          padding: 0 40px;
          li {
            list-style: none;
            height: 30px;
            line-height: 30px;
            span {
              display: inline-block;
            }
            .notice-num {
              width: 24px;
            }
            .notice-time {
              width: 120px;
              color: #999;
            }
            .notice-content {
              width: calc(100% - 166px);
              &:hover {
                color: #1890ff;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
    .dashboard-item {
      float: left;
      width: calc(50% - 12px);
      height: 360px;
      margin-right: 24px;
      margin-bottom: 24px;
      padding: 0;
      background: #ffffff;
      border-radius: 2px;
      .title {
        height: 54px;
        line-height: 54px;
        font-size: 16px;
        padding: 0 24px;
        color: #000;
        font-weight: bold;
        border-bottom: 1px solid #e8e8e8;
      }
      .content {
        padding: 36px 46px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        .content-item {
          width: calc(33% - 7.5%);
          margin-right: 10%;
          height: 100%;
          span {
            display: block;
            &.content-tips {
              color: #999;
              font-size: 14px;
              margin-bottom: 8px;
            }
            &.content-num {
              font-size: 24px;
              b {
                color: #999;
                font-size: 12px;
                font-weight: normal;
              }
            }
          }
        }
        .content-item:last-child {
          margin-right: 0;
        }
      }
    }
    .content {
      height: calc(100% - 56px);
    }
    .dashboard-item.half {
      height: 180px;
    }
    .el-tabs__nav-scroll {
      padding: 0 24px;
    }
    .el-tabs__item.is-active {
      color: #409eff !important;
    }
    .el-tabs__active-bar {
      background-color: #409eff !important;
    }
    .el-tabs__item:hover {
      color: #409eff !important;
    }
  }
}
</style>
