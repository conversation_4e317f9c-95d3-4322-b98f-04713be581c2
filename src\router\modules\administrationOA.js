/** 行政OA **/

import Layout from "@/views/layout/Layout";

const administrationOARouter = {
  path: "/administrationOA",
  component: Layout,
  name: "administrationOA",
  meta: {
    title: "行政OA",
    icon: "pzpj",
    roles: ["xingzhengOA"]
  },
  children: [
    {
      path: "fromsApp",
      component: () => import("@/views/administrationOA/workspace/oa/FromsApp"),
      name: "单据提报",
      meta: {
        title: "单据提报",
        roles: ["xingzhengOA"]
      },
      children: []
    },
    {
      path: "unFinished",
      component: () =>
        import("@/views/administrationOA/workspace/oa/UnFinished.vue"),
      name: "待我处理",
      meta: {
        title: "待我处理",
        roles: ["daiwochuli_oa"]
      },
      children: []
    },
    {
      path: "finished",
      component: () =>
        import("@/views/administrationOA/workspace/oa/Finished.vue"),
      name: "已处理的",
      meta: {
        title: "已处理的",
        roles: ["yichulide_oa"]
      },
      children: []
    },
    {
      path: "mySubmit",
      component: () =>
        import("@/views/administrationOA/workspace/oa/MySubmit.vue"),
      name: "我发起的",
      meta: {
        title: "我发起的",
        roles: ["wofaqide_oa"]
      },
      children: []
    },
    {
      path: "CcMe",
      component: () => import("@/views/administrationOA/workspace/oa/CcMe.vue"),
      name: "关于我的",
      meta: {
        title: "关于我的",
        roles: ["guanyuwode_oa"]
      },
      children: []
    },

    {
      path: "processInstanceManage",
      component: () =>
        import("@/views/administrationOA/admin/ProcessInstanceManage.vue"),
      name: "数据管理",
      meta: {
        title: "数据管理",
        roles: ["shujuguanli_oa"]
      },
      children: []
    },

    {
      path: "formsPanel",
      component: () => import("@/views/administrationOA/admin/FormsPanel"),
      name: "流程管理",
      meta: {
        title: "流程管理",
        roles: ["liuchengguanli_oa"]
      },
      children: []
    },

    {
      path: "formProcessDesign",
      component: () =>
        import("@/views/administrationOA/admin/FormProcessDesign"),
      name: "流程设置",
      meta: {
        title: "流程设置"
      },
      hidden: true,
      children: []
    },
    // ======= app

    // 单据详情
    {
      path: "AppFormAuth",
      component: () =>
        import("@/views/administrationOA/workspace/approval/AppFormAuth"),
      name: "单据详情",
      meta: {
        title: "单据详情"
      },
      hidden: true,
      children: []
    },

    {
      path: "mbInitiateProcess",
      component: () =>
        import("@/views/administrationOA/workspace/MbInitiateProcess"),
      name: "发起审批",
      meta: {
        title: "发起审批"
      },
      hidden: true,
      children: []
    }

    // //////////////////
  ]
};

export default administrationOARouter;
