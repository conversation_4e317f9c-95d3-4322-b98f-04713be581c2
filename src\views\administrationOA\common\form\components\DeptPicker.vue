<template>
  <div>
    <div v-if="mode === 'DESIGN'">
      <template v-if="expansion">
        <el-checkbox-group disabled v-if="multiple" v-model="_value">
          <el-checkbox
            class="w-row-text"
            disabled
            v-for="(op, index) in options"
            :key="index"
            :label="op"
            >{{ op.name }}</el-checkbox
          >
        </el-checkbox-group>
        <el-radio-group disabled v-model="_value" v-else>
          <el-radio
            class="w-row-text"
            style="margin: 5px"
            v-for="(op, index) in options"
            :key="index"
            :label="op"
            >{{ op.name }}</el-radio
          >
        </el-radio-group>
      </template>
      <template v-else>
        <el-button
          disabled
          icon="iconfont icon-map-site"
          type="primary"
          size="mini"
          round
        >
          选择部门</el-button
        >
        <span class="placeholder"> {{ placeholder }}</span>
      </template>
    </div>
    <div v-else-if="mode === 'PC' && !readonly">
      <template v-if="expansion">
        <el-checkbox-group v-if="multiple" v-model="_value">
          <el-checkbox
            class="w-row-text"
            v-for="(op, index) in options"
            :key="index"
            :label="op"
            >{{ op.name }}</el-checkbox
          >
        </el-checkbox-group>
        <el-radio-group
          :value="_value[0]"
          @input="ev => (_value = [ev])"
          v-else
        >
          <el-radio
            class="w-row-text"
            style="margin: 5px"
            v-for="(op, index) in options"
            :key="index"
            :label="op"
            >{{ op.name }}</el-radio
          >
        </el-radio-group>
      </template>
      <template v-else>
        <!-- $refs.orgPicker.show() showSbmDlg -->
        <el-button
          icon="iconfont icon-map-site"
          type="primary"
          size="mini"
          round
          @click="showSbmDlg"
        >
          选择部门</el-button
        >

        <span class="placeholder"> {{ placeholder }}</span>
        <div style="margin-top: 5px">
          <el-tag
            size="mini"
            style="margin: 5px"
            closable
            v-for="(dept, i) in _value"
            :key="i"
            @close="delDept(i)"
            >{{ dept.name }}</el-tag
          >
        </div>

        <selectBmDlgMul
          v-if="multiple"
          :dlgState0="dlgBmMulState"
          :dlgType="dlgBmMulType"
          :dlgQuery="dlgBmMulQuery"
          :dlgSelectData="dlgBmMulSelectData"
          :isRole="false"
          title="部门"
          @closeDlg="closeBmMulDlg"
          @backFunc="dlgBmMulbackFunc"
        />

        <selectBmDlg
          v-else
          :dlgState0="dlgSbmState"
          :dlgType="dlgSbmType"
          :dlgQuery="dlgSbmQuery"
          :dlgSelectData="dlgSbmSelectData"
          :isRole="false"
          @closeDlg="closeSbmDlg"
          @backFunc="dlgSbmbackFunc"
        />
        <!-- <org-picker
          type="dept"
          :multiple="multiple"
          ref="orgPicker"
          :selected="_value"
          @ok="selected"
        /> -->
      </template>
    </div>
    <div v-else-if="mode === 'MOBILE' && !readonly">
      <template v-if="expansion">
        <checkbox-group v-if="multiple" v-model="_value" direction="horizontal">
          <checkbox
            style="margin: 5px"
            :name="op"
            shape="square"
            v-for="(op, index) in options"
            :key="index"
            >{{ op.name }}</checkbox
          >
        </checkbox-group>
        <radio-group
          v-else
          :value="_value[0]"
          @input="ev => (_value = [ev])"
          direction="horizontal"
        >
          <radio
            style="margin: 5px"
            v-for="(op, index) in options"
            :key="index"
            :name="op"
            >{{ op.name }}</radio
          >
        </radio-group>
      </template>
      <template v-else>
        <field
          readonly
          clearable
          @clear="_value = []"
          right-icon="arrow"
          clickable
          v-model="deptDesc"
          :placeholder="placeholder"
          @click="showSbmDlg"
        ></field>

        <selectBmDlgMul
          v-if="multiple"
          deviceType="MOBILE"
          :dlgState0="dlgBmMulState"
          :dlgType="dlgBmMulType"
          :dlgQuery="dlgBmMulQuery"
          :dlgSelectData="dlgBmMulSelectData"
          :isRole="false"
          title="部门"
          @closeDlg="closeBmMulDlg"
          @backFunc="dlgBmMulbackFunc"
        />
        <selectBmDlg
          v-else
          deviceType="MOBILE"
          :dlgState0="dlgSbmState"
          :dlgType="dlgSbmType"
          :dlgQuery="dlgSbmQuery"
          :dlgSelectData="dlgSbmSelectData"
          :isRole="false"
          @closeDlg="closeSbmDlg"
          @backFunc="dlgSbmbackFunc"
        />

        <!-- <org-picker
          :pc-mode="false"
          type="dept"
          :multiple="multiple"
          ref="orgPicker"
          :selected="_value"
          @ok="selected"
        /> -->
      </template>
    </div>
    <div v-else>
      {{ String(_value.map(v => v.name)) }}
    </div>
  </div>
</template>

<script>
import { Field, Radio, RadioGroup, Checkbox, CheckboxGroup } from "vant";
import componentMinxins from "../ComponentMinxins";
import OrgPicker from "@/components/workFlow/common/OrgPicker";
import selectBmDlg from "@/components/DialogWflow/selectBmDlg";
import selectBmDlgMul from "@/components/DialogWflow/selectBmDlgMul";

export default {
  mixins: [componentMinxins],
  name: "DeptPicker",
  components: {
    OrgPicker,
    selectBmDlg,
    selectBmDlgMul,
    Field,
    Radio,
    RadioGroup,
    Checkbox,
    CheckboxGroup
  },
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      }
    },
    placeholder: {
      type: String,
      default: "请选择部门"
    },
    multiple: {
      type: Boolean,
      default: false
    },
    expansion: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      showOrgSelect: false,

      dlgSbmQuery: {},
      dlgSbmState: false,
      dlgSbmType: "", // 弹框状态add, edit
      dlgSbmSelectData: { id: "", label: "" },

      dlgBmMulQuery: {},
      dlgBmMulState: false,
      dlgBmMulType: "", // 弹框状态add, edit
      dlgBmMulSelectData: ""
    };
  },
  computed: {
    deptDesc: {
      get() {
        if (this._value && this._value.length > 3) {
          return `${String(this._value.slice(0, 3).map(v => v.name))}... 等${
            this._value.length
          }个部门`;
        } else if (this._value && this._value.length > 0) {
          return String(this._value.map(v => v.name));
        } else {
          return null;
        }
      },
      set(val) {}
    }
  },
  methods: {
    // === 多选部门
    showBmMulDlg() {
      let selectList = JSON.parse(JSON.stringify(this._value));

      let list = [];
      selectList.forEach(item => {
        list.push({ id: item.id, label: item.name });
      });
      console.log("===list", list);
      this.dlgBmMulSelectData = list.length ? list : "";
      this.dlgBmMulState = true;
    },
    closeBmMulDlg() {
      this.dlgBmMulState = false;
    },
    dlgBmMulbackFunc(list0) {
      console.log("弹窗发挥", list0);

      let select = [];

      for (let i = 0; i < list0.length; i++) {
        let obj = {
          type: "dept",
          id: list0[i].id,
          name: list0[i].label,

          isLeader: null,
          avatar: "",
          sex: null,
          selected: null
        };

        select.push(obj);
      }

      this._value = select;
    },

    // === 单选部门
    showSbmDlg() {
      console.log("===this.multiple", this.multiple);
      if (this.multiple) {
        this.showBmMulDlg();
        return false;
      }

      let list = JSON.parse(JSON.stringify(this._value));
      if (list && list.length) {
        this.dlgSbmSelectData = {
          id: list[0].id,
          label: list[0].name
        };
      } else {
        this.dlgSbmSelectData = { id: "", label: "" };
      }
      this.dlgSbmState = true;
    },
    // 关闭弹窗
    closeSbmDlg() {
      this.dlgSbmState = false;
    },
    dlgSbmbackFunc(list0) {
      console.log("返回数据", list0);

      if (Object.prototype.toString.apply(list0).indexOf("Object") >= 0) {
        list0 = [list0];
      }

      let select = [];
      for (let item of list0) {
        let obj = {
          type: "dept",
          id: item.id,
          name: item.label,

          isLeader: null,
          avatar: "",
          sex: null,
          selected: null
        };

        select.push(obj);
      }

      this.showOrgSelect = false;
      this._value = select;
    },

    /////////
    selected(values) {
      console.log("===values", values);
      this.showOrgSelect = false;
      this._value = values;
    },
    delDept(i) {
      this._value.splice(i, 1);
    }
  }
};
</script>

<style lang="scss" scoped>
.placeholder {
  margin-left: 10px;
  color: #adabab;
  font-size: smaller;
}

/deep/ .el-checkbox-group {
  line-height: 10px;
}
</style>
