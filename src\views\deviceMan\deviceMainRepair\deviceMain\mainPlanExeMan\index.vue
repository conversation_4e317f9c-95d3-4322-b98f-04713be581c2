<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">执行状态：</div>
            <el-select
              class="fl"
              style="width: 120px"
              v-model="listQuery.invokeStatus"
              placeholder="执行状态"
              @change="searchFunc"
              filterable
              clearable
            >
              <el-option
                v-for="item of axztSelect"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-item">
            <div class="search-item-label lh28">计划日期：</div>
            <el-date-picker
              v-model="timeRange"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 220px"
              @change="handleTimeRangeChange"
            >
            </el-date-picker>
          </div>
          <div class="search-item">
            <div class="search-item-label lh28">是否超时：</div>
            <el-select
              v-model="listQuery.timeOut"
              placeholder="是否超时"
              style="width: 120px"
              clearable
            >
              <el-option label="未超时" value="0"></el-option>
              <el-option label="超时" value="1"></el-option>
            </el-select>
          </div>
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input
              class="fl"
              style="width: 160px"
              v-model="listQuery.equName"
              @keyup.enter.native="searchFunc"
              placeholder="关键字"
              clearable
            >
            </el-input>

            <el-button
              class="fl ml10"
              @click="searchFunc"
              icon="el-icon-search"
              type="success"
              >查询</el-button
            >
            <el-button
              icon="el-icon-download"
              type="primary"
              size="mini"
              @click="exportExcel"
              >导出</el-button
            >
            <el-popover
              placement="bottom"
              width="800"
              trigger="click"
              v-model="popShow"
            >
              <div class="m-page-con">
                <el-table
                  height="400px"
                  class="m-small-table"
                  border
                  fit
                  highlight-current-row
                  :data="exportList"
                >
                  <el-table-column
                    label="#"
                    type="index"
                    align="center"
                    width="60"
                  >
                  </el-table-column>
                  <el-table-column label="备注" align="center" width="auto">
                    <template slot-scope="scope">
                      <span>{{ scope.row.remark }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="创建时间" align="center" width="auto">
                    <template slot-scope="scope">
                      <span>{{ scope.row.createTime }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    width="110"
                    align="center"
                    class-name="small-padding fixed-width"
                  >
                    <template slot-scope="scope">
                      <el-button
                        v-if="scope.row.fileUrl"
                        @click="downloadItem(scope.row.fileUrl)"
                        type="primary"
                        size="mini"
                        icon="el-icon-download"
                        plain
                      >
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="page-container">
                <pagination
                  :total="totalExport"
                  :page.sync="listQueryExport.pageNo"
                  :limit.sync="listQueryExport.pageSize"
                  @pagination="getExportList"
                />
              </div>
              <el-button
                style="margin-left: 9px"
                slot="reference"
                icon="el-icon-document"
                type="primary"
                size="mini"
                @click.stop="viewItem"
                >查看导出</el-button
              >
            </el-popover>
          </div>
        </div>
      </div>
    </div>

    <el-table
      height="calc(100vh - 290px)"
      ref="tableRef"
      class="m-small-table"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      :row-class-name="tableRowClassName"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (listQuery.pageNo - 1) * listQuery.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column
        label="设备编码"
        align="center"
        prop="equCode"
        width="auto"
      >
      </el-table-column>
      <el-table-column
        label="计划名称"
        align="center"
        prop="maintenanceName"
        show-overflow-tooltip
      />
      <el-table-column
        label="设备名称"
        align="center"
        prop="equName"
        show-overflow-tooltip
      />
      <el-table-column
        label="设备型号"
        align="center"
        prop="equModel"
        show-overflow-tooltip
      />
      <el-table-column
        label="设备位置"
        align="center"
        prop="equPosition"
        show-overflow-tooltip
      />
      <el-table-column
        label="计划保养日期"
        align="center"
        prop="maintenanceDate"
        width="110"
      />
      <el-table-column
        label="保养事项"
        align="center"
        prop="maintenanceItem"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div v-if="scope.row.maintenanceItem.length == 0"></div>
          <div v-else>
            <span
              v-for="(item, index) of scope.row.maintenanceItem"
              :key="index"
            >
              <span>{{ item.name }}</span>
              <span v-if="index != scope.row.maintenanceItem.length - 1"
                >,</span
              >
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="执行时间"
        align="center"
        prop="invokeTime"
        width="160"
      >
      </el-table-column>
      <el-table-column label="执行情况" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.invokeStatusStr }}</span>
          <!-- <span v-if="scope.row.invokeStatus == '1'"
            >（{{ scope.row.invokeStatusInfo }}）</span
          > -->
        </template>
      </el-table-column>

      <!-- <el-table-column label="更换配件" align="center" prop="accessoryJson" /> -->
      <el-table-column
        label="执行人"
        align="center"
        prop="invokeUserName"
        show-overflow-tooltip
      /><el-table-column
        label="备注"
        align="center"
        prop="info"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="保养周期"
        align="center"
        prop="maintenanceCycleStr"
        show-overflow-tooltip
      />
      <el-table-column label="是否超时" align="center" prop="isTimeout">
      </el-table-column>
      <el-table-column label="操作" width="180" align="left">
        <template slot-scope="scope">
          <el-button
            v-if="
              scope.row.invokeStatus == '0' &&
              userRoles.includes('equ_shebeibaoyangchongpai')
            "
            @click="showDlg('reDispatch', scope.row)"
            icon="el-icon-edit"
            size="mini"
            type="primary"
            title="重派"
            plain
            >重派</el-button
          >
          <el-button
            @click="showDlg('info', scope.row)"
            icon="el-icon-document"
            size="mini"
            type="success"
            title="详情"
            plain
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      class="mt10"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <div class="clear"></div>
    <!-- 弹窗 新增/编辑 -->
    <addDlg
      ref="addDlg"
      :dlgData0="dlgData"
      :dlgType="dlgType"
      :dlgQuery="dlgQuery"
      @closeDlg="closeDlg"
      @getList="getList"
      :projectList="projectList"
    />

    <ExeDlg
      ref="ExeDlg"
      :dlgData0="dlgExeData"
      :dlgType="dlgExeType"
      :dlgQuery="dlgExeQuery"
      @getList="getList"
    />

    <el-dialog
      :title="'设备二维码-' + selectRow.equName"
      :visible.sync="dialogVisible"
      width="300px"
    >
      <div class="qrbox2">
        <div v-if="dialogVisible" ref="qrCode"></div>
      </div>
    </el-dialog>

    <el-dialog
      :title="'导出备注'"
      :close-on-click-modal="false"
      :visible.sync="dlgShow"
      top="30px"
      :width="'600px'"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :rules="dlgRules"
        :model="dlgData"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="导出备注" prop="remark">
          <el-input
            :autosize="{ minRows: 5, maxRows: 10 }"
            v-model="dlgData.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">返回</el-button>
        <el-button
          @click="subDlg"
          type="success"
          icon="el-icon-check"
          :loading="dlgBtnLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as utils from "@/utils";
import { getAction, deleteAction } from "@/api";
import QRCode from "qrcodejs2";
import {
  maintenanceInvokeExport,
  pageGreenExportDownloadPage,
  delGreenExportDownload,
} from "@/api/deviceMan/reportMan.js";
import Pagination from "@/components/Pagination"; // 分页
import addDlg from "./addDlg";
import ExeDlg from "./ExeDlg";
import { MxDfProject } from "@/mixins/MxDfProject.js";
let dlgDataEmpty = {
  id: 0,
  remark: "",
};
let listQueryEmpty = {
  invokeStatus: "",
  equName: "", //	模糊查询	body	false	string
  pageNo: 1,
  pageSize: 20,
  planStartTime: "",
  planEndTime: "",
  timeOut: "",
  projectId: "",
};
export default {
  components: {
    Pagination,
    addDlg,
    ExeDlg,
  },
  mixins: [MxDfProject],
  props: {},
  data() {
    return {
      dlgBtnLoading: false,
      dlgShow: false,
      // 表单验证
      dlgRules: {
        remark: [{ required: true, message: "必填字段", trigger: "blur" }],
      },
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)), // 弹窗值
      popShow: false,
      exportList: [],
      listQueryExport: {
        pageNo: 1,
        pageSize: 10,
        type: "TABLE_SBBYJL",
      },
      totalExport: 0,
      timeRange: [], // 用于存储时间范围
      userRoles: JSON.parse(decodeURI(window.localStorage.userRoles)),
      dlgAddDeviceMainType: "add",
      projectList: [],
      userInfo: JSON.parse(window.localStorage.userInfo),
      searchMoreState: false, // 更多筛选
      tableKey: 0,
      list: [],
      selectList: [], // 选中
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      selectRow: {},
      dialogVisible: false,
      dlgQuery: {},
      dlgType: "", // 弹框状态add, edit
      dlgData: {},
      dlgExeQuery: {},
      dlgExeType: "", // 弹框状态add, edit
      dlgExeData: {},
      dlgAddBaoshiQuery: {},
      dlgAddBaoshiType: "", // 弹框状态add, edit
      dlgAddBaoshiData: {},
      axztSelect: [
        { id: 0, name: "待执行" },
        { id: 1, name: "已执行" },
      ],
    };
  },
  computed: {
    
  },
  watch: {},
  created() {},
  mounted() {
    this.listQuery.projectId = this.userInfo.projectId;
    this.getList();
  },
  methods: {
    tableRowClassName({ row }) {
      if (row.invokeStatusStr === "已执行") return;
      if (this.isAfterCurrentTime(row.maintenanceDate)) return;
      if (row.maintenanceCycleStr === "日常") {
        return this.isToday(row.maintenanceDate) ? "" : "recruitingRed";
      }
      const maintenanceDate = new Date(row.maintenanceDate);
      let compareDate; // 用于比较的日期

      // 创建周期映射表
      const cycleMap = {
        年: 365,
        半年: 183,
        季度: 90,
        月: 30,
      };

      // 否则使用当前时间进行比较
      compareDate = new Date();
      // 获取当前行对应的周期天数（默认兜底 30 天）
      const threshold = cycleMap[row.maintenanceCycleStr] || 30;
      // 计算日期差异天数，并取绝对值
      const daysDiff = Math.abs(
        Math.ceil((compareDate - maintenanceDate) / (1000 * 60 * 60 * 24))
      );

      // 如果日期差异超过30天，则返回特定类名
      return daysDiff > threshold ? "recruitingRed" : "";
    },
    isAfterCurrentTime(dateStr) {
      const inputDate = new Date(dateStr);
      const currentDate = new Date();
      return inputDate > currentDate;
    },
    isToday(dateStr) {
      // 将传入的日期字符串转换为 Date 对象
      const inputDate = new Date(dateStr);
      // 获取当前日期的 Date 对象
      const currentDate = new Date();

      // 分别获取年、月、日进行比较
      return (
        inputDate.getFullYear() === currentDate.getFullYear() &&
        inputDate.getMonth() === currentDate.getMonth() &&
        inputDate.getDate() === currentDate.getDate()
      );
    },
    handleTimeRangeChange(value) {
      if (value) {
        this.listQuery.planStartTime = value[0]; // 设置开始时间
        this.listQuery.planEndTime = value[1]; // 设置结束时间
        // 如果需要，可以在这里调用搜索函数或更新其他相关状态
      } else {
        this.listQuery.planStartTime = "";
        this.listQuery.planEndTime = "";
      }
    },
    exportData() {
      if (!this.listQuery.projectId) {
        this.$message.warning("请选择项目");
        return;
      }
      let exportParams = JSON.parse(JSON.stringify(this.listQuery));

      getAction("/green/equ/maintenance-invoke/export", exportParams).then(
        (res) => {
          console.log(res);
        }
      );
    },
    // 查看
    viewItem() {
      this.popShow = !this.popShow;
      if (this.popShow) {
        this.getExportList();
      }
    },
    // 获取导出记录列表
    async getExportList() {
      try {
        const res = await pageGreenExportDownloadPage(this.listQueryExport);
        const { data, code } = res.data;
        if (code === "200") {
          this.totalExport = data.total;
          this.exportList = data.list || [];
        } else {
          this.$message.error("获取导出列表失败：" + (data.msg || "未知错误"));
        }
      } catch (error) {
        console.error("请求导出列表时发生错误：", error);
        this.$message.error("请求导出列表时发生错误，请稍后再试。");
      }
    },
    // 下载
    downloadItem(url) {
      window.open(url, "_blank");
    },
    // 删除
    delItem1(data) {
      this.$confirm("此操作将删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delGreenExportDownload(data.id).then((res) => {
          if (res.data.code === "200") {
            this.$message.success(res.data.msg);
            this.getExportList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    exportExcel() {
      if (!this.listQuery.projectId) {
        this.$message.warning("请选择项目");
        return false;
      }
      this.popShow = false;
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs.dlgForm.clearValidate();
      });
    },
    async subDlg() {
      if (this.dlgData.remark === "") {
        this.$message.warning("请输入导出备注");
        return;
      }
      let loading = this.$loading({
        lock: true,
        text: "导出中,请稍候",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.dlgBtnLoading = true;

      let postData = JSON.parse(JSON.stringify(this.listQuery));

      postData.remark = this.dlgData.remark;
      const res1 = await maintenanceInvokeExport(postData);
      this.dlgBtnLoading = false;
      loading.close();
      let res = res1.data;
      if (res.code === "200") {
        this.$message.success("操作成功，请在我的导出中下载");
        this.dlgShow = false;
      } else {
        this.$message.error(res.msg);
      }
    },
    showQrCode(row) {
      this.selectRow = row;
      this.dialogVisible = true;

      setTimeout(() => {
        let qrUrl = row.equCode + "";
        this.qrcode = new QRCode(this.$refs.qrCode, {
          text: qrUrl,
          width: 200,
          height: 200,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H,
        });
      }, 200);
    },
    parseTime(time) {
      return time.replace(/T/g, " ");
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },
    searchFunc() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    getList() {
      this.list = [];

      let sendObj = JSON.parse(JSON.stringify(this.listQuery));

      this.listLoading = true;
      getAction("sa/green/equ/maintenance-invoke/page", sendObj).then(
        (res0) => {
          let res = res0.data;
          this.listLoading = false;
          if (res.code == 200) {
            if (utils.isNull(res.data)) {
              this.list = [];
              this.total = 0;
            } else {
              let list = res.data.list;
              for (let item of list) {
                if (item.maintenanceItem) {
                  item.maintenanceItem = JSON.parse(item.maintenanceItem);
                } else {
                  item.maintenanceItem = [];
                }

                if (item.accessoryJson) {
                  item.accessoryJson = JSON.parse(item.accessoryJson);
                } else {
                  item.accessoryJson = [];
                }
              }
              console.log("=====list", list);
              this.list = list;
              this.total = res.data.total;

              this.$nextTick(() => {
                this.$refs.tableRef.doLayout();
              });
            }
          } else {
            this.total = 0;
            this.$message({
              type: "warning",
              message: res.msg,
            });
          }
        }
      );
    },

    showDlg(type, row) {
      this.dlgQuery = row; // 查询条件
      // if (type == "add") {
      //   this.dlgData = { id: 0 }; // 表单数据
      //   this.$refs.addDlg.isZhixing = false;
      // } else

      this.dlgData = row;
      this.$refs.addDlg.isZhixing = false;
      this.dlgType = type;
      this.$refs.addDlg.dlgState = true;
    },
    showExeDlg(row) {
      this.dlgExeData = row;
      this.dlgExeType = "edit";
      this.$refs.ExeDlg.dlgState = true;
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.qrbox2 {
  padding: 30px 0;
  width: 200px;
  height: 200px;
  margin: 0 auto;
  box-sizing: content-box;
}
</style>
