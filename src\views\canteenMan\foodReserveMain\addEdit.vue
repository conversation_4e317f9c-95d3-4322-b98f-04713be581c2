<template>
  <el-dialog
    width="800px"
    :title="getDialogTitle(dlgType)"
    :visible.sync="dialogVisible"
    append-to-body
    @close="onClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      label-width="120px"
      :disabled="dlgType == 'info'"
    >
      <el-form-item label="预约开始时间" prop="startTime">
        <el-date-picker
          v-model="formData.startTime"
          type="date"
          placeholder="选择日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptionsStart"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="预约结束时间" prop="endTime">
        <el-date-picker
          v-model="formData.endTime"
          type="date"
          placeholder="选择日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptionsEnd"
        >
        </el-date-picker>
      </el-form-item>

      <el-button
        style="margin-bottom: 10px"
        type="success"
        icon="el-icon-plus"
        class="fl ml10"
        size="small"
        @click="showFoodsDialog"
        >选择菜品
      </el-button>
      <el-table
        ref="tableBar"
        class="mt10 m-small-table"
        :key="1324"
        :data="formData.list"
        border
        fit
        highlight-current-row
        max-height="500"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
          width="60"
        ></el-table-column>
        <el-table-column
          prop="equTypeStr"
          label="菜品图片"
          width="auto"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-image
              style="width: 30px; height: 30px"
              :src="scope.row.imgUrl[0]"
              :preview-src-list="scope.row.imgUrl"
            ></el-image>
          </template>
        </el-table-column>
        <!-- 其他列省略... -->
        <el-table-column
          prop="name"
          label="菜品名称"
          width="120"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="model"
          label="规格"
          width="100"
          align="center"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="unit"
          label="单位"
          width="80"
          align="center"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="价格"
          width="170"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.price"
              :min="0"
              :precision="2"
            ></el-input-number>
            元
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="90"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              @click="diaRowDel(scope)"
              type="danger"
              size="mini"
              title="删除"
              icon="el-icon-delete"
              plain
            ></el-button>
            <!-- <el-button @click="showHzmxDia(dlgData.id, scope.row.goodsBm)" type="primary" size="mini" icon="el-icon-document" plain
            >查看明细</el-button
          > -->
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <div slot="footer">
      <el-button @click="close" size="small">取 消</el-button>
      <el-button
        size="small"
        @click="handleConfirm"
        v-show="dlgType !== 'info'"
        type="success"
        icon="el-icon-check"
        :loading="btnLoading"
        >确 定</el-button
      >
    </div>
    <Foodsdialog
      :visible.sync="foodsVisible"
      :foods="selectedFoods"
      @close="handleFoodsClose"
      @confirm="handleFoodsConfirm"
    />
  </el-dialog>
</template>

<script>
import { postAction, getAction, putAction } from "@/api";
import { uploadImg } from "@/utils/uploadImg";
import Foodsdialog from "@/views/canteenMan/components/Foodsdialog"; // 多选商品
const formDataEmpty = {
  dishesName: undefined,
  endTime: undefined,
  list: undefined,
  startTime: undefined,
};
export default {
  components: { Foodsdialog },
  props: {},
  data() {
    return {
      // 开始日期 :picker-options 中引用
      pickerOptionsStart: {
        disabledDate: (time) => {
          let endDateVal = this.formData.endTime;
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime();
          }
        },
      },
      // 结束日期 :picker-options 中引用
      pickerOptionsEnd: {
        disabledDate: (time) => {
          let beginDateVal = this.formData.startTime;
          if (beginDateVal) {
            return (
              time.getTime() <
              new Date(beginDateVal).getTime() - 1 * 24 * 60 * 60 * 1000
            );
          }
        },
      },
      selectedFoods: [],
      foodsVisible: false,
      dlgType: "add",
      dialogVisible: false,
      btnLoading: false,
      formData: Object.assign({}, formDataEmpty),
      rules: {
        startTime: [
          {
            required: true,
            message: "必填字段",
            trigger: "change",
          },
        ],
        endTime: [
          {
            required: true,
            message: "必填字段",
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {},
  methods: {
    diaRowDel(scope) {
      let index = scope.$index;
      this.$confirm(`确认删除?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.formData.list.splice(index, 1);
          this.selectedFoods.splice(index, 1);
          this.formData = JSON.parse(JSON.stringify(this.formData));
        })
        .catch(() => {});
    },
    showFoodsDialog() {
      this.foodsVisible = true;
    },
    handleFoodsClose() {
      this.foodsVisible = false;
    },
    handleFoodsConfirm(val) {
      // 处理确定按钮点击事件，例如将选中的菜品添加到表单数据中
      this.selectedFoods = JSON.parse(JSON.stringify(val));
      this.formData.list = JSON.parse(JSON.stringify(val));
      this.foodsVisible = false;
    },
    getDialogTitle(type) {
      const titleMap = {
        add: "添加",
        edit: "编辑",
        info: "详情",
      };
      return titleMap[type] || "详情";
    },
    uploadQj1(file) {
      if (file.size > 5 * 1024 * 1024) {
        this.$message({
          type: "warning",
          message: "上传图片大小不能超过5M",
        });
        return false;
      }
      uploadImg(file, "ERP_web/greenMan/bch/bch_").then((res) => {
        this.formData.imgUrl.push(res);
      });
    },
    delUploadImgByArr(index) {
      this.$confirm("是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning", // // success, warning, info, error
      }).then(() => {
        this.$message({
          type: "success", // success, warning, info, error
          message: "删除成功",
        });
        let formData = this.formData;
        formData.imgUrl.splice(index, 1);
      });
    },
    init(type, row) {
      this.dlgType = type;
      this.dialogVisible = true;
      if (type == "add") {
      } else {
        this.getInfo(row);
      }
    },
    getInfo(row) {
      getAction(`/canteen/cn/food-booking/get?id=${row.id}`).then((res) => {
        let { code, data } = res.data;
        if (code === "200") {
          data.list.forEach((item) => {
            item.imgUrl = item.imgUrl ? JSON.parse(item.imgUrl) : [];
            item.id = item.dishesId;
          });
          this.formData = data ? JSON.parse(JSON.stringify(data)) : [];
          this.selectedFoods = JSON.parse(JSON.stringify(data.list));
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    onClose() {
      this.dialogVisible = false;
      this.$refs["elForm"].resetFields();
      this.formData = Object.assign({}, formDataEmpty);
      this.selectedFoods = [];
    },
    close() {
      this.dialogVisible = false;
      this.$refs["elForm"].resetFields();
      this.formData = Object.assign({}, formDataEmpty);
      this.selectedFoods = [];
    },
    handleConfirm() {
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          const postData = JSON.parse(JSON.stringify(this.formData));
          if (postData.list.length == 0) {
            this.$message.error("菜品不能为空");
            return;
          }
          postData.list.forEach((item) => {
            if (item.price == undefined) {
              this.$message.error("菜品价格不能为空");
              throw new Error("菜品价格不能为空");
            }
          });
          postData.dishesName = postData.list
            .map((item) => item.name)
            .join(",");
          postData.list = postData.list.map((item) => {
            return {
              dishesId: item.id,
              imgUrl: JSON.stringify(item.imgUrl),
              model: item.model,
              name: item.name,
              price: item.price,
              unit: item.unit,
            };
          });
          this.btnLoading = true;

          const actionUrl =
            this.dlgType === "edit"
              ? `/canteen/cn/food-booking/update`
              : `/canteen/cn/food-booking/create`;

          const actionMethod = this.dlgType === "edit" ? putAction : postAction;

          if (this.dlgType !== "edit" && postData.id) {
            delete postData.id;
          }

          actionMethod(actionUrl, postData).then((res) => {
            this.btnLoading = false;
            if (res.data.code === "200") {
              this.$message({
                type: "success",
                message: this.dlgType === "edit" ? "编辑成功！" : "添加成功！",
              });
              this.dialogVisible = false;
              this.resetFormAndSearch();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },

    resetFormAndSearch() {
      this.$refs["elForm"].resetFields();
      this.$parent.searchFunc();
    },
  },
};
</script>

<style></style>
