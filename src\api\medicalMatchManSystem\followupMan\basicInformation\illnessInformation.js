import request from '@/utils/request'
import { requestExcel } from '@/utils'

/*
*疾病信息
*/

// 动态查询疾病信息 分页 
export function findSickDynamic(data) 
{
	return request({
		url: `/follow/findSickDynamic`,
		method: 'post',
		data
	})
}

// 修改疾病信息状态（删除疾病信息）
export function updateSick(data)
{
	return request({
		url: `/follow/updateSick`,
		method: 'post',
		data
	})
}

// 新增/修改疾病信息接口
export function saveOrUSick(data)
{
	return request({
		url: `/follow/saveOrUSick`,
		method: 'post',
		data
	})
}

// 导入
export function importExcelSick(data)
{
	return requestExcel('/follow/importExcelSick', data)
}





