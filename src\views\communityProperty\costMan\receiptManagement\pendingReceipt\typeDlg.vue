<template>
  <!-- 弹窗 新增/编辑 -->
  <el-dialog class="mazhenguo" :title="title" :close-on-click-modal="false" :visible.sync="dlgState" append-to-body
    width="1200px" top="30px">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="归属小区：" prop="communityId">
          <el-select style="width: 150px;" v-model="listQuery.communityId" filterable clearable placeholder="请选择小区"
            @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <el-form-item label="归属楼栋：" prop="floorId" v-if="dlgType==1">
            <el-select style="width: 150px;" v-model="listQuery.floorId" filterable clearable placeholder="请选择楼栋"
              @change="floorChange">
              <el-option v-for="item in buildingList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属单元：" prop="unitId" v-if="dlgType==1">
            <el-select style="width: 150px;" v-model="listQuery.unitId" filterable clearable placeholder="请选择单元">
              <el-option v-for="item in unitList" :key="item.id" :label="item.unitName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="isLabel">
            <el-input style="width: 150px;" @keyup.enter.native="getList" :placeholder="isPlaceholder" v-model="listQuery.label">
              <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
            </el-input>
          </el-form-item>
          <el-button icon="el-icon-search" type="success" size="mini" @click="getList">搜索</el-button>
        </el-form-item>
      </el-form>
      <div class="table-container">
        <el-table class="m-small-table" style="max-height: 600px;overflow-y: auto;" v-loading="listLoading" :data="list"
          border fit highlight-current-row @row-click="tableRowClick">
          <!-- <el-table-column label="公式名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column> -->
          <el-table-column label="" align="center" width="60">
            <template slot-scope="scope">
              <el-radio v-model="selectData.id" :label="scope.row.id" style="width: 16px"><span></span></el-radio>
            </template>
          </el-table-column>
          <el-table-column label="#" align="center" width="60">
            <template slot-scope="scope">
              {{ (listQuery.page - 1) * listQuery.limit + scope.$index + 1 }}
            </template>
          </el-table-column>

          <el-table-column label="小区名称" prop="communityName"> </el-table-column>
          <el-table-column label="房屋" prop="roomFullName" v-if="dlgType == 1"> </el-table-column>
          <el-table-column label="车位编号" prop="numStr" v-if="dlgType == 2"> </el-table-column>
          <el-table-column label="车库编号" prop="numStr" v-if="dlgType == 3"> </el-table-column>

          <el-table-column label="业主" prop="memberName" v-if="dlgType == 1"> </el-table-column>
          <el-table-column label="业主" prop="ownerName" v-if="dlgType == 3 || dlgType == 2"> </el-table-column>

          <el-table-column label="电话" prop="memberPhone" v-if="dlgType == 1"> </el-table-column>
          <el-table-column label="电话" prop="ownerPhone" v-if="dlgType == 3 || dlgType == 2"> </el-table-column>

        </el-table>
      </div>

      <div class="page-container">
        <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dlgState = false" icon="el-icon-back">返回</el-button>
      <el-button type="success" @click="dlgSubFunc" icon="el-icon-check">
        确认
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import {
  buildingroomPage,
  cogaragePage,
  coparkingPage,
  communityPage,
  cofloorCommunity,
  buildingunitFloor
} from "@/api/communityMan";
export default {
  components: {
    Pagination,
  },
  props: {
    dlgType: {
      type: String,
      default: ""
    },
    dlgState0: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val;
    },
    dlgState(val) {
      if (val) {
        this.getCommunityList();
        setTimeout(() => {
          if (this.dlgType == 1) {
            this.title = "选择房屋";
            this.isLabel='归属房屋'
            this.isPlaceholder='请输入房屋名称'
            this.getRoomPage();
          } else if (this.dlgType == 2) {
            this.title = "选择车位";
            this.isLabel='车位编号'
            this.isPlaceholder='请输入车位编号'
            this.getCoparkingList();
          } else {
            this.title = "选择车库";
            this.isLabel='车库编号'
            this.isPlaceholder='请输入车库编号'
            this.getCogarageList();
          }
        }, 50);
      } else {
        this.closeDlg()
      }
    }
  },
  data() {
    return {
      title: "",
      isLabel:'',
      isPlaceholder:'',
      dlgState: false,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
        communityId: "",
        floorId: "",
        unitId: ""
      },
      buildingList: [],
      unitList: [],
      communityList: [],
      selectData: { id: "" },
    };
  },
  methods: {
    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200
      };
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data;
        }
      });
    },
    floorChange() {
      let floorId
      floorId = this.listQuery.floorId
      this.listQuery.unitId = ''
      this.getUnitList(floorId)
    },
    // 获取楼栋列表
    getBuildingList(id) {
      if (utils.isNull(id)) {
        return
      }
      cofloorCommunity(id).then((res) => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data
        }
      })
    },
    // 获取单元列表
    getUnitList(id) {
      if (utils.isNull(id)) {
        return
      }
      buildingunitFloor(id).then((res) => {
        if (res.data.code == 200) {
          this.unitList = res.data.data
        }
      })
    },
    //获取房屋
    getRoomPage() {
      this.listLoading = true;
      buildingroomPage(this.listQuery).then(res => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data));
          this.total = res.data.page ? res.data.page.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    communityChange() {
      let communityId;
      communityId = this.listQuery.communityId;
      this.listQuery.floorId = "";
      this.listQuery.unitId = "";
      this.buildingList = [];
      this.unitList = [];
      this.getBuildingList(communityId);
    },
    // 获取车库
    getCogarageList() {
      this.listLoading = true;
      cogaragePage(this.listQuery).then(res => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data));
          this.total = res.data.page ? res.data.page.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 获取车位
    getCoparkingList() {
      this.listLoading = true;
      coparkingPage(this.listQuery).then(res => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data));
          this.total = res.data.page ? res.data.page.total : 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.getList();
    },
    getList() {
      if (this.dlgType == 1) {
        this.getRoomPage();
      } else if (this.dlgType == 2) {
        this.getCoparkingList();
      } else {
        this.getCogarageList();
      }
    },
    // 点击行
    tableRowClick(row, column, event) {
      this.selectData = row;
    },
    closeDlg() {
      this.listQuery={
        page: 1,
        limit: 20,
        label: "",
        communityId: "",
        floorId: "",
        unitId: ""
      }
      this.$emit("closeDlg");
    },
    // 弹窗提交 ------
    dlgSubFunc() {
      if (utils.isNull(this.selectData) || utils.isNull(this.selectData.id)) {
        this.$emit("backFunc", "");
      } else {
        this.$emit("backFunc", this.selectData);
      }

      this.closeDlg();
    },
  }
};
</script>
<style scoped>
::v-deep.el-table::before {
  display: none !important;
}
</style>
