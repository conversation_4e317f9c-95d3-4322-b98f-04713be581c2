<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="部门" prop="branchName">
          <el-input
            v-model="formData.branchName"
            placeholder="请选择部门"
            readonly
            :disabled="isReadonly"
            @focus="!isReadonly && showBmTree()"
            :style="isReadonly ? '' : 'cursor: pointer'"
          >
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入名称"
            maxlength="100"
            :disabled="isReadonly"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="图纸类型" prop="type">
          <el-select
            v-model="formData.type"
            placeholder="请选择图纸类型"
            style="width: 100%"
            :disabled="isReadonly"
            @change="handleTypeChange"
          >
            <el-option
              v-for="item of drawingTypeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="楼宇" prop="premises">
          <el-input
            v-model="formData.premises"
            placeholder="请输入楼宇"
            maxlength="50"
            :disabled="isReadonly"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="楼层" prop="storey">
          <el-input
            v-model="formData.storey"
            placeholder="请输入楼层"
            maxlength="20"
            :disabled="isReadonly"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="用户" prop="postName">
      <el-input
        v-model="formData.postName"
        :title="formData.postName"
        placeholder="请选择用户"
        readonly
        :disabled="isReadonly"
        @focus="!isReadonly && showUserMulDlg()"
        :style="isReadonly ? '' : 'cursor: pointer'"
      >
        <i slot="suffix" class="el-input__icon el-icon-search"></i>
      </el-input>
    </el-form-item>

    <el-form-item label="备注">
      <el-input
        v-model="formData.remark"
        type="textarea"
        :rows="3"
        placeholder="请输入备注信息，最多500字"
        maxlength="500"
        :disabled="isReadonly"
      />
    </el-form-item>

    <!-- 多选员工 -->
    <selectUserMulDlg
      ref="userMulDlgRef"
      :dlgType="dlgUserMulType"
      :dlgQuery="dlgUserMulQuery"
      :selectList0="dlgUserMulSelectList"
      @backFunc="dlgUserMulBackFunc"
      :noRule="noRule"
      :isOnJob="true"
    />
  </div>
</template>

<script>
import { postAction } from "@/api";
import { getDataDict } from "@/utils";
import selectUserMulDlg from "@/components/DialogWflow/selectUserMulDlg";

export default {
  name: "BasicForm",
  props: {
    formData: {
      type: Object,
      required: true,
    },
    dlgType: {
      type: String,
      default: "",
    },
  },
  components: {
    selectUserMulDlg,
  },
  computed: {
    // 是否为只读模式
    isReadonly() {
      return this.dlgType === "info";
    },

    // 部门树选择结果
    bmTreeBranchId() {
      return this.$store.getters.bmTreeBranchId;
    },
    bmTreeBranchName() {
      return this.$store.getters.bmTreeBranchName;
    },
    bmTreeSelect() {
      return this.$store.getters.bmTreeSelect;
    },
  },

  watch: {
    // 监听部门选择
    bmTreeBranchId(val) {
      if (val === "empty") {
        return false;
      }
      this.formData.branchId = val;
    },
    bmTreeBranchName(val) {
      if (val === "empty") {
        return false;
      }
      this.formData.branchName = val;
    },
    bmTreeSelect(val) {
      if (val === "empty") {
        return false;
      }
      let valJson = JSON.parse(val);
      console.log(valJson, "bmTreeSelect");
    },
  },

  data() {
    return {
      dlgUserMulQuery: {},
      dlgUserMulType: "", // 弹框状态add, edit
      dlgUserMulSelectList: "",
      noRule: false,
      // 岗位选择数据
      selectDlgData: [],

      // 选项数据
      projectList: [], // 保留但不使用，避免模板引用错误
      drawingTypeOptions: [],
    };
  },

  mounted() {
    // this.loadProjects(); // 取消项目加载
    getDataDict(this, "tuzhileixing", "drawingTypeOptions");
  },

  methods: {
    dlgUserMulBackFunc(list0) {
      console.log("弹窗返回", list0);
      let personIds = [];
      let personNames = [];

      list0.forEach((item) => {
        personIds.push(item.id);
        personNames.push(item.label);
      });

      this.formData.postId = personIds.join(",");
      this.formData.postName = personNames.join(",");
    },
    handleTypeChange() {
      // 使用箭头函数简化代码
      const selectedType = this.drawingTypeOptions.find(
        (item) => item.id === this.formData.type
      );

      if (selectedType) {
        this.formData.typeStr = selectedType.name;
      } else {
        this.formData.typeStr = "";
      }
    },
    /**
     * 显示部门树选择对话框
     */
    showUserMulDlg() {
      if (this.formData.postId && this.formData.postName) {
        let idList = this.formData.postId.split(",");
        let nameList = this.formData.postName.split(",");

        // 检查idList和nameList长度是否一致
        if (idList.length !== nameList.length) {
          console.error("ID列表和名称列表长度不一致");
          return;
        }

        let list = idList.map((id, index) => ({
          id: id,
          label: nameList[index],
        }));

        this.dlgUserMulSelectList = JSON.stringify(list);
      } else {
        this.dlgUserMulSelectList = "";
      }

      this.dlgUserMulQuery = {};
      this.dlgUserMulType = "edit";
      this.$refs.userMulDlgRef.show();
    },
    showBmTree() {
      this.$store.commit("SET_BMTREEISROLE", true);
      this.$store.commit("SET_BMTREESTATE", true);
    },
  },
};
</script>
