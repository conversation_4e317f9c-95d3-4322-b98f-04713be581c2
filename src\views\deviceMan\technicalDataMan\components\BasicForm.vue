<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="项目" prop="projectId">
          <el-select
            v-model="formData.projectId"
            placeholder="请选择项目"
            style="width: 100%"
            filterable
            clearable
            :disabled="isReadonly"
            @change="handleProjectChange"
          >
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="部门" prop="branchName">
          <el-input
            v-model="formData.branchName"
            placeholder="请选择部门"
            readonly
            :disabled="isReadonly"
            @focus="!isReadonly && showBmTree()"
            :style="isReadonly ? '' : 'cursor: pointer'"
          >
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 事业部和项目部通过部门选择自动带出，显示为只读 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="事业部">
          <el-input
            v-model="formData.branchOperationName"
            readonly
            :disabled="isReadonly"
            placeholder="通过部门选择自动带出"
            style="background-color: #f5f7fa"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="项目部">
          <el-input
            v-model="formData.branchProjectName"
            readonly
            :disabled="isReadonly"
            placeholder="通过部门选择自动带出"
            style="background-color: #f5f7fa"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入名称"
            maxlength="100"
            :disabled="isReadonly"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="图纸类型" prop="type">
          <el-select
            v-model="formData.type"
            placeholder="请选择图纸类型"
            style="width: 100%"
            :disabled="isReadonly"
            @change="handleTypeChange"
          >
            <el-option
              v-for="item of drawingTypeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="楼宇" prop="premises">
          <el-input
            v-model="formData.premises"
            placeholder="请输入楼宇"
            maxlength="50"
            :disabled="isReadonly"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="楼层" prop="storey">
          <el-input
            v-model="formData.storey"
            placeholder="请输入楼层"
            maxlength="20"
            :disabled="isReadonly"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="岗位" prop="postName">
      <el-input
        v-model="formData.postName"
        :title="formData.postName"
        placeholder="请选择岗位"
        readonly
        :disabled="isReadonly"
        @focus="!isReadonly && showPostDlg()"
        :style="isReadonly ? '' : 'cursor: pointer'"
      >
        <i slot="suffix" class="el-input__icon el-icon-search"></i>
      </el-input>
    </el-form-item>

    <el-form-item label="备注">
      <el-input
        v-model="formData.remark"
        type="textarea"
        :rows="3"
        placeholder="请输入备注信息，最多500字"
        maxlength="500"
        :disabled="isReadonly"
      />
    </el-form-item>
  </div>
</template>

<script>
import { postAction } from "@/api";
import { getDataDictNum } from "@/utils";

export default {
  name: "BasicForm",
  props: {
    formData: {
      type: Object,
      required: true,
    },
    dlgType: {
      type: String,
      default: "",
    },
  },

  computed: {
    // 是否为只读模式
    isReadonly() {
      return this.dlgType === "info";
    },

    // 岗位列表
    diaPostGet: {
      get: function () {
        return this.$store.getters.diaPostGet;
      },
      set: function (val) {
        this.$store.commit("SET_DIAPOST_GET", "");
      },
    },

    // 部门树选择结果
    bmTreeBranchId() {
      return this.$store.getters.bmTreeBranchId;
    },
    bmTreeBranchName() {
      return this.$store.getters.bmTreeBranchName;
    },
    bmTreeSelect() {
      return this.$store.getters.bmTreeSelect;
    },
  },

  watch: {
    diaPostGet(val) {
      if (!val) {
        return false;
      }
      let postList = JSON.parse(val);
      console.log("岗位选择返回:", postList);
      this.selectDlgData = JSON.parse(JSON.stringify(postList));

      let postIds = [];
      let postNames = [];
      postList.map(function (item) {
        postIds.push(item.postId);
        postNames.push(
          item.label +
            "(" +
            item.postCode +
            (item.userName ? item.userName : "空岗") +
            ")"
        );
      });

      this.formData.postId = postIds.toString();
      this.formData.postName = postNames.toString();
    },

    // 监听部门选择
    bmTreeBranchId(val) {
      if (val === "empty") {
        return false;
      }
      this.formData.branchId = val;
    },
    bmTreeBranchName(val) {
      if (val === "empty") {
        return false;
      }
      this.formData.branchName = val;
    },
    bmTreeSelect(val) {
      if (val === "empty") {
        return false;
      }
      let valJson = JSON.parse(val);
      console.log(valJson, "bmTreeSelect");
      this.formData.branchOperationId = valJson.branchOperationId;
      this.formData.businessUnit = valJson.branchOperationName;
      this.formData.branchProjectId = valJson.branchProjectId;
      this.formData.branchProjectName = valJson.branchProjectName;
    },
  },

  data() {
    return {
      // 岗位选择数据
      selectDlgData: [],

      // 选项数据
      projectList: [],
      drawingTypeOptions: [],
    };
  },

  mounted() {
    this.loadProjects();
    getDataDictNum(this, "tuzhileixing", "drawingTypeOptions");
  },

  methods: {
    /**
     * 加载项目列表
     */
    loadProjects() {
      try {
        // 从localStorage获取项目列表
        const userInfo = JSON.parse(window.localStorage.ERPUserInfo || "{}");
        this.projectList = userInfo.projects || [];
      } catch (error) {
        console.error("获取项目列表失败:", error);
        this.projectList = [];
      }
    },

    /**
     * 项目变化处理
     */
    handleProjectChange() {
      // 获取选中项目的名称
      const selectedProject = this.projectList.find(
        (item) => item.id === this.formData.projectId
      );

      if (selectedProject) {
        this.formData.projectName = selectedProject.name;
      } else {
        this.formData.projectName = "";
      }
    },
    handleTypeChange() {
      // 使用箭头函数简化代码
      const selectedType = this.drawingTypeOptions.find(
        (item) => item.id === this.formData.type
      );

      if (selectedType) {
        this.formData.typeStr = selectedType.name;
      } else {
        this.formData.typeStr = "";
      }
    },
    /**
     * 显示部门树选择对话框
     */
    showBmTree() {
      this.$store.commit("SET_BMTREEISROLE", true);
      this.$store.commit("SET_BMTREESTATE", true);
    },

    /**
     * 显示岗位选择对话框
     */
    showPostDlg() {
      this.$store.commit("SET_DIAPOST_TYPE", "post-*-");
      if (this.selectDlgData.length !== 0) {
        this.$store.commit(
          "SET_DIAPOST_GET",
          JSON.stringify(this.selectDlgData)
        );
      } else {
        this.$store.commit("SET_DIAPOST_GET", "");
      }
      this.$store.commit("SET_DIAPOST_MUL", true);
      this.$emit("show-post-dialog");
    },
  },
};
</script>
