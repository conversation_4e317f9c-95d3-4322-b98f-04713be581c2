<template>
  <div class="mazhenguo app-container">
    <div class="filter-container">
      <el-form inline :model="params" @submit.native.prevent>
        <el-form-item label="发起人:">
          <el-input
            @focus="showUserDlg"
            v-model="params.startUserName"
            placeholder="请选择"
            readonly
          >
            <i
              slot="suffix"
              @click="resetSearchItem(['startUserId', 'startUserName'])"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>

        <el-form-item label="单据类型:">
          <el-cascader
            style="width: 200px"
            v-model="params.formId"
            :options="formTypeList"
            :props="{
              expandTrigger: 'hover',
              children: 'items',
              value: 'id',
              label: 'name'
            }"
            @change="searchFunc"
            collapse-tags
            clearable
            filterable
          ></el-cascader>
        </el-form-item>
        <el-form-item label="发起时间:">
          <el-date-picker
            style="width: 230px"
            v-model="params.dateRange"
            @change="searchFunc"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchFunc"
          >搜索</el-button
        >
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :header-cell-style="{ background: '#e8e8e8' }"
      style="width: 100%;"
      @row-click="showProcess"
      height="calc(100vh - 308px)"
    >
      <el-table-column
        prop="processDefName"
        label="审批类型"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="owner"
        show-overflow-tooltip
        label="发起人"
        min-width="100px"
      >
        <template slot-scope="scope">
          {{ scope.row.owner ? scope.row.owner.name : "" }}

          <!-- <avatar
            :size="35"
            :name="scope.row.owner.name"
            :src="scope.row.owner.avatar"
          /> -->
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="taskName"
        label="任务节点名"
      />
      <el-table-column
        show-overflow-tooltip
        prop="taskCreateTime"
        label="任务开始时间"
        min-width="120px"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="taskEndTime"
        label="处理完成时间"
        min-width="120px"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="duration"
        label="处理耗时"
        min-width="120px"
      >
        <template slot-scope="scope">
          {{ getDuration(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="处理结果">
        <template slot-scope="scope">
          <el-tag
            type="success"
            size="medium"
            v-if="scope.row.taskResult === 'agree'"
            >已同意</el-tag
          >
          <el-tag
            type="danger"
            size="medium"
            v-else-if="scope.row.taskResult === 'refuse'"
            >已拒绝</el-tag
          >
          <el-tag
            type="warning"
            size="medium"
            v-else-if="scope.row.taskResult === 'recall'"
            >已退回</el-tag
          >
          <el-tag
            type="primary"
            size="medium"
            v-else-if="scope.row.taskResult === 'transfer'"
            >已转交</el-tag
          >
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right" class="mt10">
      <!-- hide-on-single-page -->
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="params.pageSize"
        :current-page.sync="params.pageNo"
        @current-change="getIdoList"
      ></el-pagination>
    </div>
    <el-drawer
      :size="isMobile ? '100%' : '500px'"
      direction="rtl"
      title="审批详情"
      :visible.sync="processVisible"
    >
      <instance-preview
        v-if="processVisible"
        :node-id="selectInstance.nodeId"
        :instance-id="selectInstance.instanceId"
        @handler-after="handlerAfter"
      ></instance-preview>
    </el-drawer>

    <!-- closeFree -->
    <w-dialog
      v-if="!isMobile"
      :title="`发起 - ${selectInstance.processDefName}`"
      width="1000px"
      v-model="openItemDl"
      okText="提 交"
      @cancel="openItemDl = false"
      @ok="submitForm"
    >
      <initiate-process
        ref="processForm"
        :node-id="selectInstance.nodeId"
        :instance-id="selectInstance.instanceId"
        :code="selectInstance.formId"
        v-if="openItemDl"
        @ok="openItemDl = false"
      ></initiate-process>
    </w-dialog>
    <selectUserDlg
      :dlgState0="dlgUserState"
      :dlgData0="dlgUserData"
      :dlgType="dlgUserType"
      :dlgQuery="dlgUserQuery"
      @closeDlg="closeUserDlg"
      @dlgUserSubFunc="dlgUserSubFunc"
    />
  </div>
</template>

<script>
import taskApi from "@/api/workFlow/processTask";
import moment from "moment";
import InstancePreview from "../approval/ProcessInstancePreview";
import InitiateProcess from "../InitiateProcess";
import { getGroupModelsByUser } from "@/api/workFlow/modelGroup";
import selectUserDlg from "@/components/Dialog2/selectUserDlg";
import * as utils from "@/utils";

let paramsEmpty = {
  pageSize: 20,
  pageNo: 1,
  code: "",
  finished: null,

  label: "",
  startUserId: "",
  startUserName: "",
  formId: "", // 单据类型
  dateRange: []
  // beginDate
  // endDate
};

export default {
  name: "Finished",
  components: { InstancePreview, InitiateProcess, selectUserDlg },
  data() {
    return {
      total: 0,
      params: {
        pageSize: 10,
        pageNo: 1,
        finished: null,
        code: ""
      },
      openItemDl: false,
      selectInstance: {},
      loading: false,
      processVisible: false,
      formTypeList: [],
      dataList: [],
      dlgUserQuery: {},
      dlgUserState: false,
      dlgUserType: "", // 弹框状态add, edit
      dlgUserData: {}
    };
  },
  computed: {
    isMobile() {
      return window.screen.width < 450;
    }
  },
  mounted() {
    if (this.isMobile) {
      document.title = "已处理的";
    }
    this.params = JSON.parse(JSON.stringify(paramsEmpty));
    this.getFormTypeList();
    this.searchFunc();
  },
  methods: {
    showUserDlg() {
      this.dlgUserQuery = "";
      this.dlgUserState = true;
    },
    // 关闭弹窗
    closeUserDlg() {
      this.dlgUserState = false;
    },
    // 选择员工返回
    dlgUserSubFunc(data) {
      console.log("车辆返回", data);
      if (utils.isNull(data)) return false;
      this.params.startUserId = data.id;
      this.params.startUserName = data.label;

      this.searchFunc();
    },
    getFormTypeList() {
      getGroupModelsByUser()
        .then(rsp => {
          this.loading = false;
          let list = rsp.data;
          for (let item of list) {
            if (item.items && item.items.length > 0) {
              for (let item2 of item.items) {
                item2.id = item2.formId;
                item2.name = item2.formName;
              }
            }
          }

          console.log("===list", list);
          this.formTypeList = list;
        })
        .catch(err => {
          this.loading = false;
          this.$err(err, "获取分组异常");
          this.recentlyUsed.length = 0;
        });
    },
    searchFunc() {
      this.params.pageNo = 1;
      this.getIdoList();
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.params[item] = "";
      }
      this.searchFunc();
    },

    getIdoList() {
      let sendObj = JSON.parse(JSON.stringify(this.params));
      if (sendObj.dateRange && sendObj.dateRange.length > 0) {
        sendObj.beginDate = sendObj.dateRange[0];
        sendObj.endDate = sendObj.dateRange[1];
      } else {
        sendObj.beginDate = "";
        sendObj.endDate = "";
      }
      delete sendObj.dateRange;

      if (sendObj.formId && sendObj.formId.length > 0) {
        sendObj.formId = sendObj.formId[1];
      } else {
        sendObj.formId = "";
      }
      sendObj.startUser = sendObj.startUserId;

      delete sendObj.startUserId;
      delete sendObj.startUserName;

      this.loading = true;
      taskApi
        .getIdoList(sendObj)
        .then(rsp => {
          this.loading = false;
          this.total = rsp.data.total;
          this.dataList = rsp.data.records;
        })
        .catch(e => {
          this.loading = false;
        });
    },
    reSubmit(row) {
      this.selectInstance = row;
      this.openItemDl = true;
      if (this.isMobile) {
        this.$router.push(
          `/mbinitiate?code=${row.formId}&instanceId=${row.instanceId}`
        );
      }
    },
    submitForm() {
      this.$refs.processForm.validate(valid => {
        if (!this.isMobile) {
          if (valid) {
            this.$refs.processForm.submit();
          } else {
            this.$message.warning("请完善表单😥");
          }
        }
      });
    },
    showProcess(row) {
      this.processVisible = true;
      this.selectInstance = row;
    },
    getDuration(row) {
      let end = this.$isNotEmpty(row.taskEndTime)
        ? row.taskEndTime
        : moment().format("YYYY-MM-DD HH:mm:ss");
      return this.$timeCoverStr(row.createTime, end);
    },
    handlerAfter() {
      this.processVisible = false;
      this.getIdoList();
    }
  }
  // watch: {
  //   params: {
  //     deep: true,
  //     handler() {
  //       this.getIdoList();
  //     }
  //   }
  // }
};
</script>

<style scoped></style>
