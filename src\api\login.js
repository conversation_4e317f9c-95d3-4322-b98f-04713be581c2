import request from "@/utils/request";
// import { BASIC_TOKEN } from "@/configs/const";
let BASIC_TOKEN = "d2ViLWFwcDphc2Q0cnhjeA=="

export function login(data) {
  return request({
    url: "/zuul/oauth/token",
    method: "post",
    data
  });
}

export function loginByUsername(data) {
  return request({
    url: "/oauth/token",
    method: "post",
    headers: {
      Authorization: "Basic " + BASIC_TOKEN
    },
    transformRequest: [
      function (data) {
        let ret = "";
        for (let it in data) {
          ret +=
            encodeURIComponent(it) + "=" + encodeURIComponent(data[it]) + "&";
        }
        ret = ret.substr(0, ret.length - 1);
        return ret;
      }
    ],
    data
  });
}

export function logout() {
  return request({
    url: "/login/logout",
    method: "post"
  });
}

// 获取 登录人 权限
export function findRoleByToken() {
  return request({
    url: "/sys/findRoleByToken",
    method: "get"
  });
}

// 修改登录人密码

export function updatePwd(data) {
  return request({
    url: `/sys/updatePwd`,
    method: "post",
    data
  });
}

// 获取未读消息条数  返回 msgUnRead-消息未读，noticeUnRead-公告未读，backlog通知未读
export function findMsgAndNoticeUnRead(data) {
  return request({
    url: `/msg/findMsgAndNoticeUnRead`,
    method: "get",
    data
  });
}

// 获取验证码
export function sendSms(phone) {
  return request({
    url: "/sys/sendSms?phone=" + phone,
    method: "get"
  });
}

// 验证手机号是否重复
export function checkUserPhone(phone) {
  return request({
    url: "/sys/checkUserPhone/" + phone,
    method: "get"
  });
}

// 注册
export function addSysUser(data) {
  return request({
    url: "/sys/addSysUser",
    method: "post",
    headers: {
      Authorization: "Basic " + BASIC_TOKEN
    },
    data
  });
}
