<template>
  <!-- 弹窗 新增/编辑 -->
  <!-- :title="dlgType === 'add' ? '添 加' : '修 改'" -->
  <el-dialog title="分类汇总详情" :close-on-click-modal="false" :visible.sync="dlgState" append-to-body width="1200px" top="30px">
    <div class="gzb-box">
      <el-table
        v-loading="listLoading"
        height="calc(100vh - 300px)"
        class="m-small-table"
        ref="tableRef"
        :data="list"
        border
        fit
        highlight-current-row
        @sort-change="sortChange"
      >
        <el-table-column label="#" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="发放单号" prop="flowNo" width="130" align="center"></el-table-column>
        <el-table-column label="操作人" prop="createUserName" width="100" align="center"></el-table-column>

        <el-table-column label="时间" prop="createTime" width="170" align="center"></el-table-column>

        <el-table-column label="档案名称" prop="archivesName"></el-table-column>
        <el-table-column label="材质" prop="clothMaterialText"></el-table-column>
        <el-table-column label="规格" prop="clothSpecificationText"></el-table-column>
        <el-table-column label="重量" prop="weight" width="100" align="center"></el-table-column>
        <el-table-column label="数量" prop="totalCount" width="100" align="center"></el-table-column>
        <!-- <el-table-column label="总数" prop="totalCount" width="120" align="center"></el-table-column>
        <el-table-column label="科室已入库数量" prop="offsetCount" width="120" align="center"></el-table-column> -->

        <!-- backCount 洗消中心取回数量 -->

        <!-- <el-table-column label="啊啊啊啊" width="120px" align="center" sort-by="label" sortable="custom">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column> -->
      </el-table>
      <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />

      <div class="clear"></div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件

// 工具
// import { phoneReg } from '@/utils/regUtil'
import * as utils from '@/utils'

// 组件
import Pagination from '@/components/Pagination' // 分页

// 接口
import { postAction, getAction } from '@/api'

let listQueryEmpty = {
  page: 1,
  size: 20,
}
export default {
  components: {
    Pagination,
  },
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgData0: {},
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        console.log('val', val)
        setTimeout(() => {
          // this.dlgType1 = this.dlgType.split('-')[0]
          // this.dlgType2 = this.dlgType.split('-')[1]
          // // console.log('this.dlgType1', this.dlgType2 == 'info')
          // // console.log('this.dlgType2', this.dlgType2 == 'shenpi')
          // if (this.dlgType1 == 'chushen') {
          //   if (this.dlgType2 == 'info') {
          //     this.dlgTitle = '初审详情'
          //   }
          //   if (this.dlgType2 == 'shenpi') {
          //     this.dlgTitle = '初审审批'
          //   }
          // }
          // if (this.dlgType1 == 'fushen') {
          //   if (this.dlgType2 == 'info') {
          //     this.dlgTitle = '复审详情'
          //   }
          //   if (this.dlgType2 == 'shenpi') {
          //     this.dlgTitle = '复审审批'
          //   }
          // }
          // this.listQuery.rollId = this.dlgQuery.id

          // this.rowMonthDayNum = new Date(this.dlgQuery.year, this.dlgQuery.month, 0).getDate()
          this.searchFunc()
        }, 50)
      } else {
        this.total = 0
        this.$emit('closeDlg')
      }
    },
  },
  data() {
    return {
      // 弹窗
      dlgTitle: '',
      dlgType1: '',
      dlgType2: '',

      dlgState: false,
      dlgLoading: false,
      dlgData: {},
      dlgRules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        tableId: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      dlgSubLoading: false, // 提交loading
      // 其他数据 下拉框
      aaaaSelect: [], // 关联表单

      // 列表
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
    }
  },
  created() {},
  methods: {
    // -- << 列表
    searchFunc() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.list = []
      this.listLoading = true

      let sendObj = {
        archivesId: this.dlgQuery.archivesId,
        projectId: this.dlgQuery.projectId,
        type: this.dlgQuery.type,
        branchId: this.dlgQuery.branchId || '',
        startDate: this.dlgQuery.startDate,
        endDate: this.dlgQuery.endDate,
        page: this.listQuery.page,
        limit: this.listQuery.size,
      }

      postAction(`/cloth/coflow/archivesFlowInfoPage`, sendObj).then((res0) => {
        let res = res0.data
        this.listLoading = false
        if (res.code == 200) {
          this.list = res.data.records
          this.total = res.data.total

          this.$nextTick(() => {
            this.$refs.tableRef.doLayout()
          })
        } else {
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },

    // 搜索框 清空单个条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },
    // 排序
    sortChange(data) {
      let type = data.column.sortBy
      let order = data.order
      if (order == null) {
        type = ''
        order = ''
      } else {
        if (order == 'descending') {
          order = 'desc'
        } else {
          order = 'asc'
        }
      }
      this.listQuery.sortParam = type
      this.listQuery.sortOrder = order
      this.getList()
    },
    // -- >> 列表

    // 弹窗提交 ------
    dlgSubFunc() {
      this.dlgSubLoading = true

      let sendObj = {
        rollId: this.listQuery.rollId,
      }
      if (this.dlgType1 == 'chushen') {
        sendObj.type = 1
      }
      if (this.dlgType1 == 'fushen') {
        sendObj.type = 2
      }

      // console.log('sendObj', sendObj)
      // return false
      auditPayRoll(sendObj).then((res0) => {
        this.subAjaxBack(res0)
      })
    },
    subAjaxBack(res0) {
      let res = res0.data
      this.dlgSubLoading = false
      if (res.code == 200) {
        this.$message.success(res.msg)
        this.dlgState = false
        this.$emit('getList')
        this.$emit('closeDlg')
      } else {
        this.$message({
          type: 'warning',
          message: res.msg,
        })
      }
    },

    closeDlg() {
      this.dlgLoading = false
      this.dlgSubLoading = false
      // this.$refs['dlgDataForm'].clearValidate()
      this.$emit('closeDlg')
    },

    // 更新外层列表
    upList1() {
      this.$emit('getList')
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.gzb-box {
  // padding: 0 20px;
  .gzb-top,
  .gzb-bottom {
    // margin-bottom: 10px;
    p {
      margin: 0px;
      display: inline-block;
      margin-right: 40px;
    }
  }
  .gzb-bottom {
    margin-top: 10px;
    p {
      margin-right: 20px;
    }
  }
}
</style>