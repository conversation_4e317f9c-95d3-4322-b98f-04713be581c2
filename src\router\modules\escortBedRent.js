/** 陪护床管理 **/
import Layout from "@/views/layout/Layout";

const escortBedRentRouter = {
  path: "/escortBedRent",
  component: Layout,
  name: "escortBedRent",
  meta: {
    title: "租床服务",
    icon: "zcfw",
    roles: ["p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]
  },
  children: [
    {
      path: "escortBedMan",
      component: () => import("@/views/escortBedRent/escortBedMan"),
      name: "陪护床管理",
      meta: {
        title: "陪护床管理",
        roles: ["peihuchuangguanli"]
      },
      children: []
    },
    // 轮椅管理
    {
      path: "wheelchairMan",
      component: () => import("@/views/escortBedRent/wheelchairMan"),
      name: "轮椅管理",
      meta: {
        title: "轮椅管理",
        roles: ["lunyiguanli"]
      },
      children: []
    },

    {
      path: "escortBedPriceMan",
      component: () => import("@/views/escortBedRent/escortBedPriceMan"),
      name: "价格标准管理",
      meta: {
        title: "价格标准管理",
        roles: ["peihuchuangjiageguanli"]
      },
      children: []
    },
    {
      path: "escortBeddingMan",
      component: () => import("@/views/escortBedRent/escortBeddingMan"),
      name: "被褥租用查询",
      meta: {
        title: "被褥租用查询",
        roles: ["beiruguanli"]
      },
      children: []
    },
    {
      path: "escortLockMan",
      component: () => import("@/views/escortBedRent/escortLockMan"),
      name: "蓝牙锁管理",
      meta: {
        title: "蓝牙锁管理",
        roles: ["peihuchuangshebeiguanli"]
      },
      children: []
    },
    {
      path: "escortLockQuery",
      component: () => import("@/views/escortBedRent/escortLockQuery"),
      name: "蓝牙锁电量查询",
      meta: {
        title: "蓝牙锁电量查询",
        roles: ["lanyasuodianliangchaxun"]
      },
      children: []
    },
    {
      path: "escortBedOrder",
      component: () => import("@/views/escortBedRent/escortBedOrder"),
      name: "陪护床订单管理",
      meta: {
        title: "陪护床订单管理",
        roles: ["peihuchuangdingdanguanli"]
      },
      children: []
    },
    {
      path: "wheelchairOrder",
      component: () => import("@/views/escortBedRent/wheelchairOrder"),
      name: "轮椅订单管理",
      meta: {
        title: "轮椅订单管理",
        roles: ["lunyidingdanguanli_web"]
      },
      children: []
    }
  ]
};

export default escortBedRentRouter;
