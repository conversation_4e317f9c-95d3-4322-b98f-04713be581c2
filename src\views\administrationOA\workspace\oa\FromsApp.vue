<template>
  <div
    v-loading="loading"
    style="height: 100%; overflow: auto; box-sizing: border-box"
  >
    <div v-if="!isMobile">
      <!-- <div class="fixed-search hidden-xs-only">
      <el-input
        size="small"
        clearable
        placeholder="搜索表单"
        prefix-icon="el-icon-search"
        v-model="searchForm"
      ></el-input>
    </div> -->
      <div style="padding: 15px; padding-bottom: 0">
        <el-row :gutter="20">
          <el-col
            v-for="(item, index) of navListWeb"
            :key="index"
            :xs="24"
            :sm="8"
            :md="8"
            :lg="8"
            :xl="8"
          >
            <div class="count" @click="toFunc(item.linkUrlWeb)">
              <div>
                <p>{{ item.title }}</p>
                <div>{{ item.num }}</div>
              </div>
              <img :src="item.imgUrlWeb" />
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="group" v-if="searchForm && searchForm.trim() !== ''">
        <div class="group-title">搜索结果</div>
        <div class="group-container">
          <div
            class="group-item"
            v-for="(item, index) in searchResult"
            :key="index"
            @click="enterItem(item)"
          >
            <div>
              <i
                :class="item.logo.icon"
                :style="'background: ' + item.logo.background"
              ></i>
              <ellipsis class="item-name" hover-tip :content="item.formName" />
            </div>
          </div>
          <el-empty
            v-if="searchResult.length === 0"
            :image-size="100"
            :description="`未搜索到 '${searchForm}' 相关表单`"
          ></el-empty>
        </div>
      </div>
      <div v-else>
        <div class="group" v-if="recentlyUsed && recentlyUsed.length > 0">
          <div class="group-title">
            最近使用
            <el-link
              style="float: right"
              :underline="false"
              type="text"
              icon="el-icon-delete"
              @click="clearUsed"
              >清空</el-link
            >
          </div>
          <div class="group-container">
            <div
              class="group-item"
              v-for="(item, index) in recentlyUsed"
              :key="index"
              @click="enterItem(item)"
            >
              <div>
                <i
                  :class="item.logo.icon"
                  :style="'background: ' + item.logo.background"
                ></i>
                <ellipsis
                  class="item-name"
                  hover-tip
                  :content="item.formName"
                />
              </div>
            </div>
          </div>
        </div>

        <div
          class="group"
          v-for="(group, index) in formList.list"
          :key="index"
          v-show="group.items.length > 0 && group.id > 0"
        >
          <div class="group-title">{{ group.name }}</div>
          <div class="group-container">
            <div
              class="group-item"
              v-for="(item, index) in group.items"
              :key="index"
              @click="enterItem(item)"
            >
              <div>
                <i
                  :class="item.logo.icon"
                  :style="'background: ' + item.logo.background"
                ></i>
                <ellipsis
                  class="item-name"
                  hover-tip
                  :content="item.formName"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- closeFree -->
      <w-dialog
        v-if="!isMobile"
        :title="`发起 - ${selectForm.formName}`"
        width="1000px"
        v-model="openItemDl"
        okText="提 交"
        @cancel="openItemDl = false"
        @ok="submitForm"
      >
        <initiate-process
          ref="processForm"
          :code="selectForm.formId"
          v-if="openItemDl"
          @ok="openItemDl = false"
        ></initiate-process>
      </w-dialog>
    </div>

    <div v-else>
      <div class="bg-white" v-if="navList.length">
        <el-row>
          <el-col :span="6" v-for="(item, index) of navList" :key="index">
            <div class="mzg-badge-box padding-tb" @click="appNavClick(item)">
              <img class="img1" :src="item.imgUrl" alt="" />
              <div class="text-center title">{{ item.title }}</div>
              <badge
                v-if="item.num && item.num > 0"
                class="mzg-badge"
                :content="item.num"
                max="99"
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- <div>
        <div
          v-for="(item, index) of navList"
          :key="index"
          @click="appNavClick(item)"
          class="flexc padding-lr bg-white nav-item"
          style="padding-bottom:0"
        >
          <img class="img1" :src="item.imgUrl" alt="" />
          <div class="flex-sub flexc solid-bottom padding-tb">
            <div class="flex-sub" style="padding: 10">
              <div class="text-bold title">{{ item.title }}</div>
              <div class="text-gray">点击查看</div>
            </div>
            <div class="text-red mcount">{{ item.num }}</div>
            <img class="img2" src="@/assets/images/OA/icon_right.png" alt="" />
          </div>
        </div>
      </div> -->

      <!-- 单据列表 -->
      <div class="form-list margin-top bg-white padding-top-sm">
        <!-- 常用功能 -->
        <div
          v-if="recentlyUsed.length"
          class="padding-lr-sm padding-top-sm"
          style="margin-bottom: 10px"
        >
          <div class="text-bold title clearFix">
            最近使用
            <el-link
              style="float: right"
              :underline="false"
              type="text"
              icon="el-icon-delete"
              @click="clearUsed"
              >清空</el-link
            >
          </div>
          <div class="padding-top">
            <el-row>
              <el-col
                :span="6"
                v-for="(item, index) of recentlyUsed"
                :key="index"
              >
                <div @click="enterItem(item)" class="padding-bottom">
                  <div>
                    <i
                      :class="item.logo.icon"
                      :style="'background: ' + item.logo.background"
                    ></i>
                    <ellipsis
                      class="item-name"
                      hover-tip
                      :content="item.formName"
                    />
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 分组 -->
        <div
          v-for="(group, index) in formList.list"
          :key="index"
          class="padding-lr-sm padding-top-sm"
          style="margin-bottom: 10px"
          v-if="group.items.length > 0"
        >
          <div class="text-bold title">{{ group.name }}</div>
          <div class="padding-top">
            <el-row>
              <el-col
                :span="6"
                v-for="(item, index) of group.items"
                :key="index"
              >
                <div @click="enterItem(item)" class="padding-bottom">
                  <div>
                    <i
                      :class="item.logo.icon"
                      :style="'background: ' + item.logo.background"
                    ></i>
                    <ellipsis
                      class="item-name"
                      hover-tip
                      :content="item.formName"
                    />
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getGroupModelsByUser,
  getProcessCountData
} from "@/api/workFlow/modelGroup";
import InitiateProcess from "../InitiateProcess";
import { Badge } from "vant";

export default {
  name: "ApprovalGroup",
  components: { InitiateProcess, Badge },
  data() {
    return {
      taskCount: {
        todo: 0,
        mySubmited: 0,
        cc: 0
      },
      recentlyUsed: [],
      //searchResult: [],
      searchForm: "",
      loading: false,
      openItemDl: false,
      selectForm: {},
      formItem: {},
      actives: [],
      formList: {
        list: [],
        inputs: "",
        searchResult: []
      },
      pending: {
        list: []
      },
      popupStyle: {
        height: "100%",
        width: "100%",
        background: "#f7f7f9"
      },

      // app
      navListAll: [
        {
          title: "待我处理",
          imgUrl: require("@/assets/images/OA/icon_waitido.png"),
          num: "0",
          linkUrl: "unFinished",
          role: "daiwochuli_oa",

          linkUrlWeb: "unFinished",
          imgUrlWeb: require("@/assets/workFlow/image/pending.png")
        },
        {
          title: "我发起的",
          imgUrl: require("@/assets/images/OA/icon_isend.png"),
          num: "",
          linkUrl: "mySubmit",
          role: "wofaqide_oa",

          linkUrlWeb: "mySubmit",
          imgUrlWeb: require("@/assets/workFlow/image/submit.png")
        },
        {
          title: "关于我的",
          imgUrl: require("@/assets/images/OA/icon_aboutus.png"),
          num: "0",
          linkUrl: "ccMe",
          role: "guanyuwode_oa",

          linkUrlWeb: "CcMe",
          imgUrlWeb: require("@/assets/workFlow/image/cc.png")
        },
        {
          title: "已处理",
          imgUrl: require("@/assets/images/OA/yibanli.png"),
          num: "0",
          linkUrl: "finished",
          role: "yichulide_oa"
        }
      ],
      navList: [],
      navListWeb: []
    };
  },

  mounted() {
    if (this.isMobile) {
      console.log("修改 title");
      document.title = "单据提报";
    }

    let userRoles = window.localStorage.userRoles;
    let roles = JSON.parse(decodeURI(userRoles));

    let navList = [];
    for (let item of this.navListAll) {
      if (roles.indexOf(item.role) >= 0) {
        navList.push(item);
      }
    }
    this.navList = navList;

    console.log("===走这了吗");

    this.getGroupModels();
    this.getCountData();
    this.recentlyUsed = JSON.parse(
      localStorage.getItem(`recentlyUsed:${(this.loginUser || {}).id}`) || "[]"
    );
  },
  computed: {
    isMobile() {
      return window.screen.width < 450;
    },
    loginUser() {
      return this.$store.state.loginUser;
    },
    searchResult() {
      let result = [];
      this.formList.list.forEach(group => {
        group.items.forEach(item => {
          if (item.formName.indexOf(this.searchForm) > -1) {
            result.push(item);
          }
        });
      });
      return result;
    }
  },
  methods: {
    // === 移动端
    appNavClick(item) {
      console.log("===appNavClick");
      wx.miniProgram.navigateTo({
        url: `/pages/administrationOA/${item.linkUrl}/${item.linkUrl}`
      });
    },

    ///////////////////
    closed() {
      this.openItemDl = false;
    },
    getGroupModels() {
      this.loading = true;
      const idSet = new Set();
      getGroupModelsByUser()
        .then(rsp => {
          this.loading = false;
          this.formList.list = rsp.data;
          this.formList.list.forEach(group => {
            this.actives.push(group.name);
            group.items.forEach(item => {
              item.logo = JSON.parse(item.logo);
              idSet.add(item.formId);
            });
          });
          this.groups = rsp.data;
          this.filterRecentlyUsed(idSet);
        })
        .catch(err => {
          this.loading = false;
          this.$err(err, "获取分组异常");
          this.recentlyUsed.length = 0;
        });
    },
    async filterRecentlyUsed(collect) {
      this.recentlyUsed = this.recentlyUsed.filter(v => collect.has(v.formId));
    },
    getCountData() {
      getProcessCountData().then(rsp => {
        this.taskCount = rsp.data;

        let navListWeb = [];
        for (let item of this.navList) {
          if (item.title == "待我处理") {
            item.num = this.taskCount.todo || 0;
            navListWeb.push(item);
          }
          if (item.title == "我发起的") {
            item.num = this.taskCount.mySubmited || 0;
            navListWeb.push(item);
          }
          if (item.title == "关于我的") {
            item.num = this.taskCount.cc || 0;
            navListWeb.push(item);
          }
        }

        this.navListWeb = navListWeb;
        console.log("===this.navListWeb", this.navListWeb);
      });
    },
    toFunc(path) {
      if (this.isMobile) {
        console.log("跳转了吗");
        wx.miniProgram.navigateTo({
          url: "/pages/administrationOA/unFinished/unFinished"
        });
        return false;
      }
      this.$router.push("/administrationOA/" + path);
    },
    enterItem(item) {
      if (!this.$isNotEmpty(item.processDefId)) {
        this.$message.warning("该流程还未发布😥");
        return;
      }
      this.selectForm = item;
      this.openItemDl = true;
      this.recentlyUsed.removeByKey("formId", item.formId);
      this.recentlyUsed.unshift(item);
      if (this.recentlyUsed.length >= 20) {
        this.recentlyUsed.splice(1, this.recentlyUsed.length - 1);
      }
      localStorage.setItem(
        `recentlyUsed:${(this.loginUser || {}).id}`,
        JSON.stringify(this.recentlyUsed)
      );
      if (this.isMobile) {
        this.$router.push(
          "/administrationOA/mbInitiateProcess?code=" + item.formId
        );
      }
    },
    clearUsed() {
      this.recentlyUsed = [];
      localStorage.setItem(`recentlyUsed:${(this.loginUser || {}).id}`, "[]");
    },
    submitForm() {
      this.$refs.processForm.validate(valid => {
        if (!this.isMobile) {
          if (valid) {
            this.$refs.processForm.submit();
          } else {
            this.$message.warning("请完善表单😥");
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/workFlow/theme.scss";
@import "~@/assets/css/colorui.scss";
.group {
  border-radius: 15px;
  padding: 20px 20px;
  margin: 0 15px 15px 15px;
  background: $theme-aside-bgc;
  .group-title {
    font-size: 15px;
    color: #303133;
    margin-bottom: 5px;
  }

  .group-container {
  }

  .group-item {
    display: inline-block;
    cursor: pointer;
    border-radius: 10px;
    text-align: center;
    margin: 5px;
    padding: 10px;
    width: 70px;

    i {
      padding: 8px;
      border-radius: 8px;
      font-size: 20px;
      color: #fff;
      background: #38adff;
      height: 36px;
      width: 36px;
      line-height: 20px;
      &:hover {
        box-shadow: 0 0 15px 0 #9f9999;
        //padding: 10px;
      }
    }
    .item-name {
      font-size: 12px;
      color: #303133;
      max-width: 80px;
      margin-top: 3px;
    }

    & > div {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}
.fixed-search {
  position: fixed;
  top: 25px;
  right: 220px;
}
.count {
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  // background: $theme-aside-bgc;
  background: #fff;
  border-radius: 8px;
  height: 80px;
  // margin: 0 15px 20px 15px;
  margin-bottom: 15px;
  & > div {
    left: 15px;
    position: absolute;
    div {
      color: #b9b8b8;
      font-size: 17px;
      font-weight: bold;
    }
  }
  p {
    color: #303133;
    font-size: 14px;
  }
  img {
    position: absolute;
    right: 15px;
    top: 20px;
    width: 40px;
    height: 40px;
  }
  &:hover {
    box-shadow: 0 0 10px #eeeeee;
  }
}

// ---------- 移动端

.mzg-badge-box {
  position: relative;
  background: #fff;
  overflow: auto;
  padding-top: 20px;
  padding-bottom: 20px;
  .mzg-badge {
    position: absolute;
    left: 54%;
    top: 12%;
  }
  .img1 {
    width: 30px;
    height: 30px;
    display: block;
    margin: 0 auto;
  }
  .title {
    font-size: 14px;
    margin-top: 12px;
    font-weight: bold;
  }
}

.nav-item {
  &:last-child {
    .solid-bottom {
      border: none;
    }
  }

  .img2 {
    width: 20px;
    height: 20px;
    margin-top: 1px;
  }
  .mcount {
    font-size: 20px;
    margin-right: 10px;
  }
}
// 列表
.form-list {
  .title {
    font-size: 16px;
  }
  i {
    display: block;
    margin: 0 auto;
    padding: 10px;
    border-radius: 8px;
    font-size: 20px;
    color: #fff;
    background: #38adff;
    height: 40px;
    width: 40px;
    line-height: 20px;
  }
  .item-name {
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    color: #666;
  }
}
</style>
