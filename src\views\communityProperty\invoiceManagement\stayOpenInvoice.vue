<template>
  <!-- 待开发票 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="" prop="status">
          <el-select style="width: 160px" v-model="listQuery.status" filterable clearable placeholder="状态:" @change="communityChange">
            <el-option v-for="item in statusOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="enterpriseName">
          <el-select
            style="width: 260px"
            v-model="listQuery.enterpriseName"
            filterable
            clearable
            placeholder="开票户"
            @change="enterpriseNameChange"
          >
            <el-option v-for="item in houseTypeList" :key="item.index" :label="item" :value="item"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native="getList" placeholder="业主姓名/业主手机号" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList">搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="申请时间" align="center" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开票业主" align="center" width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开票户" align="center" width="250">
          <template slot-scope="scope">
            <span>{{ scope.row.enterpriseName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发票抬头" align="center" width="250">
          <template slot-scope="scope">
            <span>{{ scope.row.invoiceTitle }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发票金额" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发票性质" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.invoiceNature == 0 ? '电子发票' : '纸质发票' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开票时间" align="center" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.auditTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center" prop="status" :formatter="formatState"> </el-table-column>

        <el-table-column label="操作" header-align="center" align="left" width="260" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">详情</el-button>
            <el-button
              v-if="scope.row.status !== '3'"
              type="danger"
              size="mini"
              icon="el-icon-remove-outline"
              plain
              @click="cancellation(scope.row)"
              >作废</el-button
            >
            <el-button
              v-if="scope.row.status == '0' && scope.row.status !== '3'"
              type="success"
              size="mini"
              icon="el-icon-check"
              plain
              @click="makeInvoice(scope.row)"
              >开票</el-button
            >
            <el-button
              v-if="scope.row.status == '99' && scope.row.status !== '3'"
              type="success"
              size="mini"
              icon="el-icon-refresh"
              plain
              @click="anewInvoice(scope.row)"
              >重新申请</el-button
            >
            <el-button
              v-if="scope.row.status == '1' && scope.row.status !== '3'"
              type="warning"
              size="mini"
              icon="el-icon-sold-out"
              plain
              @click="receive(scope.row)"
              >领取</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal="false" :visible.sync="dlgShow" width="900px" append-to-body :title="dlgShowTitle">
      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <div class="infoBox">
          <div style="width: 400px">
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              费用主体:<span class="dlgKey">{{ dlgData.roomName }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              发票性质:<span class="dlgKey">{{ dlgData.invoiceNature == 0 ? '电子发票' : '纸质发票' }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              发票类型:<span class="dlgKey">{{ dlgData.invoiceType == 0 ? '普通发票' : '增值税专用发票' }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              税号:<span class="dlgKey">{{ dlgData.dutyParagraph }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              基本开户账号:<span class="dlgKey">{{ dlgData.bankNum }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              固定电话:<span class="dlgKey">{{ dlgData.telephone }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder" v-if="dlgType !== 'add'">
              开票时间:<span class="dlgKey">{{ dlgData.auditTime }}</span>
            </div>
          </div>
          <div style="width: 300px">
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              业主姓名:<span class="dlgKey">{{ dlgData.userName }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              发票金额:<span class="dlgKey">{{ dlgData.amount }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              抬头:<span class="dlgKey">{{ dlgData.invoiceTitle }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              开户行名称:<span class="dlgKey">{{ dlgData.bankName }}</span>
            </div>
            <div style="margin: 0 0 30px 0; font-weight: bolder">
              地址:<span class="dlgKey">{{ dlgData.address }}</span>
            </div>
            <div style="margin: 0 60px 30px 0; font-weight: bolder" v-if="dlgType == 'edit' || dlgType == 'receive'">
              状态:<span class="dlgKey">{{ editStatus }}</span
              >&nbsp;&nbsp;&nbsp;
              <span style="color: blue" v-if="dlgData.invoiceUrl"><a :href="dlgData.invoiceUrl">下载链接</a></span>
            </div>

            <div style="margin: 0 0 30px 0; font-weight: bolder">
              开票人:<span class="dlgKey">{{ dlgData.userName }}</span>
            </div>
          </div>
          <div style="width: 450px">
            <div style="margin: 0 100px 30px 0; font-weight: bolder">
              业主手机号:<span class="dlgKey">{{ dlgData.phone }}</span>
            </div>
            <div style="margin: 0 100px 30px 0; font-weight: bolder">
              开票户:<span class="dlgKey">{{ dlgData.enterpriseName }}</span>
            </div>
            <!-- <div style="margin:0 60px 30px 0" v-if="dlgType=='edit'||dlgType=='receive'&&invoiceNature==0"><span class="dlgKey">下载链接</span></div> -->
          </div>
        </div>
        <el-form-item label="备注" class="mt20">
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.auditRemark"
            placeholder="请输入备注"
            :disabled="dlgType == 'edit'"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="warning" @click="turnDown" v-if="dlgType == 'add'" icon="el-icon-back">驳回</el-button>
        <el-button v-if="dlgType == 'add'" type="success" :loading="dlgLoading" @click="subAjax" icon="el-icon-check">开票 </el-button>
        <el-button v-if="dlgType == 'receive'" type="success" :loading="dlgLoading" @click="notarizeReceive" icon="el-icon-check"
          >确认领取
        </el-button>
        <el-button :loading="dlgLoading" @click="dlgShow = false" icon="el-icon-close">取消 </el-button>
      </div>
    </el-dialog>

    <!-- 重新申请 -->
    <el-dialog title="重新申请" :visible.sync="anewInvoiceDlg" width="600px" :close-on-click-modal="false">
      <el-form :model="anewInvoiceData" :rules="rulesAnewInvoice" ref="informationChangeForm">
        <el-form-item label="">
          <span style="margin-right: 20px" class="information"
            ><span class="fontLable">费用主体: </span>{{ anewInvoiceData.roomName }}</span
          >
          <span class="information"><span class="fontLable">业主姓名: </span>{{ anewInvoiceData.userName }}</span>
        </el-form-item>
        <el-form-item label="">
          <span style="margin-right: 20px" class="information"><span class="fontLable">业主手机号: </span>{{ anewInvoiceData.phone }}</span>
          <span class="information"><span class="fontLable">开票金额: </span>{{ anewInvoiceData.amount }}</span>
        </el-form-item>
        <el-form-item label="发票性质:" prop="invoiceNature">
          <el-radio-group v-model="anewInvoiceData.invoiceNature">
            <el-radio label="0">电子发票</el-radio>
            <el-radio label="1">纸质发票</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="抬头:">
          <span class="information"> {{ oldInvoiceTitle }}&nbsp;&nbsp;&nbsp;</span>
        </el-form-item>
        <el-form-item label="发票类型:" prop="invoiceType">
          <el-radio-group v-model="anewInvoiceData.invoiceType">
            <el-radio label="0">普通发票</el-radio>
            <el-radio label="1" v-if="invoiceType == 1">增值税专用发票</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="抬头类型:" prop="titleType">
          <el-radio-group v-model="anewInvoiceData.titleType">
            <el-radio label="0">个人</el-radio>
            <el-radio label="1">单位</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票抬头" label-width="90px" prop="invoiceTitle">
          <el-input v-model="anewInvoiceData.invoiceTitle" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          prop="dutyParagraph"
          label="单位税号"
          label-width="90px"
          :rules="
            anewInvoiceData.invoiceType == 1 || anewInvoiceData.titleType == 1
              ? [
                  {
                    required: true,
                    message: '请输入单位税号',
                    trigger: 'blur',
                  },
                  // { pattern: /[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}/, message: '请输入正确单位税号' }
                ]
              : []
          "
        >
          <el-input v-model="anewInvoiceData.dutyParagraph" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          prop="bankName"
          label="开户银行名称"
          label-width="120px"
          :rules="
            anewInvoiceData.invoiceType == 1
              ? [
                  {
                    required: true,
                    message: '请输入银行名称',
                    trigger: 'blur',
                  },
                ]
              : []
          "
        >
          <el-input v-model="anewInvoiceData.bankName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="基本开户账号"
          prop="bankNum"
          label-width="120px"
          :rules="
            anewInvoiceData.invoiceType == 1
              ? [
                  {
                    required: true,
                    message: '请输入开户账号',
                    trigger: 'blur',
                  },
                ]
              : []
          "
        >
          <el-input v-model="anewInvoiceData.bankNum" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="地址"
          prop="address"
          label-width="60px"
          :rules="
            anewInvoiceData.invoiceType == 1
              ? [
                  {
                    required: true,
                    message: '请输入地址',
                    trigger: 'blur',
                  },
                ]
              : []
          "
        >
          <el-input v-model="anewInvoiceData.address" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="固定电话"
          label-width="90px"
          prop="telephone"
          :rules="
            anewInvoiceData.invoiceType == 1
              ? [
                  {
                    required: true,
                    message: '请输入固定电话',
                    trigger: 'blur',
                  },
                ]
              : []
          "
        >
          <el-input v-model="anewInvoiceData.telephone" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="anewInvoiceDlg = false">取 消</el-button>
        <el-button type="primary" @click="subDlg">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { saveOrU, communityPage, roomTypeDel, roomTypePage } from '@/api/communityMan'
import { payInvoicePage, audit, payInvoiceReceive, getTitle, payInvoiceSave, payInvoiceCancel } from '@/api/invoiceManagement'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import memberDlg from '@/components/Dialog/communityMan/memberDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'
//重开
let anewInvoiceDataEmpty = {
  roomId: '',
  roomName: '',
  userName: '',
  userId: '',
  phone: '',
  invoiceNature: '',
  invoiceTitle: '',
  invoiceType: '',
  titleType: '',
  dutyParagraph: '',
  bankName: '',
  bankNum: '',
  address: '',
  telephone: '',
  amount: '',
  billSumId: '',
  projectId: '',
  communityId: '',
  id: '',
  payName: '', //付费对象
  feeFlagName: '', //费用标识
  feeName: '', //费用类型
}

let dlgDataEmpty = {
  auditUserId: '',
  auditUserName: '',

  auditRemark: '',
  status: '',
}

export default {
  name: 'roomInfo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    memberDlg,
  },
  data() {
    return {
      anewInvoiceDlg: false,
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // add\edit\receive
      dlgShowTitle: '', // 标题

      rules: {
        name: [{ required: true, message: '请输入房屋类型', trigger: 'change' }],
        communityId: [{ required: true, message: '请选择小区', trigger: 'change' }],
        mid: [{ required: true, message: '请输入商户号', trigger: 'change' }],
      },
      rulesAnewInvoice: {
        invoiceTitle: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }],
        invoiceNature: [{ required: true, message: '请选择发票性质', trigger: 'change' }],
        invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
        titleType: [{ required: true, message: '请选择抬头类型', trigger: 'change' }],
      },
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgDataOld: JSON.parse(JSON.stringify(dlgDataEmpty)),
      anewInvoiceData: JSON.parse(JSON.stringify(anewInvoiceDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        status: '',
        enterpriseName: '',
      },
      // communityList: [],
      statusOption: [
        {
          value: '0',
          label: '已申请',
        },
        {
          value: '1',
          label: '已开票',
        },
        {
          value: '2',
          label: '已领取',
        },
        {
          value: '3',
          label: '已作废',
        },
        {
          value: '9',
          label: '待开票',
        },
        {
          value: '99',
          label: '已驳回',
        },
      ],
      houseTypeList: [],
      userInfo: {},
      selectRow: {},
      editStatus: '',
      invoiceNature: '', //发票类型
      id: '',
      anewInvoiceData: {}, //重新申请
      oldInvoiceTitle: '', //历史发票抬头
      invoiceType: '', //增值税
    }
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.dlgData.projectId = this.userInfo.projectId
    this.anewInvoiceData.projectId = this.userInfo.projectId
    if (this.$route.query.communityId) {
      this.listQuery.communityId = parseInt(this.$route.query.communityId)
      this.addItem()
    }
    this.getList()
    this.getRoomType()
    // this.getCommunityList();
  },

  methods: {
    formatState(row, column) {
      switch (row.status) {
        case '0':
          return '待开票'
        case '1':
          return '已开票'
        case '2':
          return '已领取'
        case '3':
          return '已作废'
        default:
          return '已驳回'
      }
    },
    // 获取房屋类型列表
    getRoomType() {
      let postParam = {
        page: 1,
        limit: 200,
      }
      roomTypePage(postParam).then((res) => {
        if (res.data.code == 200) {
          if (this.dlgShow) {
            this.houseTypeList = res.data.data
          } else {
            console.log(res.data.data, 'res.data.data')
            let arr = []

            for (let i of res.data.data) {
              arr.push(i.enterpriseName)
              arr = Array.from(new Set(arr))
              this.houseTypeList = arr
              console.log(arr, 'arr')
              console.log(i.enterpriseName, 'i.enterpriseName')
            }
            for (let i = 0; i < this.houseTypeList.length; i++) {
              if (this.houseTypeList[i] == '' || this.houseTypeList[i] == null || typeof this.houseTypeList[i] == 'undefined') {
                this.houseTypeList.splice(i, 1)
                i = i - 1
              }
            }
            return this.houseTypeList
          }
        }
      })
    },
    enterpriseNameChange() {
      let enterpriseName
      if (this.dlgShow) {
        enterpriseName = this.dlgData.enterpriseName
        this.dlgData.floorId = ''
        this.dlgData.unitId = ''
      } else {
        enterpriseName = this.listQuery.enterpriseName
        this.listQuery.floorId = ''
        this.listQuery.unitId = ''
      }
      this.getList(enterpriseName)
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },

    communityChange() {
      let communityId
      if (this.dlgShow) {
        communityId = this.dlgData.communityId
        this.dlgData.floorId = ''
        this.dlgData.unitId = ''
      } else {
        communityId = this.listQuery.communityId
        this.listQuery.floorId = ''
        this.listQuery.unitId = ''
      }
      this.getList(communityId)
    },

    // // 获取小区列表
    // getCommunityList() {
    //   console.log("getCommunityList");
    //   let postParam = {
    //     page: 1,
    //     limit: 200,
    //   };
    //   communityPage(postParam).then((res) => {
    //     if (res.data.code == 200) {
    //       this.communityList = res.data.data;
    //     }
    //   });
    // },

    // 获取数据
    getList() {
      this.list = []
      this.listOld = []
      this.listQuery.projectId = this.userInfo.projectId
      payInvoicePage(this.listQuery).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.data
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 开票弹框
    makeInvoice(row) {
      this.dlgType = 'add'
      this.dlgShowTitle = '开票申请'
      this.id = row.id
      if (row.roomName) {
        this.dlgData.roomName = row.roomName
      } else if (row.parkingName) {
        this.dlgData.roomName = row.parkingName
      } else {
        this.dlgData.roomName = row.garageName
      }
      this.dlgData = JSON.parse(JSON.stringify(row))
      this.dlgDataOld = JSON.parse(JSON.stringify(row))
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },
    // 开票提交接口
    subAjax() {
      let postParam = {
        auditUserId: this.dlgData.userId,
        auditUserName: this.dlgData.userName,
        auditRemark: this.dlgData.auditRemark,
        status: 1,
        id: this.id,
      }
      audit(postParam).then((res) => {
        if (res.data.code == 200) {
          this.getList()
          this.dlgShow = false
          this.$nextTick(() => {
            this.$refs['dlgForm'].clearValidate()
            this.$refs['dlgForm'].resetField()
          })
          this.$message.success('开票成功')
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    //驳回
    turnDown() {
      let postParam = {
        auditUserId: this.dlgData.userId,
        auditUserName: this.dlgData.userName,
        auditRemark: this.dlgData.auditRemark,
        status: 99,
        id: this.id,
      }
      audit(postParam).then((res) => {
        if (res.data.code == 200) {
          this.getList()
          this.dlgShow = false
          this.$nextTick(() => {
            this.$refs['dlgForm'].clearValidate()
            this.$refs['dlgForm'].resetField()
          })
          this.$message.success(res.data.msg)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    //重新开票
    anewInvoice(row) {
      this.anewInvoiceDlg = true
      this.invoiceType = row.invoiceType
      this.anewInvoiceData = JSON.parse(JSON.stringify(row))
      this.anewInvoiceData.projectId = this.userInfo.projectId
      if (row.roomName) {
        this.anewInvoiceData.roomName = row.roomName
      } else if (row.parkingName) {
        this.anewInvoiceData.roomName = row.parkingName
      } else {
        this.anewInvoiceData.roomName = row.garageName
      }
      this.anewInvoiceData.id = row.id
      //  getTitle(row.ownerId).then((res) => {
      //   console.log(res, "res");
      //   if (res.data.code == 200) {
      //     this.oldInvoiceTitle = res.data.data.invoiceTitle;
      //   } else {
      //     this.oldInvoiceTitle = "";
      //   }
      // });
    },
    //提交
    subDlg() {
      this.$refs['informationChangeForm'].validate((valid) => {
        if (valid) {
          payInvoiceSave(this.anewInvoiceData).then((res) => {
            if (res.data.code == 200) {
              this.anewInvoiceDlg = false
              this.getList()
              this.$message.success('提交成功')
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },
    //详情
    editItem(row) {
      this.dlgType = 'edit'
      this.dlgShow = true
      this.dlgShowTitle = '开票详情'
      this.dlgData = JSON.parse(JSON.stringify(row))
      this.dlgDataOld = JSON.parse(JSON.stringify(row))
      this.invoiceNature = row.invoiceNature
      if (row.status == 0) {
        this.editStatus = '待开票'
      } else {
        this.editStatus = '已开票'
      }
    },

    // 领取
    receive(row) {
      this.dlgType = 'receive'
      this.dlgShowTitle = '领取'
      this.dlgShow = true
      this.id = row.id
      this.dlgData = JSON.parse(JSON.stringify(row))
      this.dlgDataOld = JSON.parse(JSON.stringify(row))
      this.invoiceNature = row.invoiceNature
      if (row.status == 0) {
        this.editStatus = '待开票'
      } else {
        this.editStatus = '已开票'
      }
    },
    //作废
    cancellation(row) {
      this.$confirm('确认作废?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let listQuery = {
          id: row.id,
        }
        payInvoiceCancel(listQuery).then((res) => {
          console.log(res, 'res')
          this.getList()
          this.$message.success(res.data.msg)
        })
      })
    },
    //确认领取
    notarizeReceive() {
      let listQuery = {
        id: this.id,
      }
      payInvoiceReceive(listQuery).then((res) => {
        if (res.data.code == 200) {
          this.dlgShow = false
          this.getList()
          this.$message.success('领取成功')
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.dlgKey {
  font-weight: normal;
  display: inline-block;
  margin-left: 5px;
  font-size: 13px;
}
.infoBox {
  display: flex;
  justify-content: space-between;
}
.fontLable {
  font-weight: bolder;
}
</style>


