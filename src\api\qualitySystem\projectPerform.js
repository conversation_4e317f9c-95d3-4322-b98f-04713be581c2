import request from '@/utils/request'

/*
 *项目执行
 */

// 任务详情列表 
export function qworkPage(data) {
  return request({
    url: `/report/qwork/page`,
    method: 'post',
    data
  })
}

// 任务详情列表 web端
export function qworkPageWeb(data) {
  return request({
    url: `/report/qwork/page/web`,
    method: 'post',
    data
  })
}


// 任务详情
export function qworkInfo(id) {
  return request({
    url: `/report/qwork/getInfo/${id}`,
    method: 'get'
  })
}

// 执行管理统计
export function executiveManagementa(data) {
  return request({
    url: `/report/qwork/executiveManagementa`,
    method: 'post',
    data
  })
}
