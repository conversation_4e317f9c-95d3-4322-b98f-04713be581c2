<template>
  <!-- 优惠券设置 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <!-- <el-form-item label="所在小区：">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="公式名称：">
          <el-input @keyup.enter.native='getList' placeholder='请输入公式名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item> -->

        <!-- <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入规则名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item> -->

        <el-button icon="el-icon-search" type="success" size="mini" @click="searchFn">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="showDlg('add')">添加</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <!-- <el-table-column label="公式名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="#" align="center" width="60">
          <template slot-scope="scope">
            {{ (listQuery.page - 1) * listQuery.limit + scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column label="名称" prop="name"> </el-table-column>
        <el-table-column label="截止日期" prop="endTime"> </el-table-column>

        <el-table-column label="收费项目" prop="configName"> </el-table-column>

        <el-table-column label="金额" prop="discount"> </el-table-column>

        <el-table-column label="优惠券数量" prop="total"> </el-table-column>

        <el-table-column label="已用数量" prop="useNum"> </el-table-column>

        <el-table-column label="所属小区" prop="communityName"> </el-table-column>

        <el-table-column label="创建人" prop="createByName"> </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <!-- <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="showDlg('edit', scope.row)"
              >编辑</el-button
            > -->
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="showInfoDlg(scope.row)"
              >详情</el-button
            >
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <!-- 弹窗 新增/编辑 -->
    <addDlg
      :dlgState0="dlgState"
      :dlgData0="dlgData"
      :dlgType="dlgType"
      :dlgQuery="dlgQuery"
      @closeDlg="closeDlg"
      @getList="getList"
    />
    <infoDlg
      :dlgState0="dlgInfoState"
      :dlgData0="dlgInfoData"
      :dlgType="dlgInfoType"
      :dlgQuery="dlgInfoQuery"
      @closeDlg="closeInfoDlg"
      @getList="getList"
    />
  </div>
</template>

<script>
import addDlg from './addDlg'
import infoDlg from './infoDlg'

import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'

import { formulaPage, formulaAddOrUpdate, formulaDel } from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

import { postAction, getAction } from '@/api'

let dlgDataEmpty = {
  id: '',
  communityId: '',
  communityName: '',
  contentJson: [], // 1参数 bud2数值 3符号
  contentValue: '',
  content: '',
  name: '',
}

export default {
  name: 'formulaSetup',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    addDlg,
    infoDlg,
  },
  data() {
    return {
      // 弹窗数据
      dlgQuery: {},
      dlgState: false,
      dlgType: '', // 弹框状态add, edit
      dlgData: {},
      // 详情弹窗
      dlgInfoQuery: {},
      dlgInfoState: false,
      dlgInfoType: '', // 弹框状态add, edit
      dlgInfoData: {},

      /////

      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
      },
      communityList: [],
      userInfo: {},

      ruleTypeList: [
        {
          id: 'builtUpArea',
          name: '建筑面积',
        },
        {
          id: 'insideArea',
          name: '套内面积',
        },
        {
          id: 'layer',
          name: '楼层',
        },
      ],

      symbolList: [
        {
          id: '+',
          name: '+',
        },
        {
          id: '-',
          name: '-',
        },
        {
          id: '*',
          name: '*',
        },
        {
          id: '/',
          name: '/',
        },
        {
          id: '(',
          name: '(',
        },
        {
          id: ')',
          name: ')',
        },
      ],
    }
  },

  created() {
    // this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    this.searchFn()
  },

  methods: {
    // << --- 弹窗 ---
    // -- 表单弹窗
    showDlg(type, row) {
      if (type == 'add') {
        this.dlgQuery = { id: 0 }
      } else {
        this.dlgQuery = row
      }
      this.dlgType = type
      this.dlgState = true
    },
    // 关闭弹窗
    closeDlg() {
      this.dlgState = false
    },

    // 详情弹窗
    // -- 表单弹窗
    showInfoDlg(row) {
      this.dlgInfoQuery = row
      this.dlgInfoType = 'info'
      this.dlgInfoState = true
    },
    // 关闭弹窗
    closeInfoDlg() {
      this.dlgInfoState = false
    },
    // >> --- 弹窗 ---
    //////
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
      }
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    addFormula(type) {
      let param = {
        type,
        value: '',
      }
      if (type == 1) {
        param.list = JSON.parse(JSON.stringify(this.ruleTypeList))
      } else if (type == 3) {
        param.list = JSON.parse(JSON.stringify(this.symbolList))
      }
      this.dlgData.contentJson.push(param)
    },

    delFormula() {
      this.dlgData.contentJson.pop()
      this.calcFormula()
    },

    calcFormula() {
      let content = ''
      let contentValue = ''
      for (let i of this.dlgData.contentJson) {
        contentValue += i.value
        if (i.type == 1) {
          content += utils.getNameById(i.value, i.list)
        } else if (i.type == 2) {
          content += i.value
        } else if (i.type == 3) {
          content += utils.getNameById(i.value, i.list)
        }
      }
      this.dlgData.content = content
      this.dlgData.contentValue = contentValue
    },
    formatList() {},

    searchFn() {
      this.listQuery.page = 1
      this.getList()
    },
    // 获取数据
    getList() {
      this.count++
      this.listLoading = true
      postAction('/unity/coupon/page', this.listQuery).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page.total ? res.data.page.total : 0
          // this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    addItem() {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg() {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.contentJson = JSON.stringify(postParam.contentJson)
          postParam.projectId = this.userInfo.projectId
          postParam.creator = Cookie.get('userId')
          postParam.createName = Cookie.get('userName')
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          this.dlgLoading = true
          formulaAddOrUpdate(postParam).then((res) => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 启用停用
    delItem(data) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        postAction('/unity/coupon/addOrUpdate', { id: data.id, flag: 1 }).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


