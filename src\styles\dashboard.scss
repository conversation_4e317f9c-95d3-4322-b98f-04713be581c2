p {
  margin: 0px;
  padding: 0px;
}
// 绿色： #42b983
.mt10 {
  margin-top: 10px;
}
.mt16 {
  margin-top: 16px;
}
.ml16 {
  margin-left: 16px;
}
.dash-item {
  width: 100%;
  background: #fff;
  border-radius: 3px;
  padding: 16px;
}
.dash-item1 {
  width: 100%;
}
.dash-title {
  padding-bottom: 20px;
  color: #333;
  font-size: 14px;
}
// 主体内容
.main-left-drz {
  // 第一行 待入职，带转正，待调动，待离职
  .erpIndex-wait-item {
    position: relative;
    width: 100%;
    height: 100px;
    background: #fff;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 3px;
    overflow: hidden;
    cursor: pointer;
  }
  .erpIndex-wait-item-l {
    vertical-align: bottom;
    color: #999;
    margin-left: 10px;
    margin-top: 4px;
    .span-1 {
      font-size: 36px;
      color: #333;
    }
  }
  .erpIndex-wait-item-r {

  }
  .erpIndex-wait-item-b {
    position: absolute;
    bottom: 0px;
    left: 0px;
    width: 100%;
    line-height: 30px;
    height: 30px;
    // background-color: #000;
    color: #fff;
    font-size: 14px;
    padding-right: 6px;
    text-align: right;
  }
  .erpIndex-wait-item-b.bg-1 {
    background-image: linear-gradient(-90deg,#87f4e2,#42d2b9);
  }
  .erpIndex-wait-item-b.bg-2 {
    background-image: linear-gradient(-90deg,#81d4ff,#4cb5f2);
  }
  .erpIndex-wait-item-b.bg-3 {
    background-image: linear-gradient(-90deg,#f3dfb3,#ddb146);
  }
  .erpIndex-wait-item-b.bg-4 {
    background-image: linear-gradient(-90deg,#e2e1e5 4%,#cdbed1 96%);
  }
}

// 项的背景白 圆角
.m-dash-bar {
  width: 100%;
  background: #fff;
  border-radius: 3px;
  padding: 16px;
}
.m-dash-bar-title {
  padding-bottom: 20px;
  color: #333;
  font-size: 16px;
}

////// [[ 页面样式
.dash-ad {
  width: 100%;
  
  background: #19AA8D;
  color: #fff;
  text-align: center;
  height: 100px;
  line-height: 1.7;
  box-sizing: border-box;
}
.dash-ad p {
  height: 100%;
  vertical-align: middle;
}


////// ]] 页面样式

