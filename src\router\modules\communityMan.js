/** 小区管理 **/

import Layout from "@/views/layout/Layout";

const communityManRouter = {
  path: "/communityProperty",
  component: Layout,
  name: "communityProperty",
  meta: {
    title: "小区物业",
    icon: "xqwy",
    roles: ["xia<PERSON><PERSON><PERSON><PERSON><PERSON>"]
  },
  children: [
    // 新流程图设置
    // {
    //   path: "processList2",
    //   component: () =>
    //     import("@/views/communityProperty/processMan2/processList/index"),
    //   name: "流程管理",
    //   meta: {
    //     title: "流程管理",
    //     roles: ["liuchengguanli"]
    //   }
    // },
    // {
    //   path: "processSet2",
    //   component: () =>
    //     import("@/views/communityProperty/processMan2/processSet"),
    //   name: "流程设置",
    //   hidden: true,
    //   meta: {
    //     title: "流程设置",
    //     roles: ["liuchengguanli"],
    //     activeMenu: "/communityProperty/processList"
    //   }
    // },

    //
    {
      path: "tenancyPermit",
      component: () => import("@/views/communityProperty/communityMan/index"),

      name: "承租证相关",
      meta: {
        title: "承租证相关",
        roles: ["chengzuzhengxiangguan"]
      },
      children: [
        {
          path: "tenancyContractMan",
          component: () =>
            import(
              "@/views/communityProperty/tenancyPermit/tenancyContractMan/index"
            ),
          name: "承租合同管理",
          meta: {
            title: "承租合同管理",
            roles: ["chengzuhetongguanli"]
          },
          children: []
        }
      ]
    },

    {
      path: "index",
      component: () => import("@/views/communityProperty/index"),
      name: "小区物业首页",
      meta: {
        title: "小区物业首页",
        roles: ["xiaoquwuye"]
      }
    },
    {
      path: "communityMan",
      component: () => import("@/views/communityProperty/communityMan/index"),
      meta: {
        title: "小区管理",
        roles: ["xiaoquguanli"]
      },
      children: [
        {
          path: "activeMan",
          component: () =>
            import("@/views/communityProperty/communityMan/activeMan/index"),
          name: "活动管理",
          meta: {
            title: "活动管理",
            // roles: ["xiaoquguanli"]
            roles: ["huodongguanli_web"]
          }
        },
        {
          path: "activePrize",
          component: () =>
            import("@/views/communityProperty/communityMan/activePrize/index"),
          name: "活动奖品",
          meta: {
            title: "活动奖品",
            // roles: ["xiaoquguanli"]
            roles: ["huodongjiangpin_web"]
          }
        },

        // ///////
        {
          path: "basicInfo",
          component: () =>
            import("@/views/communityProperty/communityMan/basicInfo"),
          name: "基础信息",
          meta: {
            title: "基础信息",
            roles: ["jichuxinxi"]
          }
        },
        {
          path: "buildInfo",
          component: () =>
            import("@/views/communityProperty/communityMan/buildInfo"),
          name: "楼栋信息",
          meta: {
            title: "楼栋信息",
            roles: ["loudongxinxi"]
          }
        },
        {
          path: "unitInfo",
          component: () =>
            import("@/views/communityProperty/communityMan/unitInfo"),
          name: "单元信息",
          meta: {
            title: "单元信息",
            roles: ["danyuanxinxi"]
          }
        },
        {
          path: "roomInfo",
          component: () =>
            import("@/views/communityProperty/communityMan/roomInfo"),
          name: "房屋信息",
          meta: {
            title: "房屋信息",
            roles: ["fangwuxinxi"]
          }
        },
        {
          path: "roomType",
          component: () =>
            import("@/views/communityProperty/communityMan/roomType"),
          name: "房产类型",
          meta: {
            title: "房产类型",
            roles: ["fangchanleixing"]
          }
        },
        {
          path: "garageInfo",
          component: () =>
            import("@/views/communityProperty/communityMan/garageInfo"),
          name: "车库信息",
          meta: {
            title: "车库信息",
            roles: ["chekuxinxi"]
          },
          children: []
        },
        {
          path: "parkingInfo",
          component: () =>
            import("@/views/communityProperty/communityMan/parkingInfo"),
          name: "车位信息",
          meta: {
            title: "车位信息",
            roles: ["cheweixinxi"]
          }
        },
        {
          path: "stewardInfo",
          component: () =>
            import("@/views/communityProperty/communityMan/stewardInfo"),
          name: "物业管家",
          meta: {
            title: "物业管家",
            roles: ["wuyeguanjia"]
          }
        },
        {
          path: "repairFund",
          component: () =>
            import("@/views/communityProperty/communityMan/repairFund"),
          name: "维修基金",
          meta: {
            title: "维修基金",
            roles: ["weixiujijin"]
          }
        },
        {
          path: "adPosition",
          component: () =>
            import("@/views/communityProperty/communityMan/adPosition"),
          name: "广告位管理",
          meta: {
            title: "广告位管理",
            roles: ["guanggaoweiguanli"]
          }
        },
        {
          path: "businessHandle",
          component: () =>
            import("@/views/communityProperty/communityMan/businessHandle"),
          name: "业务受理",
          meta: {
            title: "业务受理",
            roles: ["yewushouli_web"]
          }
        }
      ]
    },
    {
      path: "invoiceManagement",
      component: () =>
        import("@/views/communityProperty/invoiceManagement/index"),
      meta: {
        title: "发票管理",
        roles: ["fapiaoguanli"]
      },
      children: [
        {
          path: "applyForInvoice",
          component: () =>
            import(
              "@/views/communityProperty/invoiceManagement/applyForInvoice"
            ),
          name: "申请开票",
          meta: {
            title: "申请开票",
            roles: ["shenqingkaipiao"]
          },
          children: []
        },
        {
          path: "stayOpenInvoice",
          component: () =>
            import(
              "@/views/communityProperty/invoiceManagement/stayOpenInvoice"
            ),
          name: "待开发票",
          meta: {
            title: "待开发票",
            roles: ["daikaifapiao"]
          },
          children: []
        },
        {
          path: "haveOpenedInvoice",
          component: () =>
            import(
              "@/views/communityProperty/invoiceManagement/haveOpenedInvoice"
            ),
          name: "已开发票",
          meta: {
            title: "已开发票",
            roles: ["yikaifapiao"]
          },
          children: []
        }
      ]
    },
    {
      path: "enterpriseMan",
      component: () => import("@/views/communityProperty/enterpriseMan/index"),
      meta: {
        title: "企业管理",
        roles: ["qiyeguanli"]
      },
      children: [
        {
          path: "enterpriseInfo",
          component: () =>
            import("@/views/communityProperty/enterpriseMan/enterpriseInfo"),
          name: "企业信息",
          meta: {
            title: "企业信息",
            roles: ["qiyexinxi"]
          },
          children: []
        },
        {
          path: "dataScreen",
          component: () =>
            import("@/views/communityProperty/enterpriseMan/dataScreen"),
          name: "数据大屏",
          meta: {
            title: "数据大屏",
            roles: ["shujudaping"]
          },
          children: []
        },
        {
          path: "selfHelpSigned",
          component: () =>
            import("@/views/communityProperty/enterpriseMan/selfHelpSigned"),
          name: "自助签约",
          meta: {
            title: "自助签约",
            roles: ["shujudaping"]
          },
          children: []
        }
      ]
    },
    {
      path: "ownerMan",
      component: () => import("@/views/communityProperty/ownerMan/index"),
      meta: {
        title: "业主管理",
        roles: ["yezhuguanli"]
      },
      children: [
        {
          path: "ownerInfo",
          component: () =>
            import("@/views/communityProperty/ownerMan/ownerInfo"),
          name: "业主信息",
          meta: {
            title: "业主信息",
            roles: ["yezhuxinxi"]
          }
        },
        {
          path: "memberInfo",
          component: () =>
            import("@/views/communityProperty/ownerMan/memberInfo"),
          name: "成员管理",
          meta: {
            title: "成员管理",
            roles: ["chengyuanguanli"]
          }
        }
      ]
    },
    {
      path: "costMan",
      component: () => import("@/views/communityProperty/costMan/index"),
      meta: {
        title: "缴费管理",
        roles: ["jiaofeiguanli"]
      },
      children: [
        {
          path: "receiptManagement",
          component: () => import("@/views/communityProperty/costMan/receiptManagement/index"),
          name: "收据管理",
          meta: {
            title: "收据管理",
            roles: ["shoujuguanli"]  // jiaofeiguanli shoujuguanli
          },
          children: [
            {
              path: "pendingReceipt",
              component: () => import("@/views/communityProperty/costMan/receiptManagement/pendingReceipt/index"),
              name: "待开收据",
              meta: {
                title: "待开收据",
                roles: ["daikaishouju"]  // daikaishouju
              },
              children: []
            },
            {
              path: "receiptInquiry",
              component: () => import("@/views/communityProperty/costMan/receiptManagement/receiptInquiry/index"),
              name: "收据查询",
              meta: {
                title: "收据查询",
                roles: ["shoujuchaxun"]  // shoujuchaxun
              },
              children: [
    
    
              ]},
          ]
        },
        // receiptMan
        // 菜单
        // 缴费管理-收据管理-待开收据/收据查询     
      
        {
          path: "index",
          component: () => import("@/views/communityProperty/index"),
          name: "小区物业首页",
          meta: {
            title: "小区物业首页",
            roles: ["xiaoquwuye"]
          }
        },
        {
          path: "index",
          component: () => import("@/views/communityProperty/index"),
          name: "小区物业首页",
          meta: {
            title: "小区物业首页",
            roles: ["xiaoquwuye"]
          }
        },
        //
        {
          path: "couponMan",
          component: () =>
            import("@/views/communityProperty/costMan/couponMan/index"),
          name: "优惠券设置",
          meta: {
            title: "优惠券设置",
            roles: ["youhuijuanshezhi"]
          },
          children: []
        },
        ////
        {
          path: "formulaSetup",
          component: () =>
            import("@/views/communityProperty/costMan/formulaSetup"),
          name: "计费公式设置",
          meta: {
            title: "计费公式设置",
            roles: ["jifeigongshishezhi"]
          },
          children: []
        },
        {
          path: "billingSetup",
          component: () =>
            import("@/views/communityProperty/costMan/billingSetup"),
          name: "计费设置",
          meta: {
            title: "计费设置",
            roles: ["jifeishezhi"]
          },
          children: []
        },
        {
          path: "discountSetup",
          component: () =>
            import("@/views/communityProperty/costMan/discountSetup"),
          name: "滞纳金设置",
          meta: {
            title: "滞纳金设置",
            roles: ["zhekoushezhi"]
          },
          children: []
        },
        {
          path: "billingSetupDesc/:id",
          component: () =>
            import("@/views/communityProperty/costMan/billingSetupDesc"),
          name: "计费滞纳金设置",
          meta: {
            title: "计费滞纳金设置",
            roles: ["jifeishezhi"]
          },
          children: [],
          hidden: true
        },
        {
          path: "roomPay",
          component: () => import("@/views/communityProperty/costMan/roomPay"),
          name: "房屋缴费",
          meta: {
            title: "房屋缴费",
            roles: ["fangwujiaofei"],
            keepAlive: true 
          },
          children: []
        },
        {
          path: "garagePay",
          component: () =>
            import("@/views/communityProperty/costMan/garagePay"),
          name: "车库缴费",
          meta: {
            title: "车库缴费",
            roles: ["chekujiaofei"],
            keepAlive: true
          },
          children: []
        },
        {
          path: "parkingPay",
          component: () =>
            import("@/views/communityProperty/costMan/parkingPay"),
          name: "车位缴费",
          meta: {
            title: "车位缴费",
            roles: ["cheweijiaofei"],
            keepAlive: true
          },
          children: []
        },
        {
          path: "payCost/:id/:type",
          component: () => import("@/views/communityProperty/costMan/payCost"),
          name: "欠费催缴",
          meta: {
            title: "欠费催缴",
            roles: ["fangwujiaofei", "chekujiaofei", "cheweijiaofei"]
          },
          children: [],
          hidden: true
        },
        {
          path: "payCostDesc/:id/:type",
          component: () =>
            import("@/views/communityProperty/costMan/payCostDesc"),
          name: "查看费用",
          meta: {
            title: "查看费用",
            roles: ["fangwujiaofei", "chekujiaofei", "cheweijiaofei"]
          },
          children: [],
          hidden: true
        },
        {
          path: "discountApply",
          component: () =>
            import("@/views/communityProperty/costMan/discountApply"),
          name: "优惠申请",
          meta: {
            title: "优惠申请",
            roles: ["youhuishenqing"]
          },
          children: []
        },
        {
          path: "discountApplyQuery",
          component: () =>
            import("@/views/communityProperty/costMan/discountApplyQuery"),
          name: "优惠申请记录",
          meta: {
            title: "优惠申请记录",
            roles: ["youhuishenqingchaxun"]
          },
          children: []
        },
        {
          path: "discountAudit",
          component: () =>
            import("@/views/communityProperty/costMan/discountAudit"),
          name: "优惠审核",
          meta: {
            title: "优惠审核",
            roles: ["youhuishenhe"]
          },
          children: []
        },
        {
          path: "payCostConfirm",
          component: () =>
            import("@/views/communityProperty/costMan/payCostConfirm"),
          name: "收费确认",
          meta: {
            title: "收费确认",
            roles: ["querenshoufei"]
          },
          children: []
        },
        {
          path: "oweInfo",
          component: () => import("@/views/communityProperty/costMan/oweInfo"),
          name: "欠费信息",
          meta: {
            title: "欠费信息",
            roles: ["qianfeixinxi"]
          },
          children: []
        }
      ]
    },
    {
      path: "punchMan",
      component: () => import("@/views/communityProperty/punchMan/index"),
      meta: {
        title: "打卡管理",
        roles: ["wuyedakaguanli"]
      },
      children: [
        {
          path: "groupMan",
          component: () =>
            import("@/views/communityProperty/punchMan/groupMan"),
          name: "考勤组管理",
          meta: {
            title: "考勤组管理",
            roles: ["wuyerenlianshenhe"]
          }
        },
        {
          path: "faceAudit",
          component: () =>
            import("@/views/communityProperty/punchMan/faceAudit"),
          name: "人脸审核",
          meta: {
            title: "人脸审核",
            roles: ["wuyerenlianshenhe"]
          }
        },
        {
          path: "punchStatistic",
          component: () =>
            import("@/views/communityProperty/punchMan/punchStatistic"),
          name: "打卡统计",
          meta: {
            title: "打卡统计",
            roles: ["wuyedakatongji"]
          },
          children: []
        },
        {
          path: "outgoingStatistics",
          component: () =>
            import("@/views/communityProperty/punchMan/outgoingStatistics"),
          name: "外出统计",
          meta: {
            title: "外出统计",
            roles: ["waichutongji"]
          },
          children: []
        }
      ]
    },
    {
      path: "repairMan",
      component: () => import("@/views/communityProperty/repairMan/index"),
      meta: {
        title: "报修管理",
        roles: ["baoxiuguanli"]
      },
      children: [
        {
          path: "repairSubjectSetup",
          component: () =>
            import("@/views/communityProperty/repairMan/repairSubjectSetup"),
          name: "报修科目设置",
          meta: {
            title: "报修科目设置",
            roles: ["baoxiukemushezhi"]
          },
          children: []
        },
        {
          path: "repairSetup",
          component: () =>
            import("@/views/communityProperty/repairMan/repairSetup"),
          name: "报修设置",
          meta: {
            title: "报修设置",
            roles: ["baoxiushezhi"]
          },
          children: []
        },
        {
          path: "telRepair",
          component: () =>
            import("@/views/communityProperty/repairMan/telRepair"),
          name: "电话报修",
          meta: {
            title: "电话报修",
            roles: ["dianhuabaoxiu"]
          },
          children: []
        },
        {
          path: "repairPool",
          component: () =>
            import("@/views/communityProperty/repairMan/repairPool"),
          name: "工单池",
          meta: {
            title: "工单池",
            roles: ["gongdanchi"]
          },
          children: []
        },
        {
          path: "repairTodo",
          component: () =>
            import("@/views/communityProperty/repairMan/repairTodo"),
          name: "报修待办",
          meta: {
            title: "报修待办",
            roles: ["baoxiudaiban"]
          },
          children: []
        },
        {
          path: "repairDone",
          component: () =>
            import("@/views/communityProperty/repairMan/repairDone"),
          name: "报修已办",
          meta: {
            title: "报修已办",
            roles: ["baoxiuyiban"]
          },
          children: []
        },
        {
          path: "repairConfirm",
          component: () =>
            import("@/views/communityProperty/repairMan/repairConfirm"),
          name: "报修费用确认",
          meta: {
            title: "报修费用确认",
            roles: ["baoxiufeiyongqueren"]
          },
          children: []
        }
      ]
    },
    {
      path: "visitorMan",
      component: () => import("@/views/communityProperty/visitorMan/index"),
      meta: {
        title: "访客管理",
        roles: ["fangkeguanli"]
      },
      children: [
        {
          path: "visitorReg",
          component: () =>
            import("@/views/communityProperty/visitorMan/visitorReg"),
          name: "访客登记",
          meta: {
            title: "访客登记",
            roles: ["fangkedengji"]
          }
        },
        {
          path: "visitorRecord",
          component: () =>
            import("@/views/communityProperty/visitorMan/visitorRecord"),
          name: "访客记录",
          meta: {
            title: "访客记录",
            roles: ["fangkejilu"]
          },
          children: []
        }
      ]
    },
    {
      path: "complaintMan",
      component: () => import("@/views/communityProperty/complaintMan/index"),
      meta: {
        title: "投诉咨询",
        roles: ["tousuzixun"]
      },
      children: [
        {
          path: "complaintReg",
          component: () =>
            import("@/views/communityProperty/complaintMan/complaintReg"),
          name: "投诉登记",
          meta: {
            title: "投诉登记",
            roles: ["tousudengji"]
          },
          children: []
        },
        {
          path: "complaintTodo",
          component: () =>
            import("@/views/communityProperty/complaintMan/complaintTodo"),
          name: "投诉待办",
          meta: {
            title: "投诉待办",
            roles: ["tousudaiban"]
          },
          children: []
        },
        {
          path: "complaintDone",
          component: () =>
            import("@/views/communityProperty/complaintMan/complaintDone"),
          name: "投诉已办",
          meta: {
            title: "投诉已办",
            roles: ["tousuyiban"]
          },
          children: []
        }
      ]
    },
    {
      path: "reportMan",
      component: () => import("@/views/communityProperty/reportMan/index"),
      meta: {
        title: "报表统计",
        roles: ["baobiaotongji"]
      },
      children: [
        {
          path: "dailySettlementTable",
          component: () =>
            import("@/views/communityProperty/reportMan/dailySettlementTable"),
          name: "物业收费日结表",
          meta: {
            title: "物业收费日结表",
            roles: ["wuyeshoufeirijiebiao"]
          }
        },
        {
          path: "summaryTable",
          component: () =>
            import("@/views/communityProperty/reportMan/summaryTable"),
          name: "物业收费汇总表",
          meta: {
            title: "物业收费汇总表",
            roles: ["wuyeshoufeihuizongbiao"]
          }
        },
        {
          path: "ownerReport",
          component: () =>
            import("@/views/communityProperty/reportMan/ownerReport"),
          name: "业主统计表",
          meta: {
            title: "业主统计表",
            roles: ["yezhutongjibiao"]
          }
        },
        {
          path: "paySummary",
          component: () =>
            import("@/views/communityProperty/reportMan/paySummary"),
          name: "缴费汇总表",
          meta: {
            title: "缴费汇总表",
            roles: ["jiaofeihuizongbiao"]
          }
        },
        {
          path: "payDetail",
          component: () =>
            import("@/views/communityProperty/reportMan/payDetail"),
          name: "缴费明细表",
          meta: {
            title: "缴费明细表",
            roles: ["jiaofeimingxibiao"]
          }
        },
        {
          path: "oweStatistic",
          component: () =>
            import("@/views/communityProperty/reportMan/oweStatistic"),
          name: "欠费明细",
          meta: {
            title: "欠费明细",
            roles: ["qianfeitongjibiao"]
          }
        },
        {
          path: "complaintReport",
          component: () =>
            import("@/views/communityProperty/reportMan/complaintReport"),
          name: "投诉咨询明细",
          meta: {
            title: "投诉咨询明细",
            roles: ["tousuzixuntongji"]
          },
          children: []
        },
        {
          path: "repairReport",
          component: () =>
            import("@/views/communityProperty/reportMan/repairReport"),
          name: "报修明细",
          meta: {
            title: "报修明细",
            roles: ["baoxiutongji"]
          },
          children: []
        },
        {
          path: "adPosStatistic",
          component: () =>
            import("@/views/communityProperty/reportMan/adPosStatistic"),
          name: "广告位统计",
          meta: {
            title: "广告位统计",
            roles: ["guanggaoweitongji"]
          },
          children: []
        },
        {
          path: "repairStatistic",
          component: () =>
            import("@/views/communityProperty/reportMan/repairStatistic"),
          name: "工单统计",
          meta: {
            title: "工单统计",
            roles: ["gongdantongji"]
          },
          children: []
        }
      ]
    }
  ]
};

export default communityManRouter;
