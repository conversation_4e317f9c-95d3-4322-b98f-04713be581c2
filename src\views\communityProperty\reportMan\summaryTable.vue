<!-- 物业收费汇总表 -->
<template>
    <div class="app-container" ref="orderQuery">
        <div class="filter-container">
            <el-form inline :model="listQuery" @submit.native.prevent>

                <el-form-item label="选择小区" prop="communityId">
                    <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
                        <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <!-- <el-form-item label="关键字：">
                    <el-input @keyup.enter.native='getList' style="width:205px" placeholder=''
                        v-model="listQuery.label">
                        <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
                    </el-input>
                </el-form-item> -->
                <el-form-item label="选择日期:">
                    <el-date-picker v-model="listQuery.rangeDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>

                <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
                <el-button icon="el-icon-download" type="primary" size="mini" @click="showDlg">导出</el-button>
                <el-popover placement="bottom" width="800" trigger="manual" v-model="popShow">
                    <div class="m-page-con">
                        <el-table class="m-small-table" border height="345" fit highlight-current-row :data="exportList">
                            <el-table-column label="#" type="index" align="center" width="60"> </el-table-column>
                            <el-table-column label="备注" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <span>{{ scope.row.remark }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="创建时间">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.createTime }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
                                <template slot-scope="scope">
                                    <el-button v-if="scope.row.fileUrl" @click="downloadItem(scope.row.fileUrl)"
                                        type="primary" size="mini" icon="el-icon-edit" plain>
                                        下载
                                    </el-button>
                                    <el-button type="danger" size="mini" icon="el-icon-delete" @click="delItem(scope.row)"
                                        plain>删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="page-container">
                        <pagination :total="totalExport" :page.sync="listQueryExport.page"
                            :limit.sync="listQueryExport.limit" @pagination="getExportList" />
                    </div>
                    <el-button style="margin-left: 9px" slot="reference" icon="el-icon-check" type="success" size="mini"
                        @click.stop="viewItem">查看导出</el-button>
                </el-popover>
            </el-form>
        </div>
        <div class="table-container">
            <el-table ref="tableData" class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit
                highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
                <el-table-column label="序号" type="index" align="center" width="60">
                </el-table-column>

                <el-table-column label="缴费日期">
                    <template slot-scope="scope">
                        <span>{{ scope.row.pay_date }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="物业费">
                    <template slot-scope="scope">
                        <span>{{ scope.row.wuyefei }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="电梯费">
                    <template slot-scope="scope">
                        <span>{{ scope.row.diantifei }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="车位管理费">
                    <template slot-scope="scope">
                        <span>{{ scope.row.cheweiguanlifei }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="车位租金">
                    <template slot-scope="scope">
                        <span>{{ scope.row.cheweizujin }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="其他费用">
                    <template slot-scope="scope">
                        <span>{{ scope.row.qitafeiyong }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="缴费属期">
                    <template slot-scope="scope">
                        <span>{{ scope.row.bill_date }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="缴费方式" align="center">
                    <el-table-column prop="wxPay" label="微信" width="120">
                    </el-table-column>
                    <el-table-column prop="otherPay" label="微信其他缴费" width="120">
                    </el-table-column>
                    <el-table-column prop="zfbPay" label="支付宝" width="120">
                    </el-table-column>
                    <el-table-column prop="cashPay" label="现金" width="120">
                    </el-table-column>
                </el-table-column>
                <el-table-column label="实收金额">
                    <template slot-scope="scope">
                        <span>{{ scope.row.amountPay }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="备注" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.remark }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="page-container">
            <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
        </div>
        <el-dialog title="导出" :close-on-click-modal="false" :visible.sync="dlgState" width="600px" append-to-body>
            <el-form ref="dlgForm" :rules="dlgRules" :model="dlgData" label-position="right" label-width="90px" size="mini"
                @submit.native.prevent>
                <el-form-item label="备注" prop="remark">
                    <el-input :rows="3" type="textarea" resize="none" v-model="dlgData.remark" placeholder="请输入内容" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dlgState = false" icon="el-icon-close">关闭</el-button>
                <el-button v-if="dlgType != 'desc'" type="success" @click="exportExcel" icon="el-icon-check">
                    <span>提交</span>
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>
  
<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'

import {
    getSumInfo,
} from '@/api/reportMan'

import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'
import { postAction, getAction } from '@/api'
let dlgDataEmpty = {

}
export default {
    name: 'ownerReport',
    extends: WorkSpaceBase,
    components: {
        Pagination,
    },
    data() {
        return {

            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - (1000 * 60 * 60 * 24);
                },
            },
            // 弹窗 类型
            dlgShow: false,  // 新增
            dlgType: '',    // ADD\EDIT
            dlgTitle: '', // 标题
            // 弹窗数据
            dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
            count: 0,
            list: [],
            total: 0,
            listLoading: false,
            dlgLoading: false,
            listQuery: {
                page: 1,
                limit: 20,
                // label: '',
                communityId: '',
                rangeDate:[]
            },
            userInfo: {},
            communityList: [],
            buildingList: [],
            unitList: [],
            roomList: [],
            typeList: [
                {
                    id: 1,
                    name: '投诉'
                },
                {
                    id: 2,
                    name: '建议'
                },
            ],
            dlgState: false,
            popShow: false,
            // 表单验证
            dlgRules: {
                remark: [{ required: true, message: '必填字段', trigger: 'blur' }],
            },
            listQueryExport: {
                userId: '',
                page: 1,
                limit: 20,
                tableType: '',
                processType: ''
            },
            totalExport:0,
            exportList:[]
        }
    },
    created() {
        this.getCommunityList()
        this.userInfo = JSON.parse(window.localStorage.userInfo)
    },

    mounted() {
        this.$nextTick(() => {
            $(this.$refs.orderQuery)[0].addEventListener('click', () => {
                this.popShow = false
            })
        })
    },
    methods: {
        showDlg() {
            if (utils.isNull(this.listQuery.communityId)) {
                this.$message.warning("请选择小区")
                return
            }
            if (this.listQuery.rangeDate.length<=0) {
                this.$message.warning("请选择日期")
                return
            }
            this.popShow = false
            this.dlgState = true
            this.dlgData=JSON.parse(JSON.stringify(dlgDataEmpty))
            this.$nextTick(()=>{
                this.$refs.dlgForm.clearValidate();
            })
        },

        exportExcel() {
            this.$refs.dlgForm.validate((valid) => {
                if (valid) {
                    // 导出
                    let sendObj = JSON.parse(JSON.stringify(this.dlgData))
                    // sendObj.userId = this.userInfo.id
                    sendObj.label = this.listQuery.label
                    sendObj.communityId = this.listQuery.communityId
                    sendObj.page=this.listQuery.page
                    sendObj.limit=this.listQuery.communityId
                    sendObj.startDate = this.listQuery.rangeDate[0]
                    sendObj.endDate = this.listQuery.rangeDate[1]
                    // sendObj.tableType = 'huizongbiao'
                    let loading = this.$loading({
                        lock: true,
                        text: "导出中",
                        background: "rgba(0, 0, 0, 0.7)"
                    });
                    postAction("/unity/payBillDayRecord/getSumInfoExport", sendObj).then(res => {
                        loading.close();
                        if (res.data.code == 200) {
                            this.dlgState = false
                            this.$message.success("操作成功，请在查看导出中下载");
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    });
                }})

        },
        // 查看
        viewItem() {
            this.popShow = !this.popShow
            if (this.popShow) {
                this.getExportList()
            }
        },
        getExportList() {
            this.listQueryExport.userId = this.userInfo.id
            this.listQueryExport.tableType = 'huizongbiao'
            postAction('/unity/listBillExportDownload',this.listQueryExport).then((res) => {
                if (res.data.code == 200) {
                    this.totalExport = res.data.page ? res.data.page.total : 0
                    this.exportList = res.data.data ? JSON.parse(JSON.stringify(res.data.data)) : []
                }
            })
        },
        // 下载
        downloadItem(url) {
            window.open(url, '_blank')
        },
        // 删除
        delItem(data) {
            this.$confirm('此操作将删除该记录, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    getAction(`/unity/delBillExportDownload/${data.id}`).then((res) => {
                        if (res.data.code == 200) {
                            this.$message.success(res.data.msg)
                            this.getExportList()
                        } else {
                            this.$message.error(res.data.msg)
                        }
                    })
                })
                .catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除',
                    })
                })
        },
        // 获取小区列表
        getCommunityList() {
            let postParam = {
                page: 1,
                limit: 200
            }
            communityPage(postParam).then(res => {
                if (res.data.code == 200) {
                    this.communityList = res.data.data
                }
            })
        },

        resetSearchItem(arr) {
            for (let item of arr) {
                this.listQuery[item] = ''
            }
        },

        formatList() {
            for (let i of this.list) {
                i.communityName = utils.getNameById(this.listQuery.communityId, this.communityList)
            }
        },

        // 获取数据
        getList() {
            if (utils.isNull(this.listQuery.communityId)) {
                this.$message.warning("请选择小区")
                return
            }
            if (this.listQuery.rangeDate.length<=0) {
                this.$message.warning("请选择日期")
                return
            }
            this.list =[]
            this.count++
            this.listLoading = true
            let sendObj = JSON.parse(JSON.stringify(this.listQuery))
            sendObj.startDate=sendObj.rangeDate[0]
            sendObj.endDate=sendObj.rangeDate[1]
            getSumInfo(sendObj).then(res => {
                this.listLoading = false
                if (res.data.code == 200) {
                    this.list = JSON.parse(JSON.stringify(res.data.data))
                    this.formatList()
                    this.total = res.data.page ? res.data.page.total : 0
                } else {
                    this.$message.error(res.data.msg)
                }
            })
        },

    }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
    text-align: center;
}
</style>
  
  
  