/** 报警配置 **/

import Layout from "@/views/layout/Layout";

const alarmConfigurationRouter = {
  path: "/alarmConfiguration",
  component: Layout,
  name: "alarmConfiguration",
  meta: {
    title: "报警配置",
    icon: "bjpz",
    roles: ["dian<PERSON><PERSON><PERSON><PERSON>_baojingpeizhi"]
  },
  children: [
    {
      path: "ruleConfiguration",
      component: () => import("@/views/alarmConfiguration/ruleConfiguration"),
      name: "规则配置",
      meta: {
        title: "规则配置",
        roles: ["baojingpeizhi_guizepeizhi"]
      }
    },
    {
      path: "smokeDetector",
      component: () => import("@/views/alarmConfiguration/alarmConfiguration"),
      name: "报警配置",
      meta: {
        title: "报警配置",
        roles: ["baojingpeizhi_baojingpeizhi"]
      }
    },
    {
      path: "alarmQuery",
      component: () => import("@/views/alarmConfiguration/alarmQuery/index"),
      meta: {
        title: "报警查询",
        roles: ["baojingpeizhi_baojingchaxun"]
      },
      children: [
        {
          path: "telephoneAlarmQuery",
          component: () => import("@/views/alarmConfiguration/alarmQuery/telephoneAlarmQuery"),
          name: "电话报警查询",
          meta: {
            title: "电话报警查询",
            roles: ["baojingpeizhi_dianhuabaojingchaxun"]
          },
          children: []
        },
        {
          path: "smsAlarmQuery",
          component: () => import("@/views/alarmConfiguration/alarmQuery/smsAlarmQuery"),
          name: "短信报警查询",
          meta: {
            title: "短信报警查询",
            roles: ["baojingpeizhi_duanxinbaojingchaxun"]
          },
          children: []
        },
      ]
    }

  ]
};

export default alarmConfigurationRouter;
