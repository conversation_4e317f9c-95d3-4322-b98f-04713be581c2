<template>
  <el-dialog :close-on-click-modal="false" :title="'选择部门'" :visible.sync="dlgShow" @close="closeDlg">
    <el-input placeholder="输入名称进行过滤" v-model="filterText"> </el-input>
    <el-tree ref="treeDom" highlight-current node-key="id" :data="list" @node-click="nodeClick" :default-expanded-keys="defaultOpenList" :filter-node-method="filterNode" :expand-on-click-node="false">
    </el-tree>
    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-span" v-if="selectName">当前选中：{{ selectName }}</span>
      <el-button icon="el-icon-back" @click="closeDlg"> 取 消 </el-button>
      <el-button icon="el-icon-check" type="success" @click="submitDlg"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'
import * as utils from '@/utils'
import { findBranchTreeByProject } from '@/api/propertyMan/deptMaintenance.js'

export default {
  components: {
  },

  props: {
    superDlgShow: {
      type: Boolean,
      required: true,
      default: false,
    },

    superDlgType: {
      type: String,
      required: false,
      default: '',
    },

    superProjectId: {
      type: Number | String,
      required: false,
      default: ''
    },

    superSelectId: {
      type: Number | String,
      required: false,
      default: ''
    },

    superSelectName: {
      type: String,
      required: false,
      default: ''
    },
  },

  data () {
    return {
      dlgShow: this.superDlgShow,

      defaultOpenList: [],

      list: [],

      filterText: '',

      selectId: '',

      selectName: '',
    }
  },

  computed: {


  },

  watch: {
    filterText (val) {
      this.$refs.treeDom.filter(val);
    },

    superDlgShow: {
      immediate: true,
      handler (val) {
        this.dlgShow = val
        if (val) {
          this.selectId = this.superSelectId
          this.selectName = this.superSelectName
          this.getList()
        }
      }
    },

  },

  methods: {
    filterNode (value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 树节点点击
    nodeClick (data, node) {
      this.selectId = data.id
      this.selectName = data.label
    },

    getList () {
      this.list = []
      findBranchTreeByProject(this.superProjectId).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.list
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = this.list.length > 0 ? [this.list[0]['id']] : []
          }
          this.$nextTick(() => {
            this.$refs.treeDom.setCurrentKey(this.selectId)
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    submitDlg () {
      if (utils.isNull(this.selectId)) {
        this.$message.warning('请选择部门')
        return
      }
      let superParam = {
        selectId: this.selectId,
        selectName: this.selectName
      }
      this.$emit('superFunc', superParam)
      this.closeDlg()
    },

    closeDlg () {
      this.$emit('update:superDlgShow', false)
    }


  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 800px;

  .el-dialog__body {
    height: calc(100% - 110px);

    .el-tree {
      margin-top: 10px;
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
}
</style>