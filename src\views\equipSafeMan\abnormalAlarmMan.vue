<template>
  <!-- 异常报警管理 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline @submit.native.prevent>
        <el-form-item label="创建时间:">
          <el-date-picker
            v-model="listQuery.dateRange"
            type="daterange"
            range-separator="~"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="listQuery.label"
            placeholder="请输入关键字"
            style="width: 140px"
          >
            <i
              @click="clearQuery(['label'])"
              slot="suffix"
              class="el-input__icon el-icon-error"
            ></i>
          </el-input>
        </el-form-item>
        <el-form-item
          label=""
          v-if="$route.path == '/dataCenter/abnormalAlarmMan'"
        >
          <el-select
            style="width: 140px"
            v-model="listQuery.equipType"
            clearable
            placeholder="请选择分类"
          >
            <el-option
              v-for="item of equipTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-cascader
            style="width: 140px"
            ref="refSubCat"
            clearable
            placeholder="请选择区域"
            :props="{ value: 'id', label: 'name', emitPath: false }"
            :show-all-levels="false"
            @change="areaChange"
            v-model="listQuery.areaId"
            :options="areaList"
          ></el-cascader>
        </el-form-item>

        <el-form-item>
          <el-select
            v-model="listQuery.equipRoomIds"
            clearable
            multiple
            collapse-tags
            placeholder="请选择设备间"
            style="width: 210px"
          >
            <el-option
              v-for="item of sbjSelect"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-select
            v-model="listQuery.status"
            clearable
            placeholder="处理状态"
            style="width: 100px"
          >
            <el-option
              v-for="item of statusSelect"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="handlerResult">
          <el-select
            clearable
            style="width: 200px;"
            v-model="listQuery.handlerResult"
            placeholder="处理结果"
          >
            <el-option
              v-for="item in gjSelect"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="listQuery.alertType"
            clearable
            filterable
            placeholder="异常类型"
            style="width: 140px"
          >
            <el-option
              v-for="item of alertSelect"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          @click="searchFunc"
          >搜索</el-button
        >
        <el-button
          class="search-right-btn"
          @click="downLoadFunc"
          icon="el-icon-download"
          size="mini"
          >导出</el-button
        >
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        class="m-small-table el-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :row-class-name="tableRowClassName"
      >
        <el-table-column label="序号" width="60" align="center">
          <template slot-scope="scope">
            <span>{{
              (listQuery.page - 1) * listQuery.limit + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警时间" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="分类"
          width="90"
          show-overflow-tooltip
          v-if="$route.path == '/dataCenter/abnormalAlarmMan'"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.equipTypeStr }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警对象">
          <template slot-scope="scope">
            <span
              v-if="scope.row.type == 0"
              class="m-a"
              @click="showBjDia(scope.row)"
              >{{ scope.row.nodeName }}</span
            >
            <span v-else>{{ scope.row.nodeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警类型">
          <template slot-scope="scope">
            <span>{{ scope.row.alertName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警级别" width="90" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.alarmLevel == '1'">一级报警</span>
            <span v-if="scope.row.alarmLevel == '2'">二级报警</span>
            <span v-if="scope.row.alarmLevel == '3'">三级报警</span>
          </template>
        </el-table-column>

        <el-table-column label="报警内容" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.content }}</span>
          </template>
        </el-table-column>
        <el-table-column label="区域" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.area }}</span>
          </template>
        </el-table-column>
        <el-table-column label="设备间名称">
          <template slot-scope="scope">
            <span>{{ scope.row.equipRoomName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="设备名称">
          <template slot-scope="scope">
            <span>{{ scope.row.equipName }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="设备类型">
          <template slot-scope="scope">
            <span v-if="scope.row.type==0">设备报警</span>
            <span v-else>摄像头报警</span>
          </template>
        </el-table-column> -->

        <el-table-column label="单位" width="70" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.unit }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警值">
          <template slot-scope="scope">
            <span>{{ scope.row.alarmValue }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警限值">
          <template slot-scope="scope">
            <span>{{ scope.row.endValue }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="70" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.status == 0" class="fblur">待处理</span>
            <span v-if="scope.row.status == 1" class="fwarning">处理中</span>
            <span v-if="scope.row.status == 2" class="fsuccess">已完成</span>
            <span v-if="scope.row.status == 3" class="finfo">已忽略</span>
          </template>
        </el-table-column>

        <el-table-column label="处理人" width="70" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.handler }}</span>
          </template>
        </el-table-column>

        <el-table-column label="处理/忽略备注" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.handlerRemark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="处理时间">
          <template slot-scope="scope">
            <!-- <span>{{ scope.row.status==2?scope.row.handlerTime:'' }}</span> -->
            <span>{{ scope.row.handlerTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button
              v-if="
                (scope.row.status == 0 && !scope.row.orderId) ||
                  (scope.row.status == 1 && !scope.row.orderId)
              "
              icon="el-icon-check"
              type="primary"
              size="mini"
              @click="editFunc(scope.row, 'edit')"
              plain
              >处理</el-button
            >
            <el-button
              v-if="scope.row.status == 2 || scope.row.orderId"
              icon="el-icon-more"
              type="info"
              size="mini"
              @click="infoFunc(scope.row, 'info')"
              plain
              >详情</el-button
            >
            <el-button
              v-if="
                userInfo.isBindOrder == 1 &&
                  scope.row.status == 0 &&
                  !scope.row.orderId &&
                  isBaoShiPage
              "
              icon="el-icon-document-checked"
              type="success"
              size="mini"
              @click="showPostItDlg(scope.row)"
              plain
              >报事</el-button
            >
            <!-- <el-button
              v-if="scope.row.status == 0"
              icon="el-icon-close"
              type="warning"
              size="mini"
              @click="editFunc(scope.row, 'IGNORE')"
              plain
              >忽略</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>

    <el-dialog
      :close-on-click-modal="false"
      :title="dlgType == 'add' ? '异常报警处理' : '报警处理详情'"
      :visible.sync="dlgShow"
      width="1200px"
      top="30px"
      append-to-body
    >
      <el-tabs v-model="activeName0">
        <el-tab-pane label="设备报警" name="sbbj">
          <el-row type="flex" justify="space-between">
            <el-col
              :span="11"
              class="giveAlarmDlgCol"
              v-if="giveAlarmRow.type == 0"
            >
              <div
                style="font-weight: bolder;color: #49a2ff;line-height: 16px;position: relative;"
              >
                <span style="position: absolute;top: -1px;">|</span>
                <span class="ml10">设备图纸</span>
                <el-button
                  style="margin-left:320px"
                  type="success"
                  @click="viewLargerImg"
                  size="mini"
                  icon="el-icon-check"
                >
                  <span>查看大图</span>
                </el-button>
              </div>
              <div style="flex: 1;position: relative" class="mt10 dflex">
                <div class="drop-box dflex" v-if="equipmentDrawing">
                  <div class="drop-right">
                    <!-- <el-upload style="width: 980px; height: 660px" v-if="!dropDiaImgUrl" class="avatar-uploader" title="点击上传图片" action="" :show-file-list="false" :before-upload="uploadFunc1">
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload> -->

                    <img
                      :src="equipmentDrawing"
                      :style="`width:${imgWidth}px; height:${imgHeight}px`"
                    />
                    <!-- <el-image
                  class="drop-right-img"
                  :preview-src-list="equipmentDrawingList"
                  :src="equipmentDrawing"
                  alt=""
                >
                  <div slot="error" class="image-slot">
                    <i class=""></i>
                  </div>
                </el-image> -->

                    <!-- <div style="width: 400px; height: 400px; border: 1px solid #000">
            
          </div> -->
                  </div>

                  <div class="drop-left flex1">
                    <!-- 相对定位 可移动的 -->
                    <div class="drop-left-p">
                      <img
                        class="icon_cgq_right"
                        style="position: absolute;"
                        v-for="(item, index) of dropDiaXYList"
                        :key="index"
                        :style="
                          `left:${(item.left + 10 + 10) * iconScale1 -
                            10}px; top:${(item.top + 10 + 10) * iconScale1 -
                            10}px`
                        "
                        :data-index="index"
                        name="sensor-p-item"
                        :title="item.name"
                        @mousedown="moveRightFunc($event, index)"
                        :src="
                          `/static/image/${item.imgName || 'icon_sbxq'}.gif`
                        "
                        alt=""
                      />
                      <!-- <div
              class="icon_cgq_right success"
              v-for="(item,index) of dropDiaXYList"
              :key="index"
              :data-index="index"
              :style="`left:${item.left}px; top:${item.top}px`"
              name='sensor-p-item'
              :title="item.name"
              @mousedown="moveRightFunc($event, index)">
            </div> -->
                    </div>
                    <!-- 绝对定位 固定的 -->
                    <div class="drop-left-a" style="display:none">
                      <div
                        v-for="(item, index) of dropDiaList"
                        :key="index"
                        :data-index="index"
                        @mousedown="moveLeftFunc($event, index)"
                        class="sensor-item sensor-a-item"
                      >
                        <div class="flex sensor-item-top">
                          <!-- << 图片 -->
                          <img
                            class="icon_cgq"
                            :src="
                              `/static/image/${item.imgName || 'icon_cgq'}.png`
                            "
                            alt=""
                          />
                          <!-- <div class="icon_cgq success"></div> -->
                          <!-- >> 图片 -->

                          <div
                            :class="
                              `flex1 not-select ${
                                item.isSelected ? 'fsuccess fbold' : ''
                              } `
                            "
                            style="line-height: 23px"
                          >
                            {{ item.name }}
                          </div>
                          <i
                            v-if="item.iconState"
                            class="el-icon-back fdanger fr"
                            @click="backFunc(index)"
                            style="font-size: 22px"
                            title="撤销"
                          ></i>
                          <!-- {{positionX}}
                {{positionY}} -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div></div>
              </div>
            </el-col>
            <el-col :span="6" class="giveAlarmDlgCol">
              <div
                style="font-weight: bolder;color: #49a2ff;line-height: 16px;position: relative;"
              >
                <span style="position: absolute;top: -1px;">|</span>
                <span class="ml10">报警信息</span>
              </div>
              <div>
                <span style="font-weight: bolder;">区域:</span>
                <span style="color: #767778;">{{ giveAlarmRow.area }}</span>
              </div>
              <div>
                <span style="font-weight: bolder;">报警设备:</span>
                <span style="color: #767778;">{{
                  giveAlarmRow.equipName
                }}</span>
              </div>
              <div>
                <span style="font-weight: bolder;">报警区域:</span>
                <span style="color: #767778;">{{
                  giveAlarmRow.equipRoomName == "" ||
                  !giveAlarmRow.equipRoomName
                    ? giveAlarmRow.area + giveAlarmRow.equipName
                    : giveAlarmRow.equipRoomName + giveAlarmRow.equipName
                }}</span>
              </div>
              <div>
                <span style="font-weight: bolder;">报警类型:</span>
                <span style="color: #767778;">{{
                  giveAlarmRow.alertName
                }}</span>
              </div>
              <div>
                <span style="font-weight: bolder;">报警时间:</span>
                <span style="color: #767778;">{{
                  giveAlarmRow.createTime
                }}</span>
              </div>
              <div>
                <span style="font-weight: bolder;">责任人:</span>
                <span style="color: #767778;"></span>
              </div>
              <div>
                <span style="font-weight: bolder;">责任人联系电话:</span>
                <span style="color: #767778;"></span>
              </div>
              <div>
                <span style="font-weight: bolder;">报警内容:</span>
                <span style="color: #767778;">{{ giveAlarmRow.content }}</span>
              </div>
            </el-col>
            <el-col
              :span="6"
              class="giveAlarmDlgCol"
              v-if="giveAlarmRow.type == 0"
            >
              <div
                style="font-weight: bolder;color: #49a2ff;line-height: 16px;position: relative;"
              >
                <span style="position: absolute;top: -1px;">|</span>
                <span class="ml10">设备信息</span>
              </div>
              <div>
                <span style="font-weight: bolder;">{{
                  alarmDetailData.pojo.name
                }}</span>
              </div>
              <div>
                <span style="font-weight: bolder;">区域:</span>
                <span style="color: #767778;">{{ giveAlarmRow.area }}</span>
              </div>
              <div>
                <span style="font-weight: bolder;">设备类型:</span>
                <span style="color: #767778;">{{
                  alarmDetailData.pojo.nodeArchivesName
                }}</span>
              </div>
              <!-- <div>
            <span style="font-weight: bolder;">资产状态:</span>
            <span style="color: #49a73e;">闲置{{}}</span>
          </div> -->
              <div>
                <span style="font-weight: bolder;">IOT设备状态:</span>
                <span
                  style="color: #767778;"
                  v-if="alarmDetailData.pojo.state == 2"
                  >离线</span
                >
                <span
                  style="color: #767778;"
                  v-if="alarmDetailData.pojo.state == 1"
                  >报警</span
                >
                <span
                  style="color: #767778;"
                  v-if="alarmDetailData.pojo.state == 0"
                  >正常</span
                >
              </div>
              <div>
                <span style="font-weight: bolder;">设备机构:</span>
                <span style="color: #767778;">{{ userInfo.projectName }}</span>
              </div>
              <div>
                <span style="font-weight: bolder;">设备区域:</span>
                <span style="color: #767778;">{{
                  alarmDetailData.pojo.equipRoomName
                }}</span>
              </div>
              <div>
                <span style="font-weight: bolder;">安装位置:</span>
                <span style="color: #767778;">{{
                  giveAlarmRow.alarmAddress
                }}</span>
              </div>
            </el-col>
          </el-row>
          <div
            class="mt20"
            style="font-size: 14px;font-weight: bolder;"
            v-if="
              (giveAlarmRow.alarmVideo && giveAlarmRow.alarmVideo != '') ||
                (giveAlarmRow.alarmPicture && giveAlarmRow.alarmPicture != '')
            "
          >
            报警实况
          </div>
          <div>
            <el-row>
              <el-col :span="12">
                <div
                  v-if="
                    giveAlarmRow.alarmVideo && giveAlarmRow.alarmVideo != ''
                  "
                >
                  <video
                    ref="graphics"
                    style="width: 500px;max-height: 200px;"
                    id="video"
                    controls
                  >
                    <source :src="giveAlarmRow.alarmVideo" type="video/mp4" />
                  </video>
                </div>
              </el-col>
              <el-col :span="12">
                <div
                  v-if="
                    giveAlarmRow.alarmPicture && giveAlarmRow.alarmPicture != ''
                  "
                >
                  <el-image
                    style="width: 100%;height: 200px;max-width: 500px;"
                    v-if="giveAlarmRow.alarmPicture"
                    class="upload-img"
                    :preview-src-list="srcList"
                    :src="this.giveAlarmRow.alarmPicture"
                    alt=""
                  ></el-image>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="mt20" style="font-size: 14px;font-weight: bolder;">
            报警处理
          </div>
          <el-form
            class="mt20"
            ref="dlgForm"
            :rules="dlgRules"
            :model="dlgData"
            label-position="right"
            label-width="110px"
            size="mini"
            @submit.native.prevent
            :disabled="dlgType == 'info' ? true : false"
          >
            <el-row>
              <el-col :span="6">
                <el-form-item label="处理类型" prop="status">
                  <el-radio-group
                    v-model="dlgData.status"
                    @change="radioChange()"
                  >
                    <el-radio :label="1" :disabled="isDis">处理中</el-radio>
                    <el-radio :label="2">已处理</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="预计处理时长"
                  v-if="dlgData.status == '1'"
                  prop="handlerHours"
                >
                  <el-radio-group
                    v-model="dlgData.handlerHours"
                    :disabled="isDis"
                  >
                    <el-radio :label="1">1小时</el-radio>
                    <el-radio :label="6">6小时</el-radio>
                    <el-radio :label="12">12小时</el-radio>
                    <el-radio :label="24">24小时</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item
              label="结果"
              prop="handlerResultStr"
              v-if="dlgData.status == '2'"
            >
              <el-select
                style="width: 200px;"
                v-model="dlgData.handlerResultStr"
                placeholder="请选择"
                @change="gjSelectProductType"
              >
                <el-option
                  v-for="item in gjSelect"
                  :key="item.id"
                  :label="item.name"
                  :value="{ value: item.id, label: item.name }"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-row v-if="dlgData.status == '2'">
              <el-col :span="6">
                <el-form-item label="现场确认人员" prop="handler">
                  <el-input
                    style="width: 200px;"
                    v-model="dlgData.handler"
                    @focus="showUserTree"
                    placeholder="请选择现场确认人员"
                    readonly
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="确认时间" prop="confirmationTime">
                  <el-date-picker
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    v-model="dlgData.confirmationTime"
                    type="datetime"
                    placeholder="选择日期时间"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item
              label="备注"
              prop="handlerRemark"
              v-if="dlgData.status == '2'"
            >
              <el-input
                :rows="3"
                type="textarea"
                resize="none"
                v-model="dlgData.handlerRemark"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="报事信息" name="bsxx" v-if="isBaoshi">
          <div class="bpmn-body">
            <div class="dflex">
              <el-form
                ref="dia2Form"
                label-width="110px"
                class="dia-left"
                style="padding-left: 0"
                :model="dia2Data"
              >
                <!-- 12 报事登记 登记处理 -->

                <div class="dia-left-item" style="margin-top: 24px">
                  <div class="dia-bar-title">报事登记</div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="单号">
                        {{
                          dia2Data.id == "" ? "系统自动生成" : dia2Data.orderNo
                        }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="项目" prop="projectId">
                        {{ dia2Data.projectName }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item
                        v-if="dia2Data.buildingName"
                        label="楼栋信息"
                        prop="buildingName"
                      >
                        <span>{{ dia2Data.buildingName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col v-if="dia2Data.address" :span="12">
                      <el-form-item label="详细地址" prop="address">
                        <span>{{ dia2Data.address }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="点位" prop="areaName">
                        <span>{{ dia2Data.areaName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="科目" prop="subjectName">
                        <span>{{ dia2Data.subjectName }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item label="内容描述" prop="content">
                    <span>{{ dia2Data.content }}</span>
                  </el-form-item>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="报事人" prop="orderUserName">
                        <span>{{ dia2Data.orderUserName }}</span>
                      </el-form-item>
                    </el-col>
                    <!-- <el-col :span="12">
                      <el-form-item label="报事人科室" prop="departmentName">
                        <span>{{ dia2Data.departmentName }}</span>
                      </el-form-item>
                    </el-col> -->
                    <el-col :span="12">
                      <el-form-item label="报事人电话" prop="orderUserPhone">
                        <span>{{ dia2Data.orderUserPhone }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <!-- <el-col :span="12">
                      <el-form-item label="报事接单人" prop="orderUserPhone">
                        <span>{{ dia2Data.orderExecutorName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="接单模式" prop="modelTypeText">
                        <span>{{ dia2Data.modelTypeText }}</span>
                      </el-form-item>
                    </el-col> -->
                    <!-- <el-col :span="24">
                      <el-form-item
                        label="报事执行人岗位"
                        prop="orderUserPhone"
                      >
                        <span>{{ dia2Data.orderExecutorPostName }}</span>
                      </el-form-item>
                    </el-col> -->
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="来源">
                        {{ dia2Data.orderSource }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="紧急度" prop="urgency">
                        <span>{{ dia2Data.urgencyText }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="是否预约" prop="isArrival">
                        <span>{{ dia2Data.isArrival == 1 ? "是" : "否" }}</span>
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="类型" prop="userType">
                        <span v-if="dia2Data.userType == 'LXERP'">龙行erp</span>
                        <span v-else-if="dia2Data.userType == 'JYTUSER'"
                          >简E通</span
                        >
                        <span v-else></span>
                      </el-form-item>
                    </el-col>

                    <el-col :span="12" v-if="dia2Data.isArrival == 1">
                      <el-form-item label="预约到达时间" prop="arrivalTime">
                        <span>{{ dia2Data.arrivalTime }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 3 报事审核 -->
                <div v-if="dia2Data.auditResultText" class="dia-left-item">
                  <div class="dia-bar-title">报事审核</div>
                  <el-form-item label="审核结果">
                    <span>{{ dia2Data.auditResultText }}</span>
                  </el-form-item>

                  <el-form-item label="审核意见">
                    <span>{{ dia2Data.auditContent }}</span>
                  </el-form-item>
                  <el-row v-if="dia2Data.auditName">
                    <el-col :span="12">
                      <el-form-item label="审核人">
                        {{ dia2Data.auditName }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="审核时间">
                        {{ dia2Data.auditTime }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <!-- 4 报事派工 -->
                <div v-if="dia2Data.usersInfoNames" class="dia-left-item">
                  <div class="dia-bar-title">报事派工</div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="接单人">
                        <span>{{ dia2Data.usersInfoNames }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-if="dia2Data.dispatchName">
                    <el-col :span="12">
                      <el-form-item label="派工人">
                        {{ dia2Data.dispatchName }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="派工时间">
                        {{ dia2Data.dispatchTime }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <!-- 接单确认 -->
                <div v-if="dia2Data.confirmOrderUserName" class="dia-left-item">
                  <div class="dia-bar-title">接单确认</div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="确认人">
                        {{ dia2Data.confirmOrderUserName }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="确认时间">
                        {{ dia2Data.confirmOrderTime }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <!-- 报事到达 -->
                <div v-if="dia2Data.arriveUserName" class="dia-left-item">
                  <div class="dia-bar-title">报事到达</div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="到达人">
                        {{ dia2Data.arriveUserName }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="到达时间">
                        {{ dia2Data.arriveTime }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <!-- 报事回单 -->
                <div v-if="dia2Data.archiver" class="dia-left-item">
                  <div class="dia-bar-title">报事回单</div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="回单人">
                        {{ dia2Data.confirmOrderUserName }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="回单时间">
                        {{ dia2Data.archiveTime.slice(0, -3) }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item label="回单内容">
                    {{ dia2Data.archiveContent }}
                  </el-form-item>
                  <el-form-item
                    v-if="dia2Data.archiveUrl && dia2Data.archiveUrl.length > 0"
                    label="回单照片"
                  >
                    <el-image
                      class="archiveUrl"
                      v-for="(item, index) of dia2Data.archiveUrl"
                      :key="index"
                      :src="item"
                      :preview-src-list="[item]"
                    ></el-image>
                  </el-form-item>
                </div>
                <!-- 5 << 报事回访 -->
                <div v-if="dia2Data.returnVisitText" class="dia-left-item">
                  <div class="dia-bar-title">报事回访</div>
                  <el-form-item label="回访方式">
                    <span>{{ dia2Data.returnVisitText }}</span>
                  </el-form-item>

                  <div v-if="dia2Data.returnVisit != 3">
                    <el-form-item label="满意度">
                      <span>{{ dia2Data.isSatisfiedText }}</span>
                    </el-form-item>

                    <el-form-item label="回访记录">
                      <span>{{ dia2Data.returnRemark }}</span>
                    </el-form-item>
                  </div>
                  <el-row v-if="dia2Data.returnUser">
                    <el-col :span="12">
                      <el-form-item label="回访人">
                        {{ dia2Data.returnUser }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="回访时间">
                        {{ dia2Data.returnTime }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 6 报事归档 -->
                <div v-if="dia2Data.archiveOrderUser" class="dia-left-item">
                  <div class="dia-bar-title">报事归档</div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="归档人">
                        {{ dia2Data.archiveOrderUser }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="归档时间">
                        {{ dia2Data.archiveOrderTime }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <!-- 7无改派 -->
                <!-- 8 跟进 -->
                <div v-if="dia2Data.followWayText" class="dia-left-item">
                  <div class="dia-bar-title">跟进</div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="跟进方式">
                        <span>{{ dia2Data.followWayText }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="未完成原因">
                        <span>{{ dia2Data.undoneReasonText }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item label="跟进备注">
                    <span>{{ dia2Data.remark }}</span>
                  </el-form-item>
                  <el-form-item label="预约完成时间">
                    <span>{{ dia2Data.expectedTime }}</span>
                  </el-form-item>
                </div>
                <!-- 9 关注 -->
                <div v-if="dia2Data.attentionName" class="dia-left-item">
                  <div class="dia-bar-title">关注</div>
                  <!-- {{dia2Data.gzUserInfosNames}} -->
                  <el-form-item label="关注人">
                    <span>{{ dia2Data.attentionName }}</span>
                  </el-form-item>
                </div>
                <!-- 作废 -->
                <div v-if="dia2Data.invalidReasonText" class="dia-left-item">
                  <div class="dia-bar-title">
                    {{ dia2Data.status == "8" ? "作废信息" : "流转信息" }}
                  </div>
                  <!-- {{dia2Data.gzUserInfosNames}} -->
                  <el-row>
                    <el-col :span="12">
                      <el-form-item
                        :label="
                          dia2Data.status == '8' ? '作废原因' : '流转原因'
                        "
                      >
                        {{ dia2Data.invalidReasonText }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        :label="
                          dia2Data.status == '8' ? '作废备注' : '流转备注'
                        "
                      >
                        {{ dia2Data.invalidRemark }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <!-- 暂缓 -->
                <div
                  v-if="
                    dia2Data.orderSuspendMaintenanceLogs &&
                      dia2Data.orderSuspendMaintenanceLogs.length > 0
                  "
                  class="dia-left-item"
                >
                  <div class="dia-bar-title">暂缓原因</div>

                  <el-row
                    v-for="(item,
                    index) in dia2Data.orderSuspendMaintenanceLogs"
                    :key="index"
                  >
                    <el-col :span="12">
                      <el-form-item label="时间">
                        {{ item.createTime }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="理由">
                        {{ item.cause }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <!-- 物料 -->
                <div
                  v-if="
                    dia2Data.materialDetails &&
                      dia2Data.materialDetails.length > 0
                  "
                  class="dia-left-item"
                >
                  <div class="dia-bar-title">物料信息</div>

                  <el-row
                    v-for="(item, index) in dia2Data.materialDetails"
                    :key="index"
                  >
                    <el-col :span="8">
                      <el-form-item label="物料名称">
                        {{ item.name }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="物料规格">
                        {{ item.model }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="物料数量">
                        {{ item.num }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-form>
              <div class="dia-right flex1">
                <div class="">
                  <div class="dia-right-title">附件</div>
                  <div class="mt10">
                    <div
                      v-for="(item, index) of dia2Data.otherFiles"
                      :key="index"
                    >
                      <div
                        v-if="item.type == 'wav'"
                        style="margin-top: 4px; margin-bottom: 4px"
                      >
                        <div class="fl" style="line-height: 36px">语音：</div>
                        <audio
                          controls
                          class="fl"
                          style="width: 280px; height: 36px"
                          :id="`audio-${index}`"
                          :src="item.url"
                        ></audio>
                        <!-- <div class="fl dia-fj-name elli" :title="item.name">{{item.name}}</div> -->
                        <!-- <div class="fr m-a" @click="playAudio(index)">播放</div> -->
                        <div class="clear"></div>
                      </div>
                      <div v-else>
                        <div class="fl">图片：</div>
                        <div class="fl dia-fj-name elli" :title="item.name">
                          {{ item.name }}
                        </div>
                        <el-image
                          :id="`dia-fj-${index}`"
                          style="width: 0px; height: 0px"
                          :preview-src-list="[item.url]"
                          :src="item.url"
                          alt=""
                        ></el-image>
                        <div @click="showBigImg(index)" class="fr m-a">
                          查看
                        </div>
                        <div class="clear"></div>
                      </div>
                    </div>
                  </div>
                  <el-upload action="" :show-file-list="false">
                    <!-- <el-button
                      v-if="dia2TypeIndex == 1"
                      class="mt10"
                      icon="el-icon-plus"
                      type="primary"
                      >选择本地文件</el-button
                    > -->
                  </el-upload>
                </div>
                <div class="mt40" v-if="dia2Data.orderFollowupList.length > 0">
                  <div class="dia-right-title">跟进</div>

                  <div
                    v-for="(item, index) of dia2Data.orderFollowupList"
                    :key="index"
                    class="mt20"
                  >
                    <div class="fbold">第 {{ index + 1 }} 次跟进</div>
                    <div>跟进方式：{{ item.followWayText }}</div>
                    <div>跟进人：{{ item.createUser }}</div>
                    <div>跟进时间：{{ item.createTime }}</div>
                    <div>未完成原因：{{ item.undoneReasonText }}</div>
                    <div>跟进备注：{{ item.remark }}</div>
                    <div>预计完成时间：{{ item.expectedTime }}</div>
                  </div>
                </div>
                <div class="mt40" v-if="dia2Data.attentionName">
                  <div class="dia-right-title">关注</div>

                  <div class="mt20">
                    <div>关注人：{{ dia2Data.attentionName }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">关闭</el-button>
        <el-button
          v-if="dlgType != 'info'"
          type="success"
          @click="subGiveAlarmDlg"
          icon="el-icon-check"
        >
          <span>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <!-- << 弹窗 报警信息 -->
    <el-dialog
      title="历史记录"
      :close-on-click-modal="false"
      :append-to-body="true"
      :visible.sync="diaBjState"
      width="1200px"
      top="30px"
      icon-class="el-icon-info"
    >
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="列表" name="liebiao">
          <el-date-picker
            style="width: 300px"
            class="fl"
            @change="getBjList"
            v-model="diaBjQuery.dateRange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            class="search-right-btn fl"
            @click="getBjList"
            >搜索</el-button
          >

          <div class="clear"></div>

          <el-table
            ref="tableBar"
            class="m-small-table mt10"
            v-loading="listLoading"
            :key="diaBjTableKey"
            :data="diaBjList"
            border
            fit
            highlight-current-row
            style="width: 100%"
            max-height="500px"
          >
            <el-table-column label="#" type="index" align="center" width="70">
              <template slot-scope="scope">
                <span>{{
                  (diaBjQuery.page - 1) * diaBjQuery.limit + scope.$index + 1
                }}</span>
              </template>
            </el-table-column>

            <el-table-column
              v-for="(item, index) of diaBjTHList"
              :key="index"
              :label="item.label"
            >
              <template slot-scope="scope">
                <span>{{ scope.row[item.key] }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <pagination
            class="mt10"
            v-show="diaBjTotal > 0"
            :total="diaBjTotal"
            :page.sync="diaBjQuery.page"
            :limit.sync="diaBjQuery.limit"
            @pagination="getBjList"
          />
        </el-tab-pane>
        <el-tab-pane label="趋势" name="qushi">
          <el-date-picker
            class="fl ml10"
            style="width: 350px"
            @change="getZxt"
            v-model="zxtQuery.dateRange"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="mini"
          >
          </el-date-picker>
          <el-select
            @change="handleSelectChange"
            class="fl ml10"
            style="width: 200px"
            v-model="zxtQuery.disRespVos"
            multiple
            collapse-tags
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.type"
              :label="item.name"
              :value="`${item.type},${item.name}`"
            >
            </el-option>
          </el-select>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            class="search-right-btn fl"
            @click="getZxt"
            >搜索</el-button
          >
          <div class="clear"></div>

          <div
            v-if="showChart2"
            id="echart-bar2"
            style="height: 500px; margin-top: 16px"
          ></div>
        </el-tab-pane>
      </el-tabs>
      <div class="clear"></div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="diaBjState = false" icon="el-icon-back"
          >取消</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      title="设备图纸"
      :close-on-click-modal="false"
      :visible.sync="dropDiaState"
      width="1024px"
      top="30px"
      append-to-body
    >
      <div style="width: 981px; overflow-x:hidden">
        <div
          class="dflex"
          style="position: relative;border: 1px solid #e8e8e8;"
        >
          <div style="width: 980px;">
            <img
              style=" width: 980px;-webkit-user-drag: none;"
              :src="equipmentDrawing"
              alt=""
            />
          </div>
          <div class="drop-left flex1" :style="`height:100px; padding: 10px`">
            <!-- 相对定位 可移动的 -->
            <!-- :style="`left:${item.left + 100}px; top:${item.top}px`" -->
            <div class="drop-left-p">
              <img
                class="icon_cgq_right"
                style="position: absolute;width:40px;"
                v-for="(item, index) of dropDiaXYList"
                :key="index"
                :style="`left:${item.left - 7}px; top:${item.top - 5}px`"
                :data-index="index"
                name="sensor-p-item"
                :title="item.name"
                :src="`/static/image/${item.imgName || 'icon_sbxq'}.gif`"
                alt=""
              />
              <!-- <div
              class="icon_cgq_right success"
              v-for="(item,index) of dropDiaXYList"
              :key="index"
              :data-index="index"
              :style="`left:${item.left}px; top:${item.top}px`"
              name='sensor-p-item'
              :title="item.name"
              @mousedown="moveRightFunc($event, index)">
            </div> -->
            </div>
            <!-- 绝对定位 固定的 -->
            <div class="drop-left-a" style="display:none">
              <div
                v-for="(item, index) of dropDiaList"
                :key="index"
                :data-index="index"
                class="sensor-item sensor-a-item"
              >
                <div class="flex sensor-item-top">
                  <!-- << 图片 -->
                  <img
                    class="icon_cgq"
                    :src="`/static/image/${item.imgName || 'icon_cgq'}.png`"
                    alt=""
                  />
                  <!-- <div class="icon_cgq success"></div> -->
                  <!-- >> 图片 -->

                  <div
                    :class="
                      `flex1 not-select ${
                        item.isSelected ? 'fsuccess fbold' : ''
                      } `
                    "
                    style="line-height: 23px"
                  >
                    {{ item.name }}
                  </div>
                  <i
                    v-if="item.iconState"
                    class="el-icon-back fdanger fr"
                    @click="backFunc(index)"
                    style="font-size: 22px"
                    title="撤销"
                  ></i>
                  <!-- {{positionX}}
                {{positionY}} -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dropDiaState = false" icon="el-icon-back"
          >关闭</el-button
        >
        <!-- <el-button type="success" @click="subGiveAlarmDlg" icon="el-icon-check">
          <span>提交</span>
        </el-button> -->
      </div>
    </el-dialog>

    <Usertree />
    <PostItDlg
      :dlgState0="postItDlg"
      :dlgQuery="dlgQuery"
      :dlgType="postDlgType"
      @closeDlg="closeDlg"
    />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Cookie from "js-cookie";
import PostItDlg from "./components/postItDlg.vue";
import {
  regionTree,
  findModulesByProjectId
} from "@/api/basicManSystem/comprehenOperateMonitor.js"; //查区域
import Usertree from "@/components/Dialog/Usertree";
import Pagination from "@/components/Pagination"; // 分页
import * as utils from "@/utils";
import {
  arrId2Name, // 根据id 获取name
  isNull,
  getDataDictOther // 数据字典
} from "@/utils";
import * as echarts from "echarts";
import moment, { localeData } from "moment"; //导入文件
import { getAction, postAction } from "../../api";
// 接口
import {
  findAbnormalAlarmPage, // 分页
  handleAlarm, // 处理
  ignoreAlarm // 忽略
} from "@/api/mzgApi";

import {
  findEquioPage, // 设备间
  protocolLoran
} from "@/api/safetyMonitoringApi";
import { QNRTPlayer } from "qn-rtplayer-web";
let player = new QNRTPlayer();
let dlgDataEmpty = {
  id: "",
  handlerId: "",
  handler: "",
  handlerRemark: "",
  confirmationTime: "",
  status: "",
  handlerHours: "",
  handlerResultStr: "",
  handlerResult: ""
};

// 报警信息弹窗
let diaBjQueryEmpty = {
  id: "",
  label: "",
  page: 1,
  limit: 10,
  dateRange: []
};
//折线图
let zxtQueryEmpty = {
  disRespVos: [],
  nodeId: "",
  startTime: "",
  endTime: "",
  dateRange: []
};
//报事
let dia2DataEmpty = {
  id: "",
  projectId: "", // 项目id
  projectName: "", // 项目名称
  areaId: "", // 报事区域id
  areaName: "", // 报事区域名称
  subjectId: "", // 报事科目id
  subjectName: "", // 报事科目名称
  content: "", // 报事内容
  orderUserName: "", // 报事人姓名
  orderUserId: "0", // 报事人id
  orderExecutorName: [], //报事接单人
  orderExecutorId: [],
  orderUserPhone: "", // 报事人电话
  urgency: "", // 紧急度
  urgencyText: "", // 紧急度
  isArrival: 1, // 是否预约  0 不预约   1 预约
  userType: "JYTUSER", // LXERP-龙行erp  JYTUSER-简E通
  modelType: "",
  modelTypeText: "", //接单模式  1:共同执行 2:竞争执行
  arrivalTime: "", // 预约到达时间
  otherFiles: [], // 附件信息地址 arr
  status: "2", // 0:已登记 1:已审核 2:已派工 3:已确认 4：已到达 5：已回单 6：已回访 7：已归档 8：已作废
  orderSource: "", // 报事来源
  orderFollowupList: [],
  orderExecutorPostName: "", // 报事执行人岗位名称
  orderExecutorPostId: "",
  alarmId: "" //报警id
};

export default {
  components: {
    Pagination,
    Usertree,
    PostItDlg
  },
  watch: {
    // 用户tree
    userTreeUserId(nVal) {
      if (this.dlgShow) {
        this.dlgData.handlerId = nVal;

        //console.log(this.dlgData.chargeLeaderId + "22222222222222");
        // this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      }
    },
    userTreeUserName(nVal) {
      if (this.dlgShow) {
        this.dlgData.handler = nVal;
        // this.dlgData = JSON.parse(JSON.stringify(this.dlgData));
      }
    }
  },
  computed: {
    ...mapGetters([
      // 部门
      "bmTreeBranchId",
      "bmTreeBranchName"
    ]),
    // 部门选人相关
    userTreeState: {
      get: function() {
        let userTreeState = this.$store.getters.userTreeState;
        return userTreeState;
      },
      set: function(newVal) {
        // this.$store.commit('SET_USERTREESTATE', newVal)
      }
    },
    userTreeUserId: {
      get: function() {
        let userTreeUserId = this.$store.getters.userTreeUserId;
        return userTreeUserId;
      },
      set: function(newVal) {
        // this.$store.commit('SET_USERTREEUSERID', newVal)
      }
    },
    userTreeUserName: {
      get: function() {
        let userTreeUserName = this.$store.getters.userTreeUserName;
        return userTreeUserName;
      },
      set: function(newVal) {
        // this.$store.commit('SET_USERTREEUSERNAME', newVal)
      }
    }
  },
  data() {
    return {
      isDis: false,
      sbjSelect: [], // 设备间
      dlgRules: {
        handlerRemark: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        handler: [{ required: true, message: "必填字段", trigger: "change" }],
        confirmationTime: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        handlerResultStr: [
          { required: true, message: "必填字段", trigger: "change" }
        ],
        status: [{ required: true, message: "必填字段", trigger: "change" }],
        handlerHours: [
          { required: true, message: "必填字段", trigger: "change" }
        ]
      },
      list: [],
      listQuery: {
        page: 1,
        limit: 20,
        label: "",
        status: "", // 0:待处理 1:处理中 2:已完成 不传查所有
        beginDate: "",
        endDate: "",
        dateRange: [],

        equipRoomIds: "",
        alertType: ""
      },
      areaList: [], //区域
      statusSelect: [
        { id: "0", name: "待处理" },
        { id: "1", name: "处理中" },
        { id: "2", name: "已完成" },
        { id: "3", name: "已忽略" }
      ],
      alertSelect: [
        { id: "a1001", name: "水压高" },
        { id: "a1002", name: "水压低" },
        { id: "a1003", name: "水位高" },
        { id: "a1004", name: "水位低" },
        { id: "a1005", name: "温度高" },
        // { id: "a1006", name: "温度低" },
        { id: "a1007", name: "湿度高" },
        { id: "a1008", name: "湿度低" },
        // { id: "d", name: "光照高" },
        // { id: "a1010", name: "光照低" },
        { id: "a1011", name: "过压" },
        { id: "a1012", name: "欠压" },
        // { id: "a1013", name: "电量低" },
        { id: "a1014", name: "人体活动" },
        // { id: "a1015", name: "长时间未上报" },
        { id: "a1016", name: "烟雾报警" },
        { id: "a1017", name: "三相电压不平衡" },
        { id: "a1018", name: "三相电流不平衡" },
        { id: "a1019", name: "高负载" },
        // { id: "a1020", name: "低负载" },
        { id: "a1021", name: "漏水报警" },
        { id: "a1022", name: "可燃气体报警" },
        { id: "a1023", name: "环境温度高" },
        { id: "a1024", name: "环境温度低" },
        { id: "a1025", name: "线缆温度高" },
        // { id: "a1026", name: "线缆温度低" },
        { id: "a1027", name: "有功功率高" },
        { id: "a1028", name: "有功功率低" },
        // { id: "a1029", name: "无功功率高" },
        // { id: "a1030", name: "无功功率低" },
        //{ id: "a1031", name: "视在功率高" },
        // { id: "a1032", name: "视在功率低" },
        { id: "a1033", name: "井盖打开" },
        { id: "a1034", name: "漏电电流高" },
        // { id: "a1035", name: "漏电电流低" },
        { id: "a1036", name: "差压高" },
        { id: "a1037", name: "差压低" },
        // { id: "a1038", name: "可燃气体浓度高" },
        // { id: "a1039", name: "可燃气体浓度低" },
        { id: "a1040", name: "短路报警" },
        { id: "a1041", name: "浪涌报警" },
        { id: "a1042", name: "缺项报警" },
        { id: "a1043", name: "打火报警" },
        { id: "a1044", name: "掉电报警" },
        { id: "a1045", name: "离线报警" },
        { id: "109", name: "烟火监测报警" },
        { id: "110", name: "烟雾监测报警" },
        { id: "a1048", name: "瞬时流量高" },
        { id: "a1049", name: "瞬时流量低" }
        // {id: "18",name: "移动侦测报警"},
        // {id: "20",name: "门铃按键报警"},
        // {id: "38",name: "烟感检测报警"},
        // {id: "39",name: "离岗检测报警"},
        // {id: "40",name: "婴儿哭声检测报警"},
        // {id: "41",name: "人形检测报警"},
      ],
      srcList: [], //图片

      total: 0,
      listLoading: false,

      dlgType: "",
      dlgLoading: false,
      dlgShow: false,
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      // << 弹窗-报警信息
      diaBjTableKey: 0,
      diaBjState: false,
      diaBjTHList: [],
      diaBjList: [],
      diaBjTotal: 0,
      diaBjQuery: JSON.parse(JSON.stringify(diaBjQueryEmpty)),
      // >> 弹窗-报警信息
      // << 弹窗 设置计划
      activeName: "liebiao",
      zxtQuery: JSON.parse(JSON.stringify(zxtQueryEmpty)),
      options: [],
      bjDiaRow: {},
      showChart2: false,
      echartRoom2: null,
      zxtSelect: [],

      equipmentDrawing: "",
      equipmentDrawingList: [],
      dropDiaXYList: [], // 坐标
      imgHeight: "",
      imgWidth: "",
      iconScale1: 1,
      dropDiaList: [
        { id: 1, name: "用电" },
        { id: 2, name: "环境参数" },
        { id: 23, name: "环境参数23" }
      ], // 传感器列表
      sbImgH: "",
      giveAlarmRow: {}, //报警详情/报警信息
      alarmDetailData: {
        pojo: {}
      },
      userInfo: {},
      //查看大图dlg
      dropDiaState: false,
      gjSelect: [],
      // >> 数据字典
      equipTypeList: [],
      activeName0: "sbbj",
      //报事弹框
      postItDlg: false,
      dlgQuery: {},
      postDlgType: "",
      //是否报事
      isBaoshi: false,
      dia2Data: JSON.parse(JSON.stringify(dia2DataEmpty)),
      isBaoShiPage: false
    };
  },
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
    if (
      this.$route.path == "/liftMan/alarmManagement" ||
      this.$route.path == "/environmentalSafety/abnormalAlarmMan" ||
      this.$route.path == "/equipSafeMan/abnormalAlarmMan" ||
      this.$route.path == "/electricalFireMonitoring/abnormalAlarmMan"
    ) {
      this.isBaoShiPage = true;
    } else {
      this.isBaoShiPage = false;
    }
    if (this.$route.path == "/liftMan/alarmManagement") {
      this.postDlgType = "diantijiankong";
    } else if (this.$route.path == "/equipSafeMan/abnormalAlarmMan") {
      this.postDlgType = "shebeianquan";
    } else if (
      this.$route.path == "/electricalFireMonitoring/abnormalAlarmMan"
    ) {
      this.postDlgType = "dianqihuozai";
    } else if (this.$route.path == "/environmentalSafety/abnormalAlarmMan") {
      this.postDlgType = "huanjinganquan";
    }
    console.log(this.userInfo, "this.userInfo");
    console.log(this.$route.path, "11");

    if (this.$route.query.status) {
      this.listQuery.status = this.$route.query.status;
    }
    if (this.$route.query.today) {
      this.listQuery.dateRange = [
        this.$route.query.today,
        this.$route.query.today
      ];
    }
    getDataDictOther(this, "iot_equip_type", "equipTypeList"); // 业务分类
    getDataDictOther(this, "gaojingchulijieguo", "gjSelect"); // 报警处理结果
    this.getSbjSelect(); // 获取设备间
    this.getList();
    this.getAreaList();
  },
  methods: {
    //区域
    getAreaList() {
      this.areaList = [];
      let sendObj = {
        page: 1,
        size: 9999
      };
      regionTree(sendObj).then(res => {
        if (res.data.code == "200") {
          this.areaList = this.getTreeData(res.data.data);
        } else {
          this.$message({
            type: "warning",
            message: res.data.msg
          });
        }
      });
    },
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    areaChange(selectedValues) {
      if (selectedValues) {
        if (this.dlgState) {
          this.dlgData.area = this.$refs[
            "refSubCat0"
          ].getCheckedNodes()[0].label; //获取选中name
        } else {
          this.listQuery.equipRoomId = "";
          this.listQuery.equipRoomName = "";
          this.listQuery.area = this.$refs[
            "refSubCat"
          ].getCheckedNodes()[0].label; //获取选中name
        }
      } else {
        this.dlgData.equipRoomId = "";
        this.dlgData.equipRoomName = "";
      }
      this.getSbjSelect();
    },
    //导出
    downLoadFunc() {
      this.listQuery.beginDate = this.listQuery.dateRange
        ? this.listQuery.dateRange[0]
        : "";
      this.listQuery.endDate = this.listQuery.dateRange
        ? this.listQuery.dateRange[1]
        : "";
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      delete sendObj.dateRange;
      if (sendObj.equipRoomIds.length > 0) {
        sendObj.equipRoomIds = sendObj.equipRoomIds.join(",");
      } else {
        sendObj.equipRoomIds = "";
      }
      let loading = this.$loading({
        lock: true,
        text: "导出中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      postAction("/iot/abnormalAlarmExport", sendObj).then(res0 => {
        loading.close();
        window.open(res0.data.data.url);
      });
    },
    radioChange() {
      this.dlgData.handlerId = "";
      this.dlgData.handler = "";
      this.dlgData.handlerRemark = "";
      this.dlgData.confirmationTime = "";
      this.dlgData.handlerHours = "";
      this.dlgData.handlerResultStr = "";
      this.dlgData.handlerResult = "";
      this.$nextTick(() => {
        this.$refs["dlgForm"].clearValidate();
      });
    },
    // 选人弹窗
    showUserTree() {
      this.$store.commit("SET_USERTREESQTYPE", "");
      this.$store.commit("SET_USERTREESTATE", true);
    },
    gjSelectProductType(data) {
      let { value, label } = data;
      this.dlgData.handlerResultStr = label;
      this.dlgData.handlerResult = value;
    },
    getEquipmentDrawing(id) {
      this.equipmentDrawing = "";
      this.equipmentDrawingList = [];
      getAction(`/iot/findListByEquipId/${id}`).then(res1 => {
        let res = res1.data;
        if (res.code == 200) {
          let dropDiaList = res.list;
          if (res.data.imgUrl != null && res.data.imgUrl != "") {
            this.equipmentDrawing = res.data.imgUrl;
            this.equipmentDrawingList = res.data.imgUrl.split(",");

            // 坐标 - 并将坐标渲染到画布上
            if (res.data.coordinate == null || res.data.coordinate == "") {
              this.dropDiaXYList = [];
            } else {
              let arr = [];
              let coordinate = JSON.parse(res.data.coordinate);
              let obj = coordinate.find(obj => {
                return obj.id == this.giveAlarmRow.nodeId;
              });
              if (obj != undefined) {
                arr.push(obj);
              } else {
                arr = [];
              }
              // console.log(obj,"obj");
              let dropDiaXYList = (this.dropDiaXYList = arr);
              // this.dropDiaXYList = JSON.parse(JSON.stringify(dropDiaXYList))
            }
            console.log(this.dropDiaXYList, "this.dropDiaXYList");
          } else {
            this.equipmentDrawing = "";
            this.equipmentDrawingList = [];
          }
          let img = new Image();
          img.src = this.equipmentDrawing;
          img.onload = async () => {
            this.imgWidth = 400 / img.width;
            this.imgHeight = 202 / img.height;
            if (img.width / img.height > 400 / 202) {
              this.imgWidth = 400;
              this.imgHeight = (img.height * 400) / img.width;
            } else {
              this.imgWidth = (img.width * 202) / img.height;
              this.imgHeight = 202;
            }
            this.iconScale1 = this.imgWidth / 980;
          };

          this.dropDiaList = JSON.parse(JSON.stringify(dropDiaList));
          // 图片高度
          setTimeout(() => {
            this.sbImgH = $(".drop-right-img").height() + 4;
          }, 500);
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
        console.log(res, "res");
      });
    },
    //tab切换
    handleClick(tab, event) {
      this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty));
      this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
      this.diaBjQuery.id = this.bjDiaRow.nodeId;
      this.zxtSelect = [];
      console.log(tab, event);
      if (tab.name == "liebiao") {
        this.getBjList();
      } else {
        const now = moment();
        const start = moment(now).subtract(3, "hours");
        const end = moment().format("YYYY-MM-DD HH:mm");
        let startTime = moment(start).format("YYYY-MM-DD HH:mm");
        // let endTime = moment(end).format('YYYY-MM-DD HH:mm');
        this.zxtQuery.dateRange = [startTime, end];
        this.getDxList();
        this.getZxt();
      }
    },
    getDxList() {
      getAction(`/iot/trend/nodeDataDis/${this.bjDiaRow.nodeId}`).then(res1 => {
        let res = res1.data;
        if (res.code == 200) {
          this.options = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleSelectChange() {
      this.zxtSelect = [];
      this.zxtQuery.disRespVos.forEach(element => {
        console.log(element, "element");
        let [type, name] = element.split(",");
        this.zxtSelect.push({ type, name });
      });
    },
    getZxt() {
      this.showChart2 = false;
      if (
        isNull(this.zxtQuery.dateRange) ||
        this.zxtQuery.dateRange.length <= 0
      ) {
        this.$message.warning("请先选择起止时间");
        return false;
      }
      let sendObj = JSON.parse(JSON.stringify(this.zxtQuery));
      // 日期范围
      sendObj.startTime = "";
      sendObj.endTime = "";
      if (
        !isNull(this.zxtQuery.dateRange) &&
        this.zxtQuery.dateRange.length > 0
      ) {
        sendObj.startTime = this.zxtQuery.dateRange[0];
        sendObj.endTime = this.zxtQuery.dateRange[1];
      }
      sendObj.disRespVos = this.zxtSelect;
      sendObj.nodeId = this.bjDiaRow.nodeId;
      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      postAction("/iot/trend/nodeDataTrend", sendObj).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == 200) {
          if (!utils.isNull(res.data) && res.data.list.length > 0) {
            this.showChart2 = true;
            // this.list = res.data;
            setTimeout(() => {
              this.setEchartBar2(res.data.list, res.data.times);
              //   this.createRoom(res.data.list)
            }, 100);
          }
        }
      });
    },
    //创建折线图
    setEchartBar2(arr, dataMap) {
      console.log(arr, "arr");
      if (this.showChart2 == false) {
        // if (!utils.isNull(arr)) {
        //   this.echartRoom2.clear();
        // }
        return;
      }
      // << 本月1号到当天
      let xList = [];
      let xList0 = [];
      // let dateObj = new Date();
      // console.log("dateObj.getDate()", dateObj.getDate());
      // console.log(this.getEveryDayDateByBetweenDate(this.listQuery.dateRange[0],this.listQuery.dateRange[1]),'时间间隔');
      // let dayNum = parseInt(dateObj.getDate());
      // let month = utils.return2Num2(dateObj.getMonth() + 1);
      // let year = dateObj.getFullYear();
      // for (let i = 0; i < dayNum; i++) {
      //   let key = `${year}-${month}-${utils.return2Num2(i + 1)}`;
      //   xList.push(key);
      //   xList0.push(`${year}-${month}-${utils.return2Num2(i + 1)}`);
      // }

      // 拼接数据
      let data = [];
      let listData = [];
      for (let index = 0; index < dataMap.length; index++) {
        let obj = {
          yearMonthDate: dataMap[index],
          count: 0,
          type: ""
        };
        listData.push(obj);
      }
      for (let i = 0; i < arr.length; i++) {
        let itemLine = arr[i];
        let lineObj = {
          name: itemLine.name,
          type: "line",
          stack: "",
          data: []
        };
        let map = itemLine.list;
        // console.log('111111111map', map)
        // console.log('111111111listData', listData)
        for (let key = 0; key < map.length; key++) {
          for (let k = 0; k < listData.length; k++) {
            if (map[key].time == listData[k].yearMonthDate) {
              lineObj.data.push(map[key].value);
              // listData[k].value = map[key].value;
              // listData[k].type = map[key].type;
            }
          }
        }
        data.push(lineObj);
        // let arrData = [];
        // console.log("==listData", listData);
        // for (let o = 0; o < listData.length; o++) {
        //   arrData.push(listData[o].value);
        // }
        // console.log(arrData, "arrData");
        // lineObj.data = arrData;
        // data.push(lineObj);
      }
      console.log(data, "data--------------");
      // xList0.push(map[key].yearMonthDate)
      xList0 = dataMap;
      // 绘制图标
      var myChart = echarts.init(document.getElementById("echart-bar2"));
      var option = {
        title: {
          text: ""
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },

        legend: {
          left: 10
        },
        grid: {
          left: "2%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false, // true-刻度中间 false-刻度线上
          data: xList0
        },
        yAxis: {
          type: "value"
          // name: '单位（吨）',
          // nameTextStyle: {
          //   color: '#aaa',
          //   nameLocation: 'start',
          // },
        },
        series: data
        // series: [[1,2,3],[12,22,32],[13,23,33]]
      };
      myChart.clear();
      myChart.setOption(option);
      myChart.on("click", param => {
        console.log("param", param);
        // // componentIndex
        // // dataIndex
        // let msg = `${this.echartLineData[param.componentIndex].name}：${
        //   this.echartLineData[param.componentIndex].data[param.dataIndex]
        // }`
        // alert(msg)
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    showBjDia(row) {
      // 调用接口
      (this.activeName = "liebiao"),
        (this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty)));
      this.diaBjQuery.id = row.nodeId;
      this.bjDiaRow = row;
      this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
      this.getBjList();
    },
    // 获取列表
    getBjList() {
      let beginTime = "";
      let endTime = "";
      if (
        this.diaBjQuery.dateRange != null &&
        this.diaBjQuery.dateRange.length != 0
      ) {
        beginTime = this.diaBjQuery.dateRange[0];
        endTime = this.diaBjQuery.dateRange[1];
      }
      let sendObj = {
        page: this.diaBjQuery.page,
        limit: this.diaBjQuery.limit,
        id: this.diaBjQuery.id,
        beginTime,
        endTime
      };

      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      protocolLoran(sendObj).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == "200") {
          if (res.data == null) {
            this.diaBjTotal = 0;
            this.$message({
              type: "warning",
              message: "暂无报警信息"
            });
            return false;
          }
          // 表头
          let diaBjTHList = [];
          for (let key in res.data.field) {
            let label = res.data.field[key];
            let obj = {
              key,
              label
            };
            diaBjTHList.push(obj);
          }
          this.diaBjTHList = diaBjTHList;

          // 表格数据
          this.diaBjTotal = res.data.total;

          this.diaBjList = res.list;
          this.diaBjState = true;
          this.diaBjTableKey++;
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // 高亮
    tableRowClassName({ row, rowIndex }) {
      if (row.alarmLevel == "3") {
        return "color-warn1";
      } else if (row.alarmLevel == "2") {
        return "color-warn2";
      } else if (row.alarmLevel == "1") {
        return "color-warn3";
      }
      return "";
    },
    // << 获取设备间
    getSbjSelect() {
      let sendObj = {
        label: "",
        page: 1,
        limit: 9999,
        isEquipRoom: 0, // 0设备间 1设备 不传查所有
        areaId: this.listQuery.areaId
      };
      if (this.$route.path == "/equipSafeMan/abnormalAlarmMan") {
        sendObj.equipType = 1;
      }
      if (this.$route.path == "/electricalFireMonitoring/abnormalAlarmMan") {
        sendObj.equipType = 2;
      }
      if (this.$route.path == "/environmentalSafety/abnormalAlarmMan") {
        sendObj.equipType = 3;
      }
      if (this.$route.path == "/energyMonitoring/abnormalAlarmMan") {
        sendObj.equipType = 4;
      }
      findEquioPage(sendObj).then(res1 => {
        this.listLoading = false;
        let res = res1.data;
        if (res.code == "200") {
          this.sbjSelect = JSON.parse(JSON.stringify(res.list));
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    getList() {
      this.list = [];
      this.listLoading = true;
      this.listQuery.beginDate = this.listQuery.dateRange
        ? this.listQuery.dateRange[0]
        : "";
      this.listQuery.endDate = this.listQuery.dateRange
        ? this.listQuery.dateRange[1]
        : "";

      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      delete sendObj.dateRange;
      if (sendObj.equipRoomIds.length > 0) {
        sendObj.equipRoomIds = sendObj.equipRoomIds.join(",");
      } else {
        sendObj.equipRoomIds = "";
      }
      if (this.$route.path == "/equipSafeMan/abnormalAlarmMan") {
        sendObj.equipType = 1;
      }
      if (this.$route.path == "/electricalFireMonitoring/abnormalAlarmMan") {
        sendObj.equipType = 2;
      }
      if (this.$route.path == "/environmentalSafety/abnormalAlarmMan") {
        sendObj.equipType = 3;
      }
      if (this.$route.path == "/energyMonitoring/abnormalAlarmMan") {
        sendObj.equipType = 4;
      }
      // sendObj.equipRoomName = utils.arrId2Name(this.sbjSelect, sendObj.equipRoomIds);
      // delete sendObj.equipRoomIds;

      findAbnormalAlarmPage(sendObj).then(res => {
        this.listLoading = false;
        if (res.data.code == 200) {
          this.total = res.data.data.total;
          this.list = JSON.parse(JSON.stringify(res.data.list));
        } else {
          this.$message.warning(res.data.msg);
        }
      });
    },

    clearQuery(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },

    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    //查看大图
    viewLargerImg() {
      this.dropDiaState = true;
    },

    editFunc(data, type) {
      this.dlgType = type;
      this.giveAlarmRow = data;
      this.isBaoshi = false;
      this.srcList = [];
      if (data.status != 1 && data.status != 2) {
        this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
        this.isDis = false;
      } else if (data.status == 1) {
        this.dlgData.status = data.status;
        this.dlgData.handlerHours = data.handlerHours;
        this.isDis = true;
      } else {
        this.isDis = false;
        this.dlgData.status = data.status;
        this.dlgData.handlerHours = 0;
      }
      // if (data.alarmVideo&&data.alarmVideo!='') {
      // this.$nextTick(() => {
      //       player.init();
      //       player.play(
      //           data.alarmVideo,
      //         document.getElementById("graphics")
      //       );
      //       player.setConfig({ controls: true, playsinline: true });
      //       console.log(player,'player====');
      //     });
      // }
      if (data.alarmPicture && data.alarmPicture != "") {
        this.srcList.push(data.alarmPicture);
      }
      console.log(this.srcList, "this.srcList");

      this.dlgData.id = data.id;
      if (data.type != 1) {
        this.getInfo1(data.nodeId);
      }
      this.dlgShow = true;
    },
    infoFunc(data, type) {
      this.srcList=[]
      this.isBaoshi = false;
      this.activeName0 = "sbbj";
      this.dlgType = type;
      this.giveAlarmRow = data;
      this.dlgData.id = data.id;
      if (data.type != 1) {
        this.getInfo1(data.nodeId);
      }
      console.log(data, "data");

      if (data.orderId) {
        this.isBaoshi = true;
        this.getBaoShiInfo(data.orderId);
      }
      this.dlgShow = true;
      this.dlgData.handlerResultStr = data.handlerResultStr;
      this.dlgData.handlerHours = data.handlerHours;
      this.dlgData.handler = data.handler;
      this.dlgData.handlerRemark = data.handlerRemark;
      this.dlgData.confirmationTime = data.confirmationTime;
      this.dlgData.status = data.status;
      if (data.alarmPicture && data.alarmPicture != "") {
        this.srcList.push(data.alarmPicture);
      }
    },
    getBaoShiInfo(orderId) {
      getAction(`/report/order/findOrderById/${orderId}`, {}).then(res0 => {
        let res = res0.data;
        if (res.code == 200) {
          let dia2Data = JSON.parse(JSON.stringify(res.data));
          let otherFiles = dia2Data.otherFiles.split(",");
          let otherFilesNew = [];
          for (let item of otherFiles) {
            if (item) otherFilesNew.push(this.imgUrl2Obj(item));
          }
          if (otherFilesNew[0] == "") otherFilesNew = [];
          dia2Data.otherFiles = otherFilesNew;

          if (!dia2Data.usersInfo) {
            dia2Data.usersInfo = [];
          }
          // 改派
          if (dia2Data.takerNames) {
            dia2Data.modelTypeText = dia2Data.modelTypeText;
            dia2Data.usersInfoNames = dia2Data.takerNames;
          }

          dia2Data.modelType1 = dia2Data.modelTypeText1 = "";
          dia2Data.usersInfo1 = [];
          dia2Data.usersInfoNames1 = "";
          if (dia2Data.orderFollowupList == null) {
            dia2Data.orderFollowupList = [];
          }

          if (dia2Data.archiveUrl && dia2Data.archiveUrl !== "null") {
            dia2Data.archiveUrl = dia2Data.archiveUrl.split(",");
          } else {
            dia2Data.archiveUrl = "";
          }

          // >> 有数据

          this.dia2Data = JSON.parse(JSON.stringify(dia2Data));
          console.log(this.dia2Data, "this.dia2Data ");
          this.$nextTick(() => {
            this.$refs["dia2Form"].clearValidate();
          });
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    imgUrl2Obj(url) {
      let name = url.split("/");
      name = name[name.length - 1];
      let type = name.split(".")[1];
      if (type != "wav") type = "image";
      let obj = {
        type,
        url,
        name
      };
      return obj;
    },
    // 查看图片
    showBigImg(index) {
      $(`#dia-fj-${index}`).click();
    },
    // 播放语音
    playAudio(index) {
      let audioElement = document.getElementById(`audio-${index}`);
      audioElement.pause();
      setTimeout(() => {
        audioElement.play();
      }, 100);
    },
    getInfo1(id) {
      getAction(`/iot/findEquipNodeById/${id}`).then(res => {
        if (res.data.code == 200) {
          this.alarmDetailData = res.data.data;
          console.log(this.alarmDetailData, "this.alarmDetailData");
          this.getEquipmentDrawing(this.alarmDetailData.pojo.equipRoomId);
          this.dlgShow = true;
          this.$nextTick(() => {
            this.$refs["dlgForm"].clearValidate();
          });
        } else {
          this.$message({
            type: "warning",
            message: res.data.msg
          });
        }
      });
    },
    // 弹窗提交
    subGiveAlarmDlg() {
      this.$refs["dlgForm"].validate(valid => {
        if (valid) {
          if (this.giveAlarmRow.status == 1 && this.dlgData.status != 2) {
            this.$message.error("处理中报警不允许重复提交!");
            return false;
          }
          let sendObj = JSON.parse(JSON.stringify(this.dlgData));
          sendObj.id = this.giveAlarmRow.id;
          if (sendObj.status == "1") {
            sendObj.handlerId = 0;
          }
          if (this.giveAlarmRow.status == "1") {
            sendObj.handlerHours = this.giveAlarmRow.handlerHours;
          }
          // console.log(sendObj,"sendObj");
          // return
          postAction("/iot/aas/updateStatus/v2", sendObj).then(res => {
            console.log(res);
            if (res.data.code == 200) {
              if (this.dlgShow && !this.alarmRecordDlg) {
                this.dlgShow = false;
                this.getList();
              }
              if (this.alarmRecordDlg) {
                this.dlgShow = false;
                this.getAlarmRecord();
                this.getList();
              }
              this.$message.success(res.data.msg);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    //报事
    showPostItDlg(row) {
      this.postItDlg = true;
      this.dlgQuery = row;
    },
    closeDlg(type) {
      this.postItDlg = false;
      if (type == "add") {
        this.getList();
      }
    },
    // 播放语音
    playAudio(index) {
      let audioElement = document.getElementById(`audio-${index}`);
      audioElement.pause();
      setTimeout(() => {
        audioElement.play();
      }, 100);
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.el-table {
  .color-warn1 {
    color: red;
  }
  .color-warn2 {
    color: orange;
  }
  .color-warn3 {
    color: blue;
  }
}

.card2_1 {
  /deep/.el-card__body {
    height: 270px;
    overflow-y: auto;
  }
}

.card2_2 {
  /deep/.el-card__body {
    height: 580px;
    overflow-y: auto;
  }
}
.drop-right-img {
  // height: 100%;
  width: 400px;
  height: 202px;
  -webkit-user-drag: none;
}
.oneBtn {
  position: absolute;
  right: 20px;
  top: 22px;
}

.giveAlarmDlgCol {
  border: 1px solid #eff6ff;
  height: 260px;
  padding: 15px;
  background-color: #f8faff;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.towBtn {
  position: absolute;
  right: 20px;
  top: 0;
}

.alarmMessageListBox {
  display: flex;
  justify-content: space-between;
  font-weight: bolder;
  background-color: #fff7f6;
  margin-bottom: 10px;
  padding: 6px;
}

.deviceViewBox {
  // font-weight: bolder;
  // margin-bottom: 10px;
  padding: 10px;
  border-bottom: 1px solid #f1efef;
  // position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card1 {
  /deep/.el-card__body {
    height: calc(100% - 53px);
    overflow-y: auto;
  }
}

.echart {
  height: 200px;
}

////////////////
.list-yuan-btn {
  height: 28px;
  line-height: 28px;

  padding: 0 10px;
  border-radius: 14px;
  margin-right: 10px;
}

.bg-red {
  background: #ff0900 !important;
  color: white !important;
  box-shadow: 0 4px 10px #ffc5c3 !important;
}

.bg-yellow {
  background: #fef6e2;
  color: #e5ab35;
}

.bg-blue {
  background: #ecf5ff;
  color: #5eadfe;
}

.bg-green {
  background: #e2f9ee;
  color: #69dfb0;
}

//
.item-bg-red {
  background: linear-gradient(to right, #fff2f1, #fff, #fff, #fff, #fff);
}

//
.ml16 {
  margin-right: 16px;
}
.drop-left-p {
  position: absolute;
  z-index: 1;
}
.icon_cgq_right {
  width: 20px;
}
.icon_cgq_right.success {
  animation-name: animation_success; /* 执行动画名称 */
  background-color: #e1f3d8; /* 背景颜色 */
}
.icon_cgq_right.danger {
  animation-name: animation_danger; /* 执行动画名称 */
  background-color: #fde2e2; /* 背景颜色 */
}
.icon_cgq_right.warning {
  animation-name: animation_warning; /* 执行动画名称 */
  background-color: #faecd8; /* 背景颜色 */
}
// #dqhz .el-card__body {
//   padding: 0 !important;
// }
/deep/.el-card__body {
  padding: 0;
  // padding-left: 20px;
}
</style>
