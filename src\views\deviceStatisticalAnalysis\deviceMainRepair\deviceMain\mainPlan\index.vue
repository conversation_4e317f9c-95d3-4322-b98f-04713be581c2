<template>
  <div class="app-container mazhenguo" style="margin-bottom: 32px; padding-bottom: 10px">
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <!-- <div class="search-item-label lh28">选择项目：</div>
            <el-select
              class="fl"
              style="width: 220px"
              v-model="listQuery.projectId"
              placeholder="选择项目"
              @change="searchFunc"
              filterable
              clearable
            >
              <el-option
                v-for="item of projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>

          <div class="search-item"> -->
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input class="fl" style="width: 160px" v-model="listQuery.label" @keyup.enter.native="searchFunc"
              placeholder="关键字" clearable>
            </el-input>
            <el-button class="fl ml10" @click="searchFunc" icon="el-icon-search" type="primary">查询</el-button>
            <!-- <el-button
              class="fl ml10"
              @click="showDlg('add', {})"
              icon="el-icon-plus"
              type="success"
              >添加</el-button
            >
            <el-button
              class="fl ml10"
              @click="showPlanExeDlg('add', {})"
              icon="el-icon-plus"
              type="success"
              >单次任务新增</el-button
            > -->
          </div>
        </div>
      </div>
    </div>

    <el-table height="calc(100vh - 288px)" ref="tableRef" class="m-small-table" v-loading="listLoading" :key="tableKey"
      :data="list" border fit highlight-current-row>
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="设备编码" align="center" prop="equCode" width="auto">
      </el-table-column>

      <el-table-column label="计划名称" align="center" prop="name" show-overflow-tooltip />

      <el-table-column label="设备名称" align="center" prop="equName" show-overflow-tooltip />
      <el-table-column label="设备型号" align="center" prop="equModel" show-overflow-tooltip />
      <el-table-column label="设备位置" align="center" prop="equPosition" show-overflow-tooltip />
      <el-table-column label="设备保养周期" align="center" prop="maintenanceCycleStr" width="120px" />
      <el-table-column label="保养事项" align="center" prop="maintenanceItem" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="scope.row.maintenanceItem.length == 0"></div>
          <div v-else>
            <span v-for="(item, index) of scope.row.maintenanceItem" :key="index">
              <span :title="item.value">{{ item.name }}</span>
              <span v-if="index != scope.row.maintenanceItem.length - 1">,</span>
            </span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="保养方法"
        align="center"
        prop="maintenanceMethod"
        show-overflow-tooltip
      /> -->
      <el-table-column label="岗位" align="center" prop="maintenancePersonName" show-overflow-tooltip />
      <!-- <el-table-column label="操作" width="240" align="center">
        <template slot-scope="scope">
          <el-button
            @click="showDlg('copy', scope.row)"
            icon="el-icon-document-copy"
            size="mini"
            type="primary"
            title="克隆"
            plain
            >克隆</el-button
          >
          <el-button
            @click="showDlg('edit', scope.row)"
            icon="el-icon-edit"
            size="mini"
            type="primary"
            title="编辑"
            plain
            >编辑</el-button
          >
          <el-button
            @click="delFunc(scope.row)"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            title="删除"
            plain
            >删除</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>
    <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size"
      @pagination="getList" />
    <div class="clear"></div>
    <addDlg ref="addDlg" :dlgData0="dlgData" :dlgType="dlgType" :dlgQuery="dlgQuery" 
      @getList="getList" />
    <addPlanExeDlg ref="addPlanExeDlg" :dlgData0="dlgPlanExeData" :dlgType="dlgPlanExeType"
      :dlgQuery="dlgPlanExeQuery" />
    <el-dialog :title="'设备二维码-' + selectRow.equName" :visible.sync="dialogVisible" width="300px">
      <div class="qrbox2">
        <div v-if="dialogVisible" ref="qrCode"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as utils from "@/utils";
import { postAction, getAction, deleteAction } from "@/api";
import { MxDfProject } from "@/mixins/MxDfProject.js";
import QRCode from "qrcodejs2";
import Pagination from "@/components/Pagination"; // 分页
import addDlg from "./addDlg";
import addPlanExeDlg from "../mainPlanExe/addDlg";

let listQueryEmpty = {
  label: "", //	模糊查询	body	false	string
  page: 1,
  size: 20,
  projectId: '',
};
export default {
  components: {
    Pagination,
    addDlg,
    addPlanExeDlg
  },
  mixins: [MxDfProject],
  props: {},
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),
      searchMoreState: false,
      tableKey: 0,
      list: [],
      selectList: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      dlgQuery: {},
      dlgPlanExeQuery: {},
      dlgType: "",
      dlgPlanExeType: "",
      dlgData: {},
      dlgPlanExeData: {},
      dialogVisible: false,
      selectRow: ""
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getList();
  },
  mounted() { },
  methods: {
    showQrCode(row) {
      this.selectRow = row;
      this.dialogVisible = true;
      setTimeout(() => {
        let qrUrl = row.equCode + "";
        this.qrcode = new QRCode(this.$refs.qrCode, {
          text: qrUrl,
          width: 200,
          height: 200,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H
        });
      }, 200);
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },
    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    getList() {
      this.listQuery.projectId = this.userInfo.projectId
      this.list = [];
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      sendObj.equName = sendObj.label;
      delete sendObj.label;
      sendObj.pageNo = sendObj.page;
      delete sendObj.page;
      sendObj.pageSize = sendObj.size;
      delete sendObj.size;
      this.listLoading = true;
      getAction("/green/equ/maintenance/page", sendObj).then(res0 => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = [];
            this.total = 0;
          } else {
            let list = res.data.list;
            for (let item of list) {
              if (item.maintenanceItem) {
                item.maintenanceItem = JSON.parse(item.maintenanceItem);
              } else {
                item.maintenanceItem = [];
              }
            }
            console.log('=====list', list)
            this.list = list
            this.total = res.data.total;

            this.$nextTick(() => {
              this.$refs.tableRef.doLayout();
            });
          }
        } else {
          this.total = 0;
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    delFunc(row) {
      this.delAjax(row.id);
    },
    batchDelFunc() {
      if (!this.selectList.length) return false;
      this.delAjax(sendObj);
    },
    delAjax(id) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        deleteAction("/green/equ/maintenance/delete?id=" + id).then(res0 => {
          let res = res0.data;
          if (res.code == "200") {
            this.$message({
              message: res.msg,
              type: "success"
            });
            this.getList();
          } else {
            this.$message({
              message: res.msg,
              type: "error"
            });
          }
        });
      });
    },

    showDlg(type, row) {
      this.dlgQuery = row; // 查询条件
      if (type == "add") {
        this.dlgData = { id: 0 }; // 表单数据
      } else {
        this.dlgData = row;
      }
      this.dlgType = type;
      console.log('888888')
      this.$refs.addDlg.dlgState = true;
    },
    showPlanExeDlg(type, row) {
      this.dlgPlanExeQuery = row; // 查询条件
      this.dlgData = { id: 0 }; // 表单数据
      this.$refs.addPlanExeDlg.isZhixing = false;
      this.dlgPlanExeType = type;
      this.$refs.addPlanExeDlg.dlgState = true;
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.qrbox2 {
  padding: 36px 0 64px;
  width: 200px;
  height: 200px;
  margin: 0 auto;
  box-sizing: content-box;


}
</style>
