<template>
  <!-- 异常告警管理 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline @submit.native.prevent>
        <el-form-item label="创建时间:">
          <el-date-picker
            v-model="listQuery.dateRange"
            type="daterange"
            range-separator="~"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-select v-model="listQuery.equipId" clearable placeholder="请选择设备间" style="width: 180px">
            <el-option v-for="item of sbjSelect" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-select v-model="listQuery.status" clearable placeholder="处理状态" style="width: 100px">
            <el-option v-for="item of statusSelect" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-select v-model="listQuery.alertType" clearable filterable placeholder="异常类型" style="width: 140px">
            <el-option v-for="item of alertSelect" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="listQuery.label" placeholder="请输入关键字" style="width: 140px">
            <i @click="clearQuery(['label'])" slot="suffix" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="searchFunc">搜索</el-button>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        class="m-small-table el-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :row-class-name="tableRowClassName"
      >
        <el-table-column label="序号" width="60" align="center">
          <template slot-scope="scope">
            <span>{{ (listQuery.page - 1) * listQuery.limit + scope.$index + 1 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警时间" width="140">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警对象">
          <template slot-scope="scope">
            <span v-if="scope.row.type==0" class="m-a" @click="showBjDia(scope.row)">{{
              scope.row.nodeName
            }}</span>
             <span v-else>{{ scope.row.nodeName }}</span>
          </template>
           <!-- <template slot-scope="scope">
            <span class="m-a" @click="showBjDia(scope.row)">{{ scope.row.nodeName }}</span>
          </template> -->
        </el-table-column>

        <el-table-column label="报警类型">
          <template slot-scope="scope">
            <span>{{ scope.row.alertName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警级别" width="90" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.alarmLevel == '1'">一级报警</span>
            <span v-if="scope.row.alarmLevel == '2'">二级报警</span>
            <span v-if="scope.row.alarmLevel == '3'">三级报警</span>
          </template>
        </el-table-column>

        <el-table-column label="报警内容" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.content }}</span>
          </template>
        </el-table-column>

        <el-table-column label="设备间名称">
          <template slot-scope="scope">
            <span>{{ scope.row.equipRoomName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="设备名称">
          <template slot-scope="scope">
            <span>{{ scope.row.equipName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="单位" width="70" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.unit }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警值">
          <template slot-scope="scope">
            <span>{{ scope.row.alarmValue }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报警限值">
          <template slot-scope="scope">
            <span>{{ scope.row.endValue }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="70" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.status == 0" class="fblur">待处理</span>
            <span v-if="scope.row.status == 1" class="fwarning">处理中</span>
            <span v-if="scope.row.status == 2" class="fsuccess">已完成</span>
            <span v-if="scope.row.status == 3" class="finfo">已忽略</span>
          </template>
        </el-table-column>

        <el-table-column label="处理人" width="70" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.handler }}</span>
          </template>
        </el-table-column>

        <el-table-column label="处理/忽略备注" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.handlerRemark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="处理时间">
          <template slot-scope="scope">
            <span>{{ scope.row.handlerTime }}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status == 0"
              icon="el-icon-check"
              type="primary"
              size="mini"
              @click="editFunc(scope.row, 'HANDLE')"
              plain
              >处理</el-button
            >
            <el-button
              v-if="scope.row.status == 0"
              icon="el-icon-close"
              type="warning"
              size="mini"
              @click="editFunc(scope.row, 'IGNORE')"
              plain
              >忽略</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal="false" title="异常告警处理" :visible.sync="dlgShow" top="30px" append-to-body>
      <el-form ref="dlgForm" :rules="dlgRules" :model="dlgData" label-width="80px">
        <el-form-item label="备注：" prop="remark">
          <el-input type="textarea" :autosize="{ minRows: 5, maxRows: 10 }" placeholder="请输入备注" v-model="dlgData.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-back" @click="dlgShow = false">取 消</el-button>
        <el-button type="success" icon="el-icon-check" :loading="dlgLoading" @click="submitDlg">保 存</el-button>
      </div>
    </el-dialog>

    <!-- << 弹窗 报警信息 -->
    <el-dialog
      title="历史记录"
      :close-on-click-modal="false"
      :append-to-body="true"
      :visible.sync="diaBjState"
      width="1200px"
      top="30px"
      icon-class="el-icon-info"
    >
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="列表" name="liebiao">
      <el-date-picker
        style="width: 300px"
        class="fl"
        @change="getBjList"
        v-model="diaBjQuery.dateRange"
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      >
      </el-date-picker>
      <el-button icon="el-icon-search" type="success" size="mini" class="search-right-btn fl" @click="getBjList">搜索</el-button>

      <div class="clear"></div>

      <el-table
        ref="tableBar"
        class="m-small-table mt10"
        v-loading="listLoading"
        :key="diaBjTableKey"
        :data="diaBjList"
        border
        fit
        max-height="500px"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="#" type="index" align="center" width="70">
          <template slot-scope="scope">
            <span>{{ (diaBjQuery.page - 1) * diaBjQuery.limit + scope.$index + 1 }}</span>
          </template>
        </el-table-column>

        <el-table-column v-for="(item, index) of diaBjTHList" :key="index" :label="item.label">
          <template slot-scope="scope">
            <span>{{ scope.row[item.key] }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        class="mt10"
        v-show="diaBjTotal > 0"
        :total="diaBjTotal"
        :page.sync="diaBjQuery.page"
        :limit.sync="diaBjQuery.limit"
        @pagination="getBjList"
      />
      </el-tab-pane>
      <el-tab-pane label="趋势" name="qushi">
       <el-date-picker
          class="fl ml10"
          style="width: 350px"
          @change="getZxt"
          v-model="zxtQuery.dateRange"
          type="datetimerange"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          size="mini"
        >
        </el-date-picker>
        <el-select
        @change="handleSelectChange"
        class="fl ml10"
        style="width: 200px"
    v-model="zxtQuery.disRespVos"
    multiple
    collapse-tags
    placeholder="请选择">
    <el-option
      v-for="item in options"
      :key="item.type"
      :label="item.name"
      :value="`${item.type},${item.name}`">
    </el-option>
  </el-select>
        <el-button icon="el-icon-search" type="success" size="mini" class="search-right-btn fl" @click="getZxt">搜索</el-button>
  <div class="clear"></div>

        <div
          v-if="showChart2"
          id="echart-bar2"
          style="height: 500px; margin-top: 16px"
        ></div>
    </el-tab-pane>
    </el-tabs>
      <div class="clear"></div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="diaBjState = false" icon="el-icon-back">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination' // 分页
import * as utils from '@/utils'
import {
  arrId2Name, // 根据id 获取name
  isNull
} from '@/utils'
import * as echarts from "echarts";
import moment, { localeData } from 'moment'//导入文件
import { getAction, postAction } from '@/api'
// 接口
import {
  findAbnormalAlarmPage, // 分页
  handleAlarm, // 处理
  ignoreAlarm, // 忽略
} from '@/api/mzgApi'

import {
  findEquioPage, // 设备间
  protocolLoran
} from '@/api/safetyMonitoringApi'

let dlgDataEmpty = {
  id: '',
  remark: '',
}
// 报警信息弹窗
let diaBjQueryEmpty = {
  id: '',
  label: '',
  page: 1,
  limit: 10,
  dateRange: [],
}
//折线图
let zxtQueryEmpty={
  disRespVos:[],
  nodeId:'',
  startTime:'',
  endTime:'',
  dateRange: [],
}
export default {
  components: {
    Pagination,
  },
  data() {
    return {
      sbjSelect: [], // 设备间
      dlgRules: {
        remark: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },
      list: [],
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        status: '', // 0:待处理 1:处理中 2:已完成 不传查所有
        beginDate: '',
        endDate: '',
        dateRange: [],

        equipId: '',
        alertType: '',
      },
      statusSelect: [
        { id: '0', name: '待处理' },
        { id: '1', name: '处理中' },
        { id: '2', name: '已完成' },
      ],
      alertSelect: [
     { id: "a1001", name: "水压高" },
  { id: "a1002", name: "水压低" },
  { id: "a1003", name: "水位高" },
  { id: "a1004", name: "水位低" },
  { id: "a1005", name: "温度高" },
  // { id: "a1006", name: "温度低" },
  { id: "a1007", name: "湿度高" },
  { id: "a1008", name: "湿度低" },
  // { id: "d", name: "光照高" },
  // { id: "a1010", name: "光照低" },
  { id: "a1011", name: "过压" },
  { id: "a1012", name: "欠压" },
  // { id: "a1013", name: "电量低" },
  { id: "a1014", name: "人体活动" },
  // { id: "a1015", name: "长时间未上报" },
  { id: "a1016", name: "烟雾报警" },
  { id: "a1017", name: "三相电压不平衡" },
  { id: "a1018", name: "三相电流不平衡" },
  { id: "a1019", name: "高负载" },
  // { id: "a1020", name: "低负载" },
  { id: "a1021", name: "漏水报警" },
  { id: "a1022", name: "可燃气体报警" },
  { id: "a1023", name: "环境温度高" },
  { id: "a1024", name: "环境温度低" },
  { id: "a1025", name: "线缆温度高" },
  // { id: "a1026", name: "线缆温度低" },
  { id: "a1027", name: "有功功率高" },
  { id: "a1028", name: "有功功率低" },
  // { id: "a1029", name: "无功功率高" },
  // { id: "a1030", name: "无功功率低" },
  //{ id: "a1031", name: "视在功率高" },
  // { id: "a1032", name: "视在功率低" },
  { id: "a1033", name: "井盖打开" },
  { id: "a1034", name: "漏电电流高" },
  // { id: "a1035", name: "漏电电流低" },
  { id: "a1036", name: "差压高" },
  { id: "a1037", name: "差压低" },
  // { id: "a1038", name: "可燃气体浓度高" },
  // { id: "a1039", name: "可燃气体浓度低" },
  { id: "a1040", name: "短路报警" },
  { id: "a1041", name: "浪涌报警" },
  { id: "a1042", name: "缺项报警" },
  { id: "a1043", name: "打火报警" },
  { id: "a1044", name: "掉电报警" },
  { id: "a1045", name: "离线报警" },
  { id: "109", name: "烟火监测报警" },
  { id: "110", name: "烟雾监测报警" },
  { id: "a1048", name: "瞬时流量高" },
  { id: "a1049", name: "瞬时流量低" }
  // {id: "18",name: "移动侦测报警"},
  // {id: "20",name: "门铃按键报警"},
  // {id: "38",name: "烟感检测报警"},
  // {id: "39",name: "离岗检测报警"},
  // {id: "40",name: "婴儿哭声检测报警"},
  // {id: "41",name: "人形检测报警"},
      ],
      total: 0,
      listLoading: false,

      dlgType: '',
      dlgLoading: false,
      dlgShow: false,
      dlgData: {},
       // << 弹窗-报警信息
      diaBjTableKey: 0,
      diaBjState: false,
      diaBjTHList: [],
      diaBjList: [],
      diaBjTotal: 0,
      diaBjQuery: JSON.parse(JSON.stringify(diaBjQueryEmpty)),
      // >> 弹窗-报警信息
        // << 弹窗 设置计划
      activeName: 'liebiao',
      zxtQuery:JSON.parse(JSON.stringify(zxtQueryEmpty)),
      options:[],
      bjDiaRow:{},
      showChart2: false,
       echartRoom2: null,
       zxtSelect:[]
    }
  },
  created() {
    if (this.$route.query.status) {
      this.listQuery.status = this.$route.query.status
    }
    if (this.$route.query.today) {
      this.listQuery.dateRange = [this.$route.query.today, this.$route.query.today]
    }
    this.getSbjSelect() // 获取设备间
    this.getList()
  },
  methods: {
      //tab切换
    handleClick(tab, event) {
      this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty))
      this.zxtQuery=JSON.parse(JSON.stringify(zxtQueryEmpty))
      this.diaBjQuery.id = this.bjDiaRow.nodeId
      this.zxtSelect=[]
      console.log(tab, event);
      if (tab.name=='liebiao') {
        this.getBjList()
      }else{
        const now = moment();
        const start =moment(now).subtract(3, 'hours');
        const end =  moment().format("YYYY-MM-DD HH:mm");
        let startTime = moment(start).format('YYYY-MM-DD HH:mm');
        // let endTime = moment(end).format('YYYY-MM-DD HH:mm');
        this.zxtQuery.dateRange=[startTime,end]
        this.getDxList()
        this.getZxt()
      }
    },
    getDxList(){
      getAction(`/iot/trend/nodeDataDis/${this.bjDiaRow.nodeId}`).then((res1)=>{
        let res=res1.data
          if (res.code==200) {
            this.options=res.data
          }else{
            this.$message.error(res.msg)
          }
      })
    },
    handleSelectChange(){
      this.zxtSelect=[]
      this.zxtQuery.disRespVos.forEach(element => {
        console.log(element,"element");
          let [type, name] = element.split(",");
        this.zxtSelect.push({ type, name });
  });
    },
    getZxt(){
      this.showChart2 = false;
      if (isNull(this.zxtQuery.dateRange)||this.zxtQuery.dateRange.length<=0) {
        this.$message.warning('请先选择起止时间')
        return false;
      }
      let sendObj=JSON.parse(JSON.stringify(this.zxtQuery))
      // 日期范围
      sendObj.startTime = "";
      sendObj.endTime = "";
      if (
        !isNull(this.zxtQuery.dateRange) &&
        this.zxtQuery.dateRange.length > 0
      ) {
        sendObj.startTime = this.zxtQuery.dateRange[0];
        sendObj.endTime = this.zxtQuery.dateRange[1];
      }
      sendObj.disRespVos=this.zxtSelect
      sendObj.nodeId=this.bjDiaRow.nodeId
      let loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      postAction('/iot/trend/nodeDataTrend',sendObj).then((res1)=>{
        loading.close()
        let res=res1.data
        if(res.code==200){
           if (!utils.isNull(res.data) && res.data.list.length > 0) {
            this.showChart2 = true;
            // this.list = res.data;
            setTimeout(() => {
              this.setEchartBar2(res.data.list,res.data.times);
              //   this.createRoom(res.data.list)
            }, 100);
          }
        }
      })
    },
    //创建折线图
    setEchartBar2(arr, dataMap) {
      console.log(arr, "arr");
      if (this.showChart2 == false) {
        // if (!utils.isNull(arr)) {
        //   this.echartRoom2.clear();
        // }
        return;
      }
      // << 本月1号到当天
      let xList = [];
      let xList0 = [];
      // let dateObj = new Date();
      // console.log("dateObj.getDate()", dateObj.getDate());
      // console.log(this.getEveryDayDateByBetweenDate(this.listQuery.dateRange[0],this.listQuery.dateRange[1]),'时间间隔');
      // let dayNum = parseInt(dateObj.getDate());
      // let month = utils.return2Num2(dateObj.getMonth() + 1);
      // let year = dateObj.getFullYear();
      // for (let i = 0; i < dayNum; i++) {
      //   let key = `${year}-${month}-${utils.return2Num2(i + 1)}`;
      //   xList.push(key);
      //   xList0.push(`${year}-${month}-${utils.return2Num2(i + 1)}`);
      // }

      // 拼接数据
      let data = [];
      let listData = [];
      for (let index = 0; index < dataMap.length; index++) {
        let obj = {
          yearMonthDate: dataMap[index],
          count: 0,
          type: ""
        };
        listData.push(obj);
      }
      for (let i = 0; i < arr.length; i++) {
        let itemLine = arr[i];
        let lineObj = {
          name: itemLine.name,
          type: "line",
          stack: "",
          data: []
        };
        let map = itemLine.list;
        // console.log('111111111map', map)
        // console.log('111111111listData', listData)
        for (let key = 0; key < map.length; key++) {
          for (let k = 0; k < listData.length; k++) {
            if (map[key].time == listData[k].yearMonthDate) {
              lineObj.data.push(map[key].value)
              // listData[k].value = map[key].value;
              // listData[k].type = map[key].type;
            }
          }
        }
        data.push(lineObj)
        // let arrData = [];
        // console.log("==listData", listData);
        // for (let o = 0; o < listData.length; o++) {
        //   arrData.push(listData[o].value);
        // }
        // console.log(arrData, "arrData");
        // lineObj.data = arrData;
        // data.push(lineObj);
      }
      console.log(data,"data--------------");
      // xList0.push(map[key].yearMonthDate)
      xList0 = dataMap;
      // 绘制图标
      var myChart = echarts.init(document.getElementById("echart-bar2"));
      var option = {
        title: {
          text: ""
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },

        legend: {
          left: 10
        },
        grid: {
          left: "2%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false, // true-刻度中间 false-刻度线上
          data: xList0
        },
        yAxis: {
          type: "value"
          // name: '单位（吨）',
          // nameTextStyle: {
          //   color: '#aaa',
          //   nameLocation: 'start',
          // },
        },
        series: data
        // series: [[1,2,3],[12,22,32],[13,23,33]]
      };
      myChart.clear();
      myChart.setOption(option);
      myChart.on("click", param => {
        // console.log('param', param)
        // // componentIndex
        // // dataIndex
        // let msg = `${this.echartLineData[param.componentIndex].name}：${
        //   this.echartLineData[param.componentIndex].data[param.dataIndex]
        // }`
        // alert(msg)
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    showBjDia(row) {
      // 调用接口
      this.activeName='liebiao',
      this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty))
      this.diaBjQuery.id = row.nodeId
      this.bjDiaRow=row
      this.zxtQuery=JSON.parse(JSON.stringify(zxtQueryEmpty))
      this.getBjList()
    },
    // 获取列表
    getBjList() {
      let beginTime = ''
      let endTime = ''
      if (this.diaBjQuery.dateRange != null && this.diaBjQuery.dateRange.length != 0) {
        beginTime = this.diaBjQuery.dateRange[0]
        endTime = this.diaBjQuery.dateRange[1]
      }
      let sendObj = {
        page: this.diaBjQuery.page,
        limit: this.diaBjQuery.limit,
        id: this.diaBjQuery.id,
        beginTime,
        endTime,
      }

      let loading = this.$loading({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      protocolLoran(sendObj).then((res1) => {
        loading.close()
        let res = res1.data
        if (res.code == '200') {
          if (res.data == null) {
            this.diaBjTotal = 0
            this.$message({
              type: 'warning',
              message: '暂无报警信息',
            })
            return false
          }
          // 表头
          let diaBjTHList = []
          for (let key in res.data.field) {
            let label = res.data.field[key]
            let obj = {
              key,
              label,
            }
            diaBjTHList.push(obj)
          }
          this.diaBjTHList = diaBjTHList

          // 表格数据
          this.diaBjTotal = res.data.total

          this.diaBjList = res.list
          this.diaBjState = true
          this.diaBjTableKey++
        } else {
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },
    // 高亮
    tableRowClassName({ row, rowIndex }) {
      if (row.alarmLevel == '1') {
        return 'color-warn3'
      } else if (row.alarmLevel == '2') {
        return 'color-warn2'
      } else if (row.alarmLevel == '3') {
        return 'color-warn1'
      }
      return ''
    },
    // << 获取设备间
    getSbjSelect() {
      let sendObj = {
        label: '',
        page: 1,
        limit: 9999,
        isEquipRoom: 0, // 0设备间 1设备 不传查所有
        equipType: 2
      }
      if (this.$route.path=='/electricalFireMonitoring/statisticalAnalysis/warnInfoQuery') {
        sendObj.equipType=2
      }
      if (this.$route.path=='/environmentalSafety/statisticalAnalysis/warnInfoQuery') {
        sendObj.equipType=3
      }
      if (this.$route.path=='/energyMonitoring/statisticalAnalysis/warnInfoQuery') {
        sendObj.equipType=4
      }
      findEquioPage(sendObj).then((res1) => {
        this.listLoading = false
        let res = res1.data
        if (res.code == '200') {
          this.sbjSelect = JSON.parse(JSON.stringify(res.list))
        } else {
          this.$message({
            type: 'warning',
            message: res.msg,
          })
        }
      })
    },
    // >> 获取设备间
    getList() {
      this.list = []
      this.listLoading = true
      this.listQuery.beginDate = this.listQuery.dateRange ? this.listQuery.dateRange[0] : ''
      this.listQuery.endDate = this.listQuery.dateRange ? this.listQuery.dateRange[1] : ''

      let sendObj = JSON.parse(JSON.stringify(this.listQuery))
      delete sendObj.dateRange

      sendObj.equipRoomName = utils.arrId2Name(this.sbjSelect, sendObj.equipId)
      if (this.$route.path=='/electricalFireMonitoring/statisticalAnalysis/warnInfoQuery') {
        sendObj.equipType=2
      }
      if (this.$route.path=='/environmentalSafety/statisticalAnalysis/warnInfoQuery') {
        sendObj.equipType=3
      }
      if (this.$route.path=='/energyMonitoring/statisticalAnalysis/warnInfoQuery') {
        sendObj.equipType=4
      }
      delete sendObj.equipId

      findAbnormalAlarmPage(sendObj).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.total = res.data.data.total
          this.list = JSON.parse(JSON.stringify(res.data.list))
        } else {
          this.$message.warning(res.data.msg)
        }
      })
    },

    clearQuery(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.searchFunc()
    },

    searchFunc() {
      this.getList()
    },

    editFunc(data, type) {
      this.dlgType = type
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.id = data.id
      this.dlgShow = true
    },

    // 弹窗提交
    submitDlg() {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          this.dlgLoading = true
          let method = this.dlgType === 'HANDLE' ? handleAlarm : ignoreAlarm
          method(postParam).then((res) => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss">
.el-table {
  .color-warn1 {
    color: red;
  }
  .color-warn2 {
    color: orange;
  }
  .color-warn3 {
    color: blue;
  }
}
</style>