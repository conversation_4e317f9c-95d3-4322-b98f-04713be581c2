import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

import Layout from "@/views/layout/Layout"; //Layout

import doorWarnSys from "./modules/doorWarnSys"; // 门磁报警
import greenManRouter from "./modules/greenManRouter"; // 平台管理
import deviceStatisticalAnalysisRouter from "./modules/deviceStatisticalAnalysisRouter"; // 设备统计分析
import deviceManRouter from "./modules/deviceManRouter"; // 设备管理

import siteManRouter from "./modules/siteManRouter"; // 场地管理
import canteenManRouter from "./modules/canteenManRouter"; // 食堂管理

import platformManRouter from "./modules/platformMan"; // 平台管理
import dataCenterRouter from "./modules/dataCenter"; // 数据中心
import equipSafeManRouter from "./modules/equipSafeMan"; // 设备安全管理
import environmentalSafetyRouter from "./modules/environmentalSafety"; //环境安全管理
import energyMonitoringRouter from "./modules/energyMonitoring"; //能源监控
import liftManRouter from "./modules/liftMan"; // 电梯监控
import electricalFireMonitoringRouter from "./modules/electricalFireMonitoring"; // 电气火灾监控
// import alarmConfigurationRouter from "./modules/alarmConfiguration"; // 电气火灾监控
import heatManRouter from "./modules/heatMan"; // 供热管理
import energyManRouter from "./modules/energyMan"; // 能源管理
import attendanceManRouter from "./modules/attendanceMan"; // 考勤管理
import transportManRouter from "./modules/transportMan"; // 运送管理
import clothManRouter from "./modules/clothMan"; // 被服管理
import escortBedRentRouter from "./modules/escortBedRent"; // 陪护床租用
import escortManRouter from "./modules/escortMan"; // 陪护服务
import healthPolicyManRouter from "./modules/healthPolicyMan"; // 医保政策
import accompanyManRouter from "./modules/accompanyMan"; // 陪诊陪检
import followManRouter from "./modules/followMan"; // 随访管理
import medicalWasteManRouter from "./modules/medicalWasteMan"; // 医废管理
import processMan from "./modules/processMan"; // 业务协同
import medicalRecordManRouter from "./modules/medicalRecordMan"; // 病历服务
import matterManRouter from "./modules/matterMan"; // 报事管理
import qualityManRouter from "./modules/qualityMan"; // 品质管理/旧
import communityManRouter from "./modules/communityMan"; // 小区管理
import propertyManRouter from "./modules/propertyMan"; //
import flowToneManRouter from "./modules/flowToneManRouter"; //  船口-流调管理
import flowTabManRouter from "./modules/flowTabManRouter"; //  六院-流调管理
import invoicingRouter from "./modules/invoicingRouter"; //  进销存
import disinfectRouter from "./modules/disinfectRouter"; //  消毒管理

import reportMan from "./modules/reportMan"; // 报事管理
import qualitySystem from "./modules/qualitySystem"; // 品质管理
import qualityControl from "./modules/qualityControl"; // 品质控制
import administrationOA from "./modules/administrationOA"; // 行政OA

export const constantRouterMap = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path*",
        component: () => import("@/views/redirect/index")
      }
    ]
  },
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true
  },
  {
    path: "/auth-redirect",
    component: () => import("@/views/login/authredirect"),
    hidden: true
  },

  // 页面跳转中继页面
  {
    path: "/white",
    component: () => import("@/views/white/white"),
    hidden: true
  },
  {
    path: "/white-device",
    component: () => import("@/views/white/white-device"),
    hidden: true
  },
  {
    path: "/deviceMan/mbInitiateProcess",
    component: () =>
    import("@/views/deviceMan/dynamicForm/MbInitiateProcess"),
    hidden: true
  },

  // 查看费用-打印
  {
    path: "/printPaper",
    component: () =>
      import("@/views/communityProperty/costMan/printComponent/printPaper"),
    hidden: true
  },

  // 船口-打印
  {
    path: "/chuankouPrintPaper",
    component: () => import("@/components/PrintPager/chuankouPrintPaper"),
    hidden: true
  },
  // 六院-打印
  {
    path: "/sixHospitalPaper",
    component: () => import("@/components/PrintPager/sixHospitalPaper"),
    hidden: true
  },

  // 体育医院打印
  {
    path: "/printsportsRehabilitationHospital",
    component: () =>
      import("@/components/PrintPager/printsportsRehabilitationHospital"),
    hidden: true
  },

  // 承租证打印
  {
    path: "/tenancyContractPrint",
    component: () => import("@/components/PrintPager/tenancyContractPrint"),
    hidden: true
  },

  {
    path: "/receiptPrintPaper",
    component: () => import("@/views/communityProperty/costMan/receiptManagement/pendingReceipt/receiptPrintPaper"),
    hidden: true
  },
  {
    path: "/disinfectionStatisticsQueryPrint",
    component: () =>
      import("@/views/disinfectMan/disinfectionStatisticsQuery/disinfectionStatisticsQueryPrint"),
    hidden: true
  },

  // ---- 进销存
  // 打印盘点
  {
    path: "/printPandian",
    component: () =>
      import("@/views/invoicingMan/storeMan/inventoryMan/printPandian"),
    hidden: true
  },
  // 出库-退货出库打印
  {
    path: "/print_returnOrder",
    component: () =>
      import("@/views/invoicingMan/storeMan/inventoryMan/print_returnOrder"),
    hidden: true
  },

  {
    path: "/404",
    component: () => import("@/views/errorPage/404"),
    hidden: true
  },
  {
    path: "/401",
    component: () => import("@/views/errorPage/401"),
    hidden: true
  },
  {
    // 首页
    path: "",
    component: Layout,
    redirect: "dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index"),
        name: "首页",
        meta: {
          title: "首页",
          icon: "dashboard",
          noCache: true,
          roles: ["shouye"]
        }
      }
    ]
  }
];

export default new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRouterMap
});

export const asyncRouterMap = [
  deviceManRouter,
  deviceStatisticalAnalysisRouter,
  greenManRouter,
  siteManRouter,
  canteenManRouter,
  administrationOA, // 行政OA

  doorWarnSys,
  flowTabManRouter, // 六院-流调
  flowToneManRouter, // 船口-流调管理

  platformManRouter,
  matterManRouter,
  qualityManRouter,
  dataCenterRouter,//数据中心
  equipSafeManRouter,
  environmentalSafetyRouter,//环境安全
  energyMonitoringRouter,//能源监控
  liftManRouter,//电梯监控
  electricalFireMonitoringRouter,//电气火灾监控
  // alarmConfigurationRouter,//报警配置
  energyManRouter,
  heatManRouter,
  attendanceManRouter,
  transportManRouter,
  clothManRouter,
  followManRouter,
  medicalWasteManRouter,
  processMan,
  medicalRecordManRouter,
  healthPolicyManRouter,
  accompanyManRouter,
  escortManRouter,
  escortBedRentRouter,
  communityManRouter,
  propertyManRouter,
  invoicingRouter, // 进销存
  reportMan, // 报事管理
  qualitySystem, // 品质
  qualityControl,//品质控制
  disinfectRouter, // 消毒管理

  {
    path: "*",
    redirect: "/404",
    hidden: true
  }
];
