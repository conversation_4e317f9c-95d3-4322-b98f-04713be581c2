<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' 
      title="选择岗位" 
      
      :visible.sync="gwTreeState" 
      width='900px' 
      top='30px'
      append-to-body>
      <div class="dialog-bm-bar">
        <!-- 左侧 -->
        <div class='dialog-bm-left'>
          <el-input
            placeholder="输入关键字进行过滤"
            style="margin-bottom: 10px;"
            v-model="filterBmLeftText">
          </el-input>
          <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
          <div class="m-dialog-h">
            <div class="tree-dom">
              <el-tree 
                :data="treeData" 
                ref="treeDom"
                default-expand-all
                :filter-node-method="filterNode"
                @node-click="nodeClick">
              </el-tree>
            </div>
          </div>
          
        </div>

        <!-- 右侧 -->
        <div class='dialog-bm-right'>
          <el-input
            placeholder="输入关键字进行过滤"
            style="margin-bottom: 10px;"
            @input='filterGangwei($event)'
            v-model="filterBmrightText">
          </el-input>
          <div class="m-dialog-h">
            <div class='dialog-bm-right-loading' v-show='filterBmrightLoading'>
              <i class='el-icon-loading'></i>
            </div>
            <div class='dialog-bm-right-ul' v-show='!filterBmrightLoading'>
              <a href="javascript:void(0)" v-for='(item, index) of filterBmrightList' :key='index' @click='selectGangWei(item)'><i class='el-icon-news'></i>{{ item.label }}</a>
            </div>
          </div>
          
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <span class='dialog-footer-span' v-show='gwStr'>当前选中：{{ gwStr }}</span>
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="primary" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findOrgBranchAll } from '@/api/dataDic'
// import { findOrgBranchPost } from '@/api/postMan'  // 全部岗位
import { findOrgBranchPostByCondition } from '@/api/postMan'  // 状态判断岗位

// import adminDashboard from './admin'
// import editorDashboard from './editor'

export default {
  name: 'Dashboard',
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      // 部门树
      treeData: [],
      selectNode: {},
      
      filterBmLeftText: '',  // 部门左侧筛选
      filterBmrightLoading: false, // 右侧loading
      filterBmrightText: '',  // 部门右侧筛选    
      filterBmrightList: [  // 部门右侧 列表
      ], 
      filterBmrightListOld: [],
      branchId: '',
      branchName: '',
      postId: '',
      postName: '',

      gwStr: ''

    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ]),
    gwTreeState: {
      get: function() {
        let state = this.$store.getters.gwTreeState
        if (state === true) {
          this.postName = ''
          this.gwStr = ''
          this.filterBmrightList = []
          this.filterBmrightListOld = []

          this.findOrgBranchAll()

        }
        return state
      },
      set: function(newVal) {
        this.$store.commit('SET_GWTREESTATE', newVal)
      }
    }
  },
  watch: {
    filterBmLeftText(val) {
      this.$refs.treeDom.filter(val);
    }
  },
  created() {
    // 部门
    
  },
  methods: {
    // [[ 部门弹窗相关
    // 获取部门
    findOrgBranchAll() {
      findOrgBranchAll().then(res => {
        let code = res.data.code
        let data = res.data.data
        let msg = res.data.msg

        if (code === '200') {
          this.treeData = JSON.parse(JSON.stringify(res.data.list))
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 选择部门后，获取部门岗位
    getBmGangwei(brId) {
      this.postName = ''
      this.filterBmrightLoading = true
      this.filterBmrightList = []
      this.filterBmrightListOld = []
      let sendObj = {
        brId,
        page: 1,
        sqType: this.$store.getters.gwTreeSqType,
        size: 5000
      }
      findOrgBranchPostByCondition(sendObj).then(res => {
        this.filterBmrightLoading = false
        
        if (res.data.code == 200) {
          if (res.data.list) {
            let list = res.data.list
            for (let item of list) {
              if (item.userName) {
                item.label = `${item.label}(${item.userName})`
              }
              
            }
            
            this.filterBmrightList = JSON.parse(JSON.stringify(list))
            this.filterBmrightListOld = JSON.parse(JSON.stringify(list))

          } else {
            this.$message({
              type: 'warning',
              message: res.data.msg
            })
          }
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }

      })
    },
    // 节点点击事件
    nodeClick(data, node, mNode) {
      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)

      // this.filterText = data.label
      this.selectNode = data
      // 获取部门 下 岗位
      this.getBmGangwei(data.id)
      
    },
    // 选择岗位
    selectGangWei(item) {
      this.gwStr = item.branchName + ' > ' + item.label
      this.branchId = item.branchId
      this.branchName = item.branchName
      this.postId = item.postId
      this.label = item.label
    },
    // 选择部门提交
    bumenOkFunc() {
      if (this.gwStr) {
        this.$store.commit('SET_GWTREEBRANCHID', '')
        this.$store.commit('SET_GWTREEBRANCHNAME', '')
        this.$store.commit('SET_GWTREEPOSTID', '')
        this.$store.commit('SET_GWTREEPOSTNAME', '')
        this.$store.commit('SET_GWSTR', '')
        this.$store.commit('SET_GWTREESQTYPE', '')
        this.$store.commit('SET_GWTREESTATE', false)

        setTimeout(() => {
          this.$store.commit('SET_GWTREEBRANCHID', this.branchId)
          this.$store.commit('SET_GWTREEBRANCHNAME', this.branchName)
          this.$store.commit('SET_GWTREEPOSTID', this.postId)
          this.$store.commit('SET_GWTREEPOSTNAME', this.label)
          this.$store.commit('SET_GWSTR', this.gwStr)

          
        }, 200)
      } else {
        this.$message({
          type: 'warning',
          message: '请选择岗位'
        })
      }
      
      // this.$refs['dataRightForm'].validate((valid) => {
      //   if (valid) {
      //     this.dialogTreeState = false
      //   }
      // })
      
      // this.addData.branchId = this.selectNode.id
      // this.addData.branchName = this.selectNode.label
    },
    
    // 筛选部门
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 筛选岗位
    // filterBmrightListOld
    filterGangwei(val) {
      this.filterBmrightList = JSON.parse(JSON.stringify(this.filterBmrightListOld))
      this.filterBmrightList = this.filterBmrightList.filter(item => {
        return item.label.indexOf(val) >= 0
      })
    },

    // 关闭弹窗 
    closeDialog() {
      this.$store.commit('SET_GWTREESQTYPE', '')
      this.$store.commit('SET_GWTREESTATE', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
  // 弹窗 选择部门
  .dialog-bm-bar {
    display: flex;
    justify-content: space-between;
    .dialog-bm-left,.dialog-bm-right {
      width: 400px;
    }
    // loading
    .dialog-bm-right-loading {
      padding-top: 40px;
      text-align: center;
      font-size: 60px;
    }
    .dialog-bm-right-ul {
      a {
        display: block;
        line-height: 26px;
        margin-left: 10px;
        color: #666;
        i {
          display: inline-block;
          margin-right: 8px;
          line-height: 26px;
          color: #19AA8D;
        }
      }
    }
  }
  .dialog-footer-span {
    font-size: 14px;
    color: #666;
    display: inline-block;
    padding-right: 10px;
  }
  .dialog-upload-bar-file {
    width: 160px;
    height: 160px;
    border: 1px dashed #999;
    line-height: 160px;
    font-size: 40px;
    text-align: center;
    color: #999;
    border-radius: 3px;
    cursor: pointer;
  }
  .dialog-upload-bar-img {
    width: 160px;
    cursor: pointer;
  }
</style>