// 区域dlg组件

const distDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    distId: '',

    distName: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    distId: state => state.distId,

    distName: state => state.distName
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_DISTID: (state, val) => {
      state.distId = val
    },

    SET_DISTNAME: (state, val) => {
      state.distName = val
    }
  },

  actions: {

  }
}

export default distDlg
