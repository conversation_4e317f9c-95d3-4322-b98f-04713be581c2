<template>
  <div>

  </div>
</template>

<script>
import <PERSON><PERSON> from "js-cookie";
export default {
  data() {
    return {
      url: ""
    };
  },
  created() {
    let token = <PERSON><PERSON>.get("Token");
    let userInfo = JSON.parse(window.localStorage.userInfo);
    window.localStorage.DR_ACCESS_TOKEN = token; // 必传
    window.localStorage.DR_USER_TYPE = "jyt1"; // staff-龙行员工  man-龙行管理者 jyt1-龙行云  jyt2-简E通2.0
    // window.localStorage.DR_TENANT_ID=''  // 简E通2.0传
    window.localStorage.DR_USER_ID = userInfo.id;
    let url = "https://longxingcloud.cn/screen/#/big-screen-list";

    window.open(url);
  }
};
</script>

<style lang="scss" scoped>
.iframebox {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
</style>
