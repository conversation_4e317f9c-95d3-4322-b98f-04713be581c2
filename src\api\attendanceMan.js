
import request from '@/utils/request'


///////// [[ 规则设置
// 获取规则列表  payRuleType：1:缺卡规则 2:旷工规则 3:迟到早退规则,page,size
export function payRuleList(data) {
  return request({
    url: `/ade/payRuleList`,
    method: 'post',
    data
  })
}
// 规则详情  configId：规则id
export function ruleInfo(data) {
  return request({
    url: `/ade/ruleInfo`,
    method: 'post',
    data
  })
}

// 新增缺卡、矿工、迟到早退规则计算  payRuleType：1:缺卡规则 2:旷工规则 3:迟到早退规则
export function savePayRule(data) {
  return request({
    url: `/ade/savePayRule`,
    method: 'post',
    data
  })
}

// 修改缺卡、矿工、迟到早退规则计算  payRuleType：1:缺卡规则 2:旷工规则 3:迟到早退规则
export function updatePayRule(data) {
  return request({
    url: `/ade/updatePayRule`,
    method: 'post',
    data
  })
}

// 获取应用规则的 岗位  configId：规则id,payRuleType:1:缺卡规则 2:旷工规则 3:迟到早退规则
export function getPostsByConfigId(data) {
  return request({
    url: `/sys/getPostsByConfigId`,
    method: 'post',
    data
  })
}
// 设置默认规则
export function setDefaultRule(data) {
  return request({
    url: `/ade/setDefaultRule`,
    method: 'post',
    data
  })
}

///////// [[ 规则设置

// 考勤管理
// 【【 2 考勤，考勤明细
// 获取排班列表  api/ade/scheduling/page/{year}/{month}/{page}/{size}
export function getPaiban(data) {
  return request({
    url: `/ade/scheduling/page`,
    method: 'post',
    data
  })
}

// 修改排班
export function updateSchedule(data) {
  return request({
    url: `/ade/updateSchedule`,
    method: 'post',
    data
  })
}

// 修改排班 - 上班时间
export function updateWorkTime(data) {
  return request({
    url: `/ade/updateWorkTime`,
    method: 'post',
    data
  })
}


// 考勤月报
export function findMonthlyReportByBranchId(data) {
  return request({
    url: `/ade/findMonthlyReportByBranchId`,
    method: 'post',
    data
  })
}

// 个人月报明细
export function findSchedulingByUserId(data) {
  return request({
    url: `/ade/findSchedulingByUserId`,
    method: 'post',
    data
  })
}

// 查询考勤明细
export function findSchedulingExcelByDate(data)
{
  return request({
    url: `/ade/findSchedulingExcelByDate`,
    method: 'get',
    params: data
  })
}

// 导出
export function importSchedulingExcelByDate(data) 
{
  return request({
    url: `/ade/importSchedulingExcelByDate`,
    method: 'get',
    responseType: 'arraybuffer',
    params: data
  })
}

// 【【 1 人脸相关
// 1.1 获取人脸列表
export function rldongtaichaxun(data) {
  return request({
    url: `/ade/rldongtaichaxun`,
    method: 'post',
    data
  })
}

// 审核人脸
export function updateFaceEntryStatus(afcId, status) {
  return request({
    url: `/ade/updateFaceEntryStatus/${afcId}/${status}`,
    method: 'get'
  })
}

// 考勤抽查列表
export function findFalsePunchByDynamic(data) {
  return request({
    url: `/ade/findFalsePunchByDynamic`,
    method: 'post',
    data
  })
}

// 审核打卡记录
export function updatePunchRecordById(data) {
  return request({
    url: `/ade/updatePunchRecordById`,
    method: 'post',
    data
  })
}

//  动态查询黑名单
export function findBlackListByDynamic(data) {
  return request({
    url: `/ade/findBlackListByDynamic`,
    method: 'post',
    data
  })
}
// 修改打卡状态 违规打卡 //取消违规打卡  puchExc-修改状态 1 违规打卡//0取消违规打,punchId-打卡记录id
export function updatePunchExcByPunchId(data) {
  return request({
    url: `/ade/updatePunchExcByPunchId`,
    method: 'post',
    data
  })
}
// 】】 1 人脸相关


// 单个人修改班
export function saveSchedulingByUserId(userId, createDate) {
  return request({
    url: `/ade/saveSchedulingByUserId/${userId}/${createDate}`,
    method: 'get'
  })
}

/////// 考勤统计
export function DeptStatisticsCardingRecords({ startDate, endDate }) {
  return request({
    url: `/ade/DeptStatisticsCardingRecords/${startDate}/${endDate}`,
    method: 'get'
  })
}
// 考勤统计 明细
export function cardingDeptForUser({ startDate, endDate, branchId, type }) {
  return request({
    url: `/ade/cardingDeptForUser/${startDate}/${endDate}/${branchId}/${type}`,
    method: 'get'
  })
}

// 打卡记录
export function findPunchRecordByUserId(data) {
  return request({
    url: `/ade/findPunchRecordByUserId`,
    method: 'post',
    data
  })
}

// 导出考勤Excel 表
// 192.168.1.180:7001/api/ade/exportReports/{month}/{branchId}/{type}
// type:1-不现实早退迟到；2显示
export function exportReports({month, branchId, type}) {
  return request({
    url: `/ade/exportReports/${month}/${branchId}/${type}`,
    method: 'get'
  })
}

export function exportSchedulReports({month, branchId}) {
  return request({
    url: `/ade/exportSchedulReports/${month}/${branchId}`,
    method: 'get'
  })
}



// 【【 考勤设置监管
export function findFencePost(data) {
  return request({
    url: `/sys/findFencePost`,
    method: 'post',
    data
  })
}
// 】】 考勤设置监管


