import request from "@/utils/request";

// 保存报事科目
export function subjectTemplateSave(data) {
  return request({
    url: `/unity/subjectTemplate/save`,
    method: "post",
    data
  });
}

// 报事科目查询树
export function findSubjectTree() {
  return request({
    url: `/unity/subjectTemplate/findSubjectTree`,
    method: "get"
  });
}

// 报事科目移动菜单
export function updateSubjectSortNum(data) {
  return request({
    url: `/unity/subjectTemplate/updateSubjectSortNum`,
    method: "post",
    data
  });
}

// 报事科目删除
export function updateIsDel(isDel, id) {
  return request({
    url: `/unity/subjectTemplate/updateIsDel/${isDel}/${id}`,
    method: "get"
  });
}

// 报修设置分页
export function repairSettingPage(data) {
  return request({
    url: `/unity/repairsetting/repairSettingPage`,
    method: "post",
    data
  });
}

// 新增/修改维修设置
export function repairsettingAddOrUpdate(data) {
  return request({
    url: `/unity/repairsetting/addOrUpdate`,
    method: "post",
    data
  });
}

// 报修设置删除
export function delRepairSetting(id, flag) {
  return request({
    url: `/unity/repairsetting/delRepairSetting/${id}/${flag}`,
    method: "get"
  });
}

// 工单池分页查询
export function repairpoolPage(data) {
  return request({
    url: `/unity/repairpool/page`,
    method: "post",
    data
  });
}

// 新增/修改工单池
export function repairpoolAddRepairPool(data) {
  return request({
    url: `/unity/repairpool/addRepairPool`,
    method: "post",
    data
  });
}

// 报修收费确认
export function baoxiushoufeiqueren(data) {
  return request({
    url: `/unity/repairpool/baoxiushoufeiqueren`,
    method: "post",
    data
  });
}

// 删除报修工单
export function updateRepairPoolFlag(id) {
  return request({
    url: `/unity/repairpool/updateRepairPoolFlag/${id}`,
    method: "get"
  });
}

// 工单池派工
export function repairpoolPaigong(data) {
  return request({
    url: `/unity/repairpool/paigong`,
    method: "post",
    data
  });
}

// 工单池回单
export function repairpoolHuidan(data) {
  return request({
    url: `/unity/repairpool/huidan`,
    method: "post",
    data
  });
}

// 工单池回访
export function repairpoolHuifang(data) {
  return request({
    url: `/unity/repairpool/huifang`,
    method: "post",
    data
  });
}

// 单条派工单
export function repairpoolFindOne(id) {
  return request({
    url: `/unity/repairpool/findOne/${id}`,
    method: "get"
  });
}

// 业务受理 报修工单池
export function yewushouliBaoXiuPage(data) {
  return request({
    url: `/unity/repairpool/yewushouliBaoXiuPage`,
    method: "post",
    data
  });
}

// 报修统计
export function baoxiutongji(data) {
  return request({
    url: `/unity/repairpool/baoxiutongji`,
    method: "post",
    data
  });
}
