import request from '@/utils/request'

/*
*陪诊陪检
*/

// 动态查询陪诊任务 
export function findPzTaskDynamic(data) 
{
	return request({
		url: `/u/usapi/pz/findPzTaskDynamic`,
		method: 'post',
		data
	})
}

// 查询陪诊员
export function findPzTaskUserDynamic(data)
{
	return request({
		url: `/u/usapi/pz/findPzTaskUserDynamic`,
		method: 'post',
		data
	})
}

//任务指派//重新指派
export function pzTaskAssign(data)
{
	return request({
		url: `/u/usapi/pz/pzTaskAssign`,
		method: 'post',
		data
	})
}





