<template>
  <div class="">
    <!-- {{ activeName }} -->
    <el-tabs v-model="activeName" @tab-click="handleClick" tab-position='top' class='m-tabs'>
      <el-tab-pane v-for='(item, index) of navTagArr' :key='index' :label="item.label" :name="item.name"></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findOrgBranchAll } from '@/api/dataDic'
// import { findOrgBranchPost } from '@/api/staffMan'

export default {
  data() {
    return {

      activeName: '/hrMan/rankLevelMan/rankZhiji'
    }
  },
  computed: {
    ...mapGetters([
      'navTagArr'
    ]),
    
    // navTagActive: {
    //   get: function(){
    //     return this.$store.state.NavTag.navTagActive
    //   },
    //   set: function(newVal){}
    // },
  },
  created() {
    // alert('111')
    // this.$store.commit('SET_SHOWANIM', false)
  },
  
  methods: {
    handleClick(tab, event) {
      console.log(tab.name)
      this.$router.push({path: tab.name})
      // this.navTagActive = tab.name
      // this.navTagActive =  '职等管理'
      // this.$store.commit('SET_NAVTAGACTIVE', '职等管理')
      
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>