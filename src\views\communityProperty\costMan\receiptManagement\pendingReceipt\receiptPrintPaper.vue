<template>
  <div class="app-container print" style="margin-top: -16px">
    <!-- style="margin-top: -16px" -->
    <div
      ref="paperContainer"
      :style="
        `width:${paperWidth -
          16}px;height:${paperHeight}px`
      "
      class="paper-container clearfix"
    >
      <div class="pab right-txt">
        一式三联 · 第一联业主留存 · 第二联财务留存 · 第三联物业留存
      </div>
      <div class="flex">
        <img src="/static/image/sjdy/logo.png" class="imgLogo" />
        <div class="print-title" style="">东方万弘物业管理有限公司</div>
        <div class="imgLogo"></div>
      </div>
      <div class="print-border mt10"></div>

      <div class="flex mt10">
        <div style="width: 40%">房间：{{ pageData.ownerRoomName }}</div>
        <div v-if="fieldList[0].payType == 1" class="flex1">建筑面积：{{ pageData.builtUpArea }}</div>
        <div v-else-if="fieldList[0].payType == 2" class="flex1">车位号：{{ pageData.roomName }}</div>
        <div v-else class="flex1">车库号：{{ pageData.roomName }}</div>
        <div>业主/客户：{{ pageData.ownerName }}</div>
      </div>
      <div class="flex ">
        <div style="width: 40%">收款日期：{{ fieldList[0].payTime }}</div>
        <div class="flex1">
          <div v-if="fieldList[0].payInfoJsonStr">收款类型：{{ fieldList[0].payInfoJsonStr}}</div>
          <div v-else>
              收款类型：<span v-if="fieldList[0].payWay == 1">业主自助缴费</span
            ><span v-if="fieldList[0].payWay == 2"> 扫码支付</span
            ><span
              v-if="fieldList[0].payWay == 3 && fieldList[0].offlinePayWay == 1"
              >微信支付</span
            ><span
              v-if="fieldList[0].payWay == 3 && fieldList[0].offlinePayWay == 2"
              >支付宝支付</span
            ><span
              v-if="fieldList[0].payWay == 3 && fieldList[0].offlinePayWay == 3"
              >现金支付</span
            >
          </div>
        </div>
        <div>收据编号：{{ pageData.batchNum }}</div>
      </div>
      <table class="m-table mt10" style="width: 100%; ">
        <tr>
          <td class="tac" style="width:27%">项目</td>
          <td class="tac">起始日期</td>
          <td class="tac">截止日期</td>
          <td class="tac">费用金额</td>
        </tr>
        <tr v-for="(item, index) of fieldList">
          <td class="tac">{{ item.name }}</td>
          <td class="tac">{{ item.startDate }}</td>
          <td class="tac">{{ item.endDate }}</td>
          <td class="tac">{{ item.money }}</td>
        </tr>

        <tr>
          <td class="tac">合计人民币(大写)</td>
          <td class="tac" colspan="2">{{ moneyTotalStr }}</td>
          <td class="tac">{{ moneyTotal }}</td>
        </tr>
      </table>

      <div class="flex mt20">
        <div class="flex1">填票人：{{ userInfo.label }}</div>
        <div class="flex1">服务电话：0451-88073177</div>
        <div style="width:25%">业主签字：</div>
      </div>
      <div class="print-border mt20"></div>
      <div class="flex mt10">
        <div class="flex1">本收据为系统打印手写无效，加盖公章有效。</div>
        <div class="tar mr10">小程序二维码</div>
        <img src="/static/image/sjdy/qrcode.png" class="qrcode" />
      </div>
    </div>

    <el-button
      class="no-print"
      icon="el-icon-printer"
      type="primary"
      size="medium"
      @click="printFunc()"
    >
      打印</el-button
    >
  </div>
</template>

<script>
import * as utils from "@/utils";
import { return2Num } from "@/utils/calendarData";

import { getAction } from "@/api";
// import {
//   findInfoById,
// } from '@/api/jyt/drugMail'

export default {
  components: {},
  data() {
    return {
      userInfo: "",
      paperHeight: 0,
      paperWidth: 0,
      fieldList: [
        {
          name: "",
          startDate: "",
          endDate: "",
          money: ""
        }
      ],
      id: "",
      pageData: {},

      // 合计
      moneyTotal: "", // 合计
      moneyTotalStr: "" // 合计大写
    };
  },
  computed: {},
  created() {
    this.userInfo = JSON.parse(window.localStorage.userInfo);
  },
  mounted() {
    this.$nextTick(() => {

      // 17.4cm
      // 17.9cm  18
      // this.paperWidth = utils.getDpiWidth(296);
      this.paperWidth = utils.getDpiWidth(230);
      this.paperHeight = utils.getDpiHeight(160);

      this.id = this.$route.query.batchNum;
      this.getOrderInfo();
    });
  },
  methods: {
    dealBigMoney(val) {
      console.log("之后的", utils.dealBigMoney(val));
      return utils.dealBigMoney(val);
    },

    // 获取订单信息
    getOrderInfo() {
      getAction("/unity/payBillReportPrint/getInfo/" + this.id).then(res0 => {
        let res = res0.data;
        if (res.code == 200) {
          this.pageData = res.data;

          let list = [];
          let moneyTotal = 0;
          for (let item of res.data.payFeeBillSums) {
            let obj = {
              ...item,
              name: item.configName,
              startDate: item.startDate,
              endDate: item.endDate,
              // money: itemMoney
            };

            let itemMoney = 0
            // item.payInfoJson = JSON.stringify({zfbPay: 999})
            if (item.payInfoJson) {
              let payInfoJson = JSON.parse(item.payInfoJson)
              let payInfoJsonStr = ''
              if (payInfoJson.wxPay) {
                payInfoJsonStr += '微信、'
                itemMoney += parseFloat(payInfoJson.wxPay)
              }
              if (payInfoJson.zfbPay ) {
                payInfoJsonStr += '支付宝、'
                itemMoney += parseFloat(payInfoJson.zfbPay)
              }
              if (payInfoJson.cashPay ) {
                payInfoJsonStr += '现金、'
                itemMoney += parseFloat(payInfoJson.cashPay)
              }
              itemMoney = utils.num2Round(itemMoney)
              
              obj.payInfoJsonStr = payInfoJsonStr.substr(0, payInfoJsonStr.length-1) + '支付'
            } else {
              itemMoney = item.receivedAmount
            }

            obj.money = itemMoney
            list.push(obj);
            moneyTotal += itemMoney
            
          }
          this.fieldList = list;
          moneyTotal = utils.num2Round(moneyTotal);

          this.moneyTotal = moneyTotal;
          this.moneyTotalStr = utils.formatCurrency(moneyTotal);
        } else {
          this.$message.warning("暂无该邮寄订单");
        }
      });
    },

    // 打印
    printFunc() {
      window.print();
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
// 解决火狐浏览器默认+一个空白页问题
body {
  height: auto !important;
}
#app {
  height: auto !important;
}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-container.print {
  font-size: 14px;
  overflow: visible; // 预览时可看多页
}
.app-container {
  font-size: 12px;
  position: relative;
  width: 100%;
  // 不限制高度时去掉
  // height: 100%;
  // overflow: auto;
  padding: 0;

  .paper-container {
    // background: url('/static/image/paper.jpg') no-repeat center;
    // background-size: 100%;
    background: #fff;
    position: relative;
    box-sizing: border-box;
    padding: 20px;
    .field {
      position: absolute;
      max-width: 360px;
      word-break: break-all;
    }
  }
  .el-button.no-print {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

.print-title {
  font-size: 18px;
  letter-spacing: 3px;
  font-weight: bold;
}
.qrcode {
  width: 70px;
  height: 70px;
}
.print-border {
  height: 0px;
  border-top: 2px #000 solid;
  width: 100%;
}
.right-txt {
  position: absolute;
  right: -8px;
  // writing-mode: vertical-rl;
  text-align: center;
  width: 14px;
  line-height: 1;
}
.mt10 {
  margin-top: 10px;
}
.mr10 {
  margin-right: 10px;
}
.mt20 {
  margin-top: 20px;
}
.mt30 {
  margin-top: 30px;
}

@media print {
  .no-print {
    display: none;
  }
}

//
// 表格
.tac {
  text-align: center;
}
.tar {
  text-align: right;
}
.text-bold {
  font-weight: bold;
}
.mt16 {
  margin-top: 16px;
}
.m-table {
  // background:#666;
  border-top: 1px solid #666;
  border-left: 1px solid #666;
  border-spacing: 0px;
  font-size: 12px;
  td {
    background: #fff;
    min-height: 30px;
    box-sizing: border-box;
    padding: 10px 2px;
    border-right: 1px solid #666;
    border-bottom: 1px solid #666;
  }
  .mh {
    min-height: 36px;
  }
  .m-input .el-input__inner {
    border: none;
    border-radius: 0;
    border-bottom: 1px solid #666;
    padding: 0;
    height: 18px;
    line-height: 18px;
  }
}

// 页面
.imgLogo {
  width: 62px;
  height: 48px;
}
.imgZhang {
  position: absolute;
  left: 20px;
  top: 204px;
  width: 240px;
  height: 40px;
}
.printDate {
  position: absolute;
  right: 0px;
  top: 0px;
}
</style>
