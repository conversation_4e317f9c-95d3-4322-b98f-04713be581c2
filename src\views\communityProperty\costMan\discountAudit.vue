<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="归属小区：">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native="getList" placeholder="请输入房号/车位编号/车库编号" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item label="审核状态：">
          <el-select v-model="listQuery.auditState" filterable clearable placeholder="请选择状态">
            <el-option v-for="item in auditList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="getList">搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="操作日期">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作人">
          <template slot-scope="scope">
            <span>{{ scope.row.createName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="审核人">
          <template slot-scope="scope">
            <span>{{ scope.row.auditorName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.auditState == 0" type="warning">待审核</el-tag>
            <el-tag v-if="scope.row.auditState == 1" type="success">已审核</el-tag>
            <el-tag v-if="scope.row.auditState == 2" type="danger">未通过</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="小区房号">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }} {{ scope.row.roomName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计费摘要">
          <template slot-scope="scope" v-if="scope.row.billDesc">
            <div v-for="item in scope.row.billDesc.split(',')">{{ item }}</div>
          </template>
        </el-table-column>

        <el-table-column label="原计费金额">
          <template slot-scope="scope">
            <span>{{ scope.row.payableAmount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="优惠方式">
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row.applyDetailList" :key="index">
              {{ item.discountWay == 1 ? '折扣' + item.discountValue + '%' : '优惠' + item.discountValue + '元' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="优惠后金额">
          <template slot-scope="scope">
            <span>{{ scope.row.discountedAmount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="优惠备注">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.auditState == 0"
              type="primary"
              size="mini"
              icon="el-icon-edit"
              plain
              @click="editItem(scope.row, 'EDIT')"
              >审核</el-button
            >
            <el-button v-else type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog
      :close-on-click-modal="false"
      title="优惠申请审核"
      :visible.sync="dlgShow"
      width="600px"
      top="30px"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :disabled="dlgType == 'VIEW'"
        :rules="rules"
        :model="dlgData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="所在小区" prop="communityId">
          <el-select
            v-model="dlgData.communityId"
            filterable
            clearable
            placeholder="请选择小区"
            @change="communityChange"
            disabled
          >
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="优惠类型" prop="payType">
          <el-radio-group v-model="dlgData.payType" @change="radioChange" disabled>
            <el-radio :label="'1'">房屋</el-radio>
            <el-radio :label="'2'">车位</el-radio>
            <el-radio :label="'3'">车库</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="选择房屋" prop="roomId" v-if="dlgData.payType == 1">
          <el-input v-model="dlgData.roomName" @focus="showRoomDlg" placeholder="请选择房屋" readonly disabled>
          </el-input>
        </el-form-item>

        <el-form-item label="选择车位" prop="roomId" v-if="dlgData.payType == 2" @change="parkingChange">
          <el-select v-model="dlgData.roomId" filterable clearable placeholder="请选择车位" disabled>
            <el-option v-for="item in parkingList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="选择车库" prop="roomId" v-if="dlgData.payType == 3" @change="garageChange">
          <el-select v-model="dlgData.roomId" filterable clearable placeholder="请选择车位" disabled>
            <el-option v-for="item in garageList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="计费摘要" prop="sumIds">
          <el-select
            v-model="dlgData.sumIds"
            filterable
            multiple
            clearable
            placeholder="请选择计费摘要"
            @change="billChange"
            disabled
          >
            <el-option v-for="item in billList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="应缴金额(元)" prop="payableAmount">
          <el-input-number
            v-model="dlgData.payableAmount"
            disabled
            :controls="false"
            :min="0"
            :precision="2"
            :step="1"
          ></el-input-number>
        </el-form-item>

        <el-form-item
          v-for="(item, index) in dlgData.applyDetailList"
          :key="index"
          :label="item.billName + '(元)'"
          :prop="'applyDetailList.' + index + '.discountValue'"
          :rules="rules.discountValue"
        >
          <el-input-number
            v-model="item.billAmount"
            disabled
            :controls="false"
            :min="0"
            :precision="2"
            :step="1"
          ></el-input-number>
          <el-select
            v-model="item.discountWay"
            placeholder="优惠方式"
            style="width: 120px"
            @change="discountChange"
            disabled
          >
            <el-option v-for="item in discountList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-input-number
            v-model="item.discountValue"
            disabled
            :controls="false"
            :min="0"
            :max="item.discountWay == 1 ? 100 : item.billAmount"
            :precision="2"
            :step="1"
            @change="discountChange"
          >
          </el-input-number>
          <span v-if="item.discountWay == 1"> % </span>
        </el-form-item>

        <el-form-item label="优惠后金额(元)" prop="discountedAmount">
          <el-input-number
            v-model="dlgData.discountedAmount"
            disabled
            :controls="false"
            :min="0"
            :precision="2"
            :step="1"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            type="textarea"
            disabled
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>

        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="dlgData.auditState">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">未通过</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="审核意见" prop="auditContent">
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="dlgData.auditContent"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type="success" :loading="dlgLoading" @click="subDlg" icon="el-icon-check">
          <span v-if="dlgLoading">审核中</span>
          <span v-else>审核</span>
        </el-button>
      </div>
    </el-dialog>
    <roomDlg />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, cogaragePage, coparkingPage } from '@/api/communityMan'
import {
  findBillDiscountApplyPage,
  auditApplyInfo,
  findPayBillSumPage,
  findAllPayBillInfosBySumIds,
} from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import roomDlg from '@/components/Dialog/communityMan/roomDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  payType: '1',
  communityId: '',
  communityName: '',
  roomId: '',
  roomName: '',
  sumIds: [],
  applyDetailList: [],
  payableAmount: undefined,
  discountedAmount: undefined,
  remark: '',
  auditContent: '',
  auditState: '',
}

export default {
  name: 'discountAudit',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    roomDlg,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        auditState: [{ required: true, message: '必填字段', trigger: 'change' }],
        auditContent: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        auditState: '',
      },
      communityList: [],
      userInfo: {},
      parkingList: [],
      garageList: [],
      billList: [],
      costTypeList: [],
      auditList: [
        {
          id: '0',
          name: '未审核',
        },
        {
          id: '1',
          name: '已审核',
        },
        {
          id: '2',
          name: '审核未通过',
        },
      ],
      discountList: [
        {
          id: 1,
          name: '折扣',
        },
        {
          id: 2,
          name: '优惠',
        },
      ],
    }
  },

  computed: {
    ...mapGetters('communityMan/roomDlg', {
      roomId: 'roomId',
      roomName: 'roomName',
    }),
  },

  watch: {
    roomId(val) {
      this.dlgData.roomId = val
      this.getBillList()
    },

    roomName(val) {
      this.dlgData.roomName = val
    },
  },

  created() {
    this.getCommunityList()
    utils.getDataDict(this, 'costType', 'costTypeList')
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 选择房号
    showRoomDlg() {
      if (utils.isNull(this.dlgData.communityId)) {
        this.$message.warning('请先选择小区')
        return
      }
      this.$store.commit('communityMan/roomDlg/SET_COMMUNITYID', this.dlgData.communityId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMID', this.roomId)
      this.$store.commit('communityMan/roomDlg/SET_ROOMNAME', this.roomName)
      this.$store.commit('communityMan/roomDlg/SET_DLGSHOW', true)
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    communityChange() {
      this.dlgData.roomId = ''
      this.dlgData.roomName = ''
      this.dlgData.sumIds = []
      this.getGarageList()
      this.getParkingList()
    },

    radioChange() {
      this.dlgData.roomId = ''
      this.dlgData.roomName = ''
      this.dlgData.sumIds = []
      this.getGarageList()
      this.getParkingList()
    },

    parkingChange() {
      this.getBillList()
    },

    garageChange() {
      this.getBillList()
    },

    // 计费摘要change
    billChange() {
      let amount = 0
      for (let i of this.billList) {
        if (this.dlgData.sumIds.includes(i.id)) {
          amount += i.amount
        }
      }
      this.dlgData.payableAmount = amount
      this.getBillDesc()
    },

    // 计算优惠后金额
    discountChange() {
      let total = 0
      for (let i of this.dlgData.applyDetailList) {
        let amount = 0
        if (i.discountWay == 1) {
          // 折扣
          amount = (i.billAmount * (i.discountValue || 0)) / 100
        } else {
          amount = i.discountValue || 0
        }
        total += amount
      }
      this.dlgData.discountedAmount = total
    },

    // 获取详细费用
    getBillDesc() {
      findAllPayBillInfosBySumIds(this.dlgData.sumIds.join(',')).then((res) => {
        if (res.data.code == 200) {
          for (let i of this.dlgData.applyDetailList) {
            for (let j of res.data.data) {
              if (i.billId == j.id) {
                i.feeName = utils.getNameById(j.feeType, this.costTypeList)
              }
            }
          }
          console.log(this.dlgData.applyDetailList)
          this.$forceUpdate()
        }
      })
    },

    // 获取计费摘要
    getBillList() {
      let postParam = {
        page: 1,
        limit: 200,
        payType: this.dlgData.payType,
      }
      if (postParam.payType == 1) {
        postParam.roomId = this.dlgData.roomId
      } else if (postParam.payType == 2) {
        postParam.parkingId = this.dlgData.roomId
      } else if (postParam.payType == 3) {
        postParam.garageId = this.dlgData.roomId
      }
      findPayBillSumPage(postParam).then((res) => {
        if (res.data.code == 200) {
          let list = res.data.data ? res.data.data : []
          for (let i of list) {
            i.name = i.configName + '(' + i.startDate + '~' + i.endDate + ')'
          }
          this.billList = list
        }
      })
    },

    // 获取小区列表
    getCommunityList() {
      let postParam = {
        page: 1,
        limit: 200,
      }
      communityPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取车库列表
    getGarageList() {
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.dlgData.communityId,
      }
      cogaragePage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.garageList = res.data.data
        }
      })
    },

    // 获取车位列表
    getParkingList() {
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.dlgData.communityId,
      }
      coparkingPage(postParam).then((res) => {
        if (res.data.code == 200) {
          this.parkingList = res.data.data
        }
      })
    },

    formatList() {},

    // 获取数据
    getList() {
      // if (utils.isNull(this.listQuery.communityId)) {
      //   this.$message.warning("请选择小区")
      //   return
      // }
      this.count++
      this.listLoading = true
      findBillDiscountApplyPage(this.listQuery).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page.total ? res.data.page.total : 0
          this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 编辑
    editItem(data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgData.auditState = ''
      if (this.dlgData.sumIds) {
        let sumIds = []
        for (let i of this.dlgData.sumIds.split(',')) {
          sumIds.push(parseInt(i))
        }
        this.dlgData.sumIds = sumIds
        this.getBillList()
        // this.getBillDesc()
        this.getGarageList()
        this.getParkingList()
      }
      this.dlgType = type
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg() {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = {
            id: this.dlgData.id,
            isAudit: this.dlgData.auditState,
            remark: this.dlgData.auditContent,
            auditName: this.userInfo.label,
          }
          this.dlgLoading = true
          auditApplyInfo(postParam).then((res) => {
            setTimeout(() => {
              this.dlgLoading = false
            }, 100)
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 上传对话框图片
    beforeUpload(file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then((res) => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg() {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


