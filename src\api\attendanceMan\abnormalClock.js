import request from '@/utils/request'

// 查询异常打卡列表
export function findErrorPunchList(data) {
  return request({
    url: '/workade/findErrorPunchList',
    method: 'post',
    data
  })
}

// 异常打卡信息查询
export function findErrorPunchInfo(id) {
  return request({
    url: `/workade/findErrorPunchInfo/${id}`,
    method: 'get'
  })
}

// 审核打卡信息
export function updateErrorPunchStatus(id, state) {
  return request({
    url: `/workade/updateErrorPunchStatus/${id}/${state}`,
    method: 'get'
  })
}

// 工时调整分页
export function findWorkHourPage(data) {
  return request({
    url: '/workade/findWorkHourPage',
    method: 'post',
    data
  })
}

// 工时调整
export function updateWorkHours(data) {
  return request({
    url: '/workade/updateWorkHours',
    method: 'post',
    data
  })
}