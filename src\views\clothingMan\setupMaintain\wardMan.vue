<template>
  <!--病房管理-->
  <div class="app-container">
    <section :span="8" class="tree-container">
      <el-input placeholder="输入部门名称进行过滤" v-model="filterBranch">
      </el-input>
      <el-tree ref="branchTree" highlight-current class="branch-tree" node-key="id" :data="branchList" @node-click="treeNodeClick" default-expand-all :filter-node-method="filterNode" :expand-on-click-node="false">
      </el-tree>
    </section>
    <section class="list-container">
      <div class="filter-container">
        <el-form ref="searchForm" class='clearfix' label-width="90px" @submit.native.prevent>
          <div class='fr'>
            <el-input @focus="showDistDlg()" v-model="listQuery.districtName" readonly placeholder="请选择区域"></el-input>
            <el-input v-model="listQuery.str" placeholder='请填写病房名称'>
              <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
            </el-input>
            <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">
              搜索
            </el-button>
            <el-button icon='el-icon-refresh' type="primary" size='mini' @click="resetItem">
              重置
            </el-button>
            <el-button icon='el-icon-plus' type="primary" size='mini' @click="addItem">
              新增
            </el-button>
          </div>
        </el-form>
      </div>
      <div class="table-container">
        <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row>
          <el-table-column label="序号" type="index" width="50" align="center">
          </el-table-column>

          <el-table-column label="病房名称">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="区域">
            <template slot-scope="scope">
              <span>{{ scope.row.districtName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="科室">
            <template slot-scope="scope">
              <span>{{ scope.row.branchName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="180">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" icon="el-icon-edit" @click="editItem(scope.row, scope.$index)" plain>
                编辑
              </el-button>
              <el-button type="danger" size="mini" icon="el-icon-delete" @click="delItem(scope.row, scope.$index)" plain>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="page-container">
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
    </section>

    <el-dialog :close-on-click-modal='false' :title="dlgTitle" :visible.sync="dlgShow">
      <el-form ref="dlgForm" :rules="dlgRules" :model="dlgData" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="区域名称" prop="districtName">
              <el-input @focus="showDistDlg()" v-model="dlgData.districtName" readonly placeholder="请选择区域名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属科室" :prop="dlgType == 'add' ? '' : 'branchName'">
              <span v-if="dlgType == 'add'">{{listQuery.branchName}}</span>
              <el-input v-else @focus="showBranchDlg()" v-model="dlgData.branchName" readonly placeholder="请选择所属科室"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="病房名称" prop="name">
              <el-input v-model="dlgData.name" placeholder="请填写病房名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-back" @click="dlgShow = false">取 消</el-button>
        <el-button icon="el-icon-check" :disabled="!subEnable" type="success" @click="subDlg()">保 存</el-button>
      </div>
    </el-dialog>

    <distDlg />
    <branchDlg />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'
import distDlg from '@/components/Dialog/platformMan/distDlg'
import branchDlg from '@/components/Dialog/platformMan/branchDlg'

import {
  findWardDynamic,
  updateWard,
  saveOrUWard,
} from '@/api/medicalMatchManSystem/clothingMan/wardMan'

import { findTreeByFrom } from '@/api/dataDic'


export default {
  components: {
    Pagination,
    branchDlg,
    distDlg
  },
  data () {
    return {
      dlgRules: {
        name: [
          { required: true, message: '病房名称必填', trigger: 'blur' },
          { min: 1, max: 18, message: '病房名称最多18个字符', trigger: 'blur' }
        ],
        districtName: [{ required: true, message: '区域名称必填', trigger: 'change' }],
        branchName: [{ required: true, message: '所属部门必填', trigger: 'change' }]
      },

      // 左侧部门数相关
      filterBranch: "",
      branchList: [],

      // 主表格相关
      list: [],
      listQuery: {
        page: 1,
        size: 20,
        str: '',
        branchId: '',
        branchName: '',
        districtId: '',
        districtName: ''
      },
      total: 0,
      listLoading: false,
      subEnable: true,
      dlgShow: false,
      dlgTitle: '',
      dlgType: '',
      dlgData: {
        name: '',
        branchId: '',
        branchName: '',
        districtId: '',
        districtName: ''
      },
    }
  },

  computed: {
    ...mapGetters('platformMan/branchDlg', {
      branchId: 'branchId',
      branchName: 'branchName'
    }),
    ...mapGetters('platformMan/distDlg', {
      distId: 'distId',
      distName: 'distName'
    }),
  },

  watch: {
    filterBranch (val) {
      this.$refs.branchTree.filter(val);
    },
    branchId (val) {
      this.dlgData.branchId = val
    },
    branchName (val) {
      this.dlgData.branchName = val
    },
    distId (val) {
      this.dlgShow ? this.dlgData.districtId = val : this.listQuery.districtId = val
    },
    distName (val) {
      this.dlgShow ? this.dlgData.districtName = val : this.listQuery.districtName = val
    }
  },

  created () {
    this.getList()
    this.getBranchList()
  },

  methods: {
    // 左侧部门树过滤函数
    filterNode (value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 树节点点击
    treeNodeClick (data) {
      this.listQuery.branchId = data.id
      this.listQuery.branchName = data.label
      this.getList()
    },

    // 获取部门数列表
    getBranchList () {
      this.branchList = []
      findTreeByFrom().then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let list = res.data.list
          let children = []
          for (let i of list) {
            if (i['upBranchId'] == 0) {
              children = i['children']
              break
            }
          }
          this.branchList = children
        } else {
          this.$message.error(msg)
        }
      })
    },

    // 显示部门树 弹窗
    showBranchDlg () {
      let branchId = this.dlgData.branchId
      let branchName = this.dlgData.branchName
      this.$store.commit('platformMan/branchDlg/SET_BRANCHID', branchId)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', branchName)
      this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', true)
    },

    // 显示区域列表
    showDistDlg () {
      let distId = this.dlgShow ? this.dlgData.districtId : this.listQuery.districtId
      let distName = this.dlgShow ? this.dlgData.districtName : this.listQuery.districtName

      this.$store.commit('platformMan/distDlg/SET_DISTID', distId)
      this.$store.commit('platformMan/distDlg/SET_DISTNAME', distName)
      this.$store.commit('platformMan/distDlg/SET_DLGSHOW', true)
    },

    // 主功能相关
    getList () {
      this.list = []
      this.listLoading = true
      findWardDynamic(this.listQuery).then(res => {
        this.listLoading = false
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.list = utils.isNull(res.data.list) ? [] : res.data.list
          this.total = utils.isNull(res.data.data) ? 0 : res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    resetStr () {
      this.listQuery.str = ''
      this.getList()
    },

    resetItem () {
      this.listQuery.str = ''
      this.listQuery.branchId = ''
      this.listQuery.branchName = ''
      this.listQuery.districtId = ''
      this.listQuery.districtName = ''
      this.getList()
    },

    searchItem () {
      this.getList()
    },

    addItem () {
      if (utils.isNull(this.listQuery.branchId)) {
        this.$message.error("请在左侧选择部门")
        return
      }
      this.dlgData = {
        name: '',
        branchId: '',
        branchName: '',
        districtId: '',
        districtName: ''
      }
      this.dlgShow = true
      this.dlgType = 'add'
      this.dlgTitle = '新增病房信息'
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    editItem (data, idx) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgShow = true
      this.dlgType = 'edit'
      this.dlgTitle = '编辑病房信息'
    },

    delItem (data, idx) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let postParam = {
            id: data.id
          }
          updateWard(postParam).then(res => {
            let code = res.data.code
            let msg = res.data.msg
            if (code === '200') {
              this.$message({
                type: 'success',
                message: msg
              })
              this.getList()
            } else {
              this.$message.error(msg)
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    subDlg () {
      this.$refs['dlgForm'].validate(valid => {
        if (valid) {
          this.subEnable = false
          let postParam = this.dlgData
          if (this.dlgType === 'add') {
            postParam['id'] = '0'
            postParam['branchId'] = this.listQuery.branchId
            postParam['branchName'] = this.listQuery.branchName
            if (utils.isNull(this.listQuery.branchId)) {
              this.$message.error("请选择部门")
              return
            }
          } else {
            postParam['id'] = this.dlgData.id
          }
          saveOrUWard(postParam).then(res => {
            $(".tree-on").removeClass("tree-on")
            this.subEnable = true
            let code = res.data.code
            let msg = res.data.msg
            if (code == 200) {
              this.$message({
                type: 'success',
                message: msg
              })
              this.dlgShow = false
              this.getList()
            } else {
              this.$message.error(msg)
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.tree-container {
  float: left;
  width: 400px;
  height: 100%;
  padding: 0 20px;
  .el-tree {
    border: 1px solid #cccccc;
    height: calc(100% - 50px);
    margin-top: 20px;
    padding: 10px;
  }
}
.list-container {
  float: right;
  width: calc(100% - 420px);
  height: 100%;
}
</style>