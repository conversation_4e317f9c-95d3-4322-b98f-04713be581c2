<template>
  <el-dialog
    width="800px"
    :title="getDialogTitle(dlgType)"
    :visible.sync="dialogVisible"
    append-to-body
    @close="closeDialog"
    :close-on-click-modal="false"
  >
    <el-table
      ref="tableBar"
      class="mt10 m-small-table"
      :loading="listLoading"
      :key="1324"
      :data="tableData"
      border
      fit
      highlight-current-row
      height="500"
    >
      <el-table-column
        type="index"
        label="#"
        align="center"
        width="60"
      ></el-table-column>
      <el-table-column
        prop="creatorName"
        label="用户"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        v-if="dlgType === 'dianzan'"
        prop="createTime"
        label="点赞时间"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createTime.slice(0, 10) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="dlgType === 'pinglun'"
        label="评论图片"
        width="300"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="clearfix">
            <el-image
              class="fl mr10"
              v-for="(item1, index1) of scope.row.imgUrl"
              :key="index1"
              :preview-src-list="scope.row.imgUrl"
              :z-index="9999"
              :src="item1"
              alt=""
              style="width: 40px; height: 40px"
            ></el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="dlgType === 'pinglun'"
        prop="createTime"
        label="评论时间"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createTime.slice(0, 10) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="dlgType === 'pinglun'"
        prop="appraise"
        label="内容"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column
        label="操作"
        width="100"
        align="center"
        v-if="dlgType === 'pinglun'"
      >
        <template slot-scope="scope">
          <el-button
            type="danger"
            size="mini"
            @click="delFunc(scope.row)"
            plain
            icon="el-icon-delete"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getInfo"
    />
    <div slot="footer">
      <el-button @click="closeDialog" size="small">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { postAction, getAction, putAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination";

export default {
  components: { Pagination },
  props: {},
  data() {
    return {
      tableData: [],
      searchForm: {
        pageNo: 1,
        pageSize: 20,
      },
      total: 0,
      listLoading: false,
      dlgType: "dianzan",
      selectedRow: {},
      dialogVisible: false,
    };
  },
  methods: {
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const apiUrl = `/canteen/cn/dishes-appraise/delete?id=${row.id}`;
        deleteAction(apiUrl).then((res) => {
          if (res.data.code === "200") {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.getInfo();
            this.$parent.getList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    getDialogTitle(type) {
      const titleMap = {
        dianzan: "点赞详情",
        pinglun: "评论详情",
      };
      return titleMap[type] || "详情";
    },
    init(type, row) {
      this.dlgType = type;
      this.dialogVisible = true;
      this.selectedRow = row;
      this.getInfo();
    },
    getInfo() {
      if (!this.selectedRow || !this.selectedRow.id) {
        this.$message.error("缺少必要的参数");
        return;
      }
      const apiUrl =
        this.dlgType === "dianzan"
          ? `/canteen/cn/dishes-thumbs-up/page?dishesId=${this.selectedRow.id}&pageNo=${this.searchForm.pageNo}&pageSize=${this.searchForm.pageSize}`
          : `/canteen/cn/dishes-appraise/page?dishesId=${this.selectedRow.id}&pageNo=${this.searchForm.pageNo}&pageSize=${this.searchForm.pageSize}`;
      this.listLoading = true;
      getAction(apiUrl).then((res) => {
        this.listLoading = false;
        const { code, data } = res.data;
        if (code === "200") {
          if (this.dlgType === "pinglun" && data.list) {
            data.list.forEach((item) => {
              if (item.imgUrl) {
                item.imgUrl = JSON.parse(item.imgUrl);
              }
            });
          }
          this.tableData = data.list ? [...data.list] : [];
          this.total = data.total || 0;
          console.log(this.tableData);
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    closeDialog() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style></style>