import request from "@/utils/request";

/*
 *能源监测
 */
export function energyMonitoring() {
  return request({
    url: `/iot/energyMonitoring`,
    method: "get"
  });
}

/*
 *用能占比
 */
export function energyEfficiency() {
  return request({
    url: `/iot/energyEfficiency`,
    method: "get"
  });
}

/*
 *用能排名
 */
export function energyRanking() {
  return request({
    url: `/iot/energyRanking`,
    method: "get"
  });
}

/*
 *用能趋势分析
 */
export function energyTrend() {
  return request({
    url: `/iot/energyTrend`,
    method: "get"
  });
}

// 能耗排名分析
export function regionEnergyAnalysis(yearMonth, type) {
  return request({
    url: `/iot/regionEnergyAnalysis?yearMonth=${yearMonth}&type=${type}`,
    method: "get"
  });
}

// 能耗趋势分析
export function trendEnergyAnalysis(yearMonth, type, state) {
  return request({
    url: `/iot/trendEnergyAnalysis?yearMonth=${yearMonth}&type=${type}&state=${state}`,
    method: "get"
  });
}

// 能耗结构分析
export function structureEnergyAnalysis(yearMonth, type, equipId) {
  return request({
    url: `/iot/structureEnergyAnalysis?yearMonth=${yearMonth}&type=${type}&equipId=${equipId}`,
    method: "get"
  });
}

// 区域用能查询
export function regionalEnergyConsumption(yearMonth, area, areaId) {
  return request({
    url: `/iot/regionalEnergyConsumption?yearMonth=${yearMonth}&area=${area}&areaId=${areaId}`,
    method: "get"
  });
}

// 用能异常分页
export function abnormalEquipPage(data) {
  return request({
    url: `/iot/abnormalEquipPage`,
    method: "post",
    data
  });
}

// 用能异常处理
export function abnormalEquipHandle(data) {
  return request({
    url: `/iot/abnormalEquipHandle`,
    method: "post",
    data
  });
}
