// 房间dlg组件

const roomDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    communityId: "",

    roomId: "",

    roomName: "",

    roomInfo: {}
  },

  getters: {
    dlgShow: state => state.dlgShow,

    communityId: state => state.communityId,

    roomState: state => state.roomState,

    roomId: state => state.roomId,

    roomName: state => state.roomName,

    roomInfo: state => state.roomInfo
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_COMMUNITYID: (state, val) => {
      state.communityId = val;
    },

    SET_ROOMSTATE: (state, val) => {
      state.roomState = val;
    },

    SET_ROOMID: (state, val) => {
      state.roomId = val;
    },

    SET_ROOMNAME: (state, val) => {
      state.roomName = val;
    },

    SET_ROOMINFO: (state, val) => {
      state.roomInfo = val;
    }
  },

  actions: {}
};

export default roomDlg;
