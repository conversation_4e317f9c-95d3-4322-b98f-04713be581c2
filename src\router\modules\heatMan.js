/** 陪护床管理 **/

import Layout from "@/views/layout/Layout";

const heatManRouter = {
  path: "/heatMan",
  component: Layout,
  name: "heatMan",
  meta: {
    title: "供热测温",
    icon: "form",
    roles: ["gong<PERSON><PERSON><PERSON>"]
  },
  children: [
    {
      path: "equipmentRoomMan",
      component: () => import("@/views/equipSafeMan/equipmentRoomMan"),
      name: "设备间管理",
      meta: {
        title: "设备间管理",
        roles: ["shebeijianguanli_grcw"]
        // roles: ["shebeijianguanli"]
      }
    },
    {
      path: "equipmentMan",
      component: () => import("@/views/equipSafeMan/equipmentMan"),
      name: "设备管理",
      meta: {
        title: "设备管理",
        roles: ["shebeiguanli_grcw"]
        // roles: ["shebeiguanli"]
      }
    },
    {
      path: "sensorEquip",
      component: () => import("@/views/equipSafeMan/sensorEquip"),
      name: "传感器设备",
      meta: {
        title: "传感器设备",
        roles: ["chuanganqishebei_grcw"]
        // roles: ["chuanganqi<PERSON>bei"]
      }
    },

    {
      path: "runMonitoring",
      component: () => import("@/views/heatMan/runMonitoring"),
      name: "综合运行监控",
      meta: {
        title: "综合运行监控",
        roles: ["zongheyunxingjiankong_grcw"]
        // roles: ["zongheyunxingjiankong"]
      }
    },

    {
      path: "abnormalAlarmMan",
      component: () => import("@/views/equipSafeMan/abnormalAlarmMan"),
      name: "异常报警管理",
      meta: {
        title: "异常报警管理",
        roles: ["yichanggaojingguanli_grcw"]
        // roles: ["yichanggaojingguanli"]
      }
    },

    /////////
    {
      path: "buildingMan",
      component: () => import("@/views/heatMan/buildingMan"),
      name: "楼宇管理",
      meta: {
        title: "楼宇管理",
        roles: ["louyuguanli"]
      },
      children: []
    },
    {
      path: "performanceMonitoring",
      component: () => import("@/views/heatMan/performanceMonitoring"),
      name: "综合运行监控",
      meta: {
        title: "综合运行监控",
        roles: ["huanluyunxingjiankong"]
      },
      children: []
    },
    {
      path: "loopMan",
      component: () => import("@/views/heatMan/loopMan"),
      name: "供热环路管理",
      meta: {
        title: "供热环路管理",
        roles: ["gongrehuanluguanli"]
      },
      children: []
    },
    {
      path: "roomTemperatureQuery",
      component: () => import("@/views/heatMan/roomTemperatureQuery"),
      name: "室温异常查询",
      meta: {
        title: "室温异常查询",
        roles: ["shiwenyichangchaxun"]
      },
      children: []
    },
    {
      path: "pointMan",
      component: () => import("@/views/heatMan/pointMan"),
      name: "供热点位管理",
      meta: {
        title: "供热点位管理",
        roles: ["gongredianweiguanli"]
      },
      children: []
    },
    {
      path: "equipMan",
      component: () => import("@/views/heatMan/equipMan"),
      name: "测温设备管理",
      meta: {
        title: "测温设备管理",
        roles: ["cewenshebeiguanli"]
      },
      children: []
    },
    {
      path: "strategyMan",
      component: () => import("@/views/heatMan/strategyMan/index"),
      name: "控制参数管理",
      meta: {
        title: "控制参数管理",
        roles: ["kongzhicanshuguanli"]
      },
      children: []
    },
    {
      path: "dataCollect",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "数据采集",
      meta: {
        title: "数据采集",
        roles: ["shujucaiji"]
      },
      children: [
        {
          path: "temperatureData",
          component: () =>
            import("@/views/heatMan/dataCollect/temperatureData"),
          name: "温度实时数据",
          meta: {
            title: "温度实时数据",
            roles: ["wendushishishuju"]
          },
          children: []
        },
        {
          path: "temperatureRegion",
          component: () =>
            import("@/views/heatMan/dataCollect/temperatureRegion"),
          name: "区域温度统计",
          meta: {
            title: "区域温度统计",
            roles: ["quyuwendutongji"]
          },
          children: []
        },
        {
          path: "temperatureTrend",
          component: () =>
            import("@/views/heatMan/dataCollect/temperatureTrend"),
          name: "温度趋势分析",
          meta: {
            title: "温度趋势分析",
            roles: ["wenduqushifenxi"]
          },
          children: []
        }
      ]
    }
  ]
};

export default heatManRouter;
