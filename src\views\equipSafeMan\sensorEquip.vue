<template>
  <!-- 设备安全监控 - 传感器节点 -->
  <div class="app-container">
    <!-- 搜索 -->
    <div class="filter-container">
      <el-form
        ref="searchForm"
        class="n-search"
        :model="listQuery"
        @submit.native.prevent
      >
        <div class="n-search-bar">
          <el-form-item label="关键字：">
            <el-input
              @keyup.enter.native="getList"
              class="m-shaixuan-input fl"
              placeholder="请输入关键字"
              v-model="listQuery.label"
              style="width: 200px"
            >
              <i
                @click="resetSearchItem(['label'])"
                slot="suffix"
                class="el-input__icon el-icon-error"
              ></i>
            </el-input>

            <el-select
              style="width: 180px"
              class="fl ml10"
              @change="getList"
              v-model="listQuery.equipRoomId"
              clearable
              placeholder="请选择设备间"
            >
              <el-option
                v-for="item of equipRoomList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <el-cascader
              class="fl ml10"
              ref="refSubCat"
              clearable
              placeholder="请选择区域"
              :props="{ value: 'id', label: 'name', emitPath: false }"
              :show-all-levels="false"
              @change="areaChange"
              v-model="listQuery.areaId"
              :options="areaList"
            ></el-cascader>
            <!-- <el-select
              style="width: 180px"
              class="fl ml10"
              @change="getList"
              v-model="listQuery.equipType"
              clearable
              placeholder="请选择分类"
            >
              <el-option
                v-for="item of equipTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select> -->
            <el-select
              style="width: 180px"
              class="fl ml10"
              @change="getList"
              v-model="listQuery.state"
              clearable
              placeholder="请选择设备状态"
            >
              <el-option
                v-for="item of stateList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
             <el-select
              style="width: 140px"
              class="fl ml10"
              @change="getList"
              v-model="listQuery.flag"
              clearable
              placeholder="请选择启停状态"
            >
              <el-option
                v-for="item of flagList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <el-select
              style="width: 180px"
              class="fl ml10"
              @change="getList"
              v-model="listQuery.nodeArchivesId"
              clearable
              placeholder="请选择设备类型"
            >
              <el-option
                v-for="item of equipArchivesList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <el-button
              icon="el-icon-search"
              type="success"
              size="mini"
              class="search-right-btn fl"
              @click="getList"
              >搜索</el-button
            >
          </el-form-item>

          <div class="clear"></div>
        </div>
      </el-form>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <el-table
        ref="tableBar"
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :key="tableKey"
        :data="list"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="#" type="index" align="center" width="60">
          <template slot-scope="scope">
            <span>{{
              (listQuery.page - 1) * listQuery.limit + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>

        <el-table-column label="名称" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="设备类型" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.nodeArchivesName }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="分类" show-overflow-tooltip width="90">
          <template slot-scope="scope">
            <span>{{ scope.row.equipTypeStr }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="设备标识" width="140">
          <template slot-scope="scope">
            <span class="m-a" @click="showBjDia(scope.row)">{{
              scope.row.devEui
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="协议类型" width="120">
          <template slot-scope="scope">
            <span>{{
              scope.row.agreement == 0 ? "Loranwan协议" : "485协议"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="安装位置" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.installAddr }}</span>
          </template>
        </el-table-column>
        <el-table-column label="区域" show-overflow-tooltip width="70">
          <template slot-scope="scope">
            <span>{{ scope.row.area }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属设备间" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.equipRoomName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属设备" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.equipName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="通讯状态" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.state == 2" class="fdanger fbold">异常</span>
            <span v-else class="fsuccess fbold">正常</span>
          </template>
        </el-table-column>

        <el-table-column label="报警状态" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.state == 1" class="fdanger fbold">异常</span>
            <span v-else class="fsuccess fbold">正常</span>
          </template>
        </el-table-column>

        <el-table-column label="电池状态" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.eqStatus == 0" class="fsuccess fbold"
              >正常</span
            >
            <span v-if="scope.row.eqStatus == 1" class="fdanger fbold"
              >低电</span
            >
          </template>
        </el-table-column>

        <!-- flag=0 启用，flag=2 停用 -->
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.flag == 0" class="fsuccess fbold"
              >已启用</span
            >
            <span v-if="scope.row.flag == 2" class="fdanger fbold">已停用</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="180"
          class-name="small-padding fixed-width"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-document"
              @click="showDesc(scope.row)"
              plain
              >详情</el-button
            >

            <el-button
              v-if="scope.row.flag == 2"
              type="success"
              size="mini"
              icon="el-icon-document"
              @click="changeFlag(scope.row)"
              plain
              >启用</el-button
            >
            <el-button
              v-if="scope.row.flag == 0"
              type="danger"
              size="mini"
              icon="el-icon-document"
              @click="changeFlag(scope.row)"
              plain
              >停用</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
    <div class="clear"></div>

    <!-- [[ 1 弹窗 新增/详情 -->
    <el-dialog
      :title="addDiaType == 'add' ? '新建传感器节点' : '传感器节点详情'"
      :close-on-click-modal="false"
      :visible.sync="addDiaState"
      width="1000px"
      top="30px"
      append-to-body
    >
      <el-form
        ref="diaDescForm"
        :rules="addDataRules"
        :model="addData"
        label-position="right"
        label-width="140px"
        style="width: 980px; margin-left: -40px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="name">
              <el-input
                v-model="addData.name"
                placeholder="请填写名称"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="绑定类型" prop="isArea">
              <el-radio-group v-model="addData.isArea" disabled>
                <el-radio :label="1">设备间</el-radio>
                <el-radio :label="2">区域</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="14" v-if="addData.isArea == 2">
            <el-form-item label="区域" prop="areaId">
              <el-cascader
                disabled
                ref="refSubCat"
                clearable
                placeholder="请选择区域"
                :props="{ value: 'id', label: 'name', emitPath: false }"
                :show-all-levels="false"
                @change="areaChange"
                v-model="addData.areaId"
                :options="areaList"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="addData.isArea == 1">
          <el-col :span="12">
            <el-form-item label="绑定设备间" prop="equipRoomId">
              <el-select
                v-model="addData.equipRoomId"
                clearable
                placeholder="请选择设备间"
                @change="equipRoomChange"
                :disabled="true"
              >
                <el-option
                  v-for="item of equipRoomList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="绑定设备" prop="equipId">
              <el-select
                v-model="addData.equipId"
                clearable
                placeholder="请选择设备"
                disabled
              >
                <el-option
                  v-for="item of equipList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="设备档案" prop="nodeArchivesId">
              <el-select
                @change="sbdaChange"
                v-model="addData.nodeArchivesId"
                clearable
                placeholder="请选择设备档案"
                disabled
              >
                <el-option
                  v-for="item of equipArchivesList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="addData.nodeArchivesId == 30">
            <el-form-item label="电表类型" prop="meterType">
              <el-radio-group v-model="addData.meterType" disabled>
                <el-radio label="1">单项电表</el-radio>
                <el-radio label="2">三项电表</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="设备标识" prop="devEui">
              <el-input
                v-model="addData.devEui"
                placeholder="请填写设备唯一标识"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大报警间隔" prop="alarmInterval">
              <el-input-number
                disabled
                v-model="addData.alarmInterval"
                :min="0"
                type="number"
                placeholder=""
                style="width: 150px; margin-right: 6px"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="协议类型" prop="agreement">
              <el-radio-group v-model="addData.agreement" disabled>
                <el-radio label="0">Loranwan协议</el-radio>
                <el-radio label="1">485协议</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="addData.agreement == 1">
            <el-form-item label="通讯地址">
              <el-input
                v-model="addData.addr"
                placeholder="请填写通讯地址"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="位置" prop="installAddr">
              <el-input
                v-model="addData.installAddr"
                placeholder="请填写位置"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否开启联动" prop="isOpenLink">
              <el-radio-group v-model="addData.isOpenLink" disabled>
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-table
          ref="tableBar"
          class="fr m-small-table mt10"
          :key="1653"
          :data="addData.configs"
          border
          fit
          highlight-current-row
          style="width: 920px"
          max-height="500"
        >
          <el-table-column
            type="index"
            label="#"
            align="center"
            width="60"
            fixed="left"
          >
          </el-table-column>
          <el-table-column label="参数" width="180">
            <template slot-scope="scope">
              {{ scope.row.name }}（单位:{{ scope.row.unit }}）
            </template>
          </el-table-column>
          <el-table-column label="标准值" width="140">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.standardValue"
                type="number"
                placeholder="请输入标准值"
                style="width: 120px; margin-right: 6px"
              />
            </template>
          </el-table-column>

          <el-table-column label="正常范围" width="300">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.startValue"
                type="number"
                placeholder="开始范围"
                style="width: 120px; margin-right: 6px"
              />
              至
              <el-input-number
                v-model="scope.row.endValue"
                type="number"
                placeholder="结束范围"
                style="width: 120px; margin-left: 6px"
              />
            </template>
          </el-table-column>
          <!-- <el-table-column label="最大报警间隔（分钟）" width="200">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.alarmInterval" :min="0" type='number' placeholder="最大报警间隔" style="width: 150px; margin-right: 6px"/>
            </template>
          </el-table-column> -->

          <el-table-column label="低于报警值开关/开关状态" width="260">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-search"
                type="success"
                size="mini"
                class="search-right-btn fl"
                style="margin-left: 0px"
                @click="showPlanDia(scope.row, scope.$index, 0)"
                >设置</el-button
              >
              <el-input
                class="fl ml10"
                v-if="scope.row.kgStr1"
                style="width: 160px"
                :title="scope.row.kgStr1"
                v-model="scope.row.kgStr1"
                readonly
              />
              <!-- <span v-else>暂未设置</span> -->
            </template>
          </el-table-column>
          <el-table-column label="高于报警值开关/开关状态" width="260">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-search"
                type="success"
                size="mini"
                class="search-right-btn fl"
                style="margin-left: 0px"
                @click="showPlanDia(scope.row, scope.$index, 1)"
                >设置</el-button
              >
              <el-input
                class="fl ml10"
                v-if="scope.row.kgStr2"
                style="width: 160px"
                :title="scope.row.kgStr2"
                v-model="scope.row.kgStr2"
                readonly
              />
              <!-- <span v-else>暂未设置</span> -->
            </template>
          </el-table-column>
          <!-- <el-table-column fixed="right" label="操作" width="100" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button @click="diaRowDel(scope)" type="danger" size="mini" title="删除" icon="el-icon-delete" plain></el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <div class="clear"></div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="addDiaState = false" icon="el-icon-back"
          >返回</el-button
        >
        <el-button type="success" @click="dia1FuncAdd" icon="el-icon-check"
          >提交</el-button
        >
      </div>
    </el-dialog>
    <!-- ]] 1 弹窗 弹窗 -->
    <!-- << 初始化断路器地址 -->
    <el-dialog
      title="初始化断路器地址"
      :close-on-click-modal="false"
      :visible.sync="diaDlqState"
      width="500px"
      top="30px"
      append-to-body
    >
      <el-form
        ref="diaDlqForm"
        :rules="diaDlqDataRules"
        :model="diaDlqData"
        label-position="right"
        label-width="120px"
        style="width: 470px; margin-left: -30px"
      >
        <el-form-item label="设备标识" prop="deveui">
          <el-input
            v-model="diaDlqData.deveui"
            placeholder="请填写设备标识"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="通讯地址" prop="addr">
          <el-input
            v-model="diaDlqData.addr"
            placeholder="请填写通讯地址"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="型号" prop="model">
          <el-radio-group v-model="diaDlqData.model">
            <el-radio label="00">单项</el-radio>
            <el-radio label="01">三项</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="diaDlqState = false" icon="el-icon-back"
          >返回</el-button
        >
        <el-button type="success" @click="diaDlqSubFunc" icon="el-icon-check"
          >提交</el-button
        >
      </div>
    </el-dialog>
    <!-- >> 初始化断路器地址 -->
    <!-- << 弹窗 开关 -->
    <el-dialog
      title="选择开关"
      :close-on-click-modal="false"
      :append-to-body="true"
      :visible.sync="diaKgState"
      width="900px"
      top="30px"
      icon-class="el-icon-info"
    >
      <div class="dialog-bms-bar">
        <div class="dialog-bms-right">
          <div>
            <el-popover
              class="fr popDom"
              placement="bottom-end"
              width="800"
              @show="showPopover"
              trigger="click"
            >
              <el-table
                ref="returnListRef"
                style="overflow: auto"
                max-height="400"
                :data="returnList"
              >
                <el-table-column
                  type="index"
                  width="50"
                  align="center"
                ></el-table-column>
                <el-table-column
                  property="nodeName"
                  label="开关名称"
                ></el-table-column>
                <el-table-column
                  property=""
                  label="操作"
                  width="80"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-button
                      @click="popRemoveRow(scope.row)"
                      type="danger"
                      size="mini"
                      icon="el-icon-delete"
                      plain
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
              <el-button
                class="fr search-right-btn"
                type="primary"
                slot="reference"
                icon="el-icon-arrow-down"
                >查看已选</el-button
              >
            </el-popover>

            <el-button
              class="fr search-right-btn"
              icon="el-icon-search"
              type="success"
              size="mini"
              @click="getKgList"
              >搜索</el-button
            >
            <el-input
              @change="getKgList"
              class="m-shaixuan-input fr"
              placeholder="开关名称"
              style="width: 200px"
              v-model="diaKgQuery.label"
            >
              <i
                @click="resetKgItem(['label'])"
                slot="suffix"
                class="el-input__icon el-icon-error"
              ></i>
            </el-input>
            <div class="clear"></div>
          </div>

          <!-- 表格 -->
          <el-table
            ref="multipleTable"
            class="m-small-table mt10"
            v-loading="listLoading"
            :key="10210811"
            :data="diaKgList"
            border
            fit
            highlight-current-row
            style="width: 100%"
            tooltip-effect="dark"
            max-height="380"
            @select="tableSelectChange"
            @select-all="tableSelectAll"
            :default-sort="{ prop: 'aaa', order: 'descending' }"
          >
            <el-table-column fixed align="center" type="selection" width="55">
            </el-table-column>

            <el-table-column label="名称">
              <template slot-scope="scope">
                <span>{{ scope.row.nodeName }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <!-- :pagerCount='4' -->
          <pagination
            class="mt10"
            v-show="diaKgTotal > 0"
            :total="diaKgTotal"
            :page.sync="diaKgQuery.page"
            :limit.sync="diaKgQuery.limit"
            layout="total,prev, pager, next"
            @pagination="getKgList"
            :pager-count="5"
          />
          <div class="clear"></div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="diaKgState = false" icon="el-icon-back"
          >取消</el-button
        >
        <el-button type="success" @click="diaKgSubFunc" icon="el-icon-check"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- << 弹窗 报警信息 -->
    <el-dialog
      title="历史记录"
      :close-on-click-modal="false"
      :append-to-body="true"
      :visible.sync="diaBjState"
      width="1200px"
      top="30px"
      icon-class="el-icon-info"
    >
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="列表" name="liebiao">
          <el-date-picker
            style="width: 300px"
            class="fl"
            @change="getBjList"
            v-model="diaBjQuery.dateRange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            class="search-right-btn fl"
            @click="getBjList"
            >搜索</el-button
          >

          <div class="clear"></div>

          <el-table
            ref="tableBar"
            class="m-small-table mt10"
            v-loading="listLoading"
            :key="diaBjTableKey"
            :data="diaBjList"
            border
            fit
            highlight-current-row
            style="width: 100%"
            max-height="500px"
          >
            <el-table-column label="#" type="index" align="center" width="70">
              <template slot-scope="scope">
                <span>{{
                  (diaBjQuery.page - 1) * diaBjQuery.limit + scope.$index + 1
                }}</span>
              </template>
            </el-table-column>

            <el-table-column
              v-for="(item, index) of diaBjTHList"
              :key="index"
              :label="item.label"
            >
              <template slot-scope="scope">
                <span>{{ scope.row[item.key] }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <pagination
            class="mt10"
            v-show="diaBjTotal > 0"
            :total="diaBjTotal"
            :page.sync="diaBjQuery.page"
            :limit.sync="diaBjQuery.limit"
            @pagination="getBjList"
          />
        </el-tab-pane>
        <el-tab-pane label="趋势" name="qushi">
          <el-date-picker
            class="fl ml10"
            style="width: 350px"
            @change="getZxt"
            v-model="zxtQuery.dateRange"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            size="mini"
          >
          </el-date-picker>
          <el-select
            @change="handleSelectChange"
            class="fl ml10"
            style="width: 200px"
            v-model="zxtQuery.disRespVos"
            multiple
            collapse-tags
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.type"
              :label="item.name"
              :value="`${item.type},${item.name}`"
            >
            </el-option>
          </el-select>
          <el-button
            icon="el-icon-search"
            type="success"
            size="mini"
            class="search-right-btn fl"
            @click="getZxt"
            >搜索</el-button
          >
          <div class="clear"></div>

          <div
            v-if="showChart2"
            id="echart-bar2"
            style="height: 500px; margin-top: 16px"
          ></div>
        </el-tab-pane>
      </el-tabs>
      <div class="clear"></div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="diaBjState = false" icon="el-icon-back"
          >取消</el-button
        >
      </div>
    </el-dialog>

    <!-- << 关联摄像头 -->
    <el-dialog
      title="关联摄像头"
      top="30px"
      :close-on-click-modal="false"
      :visible.sync="cameraState"
      width="900px"
      append-to-body
    >
      <div>
        <!-- 搜索 -->
        <div class="filter-container">
          <el-form
            ref="searchForm"
            class="n-search"
            :model="cameraQuery"
            label-width="90px"
            @submit.native.prevent
          >
            <div class="n-search-bar">
              <div class="n-search-item fl">
                <el-input
                  style="width: 160px"
                  @keyup.enter.native="getCameraList"
                  class="m-shaixuan-input"
                  placeholder="输入关键字"
                  v-model="cameraQuery.label"
                >
                  <i
                    @click="resetSearchItem(['label'])"
                    slot="suffix"
                    class="el-input__icon el-icon-error"
                  ></i>
                </el-input>
              </div>

              <el-button
                style="margin-left: 4px"
                icon="el-icon-search"
                type="success"
                size="mini"
                class="search-right-btn fl"
                @click="getList"
                >搜索</el-button
              >

              <el-popover
                class="fr popDom"
                placement="bottom-end"
                width="400"
                trigger="click"
              >
                <el-table
                  ref="returnListRef"
                  style="overflow: auto"
                  max-height="400"
                  :data="diaPostList"
                >
                  <el-table-column
                    type="index"
                    width="50"
                    align="center"
                  ></el-table-column>
                  <el-table-column label="摄像头名称">
                    <template slot-scope="scope">
                      <span>{{ scope.row.name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    property=""
                    label="操作"
                    width="50"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-button
                        @click="cameraRemoveRow(scope.row)"
                        type="danger"
                        size="mini"
                        icon="el-icon-delete"
                        plain
                      ></el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button
                  style="margin-left: 4px"
                  class="fr search-right-btn"
                  type="primary"
                  slot="reference"
                  icon="el-icon-arrow-down"
                  >查看已选</el-button
                >
              </el-popover>
              <div class="clear"></div>
            </div>
          </el-form>
        </div>

        <el-table
          ref="cameraRef"
          v-loading="cameraLoading"
          :data="cameraList"
          border
          fit
          highlight-current-row
          max-height="367px"
          class="m-small-table"
          @select="cameraSelect"
          @select-all="cameraSelectAll"
          show-overflow-tooltip="true"
        >
          <!-- 单选 @row-click='rowClick' -->
          <!-- <el-table-column label="" align="center" width="80">
            <template slot-scope="scope">
              <el-radio v-model="selectedId" :label="scope.row.id" style="width: 16px;"><span></span></el-radio>
            </template>
          </el-table-column> -->
          <el-table-column align="center" type="selection" width="55">
          </el-table-column>
          <el-table-column label="序号" prop="index" align="center" width="70">
            <template slot-scope="scope">
              <span>{{
                (cameraQuery.page - 1) * cameraQuery.limit + scope.$index + 1
              }}</span>
            </template>
          </el-table-column>

          <el-table-column label="摄像头名称">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="ip">
            <template slot-scope="scope">
              <span>{{ scope.row.ip }}</span>
            </template>
          </el-table-column>
          <el-table-column label="端口" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.port }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          class="mt10"
          v-show="cameraTotal > 0"
          :total="cameraTotal"
          :page.sync="cameraQuery.page"
          :limit.sync="cameraQuery.limit"
          @pagination="getList"
        />
        <div class="clear"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cameraState = false" icon="el-icon-back"
          >返回</el-button
        >
        <el-button type="success" @click="cameraSubFunc" icon="el-icon-check"
          >确定</el-button
        >
      </div>
      <Bmtree />
    </el-dialog>
    <!-- << 关联摄像头 -->

    <!-- << 设置开关计划 -->
    <el-dialog
      title="设置开关计划"
      top="30px"
      :close-on-click-modal="false"
      :visible.sync="planState"
      width="900px"
      append-to-body
    >
      <div>
        <el-button
          icon="el-icon-search"
          type="success"
          size="mini"
          class="search-right-btn"
          style="margin-left: 0px"
          @click="showKg"
          >选择开关</el-button
        >
        <el-table
          ref="planRef"
          v-loading="planLoading"
          :data="planList"
          :key="planKey"
          border
          fit
          highlight-current-row
          max-height="367px"
          class="m-small-table mt10"
          show-overflow-tooltip="true"
        >
          <el-table-column type="index" label="序号" align="center" width="70">
          </el-table-column>

          <el-table-column label="开关名称">
            <template slot-scope="scope">
              <span>{{ scope.row.nodeName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-switch
                :active-value="1"
                :inactive-value="0"
                v-model="scope.row.switchState"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="延时时间/秒">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.delaySecond"
                type="number"
                placeholder="延时时间"
                style="width: 120px"
              />
            </template>
          </el-table-column>

          <!-- 上下移动 -->
          <el-table-column label="操作" width="160">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-top"
                type="primary"
                @click="planMoveFunc('0', scope.$index)"
                title="上移"
              ></el-button>
              <el-button
                icon="el-icon-bottom"
                type="primary"
                @click="planMoveFunc('1', scope.$index)"
                title="下移"
              ></el-button>
              <el-button
                icon="el-icon-delete"
                type="danger"
                @click="planMoveFunc('2', scope.$index)"
                title="删除"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="planState = false" icon="el-icon-back"
          >返回</el-button
        >
        <el-button type="success" @click="planSubFunc" icon="el-icon-check"
          >确定</el-button
        >
      </div>
      <Bmtree />
    </el-dialog>
    <!-- << 设置开关计划 -->
    <Bmtree />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'
import { regionTree } from "@/api/basicManSystem/comprehenOperateMonitor.js"; //查区域
// 接口
import {
  findEquipNodePage,
  eqinAddOrUpdInfo,
  findEquipNodeById,
  delEquipNodeById,
  findEquioPage, // 设备间/设备列表接口
  findNodelArchivesPage, // 设备档案
  findNodelArchivesById, // 设备档案详情
  setDLQAddr, // 初始化断路器地址
  findSwitchPage, // 获取开关列表
  protocolLoran, // 报警信息
  cameraPage, // 分页获取摄像头
  addCameraNode, // 关联摄像头
  findCamerasByNodeId, // 获取节点关联的摄像头
  iotOpen,
  iotStop
} from "@/api/safetyMonitoringApi";

import {
  arrId2Name, // 根据id 获取name
  isNull,
  getDataDictOther
} from "@/utils";
import * as echarts from "echarts";
import * as utils from "@/utils";
import Pagination from "@/components/Pagination"; // 分页
import moment, { localeData } from "moment"; //导入文件
import { getAction, postAction } from "../../api";
// 弹窗
import Bmtree from "@/components/Dialog/Bmtree";

let listQueryEmpty = {
  label: "",
  page: 1,
  limit: 20,
  equipRoomId: "",
  state: '0',
 flag:0,
  nodeArchivesId: ""
};

// [[ 弹窗 到货通知
let addDataEmpty = {
  imgName: "", // 图片地址
  name: "", // 节点名称
  equipRoomId: "",
  equipId: "", // 设备或设备间id
  nodeArchivesId: "", // 设备档案id
  devEui: "", // 设备唯一标识
  agreement: "", // 协议类型
  addr: "", // 通信地址 （协议类型=1时显示）
  installAddr: "", // 安装位置
  isOpenLink: "", // 是否开启联动
  protocolType: "", // 	协议类型
  alarmInterval: "", // 最大报警间隔
  configs: [] // 表格
};
// ]] 弹窗 到货通知

// 选开关弹窗
let diaKgQueryEmpty = {
  label: "",
  page: 1,
  limit: 10
};

// 报警信息弹窗
let diaBjQueryEmpty = {
  id: "",
  label: "",
  page: 1,
  limit: 10,
  dateRange: []
};

// 关联摄像头
let cameraQueryEmpty = {
  label: "",
  page: 1,
  limit: 10
};

//折线图
let zxtQueryEmpty = {
  disRespVos: [],
  nodeId: "",
  startTime: "",
  endTime: "",
  dateRange: []
};
export default {
  components: {
    Pagination,
    Bmtree
  },
  data() {
    return {
      equipTypeList: [],
      // ---- search type
      searchMoreState: false, // 更多条件展开状态

      // 表格
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,

      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      selectRow: "", // 选中行数据
      // [[ 弹窗
      diaTableIndex: "",
      diaTableType: "", // 0-低于 1-高于
      addDiaState: false, // 显示
      addDiaType: "add", // add-新增，edit-编辑，desc-详情
      addData: JSON.parse(JSON.stringify(addDataEmpty)), // 初始化数据
      addDataRules: {
        isArea: [{ required: true, message: "必填字段", trigger: "change" }], // 绑定类型
        name: [{ required: true, message: '必填字段', trigger: 'input' }], // 设备名称
        // equipId: [{ required: true, message: '必填字段', trigger: 'change' }], // 设备或设备间id
        equipRoomId: [{ required: true, message: '必填字段', trigger: 'change' }], // 设备或设备间id
        nodeArchivesId: [{ required: true, message: '必填字段', trigger: 'input' }], // 设备档案id
        devEui: [{ required: true, message: '必填字段', trigger: 'input' }], // 设备唯一标识
        agreement: [{ required: true, message: '必填字段', trigger: 'input' }], // 协议类型
        addr: [{ required: true, message: '必填字段', trigger: 'input' }], // 通信地址
        installAddr: [{ required: true, message: '必填字段', trigger: 'input' }], // 安装位置
        isOpenLink: [{ required: true, message: '必填字段', trigger: 'change' }], // 是否开启联动

        alarmInterval: [
          { required: true, message: "必填字段", trigger: "change" }
        ], // 最大报警间隔
        meterType: [{ required: true, message: "必填字段", trigger: "change" }] // 电表类型
      },
      areaList: [],
      // ]] 弹窗
      //<< 断路器
      diaDlqState: false,
      diaDlqData: "", //
      diaDlqDataRules: {
        deveui: [{ required: true, message: "必填字段", trigger: "input" }], // 设备标识
        addr: [{ required: true, message: "必填字段", trigger: "input" }], // 通讯地址
        model: [{ required: true, message: "必填字段", trigger: "change" }] // 单项-00 三项-01
      },
      // >> 断路器
      // << 弹窗 开关

      // >> 弹窗 开关
      // << 数据字典
      cslxSelect: [
        { id: 1, name: "用电" },
        { id: 2, name: "环境参数" }
      ],

      stateList: [
        { id: '0', name: "全部" },
        { id: '3', name: "正常" },
        { id: '1', name: "参数异常(报警异常)" },
        { id: '2', name: "通讯异常" },
      ],
        flagList: [
        { id: 0, name: "启用" },
        { id: 2, name: "停用" }
      ],
      equipRoomList: [], // 设备间
      equipList: [], // 设备
      equipArchivesList: [], // 设备档案列表
      // >> 数据字典

      // << 新增编辑弹窗 表格开关
      diaKgState: false,
      diaKgList: [], //
      diaKgTotal: 0,
      diaKgQuery: JSON.parse(JSON.stringify(diaKgQueryEmpty)),
      returnList: [],

      // >> 新增编辑弹窗 表格开关

      // << 弹窗-报警信息
      diaBjTableKey: 0,
      diaBjState: false,
      diaBjTHList: [],
      diaBjList: [],
      diaBjTotal: 0,
      diaBjQuery: JSON.parse(JSON.stringify(diaBjQueryEmpty)),
      // >> 弹窗-报警信息

      // << 弹窗 关联摄像头
      cameraState: false, //
      cameraQuery: JSON.parse(JSON.stringify(cameraQueryEmpty)),
      diaPostList: [], // 已选
      cameraList: [],
      cameraTotal: 0,
      cameraLoading: false, // 列表刷新
      // << 弹窗 关联摄像头

      // << 弹窗 设置计划
      planKey: 0,
      planState: false,
      planList: [],
      planLoading: false,
      // << 弹窗 设置计划
      activeName: "liebiao",
      zxtQuery: JSON.parse(JSON.stringify(zxtQueryEmpty)),
      options: [],
      bjDiaRow: {},
      showChart2: false,
      echartRoom2: null,
      zxtSelect: []
    };
  },
  computed: {
    ...mapGetters([
      // 树
      "bmTreeBranchId",
      "bmTreeBranchName"
    ])
  },
  watch: {
    // 部门
    bmTreeBranchId(val) {
      if (val == "empty") {
        return false;
      }
      this.addData.branchId = val;
    },
    bmTreeBranchName(val) {
      if (val == "empty") {
        return false;
      }
      this.addData.branchName = val;
    }
  },
  created() {
    if (this.$route.query.state) {
      this.listQuery.state = this.$route.query.state
    }
    if (this.$route.query.equipType) {
      this.listQuery.equipType = this.$route.query.equipType;
    }
    if (this.$route.query.nodeArchivesId) {
      this.listQuery.nodeArchivesId = Number(this.$route.query.nodeArchivesId);
    }
 
    getDataDictOther(this, "iot_equip_type", "equipTypeList"); // 业务分类
    this.getAreaList();
    this.getEquipRoomList() // 获取设备间
    this.getEquipArchivesList() // 设备档案
    this.getList()
  },

  methods: {
     //区域
     getAreaList() {
      this.areaList = [];
      let sendObj = {
        page: 1,
        size: 9999
      };
      regionTree(sendObj).then(res => {
        if (res.data.code == "200") {
          this.areaList = this.getTreeData(res.data.data);
        } else {
          this.$message({
            type: "warning",
            message: res.data.msg
          });
        }
      });
    },
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
      //tab切换
    handleClick(tab, event) {
      this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty));
      this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
      this.diaBjQuery.id = this.bjDiaRow.id;
      this.zxtSelect = [];
      console.log(tab, event);
      if (tab.name == "liebiao") {
        this.getBjList();
      } else {
        const now = moment();
        const start = moment(now).subtract(3, "hours");
        const end = moment().format("YYYY-MM-DD HH:mm");
        let startTime = moment(start).format("YYYY-MM-DD HH:mm");
        // let endTime = moment(end).format('YYYY-MM-DD HH:mm');
        this.zxtQuery.dateRange = [startTime, end];
        this.getDxList();
        this.getZxt();
      }
    },
    getDxList() {
      getAction(`/iot/trend/nodeDataDis/${this.bjDiaRow.id}`).then(res1 => {
        let res = res1.data;
        if (res.code == 200) {
          this.options = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleSelectChange() {
      this.zxtSelect = [];
      this.zxtQuery.disRespVos.forEach(element => {
        console.log(element, "element");
        let [type, name] = element.split(",");
        this.zxtSelect.push({ type, name });
      });
    },
    getZxt() {
      this.showChart2 = false;
      if (
        isNull(this.zxtQuery.dateRange) ||
        this.zxtQuery.dateRange.length <= 0
      ) {
        this.$message.warning("请先选择起止时间");
        return false;
      }
      let sendObj = JSON.parse(JSON.stringify(this.zxtQuery));
      // 日期范围
      sendObj.startTime = "";
      sendObj.endTime = "";
      if (
        !isNull(this.zxtQuery.dateRange) &&
        this.zxtQuery.dateRange.length > 0
      ) {
        sendObj.startTime = this.zxtQuery.dateRange[0];
        sendObj.endTime = this.zxtQuery.dateRange[1];
      }
      sendObj.disRespVos = this.zxtSelect;
      sendObj.nodeId = this.bjDiaRow.id;
      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      postAction("/iot/trend/nodeDataTrend", sendObj).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == 200) {
          if (!utils.isNull(res.data) && res.data.list.length > 0) {
            this.showChart2 = true;
            // this.list = res.data;
            setTimeout(() => {
              this.setEchartBar2(res.data.list, res.data.times);
              //   this.createRoom(res.data.list)
            }, 100);
          }
        }
      });
    },
    //创建折线图
    setEchartBar2(arr, dataMap) {
      console.log(arr, "arr");
      if (this.showChart2 == false) {
        // if (!utils.isNull(arr)) {
        //   this.echartRoom2.clear();
        // }
        return;
      }
      // << 本月1号到当天
      let xList = [];
      let xList0 = [];
      // let dateObj = new Date();
      // console.log("dateObj.getDate()", dateObj.getDate());
      // console.log(this.getEveryDayDateByBetweenDate(this.listQuery.dateRange[0],this.listQuery.dateRange[1]),'时间间隔');
      // let dayNum = parseInt(dateObj.getDate());
      // let month = utils.return2Num2(dateObj.getMonth() + 1);
      // let year = dateObj.getFullYear();
      // for (let i = 0; i < dayNum; i++) {
      //   let key = `${year}-${month}-${utils.return2Num2(i + 1)}`;
      //   xList.push(key);
      //   xList0.push(`${year}-${month}-${utils.return2Num2(i + 1)}`);
      // }

      // 拼接数据
      let data = [];
      let listData = [];
      for (let index = 0; index < dataMap.length; index++) {
        let obj = {
          yearMonthDate: dataMap[index],
          count: 0,
          type: ""
        };
        listData.push(obj);
      }
      for (let i = 0; i < arr.length; i++) {
        let itemLine = arr[i];
        let lineObj = {
          name: itemLine.name,
          type: "line",
          stack: "",
          data: []
        };
        let map = itemLine.list;
        // console.log('111111111map', map)
        // console.log('111111111listData', listData)
        for (let key = 0; key < map.length; key++) {
          for (let k = 0; k < listData.length; k++) {
            if (map[key].time == listData[k].yearMonthDate) {
              lineObj.data.push(map[key].value);
              // listData[k].value = map[key].value;
              // listData[k].type = map[key].type;
            }
          }
        }
        data.push(lineObj);
        // let arrData = [];
        // console.log("==listData", listData);
        // for (let o = 0; o < listData.length; o++) {
        //   arrData.push(listData[o].value);
        // }
        // console.log(arrData, "arrData");
        // lineObj.data = arrData;
        // data.push(lineObj);
      }
      console.log(data, "data--------------");
      // xList0.push(map[key].yearMonthDate)
      xList0 = dataMap;
      // 绘制图标
      var myChart = echarts.init(document.getElementById("echart-bar2"));
      var option = {
        title: {
          text: ""
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },

        legend: {
          left: 10
        },
        grid: {
          left: "2%",
          right: "2%",
          bottom: "2%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false, // true-刻度中间 false-刻度线上
          data: xList0
        },
        yAxis: {
          type: "value"
          // name: '单位（吨）',
          // nameTextStyle: {
          //   color: '#aaa',
          //   nameLocation: 'start',
          // },
        },
        series: data
        // series: [[1,2,3],[12,22,32],[13,23,33]]
      };
      myChart.clear();
      myChart.setOption(option);
      myChart.on("click", param => {
        // console.log('param', param)
        // // componentIndex
        // // dataIndex
        // let msg = `${this.echartLineData[param.componentIndex].name}：${
        //   this.echartLineData[param.componentIndex].data[param.dataIndex]
        // }`
        // alert(msg)
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    // -- << 启停用
    changeFlag(row) {
      let flag = row.flag + "";
      let loading = this.$loading({
        lock: true,
        text: "操作中",
        background: "rgba(0, 0, 0, 0.7)"
      });
      if (flag === "0") {
        console.log("停用", row.id);
        // 0-启用  2-停用
        iotStop(row.id).then(res1 => {
          backFunc(res1);
        });
      } else {
        console.log("启用", row.id);
        iotOpen(row.id).then(res1 => {
          backFunc(res1);
        });
      }

      var backFunc = res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == "200") {
          this.$message({
            type: "success",
            message: res.msg
          });
          this.getList();
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      };
    },

    // -- >> 启停用
    // << 弹窗 开关计划
    // 列表 显示详情
    showPlanDia(row, index, type) {
      // type-0低于 1-高于
      if (type == 0) {
        if (row.switchs1) {
          this.planList = JSON.parse(JSON.stringify(row.switchs1));
        } else {
          this.planList = [];
        }
      } else {
        if (row.switchs2) {
          this.planList = JSON.parse(JSON.stringify(row.switchs2));
        } else {
          this.planList = [];
        }
      }
      this.diaTableIndex = index;
      this.diaTableType = type;

      this.planState = true;
    },
    // 选择开关弹窗
    showKg() {
      this.returnList = JSON.parse(JSON.stringify(this.planList));
      // type-0低于 1-高于
      // let type = this.diaTableType
      // let row = this.addData.configs[this.diaTableIndex]
      // if (type == 0) {
      //   if (row.switchs1) {
      //     this.returnList = JSON.parse(JSON.stringify(row.switchs1))
      //   } else {
      //     this.returnList = []
      //   }
      // } else {
      //   if (row.switchs2) {
      //     this.returnList = JSON.parse(JSON.stringify(row.switchs2))
      //   } else {
      //     this.returnList = []
      //   }
      // }
      this.diaKgState = true;
      this.getKgList();

      //// 原来的
      // showKg(row, index, type) {
      // this.diaKgQuery = JSON.parse(JSON.stringify(diaKgQueryEmpty))
      // if (type == 0) {
      //   if (row.switchs1) {
      //     this.returnList = JSON.parse(JSON.stringify(row.switchs1))
      //   } else {
      //     this.returnList = []
      //   }
      // } else {
      //   if (row.switchs2) {
      //     this.returnList = JSON.parse(JSON.stringify(row.switchs2))
      //   } else {
      //     this.returnList = []
      //   }
      // }

      // this.diaTableIndex = index
      // this.diaTableType = type
      // this.diaKgState = true
      // this.getKgList()
    },
    // 提交
    planSubFunc() {
      // 0-低于 1-高于
      let addData = JSON.parse(JSON.stringify(this.addData));

      let nameArr = [];
      for (let item of this.planList) {
        nameArr.push(item.nodeName);
      }
      if (this.diaTableType == 0) {
        addData.configs[this.diaTableIndex].switchs1 = this.planList;
        addData.configs[this.diaTableIndex].kgStr1 = nameArr.join(",");
      } else {
        addData.configs[this.diaTableIndex].switchs2 = this.planList;
        addData.configs[this.diaTableIndex].kgStr2 = nameArr.join(",");
      }
      this.addData = JSON.parse(JSON.stringify(addData));
      this.planState = false;
    },
    // 上下移动、删除
    planMoveFunc(type, index) {
      // type 0-上移 1-下移 2-删除
      let planList = JSON.parse(JSON.stringify(this.planList));
      if (type == 0) {
        let tr1 = planList[index - 1];
        let tr2 = planList[index];
        planList.splice(index, 1);
        planList.splice(index - 1, 0, tr2);

        this.planList = JSON.parse(JSON.stringify(planList));
        this.planKey++;
      } else if (type == 1) {
        let tr1 = planList[index];
        let tr2 = planList[index + 1];

        planList.splice(index, 2);

        planList.splice(index, 0, tr1);
        planList.splice(index, 0, tr2);

        this.planList = JSON.parse(JSON.stringify(planList));
        this.planKey++;
      } else if (type == 2) {
        this.$confirm("确认删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning" // // success, warning, info, error
        })
          .then(() => {
            planList.splice(index, 1);

            this.planList = JSON.parse(JSON.stringify(planList));
            this.planKey++;
          })
          .catch(() => {});
      }
    },
    // >> 弹窗 开关计划

    equipRoomChange() {
      this.addData.equipId = "";
      this.getEquipList();
    },

    // << 下拉框
    // 获取设备间
    getEquipRoomList() {
      let sendObj = {
        label: "",
        page: 1,
        limit: 9999,
        isEquipRoom: "0" // 0设备间 1设备 不传查所有
      };
      if (this.$route.path == "/electricalFireMonitoring/sensorEquip") {
        sendObj.equipType = 2;
      }
      if (this.$route.path == "/equipSafeMan/sensorEquip") {
        sendObj.equipType = 1;
      }
      if (this.$route.path == "/environmentalSafety/sensorEquip") {
        sendObj.equipType = 3;
      }
      if (this.$route.path=='/environmentalSafety/sensorEquip') {
        sendObj.equipType=3
      }
      if (this.$route.path=='/energyMonitoring/sensorEquip') {
        sendObj.equipType=4
      }
      findEquioPage(sendObj).then((res1) => {
        // this.listLoading = false
        let res = res1.data;
        if (res.code == "200") {
          this.equipRoomList = JSON.parse(JSON.stringify(res.list));
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    // 获取设备
    getEquipList() {
      let postParam = {
        label: "",
        page: 1,
        limit: 9999,
        equipRoomId: this.addData.equipRoomId,
        isEquipRoom: "1" // 0设备间 1设备 不传查所有
      };
      this.equipList = [];
      findEquioPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.equipList = JSON.parse(JSON.stringify(res.data.list));
        } else {
          this.$message.warning(res.data.msg);
        }
      });
    },

    // 获取设备档案
    getEquipArchivesList() {
      let sendObj = {
        label: "",
        page: 1,
        limit: 9999
      };
      findNodelArchivesPage(sendObj).then(res1 => {
        // this.listLoading = false
        let res = res1.data;
        if (res.code == "200") {
          this.equipArchivesList = JSON.parse(JSON.stringify(res.list));
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // >> 下拉框
    // << 外部弹窗
    showBmTree() {
      this.$store.commit("SET_BMTREEISROLE", true);
      this.$store.commit("SET_BMTREESTATE", true);
    },
    // >> 外部弹窗
    // 列表，复选框可选状态
    selectStateFunc(row, index) {
      return row.documentStatus == 0;
    },
    // 搜索框 清空单个条件
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.getList();
    },
    // 设备档案 change
    sbdaChange(val) {
      let row = "";
      for (let item of this.equipArchivesList) {
        if (item.id == val) {
          row = item;
        }
      }
      // 获取详情
      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      findNodelArchivesById(val).then(res1 => {
        loading.close();
        let res = res1.data;
        // 弹窗表格
        let configs = [];
        for (let item of res.data.list) {
          let obj = {
            name: item.name, // 参数
            unit: item.unit, // 单位

            dataType: item.dataType,
            dataTypeText: item.name,
            standardValue: "", // 标准值
            startValue: "", // 开始范围
            endValue: "", // 结束范围
            switchs1: [],
            kgStr1: "",

            switchs2: [],
            kgStr2: ""

            // alarmInterval: '',  // 最大报警间隔
          };
          configs.push(obj);
        }
        let addData = JSON.parse(JSON.stringify(this.addData));

        addData.isBattery = res.data.pojo.isBattery;
        addData.isswitch = res.data.pojo.isswitch;
        addData.imgName = res.data.pojo.imgName;
        addData.alarmInterval = res.data.pojo.alarmInterval; // 最大报警间隔
        addData.protocolType = res.data.pojo.productCode;
        addData.configs = configs;

        console.log("addData111111111111", addData);
        this.addData = JSON.parse(JSON.stringify(addData));
      });
    },
    // 删除单行
    diaRowDel(scope) {
      let goodsName = scope.row.commodityName;
      let index = scope.$index;
      this.$confirm(`是否确认删除?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning" // // success, warning, info, error
      })
        .then(() => {
          this.addData.configs.splice(index, 1);
          this.addData = JSON.parse(JSON.stringify(this.addData));
        })
        .catch(() => {});
    },
    // 显示新增弹窗
    showAddDia() {
      this.addDiaType = "add";
      this.addData = JSON.parse(JSON.stringify(addDataEmpty));
      // 打开弹窗
      this.addDiaState = true;
      this.$nextTick(() => {
        this.$refs["diaDescForm"].clearValidate();
      });
    },
    // 列表 显示详情
    showDesc(row) {
      this.addDiaType = "desc";
      // let addData = JSON.parse(JSON.stringify(addDataEmpty))
      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      findEquipNodeById(row.id).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == "200") {
          let list = res.data.list;
          for (let item of list) {
            // 将开关数组 拆成 高于 和 低于两个数组

            let switchs1 = []; // 低于
            let kgStr1 = [];
            let switchs2 = []; // 高于
            let kgStr2 = [];

            for (let item2 of item.switchs) {
              // type-0 低于   type-1 高于
              if (item2.type == 0) {
                switchs1.push(item2);
                kgStr1.push(item2.nodeName);
              }
              if (item2.type == 1) {
                switchs2.push(item2);
                kgStr2.push(item2.nodeName);
              }
            }
            item.switchs1 = switchs1;
            item.kgStr1 = kgStr1.length == 0 ? "" : kgStr1.join(",");

            item.switchs2 = switchs2;
            item.kgStr2 = kgStr2.length == 0 ? "" : kgStr2.join(",");
          }
          console.log("list", list);
          // 拼接 弹窗数据
          let addData = {
            id: row.id,
            name: res.data.pojo.name, // 节点名称
            equipId: res.data.pojo.equipId, // 设备或设备间id
            equipName: res.data.pojo.equipName,
            equipRoomId: res.data.pojo.equipRoomId, // 设备或设备间id
            equipRoomName: res.data.pojo.equipRoomName,
            nodeArchivesId: res.data.pojo.nodeArchivesId, // 设备档案id
            nodeArchivesName: res.data.pojo.nodeArchivesName,
            devEui: res.data.pojo.devEui, // 设备唯一标识
            agreement: res.data.pojo.agreement + "",
            isOpenLink: res.data.pojo.isOpenLink + "",
            addr: res.data.pojo.addr, // 通信地址

            installAddr: res.data.pojo.installAddr, // 安装位置
            protocolType: res.data.pojo.protocolType, // 	协议类型  productCode

            isswitch: res.data.pojo.isswitch,
            alarmInterval: res.data.pojo.alarmInterval || '',
            isArea: res.data.pojo.isArea,
            areaId: res.data.pojo.areaId,
            area: res.data.pojo.area,
            configs: list,
          }
          this.addData = JSON.parse(JSON.stringify(addData))
          // 打开弹窗
          this.addDiaState = true;
          this.$nextTick(() => {
            this.$refs["diaDescForm"].clearValidate();
          });
          this.getEquipList();
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // 列表 删除行
    delFunc(row) {
      this.$confirm(`是否确认删除?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning" // // success, warning, info, error
      })
        .then(() => {
          let sendObj = {
            id: row.id,
            flag: 1
          };
          let loading = this.$loading({
            lock: true,
            text: "删除中...",
            background: "rgba(0, 0, 0, 0.7)"
          });
          delEquipNodeById(sendObj).then(res1 => {
            loading.close();
            let res = res1.data;
            if (res.code == "200") {
              this.$message({
                type: "success",
                message: res.msg
              });
              this.getList();
            } else {
              this.$message({
                type: "warning",
                message: res.msg
              });
            }
          });
        })
        .catch(() => {})
    },
    areaChange(selectedValues) {
      if (selectedValues) {
        if (this.addDiaState) {
          this.addData.area = this.$refs[
            "refSubCat"
          ].getCheckedNodes()[0].label; //获取选中name
        } else {
          this.listQuery.equipRoomId = "";
          this.listQuery.equipRoomName = "";
          this.listQuery.area = this.$refs[
            "refSubCat"
          ].getCheckedNodes()[0].label; //获取选中name
        }
      }
      // this.getEquipRoomList();
    },
    // 弹窗 提交方法
    dia1FuncAdd() {
      this.$refs["diaDescForm"].validate(valid => {
        if (valid) {
          // 发送方法
          let sendFunc = () => {
            let sendObj = JSON.parse(JSON.stringify(this.addData));
            sendObj.equipRoomName = arrId2Name(
              this.equipRoomList,
              sendObj.equipRoomId
            );
            sendObj.equipName = arrId2Name(this.equipList, sendObj.equipId);
            sendObj.nodeArchivesName = arrId2Name(
              this.equipArchivesList,
              sendObj.nodeArchivesId
            );

            // 开关循环
            console.log("发送数据333", sendObj);
            for (let item of sendObj.configs) {
              // 低于
              let switchs1 = [];
              if (item.switchs1.length) {
                for (let item2 of item.switchs1) {
                  let obj = {
                    nodeId: item2.nodeId,
                    nodeName: item2.nodeName,
                    type: item2.type,
                    switchState: item2.switchState,
                    delaySecond: item2.delaySecond
                  };
                  switchs1.push(obj);
                }
              }

              // 高于
              let switchs2 = [];
              if (item.switchs2.length) {
                for (let item2 of item.switchs2) {
                  let obj = {
                    nodeId: item2.nodeId,
                    nodeName: item2.nodeName,
                    type: item2.type,
                    switchState: item2.switchState,
                    delaySecond: item2.delaySecond
                  };
                  switchs2.push(obj);
                }
              }

              item.switchs = switchs1.concat(switchs2);
            }
            let loading = this.$loading({
              lock: true,
              text: "加载中...",
              background: "rgba(0, 0, 0, 0.7)"
            });
            console.log("发送数据", sendObj);
            // return false
            eqinAddOrUpdInfo(sendObj).then(res1 => {
              loading.close();
              let res = res1.data;
              if (res.code == "200") {
                this.$message({
                  type: "success",
                  message: res.msg
                });
                this.addDiaState = false;
                this.getList();
              } else {
                this.$message({
                  type: "warning",
                  message: res.msg
                });
              }
            });
          };
          // 校验表格
          let isHas0 = false; // 是否有0
          let isValid = true;
          for (let i = 0; i < this.addData.configs.length; i++) {
            let item = this.addData.configs[i];
            if (
              item.standardValue == 0 ||
              item.startValue == 0 ||
              item.endValue == 0
            ) {
              isHas0 = true;
            }
            // 判断正常范围大小
            if (item.startValue > item.endValue) {
              isValid = false;
              this.$message({
                type: "warning",
                message: `请正确填写第 ${i + 1} 行的正常范围`
              });
              break;
            }
          }
          if (!isValid) {
            return false;
          }
          // 存在0 提示确认框
          if (isHas0) {
            this.$confirm(`表格中填写的数据存在“0”，是否继续提交?`, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning" // // success, warning, info, error
            })
              .then(() => {
                sendFunc();
              })
              .catch(() => {});
          } else {
            sendFunc();
          }
        }
      });
    },
    // 获取数据
    getList() {
      this.list = [];
      this.listLoading = true;
      console.log("发送数据", this.listQuery);
      console.log(this.$route.path, "this.$route.path");

      if (this.$route.path == "/electricalFireMonitoring/sensorEquip") {
        this.listQuery.equipType = 2;
      }
      if (this.$route.path == "/equipSafeMan/sensorEquip") {
        this.listQuery.equipType = 1;
      }
      if (this.$route.path == "/environmentalSafety/sensorEquip") {
        this.listQuery.equipType = 3;
      }
      if (this.$route.path=='/energyMonitoring/sensorEquip') {
        this.listQuery.equipType=4
      }
      findEquipNodePage(this.listQuery).then((res1) => {
        this.listLoading = false
        let res = res1.data
        if (res.code == '200') {
          this.total = res.data.total
          this.list = JSON.parse(JSON.stringify(res.list))
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // << 弹窗 初始化断路器
    showDlq(row) {
      let diaDlqData = {
        deveui: row.devEui,
        addr: row.addr,
        model: row.model || ""
      };
      console.log("diaDlqData", diaDlqData);
      this.diaDlqData = diaDlqData;
      // 打开弹窗
      this.diaDlqState = true;
      this.$nextTick(() => {
        this.$refs["diaDlqForm"].clearValidate();
      });
    },
    // 提交
    diaDlqSubFunc() {
      this.$refs["diaDlqForm"].validate(valid => {
        if (valid) {
          let loading = this.$loading({
            lock: true,
            text: "提交中...",
            background: "rgba(0, 0, 0, 0.7)"
          });
          console.log("发送数据", this.diaDlqData);
          setDLQAddr(this.diaDlqData).then(res1 => {
            loading.close();
            let res = res1.data;
            if (res.code == "200") {
              this.diaDlqState = false;
              this.$message({
                type: "success",
                message: res.msg
              });
              this.getList();
            } else {
              this.$message({
                type: "warning",
                message: res.msg
              });
            }
          });
        }
      });
    },
    // >> 弹窗 初始化断路器

    // << 弹窗 开关列表
    getKgList() {
      findSwitchPage(this.diaKgQuery).then(res1 => {
        let res = res1.data;
        if (res.code == "200") {
          let diaKgList = [];
          this.diaKgTotal = res.data.total;
          for (let item of res.list) {
            diaKgList.push({ nodeId: item.id, nodeName: item.name });
          }
          console.log("diaKgList", diaKgList);
          this.diaKgList = diaKgList;

          setTimeout(() => {
            for (let item of this.diaKgList) {
              let isHas = this.returnList.some(item2 => {
                return item2.nodeId == item.nodeId;
              });
              if (isHas) {
                this.$refs.multipleTable.toggleRowSelection(item, true);
              } else {
                this.$refs.multipleTable.toggleRowSelection(item, false);
              }
            }
          }, 100);
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    // 开关change
    switchStatusChange(row, type) {
      // type 0-低于 1-高于
      if (type == 0) {
        let switchs1 = row.switchs1;
        for (let item of switchs1) {
          item.switchState = switchs1[0].switchState;
        }
      }
      if (type == 1) {
        let switchs2 = row.switchs2;
        for (let item of switchs2) {
          item.switchState = switchs2[0].switchState;
        }
      }
    },
    // 开关弹窗提交方法
    diaKgSubFunc() {
      let planListOld = JSON.parse(JSON.stringify(this.planList));
      // this.returnList
      let planList = [];

      for (let item of this.returnList) {
        let isHas = false;
        for (let item2 of planListOld) {
          if (item.nodeId == item2.nodeId) {
            planList.push(item2);
            isHas = true;
            break;
          }
        }
        if (!isHas) {
          let obj = {
            nodeId: item.nodeId,
            nodeName: item.nodeName,
            switchState: 0, // 开关 1-开 0-关
            type: this.diaTableType, // 0-低于 1-高于
            delaySecond: 0 // 延迟操作时间
          };
          planList.push(obj);
        }
      }

      this.planList = JSON.parse(JSON.stringify(planList));
      this.diaKgState = false;

      ///// 原来的
      // let addData = this.addData

      // // diaTableType  0-低于 1-高于
      // if (this.diaTableType == 0) {
      //   addData.configs[this.diaTableIndex].switchs1 = this.returnList
      //   let kgStr = []
      //   for (let item of this.returnList) {
      //     item.type = this.diaTableType
      //     item.switchState = 0
      //     kgStr.push(item.nodeName)
      //   }
      //   addData.configs[this.diaTableIndex].kgStr1 = kgStr.join(',')
      // } else {
      //   addData.configs[this.diaTableIndex].switchs2 = this.returnList
      //   let kgStr = []
      //   for (let item of this.returnList) {
      //     item.type = this.diaTableType
      //     item.switchState = 0
      //     kgStr.push(item.nodeName)
      //   }
      //   addData.configs[this.diaTableIndex].kgStr2 = kgStr.join(',')
      // }
      // this.addData = JSON.parse(JSON.stringify(addData))
      // this.diaKgState = false
    },
    // 已选框
    showPopover() {
      for (let item of this.returnList) {
        this.$refs.returnListRef.toggleRowSelection(item, true);
      }
    },
    // 删除
    popRemoveRow(row) {
      let returnList = this.returnList.filter(item => {
        return item.nodeId != row.nodeId;
      });
      this.returnList = JSON.parse(JSON.stringify(returnList));

      // 选择商品弹窗，复选框同步改变
      for (let item of this.diaKgList) {
        let isHas = this.returnList.some(item2 => {
          return item2.nodeId == item.nodeId;
        });
        if (isHas) {
          this.$refs.multipleTable.toggleRowSelection(item, true);
        } else {
          this.$refs.multipleTable.toggleRowSelection(item, false);
        }
      }
    },
    // 表格复选框
    tableSelectChange(arr, row) {
      let checkedState = false; // true-勾选状态，false-取下选择状态
      for (let item of arr) {
        if (item.nodeId == row.nodeId) {
          checkedState = true;
          break;
        }
      }
      // 判断是否是勾选状态
      if (checkedState) {
        this.returnList.push(row);
        console.log("勾选", this.returnList);
      } else {
        let returnList = this.returnList.filter(item => {
          return item.nodeId != row.nodeId;
        });
        this.returnList = JSON.parse(JSON.stringify(returnList));
        console.log("取消选择", returnList);
      }
    },
    // 表格 全选
    tableSelectAll(arr) {
      let len = arr.length;
      console.log(arr.length);
      // 长度为0 取消全选，将list中所有数据，从returnList中移除
      // 长度不为0，全选，将list中所有数据，追加到 returnList中

      let list = JSON.parse(JSON.stringify(this.diaKgList));
      let returnList = JSON.parse(JSON.stringify(this.returnList));
      if (len == 0) {
        let newList = [];
        for (let item of returnList) {
          let hasId = list.some(item2 => {
            return item2.nodeId == item.nodeId;
          });
          if (!hasId) {
            newList.push(item);
          }
        }
        returnList = JSON.parse(JSON.stringify(newList));
      } else {
        for (let item of list) {
          let hasId = returnList.some(item2 => {
            return item2.nodeId == item.nodeId;
          });
          if (!hasId) {
            returnList.push(item);
          }
        }
      }

      console.log("完美的选中数据", returnList);
      this.returnList = returnList;
    },
    // 搜索框 清空单个条件
    resetKgItem(arr) {
      for (let item of arr) {
        this.diaKgQuery[item] = "";
      }
      this.getKgList();
    },
    // >> 弹窗 开关列表

    // << 弹窗-报警信息
    showBjDia(row) {
      // 调用接口
      (this.activeName = "liebiao"),
        (this.diaBjQuery = JSON.parse(JSON.stringify(diaBjQueryEmpty)));
      this.diaBjQuery.id = row.id;
      this.bjDiaRow = row;
      this.zxtQuery = JSON.parse(JSON.stringify(zxtQueryEmpty));
      this.getBjList();
    },
    // 获取列表
    getBjList() {
      let beginTime = "";
      let endTime = "";
      if (
        this.diaBjQuery.dateRange != null &&
        this.diaBjQuery.dateRange.length != 0
      ) {
        beginTime = this.diaBjQuery.dateRange[0];
        endTime = this.diaBjQuery.dateRange[1];
      }
      let sendObj = {
        page: this.diaBjQuery.page,
        limit: this.diaBjQuery.limit,
        id: this.diaBjQuery.id,
        beginTime,
        endTime
      };

      let loading = this.$loading({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)"
      });
      protocolLoran(sendObj).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == "200") {
          if (res.data == null) {
            this.diaBjTotal = 0;
            this.$message({
              type: "warning",
              message: "暂无报警信息"
            });
            return false;
          }
          // 表头
          let diaBjTHList = [];
          for (let key in res.data.field) {
            let label = res.data.field[key];
            let obj = {
              key,
              label
            };
            diaBjTHList.push(obj);
          }
          this.diaBjTHList = diaBjTHList;

          // 表格数据
          this.diaBjTotal = res.data.total;

          this.diaBjList = res.list;
          this.diaBjState = true;
          this.diaBjTableKey++;
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // >> 弹窗-报警信息

    // << 关联摄像头
    // 打开弹窗
    showSxtDia(row) {
      this.selectRow = row;
      this.cameraQuery = JSON.parse(JSON.stringify(cameraQueryEmpty));
      let loading = this.$loading({
        lock: true,
        text: "加载中",
        background: "rgba(0, 0, 0, 0.7)"
      });
      // 获取已关联的列表
      findCamerasByNodeId(row.id).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == "200") {
          // 打开弹窗
          this.diaPostList = res.list;
          this.cameraState = true;

          this.getCameraList(); // 获取分页
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // 获取列表
    getCameraList() {
      this.cameraList = [];
      this.cameraLoading = true;

      cameraPage(this.cameraQuery).then(res => {
        this.cameraLoading = false;
        let code = res.data.code;
        let msg = res.data.msg;
        if (code == "200") {
          let data = res.data.data;
          let cameraList = res.data.list;
          if (data == null) {
            this.cameraTotal = 0;
            this.cameraList = [];
            return false;
          }

          this.cameraTotal = data.total;
          this.cameraList = JSON.parse(JSON.stringify(cameraList));

          setTimeout(() => {
            for (let item of this.cameraList) {
              let isHas = this.diaPostList.some(item2 => {
                return item.id == item2.id;
              });
              if (isHas) {
                this.$refs.cameraRef.toggleRowSelection(item, true);
              }
            }
          }, 50);
        } else {
          this.$message.error(msg);
        }
      });
    },
    // 表格
    cameraSelect(selection, row) {
      // 判断是否存在，存在则删除，不存在则增加
      let diaPostList = JSON.parse(JSON.stringify(this.diaPostList));
      let isHas = false;
      let index = "";
      for (let i = 0; i < this.diaPostList.length; i++) {
        if (row.id == this.diaPostList[i].id) {
          index = i;
          isHas = true;
          break;
        }
      }
      if (isHas) {
        diaPostList.splice(index, 1);
      } else {
        diaPostList.push(row);
      }
      this.diaPostList = JSON.parse(JSON.stringify(diaPostList));
    },
    cameraSelectAll(selection) {
      // 全选-selection.length!=0
      let diaPostList = JSON.parse(JSON.stringify(this.diaPostList));
      if (selection.length != 0) {
        // 全选
        let selectIndexArr = [];
        for (let i = 0; i < selection.length; i++) {
          let isHas = diaPostList.some(item => {
            return selection[i].id == item.id;
          });
          !isHas && selectIndexArr.push(i);
        }
        for (let index of selectIndexArr) {
          diaPostList.push(selection[index]);
        }
        this.diaPostList = JSON.parse(JSON.stringify(diaPostList));
      } else {
        // 取消全选
        let removeIndexArr = [];
        for (let i = 0; i < diaPostList.length; i++) {
          let isHas = this.list.some(item => {
            return diaPostList[i].id == item.id;
          });
          isHas && removeIndexArr.push(i);
        }
        removeIndexArr.sort(function(a, b) {
          return a - b;
        });
        removeIndexArr = removeIndexArr.reverse();
        for (let index of removeIndexArr) {
          diaPostList.splice(index, 1);
        }
        this.diaPostList = JSON.parse(JSON.stringify(diaPostList));
      }
    },
    // 删除
    cameraRemoveRow(row) {
      let diaPostList = this.diaPostList.filter(item => {
        return item.id != row.id;
      });
      this.diaPostList = JSON.parse(JSON.stringify(diaPostList));

      // 选择商品弹窗，复选框同步改变
      for (let item of this.cameraList) {
        let isHas = this.diaPostList.some(item2 => {
          return item2.id == item.id;
        });
        if (isHas) {
          this.$refs.cameraRef.toggleRowSelection(item, true);
        } else {
          this.$refs.cameraRef.toggleRowSelection(item, false);
        }
      }
    },

    // 提交
    cameraSubFunc() {
      // this.diaPostList

      let idArr = [];
      for (let item of this.diaPostList) {
        idArr.push(item.id);
      }
      let sendObj = {
        nodeId: this.selectRow.id,
        cameras: idArr.join(",")
      };
      // 调用提交接口
      let loading = this.$loading({
        lock: true,
        text: "加载中",
        background: "rgba(0, 0, 0, 0.7)"
      });
      addCameraNode(sendObj).then(res1 => {
        loading.close();
        let res = res1.data;
        if (res.code == "200") {
          // 打开弹窗
          this.$message({
            type: "success",
            message: res.msg
          });
          this.cameraState = false;
          this.getList();
        } else {
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },
    // 搜索框 清空单个条件
    resetCameraItem(arr) {
      for (let item of arr) {
        this.cameraQuery[item] = "";
      }
      this.getCameraList();
    }
    // >> 关联摄像头
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
