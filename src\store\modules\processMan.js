
const processMan = {
  state: {
    // 通用
    processRefreshPage: false,  // 是否刷新页面
    processDialogType: 'add',  // add 新增，edit 编辑，desc 详情，auth 审核, authDesc 已办详情
    processDialogIsZhihui: false,  // 弹窗状态，是否是知会状态
    processDialogJsonStr: '',  // 详情，编辑时的数据
    processDialogTableKey: '',  // 表单 key
    processDialogData: '',  // 表单数据

    // 岗位相关
    zgdState: false,  // 增岗单
    gwqtdState: false,  // 岗位启停单
    jgdState: false,  // 减岗单
    tskqdState: false,  // 特殊考勤申请单
    qxtskqdState: false,  // 取消特殊考勤申请单
    gwhtdState: false,  // 岗位互调单

    // 员工关系
    rzsqdState: false,  // 入职申请单
    zzsqdState: false,  // 转正申请单
    tgtxdState: false,  // 调岗调薪单
    gwtxdState: false,  // 岗位调薪单
    jgsqdState: false,  // 兼岗申请单
    qxjgdState: false,  // 取消兼岗单
    sbsqdState: false,  // 社保申请单
    bgsqdState: false,  // 银行卡变更单
    lzsqdState: false,  // 离职申请单
    wckqdState: false,  // 外出考勤单

    // 考勤相关
    qjdState: false,  // 请假单
    xjdState: false,  // 销假单
    jbdState: false,  // 加班单
    cxdState: false,  // 串休单
    kqzsqdState: false,  // 考勤组申请单
    kqycsbdState: false,  // 考勤异常申报单

    // 薪酬相关
    jldState: false,  // 奖励单
    kkdState: false,  // 扣款单
    bzdState: false,  // 补助单
    dfgzdState: false,  // 代发工资单
    zkfdState: false,  // 暂/扣发单
    zfhfdState: false,  // 暂发恢复单

    // 物资物料
    wlsqdState: false,  // 物料申请单
    wllydState: false,  // 物料领用单

    // 财务业务
    jkdState: false,  // 借款单
    bxdState: false,  // 报销单

    // 行政业务
    // cxqdState: false,  // 串休单
    // cxqdState: false,  // 串休单
  },

  mutations: {
    // refreshPage
    SET_PROCESSREFRESHPAGE: (state, val) => {
      state.processRefreshPage = val
    },
    // 修改弹窗状态
    SET_PROCESSDIALOGTYPE: (state, val) => {
      state.processDialogType = val
    },

    // 知会状态
    SET_PROCESSDIALOGISZHIHUI: (state, val) => {
      state.processDialogIsZhihui = val
    },

    // 数据 字符串
    SET_PROCESSDIALOGJSONSTR: (state, val) => {
      state.processDialogJsonStr = val
    },
    SET_PROCESSDIALOGTABLEKEY: (state, val) => {
      state.processDialogTableKey = val
    },
    SET_PROCESSDIALOGDATA: (state, val) => {
      state.processDialogData = val
    },

    // 岗位相关
    TABLE_ZGD: (state, val) => {
      state.zgdState = val
    },
    TABLE_GWQTD: (state, val) => {
      state.gwqtdState = val
    },
    TABLE_JGD: (state, val) => {
      state.jgdState = val
    },
    TABLE_TSKQD: (state, val) => {  // 特殊考勤单
      state.tskqdState = val
    },
    TABLE_QXTSKQD: (state, val) => {  // 取消特殊考勤单
      state.qxtskqdState = val
    },
    TABLE_GWHTD: (state, val) => {  // 岗位互调单
      state.gwhtdState = val
    },

    

    // 员工关系
    TABLE_RZSQD: (state, val) => {
      state.rzsqdState = val
    },
    TABLE_ZZSQD: (state, val) => {
      state.zzsqdState = val
    },
    TABLE_TGTXD: (state, val) => {
      state.tgtxdState = val
    },
    TABLE_GWTXD: (state, val) => {
      state.gwtxdState = val
    },
    
    TABLE_JGSQD: (state, val) => {
      state.jgsqdState = val
    },
    TABLE_QXJGD: (state, val) => {
      state.qxjgdState = val
    },
    TABLE_SBSQD: (state, val) => {
      state.sbsqdState = val
    },
    TABLE_BGSQD: (state, val) => {
      state.bgsqdState = val
    },
    TABLE_LZSQD: (state, val) => {
      state.lzsqdState = val
    },
    TABLE_WCKQD: (state, val) => {
      state.wckqdState = val
    },

    // 考勤相关
    TABLE_QJD: (state, val) => {
      state.qjdState = val
    },
    TABLE_XJD: (state, val) => {
      state.xjdState = val
    },
    TABLE_JBD: (state, val) => {
      state.jbdState = val
    },
    TABLE_CXD: (state, val) => {
      state.cxdState = val
    },
    TABLE_KQZSQD: (state, val) => {
      state.kqzsqdState = val
    },
    TABLE_KQYCSBD: (state, val) => {
      state.kqycsbdState = val
    },

    // 薪酬相关
    TABLE_JLD: (state, val) => {
      state.jldState = val
    },
    TABLE_KKD: (state, val) => {
      state.kkdState = val
    },
    TABLE_BZD: (state, val) => {
      state.bzdState = val
    },
    
    TABLE_DFGZSQD: (state, val) => {
      state.dfgzdState = val
    },
    TABLE_ZKFD: (state, val) => {
      state.zkfdState = val
    },
    TABLE_ZFHFD: (state, val) => {
      state.zfhfdState = val
    },

    // 物资物料
    SET_WLSQDSTATE: (state, val) => {
      state.wlsqdState = val
    },
    SET_WLLYDSTATE: (state, val) => {
      state.wllydState = val
    },

    // 财务业务
    SET_JKDSTATE: (state, val) => {
      state.jkdState = val
    },
    SET_BXDSTATE: (state, val) => {
      state.bxdState = val
    },
  },

  actions: {
  }
}

export default processMan
