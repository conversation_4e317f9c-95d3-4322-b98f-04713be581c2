<template>
  <div
    class="app-container mazhenguo"
    style="margin-bottom: 32px; padding-bottom: 10px"
  >
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-date-picker
              class="fl ml10"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="预定开始日期"
              end-placeholder="预定结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 280px"
            >
            </el-date-picker>
            <el-input
              v-model="searchForm.creatorName"
              placeholder="请输入用户名称/菜品名称"
              clearable
              class="fl ml10"
              style="width: 160px"
            ></el-input>

            <el-input
              class="fl ml10"
              style="width: 160px"
              @focus="showUserDlg"
              v-model="name"
              placeholder="请选择用户"
              readonly
            >
              <i
                slot="suffix"
                @click="resetSearchItem()"
                class="el-input__icon el-icon-error"
              ></i>
            </el-input>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchFunc"
              class="fl ml10"
              >查询</el-button
            >
            <el-button
              icon="el-icon-download"
              type="primary"
              size="mini"
              @click="showExpDlg"
              >导出</el-button
            >
            <el-popover placement="bottom" width="800" v-model="popShow">
              <div class="m-page-con">
                <el-table
                  class="m-small-table"
                  height="345"
                  border
                  fit
                  highlight-current-row
                  :data="exportList"
                >
                  <el-table-column
                    label="#"
                    type="index"
                    align="center"
                    width="60"
                  >
                  </el-table-column>
                  <el-table-column label="备注" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span>{{ scope.row.remark }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="创建时间">
                    <template slot-scope="scope">
                      <span>{{ scope.row.createTime }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    width="180"
                    align="center"
                    class-name="small-padding fixed-width"
                  >
                    <template slot-scope="scope">
                      <el-button
                        v-if="scope.row.fileUrl"
                        @click="downloadItem(scope.row.fileUrl)"
                        type="primary"
                        size="mini"
                        icon="el-icon-edit"
                        plain
                      >
                        下载
                      </el-button>
                      <el-button
                        type="danger"
                        size="mini"
                        icon="el-icon-delete"
                        @click="delItem(scope.row)"
                        plain
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="page-container">
                <pagination
                  :total="totalExport"
                  :page.sync="listQueryExport.pageNo"
                  :limit.sync="listQueryExport.pageSize"
                  @pagination="getExportList"
                />
              </div>
              <el-button
                style="margin-left: 9px"
                slot="reference"
                icon="el-icon-check"
                type="success"
                size="mini"
                @click.stop="viewItem"
                >查看导出</el-button
              >
            </el-popover>
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      height="calc(100vh - 290px)"
      ref="tableBar"
      class="m-small-table"
      :loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="creatorName"
        label="用户名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="菜品图片"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <el-image
            style="width: 30px; height: 30px"
            :src="scope.row.imgUrl[0]"
            :preview-src-list="scope.row.imgUrl"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="菜品名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="createDate"
        label="预定日期"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="price"
        label="单价(元)"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="num"
        label="预定总量"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="unit"
        label="单位"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getList"
    />
    <div class="clear"></div>
    <el-dialog
      title="导出"
      :close-on-click-modal="false"
      :visible.sync="dlgState"
      width="600px"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :rules="dlgRules"
        :model="dlgData"
        label-position="right"
        label-width="90px"
        size="mini"
        @submit.native.prevent
      >
        <el-form-item label="备注" prop="remark" show-overflow-tooltip>
          <el-input
            :rows="3"
            type="textarea"
            resize="none"
            v-model="dlgData.remark"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgState = false" icon="el-icon-close"
          >关闭</el-button
        >
        <el-button type="success" @click="exportExcel" icon="el-icon-check">
          <span>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <selectUserDlg
      :dlgState0="dlgUserState"
      :dlgData0="dlgUserData"
      :dlgType="dlgUserType"
      :dlgQuery="dlgUserQuery"
      @closeDlg="closeUserDlg"
      @dlgUserSubFunc="dlgUserSubFunc"
    />
  </div>
</template>
      
<script>
import selectUserDlg from "@/components/Dialog2/selectUserDlg";
import * as utils from "@/utils";

import { getAction, deleteAction, postAction } from "@/api";
import Pagination from "@/components/Pagination";
let dlgDataEmpty = {
  remark: "",
};
export default {
  components: {
    selectUserDlg,
    Pagination,
  },
  data() {
    return {
      dlgUserQuery: {},
      dlgUserState: false,
      dlgUserType: "", // 弹框状态add, edit
      dlgUserData: {},
      name: "",
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgState: false,
      popShow: false,
      // 表单验证
      dlgRules: {
        remark: [{ required: true, message: "必填字段", trigger: "blur" }],
      },
      listQueryExport: {
        pageNo: 1,
        pageSize: 20,
        type: "TABLE_YHYDTJB",
      },
      totalExport: 0,
      exportList: [],
      dateRange: undefined,
      searchForm: {
        startTime: "",
        endTime: "",
        creator: "",
        creatorName: "",
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      total: 0,
      listLoading: false,
      dlgType: "add",
    };
  },
  mounted() {
    this.searchFunc();
  },
  methods: {
    resetSearchItem() {
      this.name = "";
      this.searchForm.creator = "";
    },
    closeUserDlg() {
      this.dlgUserState = false;
    },
    // 选择员工返回
    dlgUserSubFunc(data) {
      console.log("车辆返回", data);
      if (utils.isNull(data)) return false;
      this.searchForm.creator = data.id;
      this.name = data.label;
    },
    showUserDlg() {
      this.dlgUserQuery = "";
      this.dlgUserState = true;
    },
    showExpDlg() {
      this.popShow = false;
      this.dlgState = true;
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.$nextTick(() => {
        this.$refs.dlgForm.clearValidate();
      });
    },

    exportExcel() {
      this.$refs.dlgForm.validate((valid) => {
        if (valid) {
          // 导出
          let postData = JSON.parse(JSON.stringify(this.searchForm));
          if (this.dateRange) {
            postData.startTime = this.dateRange[0];
            postData.endTime = this.dateRange[1];
          }
          postData.remark = this.dlgData.remark;
          let loading = this.$loading({
            lock: true,
            text: "导出中",
            background: "rgba(0, 0, 0, 0.7)",
          });
          getAction(
            "/canteen/cn/food-booking-user/countPage/export",
            postData
          ).then((res) => {
            loading.close();
            if (res.data.code == 200) {
              this.dlgState = false;
              this.$message.success("操作成功，请在查看导出中下载");
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    // 查看
    viewItem() {
      this.popShow = !this.popShow;
      if (this.popShow) {
        this.getExportList();
      }
    },
    getExportList() {
      postAction("/canteen/cn/exportDownload/page", this.listQueryExport).then(
        (res) => {
          if (res.data.code === "200") {
            this.totalExport = res.data.data ? res.data.data.total : 0;
            this.exportList = res.data.data
              ? JSON.parse(JSON.stringify(res.data.data.list))
              : [];
          }
        }
      );
    },
    // 下载
    downloadItem(url) {
      window.open(url, "_blank");
    },
    // 删除
    delItem(data) {
      this.$confirm("此操作将删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          getAction(`/canteen/cn/exportDownload/del/${data.id}`).then((res) => {
            if (res.data.code === "200") {
              this.$message.success(res.data.msg);
              this.getExportList();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    searchFunc() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    getList() {
      let postData = { ...this.searchForm };
      this.listLoading = true;

      if (this.dateRange) {
        postData.startTime = this.dateRange[0];
        postData.endTime = this.dateRange[1];
      }
      getAction(`/canteen/cn/food-booking-user/countPage`, postData).then(
        (res) => {
          let { code, data } = res.data;
          this.listLoading = false;
          if (code === "200") {
            data.list.forEach((item) => {
              if (item.imgUrl) {
                item.imgUrl = JSON.parse(item.imgUrl);
              }
            });
            this.tableData = data.list || [];
            this.total = data.total || 0;
          } else {
            this.$message.error(res.data.msg);
          }
        }
      );
    },
    delFunc(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAction(`/canteen/cn/food-booking-user/delete?id=${row.id}`).then(
          (res) => {
            if (res.data.code === "200") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.searchFunc();
            } else {
              this.$message.error(res.data.msg);
            }
          }
        );
      });
    },
  },
};
</script>
      
<style rel="stylesheet/scss" lang="scss" scoped>
// 样式部分根据需要添加
</style>