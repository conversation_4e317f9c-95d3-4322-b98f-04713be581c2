<template>
  <el-dialog :close-on-click-modal='false' :title="'选择区域'" :visible.sync="dlgShow" width="900px" append-to-body>
    <el-form>
      <el-form-item label="作业项">{{workItem.rulesName}}</el-form-item>
    </el-form>
    <div class="clear-container clearfix">
      <div class="tree-container">
        <div class="filter-container">
          <span class="fbold">待选</span>
        </div>
        <el-tree ref="treeDom" show-checkbox highlight-current node-key="id" :props="defaultProps" @check="treeCheck" :check-strictly='true' :default-expanded-keys="defaultOpenList" :data="list" @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" :expand-on-click-node="false">
        </el-tree>
      </div>
      <div class="table-container">
        <span class="fbold cBlue">已选</span>
        <el-table ref="tableBar" class='m-small-table' :data="selectTableList" border fit highlight-current-row>
          <el-table-column type="index" label="序号" align="center" width="50">
          </el-table-column>
          <el-table-column label="区域" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.regionName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" align="center">
            <template slot-scope="scope">
              <el-time-picker @change="calcBeginTime" v-if="scope.$index == 0" format="HH:mm" value-format="HH:mm" v-model="scope.row.beginTime" placeholder="开始时间" style="width:120px;">
              </el-time-picker>
              <span v-else>{{ scope.row.beginTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时长" align="center">
            <template slot-scope="scope">
              <el-input-number @change="calcBeginTime" style="width:100px;" :precision="0" :step="1" :min="1" :controls="false" v-model="scope.row.duration" placeholder="请输入整数"></el-input-number>
              <b>min</b>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button icon="el-icon-delete" type="danger" size="mini" plain @click="delItem(scope.$index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="clear"></div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button icon="el-icon-close" @click="closeDlg">
        取 消
      </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg">
        确 定
      </el-button>
    </div>

  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'


export default {
  components: {
  },
  data() {
    return {
      listQuery: {
        projectId: '',
        projectName: '',
        work: '',
        page: 1,
        size: 20,
      },

      list: [],

      selectKeys: [],

      selectList: [],

      selectTableList: [],

      defaultOpenList: [],  // 默认展开

      defaultProps: {
        children: 'children',
        label: 'name'
      },
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.qualityMan.businessScopeDlgMul.dlgShow
      },
      set: function (val) {
        this.$store.commit('qualityMan/businessScopeDlgMul/SET_DLGSHOW', val)
      }
    },

    businessScopeList: {
      get: function () {
        return this.$store.state.qualityMan.businessScopeDlgMul.list
      },
      set: function (val) {
        this.$store.commit('qualityMan/businessScopeDlgMul/SET_LIST', val)
      }
    },

    workType: {
      get: function () {
        return this.$store.state.qualityMan.businessScopeDlgMul.workType
      },
      set: function (val) {
        this.$store.commit('qualityMan/businessScopeDlgMul/SET_WORKTYPE', val)
      }
    },

    projectId: {
      get: function () {
        return this.$store.state.qualityMan.businessScopeDlgMul.projectId
      },
      set: function (val) {
        this.$store.commit('qualityMan/businessScopeDlgMul/SET_PROJECTID', val)
      }
    },

    projectName: {
      get: function () {
        return this.$store.state.qualityMan.businessScopeDlgMul.projectName
      },
      set: function (val) {
        this.$store.commit('qualityMan/businessScopeDlgMul/SET_PROJECTNAME', val)
      }
    },

    workItem: {
      get: function () {
        return this.$store.state.qualityMan.businessScopeDlgMul.workItem
      },
      set: function (val) {
        this.$store.commit('qualityMan/businessScopeDlgMul/SET_WORKITEM', val)
      }
    },

    beginTime: {
      get: function () {
        return this.$store.state.qualityMan.businessScopeDlgMul.beginTime
      },
      set: function (val) {
        this.$store.commit('qualityMan/businessScopeDlgMul/SET_BEGINTIME', val)
      }
    },

  },

  watch: {

    dlgShow(val) {
      if (val) {
        this.getList()
      }
    },

    businessScopeList(val) {
      this.selectTableList = JSON.parse(JSON.stringify(val))
    },

  },

  created() {
  },

  methods: {

    // 树节点展开
    handleNodeExpand(data) {
      // 保存当前展开的节点
      let flag = false
      this.defaultOpenList.some(item => {
        if (item === data.id) { // 判断当前节点是否存在， 存在不做处理
          flag = true
          return true
        }
      })
      if (!flag) { // 不存在则存到数组里
        this.defaultOpenList.push(data.id)
      }
    },

    // 树节点关闭
    handleNodeCollapse(data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i
        }
      })
    },

    // 计算开始时间
    calcBeginTime() {
      for (let i = 0; i < this.selectTableList.length; i++) {
        if (i == 0) {
          this.selectTableList[i]['endTime'] = utils.calcTime(this.selectTableList[i]['beginTime'], this.selectTableList[i]['duration'])
        } else {
          this.selectTableList[i]['beginTime'] = this.selectTableList[i - 1]['endTime']
          this.selectTableList[i]['endTime'] = utils.calcTime(this.selectTableList[i]['beginTime'], this.selectTableList[i]['duration'])
        }
      }
      this.$forceUpdate()
    },

    // 删除项
    delItem(idx) {
      this.selectTableList.splice(idx, 1)
      this.setCheckedKeys()
    },


    // 递归全选当前下的节点
    selectAllNode(childrenList, type) {
      for (let item of childrenList) {
        // 全选，全部取消
        if (type == 'select') {
          if (!this.selectKeys.includes(item.id)) {
            this.selectKeys.push(item.id)
            this.selectList.push(item)
          }
        } else {
          if (this.selectKeys.includes(item.id)) {
            let mIndex = this.selectKeys.indexOf(item.id)

            this.selectKeys.splice(mIndex, 1)
            this.selectList.splice(mIndex, 1)
          }
        }
        if (item.children) {
          this.selectAllNode(item.children, type)
        }
      }
    },

    treeCheck(checkedNodes, checkedKeys) {
      if (checkedKeys.checkedKeys.length >= this.selectKeys.length) {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        // select-全选；remove-取消全选
        this.selectAllNode(checkedNodes.children, 'select')
      } else {
        this.selectKeys = checkedKeys.checkedKeys
        this.selectList = JSON.parse(JSON.stringify(checkedKeys.checkedNodes))
        this.selectAllNode(checkedNodes.children, 'remove')
      }
      this.$refs.treeDom.setCheckedKeys(this.selectKeys)

      let checkNodes = this.$refs.treeDom.getCheckedNodes()
      this.selectTableList = []
      for (let i of checkNodes) {
        if (i.type == 1) {
          this.selectTableList.push(JSON.parse(JSON.stringify(i)))
        }
      }
      this.formatTableList()
    },

    // 格式化 表格数据
    formatTableList() {
      let selectTableList = JSON.parse(JSON.stringify(this.selectTableList))
      for (let i of selectTableList) {
        i.regionId = i.id
        i.regionName = i.regionNameList.join("-")
        i.beginTime = ''
        i.endTime = ''
        i.duration = ''
      }
      this.selectTableList = JSON.parse(JSON.stringify(selectTableList))
      if (this.selectTableList.length > 0) {
        if (!utils.isNull(this.beginTime)) {
          this.selectTableList[0]['beginTime'] = this.beginTime
          this.calcBeginTime()
        }
      }
      this.$forceUpdate()
    },

    setCheckedKeys() {
      this.$nextTick(() => {
        let currentKey = []
        for (let i of this.selectTableList) {
          let checkId = parseInt(i.regionId)
          currentKey.push(checkId)
        }
        console.log(currentKey)
        this.selectKeys = currentKey
        this.$refs.treeDom.setCheckedKeys(currentKey)
      })
    },

    // 递归
    recursiveList(list) {
      let fn = (list, name) => {
        for (let i of list) {
          if (utils.isNull(i.regionNameList)) {
            i.regionNameList = []
          }
          if (name) {
            for (let j of name) {
              i.regionNameList.push(j)
            }
          }
          i.regionNameList.push(i.name)
          if (i.children && i.children.length > 0) {
            fn(i.children, i.regionNameList)
          }
        }
      }
      fn(list)
    },

    // 获取树列表
    getList() {
      this.list = []
      this.listQuery.projectId = this.projectId
      this.listQuery.projectName = this.projectName
      this.listQuery.work = this.workType
      loadWorkRegionTree(this.listQuery).then(res => {
        if (res.data.code == 200) {
          let list = JSON.parse(JSON.stringify(res.data.data))
          this.recursiveList(list)
          this.list = list
          if (this.defaultOpenList.length == 0) {
            this.defaultOpenList = [list[0].id]
          }
          this.setCheckedKeys()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    subDlg() {
      this.selectList = JSON.parse(JSON.stringify(this.selectTableList))
      if (this.selectList.length > 0) {
        if (utils.isNull(this.selectList[0]['beginTime'])) {
          this.$message.error("请输入第一项区域作业的开始时间")
          return
        }
      }
      this.$store.commit('qualityMan/businessScopeDlgMul/SET_LIST', this.selectList)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('qualityMan/businessScopeDlgMul/SET_DLGSHOW', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 660px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

.clear-container {
  height: calc(100% - 46px);
  .tree-container {
    float: left;
    width: 180px;
    height: 100%;
    .el-tree {
      background: #f2f2f2;
      height: calc(100% - 26px);
      margin-top: 10px;
      overflow-y: auto;
      padding-top: 10px;
    }
  }

  .table-container {
    float: right;
    width: calc(100% - 200px);
    height: 100%;
    .el-table {
      margin-top: 10px;
    }
  }
}
</style>