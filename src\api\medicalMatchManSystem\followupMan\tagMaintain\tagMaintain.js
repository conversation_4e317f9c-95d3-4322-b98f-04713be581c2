import request from '@/utils/request'

/*
* 标签维护
*/

// 新增/修改标签类别接口 
export function saveOrUTagType(data) 
{
	return request({
		url: `/follow/saveOrUTagType`,
		method: 'post',
		data
	})
}

// 修改标签类别状态
export function updateTagType(data)
{
	return request({
		url: `/follow/updateTagType`,
		method: 'post',
		data
	})
}

// 动态查询标签类别 分页
export function findTagTypeDynamic(data)
{
	return request({
		url: `/follow/findTagTypeDynamic`,
		method: 'post',
		data
	})
}


// 新增/修改标签接口
export function saveOrUTag(data) 
{
	return request({
		url: `/follow/saveOrUTag`,
		method: 'post',
		data
	})
}

// 修改标签状态
export function updateTag(data)
{
	return request({
		url: `/follow/updateTag`,
		method: 'post',
		data
	})
}

// 动态查询标签 分页
export function findTagDynamic(data)
{
	return request({
		url: `/follow/findTagDynamic`,
		method: 'post',
		data
	})
}




