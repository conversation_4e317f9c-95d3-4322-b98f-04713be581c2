/**
 * 首页 接口
 */
import request from '@/utils/request'
import Cook<PERSON> from 'js-cookie' 
// import Axios from 'axios'

// 考勤柱状图
export function findWorkRecord() {
  return request({
    url: '/ade/findWorkRecord',
    method: 'post'
  })
}

// 通知公告
export function findNoticeByDynamic(data) {
  return request({
    url: '/msg/findNoticeByDynamic',
    method: 'post',
    data
  })
}

// 顶部事项数字集合
export function findWebCount() {
  return request({
    url: `/process/service/findWebCount?userId=${Cookie.get('userId')}`,
    method: 'get'
  })
}

// 首页 日历
export function punchAttendancesByMonth(data) {
  return request({
    url: '/ade/punchAttendancesByMonth',
    method: 'post',
    data
  })
}
