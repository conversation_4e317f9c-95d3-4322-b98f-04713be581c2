// 选择工作项dlg组件

const workItemDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    list: [],

    workType: "",

    workName: "",

    dlgType: "",

    beginTime: ""
  },

  getters: {
    dlgShow: state => state.dlgShow,

    list: state => state.list,

    workType: state => state.workType,

    workName: state => state.workName,

    dlgType: state => state.dlgType,

    beginTime: state => state.beginTime
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_LIST: (state, val) => {
      state.list = val;
    },

    SET_WORKTYPE: (state, val) => {
      state.workType = val;
    },

    SET_WORKNAME: (state, val) => {
      state.workName = val;
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val;
    },

    SET_BEGINTIME: (state, val) => {
      state.beginTime = val;
    }
  },

  actions: {}
};

export default workItemDlgMul;
