<template>
  <el-dialog :close-on-click-modal="false" :title="'选择部门'" :before-close="closeDlg" :visible.sync="dlgShow" append-to-body>
    <el-input placeholder="输入部门名称进行过滤" v-model="filterBranch"> </el-input>
    <el-tree
      ref="branchTree"
      highlight-current
      node-key="id"
      :data="list"
      @node-click="treeNodeClick"
      default-expand-all
      :filter-node-method="filterNode"
      :expand-on-click-node="false"
    >
    </el-tree>

    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-span" v-if="selectBranchName">当前选中：{{ selectBranchName }}</span>
      <el-button icon="el-icon-back" @click="closeDlg"> 取 消 </el-button>
      <el-button icon="el-icon-check" type="success" @click="subDlg"> 确 定 </el-button>
    </div>
  </el-dialog>
</template>


<script>
import { mapGetters } from 'vuex'

import * as utils from '@/utils'
import { postAction, getAction } from '@/api'

import { findOrgBranchAll, findTreeByFrom } from '@/api/dataDic'

export default {
  props: {
    pageType: {
      type: String,
      default: '',
    },
  },

  components: {},
  data() {
    return {
      filterBranch: '',

      list: [],

      selectBranchId: '',

      selectBranchName: '',
    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.platformMan.branchDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', val)
      },
    },

    permission: {
      get: function () {
        return this.$store.state.platformMan.branchDlg.permission
      },
      set: function (val) {
        this.$store.commit('platformMan/branchDlg/SET_PERMISSION', val)
      },
    },

    branchId: {
      get: function () {
        return this.$store.state.platformMan.branchDlg.branchId
      },
      set: function (val) {
        this.$store.commit('platformMan/branchDlg/SET_BRANCHID', val)
      },
    },

    branchName: {
      get: function () {
        return this.$store.state.platformMan.branchDlg.branchName
      },
      set: function (val) {
        this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', val)
      },
    },
  },

  watch: {
    filterBranch(val) {
      this.$refs.branchTree.filter(val)
    },

    dlgShow(val) {
      if (val) {
        if (utils.isNull(this.branchId)) {
          this.selectBranchId = ''
          this.selectBranchName = ''
        }
        this.getList()
      }
    },

    permission(val) {},

    branchId(val) {
      this.selectBranchId = val
    },

    branchName(val) {
      this.selectBranchName = val
    },
  },

  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },

    treeNodeClick(data) {
      this.selectBranchId = data.id
      this.selectBranchName = data.label
    },

    getList() {
      this.list = []

      if (this.pageType == 'lysgl') {
        postAction('/sys/department/findTree').then((res) => {
          let code = res.data.code
          let msg = res.data.msg
          if (code == 200) {
            let list = res.data.list
            this.list = list
            this.$nextTick(() => {
              this.$refs.branchTree.setCurrentKey(this.selectBranchId)
            })
          } else {
            this.$message.error(msg)
          }
        })
      } else {
        if (this.permission) {
          // 权限树
          findOrgBranchAll().then((res) => {
            let code = res.data.code
            let msg = res.data.msg
            if (code == 200) {
              let list = res.data.list
              this.list = list
              this.$nextTick(() => {
                this.$refs.branchTree.setCurrentKey(this.selectBranchId)
              })
            } else {
              this.$message.error(msg)
            }
          })
        } else {
          findTreeByFrom().then((res) => {
            let code = res.data.code
            let msg = res.data.msg
            if (code == 200) {
              let list = res.data.list
              this.list = list
              this.$nextTick(() => {
                this.$refs.branchTree.setCurrentKey(this.selectBranchId)
              })
            } else {
              this.$message.error(msg)
            }
          })
        }
      }
    },

    subDlg() {
      this.branchId = this.selectBranchId
      this.branchName = this.selectBranchName
      this.$store.commit('platformMan/branchDlg/SET_BRANCHID', this.branchId)
      this.$store.commit('platformMan/branchDlg/SET_BRANCHNAME', this.branchName)
      this.closeDlg()
    },

    closeDlg() {
      this.$store.commit('platformMan/branchDlg/SET_DLGSHOW', false)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-tree {
  margin-top: 10px;
  height: 600px;
  overflow-y: auto;
}
</style>