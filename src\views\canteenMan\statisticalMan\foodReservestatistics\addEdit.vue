<template>
  <el-dialog
    width="800px"
    title="详情"
    :visible.sync="dialogVisible"
    append-to-body
    @close="closeDialog"
    :close-on-click-modal="false"
  >
    <el-table
      ref="tableBar"
      class="mt10 m-small-table"
      :key="1324"
      :data="tableData"
      :loading="listLoading"
      border
      fit
      highlight-current-row
      height="500"
    >
      <el-table-column
        type="index"
        label="#"
        align="center"
        width="60"
      ></el-table-column>
      <el-table-column
        prop="creatorName"
        label="用户名称"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="num"
        label="预定数量"
        width="auto"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="unit"
        label="单位"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
    <template>
      {{ selectedRow.unit || ""  }}
    </template>
    </el-table-column>
    </el-table>
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getInfo"
    />
    <div slot="footer">
      <el-button @click="closeDialog" size="small">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { postAction, getAction, putAction, deleteAction } from "@/api";
import Pagination from "@/components/Pagination";

export default {
  components: { Pagination },
  props: {},
  data() {
    return {
      searchForm: {
        pageNo: 1,
        pageSize: 20,
      },
      total: 0,
      listLoading: false,
      tableData: [],
      dlgType: "dianzan",
      dialogVisible: false,
      selectedRow: {},
    };
  },
  methods: {
    init(row) {
      this.selectedRow = row;
      this.dialogVisible = true;
      this.getInfo();
    },
    getInfo() {
      if (!this.selectedRow || !this.selectedRow.id) {
        this.$message.error("缺少必要的参数");
        return;
      }
      const apiUrl = `/canteen/cn/food-booking-user/page?foodBookingItemId=${this.selectedRow.id}&pageNo=${this.searchForm.pageNo}&pageSize=${this.searchForm.pageSize}`;
      this.listLoading = true;
      getAction(apiUrl).then((res) => {
        this.listLoading = false;

        const { code, data } = res.data;
        if (code === "200") {
          this.tableData = data.list ? [...data.list] : [];
          this.total = data.total || 0;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    closeDialog() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style></style>