<template>
  <div class="app-container" style="padding: 0; background: none;">
    <div class='departMan-main'>
      <!-- 左侧树型图 -->
      <div class='departMan-left'>
        <el-button v-if="treeList.length == 0" size="small" type="primary" style="display: block;margin: 40px auto" icon="el-icon-plus" title='添加根节点' @click.stop="addTree">
          添加根节点
        </el-button>
        <div v-else>
          <div class="btn-bar">
            <el-button class="fr ml10" @click='moveNode(1)' type='primary' icon='el-icon-sort-down' size='mini' plain>下移</el-button>
            <el-button class="fr ml10" @click='moveNode(0)' type='primary' icon='el-icon-sort-up' size='mini' plain>上移</el-button>
          </div>
          <el-tree style="margin-top: 50px;" :expand-on-click-node='false' ref="treeDom" :data="treeList" :props="defaultProps" :default-expanded-keys="defaultOpenList" node-key="id" @node-click='nodeClick' @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" highlight-current :filter-node-method="projectFilter">
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span :title='data.name'>{{ data.name }}</span>
              <span>
                <el-button class="fr" style="margin-left: 0px; padding: 0 3px;" type="text" size="mini" icon="el-icon-delete" title='删除' @click.stop="removeTree(node, data)">
                </el-button>
                <el-button class="fr" v-if="data.type != 1 && data.status!=1" style="margin-left: 0px; padding: 0 3px;" type="text" size="mini" icon="el-icon-plus" title='添加' @click.stop="appendTree(data)">
                </el-button>
              </span>
            </span>
          </el-tree>
        </div>

      </div>
      <!-- 右侧表单 -->
      <div class="departMan-right">
        <p class='departMan-right-title'>报修科目信息</p>
        <el-form ref="rightForm" :rules="rules" :model="rightFormData" label-width="140px" :style="`margin: 0 20px; overflow: auto`">
          <el-form-item label="上级" prop="superSubjectName">
            <el-input disabled style="width:500px" v-model="rightFormData.superSubjectName" placeholder="上级项目"></el-input>
          </el-form-item>
          <el-form-item label="科目名称" prop="name">
            <el-input style="width:500px" v-model="rightFormData.name" placeholder="请输入科目名称"></el-input>
          </el-form-item>
          <el-form-item label="启用/停用" prop="status">
            <el-radio-group v-model="rightFormData.status">
              <el-radio :label="0" name="status">启用</el-radio>
              <el-radio :label="1" name="status">停用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select disabled style="width:500px" v-model="rightFormData.type" clearable placeholder="请选择类型">
              <el-option label="分类" :value="0"></el-option>
              <el-option label="科目" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <div class="form-btn-bar">
            <el-button icon="el-icon-check" size='medium' type='success' @click="subForm">
              保存
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
    <!-- 新增 -->
    <el-dialog title="新增报修科目" top='30px' :close-on-click-modal='false' :visible.sync="dlgShow" width='600px' append-to-body>
      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-width="110px">
        <el-form-item v-if="dlgData.superSubjectName" label="上级" prop="superSubjectName">
          <el-input disabled v-model="dlgData.superSubjectName" placeholder="上级分类"></el-input>
        </el-form-item>
        <el-form-item label="科目名称" prop="name">
          <el-input v-model="dlgData.name" placeholder="请输入科目名称"></el-input>
        </el-form-item>
        <el-form-item label="启用/停用" prop="status">
          <el-radio-group v-model="dlgData.status">
            <el-radio label="0" name="status">启用</el-radio>
            <el-radio label="1" name="status">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="dlgData.type" clearable placeholder="请选择类型">
            <el-option label="分类" value="0"></el-option>
            <el-option label="科目" value="1"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>关闭</el-button>
        <el-button type="success" @click="subDlg" icon="el-icon-check">新增</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'

import {
  subjectTemplateSave,
  findSubjectTree,
  updateSubjectSortNum,
  updateIsDel
} from '@/api/repairMan'

import * as utils from '@/utils'

import Pagination from '@/components/Pagination'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  superSubjectId: '',  // 	上级id
  superSubjectName: '',  // 	上级名称
  id: 0,
  name: '',  // 	区域名称
  type: '',  // 类型：0:分类 1:科目
  status: '',  // 0:启用 1:停用
}

export default {
  name: 'repairSubjectSetup',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {
      userInfo: {},
      selectNode: {},
      btnLoading: false,

      rightFormData: JSON.parse(JSON.stringify(dlgDataEmpty)),

      rules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        type: [{ required: true, message: '必填字段', trigger: 'change' }],
        status: [{ required: true, message: '必填字段', trigger: 'change' }],
      },

      // 新增弹窗
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgShow: false,

      treeList: [],
      defaultOpenList: [],
      defaultProps: {
        children: 'children',
        label: 'projectName'
      },
      addSubLoading: false,
      dlgData: {},
      dlgShow: false,

      // 表格相关
      listLoading: false,
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        size: 20,
        label: "",
        projectId: ''
      },

      workTypeList: []
    }
  },

  created () {
    this.getTreeList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  mounted () {
  },

  methods: {

    // 弹窗保存方法
    diaSubFunc (sendList) {
      let sendObj = {

      }
      aaa(sendObj).then(res1 => {
        let res = res1.data
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.getList()
        } else {
          this.$message({
            type: 'warning',
            message: res.msg
          })
        }
      })

    },

    // << 左侧树
    // 弹出选择项目弹窗
    showProjectDia () {
      this.$store.commit('SET_BMTREE_TYPE', 'project')  // 只能选 末级 预算科目
      this.$store.commit('SET_BMTREESTATE', true)
    },
    // 筛选项目
    projectFilter (value, data) {
      if (!value) return true;
      return data.projectName.indexOf(value) !== -1;
    },

    // 获取左侧树
    getTreeList () {
      findSubjectTree().then(res1 => {
        let res = res1.data
        if (res.code == 200) {
          if (res.data.length > 0) {
            this.treeList = JSON.parse(JSON.stringify(res.data))
            if (this.defaultOpenList.length == 0) {
              this.defaultOpenList = [res.data[0].id]
            }
            if (!utils.isEmptyObject(this.selectNode)) {
              this.$nextTick(() => {
                this.$refs.treeDom.setCurrentKey(this.selectNode.id)
              })
            }
          } else {
            let treeList = []
          }
        } else {
          this.$message.error(msg)
        }
      })
    },

    // 树节点展开
    handleNodeExpand (data) {
      // 保存当前展开的节点
      let flag = false
      this.defaultOpenList.some(item => {
        if (item === data.id) { // 判断当前节点是否存在， 存在不做处理
          flag = true
          return true
        }
      })
      if (!flag) { // 不存在则存到数组里
        this.defaultOpenList.push(data.id)
      }
    },

    // 树节点关闭
    handleNodeCollapse (data) {
      this.defaultOpenList.some((item, i) => {
        if (item === data.id) {
          // 删除关闭节点
          this.defaultOpenList.length = i
        }
      })
    },

    // 节点点击事件
    nodeClick (data, node, mNode) {
      this.rightFormData = JSON.parse(JSON.stringify(data))
      this.$nextTick(() => {
        this.$refs['rightForm'].clearValidate()
      })
    },

    // 添加节点
    appendTree (data) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.superSubjectId = data.id
      this.dlgData.superSubjectName = data.name
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    addTree () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.superSubjectId = 0
      this.dlgData.superSubjectName = ''
      this.dlgData.id = 0
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 删除节点
    removeTree (node, data) {
      this.$confirm('是否删除该节点?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'  // // success, warning, info, error
      }).then(() => {
        updateIsDel(1, data.id).then(res => {
          let code = res.data.code
          let msg = res.data.msg
          if (code === '200') {
            node.remove(data);
            this.$message({
              type: 'success',  // success, warning, info, error
              message: '删除成功!'
            });

            this.rightFormData = JSON.parse(JSON.stringify(dlgDataEmpty))
            this.$nextTick(() => {
              this.$refs['rightForm'].clearValidate()
            })

          } else {
            this.$message({
              type: 'error',  // success, warning, info, error
              message: msg
            });
          }

        })

      })
    },
    // 上移动下移动 节点
    moveNode (type) {
      this.selectNode = this.$refs.treeDom.getCurrentNode();
      if (utils.isNull(this.selectNode)) {
        return
      }
      if (this.selectNode.id == 0) {
        this.$message({
          message: '不允许移动根节点',
          type: 'warning'
        });
        return
      }
      let selectId = this.selectNode.id
      let siblingsNodes = this.$refs.treeDom.getNode(this.selectNode.id).parent.childNodes

      if (siblingsNodes.length === 1) {
        this.$message({
          message: '当前节点不可移动',
          type: 'warning'
        })
        return false
      }
      let otherId = ''
      let otherIndex = ''

      for (let i = 0; i < siblingsNodes.length; i++) {
        let item = siblingsNodes[i].data
        if (selectId === item.id) {
          otherIndex = i
        }
      }
      if (type === 0) {
        if (otherIndex === 0) {
          this.$message({
            type: 'warning',
            message: '当前节点已经移动到最顶部'
          })
          return false
        }
        otherId = siblingsNodes[otherIndex - 1].data.id
      } else {
        if (otherIndex === siblingsNodes.length - 1) {
          this.$message({
            type: 'warning',
            message: '当前节点已经移动到最底部'
          })
          return false
        }
        otherId = siblingsNodes[otherIndex + 1].data.id
      }

      this.btnLoading = true
      let postParam = {
        areaIds: selectId + ',' + otherId
      }
      updateSubjectSortNum(postParam).then(res => {
        this.btnLoading = false
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          this.$message.success(msg)
          this.getTreeList()
        } else {
          this.$message.error(msg)
        }
      })
    },

    // 右侧表单提交
    subForm () {
      this.$refs['rightForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.rightFormData))
          let loading = this.$loading({
            lock: true,
            text: '提交中',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          subjectTemplateSave(postParam).then(res1 => {
            loading.close()
            let res = res1.data
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.getTreeList()
            } else {
              this.$message({
                type: 'warning',
                message: res.msg
              })
            }
          })

        }
      })
    },

    // 新增报修科目
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {

          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId

          let loading = this.$loading({
            lock: true,
            text: '提交中',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          subjectTemplateSave(postParam).then(res1 => {
            loading.close()
            let res = res1.data
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.dlgShow = false
              this.getTreeList()
            } else {
              this.$message({
                type: 'warning',
                message: res.msg
              })
            }
          })


        }
      })
    },

  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
#order-print {
  position: absolute;
  left: -9999px;
}

.app-container {
  height: calc(100vh - 138px);
}

.departMan-main {
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 100%;
}
//  左侧树状图样式
.departMan-left {
  background: #fff;
  width: 330px;
  min-width: 330px;
  box-sizing: border-box;
  margin-right: 10px;
  padding: 16px;
  // border: 1px solid #dcdfe6;
  overflow-y: auto;
  // .tree-on.el-tree-node__content {
  //   background: #f5f7fa;
  // }
  .custom-tree-node {
    display: inline-block;
    display: flex;
    justify-content: space-between;
    font-size: 13px;

    span:nth-child(1) {
      display: inline-block;
      flex: 1;
      width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    span:nth-child(2) {
      display: inline-block;
      width: 44px;
    }
  }
}
.departMan-right {
  background: #fff;
  flex: 1;
  box-sizing: border-box;
  // border: 1px solid #dcdfe6;
  padding: 16px;
}
.custom-tree-node {
  display: flex;
  justify-content: space-between;
  flex: 1;
  align-items: center;
  font-size: 14px;
}

// 右侧表单
.departMan-right {
  .departMan-right-title {
    font-size: 20px;
    color: #333;
    text-align: left;
    margin-top: 0px;
    margin-bottom: 30px;
    font-weight: bold;
  }
  .m-item-box {
    display: flex;
    justify-content: space-between;
    .el-form-item {
      width: 44%;
    }
  }
  .p-red {
    color: #f56c6c;
    font-size: 14px;
  }
  .input-tips {
    position: absolute;
    left: 0px;
    bottom: -18px;
    color: #f56c6c;
    font-size: 14px;
    line-height: 16px;
    margin: 0px;
    background: #fff;
    z-index: 999;
  }
  .form-btn-bar {
    padding-left: 120px;
    padding-bottom: 30px;
  }
}
.btn-bar {
  position: absolute;
  top: 10px;
  left: 2px;
  z-index: 99;

  padding: 11px 0;

  width: 312px;
  float: right;
  margin-bottom: 10px;
  background: #fff;
}

.upload-wrap {
  display: inline-block;
}

/deep/ .postList .el-form-item__content {
  max-height: 400px;
  overflow-y: auto;
}
</style>
