/** 医疗配套管理系统 **/

import Layout from '@/views/layout/Layout'

const attendanceManRouter = {
  path: '/attendanceMan',
  component: Layout,
  name: 'attendanceMan',
  meta: {
    title: '考勤管理',
    icon: 'kqgl',
    roles: ['kaoqinguanli']
  },
  children: [
    {
      path: 'rulesSet',
      component: () => import('@/views/attendanceMan/rulesSet'),
      name: '规则设置',
      meta: {
        title: '规则设置',
        roles: ['guizeshezhi']
      }
    },
    {
      path: 'holidaySet',
      component: () => import('@/views/attendanceMan/holidaySet'),
      name: '节假日设置',
      meta: {
        title: '节假日设置',
        roles: ['jiejiarishezhi']
      }
    },
    {
      path: 'groupMan',
      component: () => import('@/views/attendanceMan/groupMan'),
      name: '考勤组管理',
      meta: {
        title: '考勤组管理',
        roles: ['kaoqinzuguanli']
      }
    },
    {
      path: 'faceAudit',
      component: () => import('@/views/attendanceMan/faceAudit'),
      name: '人脸审核',
      meta: {
        title: '人脸审核',
        roles: ['renlianlurushenhe']
      }
    },
    {
      path: 'schedulingMan',
      component: () => import('@/views/attendanceMan/schedulingMan'),
      name: '排班管理',
      meta: {
        title: '排班管理',
        roles: ['paibanguanli']
      }
    },
    {
      path: 'attendanceSettle',
      component: () => import('@/views/attendanceMan/attendanceSettle'),
      name: '考勤结算',
      meta: {
        title: '考勤结算',
        roles: ['jisuankaoqinyuebao']
      },
      children: []
    },
    {
      path: 'timeAdjust',
      component: () => import('@/views/attendanceMan/timeAdjust'),
      name: '工时调整',
      meta: {
        title: '工时调整',
        roles: ['gongshitiaozheng']
      }
    },
    {
      path: 'attendanceReport',
      component: () => import('@/views/attendanceMan/attendanceReport/index'),
      name: '考勤报表',
      meta: {
        title: '考勤报表',
        roles: ['kaoqinbaobiao']
      },
      children: [
        {
          path: 'abnormalClock',
          component: () => import('@/views/attendanceMan/attendanceReport/abnormalClock'),
          name: '异常打卡',
          meta: {
            title: '异常打卡',
            roles: ['yichangdaka']
          },
          children: []
        },
        {
          path: 'attendanceDaily',
          component: () => import('@/views/attendanceMan/attendanceReport/attendanceDaily'),
          name: '考勤日报',
          meta: {
            title: '考勤日报',
            roles: ['kaoqinribao']
          },
          children: []
        },
        {
          path: 'attendanceMonth',
          component: () => import('@/views/attendanceMan/attendanceReport/attendanceMonth'),
          name: '考勤月报',
          meta: {
            title: '考勤月报',
            roles: ['kaoqinyuebao']
          },
          children: []
        },
        {
          path: 'attendanceSummary',
          component: () => import('@/views/attendanceMan/attendanceReport/attendanceSummary'),
          name: '考勤汇总',
          meta: {
            title: '考勤汇总',
            roles: ['kaoqinhuizong']
          },
          children: []
        },
        {
          path: 'attendanceMonthRecord',
          component: () => import('@/views/attendanceMan/attendanceReport/attendanceMonthRecord'),
          name: '考勤记录表',
          meta: {
            title: '考勤记录表',
            roles: ['kaoqinjilubiao']
          },
          children: []
        },
        {
          path: 'checkInRecord',
          component: () => import('@/views/attendanceMan/attendanceReport/checkInRecord'),
          name: '打卡记录表',
          meta: {
            title: '打卡记录表',
            roles: ['dakajilubiao']//dakajilubiao
          },
          children: []
        },
      ]
    },

  ]
}

export default attendanceManRouter