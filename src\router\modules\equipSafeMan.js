/** 基础运营管理系统 **/

import Layout from "@/views/layout/Layout";

const equipSafeManRouter = {
  path: "/equipSafeMan",
  component: Layout,
  name: "equipSafeMan",
  meta: {
    title: "设备安全",
    icon: "sbtzgl",
    roles: ["shebeiguanli"]
  },
  children: [
    {
      path: "comprehenOperateScreenFrame",
      component: () => import("@/views/equipSafeMan/comprehenOperateScreenFrame"),
      name: "综合运行大屏",
      meta: {
        title: "综合运行大屏",
        roles: ["zongheyunxingdaping"]
      }
    },

    {
      path: "comprehenOperateMonitorFrame",
      component: () => import("@/views/equipSafeMan/comprehenOperateMonitorFrame"),
      name: "综合运行监测",
      meta: {
        title: "综合运行监测",
        roles: ["zongheyunxingjiance"]
      }
    },

    {
      path: "comprehenOperateMonitor",
      component: () => import("@/views/equipSafeMan/comprehenOperateMonitor"),
      name: "综合运行监控",
      meta: {
        title: "综合运行监控",
        roles: ["zongheyunxingjiankong"]
      }
    },
    {
      path: "comprehenOperateMonitorEryuan",
      component: () => import("@/views/equipSafeMan/comprehenOperateMonitorEryuan"),
      name: "综合运行监控",
      meta: {
        title: "综合运行监控",
        roles: ["zongheyunxingjiankongv2"]
      }
    },
    {
      path: "comprehenOperateMonitorElevator",
      component: () => import("@/views/equipSafeMan/comprehenOperateMonitorElevator"),
      name: "综合运行监控",
      meta: {
        title: "综合运行监控",
        roles: ["zongheyunxingjiankongertongyiyuan"]
      }
    },
    {
      path: "monitorDesc/:equipId",
      name: "综合运行监控详情",
      component: () => import("@/views/equipSafeMan/monitorDesc"),
      hidden: true,
      meta: {
        title: "综合运行监控详情",
        roles: ["zongheyunxingjiankongxiangqing"]
      }
    },
    {
      path: "monitorDesc/:equipId",
      name: "综合运行监控详情",
      component: () => import("@/views/equipSafeMan/monitorDesc"),
      hidden: true,
      meta: {
        title: "综合运行监控详情",
        roles: ["zongheyunxingjiankongxiangqingv2"]
      }
    },
    {
      path: "monitorDesc/:equipId",
      name: "综合运行监控详情",
      component: () => import("@/views/equipSafeMan/monitorDesc"),
      hidden: true,
      meta: {
        title: "综合运行监控详情",
        roles: ["zongheyunxingjiankongxiangqingetyy"]
      }
    },
    {
      path: "scene",
      component: () => import("@/views/equipSafeMan/scene"),
      name: "综合运行监控3D",
      meta: {
        title: "综合运行监控3D",
        roles: ["zongheyunxingjiankong3d"]  // zongheyunxingjiankong3d
      }
    },
    
    {
      path: "systemTelemetry",
      component: () => import("@/views/equipSafeMan/systemTelemetry"),
      name: "系统遥测",
      meta: {
        title: "系统遥测",
        roles: ["xitongyaoce"]
      }
    },
    {
      path: "abnormalAlarmMan",
      component: () => import("@/views/equipSafeMan/abnormalAlarmMan"),
      name: "异常告警管理",
      meta: {
        title: "异常告警管理",
        roles: ["yichanggaojingguanli"]
      }
    },
    {
      path: "abnormalAlarmGroup",
      component: () => import("@/views/equipSafeMan/abnormalAlarmGroup"),
      name: "异常告警统计",
      meta: {
        title: "异常告警统计",
        roles: ["yichanggaojingtongji"]
      }
    },
    {
      path: "runningOperationLog",
      component: () => import("@/views/equipSafeMan/runningOperationLog"),
      name: "运行操作日志",
      meta: {
        title: "运行操作日志",
        roles: ["yunxingcaozuorizhi"]
      }
    },
    {
      path: "smsReminderQuery",
      component: () => import("@/views/equipSafeMan/smsReminderQuery"),
      name: "报警提醒记录",
      meta: {
        title: "报警提醒记录",
        roles: ["duanxintixingchaxun"]
      }
    },

    // {
    //   path: "regionMan",
    //   component: () => import("@/views/equipSafeMan/regionMan"),
    //   name: "区域配置",
    //   meta: {
    //     title: "区域配置",
    //     roles: ["shebeiquyuguanli"]
    //   }
    // },

    // {
    //   path: "equipmentRoomMan",
    //   component: () => import("@/views/equipSafeMan/equipmentRoomMan"),
    //   name: "设备间管理",
    //   meta: {
    //     title: "设备间管理",
    //     roles: ["shebeijianguanli"]
    //   }
    // },
    // {
    //   path: "equipmentMan",
    //   component: () => import("@/views/equipSafeMan/equipmentMan"),
    //   name: "设备台账管理",
    //   meta: {
    //     title: "设备台账管理",
    //     roles: ["shebeiguanli"]
    //   }
    // },
    // {
    //   path: "cameraMan",
    //   component: () => import("@/views/equipSafeMan/cameraMan"),
    //   name: "摄像头管理",
    //   meta: {
    //     title: "摄像头管理",
    //     roles: ["shexiangtouguanli"]
    //   }
    // },
    {
      path: "cameraArchives",
      component: () => import("@/views/equipSafeMan/cameraArchives"),
      name: "摄像头档案",
      meta: {
        title: "摄像头档案",
        roles: ["shexiangtoudangan"]
      }
    },
    {
      path: "cameraManNew",
      component: () => import("@/views/equipSafeMan/cameraManNew"),
      name: "摄像机管理",
      meta: {
        title: "摄像机管理",
        roles: ["shexiangtouguanli"]
      }
    },
    {
      path: "gateway",
      component: () => import("@/views/equipSafeMan/gateway/index"),
      name: "网关管理",
      meta: {
        title: "网关管理",
        roles: ["wangguanguanli"]
      }
    },
    {
      path: "sensorNode",
      component: () => import("@/views/equipSafeMan/sensorNode"),
      name: "传感器节点",
      meta: {
        title: "传感器节点",
        roles: ["chuanganqijiedian"]
      }
    },
    {
      path: "sensorEquip",
      component: () => import("@/views/equipSafeMan/sensorEquip"),
      name: "传感器设备",
      meta: {
        title: "传感器设备",
        roles: ["chuanganqishebei"]
      }
    },
    {
      path: "sensorExecutionPlan",
      component: () => import("@/views/equipSafeMan/sensorExecutionPlan"),
      name: "传感器执行计划",
      meta: {
        title: "传感器执行计划",
        roles: ["chuanganqizhixingjihua"]
      }
    },
    {
      path: "switchEquip",
      component: () => import("@/views/equipSafeMan/switchEquip"),
      name: "开关设备",
      meta: {
        title: "开关设备",
        roles: ["kaiguanshebei"]
      }
    },

    {
      path: "operationMonitoring",
      component: () => import("@/views/equipSafeMan/operationMonitoring"),
      name: "运行监控",
      meta: {
        title: "运行监控",
        roles: ["yunxingjiankong"]
      }
    },
    {
      path: "equipmentRoomConf",
      component: () => import("@/views/equipSafeMan/equipmentRoomConf"),
      name: "设备间配置",
      meta: {
        title: "设备间配置",
        roles: ["shebeijianpeizhi"]
      }
    },
    {
      path: "fireCameraMonitoring",
      component: () => import("@/views/equipSafeMan/fireCameraMonitoring"),
      name: "控烟监控",
      meta: {
        title: "控烟监控",
        roles: ["kongyanjiankong"]
      }
    },

    {
      path: "sceneManage",
      component: () => import("@/views/equipSafeMan/sceneManage"),
      name: "场景管理",
      meta: {
        title: "场景管理",
        roles: ["shebei_changjingguanli"]  // shebei_changjingguanli
      }
    },
  ]
};

export default equipSafeManRouter;
