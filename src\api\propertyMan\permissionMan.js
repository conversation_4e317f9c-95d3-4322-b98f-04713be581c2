import request from '@/utils/request'

/*
 *权限权限相关
 */

// 查询权限列表 
export function spusPage(data) {
  return request({
    url: `/sys/spusPage`,
    method: 'post',
    data
  })
}
// 新增权限
export function saveProUser(data) {
  return request({
    url: `/sys/saveProUser`,
    method: 'post',
    data
  })
}

// 删除权限
export function removeProUser(id) {
  return request({
    url: `/sys/removeProUser?id=${id}`,
    method: 'get'
  })
}
