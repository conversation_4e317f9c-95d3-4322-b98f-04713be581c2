<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区：">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="费用类型：">
          <el-select v-model="listQuery.feeTypeCd" filterable clearable placeholder="请选择费用类型">
            <el-option v-for="item in costTypeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收费项目：">
          <el-input @keyup.enter.native='getList' placeholder='请输入收费项目' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>添加</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="收费项目">
          <template slot-scope="scope">
            <span>{{ scope.row.feeName	}}</span>
          </template>
        </el-table-column>

        <el-table-column label="费用类型">
          <template slot-scope="scope">
            <span>{{ scope.row.feeTypeCdText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="费用标识">
          <template slot-scope="scope">
            <span>{{ scope.row.feeFlagText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="付费类型">
          <template slot-scope="scope">
            <span>{{ scope.row.paymentCdText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="缴费周期(单位：月)">
          <template slot-scope="scope">
            <span>{{ scope.row.paymentCycle }}</span>
          </template>
        </el-table-column>

        <el-table-column label="计费公式">
          <template slot-scope="scope">
            <span>{{ scope.row.contentName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row, 'EDIT')">编辑</el-button>
            <el-button type="success" size="mini" icon="el-icon-edit" plain @click="discountItem(scope.row)">滞纳金</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑计费设置" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :disabled="dlgType == 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="所在小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区" @change="getFormulaList">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="费用类型" prop="feeTypeCd">
          <el-select v-model="dlgData.feeTypeCd" filterable clearable placeholder="请选择费用类型">
            <el-option v-for="item in costTypeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="收费项目" prop="feeName">
          <el-input v-model="dlgData.feeName	" placeholder="请输入收费项目" />
        </el-form-item>

        <el-form-item label="费用标识" prop="feeFlag">
          <el-select v-model="dlgData.feeFlag" filterable clearable placeholder="请选择费用标识">
            <el-option v-for="item in feeFlagList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="付费类型" prop="paymentCd">
          <el-select v-model="dlgData.paymentCd" filterable clearable placeholder="请选择费用标识">
            <el-option v-for="item in paymentCdList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="计算公式" prop="formulaId">
          <el-select v-model="dlgData.formulaId" filterable clearable placeholder="请选择计算公式">
            <el-option v-for="item in computingFormulaList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage } from '@/api/communityMan'
import { payFeeConfigPage, addOrUpdatePayFeeConfig, delPayFeeConfigById, formulaPage } from '@/api/costMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  communityId: '',
  communityName: '',
  feeTypeCd: '',
  feeTypeCdText: '',
  feeFlag: '',
  feeFlagText: '',
  formulaId: '',
  paymentCd: '',
  feeName: '',
  squarePrice: 0,
  amount: 0,
  additionalAmount: 0,
}


export default {
  name: 'discountSetup',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        feeTypeCd: [{ required: true, message: '必填字段', trigger: 'change' }],
        squarePrice: [{ required: true, message: '必填字段', trigger: 'blur' }],
        feeFlag: [{ required: true, message: '必填字段', trigger: 'change' }],
        feeName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        paymentCd: [{ required: true, message: '必填字段', trigger: 'change' }],
        formulaId: [{ required: true, message: '必填字段', trigger: 'change' }],
        squarePrice: [{ required: true, message: '必填字段', trigger: 'blur' }],
        additionalAmount: [{ required: true, message: '必填字段', trigger: 'blur' }],
        amount: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      dlgDataOld: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        feeTypeCd: ''
      },
      communityList: [],
      userInfo: {},
      costTypeList: [],
      computingFormulaList: [],
      feeFlagList: [
        {
          id: '1',
          name: '周期性费用'
        },
        {
          id: '2',
          name: '一次性费用'
        },
      ],

      paymentCdList: [
        {
          id: '1',
          name: '预付费'
        },
        {
          id: '2',
          name: '后付费'
        }
      ],
    }
  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    utils.getDataDict(this, 'costType', 'costTypeList')
  },

  methods: {
    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/workade/kaoqinyuebaodaochu?` + param
      window.open(sendUrl)
    },

    // 下载
    downloadItem () {
      let url =
        'https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E7%97%85%E7%A7%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xls'
      window.open(url)
    },

    // 上传
    uploadItem (file) {
      let name = file.name.split('.')
      let suffix = name[name.length - 1]

      if (suffix !== 'xls' && suffix !== 'xlsx') {
        this.$message({
          type: 'warning',
          message: '只能上传xls/xlsx文件'
        })
        return false
      }

      let loading = this.$loading({
        lock: true,
        text: '导入中',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      let postParam = {
        file
      }

      importExcelDisease(postParam).then(res => {
        loading.close()
        if (res.data.code == 200) {
          this.$message.success('导入成功')
          this.getList()
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })

      return false
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取公式列表
    getFormulaList () {
      if (utils.isNull(this.dlgData.communityId)) {
        return
      }
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.dlgData.communityId
      }
      this.computingFormulaList = []
      formulaPage(postParam).then(res => {
        if (res.data.code == 200) {
          let list = res.data.data ? res.data.data : []
          for (let i of list) {
            this.computingFormulaList.push({
              id: i.id,
              name: i.name + ' ' + i.content
            })
          }
        }
      })

    },

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    formatList () {
      for (let i of this.list) {
        i.feeFlagText = utils.getNameById(i.feeFlag, this.feeFlagList)
        i.paymentCdText = utils.getNameById(i.paymentCd, this.paymentCdList)
      }
    },

    // 获取数据
    getList () {
      // if (utils.isNull(this.listQuery.communityId)) {
      //   this.$message.warning("请选择小区")
      //   return
      // }
      this.count++
      this.listLoading = true
      payFeeConfigPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page.total ? res.data.page.total : 0
          this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.getFormulaList()
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },


    // 提交接口
    subAjax () {
      let postParam = JSON.parse(JSON.stringify(this.dlgData))
      postParam.projectId = this.userInfo.projectId
      postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
      postParam.feeTypeCdText = utils.getNameById(postParam.feeTypeCd, this.costTypeList)
      postParam.contentName = utils.getNameById(postParam.formulaId, this.computingFormulaList)
      this.dlgLoading = true
      addOrUpdatePayFeeConfig(postParam).then(res => {
        this.dlgLoading = false
        if (res.data.code == 200) {
          this.getList()
          this.dlgShow = false
          this.$message.success(res.data.msg)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          if (this.dlgType == 'EDIT') {
            if (this.dlgData.squarePrice == this.dlgDataOld.squarePrice) {
              this.subAjax()
            } else {
              const h = this.$createElement;
              this.$msgbox({
                title: '房屋信息改动',
                message: h('div', { class: 'confirmTip' }, [
                  h('p', { class: 'fwarning' }, '原单价:' + this.dlgDataOld.squarePrice + '元/m²'),
                  h('p', { class: 'fsuccess' }, '更改后单价:' + this.dlgData.squarePrice + '元/m²'),
                  h('p', null, '修改单价后，新产生计费会有变化，是否继续?'),
                ]),
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                beforeClose: (action, instance, done) => {
                  if (action === 'confirm') {
                    this.subAjax()
                    done();
                  } else {
                    done();
                  }
                }
              })
            }
          } else {
            this.subAjax()
          }
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgDataOld = JSON.parse(JSON.stringify(this.dlgData))
      this.dlgType = type
      this.dlgShow = true
      this.getFormulaList()
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 滞纳金
    discountItem (data) {
      this.$router.push({ path: `/communityProperty/costMan/billingSetupDesc/${data.id}` })
    },

    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delPayFeeConfigById(data.id, flag).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


