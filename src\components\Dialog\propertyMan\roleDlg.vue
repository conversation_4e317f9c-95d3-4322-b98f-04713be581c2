<template>
  <el-dialog :close-on-click-modal='false' :title="'选择角色'" @close="closeDlg" :visible.sync="dlgShow" top="5vh" append-to-body>
    <div class="filter-container">
      <div class='fr'>
        <el-input v-model="listQuery.str" placeholder='请填写角色名称'>
          <i slot="suffix" @click="resetStr" class="el-input__icon el-icon-error"></i>
        </el-input>
        <el-button icon='el-icon-search' type="success" size='mini' @click="searchItem">
          搜索
        </el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' :data="list" @row-click="rowClick" border fit highlight-current-row>
        <el-table-column label="#" width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectRoleId" :label="scope.row.id">
              <i></i>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="角色名称" width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="220px">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">
        取 消
      </el-button>
      <el-button type="primary" @click="subDlg" icon="el-icon-check">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookie from 'js-cookie'

import Pagination from '@/components/Pagination'
import * as utils from '@/utils'

import { findRoleLike } from '@/api/powermanager.js'

export default {
  components: {
    Pagination
  },
  data () {
    return {
      list: [],

      listQuery: {
        size: 10,
        page: 1,
      },

      total: 0,

      selectRoleId: '',

      selectRoleName: '',

    }
  },

  computed: {
    dlgShow: {
      get: function () {
        return this.$store.state.propertyMan.roleDlg.dlgShow
      },
      set: function (val) {
        this.$store.commit('propertyMan/roleDlg/SET_DLGSHOW', val)
      }
    },

    dlgType: {
      get: function () {
        return this.$store.state.propertyMan.roleDlg.dlgType
      },
      set: function (val) {
        this.$store.commit('propertyMan/roleDlg/SET_DLGTYPE', val)
      }
    },

    roleId: {
      get: function () {
        return this.$store.state.propertyMan.roleDlg.roleId
      },
      set: function (val) {
        this.$store.commit('propertyMan/roleDlg/SET_ROLEID', val)
      }
    },

    roleName: {
      get: function () {
        return this.$store.state.propertyMan.roleDlg.roleName
      },
      set: function (val) {
        this.$store.commit('propertyMan/roleDlg/SET_ROLENAME', val)
      }
    },

  },

  watch: {

    dlgShow (val) {
      if (val) {
        if (utils.isNull(this.roleId)) {
          this.selectRoleId = ""
          this.selectRoleName = ""
        }
        this.getList()
      }
    },

    roleId (val) {
      this.selectRoleId = val
    },

    roleName (val) {
      this.selectRoleName = val
    },

  },

  methods: {
    resetStr () {
      this.listQuery.str = ""
      this.getList()
    },

    searchItem () {
      this.getList()
    },

    rowClick (row, column, event) {
      this.selectRoleId = row['id']
      this.selectRoleName = row['label']
    },

    getList () {
      this.list = []
      let method = findRoleLike
      method(this.listQuery).then(res => {
        let code = res.data.code
        let msg = res.data.msg
        if (code == 200) {
          let data = res.data.data
          if (utils.isNull(data)) {
            this.total = 0
            return
          }
          let list = res.data.list
          this.list = list
          this.total = res.data.data.total
        } else {
          this.$message.error(msg)
        }
      })
    },

    subDlg () {
      this.roleId = this.selectRoleId
      this.roleName = this.selectRoleName
      this.$store.commit('propertyMan/roleDlg/SET_ROLEID', this.roleId)
      this.$store.commit('propertyMan/roleDlg/SET_ROLENAME', this.roleName)
      this.closeDlg()
    },

    closeDlg () {
      this.$store.commit('propertyMan/roleDlg/SET_DLGTYPE', '')
      this.$store.commit('propertyMan/roleDlg/SET_DLGSHOW', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  height: 600px;
}

/deep/ .el-dialog__body {
  height: calc(100% - 110px);
}

/deep/ .el-tree {
  margin-top: 10px;
  height: calc(100% - 30px);
  overflow-y: auto;
}

.filter-container {
  height: 50px;
}

.filter-container button {
  height: 28px;
}

.filter-container .fr > .el-input,
.filter-container .fr > .el-select {
  width: 200px;
  margin-left: 10px;
}

.left-right-container {
  height: 100%;
}

.left-container {
  float: left;
  height: 100%;
  width: 300px;
}

.right-container {
  float: right;
  height: 100%;
  width: calc(100% - 310px);
}
</style>