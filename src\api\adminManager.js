import request from '@/utils/request'

/*
*用户管理相关
*/

// 查询用户列表 
export function findUserLike(data) {
	return request({
		url: `/sys/findUserLike`,
		method: 'post',
		data
	})
}
// 新增角色
export function saveSysUser(data) {
	return request({
		url: `/sys/saveSysUser`,
		method: 'post',
		data
	})
}
// 修改角色
export function upDateSysUser(data) {
	return request({
		url: `/sys/upDateSysUser`,
		method: 'post',
		data
	})
}
// 删除角色
export function delSysUser(data) {
	return request({
		url: `/sys/delSysUser`,
		method: 'post',
		data
	})
}





