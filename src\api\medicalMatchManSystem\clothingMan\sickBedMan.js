import request from '@/utils/request'

/*
 * 病床管理
 */

// 新增/修改病床信息接口
export function saveOrUSickBed(data) {
  return request({
    url: `/sys/saveOrUSickBed`,
    method: 'post',
    data
  })
}

// 修改病床信息状态（删除病床信息）
export function updateSickBed(data) {
  return request({
    url: `/sys/updateSickBed`,
    method: 'post',
    data
  })
}

// 动态查询病床信息 分页
export function findSickBedDynamic(data) {
  return request({
    url: `/sys/findSickBedDynamic`,
    method: 'post',
    data
  })
}
