const usersTable = {
  state: {
    usersTableState: false,
    usersTableBranchId: '',
    usersTableBranchName: '',
    usersTableUserIds: '',
    usersTableUserNames: '',
  },

  mutations: {
    SET_USERSTABLESTATE: (state, usersTableState) => {
      state.usersTableState = usersTableState
    },
    SET_USERSTABLEBRANCHID: (state, usersTableBranchId) => {
      state.usersTableBranchId = usersTableBranchId
    },
    SET_USERSTABLEBRANCHNAME: (state, usersTableBranchName) => {
      state.usersTableBranchName = usersTableBranchName
    },
    SET_USERSTABLEUSERIDS: (state, usersTableUserIds) => {
      state.usersTableUserIds = usersTableUserIds
    },
    SET_USERSTABLEUSERNAMES: (state, usersTableUserNames) => {
      state.usersTableUserNames = usersTableUserNames
    },
  },

  actions: {

  }
}

export default usersTable
