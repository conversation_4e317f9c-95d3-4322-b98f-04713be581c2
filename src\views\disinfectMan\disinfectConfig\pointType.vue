<template>
  <!--点位类型设置-->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <!-- <el-form-item label="选择类型:">
          <el-select v-model="listQuery.pointTypeId" filterable clearable placeholder="请选择类型">
            <el-option v-for="item in list" :key="item.id" :label="item.typeName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native="getList" placeholder="请输入关键字" v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon="el-icon-search" type="success" size="mini" @click="searchItem">搜索</el-button>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="addItem">添加</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table
        class="m-small-table"
        height="100%"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        :empty-text="count == 0 ? '请搜索' : '暂无数据'"
      >
        <el-table-column label="序号" type="index" align="center" width="60"> </el-table-column>

        <el-table-column label="类型名称">
          <template slot-scope="scope">
            <span>{{ scope.row.typeName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="显示名称">
          <template slot-scope="scope">
            <span>{{ scope.row.showName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="消毒频率">
          <template slot-scope="scope">
            <span>{{ scope.row.frequency }}</span>
          </template>
        </el-table-column>

        <el-table-column label="消毒时长/h">
          <template slot-scope="scope">
            <span>{{ scope.row.duration }}</span>
          </template>
        </el-table-column>

        <el-table-column label="表头备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.title }}</span>
          </template>
        </el-table-column>

        <el-table-column label="表尾备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.tail }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="320" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row)">删除</el-button>
            <el-button type="success" size="mini" icon="el-icon-plus" plain @click="addXdx(scope.row)">消毒项</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="dlgShow" width="600px" append-to-body>
      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="类型名称" prop="typeName" label-width="110px">
          <el-input v-model="dlgData.typeName" placeholder="请输入类型名称" />
        </el-form-item>

        <el-form-item label="显示名称" prop="showName" label-width="110px">
          <el-input v-model="dlgData.showName" placeholder="请输入显示名称" />
        </el-form-item>

        <el-form-item label="消毒频率" prop="frequencyValue" label-width="110px">
          <el-select v-model="dlgData.frequencyValue" filterable clearable placeholder="请选择消毒频率">
            <el-option v-for="item in frequencyList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="消毒时长(/h)" prop="duration" label-width="110px">
          <el-input-number v-model="dlgData.duration" controls-position="right" :min="0" :precision="0" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item label="签字人数" prop="signType" label-width="110px">
          <el-radio-group v-model="dlgData.signType">
            <el-radio :label="0">单人</el-radio>
            <el-radio :label="1">多人</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="表头备注" label-width="110px">
          <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" v-model="dlgData.title" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="表尾备注" label-width="110px">
          <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" v-model="dlgData.tail" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button type="success" :loading="dlgLoading" @click="subDlg" icon="el-icon-check">
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" title="消毒项设置" :visible.sync="xdxDlgShow" width="600px" append-to-body @close="cloXdxDlg">
      <div>
        <el-form ref="xdxDlgForm" :rules="xdxRules" :model="xdxDlgData" label-position="right" label-width="100px">
          <el-button icon="el-icon-plus" type="primary" size="mini" @click="addAttribute">添加属性</el-button>
          <el-dropdown trigger="click" @command="handleCommand">
            <el-button type="success"> <i class="el-icon-arrow-down el-icon-plus" style="margin-right: 5px"></i>添加消毒方式 </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="item" v-for="(item, index) in disinfectionMethodList" :key="index">{{
                item.name
              }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <el-table
            v-if="attributeList.length > 0"
            class="m-small-table"
            max-height="200px"
            style="margin-top: 10px"
            v-loading="listLoading"
            :data="attributeList"
            border
            fit
            highlight-current-row
            :empty-text="count == 0 ? '请搜索' : '暂无数据'"
          >
            <el-table-column label="属性名称">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型">
              <template slot-scope="scope">
                <span v-if="scope.row.type == 1">单选</span>
                <span v-if="scope.row.type == 0">文本</span>
                <span v-if="scope.row.type == 2">多选</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delAttributeItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-for="(item1, index1) in cardList" :key="index1" style="border: 1px solid #e9e9e9; margin-top: 20px">
            <el-card class="box-card" style="box-shadow: none; border: none; width: 557px">
              <div slot="header" class="" style="position: relative">
                <span style="margin-bottom: 10px; font-size: 16px; font-weight: bolder">{{ item1.name }}</span>
                <div class="fr" id="popovers">
                  <el-popover
                    width="20"
                    v-model="item1.isShow"
                    placement="right"
                    class="popover"
                    style="
                  min-width:100px:
                  font-size: 10px;
                  font-weight: bolder;
                "
                  >
                    <div style="text-align: center; margin: 0">
                      <el-button type="text" size="mini" @click="delCard(item1, index1)" style="font-size: 14px; margin-right: 74px"
                        >删除</el-button
                      >
                    </div>
                    <el-button slot="reference" @click="item1.isShow = true">· · ·</el-button>
                  </el-popover>
                </div>
                <div class="fr" style="display: flex; color: #999999; margin: -6px 40px 0 0">
                  <span
                    @click="upData(index1)"
                    v-if="cardList.length > 0 && index1 != 0"
                    style="cursor: pointer; margin-right: 20px; margin-bottom: 10px"
                  >
                    <img style="width: 12px; height: 16px" src="/static/image/icon_up.png" alt="" />
                  </span>
                  <div @click="downData(index1)" style="cursor: pointer; margin-right: 20px" v-if="cardList.length != index1 + 1">
                    <img style="width: 12px; height: 16px" src="/static/image/icon_down.png" alt="" />
                  </div>
                </div>

                <!-- <el-button style="float: right; padding: 3px 0;font-size:10px;font-weight: bolder;" type="text">· · ·</el-button> -->
              </div>
              <div v-for="(item2, index2) in item1.options" :key="index2" class="text item" style="margin-top: 20px; line-height: 39px">
                <div v-if="item2.isShow">
                  <el-input style="width: 200px; margin: 0 20px 0 40px" v-model="item2.name" placeholder="请输入消毒项名称" />
                  <el-button @click="cancelInput(item1, item2, index2)">取消</el-button>
                  <el-button type="primary" @click="showItemName(item2)">保存</el-button>
                </div>
                <div v-else style="border-bottom: 1px solid #e9e9e9; height: 37px; position: relative">
                  <span style="margin-left: 30px">{{ item2.name }}</span>
                  <div class="fr" id="popovers">
                    <el-popover
                      width="150px"
                      v-model="item2.isEdit"
                      class="popover2"
                      placement="right"
                      style="font-size: 10px; font-weight: bolder"
                    >
                      <div style="text-align: center; margin: 0; display: flex; flex-direction: column; justify-content: flex-start">
                        <el-button
                          type="text"
                          size="mini"
                          style="margin-right: 74px; margin-bottom: 10px; font-size: 14px"
                          @click="editAddDisinfectionItem(item2, index2)"
                          >编辑</el-button
                        >
                        <el-button
                          type="text"
                          size="mini"
                          style="margin-right: 74px; font-size: 14px; margin-left: 0px"
                          @click="delAddDisinfectionItem(item1, item2, index2)"
                          >删除</el-button
                        >
                      </div>
                      <el-button slot="reference" @click="item2.isEdit = true">· · ·</el-button>
                    </el-popover>
                  </div>
                </div>
              </div>
            </el-card>
            <div style="line-height: 37px; cursor: pointer; color: #1890ff">
              <span style="margin-left: 19px" @click="addDisinfectionItem(item1)">+ 添加消毒项</span>
            </div>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="xdxDlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button type="success" :loading="dlgLoading" @click="subXdxDlg" icon="el-icon-check">
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      title="属性设置"
      @close="closeAttributeDlg"
      :visible.sync="attributeDlgShow"
      width="600px"
      append-to-body
    >
      <el-form ref="attributeDlgForm" :rules="attributeRules" :model="attributeData" label-position="right" label-width="100px">
        <el-form-item label="属性名称" prop="name">
          <el-input v-model="attributeData.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="" prop="radio">
          <el-radio-group v-model="attributeData.type" @change="radioChange">
            <el-radio :label="0">文本</el-radio>
            <el-radio :label="1">单选</el-radio>
            <el-radio :label="2">多选</el-radio>
          </el-radio-group>
        </el-form-item>
        <div
          v-for="(item, index) in attributeData.attributeValues"
          :key="index"
          v-show="attributeData.type == 1 || attributeData.type == 2"
        >
          <el-form-item :label="`选项${index + 1}`" prop="radio">
            <el-input style="width: 200px" v-model="item.optiona" placeholder="请输入选项值" />
            <el-button
              type="success"
              v-show="attributeData.attributeValues.length == index + 1"
              icon="el-icon-plus"
              circle
              @click="addOption"
            ></el-button>
            <el-button type="danger" icon="el-icon-delete" circle @click="deleteOptions(item, index)" v-show="index > 0"></el-button>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="attributeDlgShow = false" icon="el-icon-back">取消</el-button>
        <el-button type="success" :loading="dlgLoading" @click="subAttributeDlg" icon="el-icon-check">
          <span v-if="dlgLoading">提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import {
  disPointTypePage,
  disPointTypeAdd,
  disPointTypeDel,
  dismodePage,
  disPointTypeSet,
  disPointTypeInfo,
  disPointPage,
} from '@/api/disinfectMan/disinfectConfig'
import {
  getDataDictOther, // 数据字典
} from '@/utils'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  typeName: '', //类型名称
  showName: '', //显示名称
  frequencyValue: '', //消毒频率
  frequency: '',
  duration: '', //消毒时长
  title: '', //表头备注
  tail: '', //表尾备注
  projectId: '',
  projectName: '',
  signType: 0,
}
let xdxDlgDataEmpty = {}
let attributeDlgDataEmpty = {
  name: '',
  type: 0,
  attributeValues: [
    {
      optiona: '',
    },
  ],
}

export default {
  name: 'buildInfo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data() {
    return {
      // 弹窗 状态
      dlgShow: false, // 新增
      dlgType: '', // ADD\EDIT
      dlgTitle: '', // 标题
      rules: {
        typeName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        showName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        frequencyValue: [{ required: true, message: '必填字段', trigger: 'change' }],
        duration: [{ required: true, message: '必填字段', trigger: 'blur' }],
        signType: [{ required: true, message: '必填字段', trigger: 'change' }],
      },
      xdxRules: {},
      attributeRules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      xdxDlgData: JSON.parse(JSON.stringify(xdxDlgDataEmpty)),
      attributeData: JSON.parse(JSON.stringify(attributeDlgDataEmpty)),
      xdxDlgShow: false,
      oldXdxName: '',
      attributeDlgShow: false,
      attributeList: [],
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        pointTypeId: '',
      },
      disinfectionMethodQuery: {
        page: 1,
        limit: 20,
        label: '',
      },
      listPointQuery: {
        page: 1,
        limit: 20,
        pointTypeId: '',
        label: '',
      },
      disinfectionMethodList0: [], //消毒方式
      disinfectionMethodList: [], //消毒方式
      frequencyList: [], //消毒频率列表
      addDisinfectionType: '',
      cardList: [],
      userInfo: {},
      rowId: '',
      title: '',
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getList()
    this.getDisinfectionMethod()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    getDataDictOther(this, 'xiaodupinlv', 'frequencyList') // 服务类别
  },

  methods: {
    getDisinfectionMethod() {
      dismodePage(this.disinfectionMethodQuery).then((res) => {
        if (res.data.code == 200) {
          this.disinfectionMethodList0 = JSON.parse(JSON.stringify(res.data.data))
          this.disinfectionMethodList = JSON.parse(JSON.stringify(res.data.data))
        }
      })
    },
    searchItem() {
      this.getList()
    },

    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
      this.getList()
    },

    // 获取数据
    getList() {
      this.listLoading = true
      disPointTypePage(this.listQuery).then((res) => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    addItem() {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.title = '添加点位类型'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg() {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.projectName = this.userInfo.projectName
          postParam.frequency = utils.getNameById(postParam.frequencyValue, this.frequencyList)
          this.dlgLoading = true
          disPointTypeAdd(postParam).then((res) => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    //添加消毒项
    addDisinfectionItem(item, index) {
      this.addDisinfectionType = 'ADD'
      item.options.push({
        isEdit: false,
        isShow: true,
        name: '',
        inputType: '',
        oldXdxName: '',
      })
    },
    //删除消毒项
    delAddDisinfectionItem(item1, item2, index) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        item1.options.splice(index, 1)
        item2.isEdit = false
      })
    },
    //编辑消毒项
    editAddDisinfectionItem(item, index) {
      item.inputType = 'preservation'
      item.oldXdxName = item.name
      item.isShow = true
      item.isEdit = false
    },
    //保存
    showItemName(item) {
      if (item.name == '') {
        this.$message.warning('请输入消毒项名称')
        return
      }
      item.isShow = false
      this.$forceUpdate()
    },
    //取消
    cancelInput(item1, item2, index) {
      if (item2.inputType == 'preservation') {
        item2.isShow = false
        item2.name = item2.oldXdxName
        item2.inputType = ''
        this.$forceUpdate()
      } else {
        item1.options.splice(index, 1)
      }
    },

    //消毒项提交
    subXdxDlg() {
      // if (this.attributeList.length <= 0) {
      //   this.$message.warning("请添加属性");
      //   return;
      // }
      if (this.cardList.length <= 0) {
        this.$message.warning('请添加消毒方式')
        return
      }
      for (let i of this.cardList) {
        if (i.options.length <= 0) {
          this.$message.warning('请添加消毒项')
          return
        }
        for (let item of i.options) {
          if (item.name == '') {
            this.$message.warning('请输入消毒项名称')
            return
          }
          if (item.isShow == true) {
            this.$message.warning('请先保存消毒项')
            return
          } else {
            delete item.isShow
            delete item.isEdit
            delete i.isShow
          }
        }
      }
      let sendObj = {
        id: this.rowId,
        attributes: JSON.parse(JSON.stringify(this.attributeList)),
        modes: JSON.parse(JSON.stringify(this.cardList)),
      }
      for (let i of sendObj.modes) {
        i.modeId = i.id
        i.modeName = i.name
      }
      for (let i of sendObj.attributes) {
        if (i.type == 0) {
          delete i.attributeValues
        }
      }
      disPointTypeSet(sendObj).then((res) => {
        if (res.data.code == '200') {
          this.$message.success(res.data.msg)
          this.xdxDlgShow = false
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 添加属性
    addAttribute() {
      this.attributeDlgShow = true
    },

    //属性提交
    subAttributeDlg() {
      this.$refs.attributeDlgForm.validate((valid) => {
        if (valid) {
          if (this.attributeData.type == 1 || this.attributeData.type == 2) {
            for (let i of this.attributeData.attributeValues) {
              if (i.optiona == '') {
                this.$message.warning('请输入选项值')
                return
              }
            }
          }
          this.attributeList.push({
            name: this.attributeData.name,
            type: this.attributeData.type,
            attributeValues: this.attributeData.attributeValues,
          })
          this.attributeDlgShow = false
        }
      })
    },

    delAttributeItem(idx) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.attributeList.splice(idx, 1)
        this.$forceUpdate()
      })
    },
    //添加消毒方式
    handleCommand(command) {
      this.cardList.push({
        isShow: false,
        id: command.id,
        flag: command.flag,
        name: command.name,
        remark: command.remark,
        options: [],
      })
      this.disinfectionMethodList.forEach((value, index, array) => {
        if (command.id == value.id) {
          this.disinfectionMethodList.splice(index, 1)
        }
      })
      this.disinfectionMethodList = JSON.parse(JSON.stringify(this.disinfectionMethodList))
    },
    //删除消毒方式
    delCard(item, index) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.cardList.splice(index, 1)
        item.options = []
        item.isShow = false
        this.disinfectionMethodList.push({
          id: item.id,
          flag: item.flag,
          name: item.name,
          remark: item.remark,
        })
      })
    },
    //上移
    upData(index, column) {
      if (index === 0) {
        return
      } else {
        // sort是排序
        this.cardList[index - 1].sort = Number(this.cardList[index - 1].sort) + 1
      }
      // 在上一项插入该项
      this.cardList.splice(index - 1, 0, this.cardList[index])
      // 删除后一项
      this.cardList.splice(index + 1, 1)
      this.$message.success('上移成功')
    },
    //下移
    downData(index) {
      if (index === this.cardList.length - 1) {
        return
      } else {
        // sort是排序
        this.cardList[index + 1].sort = Number(this.cardList[index + 1].sort) - 1
      }
      // 在下一项插入该项
      this.cardList.splice(index + 2, 0, this.cardList[index])
      // 删除前一项
      this.cardList.splice(index, 1)
      this.$message.success('下移成功')
    },
    closeAttributeDlg() {
      this.attributeData = JSON.parse(JSON.stringify(attributeDlgDataEmpty))
      this.$refs.attributeDlgForm.clearValidate()
    },
    radioChange() {
      this.attributeData.attributeValues = [{ optiona: '' }]
    },
    //删除选项
    deleteOptions(item, index) {
      if (this.attributeData.attributeValues.length <= 1) {
        //如果只有一个输入框则不显示
        return false
      }
      this.attributeData.attributeValues.splice(index, 1)
    },
    addOption() {
      this.attributeData.attributeValues.push({
        optiona: '',
      })
    },

    // 编辑
    editItem(data) {
      let row = JSON.parse(JSON.stringify(data))
      this.dlgData.typeName = row.typeName
      this.dlgData.id = row.id
      this.dlgData.showName = row.showName
      this.dlgData.frequencyValue = row.frequencyValue
      this.dlgData.frequency = row.frequency
      this.dlgData.duration = row.duration
      this.dlgData.title = row.title
      this.dlgData.tail = row.tail
      this.dlgType = 'EDIT'
      this.title = '修改点位类型'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    delItem(data, flag) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        disPointTypeDel(data.id).then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },
    //消毒项详情
    getInfoXdx(id) {
      disPointTypeInfo(id).then((res) => {
        let data = res.data.data
        let idsArr = []
        if (data.modes.length > 0) {
          for (let i of data.modes) {
            i.id = i.modeId
            i.name = i.modeName
            idsArr.push(i.name)
          }
          this.cardList = data.modes
        }
        if (data.attributes.length > 0) {
          this.attributeList = data.attributes
        }

        // idsArr
        console.log('====idsArr', idsArr)
        let disinfectionMethodList = []
        for (let item of this.disinfectionMethodList0) {
          if (idsArr.indexOf(item.name) >= 0) {
            continue
          }
          disinfectionMethodList.push(item)
        }
        this.disinfectionMethodList = disinfectionMethodList

        console.log('====this.disinfectionMethodList0', this.disinfectionMethodList0)
        console.log('====this.disinfectionMethodList', this.disinfectionMethodList)
      })
    },
    addXdx(row) {
      this.xdxDlgShow = true
      this.rowId = row.id
      this.getInfoXdx(row.id)
    },
    cloXdxDlg() {
      this.cardList = []
      this.attributeList = []
      this.getDisinfectionMethod()
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: '';
}
.clearfix:after {
  clear: both;
}

.box-card {
  width: 480px;
}
/deep/.el-card__body {
  padding: 0;
}
/deep/.el-card__header {
  line-height: 0px;
}
.item {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}
.popover {
  position: absolute;
  top: -10px;
  right: 0px;
}
.popover2 {
  position: absolute;
  top: 0px;
  right: 20px;
}

// /deep/.el-popover{
//   min-width: 100px !important;
//   padding: 0 !important;
// }
</style>

<style>
/* #popovers  */
/* .el-popover{
  min-width: 100px !important;
  padding: 0 !important;
} */
</style>


