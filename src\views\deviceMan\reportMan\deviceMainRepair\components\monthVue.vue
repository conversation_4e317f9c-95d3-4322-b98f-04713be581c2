<template>
  <div>
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-select
              v-model="searchForm.equPosition"
              placeholder="请选择设备所在位置"
              clearable
              filterable
              class="fl"
              style="width: 220px"
            >
              <el-option
                v-for="(item, index) in equPositionList"
                :key="index"
                :label="item.equPosition"
                :value="item.equPosition"
              ></el-option>
            </el-select>
          </div>
          <div class="search-item">
            <el-date-picker
              v-model="timeRange"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="fl"
              style="width: 220px"
            >
            </el-date-picker>
            <el-button
              type="success"
              icon="el-icon-search"
              @click="searchFunc()"
              class="fl ml10"
              >查询</el-button
            >
            <el-button
              icon="el-icon-download"
              type="primary"
              size="mini"
              @click="exportExcel"
              >导出</el-button
            >
            <el-popover
              placement="bottom"
              width="800"
              trigger="click"
              v-model="popShow"
            >
              <div class="m-page-con">
                <el-table
                  height="400px"
                  class="m-small-table"
                  border
                  fit
                  highlight-current-row
                  :data="exportList"
                >
                  <el-table-column
                    label="#"
                    type="index"
                    align="center"
                    width="60"
                  >
                  </el-table-column>
                  <el-table-column label="备注" align="center" width="auto">
                    <template slot-scope="scope">
                      <span>{{ scope.row.remark }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="创建时间" align="center" width="auto">
                    <template slot-scope="scope">
                      <span>{{ scope.row.createTime }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    width="110"
                    align="center"
                    class-name="small-padding fixed-width"
                  >
                    <template slot-scope="scope">
                      <el-button
                        v-if="scope.row.fileUrl"
                        @click="downloadItem(scope.row.fileUrl)"
                        type="primary"
                        size="mini"
                        icon="el-icon-download"
                        plain
                      >
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="page-container">
                <pagination
                  :total="totalExport"
                  :page.sync="listQueryExport.pageNo"
                  :limit.sync="listQueryExport.pageSize"
                  @pagination="getExportList"
                />
              </div>
              <el-button
                style="margin-left: 9px"
                slot="reference"
                icon="el-icon-document"
                type="primary"
                size="mini"
                @click.stop="viewItem"
                >查看导出</el-button
              >
            </el-popover>
          </div>
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      ref="tableBar"
      height="calc((100vh - 290px)/2 - 30px)"
      class="m-small-table"
      v-loading="listLoading"
      border
      fit
      highlight-current-row
      style="width: 100%; height: auto"
    >
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (searchForm.pageNo - 1) * searchForm.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equPosition"
        label="设备所在位置"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="planMaintainCount"
        label="计划保养次数"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="maintainCount"
        label="实际保养次数"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="m-a" @click="showDlg(scope.row, '实际保养次数')">{{
            scope.row.maintainCount
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="repairCount"
        label="关联维修次数"
        width="auto"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="m-a" @click="showDlg(scope.row, '关联维修次数')">{{
            scope.row.repairCount
          }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      class="mt10"
      :total="total"
      :page.sync="searchForm.pageNo"
      :limit.sync="searchForm.pageSize"
      @pagination="getList"
    />
    <div class="clear"></div>

    <div style="height: calc((100vh - 290px) / 2)" ref="barChart"></div>
    <el-dialog
      :title="'导出备注'"
      :close-on-click-modal="false"
      :visible.sync="dlgShow"
      top="30px"
      :width="'600px'"
      append-to-body
    >
      <el-form
        ref="dlgForm"
        :rules="dlgRules"
        :model="dlgData"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="导出备注" prop="remark">
          <el-input
            :autosize="{ minRows: 5, maxRows: 10 }"
            v-model="dlgData.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon="el-icon-back">返回</el-button>
        <el-button @click="subDlg" type="success" icon="el-icon-check"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      width="1500px"
      :visible.sync="dialogVisible"
      :title="`${equPosition}   ${type}  `"
      :close-on-click-modal="false"
    >
      <basic-table
        ref="basicTable"
        class="homeTable"
        :TableHeaderList="TableHeaderList"
        :tableData="basicTableData"
        :searchForm="basicTableSearchForm"
        :seriesName="type"
        :total="basicTotal"
        :tableHeight="600"
        :rowHeight="30"
        @getWeiXiuTableData="getWeiXiuTableData"
        @getBaoYangTableData="getBaoYangTableData"
      ></basic-table>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { postAction, getAction, deleteAction } from "@/api";
import basicTable from "../../basicledgerMain/basicTable.vue"; // 公用table组件
import { getDefaultDateRange } from "@/utils";

import * as echarts from "echarts";
import {
  equMaintenanceStatistic,
  equMaintenanceStatisticExport,
  pageGreenExportDownloadPage,
  delGreenExportDownload,
} from "@/api/deviceMan/reportMan.js";
import Pagination from "@/components/Pagination"; // 分页
let dlgDataEmpty = {
  id: 0,
  remark: "",
};
export default {
  components: {
    Pagination,
    basicTable,
  },
  data() {
    return {
      timeRange: undefined,
      basicTableSearchForm: {
        pageNo: 1,
        pageSize: 20,
      },
      basicTotal: 0,
      TableHeaderList: [], //table表头数据
      basicTableData: [], //table数据
      dialogVisible: false,
      type: "",
      equPosition: "",
      barChartInstance: null,
      time: "",
      dlgBtnLoading: false,
      dlgShow: false,
      // 表单验证
      dlgRules: {
        remark: [{ required: true, message: "必填字段", trigger: "blur" }],
      },
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)), // 弹窗值
      popShow: false,
      exportList: [],
      listQueryExport: {
        pageNo: 1,
        pageSize: 10,
        type: "TABLE_SBBY",
      },
      totalExport: 0,
      searchForm: {
        statisticStartTime: "",
        statisticEndTime: "",
        projectId: JSON.parse(window.localStorage.userInfo).projectId,
        equPosition: "",
        pageNo: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [],
      listLoading: false,
      equPositionList: [],
    };
  },
  created() {},
  mounted() {
    this.getEquPositionList(JSON.parse(window.localStorage.userInfo).projectId);
    this.timeRange = getDefaultDateRange();
  },
  methods: {
    async showDlg(row, type) {
      this.equPosition = row.equPosition;
      this.type = type;
      this.basicTableSearchForm = {
        pageNo: 1,
        pageSize: 20,
      };
      if (type === "实际保养次数") {
        await this.getBaoYangTableData();
      } else if (type === "关联维修次数") {
        await this.getWeiXiuTableData();
      }
    },
    getTimeRangeForApi() {
      return this.timeRange ? [this.timeRange[0], this.timeRange[1]] : ["", ""];
    },
    async fetchMaintenanceInvokeData(pageNo, pageSize) {
      const [statisticStartTime, statisticEndTime] = this.getTimeRangeForApi();

      const params = {
        projectId: this.searchForm.projectId,
        equPosition: this.equPosition,
        pageNo,
        pageSize,
        statisticStartTime,
        statisticEndTime,
      };

      const res = await getAction(
        "sa/green/equipment-manage/maintenance-statistic/maintain-item",
        params
      );
      return res.data; // 直接返回接口返回的数据
    },

    async getBaoYangTableData(query = { pageNo: 1, pageSize: 10 }) {
      this.TableHeaderList = [
        {
          FieldDisplayName: "设备所在位置",
          Field: "equPosition",
        },
        {
          FieldDisplayName: "设备名称",
          Field: "equName",
        },
        {
          FieldDisplayName: "保养内容",
          Field: "item",
        },
        {
          FieldDisplayName: "实际保养次数",
          Field: "count",
        },
      ];
      // 调用接口方法获取数据
      const { code, data, msg } = await this.fetchMaintenanceInvokeData(
        query.pageNo,
        query.pageSize
      );

      if (code === "200") {
        if (!data.list) {
          this.$message.error("暂无数据");
          return;
        }
        let list = data.list;
        console.log(list, "list");

        this.basicTableData = list || []; // 使用空数组作为默认值
        this.basicTotal = data.total || 0;
        this.dialogVisible = true;
      } else {
        this.$message.error(msg || "接口返回错误"); // 如果msg不存在，则显示默认错误信息
      }
    },
    async fetchRepairManageData(pageNo, pageSize) {
      const [statisticStartTime, statisticEndTime] = this.getTimeRangeForApi();

      const params = {
        projectId: this.searchForm.projectId,
        equPosition: this.equPosition,
        pageNo,
        pageSize,
        statisticStartTime,
        statisticEndTime,
      };

      const res = await getAction(
        "sa/green/equipment-manage/maintenance-statistic/repair-item",
        params
      );

      return res.data; // 直接返回接口返回的数据
    },

    async getWeiXiuTableData(query = { pageNo: 1, pageSize: 10 }) {
      this.TableHeaderList = [
        {
          FieldDisplayName: "设备所在位置",
          Field: "equPosition",
        },
        {
          FieldDisplayName: "设备名称",
          Field: "equName",
        },
        {
          FieldDisplayName: "维修内容",
          Field: "item",
        },
        {
          FieldDisplayName: "实际维修次数",
          Field: "count",
        },
      ];
      // 调用接口方法获取数据
      const { code, data, msg } = await this.fetchRepairManageData(
        query.pageNo,
        query.pageSize
      );

      if (code === "200") {
        if (!data.list) {
          this.$message.error("暂无数据");
          return;
        }
        this.basicTableData = data.list || []; // 使用空数组作为默认值
        this.basicTotal = data.total || 0;
        this.dialogVisible = true;
      } else {
        this.$message.error(msg || "接口返回错误"); // 如果msg不存在，则显示默认错误信息
      }
    },
    async getEquPositionList(val) {
      try {
        const res = await getAction(
          `sa/green/equ/equ-position/page?projectId=${val}&equPosition=&pageNo=1&pageSize=1000`
        );
        const { code, data } = res.data;
        if (code === "200") {
          this.equPositionList = data.list || [];
        } else {
          this.$message.error(res.data.msg);
        }
      } catch (error) {
        this.$message.error("获取数据失败");
      }
    },
    // 查看
    viewItem() {
      this.popShow = !this.popShow;
      if (this.popShow) {
        this.getExportList();
      }
    },
    // 获取导出记录列表
    async getExportList() {
      try {
        const res = await pageGreenExportDownloadPage(this.listQueryExport);
        const { data, code } = res.data;
        if (code === "200") {
          this.totalExport = data.total;
          this.exportList = data.list || [];
        } else {
          this.$message.error("获取导出列表失败：" + (data.msg || "未知错误"));
        }
      } catch (error) {
        console.error("请求导出列表时发生错误：", error);
        this.$message.error("请求导出列表时发生错误，请稍后再试。");
      }
    },
    // 下载
    downloadItem(url) {
      window.open(url, "_blank");
    },
    // 删除
    delItem1(data) {
      this.$confirm("此操作将删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delGreenExportDownload(data.id).then((res) => {
          if (res.data.code === "200") {
            this.$message.success(res.data.msg);
            this.getExportList();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    exportExcel() {
      if (!this.validateForm()) {
        return;
      }
      this.popShow = false;
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty));
      this.dlgShow = true;
      this.$nextTick(() => {
        this.$refs.dlgForm.clearValidate();
      });
    },
    async subDlg() {
      if (this.dlgData.remark === "") {
        this.$message.warning("请输入导出备注");
        return;
      }
      let loading = this.$loading({
        lock: true,
        text: "导出中,请稍候",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let postData = JSON.parse(JSON.stringify(this.searchForm));
      if (this.timeRange) {
        postData.statisticStartTime = this.timeRange[0];
        postData.statisticEndTime = this.timeRange[1];
      }
      postData.remark = this.dlgData.remark;
      const res1 = await equMaintenanceStatisticExport(postData);
      this.dlgBtnLoading = false;
      loading.close();
      let res = res1.data;
      if (res.code === "200") {
        this.$message.success("操作成功，请在我的导出中下载");
        this.dlgShow = false;
      } else {
        this.$message.error(res.msg);
      }
    },
    async searchFunc() {
      if (!this.validateForm()) {
        return;
      }

      this.searchForm.pageNo = 1;
      await this.getList();
    },

    validateForm() {
      const { projectId } = this.searchForm;
      const errorMessages = [];

      if (!projectId) {
        errorMessages.push("请选择项目");
      }

      if (errorMessages.length) {
        this.$message.warning(errorMessages.join("；"));
        return false;
      }

      return true;
    },
    async getList() {
      this.listLoading = true; // 开始加载数据，设置加载状态为true
      let postData = JSON.parse(JSON.stringify(this.searchForm));
      if (this.timeRange) {
        postData.statisticStartTime = this.timeRange[0];
        postData.statisticEndTime = this.timeRange[1];
      }
      try {
        const res = await equMaintenanceStatistic(postData);
        let { code, data } = res.data;
        if (code === "200") {
          this.tableData = data.list || [];
          this.total = data.total || 0;

          if (this.tableData.length > 0) {
            this.barChartInit(); // 只有当数据非空时才初始化图表
          } else {
            this.barChartInstance && this.barChartInstance.dispose(); // 销毁图表
            this.barChartInstance = null;
          }
        } else {
          this.$message.error(data.msg);
        }
      } catch (error) {
        console.log(error);

        this.$message.error("获取数据失败");
      } finally {
        this.listLoading = false; // 无论成功或失败，最后都设置加载状态为false
      }
    },

    barChartResize() {
      if (this.barChartInstance) {
        this.barChartInstance.resize(); // 调整图表大小
      }
    },
    barChartInit() {
      if (!this.barChartInstance) {
        this.barChartInstance = echarts.init(this.$refs.barChart);
        window.addEventListener("resize", this.barChartResize); // 添加响应式调整大小的监听
      }
      const barCommonConfig = {
        label: {
          show: true,
          position: "top",
          color: "#666",
          fontSize: 12,
        },
        barWidth: 20,
        showBackground: true,
      };

      const option = {
        title: {
          top: 15,
          left: 15,
          text: "设备保养记录",
          textStyle: {
            color: "#262626",
            fontWeight: 500,
            fontSize: 18,
          },
        },
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            console.log(params);
            if (params.seriesName === "计划保养次数") {
              return (
                params.name +
                "<br/>" +
                params.seriesName +
                ": " +
                params.value.planMaintainCount +
                "次"
              );
            }
            if (params.seriesName === "实际保养次数") {
              return (
                params.name +
                "<br/>" +
                params.seriesName +
                ": " +
                params.value.maintainCount +
                "次"
              );
            }
            if (params.seriesName === "关联维修次数") {
              return (
                params.name +
                "<br/>" +
                params.seriesName +
                ": " +
                params.value.repairCount +
                "次"
              );
            }
          },
        },
        legend: {
          data: ["计划保养次数", "实际保养次数", "关联维修次数"], // 系列名称列表，与 series 中的 name 对应
          top: 20, // legend 组件离容器顶部的距离
          left: "center", // legend 组件在容器中的水平位置，可以是 'left'、'center' 或 'right'，也可以是具体的像素值
          textStyle: {
            // 图例文字的样式
            color: "#333",
            fontSize: 14,
          },
          itemGap: 10, // 每个图例项之间的间隔
          itemWidth: 25, // 图例标记的图形宽度
          itemHeight: 14, // 图例标记的图形高度
        },
        xAxis: {
          type: "category",
          axisLabel: {
            color: "#666",
            interval: 0,
          },
          axisTick: {
            show: false, //x轴刻度线
          },
          axisLine: {
            //X轴线
            show: true,
            lineStyle: {
              color: "#E0E0E0",
            },
          },
        },
        yAxis: {
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#666",
          },
          splitLine: {
            lineStyle: {
              color: "#fff", // 虚线颜色
            },
          }, //去除背景网格线
        },
        grid: {
          top: "80px",
          left: "50px",
          right: "24px",
          bottom: "36px",
        },
        dataset: {
          dimensions: [
            "equPosition",
            "planMaintainCount",
            "maintainCount",
            "repairCount",
          ],
          // 使用函数确保在获取数据时tableData已经被正确赋值
          source: this.tableData,
        },
        series: [
          {
            ...barCommonConfig,
            type: "bar",
            name: "计划保养次数",
            itemStyle: { color: "#FFBC00" },
          },
          {
            ...barCommonConfig,
            type: "bar",
            name: "实际保养次数",
            itemStyle: { color: "#FF4E00" },
          },
          {
            ...barCommonConfig,
            type: "bar",
            name: "关联维修次数",
            itemStyle: { color: "#5BF9C7" },
          },
        ],
        //滚动条
        dataZoom: [
          {
            show: this.tableData.length > 10 ? true : false,
            height: 12,
            bottom: 0,
            startValue: 0, //起始值
            endValue: 9, //结束值
            showDetail: false,
            fillerColor: "rgba(1, 132, 213, 0.4)", // 滚动条颜色
            borderColor: "rgba(17, 100, 210, 0.12)",
            backgroundColor: "rgba(221, 220, 107, .1)", //两边未选中的滑动条区域的颜色
            handleSize: 1, //两边手柄尺寸
            showDetail: false, //拖拽时是否展示滚动条两侧的文字
            zoomLock: true, //是否只平移不缩放
            moveOnMouseMove: false, //鼠标移动能触发数据窗口平移
            // minValueSpan: 5,  // 放大到最少几个
            // maxValueSpan: 5,  //  缩小到最多几个
          },
          {
            type: "inside", // 支持内部鼠标滚动平移
            start: 0,
            // end: 20,
            startValue: 0, // 从头开始。
            endValue: 10, // 最多5个
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
          },
        ],
      };

      this.barChartInstance.setOption(option);
      this.barChartInstance.off("click");
      this.barChartInstance.on("click", async (param) => {
        console.log(param, "点击");
        this.equPosition = param.name;
        this.type = param.seriesName;
        this.basicTableSearchForm = {
          pageNo: 1,
          pageSize: 20,
        };
        if (param.seriesName === "实际保养次数") {
          await this.getBaoYangTableData();
        } else if (param.seriesName === "关联维修次数") {
          await this.getWeiXiuTableData();
        }
        // 点击弹窗方法
      });
    },
  },
  // 在组件销毁时移除监听器
  beforeDestroy() {
    window.removeEventListener("resize", this.barChartResize);
    // 如果需要，也可以销毁 echarts 实例
    if (this.barChartInstance) {
      this.barChartInstance.dispose();
    }
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped></style>