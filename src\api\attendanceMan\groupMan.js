import request from '@/utils/request'

// 考勤组分页查询
export function findGroupByDynamic(data) {
  return request({
    url: '/workade/findGroupByDynamic',
    method: 'post',
    data
  })
}

// 新建考勤组
export function saveAdeGroup(data) {
  return request({
    url: '/workade/saveAdeGroup',
    method: 'post',
    data
  })
}

// 删除考勤组
export function delgroupById(id) {
  return request({
    url: `/workade/delgroupById/${id}`,
    method: 'get'
  })
}