/** 被服管理 **/

import Layout from "@/views/layout/Layout";

const clothManRouter = {
  path: "/clothMan",
  component: Layout,
  name: "clothMan",
  meta: {
    title: "被服管理",
    icon: "bfgl",
    roles: ["beifuguan<PERSON>"]
  },
  children: [
    {
      path: "statisticalReports",
      component: () => import("@/views/clothingMan/setupMaintain/index"),
      meta: {
        title: "统计报表",
        roles: ["tongjibaobiao_web"]
      },
      alwaysShow: true,
      children: [
        {
          path: "decontaminationCenter",
          component: () => import("@/views/clothingMan/setupMaintain/index"),
          meta: {
            title: "洗消中心报表",
            roles: ["xixiaozhongxinbaobiao_web"]
          },
          alwaysShow: true,
          children: [
            // src\views\clothingMan\statisticalReports\decontaminationCenter\decontaminationReceptionInquire\index.vue
            {
              path: "decontaminationReceptionInquire",
              // import("@/views/clothingMan/setupMaintain/clothingArchives"),
              component: () =>
                import(
                  "@/views/clothingMan/statisticalReports/decontaminationCenter/decontaminationReceptionInquire/index"
                ),
              name: "洗消接收单查询",
              meta: {
                title: "洗消接收单查询",
                roles: ["xixiaojieshoudanchaxun_web"]
              },
              children: []
            },
            {
              path: "decontaminationHandoverInquire",
              component: () =>
                import(
                  "@/views/clothingMan/statisticalReports/decontaminationCenter/decontaminationHandoverInquire/index"
                ),
              name: "洗消交接单查询",
              meta: {
                title: "洗消交接单查询",
                roles: ["xixiaojiaojiedanchaxun_web"]
              },
              children: []
            }
          ]
        }
      ]
    }

    // {
    //   path: "setupMaintain",
    //   component: () => import("@/views/clothingMan/setupMaintain/index"),
    //   meta: {
    //     title: "设置维护",
    //     roles: ["beifuguanli"]
    //   },
    //   children: [
    //     {
    //       path: "clothingArchives",
    //       component: () =>
    //         import("@/views/clothingMan/setupMaintain/clothingArchives"),
    //       name: "被服档案",
    //       meta: {
    //         title: "被服档案",
    //         roles: ["beifudangan"]
    //       },
    //       children: []
    //     },
    //     {
    //       path: "storageMan",
    //       component: () =>
    //         import("@/views/clothingMan/setupMaintain/storageMan"),
    //       name: "库房管理",
    //       meta: {
    //         title: "库房管理",
    //         roles: ["kufangguanli"]
    //       },
    //       children: []
    //     },
    //     {
    //       path: "wardMan",
    //       component: () => import("@/views/clothingMan/setupMaintain/wardMan"),
    //       name: "病房管理",
    //       meta: {
    //         title: "病房管理",
    //         roles: ["bingfangguanli"]
    //       },
    //       children: []
    //     },
    //     {
    //       path: "sickBedMan",
    //       component: () =>
    //         import("@/views/clothingMan/setupMaintain/sickBedMan"),
    //       name: "病床管理",
    //       meta: {
    //         title: "病床管理",
    //         roles: ["bingchuangguanli"]
    //       },
    //       children: []
    //     }
    //   ]
    // },
    // {
    //   path: "inventoryMan",
    //   component: () => import("@/views/clothingMan/inventoryMan"),
    //   name: "库存管理",
    //   meta: {
    //     title: "库存管理",
    //     roles: ["kucunguanli"]
    //   },
    //   children: []
    // },
    // {
    //   path: "checkMan",
    //   component: () => import("@/views/clothingMan/checkMan"),
    //   name: "盘点管理",
    //   meta: {
    //     title: "盘点管理",
    //     roles: ["pandianguanli"]
    //   },
    //   children: []
    // },
    // {
    //   path: "scrapMan",
    //   component: () => import("@/views/clothingMan/scrapMan"),
    //   name: "报废管理",
    //   meta: {
    //     title: "报废管理",
    //     roles: ["baofeiguanli"]
    //   },
    //   children: []
    // },
    // {
    //   path: "lossMan",
    //   component: () => import("@/views/clothingMan/lossMan"),
    //   name: "丢失管理",
    //   meta: {
    //     title: "丢失管理",
    //     roles: ["diushiguanli"]
    //   },
    //   children: []
    // },
    // {
    //   path: "clothingClean",
    //   component: () => import("@/views/clothingMan/clothingClean"),
    //   name: "被服洗消",
    //   meta: {
    //     title: "被服洗消",
    //     roles: ["beifuxixiao"]
    //   },
    //   children: []
    // }
  ]
};

export default clothManRouter;
