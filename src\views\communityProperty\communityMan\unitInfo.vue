<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在楼栋">
          <el-select v-model="listQuery.floorId" filterable clearable placeholder="请选择楼栋">
            <el-option v-for="item in buildingList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入单元名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>新增单元</el-button>
        <el-button icon="el-icon-download" size="mini" type="primary" @click="exportExcel">
          Excel导出
        </el-button>
        <!-- <el-upload class="upload-wrap" action="" :before-upload="uploadItem">
          <el-button icon="el-icon-upload" size="mini" type="primary">
            Excel导入
          </el-button>
        </el-upload>
        <el-button icon="el-icon-download" type="primary" size="mini" @click="downloadItem()">
          模板下载
        </el-button> -->
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="楼栋名称">
          <template slot-scope="scope">
            <span>{{ scope.row.floorName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="楼栋编号">
          <template slot-scope="scope">
            <span>{{ scope.row.floorNum }}</span>
          </template>
        </el-table-column>

        <el-table-column label="单元名称">
          <template slot-scope="scope">
            <span>{{ scope.row.unitName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="单元编号">
          <template slot-scope="scope">
            <span>{{ scope.row.unitNum }}</span>
          </template>
        </el-table-column>

        <el-table-column label="总层数">
          <template slot-scope="scope">
            <span>{{ scope.row.layerCount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="建筑面积">
          <template slot-scope="scope">
            <span>{{ scope.row.unitArea }}</span>
          </template>
        </el-table-column>

        <el-table-column label="是否有电梯">
          <template slot-scope="scope">
            <span>{{ scope.row.lift == 1010 ? '是' : '否' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="320" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-plus" plain @click="addRoom(scope.row)">添加房屋</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑单元信息" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="所在小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在楼栋" prop="floorId">
          <el-select v-model="dlgData.floorId" filterable clearable placeholder="请选择楼栋">
            <el-option v-for="item in buildingListDlg" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单元名称" prop="unitName">
          <el-input v-model="dlgData.unitName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="单元地址" prop="unitAddr">
          <el-input v-model="dlgData.unitAddr" placeholder="请输入单元地址" />
        </el-form-item>

        <el-form-item label="单元编号" prop="unitNum">
          <el-input v-model="dlgData.unitNum" placeholder="请输入编号" />
        </el-form-item>

        <el-form-item label="总层数" prop="layerCount">
          <el-input-number v-model="dlgData.layerCount" :controls='false' :min="0" :precision="0" :step="1"></el-input-number>
        </el-form-item>

        <el-form-item label="建筑面积">
          <el-input-number v-model="dlgData.unitArea" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
        </el-form-item>

        <el-form-item label="是否有电梯">
          <el-radio-group v-model="dlgData.lift">
            <el-radio label="1010">是</el-radio>
            <el-radio label="2020">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注">
          <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.remark" placeholder="请输入备注" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, cofloorCommunity, buildingunitPage, buildingunitAddOrUpdate, buildingunitDisable, importUnit } from '@/api/communityMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  communityId: '',
  communityName: '',
  floorId: '',
  floorName: '',
  lift: '1010',
  unitArea: '',
  unitName: '',
  unitNum: '',
  remark: '',
  layerCount: ''
}


export default {
  name: 'buildInfo',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        unitName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        unitNum: [{ required: true, message: '必填字段', trigger: 'blur' }],
        layerCount: [{ required: true, message: '必填字段', trigger: 'blur' }],
        floorId: [{ required: true, message: '必填字段', trigger: 'change' }],
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        floorId: ''
      },
      communityList: [],
      buildingList: [],
      buildingListDlg: [],
      userInfo: {}
    }
  },
  computed: {

  },
  watch: {

  },
  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
    if (this.$route.query.communityId && this.$route.query.floorId) {
      this.listQuery.communityId = parseInt(this.$route.query.communityId)
      this.listQuery.floorId = parseInt(this.$route.query.floorId)
      cofloorCommunity(this.listQuery.communityId).then(res => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data
        }
      })
      this.addItem()
    }
  },

  methods: {
    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/unity/report/exportUnit?` + param
      window.open(sendUrl)
    },

    // 下载
    downloadItem () {
      let url =
        'https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E5%AF%BC%E5%85%A5%E5%8D%95%E5%85%83%E6%A8%A1%E6%9D%BF.xlsx'
      window.open(url)
    },

    // 上传
    uploadItem (file) {
      let name = file.name.split('.')
      let suffix = name[name.length - 1]

      if (suffix !== 'xls' && suffix !== 'xlsx') {
        this.$message({
          type: 'warning',
          message: '只能上传xls/xlsx文件'
        })
        return false
      }

      let loading = this.$loading({
        lock: true,
        text: '导入中',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      let postParam = {
        file
      }

      importUnit(postParam).then(res => {
        loading.close()
        if (res.data.code == 200) {
          this.$message.success('导入成功')
          this.getList()
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })

      return false
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    communityChange () {
      let communityId = this.listQuery.communityId
      if (this.dlgShow) {
        communityId = this.dlgData.communityId
        this.dlgData.floorId = ""
      } else {
        this.listQuery.floorId = ""
      }
      this.getBuildingList(communityId)
    },

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取楼栋列表
    getBuildingList (id) {
      if (utils.isNull(id)) {
        return
      }
      cofloorCommunity(id).then(res => {
        if (res.data.code == 200) {
          if (this.dlgShow) {
            this.buildingListDlg = res.data.data
          } else {
            this.buildingList = res.data.data
          }

        }
      })
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      buildingunitPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 显示弹窗
    addItem () {
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgData.floorId = this.listQuery.floorId
      this.getBuildingList(this.dlgData.communityId)
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          postParam.floorName = utils.getNameById(postParam.floorId, this.buildingListDlg)
          postParam.floorNum = utils.getNameById(postParam.floorId, this.buildingListDlg, 'id', 'floorNum')
          this.dlgLoading = true
          buildingunitAddOrUpdate(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 添加房屋
    addRoom (data) {
      this.$router.push({ path: '/communityProperty/communityMan/roomInfo', query: { communityId: data.communityId, floorId: data.floorId, unitId: data.id } });
    },

    // 编辑
    editItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(data))
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
      this.getBuildingList(this.dlgData.communityId)
    },

    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        buildingunitDisable(data.id, flag).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


