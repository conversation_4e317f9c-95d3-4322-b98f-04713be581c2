// 多选部门dlg组件

const branchDlgMul = {
  namespaced: true,

  state: {
    dlgShow: false,

    permission: true,

    branchIds: '',

    branchNames: '',
  },

  getters: {
    dlgShow: state => state.dlgShow,

    permission: state => state.permission,

    branchIds: state => state.branchIds,

    branchNames: state => state.branchNames
  },

  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val
    },

    SET_PERMISSION: (state, val) => {
      state.permission = val
    },

    SET_BRANCHIDS: (state, val) => {
      state.branchIds = val
    },

    SET_BRANCHNAMES: (state, val) => {
      state.branchNames = val
    }
  },

  actions: {

  }
}

export default branchDlgMul
