import request from "@/utils/request";

import { objToParam } from "@/utils/index";
// 部门移动

export function updateBranchByOrderNum(data) {
  return request({
    url: `/sys/updateBranchByOrderNum`,
    method: "post",
    data
  });
}
// [[ 用户管理
// 获取列表
export function findSysUserAll({ page, size }) {
  return request({
    url: `/sys/findSysUserAll/${page}/${size}`,
    method: "get"
  });
}
// 添加用户
export function saveSysUser(data) {
  return request({
    url: `/sys/saveSysUser`,
    method: "post",
    data
  });
}
// 修改用户
export function upDateSysUser(data) {
  return request({
    url: `/sys/upDateSysUser`,
    method: "post",
    data
  });
}

// 删除用户
export function delSysUser({ id }) {
  return request({
    url: `/sys/delSysUser/${id}`,
    method: "get"
  });
}
// ]] 用户管理

// 获取部门岗位树 (按权限)
export function findTreeAndPost() {
  return request({
    url: `/org/findTreeAndPost`,
    method: "get"
  });
}

// [[ 数据字典
// 数据字典
export function findDdGroupAll({ page, size }) {
  return request({
    url: `/msg/findDdGroupAll/${page}/${size}`,
    method: "get"
  });
}

export function findDdGroupByStr(data) {
  return request({
    url: `/msg/findDdGroupByStr`,
    method: "post",
    data
  });
}

// 新增数据字典
export function saveDdgroup(data) {
  return request({
    url: `/msg/saveDdgroup`,
    method: "post",
    data
  });
}

// 删除数据字典
export function delDdGroup({ id }) {
  return request({
    url: `/msg/delDdGroup/${id}`,
    method: "get"
  });
}
// 修改数据字典
export function upDateDdgroup(data) {
  return request({
    url: `/msg/upDateDdgroup`,
    method: "post",
    data
  });
}

// item 表
// 根据 id 获取列表
export function findDdItme({ groupChar, page, size }) {
  return request({
    url: `/msg/findDdItme/${groupChar}/${page}/${size}`,
    method: "get"
  });
}
export function findDdItmeERP({ groupChar, page, size }) {
  return request({
    url: `/erpmsg/findDdItme/${groupChar}/${page}/${size}`,
    method: "get"
  });
}

export function findDdItmeMap(projectId, groupChar) {
  return request({
    url: `/msg/findDdItmeMap/${projectId}/${groupChar}`,
    method: "get"
  });
}

// 获取其他平台数据字典
export function findDdItmeOther({ groupChar, page, size }) {
  return request({
    url: `/msg/findDdItmen/${groupChar}/${page}/${size}`,
    method: "get"
  });
}
// 根据 chars 获取列表
export function findDdItmeByChars(data) {
  return request({
    url: `/msg/findDdItmeByChars`,
    method: "post",
    data
  });
}

// 新增
export function saveOneGroupItem(data) {
  return request({
    url: "/msg/saveOneGroupItem",
    method: "post",
    data
  });
}
// 修改
export function upDateOneGroupItem(data) {
  return request({
    url: "/msg/upDateOneGroupItem",
    method: "post",
    data
  });
}
// 删除
export function delDdGroupItem({ id }) {
  return request({
    url: `/msg/delDdGroupItem/${id}`,
    method: "get"
  });
}

// ]] 数据字典
// [[ 部门相关
// 根据权限 获取部门树
export function findOrgBranchAll() {
  return request({
    url: `/sys/findTree`,
    method: "post"
  });
}



// 获取通知公告 部门树
export function findOrgBranchAll2() {
  return request({
    url: `/sys/department/findTree`,
    method: "post"
  });
}

// 获取全部的
export function findTreeByFrom() {
  return request({
    url: `/sys/findTreeByFrom`,
    method: "get"
  });
}
// 新增部门
export function saveOrgBranch(data) {
  return request({
    url: `/sys/saveOrgBranch`,
    method: "post",
    data
  });
}
// 删除部门
export function delOrgBranch(data) {
  return request({
    url: `/sys/delOrgBranch`,
    method: "post",
    data
  });
}
// 修改部门
export function upDateOrgBranch(data) {
  return request({
    url: `/sys/upDateOrgBranch`,
    method: "post",
    data
  });
}
// ]] 部门相关

// 【【 核算部门相关
// 获取部门数
// export function findAccountBranchTree() {
//   return request({
//     url: `/sys/findAccountBranchTree`,
//     method: 'get'
//   })
// }

// 新增 核算部门
export function saveAccountBranch(data) {
  return request({
    url: `/sys/saveAccountBranch`,
    method: "post",
    data
  });
}

// 修改 核算部门
export function upDateAccountBranch(data) {
  return request({
    url: `/sys/upDateAccountBranch`,
    method: "post",
    data
  });
}

// 删除 核算部门
export function delAccountBranch(id) {
  return request({
    url: `/sys/delAccountBranch/${id}`,
    method: "get"
  });
}

// 查询气泡
export function findUntreatedNum() {
  return request({
    url: `/workade/findUntreatedNum`,
    method: "get"
  });
}

export function getGreenMaopao(parameter) {
  let paramStr = objToParam(parameter)

  return request({
    url: `/green/curing/task-execution/getProcessNum` + paramStr,
    method: "get",
    
  });
}
