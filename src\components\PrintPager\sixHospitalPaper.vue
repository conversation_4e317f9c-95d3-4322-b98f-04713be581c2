<template>
  <div class="app-container print">
    <!-- height:${paperHeight}px; -->
    <!-- page-break-before:always; -->
    <div
      v-for="(dlgData, index) of dlgDataList"
      :key="index"
      ref="paperContainer"
      :style="`width:${paperWidth}px;${index !== 0 ? 'page-break-before:always' : ''}`"
      class="paper-container clearfix"
      style="margin-top: -6px"
    >
      <div class="tac text-black fs18 text-bold">哈尔滨医利科大学附属第六医院</div>
      <div class="tac text-black fs18 text-bold mt10">新冠肺炎流行病学史调查、告知及承诺书</div>
      <div class="mt10 taj text-black text-bold" style="line-height: 1.2; text-indent: 2em">
        为做好新型冠状病毒感染肺炎防控工作，根据《中华人民共和国传染病防治法》，请如实
        回答下述问题，如有故意隐瞒，当事人需承担相应法律责任，感谢您的配合！
      </div>
      <!-- 一 -->
      <div class="flexc page-title0 mt10">1、您的基本信息</div>
      <div>
        <div class="flexc lhbar">
          <div class="jbxxBox">
            <span class="page-title">姓名：{{ dlgData.name }}</span>
            <span class="page-title">身份证号：{{ dlgData.idNumber }}</span>
          </div>
          <div class="jbxxBox" style="margin-right: 300px">
            <span class="page-title">性别：{{ dlgData.sex }}</span>
            <span class="page-title">手机号码: {{ dlgData.phone }}</span>
          </div>
        </div>
        <div class="">现住址：{{ dlgData.address }}</div>

        <div class="flexlc lhbar page-title">您是否为以下特定职业人群（如是以下人群，请就诊时提供相应检测频次的核酸证明）：</div>
        <div class="clearfix">
          <div class="mr20 fl flexlc" style="height: 16px" v-for="item of tdrqSelect" :key="item.id">
            <input class="" type="checkbox" :value="item.id" :checked="dlgData.specificProfession.indexOf(item.id) >= 0" />
            &nbsp;{{ item.name }}
          </div>
        </div>
        <div class="flexc page-title0 mt10">2、您来我院的目的：</div>
        <div class="flexlc lhbar">
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.sickType == '1'" />&nbsp;就诊
            <input class="ml10" type="radio" value="0" :checked="dlgData.sickType == '2'" />&nbsp;陪诊
          </div>
        </div>

        <div class="flexc page-title0 mt10">
          3、您在7天内是否曾有确诊病例或无症状感染者的接触史，或者是否曾到过境内有确诊病例或无症 状感染者报告的社区（旅行史、居住史）
        </div>
        <div class="flexlc lhbar">
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.sickType == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.sickType == '0'" />&nbsp;否
          </div>
        </div>
        <div class="flexc page-title0 mt10">
          4、您在7天内是否有境外疫情国家或地区的旅行史或居住史，或者是否接触过来自境外有疫情国家 的相关人员
        </div>
        <div class="flexlc lhbar">
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isContactPatient == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isContactPatient == '0'" />&nbsp;否
          </div>
        </div>
        <div class="flexc page-title0 mt10">5、您的主要症状：</div>
        <div class="clearfix">
          <div class="mr20 fl flexlc" style="height: 16px" v-for="item of zyzzSelect" :key="item.id">
            <input class="" type="checkbox" :value="item.id" :checked="dlgData.medicalSymptoms.indexOf(item.id) >= 0" />
            &nbsp;{{ item.name }}
          </div>
        </div>
        <div class="flexc page-title0 mt10">6、您在7天内是否服用退烧药物</div>
        <div class="flexlc lhbar">
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isEatFebrifuge == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isEatFebrifuge == '0'" />&nbsp;否
          </div>

          <!-- <div class="ml30 flexlc" v-if="dlgData.isEatFebrifuge == '1'">
            退烧药名称：
            <div class="m-input tac" style="min-width: 100px">{{ dlgData.isEatFebrifugeName }}</div>
          </div> -->
        </div>
        <!-- <div class="flexc page-title0 mt10">6、您在7天内是否服用退烧药物</div>
     <div class="flexlc lhbar">
          <div class="flexlc lhbar">
            <input
              type="radio"
              value="1"
              :checked="dlgData.isEatFebrifuge == '1'"
            />&nbsp;是
            <input
              class="ml10"
              type="radio"
              value="0"
              :checked="dlgData.isEatFebrifuge == '0'"
            />&nbsp;否
          </div>
        </div> -->
        <div class="flexc page-title0 mt10">7、患者去向：</div>
        <div class="clearfix">
          <div class="mr20 fl flexlc" style="height: 16px" v-for="item of hzqxSelect" :key="item.id">
            <input class="" type="checkbox" :value="item.id" :checked="dlgData.patientsGo.indexOf(item.id) >= 0" />
            &nbsp;{{ item.name }}
          </div>
        </div>
        <div class="flexc page-title0 mt10">8、您在7天内是否有医疗机构就诊史：</div>
        <div class="flexlc lhbar">
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isMedicalHistory == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isMedicalHistory == '0'" />&nbsp;否
          </div>

          <span v-if="dlgData.isMedicalHistory == '1'" class="ml20">就诊日期：</span>
          <div v-if="dlgData.isMedicalHistory == '1'" class="m-input tac" style="width: 80px">
            {{ dlgData.medicalHistoryDate }}
          </div>
          <span v-if="dlgData.isMedicalHistory == '1'" class="ml20">就诊医院：</span>
          <div v-if="dlgData.isMedicalHistory == '1'" class="m-input tac" style="min-width: 80px">
            {{ dlgData.medicalHistoryName }}
          </div>
        </div>
        <div class="flexc page-title0 mt10">9、如有陪同人员，仅限一名，务必如实填写以下信息，不填写者无法进入，陪同人是否有上述情况：</div>
        <div class="flexlc lhbar">
          <div class="flexlc lhbar">
            <input type="radio" value="1" :checked="dlgData.isAccompany == '1'" />&nbsp;是
            <input class="ml10" type="radio" value="0" :checked="dlgData.isAccompany == '0'" />&nbsp;否
          </div>
        </div>
        <div class="flexc lhbar" v-if="dlgData.isAccompany == '1'">
          <div class="flexlc" style="width: 23%">陪同人员姓名: {{ dlgData.accompanyUseraName }}</div>
          <div class="flexlc ml10" style="width: 33%; margin-right: 135px">陪同人身份证号：{{ dlgData.accompanyUseraIdnumber }}</div>
        </div>
      </div>
      <!-- <div
        class="mt10 taj text-black text-bold"
        style="line-height: 1.2; text-indent: 2em"
      >
        我本人承诺上述情况属实，依照《中华人民共和国传染病防治法》第七十七条法律法规、如有隐瞒、导致传染病传播、流行，给他人人身、财产造成损失，我将承担法律责任。
      </div> -->

      <div class="flexlc lhbar mt30">
        <div class="flexlc" style="width: 30%">
          <div class="page-title0">接诊医生：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">填写日期：</div>
          <div class="m-input flex-sub tac">{{ dlgData.createTime }}</div>
        </div>
      </div>

      <!-- 有签字图片 -->
      <div v-if="dlgData.signatureUrl" class="flexlt lhbar mt30">
        <div class="flexlt" style="width: 30%">
          <div class="page-title0 lhbar flexlc">就诊患者签字：</div>
          <img class="" :src="dlgData.signatureUrl" alt="" style="display: block; width: 100px; height: 50px" />
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">打印日期：</div>
          <div class="m-input flex-sub tac">{{ printDate }}</div>
        </div>
      </div>
      <!--  无签字图片 -->
      <div v-else class="flexlc lhbar mt30">
        <div class="flexlc" style="width: 30%">
          <div class="page-title0">就诊患者签字：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">打印日期：</div>
          <div class="m-input flex-sub tac">{{ printDate }}</div>
        </div>
      </div>

      <!-- 第二行 -->
      <!-- <div class="flexlc lhbar mt30">
        <div class="flexlc" style="width: 30%">
          <div class="page-title0">分诊人员签字：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
        <div class="flexlc lhbar" style="width: 30%; margin-left: 20%">
          <div class="page-title0 lhbar flexlc">就诊医生签字：</div>
          <div class="m-input flex-sub tac"></div>
        </div>
      </div> -->
    </div>
    <el-button class="no-print" icon="el-icon-printer" type="primary" size="medium" @click="printFunc()"> 打印</el-button>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import * as utils from '@/utils'
import { return2Num } from '@/utils/calendarData'

// import {
//   findInfoById,
// } from '@/api/jyt/drugMail'

export default {
  components: {},
  data() {
    return {
      paperHeight: 0,
      paperWidth: 0,
      fieldList: [
        { name: '住宅物业管理费', desc: '111', money: '10' },
        { name: '电梯费', desc: '222', money: '20' },
        { name: '', desc: '', money: '' },
        { name: '', desc: '', money: '' },
      ],
      id: '',

      printDate: '',

      dlgData0: '',
      dlgData: '',

      // 下拉框
      tdrqSelect: [], // 是否为以下特定职业人群
      zyzzSelect: [], // 主要症状
      hzqxSelect: [], // 患者去向

      dlgDataList0: [],
      dlgDataList: [],
    }
  },
  computed: {},
  created() {
    this.setPrintDate()

    this.setFormData()
    this.getDbItems()
  },
  mounted() {
    this.$nextTick(() => {
      // console.log('utils.getDpiWidth(227)', utils.getDpiWidth(227))
      // this.paperWidth = utils.getDpiWidth(227)
      // this.paperHeight = utils.getDpiHeight(122)

      this.paperWidth = utils.getDpiWidth(188)
      this.paperHeight = utils.getDpiHeight(1600)

      // this.paperWidth = utils.getDpiWidth(210)
      // this.paperHeight = utils.getDpiHeight(140)
    })
  },
  methods: {
    setPrintDate() {
      let today = new Date()
      let year = today.getFullYear()
      let month = return2Num(today.getMonth() + 1)
      let day = return2Num(today.getDate())
      this.printDate = `${year} 年 ${month} 月 ${day} 日`
    },
    setFormData() {
      console.log('window.sessionStorage.printData', window.sessionStorage.printData)
      this.dlgDataList0 = JSON.parse(window.sessionStorage.printData)
      this.dlgDataList = JSON.parse(window.sessionStorage.printData)

      // for (let dlgData of dlgDataList) {

      // }
      // dlgData.medicalSymptoms = dlgData.medicalSymptoms.split(',')
      // dlgData.specificProfession = dlgData.specificProfession.split(',')
      // dlgData.patientsGo = dlgData.patientsGo.split(',')
      // console.log(dlgData,"dlgData");

      // if (dlgData.isAccompany + '' === '0') {
      //   dlgData.isAccompanyNum = 0
      //   dlgData.isAccompany = '0'
      // } else {
      //   dlgData.isAccompanyNum = dlgData.isAccompany
      //   dlgData.isAccompany = '1'
      // }
      // if (utils.isNull(dlgData.isEatFebrifuge)) {
      //   dlgData.isEatFebrifugeName = ''
      //   dlgData.isEatFebrifuge = '0'
      // } else {
      //   dlgData.isEatFebrifugeName = dlgData.isEatFebrifuge
      //   dlgData.isEatFebrifuge = '1'
      // }

      // this.dlgData = JSON.parse(JSON.stringify(dlgData))
      // console.log(this.dlgData,"this.dlgData");
    },
    getDbItems() {
      console.log(11111111)
      let keyArr = [
        'yd6_specificProfession', // 以下职业
        'yd6_medicalSymptoms', // 主要症状
        'yd6_patientsGo', // 患者去向
      ]
      console.log(keyArr, 'keyArr')
      let keyStr = keyArr.join(',')
      console.log(keyStr, 'keyStr')
      utils.getDbItems(keyStr, 110).then((list) => {
        console.log(list, 'list')
        // 是否为以下特定职业人群
        this.tdrqSelect = list[0]
        console.log(this.tdrqSelect, 'this.tdrqSelect')
        //  您目前是否有以下症状
        this.zyzzSelect = list[1]

        // 患者去向
        this.hzqxSelect = list[2]
      })
    },

    // ///////

    dealBigMoney(val) {
      console.log('之后的', utils.dealBigMoney(val))
      return utils.dealBigMoney(val)
    },

    // 打印
    printFunc() {
      this.dlgDataList = JSON.parse(JSON.stringify(this.dlgDataList0))
      this.$nextTick(() => {
        window.print()
      })
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss">
body {
  height: auto !important;
}
#app {
  height: auto !important;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
.app-container.print {
  font-size: 14px;
}
.app-container {
  box-sizing: border-box;
  font-size: 12px;
  position: relative;
  width: 100%;
  // height: 100%;
  overflow: auto;
  padding: 0;

  .paper-container {
    // background: url('/static/image/paper.jpg') no-repeat center;
    // background-size: 100%;
    background: #fff;
    position: relative;
    box-sizing: border-box;
    padding: 16px;
    font-size: 12px;
    // padding-top: 16px;
    .field {
      position: absolute;
      max-width: 360px;
      word-break: break-all;
    }
  }
  .el-button.no-print {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

@media print {
  .no-print {
    display: none;
  }
}

//
// 表格
.tac {
  text-align: center;
}
.tar {
  text-align: right;
}
.taj {
  text-align: justify;
}
.text-bold {
  font-weight: bold;
}
.mt16 {
  margin-top: 16px;
}
.m-table {
  // background:#666;
  border-top: 1px solid #666;
  border-left: 1px solid #666;
  border-spacing: 0px;
  font-size: 12px;
  td {
    background: #fff;
    min-height: 30px;
    box-sizing: border-box;
    padding: 1px;
    border-right: 1px solid #666;
    border-bottom: 1px solid #666;
  }
  .mh {
    min-height: 36px;
  }
}

/////
// .page-con {
//   color: #333;
// }
.page-title0 {
  font-weight: bold;
  color: #000;
}
.page-title {
  color: #000;
}
.lhbar {
  height: 26px;
  .jbxxBox {
    display: flex;
    flex-direction: column;
  }
}
.m-input {
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  padding: 0;
  height: 18px;
  line-height: 18px;
}
</style>