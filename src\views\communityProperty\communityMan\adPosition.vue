<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区：">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间筛选：">
          <el-date-picker v-model="listQuery.rangeDate" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入广告位名称' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>登记</el-button>
        <el-button icon='el-icon-check' type="primary" size='mini' @click='batchItem("BATCH")'>批量出租</el-button>
        <!-- <el-button icon="el-icon-download" size="mini" type="primary" @click="exportExcel">
          Excel导出
        </el-button> -->
        <el-upload class="upload-wrap" action="" :before-upload="uploadItem">
          <el-button icon="el-icon-upload" size="mini" type="primary">
            Excel导入
          </el-button>
        </el-upload>
        <el-button icon="el-icon-download" type="primary" size="mini" @click="downloadItem()">
          模板下载
        </el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="楼栋">
          <template slot-scope="scope">
            <span>{{ scope.row.floorName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="单元">
          <template slot-scope="scope">
            <span>{{ scope.row.unitName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="广告位名称">
          <template slot-scope="scope">
            <span>{{ scope.row.adSpace }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.state == 1" class="fsuccess">已出租</span>
            <span v-else class="fwarning">闲置</span>
          </template>
        </el-table-column>

        <el-table-column label="价格/月(元)">
          <template slot-scope="scope">
            <span>{{ scope.row.price }}</span>
          </template>
        </el-table-column>

        <el-table-column label="使用人">
          <template slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="到期时间">
          <template slot-scope="scope">
            <span>{{ scope.row.expireTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="340" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')">详情</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row, 'EDIT')">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button>
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="batchItem('VIEW', scope.row)">出租历史</el-button>
            <!-- <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItemBatch(scope.row)">删除出租广告位</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑广告位信息" :visible.sync="dlgShow" width='600px' append-to-body>

      <el-form ref="dlgForm" :disabled="dlgType == 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="所在小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在楼栋">
          <el-select v-model="dlgData.floorId" filterable clearable placeholder="请选择楼栋" @change="floorChange">
            <el-option v-for="item in buildingList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在单元">
          <el-select v-model="dlgData.unitId" filterable clearable placeholder="请选择单元">
            <el-option v-for="item in unitList" :key="item.id" :label="item.unitName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="广告位名称" prop="adSpace">
          <el-input v-model="dlgData.adSpace" placeholder="请输入广告位名称" />
        </el-form-item>

        <el-form-item label="备注">
          <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.remark" placeholder="请输入备注" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal='false' :title="'批量出租'" :visible.sync="dlgShowBatch" width='1200px' top="30px" append-to-body>
      <el-table v-if="dlgType === 'VIEW'" class='m-small-table' :data="dlgData.list" border fit highlight-current-row>
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="出租月份">
          <template slot-scope="scope">
            {{scope.row.startDate}}
          </template>
        </el-table-column>

        <el-table-column label="月租费(元)">
          <template slot-scope="scope">
            {{scope.row.price}}
          </template>
        </el-table-column>

        <el-table-column label="广告商">
          <template slot-scope="scope">
            {{scope.row.advertiser}}
          </template>
        </el-table-column>
        <!-- 
        <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItemHis(scope.row, scope.$index)">删除</el-button>
          </template>
        </el-table-column> -->

      </el-table>
      <el-form v-else ref="dlgFormBatch" :rules="rules" :model="dlgData" label-position="right" label-width="100px">

        <el-divider content-position="left">
          <el-button @click="showAdPosDlgMul" type='primary' icon='el-icon-edit'>选择广告位</el-button>
        </el-divider>
        <el-table class='m-small-table' :data="dlgData.records" border fit highlight-current-row>
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="小区名称">
            <template slot-scope="scope">
              <span>{{ scope.row.communityName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="楼栋">
            <template slot-scope="scope">
              <span>{{ scope.row.floorName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="单元">
            <template slot-scope="scope">
              <span>{{ scope.row.unitName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="广告位名称">
            <template slot-scope="scope">
              <span>{{ scope.row.adSpace }}</span>
            </template>
          </el-table-column>

          <el-table-column label="开始月份" width="150px">
            <template slot-scope="scope">
              <el-form-item label-width="0" :prop="'records.' + scope.$index + '.startDate'" :rules="[{required: true, message: '开始月份必填',trigger: 'change'}]">
                <el-date-picker style="width:120px;" @focus="focusItem(scope.$index, 'START')" @change="calcTotal" :picker-options="pickerOptions" v-model="scope.row.startDate" value-format="yyyy-MM" format="yyyy-MM" type="month" placeholder="开始月份">
                </el-date-picker>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="结束月份" width="180px">
            <template slot-scope="scope">
              <el-form-item label-width="0" :prop="'records.' + scope.$index + '.endDate'" :rules="[{required: true, message: '结束月份必填',trigger: 'change'}]">
                <el-date-picker style="width:120px;display:inline-block;" @focus="focusItem(scope.$index, 'END')" @change="calcTotal" :picker-options="pickerOptions" v-model="scope.row.endDate" value-format="yyyy-MM" format="yyyy-MM" type="month" placeholder="结束月份">
                </el-date-picker>
                <el-button type="primary" size="mini" icon="el-icon-caret-bottom" v-if="dlgData.records.length > 1 && scope.$index == 0" circle plain @click="applyItem('END')" title="应用至全部"></el-button>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="每月价格(元)" width="180px">
            <template slot-scope="scope">
              <el-form-item label-width="0" :prop="'records.' + scope.$index + '.price'" :rules="[{required: true, message: '每月价格必填',trigger: 'blur'}]">
                <el-input-number style="width:120px;display:inline-block;" @change="calcTotal" v-model="scope.row.price" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
                <el-button type="primary" size="mini" icon="el-icon-caret-bottom" v-if="dlgData.records.length > 1 && scope.$index == 0" circle plain @click="applyItem('PRICE')" title="应用至全部"></el-button>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delAdPos(scope.$index)">删除</el-button>
            </template>
          </el-table-column>

        </el-table>

        <el-row class="mt10">
          <el-col :span="12">
            <el-form-item label="广告商:" prop="advertiser">
              <el-input v-model="dlgData.advertiser" placeholder="请输入广告商名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总价格:">
              {{totalMoney}} 元
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShowBatch = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType != 'VIEW'" type='success' :loading='dlgLoading' @click="subDlgBatch" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <adPosDlgMul />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, cofloorCommunity, buildingunitFloor, advertisingspacePage, advertisingspaceAddOrUpdate, advertisingspaceDel, advertisingspaceLease, findInfoBySpaceId, delBySpaceId, delByDataId, importAdvertising } from '@/api/communityMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import adPosDlgMul from '@/components/Dialog/communityMan/adPosDlgMul'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  communityId: '',
  communityName: '',
  floorId: '',
  floorName: '',
  unitId: '',
  unitName: '',
  adSpace: '',
  userId: '',
  userName: '',
  expireTime: '',
  remark: '',
  records: [],
  list: []
}


export default {
  name: 'adPosition',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    adPosDlgMul
  },
  data () {
    return {
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < new Date();
        },
      },

      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题
      totalMoney: 0,
      dlgShowBatch: false,

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        floorId: [{ required: true, message: '必填字段', trigger: 'change' }],
        unitId: [{ required: true, message: '必填字段', trigger: 'change' }],
        userName: [{ required: true, message: '必填字段', trigger: 'change' }],
        adSpace: [{ required: true, message: '必填字段', trigger: 'blur' }],
        advertiser: [{ required: true, message: '必填字段', trigger: 'blur' }],
        expireTime: [{ required: true, message: '必填字段', trigger: 'change' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        rangeDate: ''
      },
      communityList: [],
      buildingList: [],
      unitList: [],
      userInfo: {}
    }
  },
  computed: {
    ...mapGetters('communityMan/adPosDlgMul', {
      adPosList: 'list'
    }),

  },
  watch: {
    adPosList (val) {
      let list = JSON.parse(JSON.stringify(val))
      let records = this.dlgData.records
      let idList = records.map(item => item.id)
      for (let i of list) {
        if (!idList.includes(i.id)) {
          this.dlgData.records.push({
            id: i.id,
            spaceId: i.id,
            communityId: i.communityId,
            communityName: i.communityName,
            floorId: i.floorId,
            floorName: i.floorName,
            unitId: i.unitId,
            unitName: i.unitName,
            adSpace: i.adSpace,
            startDate: '',
            endDate: '',
            price: '',
            advertiser: '',
          })
        }
      }
      this.$forceUpdate()
    },

  },
  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/workade/kaoqinyuebaodaochu?` + param
      window.open(sendUrl)
    },

    // 下载
    downloadItem () {
      let url =
        'https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E5%B9%BF%E5%91%8A%E4%BD%8D.xlsx'
      window.open(url)
    },

    // 上传
    uploadItem (file) {
      let name = file.name.split('.')
      let suffix = name[name.length - 1]

      if (suffix !== 'xls' && suffix !== 'xlsx') {
        this.$message({
          type: 'warning',
          message: '只能上传xls/xlsx文件'
        })
        return false
      }

      let loading = this.$loading({
        lock: true,
        text: '导入中',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      let postParam = {
        file
      }

      importAdvertising(postParam).then(res => {
        loading.close()
        if (res.data.code == 200) {
          this.$message.success('导入成功')
          this.getList()
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })

      return false
    },

    calcTotal () {
      let records = JSON.parse(JSON.stringify(this.dlgData.records))
      let total = 0
      for (let i of records) {
        let diff = utils.diffMonth(i.endDate, i.startDate)
        total += diff * (i.price || 0)
      }
      this.totalMoney = total.toFixed(2)
      this.$forceUpdate()
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 显示人员dlg
    showAdPosDlgMul () {
      this.$store.commit('communityMan/adPosDlgMul/SET_LIST', JSON.parse(JSON.stringify(this.dlgData.records)))
      this.$store.commit('communityMan/adPosDlgMul/SET_DLGSHOW', true)
    },

    communityChange () {
      let communityId = this.dlgData.communityId
      this.dlgData.floorId = ""
      this.dlgData.unitId = ""
      this.getBuildingList(communityId)
    },

    floorChange () {
      let floorId = this.dlgData.floorId
      this.dlgData.unitId = ""
      this.getUnitList(floorId)
    },

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取楼栋列表
    getBuildingList (id) {
      if (utils.isNull(id)) {
        return
      }
      cofloorCommunity(id).then(res => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data
        }
      })
    },

    // 获取单元列表
    getUnitList (id) {
      if (utils.isNull(id)) {
        return
      }
      buildingunitFloor(id).then(res => {
        if (res.data.code == 200) {
          this.unitList = res.data.data
        }
      })
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      this.listQuery.startDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[0]
      this.listQuery.endDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[1]
      advertisingspacePage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 月份控制
    focusItem (idx, type) {
      let item = this.dlgData.records[idx]
      let minMonth = utils.getDiffMonth("", 0)
      let maxMonth = null
      if (type === 'START') {
        minMonth = utils.getDiffMonth('', 1)
        if (!utils.isNull(item.endDate)) {
          maxMonth = utils.getDiffMonth(item.endDate, 0)
        }
      } else if (type === 'END') {
        minMonth = item.startDate ? utils.getDiffMonth(item.startDate, 0) : utils.getDiffMonth('', 1)
      }
      if (utils.isNull(maxMonth)) {
        this.pickerOptions.disabledDate = time => (time.getTime() < new Date(minMonth));
      } else {
        this.pickerOptions.disabledDate = time => (time.getTime() < new Date(minMonth) || time.getTime() > new Date(maxMonth));
      }

    },

    // 应用至
    applyItem (type) {
      for (let i in this.dlgData.records) {
        if (i == 0) {
          continue
        }
        if (type == 'END') {
          if (this.dlgData.records[0].endDate > this.dlgData.records[i].startDate) {
            this.dlgData.records[i].endDate = this.dlgData.records[0].endDate
          }
        } else if (type == 'PRICE') {
          this.dlgData.records[i].price = this.dlgData.records[0].price
        }
      }
      this.calcTotal()
      this.$forceUpdate()
    },

    // 批量出租
    batchItem (type, data) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = type
      this.dlgShowBatch = true
      if (type === 'VIEW') {
        findInfoBySpaceId(data.id).then(res => {
          if (res.data.code == 200) {
            this.dlgData.list = JSON.parse(JSON.stringify(res.data.data))
          }
        })
      }
    },

    // 删除廣告位
    delAdPos (idx) {
      this.dlgData.records.splice(idx, 1)
      this.calcTotal()
    },


    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
      this.getBuildingList(this.dlgData.communityId)
    },

    // 批量提交
    subDlgBatch () {
      this.$refs['dlgFormBatch'].validate((valid) => {
        if (valid) {
          if (this.dlgData.records.length == 0) {
            this.$message.warning("请选择广告位")
            return
          }
          let records = JSON.parse(JSON.stringify(this.dlgData.records))
          for (let i of records) {
            i.advertiser = this.dlgData.advertiser
            delete i.id
          }
          let postParam = {
            records
          }
          this.dlgLoading = true
          advertisingspaceLease(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShowBatch = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          postParam.floorName = utils.getNameById(postParam.floorId, this.buildingList)
          postParam.unitName = utils.getNameById(postParam.unitId, this.unitList, 'id', 'unitName')
          this.dlgLoading = true
          advertisingspaceAddOrUpdate(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgType = type
      this.dlgShow = true
      this.getBuildingList(this.dlgData.communityId)
      this.getUnitList(this.dlgData.floorId)
    },


    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        advertisingspaceDel(data.id).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 批量删除
    delItemBatch (data) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delBySpaceId(data.id).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 删除历史
    delItemHis (data, idx) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let postParam = {
          ids: data.id
        }
        delByDataId(postParam).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.dlgData.list.splice(idx, 1)
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/stewardInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.text-center .el-form-item__content {
  text-align: center;
}
</style>


