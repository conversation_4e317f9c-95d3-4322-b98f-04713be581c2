<template>
  <el-dialog
    class="mazhenguo"
    title="选择报事科目"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="600px"
    top="30px"
  >
    <el-input placeholder="输入关键字进行过滤" v-model="filterBranch"> </el-input>

    <el-tree
      show-checkbox
      :check-strictly="true"
      @check-change="checkChange"
      :expand-on-click-node="true"
      class="mt10"
      ref="branchTree"
      highlight-current
      node-key="id"
      :data="treeData"
      v-loading="treeLoading"
      :props="defaultProps"
      :filter-node-method="filterNode"
      :default-expanded-keys="defaultOpenList"
      style="height: 500px; overflow: auto"
    >
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span :title="data.name">{{ data.name }}</span>
      </span>
    </el-tree>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button v-if="dlgType != 'info'" type="danger" @click="removeNode" icon="el-icon-delete">清空</el-button>
      <el-button v-if="dlgType != 'info'" :loading="dlgSubLoading" type="success" @click="dlgSubFunc" icon="el-icon-check">
        <span v-if="dlgSubLoading">确定中...</span>
        <span v-else>确定</span>
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
// 组件
// 工具
import { uploadImg, uploadImg2 } from '@/utils/uploadImg'
// 接口
import * as utils from '@/utils'
import * as regUtils from '@/utils/regUtils'

import { postAction, getAction } from '@/api'

export default {
  components: {},
  props: {
    dlgType: {
      type: String,
      default: 'add',
    },
    dlgQuery: {
      type: Object,
      default: {},
    },
    dlgState0: {
      type: Boolean,
      default: false,
    },
    dlgIdList: {}, // 选中的
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          this.idMapList = []
          this.$refs.branchTree.setCheckedKeys([])
          this.filterBranch = ''
          this.getTree()
        }, 50)
      } else {
        this.$emit('closeDlg')
      }
    },

    filterBranch(val) {
      this.$refs.branchTree.filter(val)
    },
  },
  data() {
    return {
      // 树初始化
      treeLoading: false,
      idMapList: [],
      setTreeState: false,

      defaultOpenList: [],
      defaultProps: {
        children: 'children',
        label: 'name',
      },

      filterBranch: '',
      treeData: '',
      selectNode: {},

      // 弹窗
      dlgState: false,

      dlgSubLoading: false, // 提交loading
    }
  },
  created() {},
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    getTree() {
      this.treeData = []
      let userInfo = JSON.parse(window.localStorage.userInfo)
      this.treeLoading = true
      getAction(`/report/subject/findSubjectTree/${userInfo.projectId}`).then((res0) => {
        this.treeLoading = false
        let res = res0.data

        if (res.code === '200') {
          this.treeData = JSON.parse(JSON.stringify(res.data))

          let openIdList = []
          for (let item of this.treeData) {
            openIdList.push(item.id)
          }
          this.defaultOpenList = openIdList

          this.setTreeIdMap()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    setTreeIdMap() {
      let preStr = 'idMap'
      this.setTreeIdMapItem(preStr, this.treeData)
      setTimeout(() => {
        this.treeData = JSON.parse(JSON.stringify(this.treeData))

        this.$nextTick(() => {
          this.setTreeState = true
          setTimeout(() => {
            this.setTreeState = false
          }, 80)

          let idArr = this.dlgIdList || []
          this.$refs.branchTree.setCheckedKeys(idArr)
        })
      }, 100)
    },
    setTreeIdMapItem(preStr, treeArr) {
      for (let i = 0; i < treeArr.length; i++) {
        let item = treeArr[i]
        let preStr1 = preStr + '-' + item.id
        item.idMap = preStr1
        this.idMapList.push(preStr1)
        if (item.children && item.children.length > 0) {
          this.setTreeIdMapItem(preStr1, item.children)
        }
      }
    },
    checkChange(data, isSelected, hasChildSelected) {
      if (this.setTreeState) return false
      if (isSelected) {
        let selectIdList = []
        let idMapArr = data.idMap.split('-')
        idMapArr.splice(0, 1)
        let selectIdListDown = []
        for (let item of this.idMapList) {
          let reg = data.idMap + '-'
          if (item.indexOf(reg) >= 0) {
            let nextStr = item.split(reg)[1]
            let nextIdArr = nextStr.split('-')
            for (let nextIdArrId of nextIdArr) {
              if (selectIdListDown.indexOf(nextIdArrId) < 0) {
                selectIdListDown.push(nextIdArrId)
              }
            }
          }
        }
        selectIdList = [...selectIdListDown]
        this.setTreeState = true
        setTimeout(() => {
          this.setTreeState = false
        }, 80)
        for (let itemId of selectIdList) {
          this.$refs.branchTree.setChecked(itemId, true)
        }
      } else {
        let selectIdListDown = []
        for (let item of this.idMapList) {
          let reg = data.idMap + '-'
          if (item.indexOf(reg) >= 0) {
            let nextStr = item.split(reg)[1]
            let nextIdArr = nextStr.split('-')
            for (let nextIdArrId of nextIdArr) {
              if (selectIdListDown.indexOf(nextIdArrId) < 0) {
                selectIdListDown.push(nextIdArrId)
              }
            }
          }
        }

        this.setTreeState = true
        setTimeout(() => {
          this.setTreeState = false
        }, 80)
        for (let itemId of selectIdListDown) {
          this.$refs.branchTree.setChecked(itemId, false)
        }
      }
    },

    // 弹窗提交 ------
    dlgSubFunc() {
      let checkedNodes = this.$refs.branchTree.getCheckedNodes()
      let halfCheckedNodes = this.$refs.branchTree.getHalfCheckedNodes()

      let selectList0 = [...checkedNodes, ...halfCheckedNodes]
      let idArr = []
      let nameArr = []
      for (let item of selectList0) {
        idArr.push(item.id)
        nameArr.push(item.name)
      }

      let returnObj = {
        ids: idArr.join(','),
        names: nameArr.join(','),
      }

      this.$emit('backFunc', returnObj)
      this.closeDlg()
    },
    removeNode() {
      this.selectKeys = []
      this.$refs.branchTree.setCheckedKeys([])
    },

    closeDlg() {
      this.$emit('closeDlg')
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>