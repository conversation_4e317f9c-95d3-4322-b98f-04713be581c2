<template>
  <div class="">
    <!-- 弹窗 岗位 -->
    <el-dialog :close-on-click-modal='false' 
      title="选择核算部门" 
      
      :append-to-body='true'
      :visible.sync="hsbmsState" 
      width='900px' 
      top='30px'
      icon-class='el-icon-info'>
      <div class="dialog-bms-bar">
        <!-- 部门 -->
        <div class='dialog-bms-left'>
          <el-input
            placeholder="输入关键字进行过滤"
            style="margin-bottom: 10px;"
            v-model="bmsText">
          </el-input>
          <!-- <p style="margin-bottom: 10px; padding-left: 10px; color: #67C23A; width: 400px;">当前选中部门：{{ selectNode.label || '请选择' }}</p> -->
          <div class="bms-tree">
            <el-tree 
              :data="bmsData" 
              ref="bmsDom"
              default-expand-all
              :filter-node-method="bmsFilter"
              @node-click="bmsClick">
            </el-tree>
          </div>
        </div>
        <div class='dialog-bms-right'>
          <div class='m-tips' style="height: 28px; padding-left: 10px; margin-bottom: 10px;">

            <div class='m-desc' style="float: left; line-height: 27px;">
              <i class='el-icon-warning'></i>&nbsp;只能选择【末级】核算部门
            </div>
          </div>

          <!-- <el-button v-for='(item, index) of bmsArr2' :key='index' class='bms-a' type='success' size='mini' plain>{{ item.relationName }}<i class="el-icon-close" @click='delBmFunc(item.relationId, item.relationName)'></i></el-button> -->
          <div class="dialog-bms-right-table">
            <el-table
              :key="tableKey"
              :data="bmsArr2"
              border
              fit
              class='m-small-table'
              highlight-current-row>
              <el-table-column
                type="index"
                align="center"
                width="50">
              </el-table-column>
              <el-table-column label="名称" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.label }}</span>
                </template>
              </el-table-column>
              <el-table-column label="比例/%" align="center" width="90">
                <template slot-scope="scope">
                  <el-input type='number' v-model="scope.row.scale" style="width: 60px;"></el-input>
                </template>
              </el-table-column>

              <el-table-column label="操作" align="center" width="80" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button title='删除' type="danger" size="mini" plain @click="delBmFunc(scope.row.id)" icon="el-icon-delete"></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          
        </div>
        <!-- 部门多选框 -->
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" icon='el-icon-back'>取消</el-button>
        <el-button type="success" @click="bumenOkFunc" icon="el-icon-check">确定</el-button>
        
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
//import { findAccountBranchTree } from '@/api/dataDic'

export default {
  // components: { adminDashboard, editorDashboard },
  data() {
    return {
      isShow: false,
      // 部门树
      bmsData: [],
      bmsSelect: {},
      bmsText: '',  // 部门左侧筛选

      // 右侧选中的样式
      bmsIds: [],
      bmsNames: [],

      // 表格
      tableKey: 0,
      bmsArr2: [
        // { id: '1', label: '核算部门名称1', scale: '30' },
        // { id: '2', label: '核算部门名称2', scale: '30' },
        // { id: '3', label: '核算部门名称3', scale: '40' }
      ]
      

    }
  },
  computed: {
    ...mapGetters([
      'hsbmsArr'
    ]),
    
    hsbmsState: {
      get: function() {
        let state = this.$store.getters.hsbmsState

        if (state === false) {
          this.bmsArr2 = []
        }
        
        return state
      },
      set: function(newVal) {
        this.$store.commit('SET_HSBMSSTATE', newVal)
      }
    }

  },
  watch: {
    bmsText(val) {
      this.$refs.bmsDom.filter(val);
    },
    hsbmsArr(val) {
      this.bmsArr2 = JSON.parse(JSON.stringify(val))
    }
  },
  created() {
    //this.getTree()
  },
  methods: {
    // 【【 左侧相关
    // 获取部门
    // getTree() {
    //   findAccountBranchTree().then(res => {
    //     let code = res.data.code
    //     let data = res.data.data
    //     let msg = res.data.msg

    //     if (code === '200') {
    //       this.bmsData = JSON.parse(JSON.stringify(res.data.list))
    //     } else {
    //       this.$message.error(msg)
    //     }
    //   })
    // },
    // 节点左键点击事件
    bmsClick(data, node, mNode) {
      $('.tree-on').removeClass('tree-on')
      setTimeout(() => {
        $('.is-current>.el-tree-node__content').addClass('tree-on')
      }, 50)
      // 是否是末级节点
      if (data.finalStage === 0) {
        let id = data.id
        let label = data.label
        let isHas = this.bmsArr2.every(item => {
          return item.id !== id
        })
        if (isHas) {
          if (this.bmsArr2.length == 0) {
            this.bmsArr2.push({ id, label, scale: '100' })
          } else {
            this.bmsArr2.push({ id, label, scale: '0' })
            for (let item of this.bmsArr2) {
              item.scale = 0
            }
          }
          
        }
      }
    },
    // 筛选部门
    bmsFilter(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 】】 左侧相关

    // 【【 右侧相关
    // 右侧删除部门方法
    delBmFunc(id) {
      let nArr = this.bmsArr2.filter(item => {
        return item.id !== id
      })

      console.log(nArr.length)

      if (nArr.length === 1) {
        nArr[0].scale = 100
      }
      this.bmsArr2 = JSON.parse(JSON.stringify(nArr))
    },
    // 】】 右侧相关

    // 【【 其他
    // 选择部门提交
    bumenOkFunc() {
      // 提交方法
      // bmsArr2
      let valid = true
      if (this.bmsArr2.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择核算部门'
        })
        return false
      }
      let total = 0
      for (let item of this.bmsArr2) {
        let scale = parseInt(item.scale)

        if (scale === 0) {
          this.$message({
            type: 'warning',
            message: '核算比例不能为0'
          })
          valid = false
          return false
        }
        total += scale
      }
      if (total !== 100) {
        this.$message({
          type: 'warning',
          message: '请确认核算比例之和为100'
        })
        return false
      }
      if (valid) {
        this.$store.commit('SET_HSBMSARR', this.bmsArr2)
        this.closeDialog()
      }
    },
    // 关闭弹窗 
    closeDialog() {
      this.$store.commit('SET_HSBMSSTATE', false)
    }
    // 】】 其他
    
    

    // 筛选岗位
    // filterBmrightListOld
    // filterGangwei(val) {
    //   this.filterBmrightList = JSON.parse(JSON.stringify(this.filterBmrightListOld))
    //   this.filterBmrightList = this.filterBmrightList.filter(item => {
    //     return item.label.indexOf(val) >= 0
    //   })
    // },

    
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dialog-bms-bar {
  display: flex;
  justify-content: space-between;
}
.dialog-bms-left,.dialog-bms-right {
  width: 49%;
}
.bms-tree {
  height: 400px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
}
.dialog-bms-right {
  // border-radius: 5px;
  // padding: 10px;
  .dialog-bms-right-table {
    border: 1px solid #dcdfe6;
    height: 400px;
    overflow: auto;
    border-radius: 5px;

  }
  .bms-a {
    margin-bottom: 10px;
    margin-left: 0px;
    margin-right: 6px;
  }
  i {
    display: inline-block;
    margin-left: 3px;

  }
}
</style>