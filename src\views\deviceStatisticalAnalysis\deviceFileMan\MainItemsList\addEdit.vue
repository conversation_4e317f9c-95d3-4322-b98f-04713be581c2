<template>
    <div>
        <el-dialog @close="onClose" :title="title" :visible.sync="dialogVisible" width="500px"
            :close-on-click-modal="false" :custom-class="basicledgerManDlg">
            <el-form ref="elForm" :model="formData" :rules="rules" label-width="120px" :disabled="dlgType == 'info'">
                <el-form-item label="选择检修事项" prop="type">
                    <el-select v-model="formData.type" placeholder="请选择检修事项" clearable filterable ref="typeRef">
                        <el-option v-for="(item, index) in maintenanceItemsOptions" :key="index" :label="item.name"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="检修内容" prop="workDescribe">
                    <el-input type="textarea" v-model="formData.workDescribe" placeholder="请输入检修内容" clearable :rows="5" maxlength="500" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button v-show="dlgType !== 'info'" type="success" @click="handelConfirm" :loading="btnLoading">确
                    定</el-button>
            </div>
        </el-dialog>

    </div>
</template>
<script>
import { postAction, getAction, deleteAction ,putAction} from "@/api";
import { getDataDict } from "@/utils";
import { mapGetters } from "vuex";

export default {
    inheritAttrs: false,
    components: {
    },
    props: {
        dlgType: {
            type: String,
            default: "add",
        },
    },
    data() {
        return {
            title: "添加",
            dialogVisible: false,
            btnLoading: false,
            maintenanceItemsOptions: [],
            formData: {
                type: undefined,
                workDescribe: undefined,
            },
            rules: {
                type: [{
                    required: true,
                    message: '必填字段',
                    trigger: 'change'
                }],
                workDescribe: [{
                    required: true,
                    message: '必填字段',
                    trigger: 'change'
                }],
            },
        }
    },
    computed: {

    },
    watch: {

    },
    created() {
    },
    mounted() {
        getDataDict(this, "maintenanceItems", "maintenanceItemsOptions");
    },
    methods: {



        onClose() {
            this.$refs['elForm'].resetFields()
            this.dialogVisible = false;
        },


        handelConfirm() {
            if (this.dlgType == 'info') {
                this.$refs['elForm'].resetFields()
                this.dialogVisible = false;
                return
            }
            this.$refs['elForm'].validate(valid => {
                if (valid) {
                    let postData = JSON.parse(JSON.stringify(this.formData))
                    postData.typeStr = this.$refs.typeRef.selected.label;
                    delete postData.createTime
                    postData.createUserName = window.localStorage.userName

                    console.log(postData, "postData");
                    this.btnLoading = true;

                    if (this.dlgType == 'edit') {
                        putAction(`/green/equ/check-item/update`, postData).then(
                            (res) => {
                                this.btnLoading = false;
                                if (res.data.code === "200") {
                                    this.$message({
                                        type: "success", // success, warning, info, error
                                        message: "编辑成功！",
                                    });
                                    this.dialogVisible = false;
                                    this.$parent.searchFunc()
                                    this.$refs.elForm.resetFields();
                                } else {
                                    this.$message.error(res.data.msg)
                                }
                            }
                        );
                    } else {
                        if (postData.id) {
                            delete postData.id
                        }
                        postAction(`/green/equ/check-item/create`, postData).then(
                            (res) => {
                                this.btnLoading = false;
                                if (res.data.code === "200") {
                                    this.$message({
                                        type: "success", // success, warning, info, error
                                        message: "添加成功！",
                                    });
                                    this.dialogVisible = false;
                                    this.$parent.searchFunc()
                                    this.$refs.elForm.resetFields();
                                } else {
                                    this.$message.error(res.data.msg)
                                }
                            }
                        )
                    }
                }
            })
        }

    },


}

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-dialog {

    /* 表格input不对齐 */
    .formStyle .el-form-item {
        margin: 0;
    }

    /* “设备参数”和表格对齐 */
    .formStyle .el-form-item__label {
        margin-top: 7px;
    }

}
</style>
