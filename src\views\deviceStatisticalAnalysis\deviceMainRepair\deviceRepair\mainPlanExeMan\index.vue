<template>
  <div class="app-container mazhenguo" style="margin-bottom: 32px; padding-bottom: 10px">
    <div class="dflex">
      <div class="flex-sub mr10">
        <div class="clearfix">
          <div class="search-item">
            <!-- <div class="search-item-label lh28">选择项目：</div>
            <el-select
              class="fl"
              style="width: 220px"
              v-model="listQuery.projectId"
              placeholder="选择项目"
              @change="searchFunc"
              filterable
              clearable
            >
              <el-option
                v-for="item of projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-item"> -->
            <div class="search-item-label lh28">执行状态：</div>
            <el-select class="fl" style="width: 220px" v-model="listQuery.invokeStatus" placeholder="执行状态"
              @change="searchFunc" filterable clearable>
              <el-option v-for="item of axztSelect" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <div class="search-item">
            <div class="search-item-label lh28">筛选条件：</div>
            <el-input class="fl" style="width: 160px" v-model="listQuery.label" @keyup.enter.native="searchFunc"
              placeholder="关键字" clearable>
            </el-input>

            <el-button class="fl ml10" @click="searchFunc" icon="el-icon-search" type="primary">查询</el-button>
            <!-- <el-button
              class="fl ml10"
              @click="showDlg('add', {})"
              icon="el-icon-plus"
              type="success"
              >添加</el-button
            > -->
          </div>
        </div>
      </div>
    </div>

    <el-table height="calc(100vh - 288px)" ref="tableRef" class="m-small-table" v-loading="listLoading" :key="tableKey"
      :data="list" border fit highlight-current-row>
      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column label="设备编码" align="center" prop="equCode" width="auto">
      </el-table-column>
      <el-table-column label="计划名称" align="center" prop="checkName" show-overflow-tooltip />
      <el-table-column label="设备名称" align="center" prop="equName" show-overflow-tooltip />
      <el-table-column label="设备型号" align="center" prop="equModel" show-overflow-tooltip />
      <el-table-column label="设备位置" align="center" prop="equPosition" show-overflow-tooltip />
      <el-table-column label="计划检修日期" align="center" prop="checkDate" width="120" />
      <el-table-column label="检修事项" align="center" prop="checkItem" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="scope.row.checkItem.length == 0"></div>
          <div v-else>
            <span v-for="(item, index) of scope.row.checkItem" :key="index">
              <span :title="item.value">{{ item.name }}</span>
              <span v-if="index != scope.row.checkItem.length - 1">,</span>
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="执行时间" align="center" prop="invokeTime" width="160">
      </el-table-column>
      <el-table-column label="执行情况" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.invokeStatusStr }}</span>
          <!-- <span v-if="scope.row.invokeStatus == '1'"
            >（{{ scope.row.invokeStatusInfo }}）</span
          > -->
        </template>
      </el-table-column>

      <!-- <el-table-column label="更换配件" align="center" prop="accessoryJson" /> -->
      <el-table-column label="执行人" align="center" prop="invokeUserName" show-overflow-tooltip /><el-table-column
        label="备注" align="center" prop="info" width="120" show-overflow-tooltip />
      <el-table-column label="检修周期" align="center" prop="checkCycleStr" show-overflow-tooltip />



      <el-table-column label="操作" width="180" align="center">
        <template slot-scope="scope">
          <el-button @click="showDlg('info', scope.row)" icon="el-icon-document" size="mini" type="success" title="详情"
            plain>详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination class="mt10" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size"
      @pagination="getList" />
    <div class="clear"></div>
    <!-- 弹窗 新增/编辑 -->
    <addDlg ref="addDlg" :dlgData0="dlgData" :dlgType="dlgType" :dlgQuery="dlgQuery" @getList="getList"
      :projectList="projectList" />

    <ExeDlg ref="ExeDlg" :dlgData0="dlgExeData" :dlgType="dlgExeType" :dlgQuery="dlgExeQuery" @getList="getList" />


    <el-dialog :title="'设备二维码-' + selectRow.equName" :visible.sync="dialogVisible" width="300px">
      <div class="qrbox2">
        <div v-if="dialogVisible" ref="qrCode"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as utils from "@/utils";
import { getAction, deleteAction } from "@/api";
import QRCode from "qrcodejs2";

import Pagination from "@/components/Pagination"; // 分页
import addDlg from "./addDlg";
import ExeDlg from "./ExeDlg";

let listQueryEmpty = {
  invokeStatus: "",
  label: "", //	模糊查询	body	false	string
  page: 1,
  size: 20,
  projectId: ""
};
export default {
  components: {
    Pagination,
    addDlg,
    ExeDlg,
  },
  props: {},
  data() {
    return {
      dlgAddDeviceMainType: 'add',
      projectList: [],
      userInfo: JSON.parse(window.localStorage.userInfo),
      searchMoreState: false, // 更多筛选
      tableKey: 0,
      list: [],
      selectList: [], // 选中
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),
      selectRow: {},
      dialogVisible: false,
      dlgQuery: {},
      dlgType: "", // 弹框状态add, edit
      dlgData: {},
      dlgExeQuery: {},
      dlgExeType: "", // 弹框状态add, edit
      dlgExeData: {},
      dlgAddBaoshiQuery: {},
      dlgAddBaoshiType: "", // 弹框状态add, edit
      dlgAddBaoshiData: {},
      axztSelect: [{ id: 0, name: "待执行" }, { id: 1, name: "已执行" }]
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getList();
  },
  mounted() { },
  methods: {

    showQrCode(row) {
      this.selectRow = row;
      this.dialogVisible = true;

      setTimeout(() => {
        let qrUrl = row.equCode + "";
        this.qrcode = new QRCode(this.$refs.qrCode, {
          text: qrUrl,
          width: 200,
          height: 200,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H
        });
      }, 200);
    },
    parseTime(time) {
      return time.replace(/T/g, " ");
    },
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },
    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    getList() {
      this.list = [];
      let sendObj = JSON.parse(JSON.stringify(this.listQuery));
      sendObj.projectId = this.userInfo.projectId
      sendObj.equName = sendObj.label;
      delete sendObj.label;
      sendObj.pageNo = sendObj.page;
      delete sendObj.page;
      sendObj.pageSize = sendObj.size;
      delete sendObj.size;

      this.listLoading = true;
      getAction("/green/equ/check-invoke/page", sendObj).then(res0 => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = [];
            this.total = 0;
          } else {
            let list = res.data.list;
            for (let item of list) {
              if (item.checkItem) {
                item.checkItem = JSON.parse(item.checkItem);
              } else {
                item.checkItem = [];
              }

              if (item.accessoryJson) {
                item.accessoryJson = JSON.parse(item.accessoryJson);
              } else {
                item.accessoryJson = [];
              }
            }
            console.log("=====list", list);
            this.list = list;
            this.total = res.data.total;

            this.$nextTick(() => {
              this.$refs.tableRef.doLayout();
            });
          }
        } else {
          this.total = 0;
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },


    showDlg(type, row) {
      this.dlgQuery = row; // 查询条件

      this.dlgData = row;
      this.$refs.addDlg.isZhixing = false;
      this.dlgType = type;
      this.$refs.addDlg.dlgState = true;
    },
    showExeDlg(row) {
      this.dlgExeData = row;
      this.dlgExeType = "edit";
      this.$refs.ExeDlg.dlgState = true;
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.qrbox2 {
  padding: 30px 0;
  width: 200px;
  height: 200px;
  margin: 0 auto;
  box-sizing: content-box;
}
</style>
