import request from '@/utils/request'

// 项目维护
export function findProjectTree() {
  return request({
    url: '/sys/findProjectTree',
    method: 'get'
  })
}

// 添加项目信息
export function insertProjectInfo(data) {
  return request({
    url: '/sys/insertProjectInfo',
    method: 'post',
    data
  })
}

// 删除项目
export function removeProjectInfoById(id) {
  return request({
    url: '/sys/removeProjectInfoById?id=' + id,
    method: 'get'
  })
}

// 项目查询模块
export function findModulesByProjectId(id) {
  return request({
    url: '/sys/findModulesByProjectId/' + id,
    method: 'get'
  })
}