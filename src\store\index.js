import Vue from "vue";
import Vuex from "vuex";

import app from "./modules/app";
import listQuery from "./modules/query";
import errorLog from "./modules/errorLog";
import permission from "./modules/permission";
import tagsView from "./modules/tagsView";
import user from "./modules/user";

import tabStore from "./modules/tabStore"; // tab 切花页面值

import VueOrg from "./modules/vueOrg";

import bmTree from "./modules/bmTree";
import bmTree1 from "./modules/bmTree1";
import BmsDialog from "./modules/BmsDialog";
import GwsDialog from "./modules/GwsDialog";
import Usersdialog from "./modules/Usersdialog";
import UsersOrBranDialog from "./modules/UsersOrBranDialog";

import gwTree from "./modules/gwTree";
import holidayDialog from "./modules/holidayDialog";
import mapDialog from "./modules/mapDialog";
import kqDialog from "./modules/kqDialog";
import Imgenlarge from "./modules/imgenlarge";
import Usertree from "./modules/userTree";
import UsersTable from "./modules/UsersTable"; //多用户表格
import Dmantable from "./modules/dmanTable";
import Facedialog from "./modules/faceDialog";
import NavTag from "./modules/NavTag";
import processMan from "./modules/processMan"; // 流程管理

import HsbmsDialog from "./modules/HsbmsDialog"; // 核算部门 多选，配置比例
import HsbmDialog from "./modules/HsbmDialog"; // 核算部门 单选

import Branchtree from "./modules/branchTree"; // 多选部门树

import dialogStore from "./modules/dialogStore"; // 多选权限树

import platformMan from "./modules/platformMan"; // 平台管理模块

import clothingMan from "./modules/clothingMan"; // 被服管理模块

import wasteMan from "./modules/wasteMan"; // 医废管理模块

import communityMan from "./modules/communityMan"; // 小区管理模块

import schoolMan from "./modules/schoolMan"; // 校园端

import propertyMan from "./modules/propertyMan"; //  运营管理模块

import matterMan from "./modules/matterMan"; // 报事

import qualityMan from "./modules/qualityMan"; // 品质

import supplyChainMan from "./modules/supplyChainMan"; // 供应链模块
import reportMan from "./modules/reportMan"; //  报事系统

import qualitySystem from "./modules/qualitySystem"; //  品质系统
import customerCenter from "./modules/customerCenter"; //  客服中心
import financialSystem from "./modules/financialSystem"; //

import getters from "./getters";

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    app,
    listQuery,
    errorLog,
    permission,
    tagsView,
    user,

    tabStore, // tab 切换值

    VueOrg,
    bmTree,
    bmTree1,
    BmsDialog,
    GwsDialog,
    Usersdialog,
    UsersOrBranDialog,
    gwTree,
    holidayDialog,
    mapDialog,
    kqDialog,
    Imgenlarge,
    Usertree,
    UsersTable,
    Dmantable,
    Facedialog,
    NavTag,
    processMan,
    HsbmsDialog,
    HsbmDialog,
    Branchtree,
    dialogStore,
    platformMan, // 平台管理模块
    clothingMan, // 被服管理模块
    wasteMan, // 医废管理模块
    communityMan, // 小区管理模块
    schoolMan,
    propertyMan,
    matterMan,
    qualityMan,
    supplyChainMan, // 供应商模块
    reportMan,
    qualitySystem,
    customerCenter,
    financialSystem
  },
  state: {
    // 工作流
    nodeMap: new Map(),
    isEdit: null,
    loginUser: JSON.parse(localStorage.getItem("loginUser") || "{}"),
    selectedNode: {},
    selectFormItem: null,
    design: {}
  },

  getters,
  mutations: {
    // 工作流
    selectedNode(state, val) {
      state.selectedNode = val;
    },
    loadForm(state, val) {
      state.design = val;
    },
    setIsEdit(state, val) {
      state.isEdit = val;
    },
    setLoginUser(state, val) {
      state.loginUser = val;
    }
  }
});

export default store;
