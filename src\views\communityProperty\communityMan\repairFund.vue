<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="所在小区：">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间筛选：">
          <el-date-picker v-model="listQuery.rangeDate" type="daterange" range-separator="~" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入使用目的' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>基金使用登记</el-button>
        <!-- <el-button icon="el-icon-download" size="mini" type="primary" @click="exportExcel">
          Excel导出
        </el-button>
        <el-upload class="upload-wrap" action="" :before-upload="uploadItem">
          <el-button icon="el-icon-upload" size="mini" type="primary">
            Excel导入
          </el-button>
        </el-upload>
        <el-button icon="el-icon-download" type="primary" size="mini" @click="downloadItem()">
          模板下载
        </el-button> -->
      </el-form>
    </div>
    <el-form inline>
      <el-form-item label="总金额(元)：">
        {{totalItem.totalAmount || 0}}
      </el-form-item>
      <el-form-item label="已用金额(元):">
        {{totalItem.sumCosts || 0}}
      </el-form-item>
      <el-form-item label="剩余百分比">
        {{totalItem.percentage ? totalItem.percentage.toFixed(2) : 0 }} %
      </el-form-item>
    </el-form>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="小区名称">
          <template slot-scope="scope">
            <span>{{ scope.row.communityName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="使用目的">
          <template slot-scope="scope">
            <span>{{ scope.row.objectives }}</span>
          </template>
        </el-table-column>

        <el-table-column label="使用说明">
          <template slot-scope="scope">
            <span>{{ scope.row.explains }}</span>
          </template>
        </el-table-column>

        <el-table-column label="使用费用">
          <template slot-scope="scope">
            <span>{{ scope.row.costs }}</span>
          </template>
        </el-table-column>

        <el-table-column label="使用时间">
          <template slot-scope="scope">
            <span>{{ scope.row.useTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')">详情</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row, 'EDIT')">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row, 1)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="新增/编辑维修基金" :visible.sync="dlgShow" width='660px' top="30px" append-to-body>

      <el-form ref="dlgForm" :disabled="dlgType == 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="100px">
        <el-form-item label="所在小区" prop="communityId">
          <el-select v-model="dlgData.communityId" filterable clearable placeholder="请选择小区">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="使用目的" prop="objectives">
          <el-input v-model="dlgData.objectives" placeholder="请输入使用目的" />
        </el-form-item>

        <el-form-item label="使用说明">
          <el-input v-model="dlgData.explains" placeholder="请输入使用说明" />
        </el-form-item>

        <el-form-item label="使用费用" prop="costs">
          <el-input-number v-model="dlgData.costs" :controls='false' :min="0" :precision="2" :step="1"></el-input-number> 元
        </el-form-item>

        <el-form-item label="使用时间" prop="useTime">
          <el-date-picker v-model="dlgData.useTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="使用时间">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="备注">
          <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.remark" placeholder="请输入备注" />
        </el-form-item>

        <el-form-item label="图片">
          <div class='upload-bar' v-for="(item, index) in dlgData.imgUrl" :key="index">
            <el-image class='avatar' :preview-src-list="[item]" :src="item" alt=""></el-image>
            <i v-if="dlgType !== 'VIEW'" @click='delUploadImg(index)' class="el-icon-error avatar_icon"></i>
          </div>
          <el-upload v-if="dlgType !== 'VIEW'" class="avatar-uploader" action='' :show-file-list="false" :before-upload="beforeUpload">
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <userDlg />
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, repairfundPage, repairfundAddOrUpdate, repairfundDel } from '@/api/communityMan'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import userDlg from '@/components/Dialog/communityMan/userDlg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: '',
  communityId: '',
  communityName: '',
  costs: '',
  explains: '',
  objectives: '',
  remark: '',
  userId: '',
  userName: '',
  useTime: '',
  imgUrl: []
}


export default {
  name: 'repairFund',
  extends: WorkSpaceBase,
  components: {
    Pagination,
    userDlg
  },
  data () {
    return {
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'blur' }],
        userName: [{ required: true, message: '必填字段', trigger: 'change' }],
        costs: [{ required: true, message: '必填字段', trigger: 'blur' }],
        useTime: [{ required: true, message: '必填字段', trigger: 'blur' }],
        objectives: [{ required: true, message: '必填字段', trigger: 'blur' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      totalItem: {},
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        rangeDate: ''
      },
      communityList: [],
      buildingList: [],
      userInfo: {}
    }
  },
  computed: {
    ...mapGetters('communityMan/userDlg', {
      userId: 'userId',
      userName: 'userName'
    }),
  },
  watch: {
    userId (val) {
      this.dlgData.userId = val
    },

    userName (val) {
      this.dlgData.userName = val
    },
  },
  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {
    // 导出
    exportExcel () {
      let exportParam = JSON.parse(JSON.stringify(this.listQuery))
      exportParam.userId = this.userInfo.id
      exportParam.projectId = this.userInfo.projectId
      let param = Object.keys(exportParam).map(function (key) {
        return encodeURIComponent(key) + "=" + encodeURIComponent(exportParam[key]);
      }).join("&");

      let sendUrl = location.protocol + '//' + location.host + `/saapi/workade/kaoqinyuebaodaochu?` + param
      window.open(sendUrl)
    },

    // 下载
    downloadItem () {
      let url =
        'https://wlines.oss-cn-beijing.aliyuncs.com/jianyitong/template/%E7%97%85%E7%A7%8D%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xls'
      window.open(url)
    },

    // 上传
    uploadItem (file) {
      let name = file.name.split('.')
      let suffix = name[name.length - 1]

      if (suffix !== 'xls' && suffix !== 'xlsx') {
        this.$message({
          type: 'warning',
          message: '只能上传xls/xlsx文件'
        })
        return false
      }

      let loading = this.$loading({
        lock: true,
        text: '导入中',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      let postParam = {
        file
      }

      importExcelDisease(postParam).then(res => {
        loading.close()
        if (res.data.code == 200) {
          this.$message.success('导入成功')
          this.getList()
        } else {
          this.$message({
            type: 'warning',
            message: res.data.msg
          })
        }
      })

      return false
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 显示人员dlg
    showUserDlg () {
      let userId = this.dlgData.userId
      let userName = this.dlgData.userName
      this.$store.commit('communityMan/userDlg/SET_USERID', userId)
      this.$store.commit('communityMan/userDlg/SET_USERNAME', userName)
      this.$store.commit('communityMan/userDlg/SET_DLGSHOW', true)
    },

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    formatList () {
      for (let i of this.list) {
        i.communityName = utils.getNameById(i.communityId, this.communityList)
      }
    },

    // 获取数据
    getList () {
      if (utils.isNull(this.listQuery.communityId)) {
        this.$message.warning("请选择小区")
        return
      }
      this.count++
      this.listLoading = true
      this.listQuery.startDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[0]
      this.listQuery.endDate = utils.isNull(this.listQuery.rangeDate) ? "" : this.listQuery.rangeDate[1]
      repairfundPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data.list))
          this.total = res.data.data.total ? res.data.data.total : 0
          this.totalItem = res.data.data
          this.formatList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData.communityId = this.listQuery.communityId
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          postParam.projectId = this.userInfo.projectId
          postParam.communityName = utils.getNameById(postParam.communityId, this.communityList)
          postParam.imgUrl = postParam.imgUrl.join(",")
          this.dlgLoading = true
          repairfundAddOrUpdate(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              if (!utils.isNull(this.listQuery.communityId)) {
                this.getList()
              }
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgData.imgUrl = this.dlgData.imgUrl ? this.dlgData.imgUrl.split(",") : []
      this.dlgType = type
      this.dlgShow = true
    },


    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      if (flag == 0) {
        title = '确认启用?'
      } else if (flag == 2) {
        title = '确认停用?'
      }
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        repairfundDel(data.id).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/repairFund_').then(res => {
        _this.dlgData['imgUrl'].push(res)
      })
      return false
    },

    // 删除上传照片
    delUploadImg (idx) {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData.imgUrl.splice(idx, 1)
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.table-container {
  height: calc(100% - 150px);
}
</style>


