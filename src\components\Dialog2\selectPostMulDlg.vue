<template>
  <el-dialog
    class="mazhenguo"
    title="选择岗位"
    :close-on-click-modal="false"
    :visible.sync="dlgState"
    append-to-body
    width="800px"
    top="30px"
  >
    <div class="clearfix">
      <el-input
        @keyup.enter.native="searchFunc"
        class="fl"
        placeholder="关键字"
        v-model="listQuery.label"
        style="width: 200px"
      >
        <i
          @click="resetSearchItem(['label'])"
          slot="suffix"
          class="el-input__icon el-icon-error"
        ></i>
      </el-input>

      <el-input
        class="fl ml10"
        style="width: 200px"
        v-model="listQuery.branchName"
        :title="listQuery.branchName"
        placeholder="请选择部门"
        readonly
        @focus="showSbmDlg"
      >
        <i
          slot="suffix"
          @click="resetSearchItem(['branchId', 'branchName'])"
          class="el-input__icon el-icon-error"
        ></i>
      </el-input>

      <el-button
        icon="el-icon-search"
        type="primary"
        class="fl ml10"
        @click="getList"
        size="mini"
        style="padding: 7px 10px"
        >搜索</el-button
      >
      <el-popover
        class="fl"
        placement="bottom-end"
        width="800"
        @show="showPopover"
        trigger="click"
      >
        <el-table
          ref="multipleTable"
          style="height: 500px; overflow: auto"
          :data="selectList"
        >
          <el-table-column
            type="index"
            width="50"
            align="center"
          ></el-table-column>

          <!-- <el-table-column property="name" label="名称"></el-table-column>
          <el-table-column property="code" width="160" label="编码"></el-table-column> -->
          <el-table-column label="岗位名称" prop="label"></el-table-column>
          <el-table-column label="部门" prop="branchName"></el-table-column>
          <el-table-column
            label="在职人"
            prop="userName"
            width="140"
          ></el-table-column>
          <el-table-column property="" label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button
                @click="popRemoveRow(scope.row)"
                type="danger"
                size="mini"
                icon="el-icon-delete"
                plain
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button
          class="fr search-right-btn"
          type="success"
          slot="reference"
          icon="el-icon-arrow-down"
          >查看已选</el-button
        >
      </el-popover>
    </div>

    <el-table
      height="400"
      ref="tableRef"
      class="m-small-table mt10"
      v-loading="listLoading"
      :key="tableKey"
      :data="list"
      border
      fit
      highlight-current-row
      @select="tableSelectChange"
      @select-all="tableSelectAll"
      @row-click="tableRowClick"
    >
      <el-table-column type="selection" width="55" align="center">
      </el-table-column>

      <el-table-column label="#" align="center" width="60">
        <template slot-scope="scope">
          {{ (listQuery.page - 1) * listQuery.size + scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column label="岗位名称" prop="label"></el-table-column>
      <el-table-column label="部门" prop="branchName"></el-table-column>
      <el-table-column
        label="在职人"
        prop="userName"
        width="140"
      ></el-table-column>

      <!-- <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button @click="showDlg('info', scope.row)" icon="el-icon-document" size="mini" type="primary" title="详情" plain
            >详情</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      class="mt10"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="getList"
    />

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDlg" icon="el-icon-back">返回</el-button>
      <el-button
        v-if="dlgType != 'info'"
        :loading="dlgSubLoading"
        type="success"
        @click="dlgSubFunc"
        icon="el-icon-check"
      >
        <span v-if="dlgSubLoading">提交中...</span>
        <span v-else>确定</span>
      </el-button>
    </div>

    <selectBmDlg
      :dlgState0="dlgSbmState"
      :dlgType="dlgSbmType"
      :dlgQuery="dlgSbmQuery"
      :dlgSelectData="dlgSbmSelectData"
      @closeDlg="closeSbmDlg"
      @backFunc="dlgSbmbackFunc"
    />
  </el-dialog>
</template>
<script>
// 组件
// 工具
import { uploadImg, uploadImg2 } from "@/utils/uploadImg";
import Pagination from "@/components/Pagination";
import selectBmDlg from "@/components/Dialog2/selectBmDlg";
// 接口
import * as utils from "@/utils";
import * as regUtils from "@/utils/regUtils";

import { postAction, getAction } from "@/api";

let listQueryEmpty = {
  label: "", //	模糊查询	body	false	string
  page: 1,
  size: 20,

  branchId: "",
  branchName: "",
  postStatus: "" //岗位状态 0未绑定员工 1 已绑定员工
};

export default {
  components: {
    Pagination,
    selectBmDlg
  },
  props: {
    dlgType: {
      type: String,
      default: "add"
    },
    dlgQuery: {
      type: Object,
      default: {}
    },
    dlgState0: {
      type: Boolean,
      default: false
    },
    dlgData0: {},
    selectList0: {}
  },
  watch: {
    dlgState0(val) {
      this.dlgState = val;
    },
    dlgState(val) {
      if (val) {
        setTimeout(() => {
          this.listQuery = { ...listQueryEmpty, ...this.dlgQuery };

          if (this.selectList0) {
            this.selectList = this.selectList0;
          } else {
            this.selectList = [];
          }
          this.getList();
        }, 50);
      } else {
        this.$emit("closeDlg");
      }
    }
  },
  data() {
    return {
      userInfo: JSON.parse(window.localStorage.userInfo),
      selectList: [], // 选中

      tableKey: 0,
      list: [],
      total: 0,
      listLoading: false,
      listQuery: JSON.parse(JSON.stringify(listQueryEmpty)),

      dlgSubLoading: false,

      // 弹窗
      dlgState: false,
      dlgSbmQuery: {},
      dlgSbmState: false,
      dlgSbmType: "", // 弹框状态add, edit
      dlgSbmSelectData: { id: "", label: "" }
    };
  },
  created() {
    // this.getDataDict()
  },
  methods: {
    showSbmDlg() {
      if (this.listQuery.branchName) {
        this.dlgSbmSelectData = {
          id: this.listQuery.branchId,
          label: this.listQuery.branchName
        };
      } else {
        this.dlgSbmSelectData = { id: "", label: "" };
      }
      this.dlgSbmState = true;
    },
    // 关闭弹窗
    closeSbmDlg() {
      this.dlgSbmState = false;
    },
    dlgSbmbackFunc(data) {
      console.log("返回数据", data);
      this.listQuery.branchId = data.id;
      this.listQuery.branchName = data.label;
      this.searchFunc();
    },
    // 数据字典
    getDataDict() {
      let keyList = (this.keyMap = [
        // { dbKey: "sex", pageSelectKey: "sexSelect", pageKey: "sex", pageName: 'sexText' },
        { dbKey: "leaveCause", pageSelectKey: "qjsySelect" }, // 请假事由
        { dbKey: "timeOffType", pageSelectKey: "jqlxSelect" } // 假期类型
      ]);
      utils.getDbItems(this, keyList);
    },
    // 弹窗提交 ------
    dlgSubFunc() {
      this.$emit("backFunc", this.selectList);
      this.closeDlg();
    },
    closeDlg() {
      this.$emit("closeDlg");
    },

    // ------ 列表
    resetSearchItem(arr) {
      for (let item of arr) {
        this.listQuery[item] = "";
      }
      this.searchFunc();
    },
    searchFunc() {
      this.listQuery.page = 1;
      this.getList();
    },
    getList() {
      this.list = [];

      let sendObj = { ...this.listQuery, ...this.dlgQuery };
      sendObj.label = sendObj.label.trim();

      this.listLoading = true;
      postAction("/sys/pagePostUser", sendObj).then(res0 => {
        let res = res0.data;
        this.listLoading = false;
        if (res.code == 200) {
          if (utils.isNull(res.data)) {
            this.list = [];
            this.total = 0;
          } else {
            let list0 = res.data || [];
            for (let item of list0) {
              item.id = item.postId;
            }
            this.list = JSON.parse(JSON.stringify(list0));

            this.total = res.page.total;

            this.$nextTick(() => {
              this.$refs.tableRef.doLayout();
            });

            // 如果复选设置表格勾选
            let list = this.list;
            this.$nextTick(() => {
              console.log("this.selectList", this.selectList);
              if (this.selectList.length > 0) {
                for (let item of list) {
                  let isHas = this.selectList.some(row => row.id == item.id);
                  if (isHas) {
                    this.$refs.tableRef.toggleRowSelection(item, true);
                  } else {
                    this.$refs.tableRef.toggleRowSelection(item, false);
                  }
                }
              } else {
                this.$refs.tableRef.clearSelection();
              }
            });
          }
        } else {
          this.total = 0;
          this.$message({
            type: "warning",
            message: res.msg
          });
        }
      });
    },

    // -- << 已选弹窗
    showPopover() {
      // for (let item of this.selectList) {
      //   this.$refs.multipleTable.toggleRowSelection(item, true)
      // }
    },
    // 已选删除
    popRemoveRow(row) {
      console.log("----row", row);
      let selectList = this.selectList.filter(item => {
        return item.id != row.id;
      });
      this.selectList = JSON.parse(JSON.stringify(selectList));

      // 选择商品弹窗，复选框同步改变
      console.log("this.list", this.list);
      console.log("selectList", this.selectList);
      for (let item of this.list) {
        let isHas = row.id == item.id;
        console.log("----isHas", isHas);
        if (isHas) {
          this.$refs.tableRef.toggleRowSelection(item, false);
        }
      }
    },
    // >> 已选弹窗

    // 点击行

    // 点击复选框
    tableSelectChange(arr, row) {
      this.tableCheckBaseFunc(row);
    },
    // 点击行
    tableRowClick(row, column, event) {
      // let disabledCol = ['商品名称','商品编码','图片']
      // if (disabledCol.indexOf(column.label) >=0) return false
      // this.$refs.tableRef.toggleRowSelection(row)
      // this.tableCheckBaseFunc(row)
    },
    // 单行操作方法
    tableCheckBaseFunc(row) {
      let isCheck = !this.selectList.some(item => item.id == row.id); // true-勾选状态，false-取下选择状态
      console.log("isCheck", isCheck);
      // 判断是否是勾选状态
      if (isCheck) {
        // 勾选
        this.selectList.push(row);
      } else {
        // 取消选择
        let selectList = this.selectList.filter(item => {
          return item.id != row.id;
        });
        this.selectList = JSON.parse(JSON.stringify(selectList));
      }
    },
    // 表格 全选
    tableSelectAll(arr) {
      let len = arr.length;
      // console.log(arr.length)
      // 长度为0 取消全选，将list中所有数据，从selectList中移除
      // 长度不为0，全选，将list中所有数据，追加到 selectList中
      let list = JSON.parse(JSON.stringify(this.list));
      let selectList = JSON.parse(JSON.stringify(this.selectList));
      if (len == 0) {
        let newList = [];
        for (let item of selectList) {
          let hasId = list.some(item2 => item2.id == item.id);
          if (!hasId) {
            newList.push(item);
          }
        }
        selectList = JSON.parse(JSON.stringify(newList));
      } else {
        for (let item of list) {
          let hasId = selectList.some(item2 => item2.id == item.id);
          if (!hasId) {
            selectList.push(item);
          }
        }
      }
      // console.log('完美的选中数据', selectList)
      this.selectList = selectList;
    }
    // >> 新 复选
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped></style>
