<template>
  <!-- 考勤管理 - 考勤组管理 -->
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入考勤组名称/姓名' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
        <el-button icon='el-icon-plus' type="primary" size='mini' @click='addItem'>新增</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="考勤组名称">
          <template slot-scope="scope">
            <span>{{ scope.row.groupName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="显示名称">
          <template slot-scope="scope">
            <span>{{ scope.row.scheduleName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="午休时间" width="120px" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.hasNoon == 0">无午休</span>
            <span v-else>{{ scope.row.nooningStart }} ~ {{ scope.row.nooningEnd }}</span>
          </template>
        </el-table-column>

        <el-table-column label="中间卡" width="120px" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.adeGroupMiddles.length == 0">无中卡</span>
            <template v-else>
              <div v-for="item in scope.row.adeGroupMiddles" :key="item.id">
                {{ item.middleStartTime }} ~ {{ item.middleEndTime }}
              </div>
            </template>
          </template>
        </el-table-column>

        <el-table-column label="绑定人员" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-tag v-for="item in scope.row.adeGroupUsers" :key="item.id">{{ item.userName }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="260" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit" plain @click="editItem(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' :title="dlgType === 'ADD'?'新增考勤组':'编辑考勤组'" :visible.sync="dlgShow" width='900px' top="30px" append-to-body>

      <el-form ref="dlgForm" :rules="rules" :model="dlgData" label-position="right" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="考勤组名称" prop="groupName">
              <el-input v-model="dlgData.groupName" placeholder="请输入考勤组名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="在排班表中显示名称" prop="scheduleName">
              <el-input v-model="dlgData.scheduleName" placeholder="请输入在排班表中显示名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="上班时间" prop="workTime">
              <el-time-picker placeholder="上班时间" v-model="dlgData.workTime" format="HH:mm" value-format="HH:mm">
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="允许最早打卡" prop="workTimeFirst">
              可提前 <el-input-number v-model="dlgData.workTimeFirst" :controls='false' :min="0" :precision="0" :step="1"></el-input-number> 分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="统计工时开始时间" prop="workHourTime">
          <el-time-picker placeholder="统计工时开始时间" v-model="dlgData.workHourTime" format="HH:mm" value-format="HH:mm">
          </el-time-picker>
        </el-form-item>
        <el-form-item label="午休" prop="hasNoon">
          <el-time-picker placeholder="午休开始时间" v-model="dlgData.nooningStart" :picker-options="{selectableRange:`00:00:00-${dlgData.nooningEnd ? dlgData.nooningEnd + ':00' : '23:59:59'}`}" format="HH:mm" value-format="HH:mm" style="width:180px;">
          </el-time-picker>
          ~
          <el-time-picker placeholder="午休结束时间" v-model="dlgData.nooningEnd" :picker-options="{selectableRange:`${dlgData.nooningStart ? dlgData.nooningStart + ':00' : '00:00:00'}-23:59:59`}" format="HH:mm" value-format="HH:mm" style="width:180px;">
          </el-time-picker>
          <el-checkbox v-model="dlgData.noonWorkHour">午休统计工时</el-checkbox>
        </el-form-item>
        <el-form-item label="中间打卡次数" prop="punchMiddleNum">
          <el-input-number @change="punchMiddleNumChange" v-model="dlgData.punchMiddleNum" :controls='false' :min="0" :precision="0" :step="1"></el-input-number> 次
        </el-form-item>

        <el-form-item v-for="(item, index) in dlgData.adeGroupMiddles" :key="index" :label="`第${item.punchNum}次中卡`">
          <el-time-picker placeholder="中间卡开始时间" v-model="item.middleStartTime" :picker-options="{selectableRange:`00:00:00-${item.middleEndTime ? item.middleEndTime + ':00' : '23:59:59'}`}" format="HH:mm" value-format="HH:mm">
          </el-time-picker>
          ~
          <el-time-picker placeholder="中间卡结束时间" v-model="item.middleEndTime" :picker-options="{selectableRange:`${item.middleStartTime ? item.middleStartTime + ':00' : '00:00:00'}-23:59:59`}" format="HH:mm" value-format="HH:mm">
          </el-time-picker>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="下班时间" prop="leaveTime">
              <el-time-picker placeholder="下班时间" v-model="dlgData.leaveTime" format="HH:mm" value-format="HH:mm">
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="允许最晚打卡" prop="levelTimeLatest">
              可延后 <el-input-number v-model="dlgData.levelTimeLatest" :controls='false' :min="0" :precision="0" :step="1"></el-input-number> 分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input :autosize="{ minRows: 2, maxRows: 4}" v-model="dlgData.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
        <el-divider content-position="left">绑定人员 <el-button @click="showUserDlgMul" type='primary' icon='el-icon-edit'>更改绑定</el-button>
        </el-divider>
        <el-table class='m-small-table' :data="dlgData.adeGroupUsers" border fit highlight-current-row>
          <el-table-column label="序号" type="index" align="center" width="60">
          </el-table-column>

          <el-table-column label="姓名">
            <template slot-scope="scope">
              <span>{{ scope.row.label }}</span>
            </template>
          </el-table-column>

          <el-table-column label="部门">
            <template slot-scope="scope">
              <span>{{ scope.row.branchName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="排班时间" width="244px">
            <template slot-scope="scope">
              <span v-if="scope.row.type === 'EDIT'">
                {{scope.row.updateDate}}
              </span>
              <el-date-picker v-else :picker-options="pickerOptions" v-model="scope.row.updateDate" value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="date" placeholder="排班日期">
              </el-date-picker>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="danger" size="mini" icon="el-icon-delete" plain @click="delUser(scope.$index)">删除</el-button>
            </template>
          </el-table-column>

        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>
    <userDlgMul />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findGroupByDynamic, saveAdeGroup, delgroupById } from '@/api/attendanceMan/groupMan.js'
import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import userDlgMul from '@/components/Dialog/platformMan/userDlgMul'

let dlgDataEmpty = {
  id: '',
  groupName: '',
  hasNoon: 1,
  leaveTime: '',
  levelTimeLatest: '',
  noonWorkHour: false,
  nooningEnd: '',
  nooningStart: '',
  punchMiddleNum: '',
  scheduleName: '',
  workHourTime: '',
  workTime: '',
  workTimeFirst: '',
  remark: '',
  adeGroupMiddles: [],
  adeGroupUsers: []
}


export default {
  components: {
    Pagination,
    userDlgMul
  },
  data () {
    return {
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - (1000 * 60 * 60 * 24);
        },
      },

      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',  // ADD\EDIT

      rules: {
        groupName: [{ required: true, message: '必填字段', trigger: 'blur' }],
        hasNoon: [{ required: true, message: '必填字段', trigger: 'change' }],
        leaveTime: [{ required: true, message: '必填字段', trigger: 'change' }],
        levelTimeLatest: [{ required: true, message: '必填字段', trigger: 'change' }],
        noonWorkHour: [{ required: true, message: '必填字段', trigger: 'change' }],
        nooningEnd: [{ required: true, message: '必填字段', trigger: 'change' }],
        nooningStart: [{ required: true, message: '必填字段', trigger: 'change' }],
        punchMiddleNum: [{ required: true, message: '必填字段', trigger: 'change' }],
        scheduleName: [{ required: true, message: '必填字段', trigger: 'blur' },
        { min: 0, max: 2, message: '最多 2 个字符', trigger: 'blur' }],
        workHourTime: [{ required: true, message: '必填字段', trigger: 'change' }],
        workTime: [{ required: true, message: '必填字段', trigger: 'change' }],
        workTimeFirst: [{ required: true, message: '必填字段', trigger: 'change' }],
      },

      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
      },
    }
  },
  computed: {
    ...mapGetters('platformMan/userDlgMul', {
      userList: 'list'
    }),
  },
  watch: {
    userList (val) {
      let list = JSON.parse(JSON.stringify(val))
      let users = this.dlgData.adeGroupUsers
      let idList = users.map(item => item.id)
      for (let i of list) {
        if (!idList.includes(i.id)) {
          this.dlgData.adeGroupUsers.push({
            id: i.id,
            label: i.label,
            userId: i.id,
            userName: i.label,
            branchId: i.departmentId,
            branchName: i.departmentName,
            updateDate: '',
            type: 'ADD'
          })
        }
      }
      this.$forceUpdate()
    },
  },

  created () {

  },

  methods: {
    // 显示人员对话框
    showUserDlgMul () {
      this.$store.commit('platformMan/userDlgMul/SET_LIST', JSON.parse(JSON.stringify(this.dlgData.adeGroupUsers)))
      this.$store.commit('platformMan/userDlgMul/SET_DLGSHOW', true)
    },

    // 中间打卡次数
    punchMiddleNumChange () {
      this.dlgData.adeGroupMiddles = []
      for (let i = 1; i <= this.dlgData.punchMiddleNum; i++) {
        this.dlgData.adeGroupMiddles.push({
          middleEndDay: "1",
          middleEndTime: '',
          middleStartDay: '1',
          middleStartTime: '',
          punchNum: i
        })
      }
      this.$forceUpdate()
    },

    // 删除人员
    delUser (idx) {
      this.dlgData.adeGroupUsers.splice(idx, 1)
    },


    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      findGroupByDynamic(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },

    // 显示弹窗
    addItem () {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgType = 'ADD'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })
    },

    // 验证表单
    validate () {
      for (let i of this.dlgData.adeGroupMiddles) {
        if (utils.isNull(i.middleStartTime) || utils.isNull(i.middleEndTime)) {
          this.$message.error(`第${i.punchNum}次中间打卡时间未设置`)
          return false
        }
      }
      return true
    },

    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          if (!this.validate()) {
            return
          }
          let postParam = JSON.parse(JSON.stringify(this.dlgData))
          console.log(postParam);
          postParam.noonWorkHour = postParam.noonWorkHour ? '1' : '0'
          this.dlgLoading = true
          saveAdeGroup(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              // this.getList()
              // this.dlgShow = false
              // this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgData.noonWorkHour = this.dlgData.noonWorkHour == 1
      for (let i of this.dlgData.adeGroupUsers) {
        i.type = 'EDIT'
        i.id = i.userId
        i.label = i.userName
      }
      this.dlgType = 'EDIT'
      this.dlgShow = true
      this.$nextTick(() => {
        this.$refs['dlgForm'].clearValidate()
      })

    },

    // 删除
    delItem (data) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delgroupById(data.id).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })

    },
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-tag {
  margin-right: 4px;
}
</style>


