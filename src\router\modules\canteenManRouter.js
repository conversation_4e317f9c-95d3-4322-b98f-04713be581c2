/** 场地分支 **/
import Layout from "@/views/layout/Layout";
// component: () => import("@/views/hrMan/orgPlan/index"),
const canteenRouter = {
  path: "/canteenMan",
  component: Layout,
  redirect: "/examMan/course/list",
  name: "canteenMan",
  meta: {
    title: "食堂管理",
    icon: "canteenMan",
    roles: ["shitangguanli"],
  },

  children: [
    {
      path: "foodMain",
      component: () => import("@/views/canteenMan/foodMain/index"),
      name: "foodMain",
      meta: {
        title: "菜品维护",
        roles: ["st_caipinweihu"],
      },
      children: [],
    },
    {
      path: "recipeMain",
      component: () => import("@/views/canteenMan/recipeMain/index"),
      name: "recipeMain",
      meta: {
        title: "菜谱维护",
        roles: ["st_caipuweihu"],
      },
      children: [],
    },
    {
      path: "foodReserveMain",
      component: () => import("@/views/canteenMan/foodReserveMain/index"),
      name: "foodReserveMain",
      meta: {
        title: "食品预定维护",
        roles: ["st_shipinyudingweihu"],
      },
      children: [],
    },
    {
      path: "statisticalMan",
      component: () => import("@/views/heatMan/dataCollect/index"),
      name: "statisticalMan",
      meta: {
        title: "统计管理",
        roles: ["st_tongjiguanli"],
      },
      children: [
        {
          path: "foodReviewStatistics",
          component: () =>
            import(
              "@/views/canteenMan/statisticalMan/foodReviewStatistics/index"
            ),
          name: "foodReviewStatistics",
          meta: {
            title: "菜品点评统计",
            roles: ["st_caipindianpingtongji"],
          },
          children: [],
        },

        {
          path: "foodReservestatistics",
          component: () =>
            import(
              "@/views/canteenMan/statisticalMan/foodReservestatistics/index"
            ),

          name: "foodReservestatistics",
          meta: {
            title: "食品预定统计",
            roles: ["st_shipinyudingtongji"],
          },
          children: [],
        },
        {
          path: "userReservaeStatistics",
          component: () =>
            import(
              "@/views/canteenMan/statisticalMan/userReservaeStatistics/index"
            ),

          name: "userReservaeStatistics",
          meta: {
            title: "用户预定统计",
            roles: ["st_yonghuyudingtongji"],
          },
          children: [],
        },
      ],
    },
    //
  ],
};

export default canteenRouter;
