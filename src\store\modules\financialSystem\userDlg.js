// 用户dlg组件

const userDlg = {
  namespaced: true,

  state: {
    dlgShow: false,

    dlgType: "",

    userId: "",

    userName: "",

    userInfo: {},

    dlgQuery: {}
  },

  getters: {
    dlgShow: state => state.dlgShow,

    dlgType: state => state.dlgType,

    userId: state => state.userId,

    userName: state => state.userName,

    userInfo: state => state.userInfo
  },
  mutations: {
    SET_DLGSHOW: (state, val) => {
      state.dlgShow = val;
    },

    SET_DLGTYPE: (state, val) => {
      state.dlgType = val;
    },

    SET_USERID: (state, val) => {
      state.userId = val;
    },

    SET_USERNAME: (state, val) => {
      state.userName = val;
    },

    SET_USERINFO: (state, val) => {
      state.userInfo = val;
    },

    SET_DLGQUERY: (state, val) => {
      state.dlgQuery = val;
    }
  },

  actions: {}
};

export default userDlg;
