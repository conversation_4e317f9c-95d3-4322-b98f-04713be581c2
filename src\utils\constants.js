export const FORM_MAP = {
  // 1 物资需求单

  DemandStrategy: {
    title: "物资需求单",
    formDlgWidth: 1100,
    roleKey: "DemandStrategy"
  },
  RefundStrategy: {
    title: "采购退货单",
    formDlgWidth: 1100,
    roleKey: "RefundStrategy"
  },
  ProfitLoseStrategy: {
    title: "损益单",
    formDlgWidth: 1100
  },
  PurchaseStrategy: {
    title: "采购计划单",
    formDlgWidth: 1100,
    roleKey: "PurchaseStrategy"
  }
};

export const getFormKeyList = () => {
  let map = FORM_MAP;
  let arr = [];
  for (let key in map) {
    arr.push(key);
  }
  return arr;
};

export let TYJK_CONTANTS = {
  sickTypeList: [
    // 来我院目的
    { id: "1", name: "就诊" },
    { id: "2", name: "陪诊" },
    { id: "3", name: "其他" }
  ],

  medicalSymptomsList: [
    // 目前是否有以下症状
    { id: "1", name: "A.发热" },
    { id: "2", name: "B.干咳" },
    { id: "3", name: "C.乏力" },
    { id: "4", name: "D.咽痛" },
    { id: "5", name: "E.鼻塞" },
    { id: "6", name: "F.流鼻涕" },
    { id: "7", name: "G.腹泻" },
    { id: "8", name: "H.味觉改变" },
    { id: "9", name: "I.嗅觉改变" },
    { id: "10", name: "J.肌肉酸痛" },
    { id: "11", name: "K.眼部结膜炎" },
    { id: "12", name: "L.没有以上所有症状" }
  ],

  isDetectionList: [
    // 一周内是否做过下类哪项相关检测
    { id: "1", name: "核酸" },
    { id: "2", name: "抗体" },
    { id: "3", name: "血常规" },
    { id: "4", name: "肺CT" }
  ]
};
