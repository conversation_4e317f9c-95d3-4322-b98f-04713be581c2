<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form inline :model="listQuery" @submit.native.prevent>
        <el-form-item label="选择小区">
          <el-select v-model="listQuery.communityId" filterable clearable placeholder="请选择小区" @change="communityChange">
            <el-option v-for="item in communityList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报修类型">
          <el-select v-model="listQuery.repairType" filterable clearable placeholder="请选择报修类型">
            <el-option v-for="item in repairTypeListQuery" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input @keyup.enter.native='getList' placeholder='请输入报修人、报修电话、位置' v-model="listQuery.label">
            <i slot="suffix" @click="resetSearchItem(['label'])" class="el-input__icon el-icon-error"></i>
          </el-input>
        </el-form-item>
        <el-form-item label="报修状态">
          <el-select v-model="listQuery.isAudit" filterable clearable placeholder="请选择报修状态">
            <el-option v-for="item in auditList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button icon='el-icon-search' type="success" size='mini' @click='getList'>搜索</el-button>
      </el-form>
    </div>
    <div class="table-container">
      <el-table class='m-small-table' height="100%" v-loading="listLoading" :data="list" border fit highlight-current-row :empty-text="count == 0 ? '请搜索' : '暂无数据'">
        <el-table-column label="序号" type="index" align="center" width="60">
        </el-table-column>

        <el-table-column label="报修人">
          <template slot-scope="scope">
            <span>{{ scope.row.repairName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="联系方式">
          <template slot-scope="scope">
            <span>{{ scope.row.tel }}</span>
          </template>
        </el-table-column>

        <el-table-column label="预约时间">
          <template slot-scope="scope">
            <span>{{ scope.row.appointmentTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="报修类型">
          <template slot-scope="scope">
            <span>{{ scope.row.repairTypeText }}</span>
          </template>
        </el-table-column>

        <el-table-column label="维修内容">
          <template slot-scope="scope">
            <span>{{ scope.row.context }}</span>
          </template>
        </el-table-column>

        <el-table-column label="位置">
          <template slot-scope="scope">
            <div>小区:{{ scope.row.communityName }}</div>
            <div>房屋:{{ scope.row.fangwu }}</div>
          </template>
        </el-table-column>

        <el-table-column label="发生时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="维修应收费用(元)">
          <template slot-scope="scope">
            <span>{{ scope.row.priceScope }}</span>
          </template>
        </el-table-column>

        <el-table-column label="维修实收费用(元)">
          <template slot-scope="scope">
            <span>{{ scope.row.payMoney }}</span>
          </template>
        </el-table-column>

        <el-table-column label="财务确认费用(元)">
          <template slot-scope="scope">
            <span>{{ scope.row.auditMoney }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-if="scope.row.isAudit == 1" type="success" size="mini" icon="el-icon-view" plain @click="editItem(scope.row, 'VIEW')">详情</el-button>
            <el-button v-else type="primary" size="mini" icon="el-icon-check" plain @click="editItem(scope.row, 'EDIT')">确认</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-container">
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    </div>

    <el-dialog :close-on-click-modal='false' title="确认/查看报修费用" :visible.sync="dlgShow" width='600px' append-to-body top="30px">

      <el-form ref="dlgForm" :disabled="dlgType === 'VIEW'" :rules="rules" :model="dlgData" label-position="right" label-width="150px">

        <el-form-item label="应收费用(元)：" prop="priceScope">
          {{dlgData.priceScope}}
        </el-form-item>
        <el-form-item label="实收费用(元)：" prop="payMoney">
          {{dlgData.payMoney}}
        </el-form-item>
        <el-form-item label="财务确认费用(元)：" prop="auditMoney">
          <el-input-number v-model="dlgData.auditMoney" :controls='false' :min="0" :precision="2" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item label="备注：" prop="auditRemark">
          <el-input type="textarea" :autosize="{minRows: 4, maxRows: 6}" v-model="dlgData.auditRemark" placeholder="请输入备注" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dlgShow = false" icon='el-icon-back'>取消</el-button>
        <el-button v-if="dlgType !== 'VIEW'" type='success' :loading='dlgLoading' @click="subDlg" icon="el-icon-check">
          <span v-if='dlgLoading'>提交中</span>
          <span v-else>提交</span>
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Cookie from 'js-cookie'
import { mapGetters } from 'vuex'
import { communityPage, cofloorCommunity, buildingunitFloor, buildingroomPage } from '@/api/communityMan'

import {
  repairSettingPage,
  repairpoolPage,
  baoxiushoufeiqueren,
  updateRepairPoolFlag,
  repairpoolHuidan,
  repairpoolFindOne,
} from '@/api/repairMan'

import * as utils from '@/utils'
import Pagination from '@/components/Pagination'
import { uploadImg } from '@/utils/uploadImg'
import WorkSpaceBase from '@/components/WorkSpace/WorkSpaceBase'

let dlgDataEmpty = {
  id: 0,
  priceScope: '',
  payMoney: '',
  auditMoney: '',
  userName: '',
  userId: '',
  auditRemark: '',
}


export default {
  name: 'repairConfirm',
  extends: WorkSpaceBase,
  components: {
    Pagination,
  },
  data () {
    return {

      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - (1000 * 60 * 60 * 24);
        },
      },
      // 弹窗 状态
      dlgShow: false,  // 新增
      dlgType: '',    // ADD\EDIT
      dlgTitle: '', // 标题

      rules: {
        communityId: [{ required: true, message: '必填字段', trigger: 'change' }],
        danyuanId: [{ required: true, message: '必填字段', trigger: 'change' }],
        fangwuId: [{ required: true, message: '必填字段', trigger: 'change' }],
        loudongId: [{ required: true, message: '必填字段', trigger: 'change' }],
        auditMoney: [{ required: true, message: '必填字段', trigger: 'blur' }],
        context: [{ required: true, message: '必填字段', trigger: 'blur' }],
        repairObjType: [{ required: true, message: '必填字段', trigger: 'change' }],
        repairType: [{ required: true, message: '必填字段', trigger: 'change' }],
        appointmentTime: [{ required: true, message: '必填字段', trigger: 'change' }],
        userName: [{ required: true, message: '必填字段', trigger: 'change' }],
        remark: [{ required: true, message: '必填字段', trigger: 'blur' }],
        tel: [
          { required: true, message: '必填字段', trigger: 'blur' },
          {
            pattern: /^((\d{7,8})|(0\d{2,3}-\d{7,8})|(1[356789]\d{9}))$/,
            message: '号码格式有误！',
            trigger: 'blur'
          }
        ],
      },
      stateMap: {
        "0": '提交',
        "1": '派人',
        "2": '接单',
        "3": '回单',
        "4": '评价',
        "5": '回访',
        "8": '修改',
        "9": '作废',
        "10": '重派给其他人',
      },
      // 弹窗数据
      dlgData: JSON.parse(JSON.stringify(dlgDataEmpty)),
      count: 0,
      list: [],
      total: 0,
      listLoading: false,
      dlgLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        label: '',
        communityId: '',
        repairType: '',
        logType: '1',
        isAudit: '0,1'
      },
      userInfo: {},
      communityList: [],
      buildingList: [],
      unitList: [],
      roomList: [],
      repairTypeList: [],
      repairTypeListQuery: [],
      objTypeList: [
        {
          id: '1',
          name: '小区'
        },
        {
          id: '2',
          name: '楼栋'
        },
        {
          id: '3',
          name: '单元'
        },
        {
          id: '4',
          name: '房屋'
        },
      ],
      auditList: [
        {
          id: '0,1',
          name: '全部'
        },
        {
          id: '0',
          name: '待确认'
        },
        {
          id: '1',
          name: '已确认'
        }
      ],
    }
  },

  created () {
    this.getCommunityList()
    this.userInfo = JSON.parse(window.localStorage.userInfo)
  },

  methods: {


    communityChange () {
      let communityId = this.dlgData.communityId
      if (this.dlgShow) {
        this.dlgData.loudongId = ""
        this.dlgData.danyuanId = ""
        this.dlgData.repairType = ""
      } else {
        communityId = this.listQuery.communityId
        this.listQuery.repairType = ""
      }
      this.getBuildingList(communityId)
      this.getRepairTypeList()
      this.getRoomList()
    },

    floorChange () {
      let floorId = this.dlgData.loudongId
      this.dlgData.danyuanId = ""
      this.getUnitList(floorId)
      this.getRoomList()
    },

    unitChange () {
      this.getRoomList()
    },

    repairTypeChange () {
      let item = this.repairTypeList.filter(item => {
        return item.id = this.dlgData.repairType
      })
      if (item.length > 0) {
        this.dlgData.priceScope = item[0]['priceScope']
      }
    },

    // 获取小区列表
    getCommunityList () {
      let postParam = {
        page: 1,
        limit: 200
      }
      communityPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.communityList = res.data.data
        }
      })
    },

    // 获取楼栋列表
    getBuildingList (id) {
      if (utils.isNull(id)) {
        return
      }
      cofloorCommunity(id).then(res => {
        if (res.data.code == 200) {
          this.buildingList = res.data.data
        }
      })
    },

    // 获取单元列表
    getUnitList (id) {
      if (utils.isNull(id)) {
        return
      }
      buildingunitFloor(id).then(res => {
        if (res.data.code == 200) {
          this.unitList = res.data.data
        }
      })
    },

    // 获取房屋列表
    getRoomList () {
      if (this.dlgData.repairObjType != 4) {
        return
      }
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.dlgData.communityId,
        floorId: this.dlgData.loudongId,
        unitId: this.dlgData.danyuanId
      }
      buildingroomPage(postParam).then(res => {
        if (res.data.code == 200) {
          this.roomList = res.data.data
        }
      })
    },

    // 获取报修类型
    getRepairTypeList () {
      let postParam = {
        page: 1,
        limit: 200,
        communityId: this.listQuery.communityId
      }
      if (this.dlgShow) {
        postParam.communityId = this.dlgData.communityId
      }
      repairSettingPage(postParam).then(res => {
        if (res.data.code == 200) {
          if (this.dlgShow) {
            this.repairTypeList = res.data.data
          } else {
            this.repairTypeListQuery = res.data.data
          }
        }
      })
    },

    resetSearchItem (arr) {
      for (let item of arr) {
        this.listQuery[item] = ''
      }
    },

    formatList () {

    },

    // 获取数据
    getList () {
      this.count++
      this.listLoading = true
      repairpoolPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.code == 200) {
          this.list = JSON.parse(JSON.stringify(res.data.data))
          this.formatList()
          this.total = res.data.page ? res.data.page.total : 0
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },


    // 弹窗提交
    subDlg () {
      this.$refs['dlgForm'].validate((valid) => {
        if (valid) {
          let postParam = {}
          postParam.projectId = this.userInfo.projectId
          postParam.repairId = this.dlgData.id
          postParam.auditMoney = this.dlgData.auditMoney
          postParam.auditRemark = this.dlgData.auditRemark
          postParam.userId = Cookie.get('userId')
          postParam.userName = Cookie.get('userName')
          postParam.isAudit = 1
          this.dlgLoading = true
          baoxiushoufeiqueren(postParam).then(res => {
            this.dlgLoading = false
            if (res.data.code == 200) {
              this.getList()
              this.dlgShow = false
              this.$message.success(res.data.msg)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    },

    // 编辑
    editItem (data, type) {
      this.dlgData = JSON.parse(JSON.stringify(dlgDataEmpty))
      this.dlgData = Object.assign(this.dlgData, JSON.parse(JSON.stringify(data)))
      this.dlgType = type
      this.dlgShow = true
    },

    // 启用停用
    delItem (data, flag) {
      let title = '确认删除?'
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateRepairPoolFlag(data.id, flag).then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
      })
    },

    // 上传对话框图片
    beforeUpload (file) {
      let _this = this
      uploadImg(file, 'jianyitong/web/ownerInfo_').then(res => {
        _this.dlgData['photo'] = res
      })
      return false
    },

    // 删除上传照片
    delUploadImg () {
      let _this = this
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.dlgData['photo'] = ''
      })
    },

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-image {
  width: 150px;
  height: 150px;
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>


