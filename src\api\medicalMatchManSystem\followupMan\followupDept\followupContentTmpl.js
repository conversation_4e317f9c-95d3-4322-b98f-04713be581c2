import request from '@/utils/request'

/*
*随访内容模板
*/

// 分页查询 
export function findContentTemplateDynamic(data) 
{
	return request({
		url: `/follow/findContentTemplateDynamic`,
		method: 'post',
		data
	})
}

// 删除
export function updateContentTemplate(data)
{
	return request({
		url: `/follow/updateContentTemplate`,
		method: 'post',
		data
	})
}

// 新增/修改
export function saveOrUContentTemplate(data)
{
	return request({
		url: `/follow/saveOrUContentTemplate`,
		method: 'post',
		data
	})
}
