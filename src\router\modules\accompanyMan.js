/** 陪诊陪检 **/

import Layout from "@/views/layout/Layout";

const accompanyManRouter = {
  path: "/accompanyMan",
  component: Layout,
  name: "accompanyMan",
  meta: {
    title: "陪诊陪检",
    icon: "pzpj",
    roles: ["peizhenpeijian"]
  },
  children: [
    {
      path: "accompanySetup",
      component: () => import("@/views/accompanyMan/accompanySetup"),
      name: "陪诊陪检设置",
      meta: {
        title: "陪诊陪检设置",
        roles: ["peizhenpeijianshezhi"]
      },
      children: []
    },
    {
      path: "orderMan",
      component: () => import("@/views/accompanyMan/orderMan"),
      name: "订单管理",
      meta: {
        title: "订单管理",
        roles: ["dingdanguanli"]
      }
    },
    {
      path: "orderAssign",
      component: () => import("@/views/accompanyMan/orderAssign"),
      name: "订单指派",
      meta: {
        title: "订单指派",
        roles: ["dingdanzhipai"]
      }
    }
  ]
};

export default accompanyManRouter;
